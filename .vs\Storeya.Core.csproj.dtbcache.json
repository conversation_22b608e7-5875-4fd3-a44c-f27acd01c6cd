{"RootPath": "C:\\dev\\storeya\\trunk\\Storeya.Core", "ProjectFileName": "Storeya.Core.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Api\\BaseDataPage.cs"}, {"SourceFile": "Api\\CatalogPageData.cs"}, {"SourceFile": "Api\\FbActionTypes.cs"}, {"SourceFile": "Api\\Extensions.cs"}, {"SourceFile": "Api\\ProductPageData.cs"}, {"SourceFile": "Api\\FbActionEntity.cs"}, {"SourceFile": "Api\\ProductEntity.cs"}, {"SourceFile": "Api\\ShopEntity.cs"}, {"SourceFile": "Api\\WebHookEventsManager.cs"}, {"SourceFile": "Api\\WebHookType.cs"}, {"SourceFile": "DbModels\\AdCopy.cs"}, {"SourceFile": "DbModels\\AdServerUser.cs"}, {"SourceFile": "DbModels\\AdWordsCallsAd.cs"}, {"SourceFile": "DbModels\\AdWordsCallsCampaign.cs"}, {"SourceFile": "DbModels\\AdwordsCampaignEntity.cs"}, {"SourceFile": "DbModels\\Agency.cs"}, {"SourceFile": "DbModels\\AISeoProductOptimizer.cs"}, {"SourceFile": "DbModels\\AISeoProductOptimizerHistory.cs"}, {"SourceFile": "DbModels\\AIShopInfo.cs"}, {"SourceFile": "DbModels\\AllOrder.cs"}, {"SourceFile": "DbModels\\AppsForAppStore.cs"}, {"SourceFile": "DbModels\\AppsInstallation.cs"}, {"SourceFile": "DbModels\\BackgroundDowngradeQueue.cs"}, {"SourceFile": "DbModels\\BackgroundTask.cs"}, {"SourceFile": "DbModels\\Banner.cs"}, {"SourceFile": "DbModels\\BannerShopImage.cs"}, {"SourceFile": "DbModels\\BannersLight.cs"}, {"SourceFile": "DbModels\\Benchmark.cs"}, {"SourceFile": "DbModels\\BenchmarkApi.cs"}, {"SourceFile": "DbModels\\BenchmarkImagesOptimization.cs"}, {"SourceFile": "DbModels\\BenchmarkImagesOptimizationHistory.cs"}, {"SourceFile": "DbModels\\BigCommerceConnectedShop.cs"}, {"SourceFile": "DbModels\\BlacklistedClient.cs"}, {"SourceFile": "DbModels\\BoReminder.cs"}, {"SourceFile": "DbModels\\CampaignsUtms.cs"}, {"SourceFile": "DbModels\\CancellationFeedback.cs"}, {"SourceFile": "DbModels\\Category.cs"}, {"SourceFile": "DbModels\\CCreatorCollect.cs"}, {"SourceFile": "DbModels\\CCreatorCollection.cs"}, {"SourceFile": "DbModels\\CCreatorJob.cs"}, {"SourceFile": "DbModels\\CCreatorPattern.cs"}, {"SourceFile": "DbModels\\CCreatorProduct.cs"}, {"SourceFile": "DbModels\\ChargeTask.cs"}, {"SourceFile": "DbModels\\CollectionProduct.cs"}, {"SourceFile": "DbModels\\ConnectedWeeblySite.cs"}, {"SourceFile": "DbModels\\ContactForm.cs"}, {"SourceFile": "DbModels\\ContentItem.cs"}, {"SourceFile": "DbModels\\ContentMix.cs"}, {"SourceFile": "DbModels\\ContentMixesUser.cs"}, {"SourceFile": "DbModels\\CouponPop.cs"}, {"SourceFile": "DbModels\\CouponPopCode.cs"}, {"SourceFile": "DbModels\\CouponPopEvent.cs"}, {"SourceFile": "DbModels\\CouponPopEventsV2.cs"}, {"SourceFile": "DbModels\\CouponPopInstallationDomain.cs"}, {"SourceFile": "DbModels\\CouponPopStat.cs"}, {"SourceFile": "DbModels\\CouponPopStatsV2.cs"}, {"SourceFile": "DbModels\\CouponPopView.cs"}, {"SourceFile": "DbModels\\CouponPopViewsV2.cs"}, {"SourceFile": "DbModels\\CrmContact.cs"}, {"SourceFile": "DbModels\\CrmEvent.cs"}, {"SourceFile": "DbModels\\CurrencyRatesToUSD.cs"}, {"SourceFile": "DbModels\\DashboardAlert.cs"}, {"SourceFile": "DbModels\\DashboardProData.cs"}, {"SourceFile": "DbModels\\EbookUser.cs"}, {"SourceFile": "DbModels\\EcwidConnectedShop.cs"}, {"SourceFile": "DbModels\\EmailCampaign.cs"}, {"SourceFile": "DbModels\\EmailCampaignShop.cs"}, {"SourceFile": "DbModels\\EmailNotification.cs"}, {"SourceFile": "DbModels\\EmailProvider_ActiveCampaign.cs"}, {"SourceFile": "DbModels\\EmailProvider_Awerber.cs"}, {"SourceFile": "DbModels\\EmailProvider_CampaignMonitor.cs"}, {"SourceFile": "DbModels\\EmailProvider_ConstantContact.cs"}, {"SourceFile": "DbModels\\EmailProvider_GetResponse.cs"}, {"SourceFile": "DbModels\\EmailProvider_Infusionsoft.cs"}, {"SourceFile": "DbModels\\EmailProvider_Robly.cs"}, {"SourceFile": "DbModels\\EmailsLog.cs"}, {"SourceFile": "DbModels\\EmailTemplate.cs"}, {"SourceFile": "DbModels\\ePagesConnecttedShop.cs"}, {"SourceFile": "DbModels\\Event.cs"}, {"SourceFile": "DbModels\\ExitPop.cs"}, {"SourceFile": "DbModels\\ExitPopCode.cs"}, {"SourceFile": "DbModels\\ExitPopEvent.cs"}, {"SourceFile": "DbModels\\ExitPopInstallationDomain.cs"}, {"SourceFile": "DbModels\\ExitPopView.cs"}, {"SourceFile": "DbModels\\ExpAccount.cs"}, {"SourceFile": "DbModels\\ExpAdsStructure.cs"}, {"SourceFile": "DbModels\\Experiment.cs"}, {"SourceFile": "DbModels\\ExpGroup.cs"}, {"SourceFile": "DbModels\\ExpObject.cs"}, {"SourceFile": "DbModels\\ExpResult.cs"}, {"SourceFile": "DbModels\\ExpResultsByObject.cs"}, {"SourceFile": "DbModels\\ExtAppsScript.cs"}, {"SourceFile": "DbModels\\ExtAppsToken.cs"}, {"SourceFile": "DbModels\\ExternalApp.cs"}, {"SourceFile": "DbModels\\ExtPricingFeature.cs"}, {"SourceFile": "DbModels\\ExtPricingPlan.cs"}, {"SourceFile": "DbModels\\ExtPricingPlanFeature.cs"}, {"SourceFile": "DbModels\\FacebookB2BLeads.cs"}, {"SourceFile": "DbModels\\FanGate.cs"}, {"SourceFile": "DbModels\\FbAction.cs"}, {"SourceFile": "DbModels\\FbAdsBusiness.cs"}, {"SourceFile": "DbModels\\FbAdsChangeLog.cs"}, {"SourceFile": "DbModels\\FbAdsToken.cs"}, {"SourceFile": "DbModels\\FbGrader.cs"}, {"SourceFile": "DbModels\\FbPage.cs"}, {"SourceFile": "DbModels\\FbPagesTab.cs"}, {"SourceFile": "DbModels\\FbProfile.cs"}, {"SourceFile": "DbModels\\FsWebhookLog.cs"}, {"SourceFile": "DbModels\\FutureEmail.cs"}, {"SourceFile": "DbModels\\GA4ConnectedProperties.cs"}, {"SourceFile": "DbModels\\GAConnectedAccount.cs"}, {"SourceFile": "DbModels\\GAConnectedAccountsStat.cs"}, {"SourceFile": "DbModels\\GAConnectedAccountsStatsHistory.cs"}, {"SourceFile": "DbModels\\GAConnectedProfile.cs"}, {"SourceFile": "DbModels\\GaDashboardData.cs"}, {"SourceFile": "DbModels\\GaEvent.cs"}, {"SourceFile": "DbModels\\GalleryBlackList.cs"}, {"SourceFile": "DbModels\\GalleryProduct.cs"}, {"SourceFile": "DbModels\\GoggleCoupon.cs"}, {"SourceFile": "DbModels\\GoogleAdwordsCoupon.cs"}, {"SourceFile": "DbModels\\GroupDeal.cs"}, {"SourceFile": "DbModels\\GroupDealsTranslation.cs"}, {"SourceFile": "DbModels\\GroupDealUser.cs"}, {"SourceFile": "DbModels\\GrowthHero.cs"}, {"SourceFile": "DbModels\\HootSuiteUser.cs"}, {"SourceFile": "DbModels\\InstagramTabSetting.cs"}, {"SourceFile": "DbModels\\InventoryChangesLog.cs"}, {"SourceFile": "DbModels\\LauncherCommand.cs"}, {"SourceFile": "DbModels\\LauncherCommandType.cs"}, {"SourceFile": "DbModels\\LauncherTriggeredCommand.cs"}, {"SourceFile": "DbModels\\Layout.cs"}, {"SourceFile": "DbModels\\LeadDetail.cs"}, {"SourceFile": "DbModels\\LeadsForSale.cs"}, {"SourceFile": "DbModels\\LexityScript.cs"}, {"SourceFile": "DbModels\\LexityStore.cs"}, {"SourceFile": "DbModels\\LikeBox.cs"}, {"SourceFile": "DbModels\\Log4Net_Data.cs"}, {"SourceFile": "DbModels\\MagentoConnectedShop.cs"}, {"SourceFile": "DbModels\\MCConnectedAccount.cs"}, {"SourceFile": "DbModels\\MCConnectedSite.cs"}, {"SourceFile": "DbModels\\MerchantActivityLog.cs"}, {"SourceFile": "DbModels\\MerchantLogin.cs"}, {"SourceFile": "DbModels\\MerchantsOrder.cs"}, {"SourceFile": "DbModels\\MerchantsOrdersIpn.cs"}, {"SourceFile": "DbModels\\MonopolYa.cs"}, {"SourceFile": "DbModels\\NetoConnectedShop.cs"}, {"SourceFile": "DbModels\\PaymentsEventsHistory.cs"}, {"SourceFile": "DbModels\\PaymentsTransaction.cs"}, {"SourceFile": "DbModels\\PaypalConnectedUser.cs"}, {"SourceFile": "DbModels\\PinterestSetting.cs"}, {"SourceFile": "DbModels\\PlatformExtendedForMagentoApi.cs"}, {"SourceFile": "DbModels\\PlimusContract.cs"}, {"SourceFile": "DbModels\\PlimusIpnCall.cs"}, {"SourceFile": "DbModels\\PoolCode.cs"}, {"SourceFile": "DbModels\\PrestashopMerchant.cs"}, {"SourceFile": "DbModels\\PriceRanx.cs"}, {"SourceFile": "DbModels\\PriceRule.cs"}, {"SourceFile": "DbModels\\proc_CouponPops_Stats2_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_CouponPops_Stats3_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_CouponPops_Stats_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_Events_TopProduct_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_ExitPops_AllShops_Stats_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_ExitPops_Stats_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_Log4Net_Data_Agregated_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_RffOrders_AllShops_SelectByDate_Result.cs"}, {"SourceFile": "DbModels\\proc_RffOrders_AllShops_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_RffOrders_ForReport_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_Users_ForEmailList_Select_Result.cs"}, {"SourceFile": "DbModels\\proc_Visits_TopShops_Select_Result.cs"}, {"SourceFile": "DbModels\\Product.cs"}, {"SourceFile": "DbModels\\ProductDescribers.cs"}, {"SourceFile": "DbModels\\ProductDescribersBulk.cs"}, {"SourceFile": "DbModels\\ProductDescribersHistory.cs"}, {"SourceFile": "DbModels\\ProductImage.cs"}, {"SourceFile": "DbModels\\ProductsFeed.cs"}, {"SourceFile": "DbModels\\Products_ImportStatus.cs"}, {"SourceFile": "DbModels\\Referer.cs"}, {"SourceFile": "DbModels\\ReportSubscriber.cs"}, {"SourceFile": "DbModels\\RffEmailsUnsubscribed.cs"}, {"SourceFile": "DbModels\\RffEvent.cs"}, {"SourceFile": "DbModels\\RffOffer.cs"}, {"SourceFile": "DbModels\\RffOrder.cs"}, {"SourceFile": "DbModels\\RffSharesViaEmail.cs"}, {"SourceFile": "DbModels\\ScratchPromotion.cs"}, {"SourceFile": "DbModels\\ScratchTranslation.cs"}, {"SourceFile": "DbModels\\Series.cs"}, {"SourceFile": "DbModels\\SeriesEmail.cs"}, {"SourceFile": "DbModels\\SeriesEmailContent.cs"}, {"SourceFile": "DbModels\\SeriesSystemEvent.cs"}, {"SourceFile": "DbModels\\SeriesUser.cs"}, {"SourceFile": "DbModels\\SeriesUsersEvent.cs"}, {"SourceFile": "DbModels\\Shop.cs"}, {"SourceFile": "DbModels\\ShopAchievement.cs"}, {"SourceFile": "DbModels\\ShopAdmin.cs"}, {"SourceFile": "DbModels\\ShopApp.cs"}, {"SourceFile": "DbModels\\ShopAttributesValue.cs"}, {"SourceFile": "DbModels\\ShopContract.cs"}, {"SourceFile": "DbModels\\ShopDetail.cs"}, {"SourceFile": "DbModels\\ShopGallery.cs"}, {"SourceFile": "DbModels\\ShopifyAppCharge.cs"}, {"SourceFile": "DbModels\\ShopifyAppstoreReview.cs"}, {"SourceFile": "DbModels\\ShopifyConnectedShop.cs"}, {"SourceFile": "DbModels\\ShopifyMerchant.cs"}, {"SourceFile": "DbModels\\ShopLocalization.cs"}, {"SourceFile": "DbModels\\ShopMarketingTool.cs"}, {"SourceFile": "DbModels\\ShopOfTheWeek.cs"}, {"SourceFile": "DbModels\\ShopOnlinePromotion.cs"}, {"SourceFile": "DbModels\\ShopPaymentSetting.cs"}, {"SourceFile": "DbModels\\ShopPromotion.cs"}, {"SourceFile": "DbModels\\ShopPromotionsUsageHistory.cs"}, {"SourceFile": "DbModels\\ShopScreenshoot.cs"}, {"SourceFile": "DbModels\\ShopSubscription.cs"}, {"SourceFile": "DbModels\\Split_Result.cs"}, {"SourceFile": "DbModels\\SQLDataDashboard.cs"}, {"SourceFile": "DbModels\\SQLDataDashboardReport.cs"}, {"SourceFile": "DbModels\\SQLDataReport.cs"}, {"SourceFile": "DbModels\\SquareConnectedShop.cs"}, {"SourceFile": "DbModels\\StorenvyConnectedShop.cs"}, {"SourceFile": "DbModels\\StoreYaDB.Context.cs"}, {"SourceFile": "DbModels\\StoreYaDB.cs"}, {"SourceFile": "DbModels\\StoreYaDB.Designer.cs"}, {"SourceFile": "DbModels\\SuperAdminRemark.cs"}, {"SourceFile": "DbModels\\Sweepstake.cs"}, {"SourceFile": "DbModels\\SweepstakesTranslation.cs"}, {"SourceFile": "DbModels\\SweepstakesUser.cs"}, {"SourceFile": "DbModels\\SyncSettings_Amazon.cs"}, {"SourceFile": "DbModels\\SyncSettings_CafepressApi.cs"}, {"SourceFile": "DbModels\\SyncSettings_Csv.cs"}, {"SourceFile": "DbModels\\SyncSettings_eBay.cs"}, {"SourceFile": "DbModels\\SyncSettings_EtsyApi.cs"}, {"SourceFile": "DbModels\\SyncSettings_GoogleBase.cs"}, {"SourceFile": "DbModels\\SyncSettings_MagentoApi.cs"}, {"SourceFile": "DbModels\\SyncSettings_PrestaShopApi.cs"}, {"SourceFile": "DbModels\\SyncSettings_ShopCOM.cs"}, {"SourceFile": "DbModels\\SyncSettings_ShopifyApi.cs"}, {"SourceFile": "DbModels\\SyncSettings_SpreeCommerceApi.cs"}, {"SourceFile": "DbModels\\SyncSettings_SquareAPI.cs"}, {"SourceFile": "DbModels\\SyncSettings_TictailApi.cs"}, {"SourceFile": "DbModels\\SyncSettings_TradeTubeR.cs"}, {"SourceFile": "DbModels\\SyncSettings_WordPress.cs"}, {"SourceFile": "DbModels\\SyncSettings_ZazzleApi.cs"}, {"SourceFile": "DbModels\\SyncSummary.cs"}, {"SourceFile": "DbModels\\SystemEvent.cs"}, {"SourceFile": "DbModels\\SystemVariable.cs"}, {"SourceFile": "DbModels\\TargetingRule.cs"}, {"SourceFile": "DbModels\\TbAccountTrafficChannel.cs"}, {"SourceFile": "DbModels\\TbAdCampaign.cs"}, {"SourceFile": "DbModels\\TbAdCampaignsPerformance.cs"}, {"SourceFile": "DbModels\\TbAdCampaignsPerformanceByUtm.cs"}, {"SourceFile": "DbModels\\TbAdditionalData.cs"}, {"SourceFile": "DbModels\\TbAgreement.cs"}, {"SourceFile": "DbModels\\TbBigSpender.cs"}, {"SourceFile": "DbModels\\TbCampaign.cs"}, {"SourceFile": "DbModels\\TbFacebookAdsSetup.cs"}, {"SourceFile": "DbModels\\TBFeeHistory.cs"}, {"SourceFile": "DbModels\\TbInternalTask.cs"}, {"SourceFile": "DbModels\\TbPerformanceAlert.cs"}, {"SourceFile": "DbModels\\TBReport.cs"}, {"SourceFile": "DbModels\\TbReports2.cs"}, {"SourceFile": "DbModels\\TbRffEmailsShared.cs"}, {"SourceFile": "DbModels\\TbRffReferedUser.cs"}, {"SourceFile": "DbModels\\TbUpgradeCandidate.cs"}, {"SourceFile": "DbModels\\TbUserCreative.cs"}, {"SourceFile": "DbModels\\TeamKPIHistoryData.cs"}, {"SourceFile": "DbModels\\Testimonial.cs"}, {"SourceFile": "DbModels\\TrafficBooster.cs"}, {"SourceFile": "DbModels\\TrafficBoosterImpression.cs"}, {"SourceFile": "DbModels\\TrafficBoosterImpressionsDaily.cs"}, {"SourceFile": "DbModels\\TrustBadge.cs"}, {"SourceFile": "DbModels\\TTApp.cs"}, {"SourceFile": "DbModels\\TTAppsAlert.cs"}, {"SourceFile": "DbModels\\TwitterTab.cs"}, {"SourceFile": "DbModels\\User.cs"}, {"SourceFile": "DbModels\\UserPermission.cs"}, {"SourceFile": "DbModels\\UsersFromShopCOM.cs"}, {"SourceFile": "DbModels\\UsersInMailGroup.cs"}, {"SourceFile": "DbModels\\UsersReferedFrom.cs"}, {"SourceFile": "DbModels\\UsersUnpublishedNotification.cs"}, {"SourceFile": "DbModels\\UsersUnsubscribed.cs"}, {"SourceFile": "DbModels\\Video.cs"}, {"SourceFile": "DbModels\\Visit.cs"}, {"SourceFile": "DbModels\\WallaShopsMonthlyData.cs"}, {"SourceFile": "DbModels\\WallaShopsProduct.cs"}, {"SourceFile": "DbModels\\WixConnectedSite.cs"}, {"SourceFile": "DbModels\\WixTransaction.cs"}, {"SourceFile": "DbModels\\WixUser.cs"}, {"SourceFile": "DbModels\\WixWebhooksLog.cs"}, {"SourceFile": "DbModels\\YextConnectedSite.cs"}, {"SourceFile": "DbModels\\YouTubeTabSetting.cs"}, {"SourceFile": "Entities\\Application.cs"}, {"SourceFile": "Entities\\CancellationType.cs"}, {"SourceFile": "Entities\\CatalogSourcePlatforms.cs"}, {"SourceFile": "Entities\\DashboardNotifications.cs"}, {"SourceFile": "Entities\\Promotions.cs"}, {"SourceFile": "Entities\\Ebook.cs"}, {"SourceFile": "Entities\\EmailList.cs"}, {"SourceFile": "Entities\\EmailManager.cs"}, {"SourceFile": "Entities\\EmailProvider.cs"}, {"SourceFile": "Entities\\EmailTemplateType.cs"}, {"SourceFile": "Entities\\Facebook\\FacebookPage.cs"}, {"SourceFile": "Entities\\Facebook\\JsonTabs.cs"}, {"SourceFile": "Entities\\Import\\CatalogStates.cs"}, {"SourceFile": "Entities\\Import\\AfterImportStates.cs"}, {"SourceFile": "Entities\\Import\\ImportFileFormat.cs"}, {"SourceFile": "Entities\\Import\\ResponseObjects.cs"}, {"SourceFile": "Entities\\InventoryChangesTracker.cs"}, {"SourceFile": "Entities\\MerchantActivity.cs"}, {"SourceFile": "Entities\\OriginMarketplaces.cs"}, {"SourceFile": "Entities\\PaymentDetailes.cs"}, {"SourceFile": "Entities\\Permissions.cs"}, {"SourceFile": "Entities\\ProductRawData.cs"}, {"SourceFile": "Entities\\RawCategory.cs"}, {"SourceFile": "Entities\\RawCollection.cs"}, {"SourceFile": "Entities\\RawProductImage.cs"}, {"SourceFile": "Entities\\ReservedForLoggingShops.cs"}, {"SourceFile": "Entities\\StoreYaEmployee.cs"}, {"SourceFile": "Entities\\SystemEvents.cs"}, {"SourceFile": "Entities\\Tabs\\TabApplicationsTypes.cs"}, {"SourceFile": "Entities\\TargetingRules.cs"}, {"SourceFile": "Entities\\TbShop.cs"}, {"SourceFile": "Entities\\VariableTypes.cs"}, {"SourceFile": "Entities\\WebsiteAnalyser\\Result.cs"}, {"SourceFile": "Entities\\WebsiteAnalyser\\ValidationStatus.cs"}, {"SourceFile": "Helpers\\AdExtractorManager.cs"}, {"SourceFile": "Helpers\\Aes128bitEncryptionHelper.cs"}, {"SourceFile": "Helpers\\AIShopInfoHelper.cs"}, {"SourceFile": "Helpers\\Api Tools\\Scripts.cs"}, {"SourceFile": "Helpers\\Api Tools\\Utils.cs"}, {"SourceFile": "Helpers\\ApiKeyHelper.cs"}, {"SourceFile": "Helpers\\BingSearcherHelper.cs"}, {"SourceFile": "Helpers\\BlackListHelper.cs"}, {"SourceFile": "Helpers\\BoReminderHelper.cs"}, {"SourceFile": "Helpers\\CacheHelper.cs"}, {"SourceFile": "Helpers\\ChanelCollector.cs"}, {"SourceFile": "Helpers\\ChatGPTStorePageParser.cs"}, {"SourceFile": "Helpers\\DashboardAlertsNotifications.cs"}, {"SourceFile": "Helpers\\ExceptionExtensions.cs"}, {"SourceFile": "Helpers\\ImaggeHelper.cs"}, {"SourceFile": "Helpers\\IPNCallHelper.cs"}, {"SourceFile": "Helpers\\LogInHelper.cs"}, {"SourceFile": "Helpers\\PaymentsEventsHistoryHelper.cs"}, {"SourceFile": "Helpers\\PermissionManagerHelper.cs"}, {"SourceFile": "Helpers\\ShopifyDataCollector.cs"}, {"SourceFile": "Helpers\\BudgetUpgradeHelper.cs"}, {"SourceFile": "Helpers\\TrafficBoosterComparer.cs"}, {"SourceFile": "Helpers\\TrafficboosterSetups.cs"}, {"SourceFile": "Helpers\\VerificationCodeGenerator.cs"}, {"SourceFile": "Helpers\\WebSitesSignalsHelper.cs"}, {"SourceFile": "Helpers\\WhoIsAPI.cs"}, {"SourceFile": "Models\\AdWords\\AdCreatorHelper.cs"}, {"SourceFile": "Models\\BitStudioApi.cs"}, {"SourceFile": "Models\\ChatGPT\\ChatGPTImageManager.cs"}, {"SourceFile": "Models\\ChatGPT\\ChatGPTStructuredModels.cs"}, {"SourceFile": "Models\\ChatGPT\\PrePlexityManager.cs"}, {"SourceFile": "Models\\ChatGPT\\GeminiManager.cs"}, {"SourceFile": "Models\\DataProviders\\ShopCom\\ShopComProvider.cs"}, {"SourceFile": "Models\\EmailsSeriesSystem\\EmailsSeriesHelper.cs"}, {"SourceFile": "Models\\EmailsSeriesSystem\\EmailsSeriesManager.cs"}, {"SourceFile": "Helpers\\FileDeleterHelper.cs"}, {"SourceFile": "Helpers\\ShopifyAppstoreReviewsHelper.cs"}, {"SourceFile": "Helpers\\FacebookB2BLeadsHelper.cs"}, {"SourceFile": "Helpers\\EmailTrackingHelper.cs"}, {"SourceFile": "Helpers\\DashboardAlertsHelper.cs"}, {"SourceFile": "Helpers\\DBCurrencyHelper.cs"}, {"SourceFile": "Helpers\\MCURLHelper.cs"}, {"SourceFile": "Helpers\\CloudManager.cs"}, {"SourceFile": "Helpers\\ConsoleAppArgHelper.cs"}, {"SourceFile": "Helpers\\EncryptionHelper.cs"}, {"SourceFile": "Helpers\\ExtentionsHelper.cs"}, {"SourceFile": "Helpers\\FeedsHelper.cs"}, {"SourceFile": "Helpers\\FormattedDbEntityValidationException.cs"}, {"SourceFile": "Helpers\\GoogAccountHelper.cs"}, {"SourceFile": "Helpers\\HtmlParser.cs"}, {"SourceFile": "Helpers\\LauncherHelper.cs"}, {"SourceFile": "Helpers\\NumbersFormater.cs"}, {"SourceFile": "Helpers\\OutbrainHelper.cs"}, {"SourceFile": "Helpers\\PaymentsTransactionHelper.cs"}, {"SourceFile": "Helpers\\PDFHelper.cs"}, {"SourceFile": "Helpers\\PromotionsHelper.cs"}, {"SourceFile": "Helpers\\ShopsHelper.cs"}, {"SourceFile": "Helpers\\SslHelper.cs"}, {"SourceFile": "Helpers\\TbCategoriesHelper.cs"}, {"SourceFile": "Helpers\\ConsoleAppHelper.cs"}, {"SourceFile": "Helpers\\CountriesHelper.cs"}, {"SourceFile": "Helpers\\CurrencyHelper.cs"}, {"SourceFile": "Helpers\\DateTimeHelper.cs"}, {"SourceFile": "Helpers\\Eventer.cs"}, {"SourceFile": "Helpers\\GaStatsHelper.cs"}, {"SourceFile": "Helpers\\GoogleBaseFeedHelper.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\BarChart.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\Chart.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\ChartAxis.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\ChartData.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\FillArea.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\LinearGradientFill.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\LinearStripesFill.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\LineChart.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\PieChart.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\RangeMarker.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\ScatterPlot.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\ShapeMarker.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\SolidFill.cs"}, {"SourceFile": "Helpers\\GoogleImageCharts\\VennDiagram.cs"}, {"SourceFile": "Helpers\\Helper.cs"}, {"SourceFile": "Helpers\\HttpRequestResponseHelper.cs"}, {"SourceFile": "Helpers\\ImagePathHelper.cs"}, {"SourceFile": "Helpers\\MailHelper.cs"}, {"SourceFile": "Helpers\\MathHelper.cs"}, {"SourceFile": "Helpers\\MobileHelper.cs"}, {"SourceFile": "Helpers\\PasswordHelper.cs"}, {"SourceFile": "Helpers\\PermissionsHelper.cs"}, {"SourceFile": "Helpers\\PriceParser.cs"}, {"SourceFile": "Helpers\\ProductImageHelper.cs"}, {"SourceFile": "Helpers\\SequenceHelper.cs"}, {"SourceFile": "Helpers\\StoreyaApiHelper.cs"}, {"SourceFile": "Helpers\\SystemVarHelper.cs"}, {"SourceFile": "Helpers\\TbFacebookAdsHelper.cs"}, {"SourceFile": "Helpers\\TranslationHelper.cs"}, {"SourceFile": "Helpers\\UserLocationHelper.cs"}, {"SourceFile": "Helpers\\WallashopsProductsStatuses.cs"}, {"SourceFile": "Helpers\\WebHookHelper.cs"}, {"SourceFile": "Helpers\\WebPageParser.cs"}, {"SourceFile": "Helpers\\WebRequestHelper.cs"}, {"SourceFile": "Helpers\\WeeblyAuthHelper.cs"}, {"SourceFile": "Helpers\\WidgetStatusHelper.cs"}, {"SourceFile": "Helpers\\WixHelper.cs"}, {"SourceFile": "Helpers\\XmlHelper.cs"}, {"SourceFile": "Helpers\\ZendescCommentsObject.cs"}, {"SourceFile": "Helpers\\ZenDeskApi.cs"}, {"SourceFile": "Models\\Account\\AccountManager.cs"}, {"SourceFile": "Models\\Account\\EmailAccountManager.cs"}, {"SourceFile": "Models\\AdCopiesManager.cs"}, {"SourceFile": "Models\\AdCopyExtractor.cs"}, {"SourceFile": "Models\\AdCopyExtractorViewModel.cs"}, {"SourceFile": "Models\\AdCopyTypes.cs"}, {"SourceFile": "Models\\AdWords\\AccountNameParser.cs"}, {"SourceFile": "Models\\AdWords\\AdCopyValidator.cs"}, {"SourceFile": "Models\\AdWords\\AdwordsCampaignEntitiesHelper.cs"}, {"SourceFile": "Models\\AdWords\\AdwordsKeywordsHelper2.cs"}, {"SourceFile": "Models\\AdWords\\AdwordsManager.cs"}, {"SourceFile": "Models\\AdWords\\AdwordsMCCConfiguration.cs"}, {"SourceFile": "Models\\AdWords\\AdwordsObjects.cs"}, {"SourceFile": "Models\\AdWords\\AdWordsPixelParser.cs"}, {"SourceFile": "Models\\AdWords\\AdwordsZapMCCConfiguration.cs"}, {"SourceFile": "Models\\AdWords\\BaseAdWordsCampaign.cs"}, {"SourceFile": "Models\\AdWords\\BidOptimizer.cs"}, {"SourceFile": "Models\\AdWords\\BudjetStatuses.cs"}, {"SourceFile": "Models\\AdWords\\CampaignNameData.cs"}, {"SourceFile": "Models\\AdWords\\IAdwordsMCCConfiguration.cs"}, {"SourceFile": "Models\\AdWords\\LocationsData.cs"}, {"SourceFile": "Models\\AdWords\\ManagedCustomer.cs"}, {"SourceFile": "Models\\AdWords\\ManagedCustomerTreeNode.cs"}, {"SourceFile": "Models\\AdWords\\MarketingAdsApiProvider.cs"}, {"SourceFile": "Models\\AdWords\\PerformanceDataAtDashboard.cs"}, {"SourceFile": "Models\\AdWords\\ResponsiveDisplayAdHelper.cs"}, {"SourceFile": "Models\\AdWords\\SearchAdWordsCampaign.cs"}, {"SourceFile": "Models\\AdWords\\CallOnlyAdWordsCampaign.cs"}, {"SourceFile": "Models\\AdWords\\CampaignFactory.cs"}, {"SourceFile": "Models\\AdWords\\CampaignsTypes.cs"}, {"SourceFile": "Models\\AdWords\\DsaUrlsHelper.cs"}, {"SourceFile": "Models\\AdWords\\LanguagesCodes.cs"}, {"SourceFile": "Models\\AdWords\\CountriesCodes.cs"}, {"SourceFile": "Models\\AdWords\\MarketplaceUrlHelper.cs"}, {"SourceFile": "Models\\AdWords\\ShoppingAdWordsCampaignTypes.cs"}, {"SourceFile": "Models\\AdWords\\UploadCsvObject.cs"}, {"SourceFile": "Models\\AmplitudeEventsManager.cs"}, {"SourceFile": "Models\\AnalyticsModels\\PixelAnalyzer.cs"}, {"SourceFile": "Models\\ApiAuthHelper.cs"}, {"SourceFile": "Models\\AppStore\\SubscriptionChange.cs"}, {"SourceFile": "Models\\AuditTool\\AuditTool.cs"}, {"SourceFile": "Models\\AuditTool\\AuditToolData.cs"}, {"SourceFile": "Models\\AuditTool\\AuditToolResults.cs"}, {"SourceFile": "Models\\AuditTool\\BenchmarkApiManager.cs"}, {"SourceFile": "Models\\AuditTool\\BenchmarkApiJsonResults.cs"}, {"SourceFile": "Models\\AuditTool\\BenchmarkImagesManager.cs"}, {"SourceFile": "Models\\AuditTool\\BenchmarksManager.cs"}, {"SourceFile": "Models\\AuditTool\\GrowthHeroManager.cs"}, {"SourceFile": "Models\\AuditTool\\PageSpeedResult.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\Magento2_PrimaryImportTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\Magento2_SyncTask.cs"}, {"SourceFile": "Models\\BFEmailDTO\\BFEmailDTO.cs"}, {"SourceFile": "Models\\Bing\\BingSearchContainer.cs"}, {"SourceFile": "Models\\BI\\BiAnalysisResponse.cs"}, {"SourceFile": "Models\\BI\\BiAssistant.cs"}, {"SourceFile": "Models\\BI\\BiDataRow.cs"}, {"SourceFile": "Models\\BI\\BiPerformanceRow.cs"}, {"SourceFile": "Models\\BI\\BiTrendFinder.cs"}, {"SourceFile": "Models\\BI\\BiTrendFinderResult.cs"}, {"SourceFile": "Models\\BlackListManager.cs"}, {"SourceFile": "Models\\BoReports\\IpnDatasModel.cs"}, {"SourceFile": "Models\\BoReports\\UpgradeData.cs"}, {"SourceFile": "Models\\BoReports\\UpgradesManager.cs"}, {"SourceFile": "Models\\BudgetValidator\\BudgetValidateStrategy.cs"}, {"SourceFile": "Models\\BudgetValidator\\BudgetSpendStat.cs"}, {"SourceFile": "Models\\BudgetValidator\\IValidatorStrategy.cs"}, {"SourceFile": "Models\\CFOViewHelper.cs"}, {"SourceFile": "Models\\Charges\\BlueSnapApiError.cs"}, {"SourceFile": "Models\\Charges\\BlueSnapApiWrapper.cs"}, {"SourceFile": "Models\\Charges\\ACHData.cs"}, {"SourceFile": "Models\\Charges\\BluesnapSubscriptions.cs"}, {"SourceFile": "Models\\Charges\\SubscriptionManagerNew.cs"}, {"SourceFile": "Models\\Charges\\SubscriptionModel.cs"}, {"SourceFile": "Models\\AmplitudeApi.cs"}, {"SourceFile": "Models\\ChatGPT\\ChatGPTManager.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\ActiveCampaign.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\OmnisendApiProvider.cs"}, {"SourceFile": "Helpers\\ExperimentsHelper.cs"}, {"SourceFile": "Models\\DataDogApiClient.cs"}, {"SourceFile": "Models\\EmailsSeriesSystem\\ISeriesEmailMessage .cs"}, {"SourceFile": "Models\\EmailsSeriesSystem\\Series1_EmailMessage.cs"}, {"SourceFile": "Models\\EmailsSeriesSystem\\SeriesEmpty_EmailMessage.cs"}, {"SourceFile": "Models\\EmailsSeriesSystem\\SeriesUpgrade_EmailMessage.cs"}, {"SourceFile": "Models\\EmailsSeriesSystem\\UserName_EmailMessage.cs"}, {"SourceFile": "Models\\EmailsSeriesSystem\\SetupMissingThings_EmailMessage .cs"}, {"SourceFile": "Models\\EmailsSeriesSystem\\SeriesExceptions.cs"}, {"SourceFile": "Models\\FastSpring\\Configurations\\FastSpringDevConfiguration.cs"}, {"SourceFile": "Models\\FastSpring\\Configurations\\FastSpringLocalConfiguration.cs"}, {"SourceFile": "Models\\FastSpring\\Configurations\\FastSpringProdConfiguration.cs"}, {"SourceFile": "Models\\FastSpring\\FastSpringEncryptionHelper.cs"}, {"SourceFile": "Models\\FastSpring\\FastSpringException.cs"}, {"SourceFile": "Models\\FastSpring\\FastSpringPaymentService.cs"}, {"SourceFile": "Models\\FastSpring\\Models\\FSImportFromBSModel.cs"}, {"SourceFile": "Models\\FastSpring\\Models\\FSOrdersModel.cs"}, {"SourceFile": "Models\\FastSpring\\FastSpringHelper.cs"}, {"SourceFile": "Models\\FastSpring\\Models\\FSAccountsModel.cs"}, {"SourceFile": "Models\\FastSpring\\FastSpringApiProvider.cs"}, {"SourceFile": "Models\\FastSpring\\Models\\FSSubscriptionsModel.cs"}, {"SourceFile": "Models\\FastSpring\\IFastSpringConfiguration.cs"}, {"SourceFile": "Models\\FastSpring\\Models\\FSWebhooksModel.cs"}, {"SourceFile": "Models\\GA\\Ga4SimpleService.cs"}, {"SourceFile": "Models\\GA\\GA4Helper.cs"}, {"SourceFile": "Models\\GA\\GAHelper.cs"}, {"SourceFile": "Models\\GA\\IGaSimpleService.cs"}, {"SourceFile": "Models\\GoogleMerchantCenter\\GoogleAccountConnector.cs"}, {"SourceFile": "Models\\GoogleMerchantCenter\\GoogleMCAccount.cs"}, {"SourceFile": "Models\\GoogleMerchantCenter\\GoogleMCConfig.cs"}, {"SourceFile": "Models\\GoogleMerchantCenter\\GoogleMcSiteStatuses.cs"}, {"SourceFile": "Models\\GoogleMerchantCenter\\GoogleMcRestApi.cs"}, {"SourceFile": "Models\\InstantlyClient.cs"}, {"SourceFile": "Models\\LeadsForSalesManager.cs"}, {"SourceFile": "Models\\FastSpring\\FastSpringWebhookParser.cs"}, {"SourceFile": "Models\\Payments\\BlueSnapIpnCallEntity.cs"}, {"SourceFile": "Models\\ProductDescriber\\PartialDBClasses.cs"}, {"SourceFile": "Models\\ProductDescriber\\PDProduct.cs"}, {"SourceFile": "Models\\ProductDescriber\\ProductDescriberBulkManager.cs"}, {"SourceFile": "Models\\ProductDescriber\\ProductDescriberManager.cs"}, {"SourceFile": "Models\\SendingLettersToClients\\SendingLettersToClients.cs"}, {"SourceFile": "Models\\Shopify\\ImaggaAPIClient.cs"}, {"SourceFile": "Models\\Shopify\\ShopifyOrdersSourceModel.cs"}, {"SourceFile": "Models\\Shopify\\ShopifyOrdersSourceService.cs"}, {"SourceFile": "Models\\ShopsRefactor\\Refactor_AdwRemarketingCode.cs"}, {"SourceFile": "Models\\ShopValidation\\BigCommerceMissingThingsChecker.cs"}, {"SourceFile": "Models\\ShopValidation\\MissingThingsChecker.cs"}, {"SourceFile": "Models\\ShopValidation\\MissingThings.cs"}, {"SourceFile": "Models\\ShopValidation\\ShopifyMissingThingsChecker.cs"}, {"SourceFile": "Models\\ShopValidation\\WooCommerceMissingThingsChecker.cs"}, {"SourceFile": "Models\\SimilarWebApi.cs"}, {"SourceFile": "Models\\TbDashboardHelperTests.cs"}, {"SourceFile": "Models\\DashboardProDataManager.cs"}, {"SourceFile": "Models\\DashboardProDataModel.cs"}, {"SourceFile": "Models\\DataProviders\\NetoApiProvider.cs"}, {"SourceFile": "Models\\DataProviders\\SquareApiProvider.cs"}, {"SourceFile": "Models\\DataProviders\\Wix\\WixApiProvider.cs"}, {"SourceFile": "Models\\DataProviders\\Wix\\WixDataProvider.cs"}, {"SourceFile": "Models\\DemoAccountManager.cs"}, {"SourceFile": "Models\\DimensionsManager.cs"}, {"SourceFile": "Models\\FbAds\\FbAdsSetupException.cs"}, {"SourceFile": "Models\\FbConversionApiHelper.cs"}, {"SourceFile": "Models\\GlobalEFeedObject.cs"}, {"SourceFile": "Models\\GlobalEManager.cs"}, {"SourceFile": "Models\\Google.Ads\\GoogleAdsAdGroup.cs"}, {"SourceFile": "Models\\Google.Ads\\GoogleAdsCampaign.cs"}, {"SourceFile": "Models\\Google.Ads\\GoogleAdsConversion.cs"}, {"SourceFile": "Models\\ImageOptimizer\\ImageNamer.cs"}, {"SourceFile": "Models\\ImageOptimizer\\ImageObject.cs"}, {"SourceFile": "Models\\ImageOptimizer\\ImageOptimizer.cs"}, {"SourceFile": "Models\\Neto\\INetoConfigurations.cs"}, {"SourceFile": "Models\\Neto\\NetoApiHelper.cs"}, {"SourceFile": "Models\\Neto\\NetoConfigurations.cs"}, {"SourceFile": "Models\\Neto\\NetoConnectorException.cs"}, {"SourceFile": "Models\\Neto\\NetoLocalConfigurations.cs"}, {"SourceFile": "Models\\Neto\\NetoProducts.cs"}, {"SourceFile": "Models\\Neto\\NetoSandBoxConfigurations.cs"}, {"SourceFile": "Models\\NotificationModel.cs"}, {"SourceFile": "Models\\Experimenter\\ExpAdsStructureManager.cs"}, {"SourceFile": "Models\\Experimenter\\ExpDefinition.cs"}, {"SourceFile": "Models\\Experimenter\\ExpResultRow.cs"}, {"SourceFile": "Models\\Experimenter\\IExperimentDataSource.cs"}, {"SourceFile": "Models\\FbAds\\FacebookCampaing.cs"}, {"SourceFile": "Models\\FbAds\\FbAdsAccountCreator.cs"}, {"SourceFile": "Models\\FbAds\\FbAdsAccountValidator.cs"}, {"SourceFile": "Models\\FbAds\\FbCampaignNameParser.cs"}, {"SourceFile": "Models\\FbAds\\FBCatalogBatchApi.cs"}, {"SourceFile": "Models\\FbAds\\FbDataCollector.cs"}, {"SourceFile": "Models\\FbAds\\TbReports2Record.cs"}, {"SourceFile": "Models\\GA\\BaseGoogleAnalyticsRequest.cs"}, {"SourceFile": "Models\\GA\\EventHitRequest.cs"}, {"SourceFile": "Models\\GA\\GaCompReportData.cs"}, {"SourceFile": "Models\\GA\\GaDataViewModel.cs"}, {"SourceFile": "Models\\GA\\GoogleAnalyticsProvider.cs"}, {"SourceFile": "Models\\GA\\HitType.cs"}, {"SourceFile": "Models\\GA\\TbReportMcf.cs"}, {"SourceFile": "Models\\GrowthHeroModels\\AdDetails.cs"}, {"SourceFile": "Models\\GrowthHeroModels\\GrowthHeroAgeModel.cs"}, {"SourceFile": "Models\\GrowthHeroModels\\GrowthHeroGenderModel.cs"}, {"SourceFile": "Models\\GrowthHeroModels\\GrowthHeroLoosers.cs"}, {"SourceFile": "Models\\GrowthHeroModels\\GrowthHeroMissingOpportunities.cs"}, {"SourceFile": "Models\\GrowthHeroModels\\GrowthHeroMissingOpportunitiesModel.cs"}, {"SourceFile": "Models\\GrowthHeroModels\\GrowthHeroModel.cs"}, {"SourceFile": "Models\\GrowthHeroModels\\GrowthHeroWastedModel.cs"}, {"SourceFile": "Models\\GrowthHeroModels\\InsightsViewModel.cs"}, {"SourceFile": "Models\\InternalAlgoMetrics\\IntermalAlgoMetricsRow.cs"}, {"SourceFile": "Models\\InternalAlgoMetrics\\IntermalAlgoShopData.cs"}, {"SourceFile": "Models\\InternalAlgoMetrics\\InternalAlgoMetricsManager.cs"}, {"SourceFile": "Models\\InternalAlgoMetrics\\ShopBudgetSpendStat.cs"}, {"SourceFile": "Models\\Launcher.cs"}, {"SourceFile": "Models\\LeadDetailsProvider.cs"}, {"SourceFile": "Models\\Marketplaces\\ShopifyConnectorException.cs"}, {"SourceFile": "Models\\MixPanelApi.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\BizzyProvider.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\CampaignMonitor.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\ConstantContactProvider.cs"}, {"SourceFile": "Models\\AppStore\\AllApps.cs"}, {"SourceFile": "Models\\AppStore\\AppStatuses.cs"}, {"SourceFile": "Models\\AppStore\\AppStoreEmailEntity.cs"}, {"SourceFile": "Models\\AppStore\\AppStoreManager.cs"}, {"SourceFile": "Models\\AppStore\\AppTypes.cs"}, {"SourceFile": "Models\\AppStore\\AppEntity.cs"}, {"SourceFile": "Models\\AppStore\\ShopConnectionManager.cs"}, {"SourceFile": "Models\\AppStore\\SpecificSubscriptionID.cs"}, {"SourceFile": "Models\\AppStore\\SubscriptionChangeResult.cs"}, {"SourceFile": "Models\\AppStore\\SubscriptionChangeManager.cs"}, {"SourceFile": "Models\\AppStore\\SubscriptionManager.cs"}, {"SourceFile": "Models\\BackgroundTasks\\BackgroundTaskReporter.cs"}, {"SourceFile": "Models\\BackgroundTasks\\BackgroundTaskReportEventArgs.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\Amazon_PrimaryImportTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\Amazon_SyncTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\AbstructPrimaryImportTask_WithOnlyImages.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\ExtendedGoogleBase_PrimaryImportTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\ExtendedGoogleBase_SyncTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\GoogleBase_PrimaryImportTask.cs.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\GoogleBase_SyncTask.cs.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\SpreeCommerce_PrimaryImportTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\SpreeCommerce_SyncTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\Zazzle_PrimaryTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\Zazzle_SyncTask.cs"}, {"SourceFile": "Models\\Bigcommerce\\AuthResponse.cs"}, {"SourceFile": "Models\\Bigcommerce\\BigcommerceApi.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\ClientEmailPoviderType.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\ConversioProvider.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\GetResponseProvider.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\Infusionsoft.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\KlaviyoProvider.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\MailChimpProvider.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\Ometria.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\RoblyProvider.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\SendGridProvider.cs"}, {"SourceFile": "Models\\ClientEmailProviders\\ZapierProvider.cs"}, {"SourceFile": "Models\\ConvertApi.cs"}, {"SourceFile": "Models\\CouponPopStatsManager\\CpStatsEntities.cs"}, {"SourceFile": "Models\\CouponPopStatsManager\\StatsManager.cs"}, {"SourceFile": "Models\\CRM\\CrmEventsManager.cs"}, {"SourceFile": "Models\\CRM\\CrmEventTypes.cs"}, {"SourceFile": "Models\\CRM\\Pipedrive.cs"}, {"SourceFile": "Models\\CRM\\PipedriveJsonGetByTerm.cs"}, {"SourceFile": "Models\\CRM\\PipedriveJsonPerson.cs"}, {"SourceFile": "Models\\DataProviders\\EcwidProvider.cs"}, {"SourceFile": "Models\\DataProviders\\ePagesProvider.cs"}, {"SourceFile": "Models\\DataProviders\\InstagramProvider.cs"}, {"SourceFile": "Models\\DataProviders\\Lexity\\JsonScriptBlob.cs"}, {"SourceFile": "Models\\DataProviders\\Lexity\\JsonStore.cs"}, {"SourceFile": "Models\\DataProviders\\DataProviderHelper.cs"}, {"SourceFile": "Models\\DataProviders\\EtsyEntities\\Categories\\JsonEtsyCategories.cs"}, {"SourceFile": "Models\\DataProviders\\EtsyEntities\\Images\\JsonEtsyProductImages.cs"}, {"SourceFile": "Models\\DataProviders\\EtsyEntities\\OAuth\\OAuth.cs"}, {"SourceFile": "Models\\DataProviders\\EtsyEntities\\Product\\JsonEtsyProduct.cs"}, {"SourceFile": "Models\\DataProviders\\EtsyEntities\\Sections\\JsonEtsySections.cs"}, {"SourceFile": "Models\\DataProviders\\EtsyEntities\\ShopItems\\JsonEtsyItemsByShop.cs"}, {"SourceFile": "Models\\DataProviders\\EtsyOAuthAuthentication.cs"}, {"SourceFile": "Models\\DataProviders\\GenericSerializer.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\ShopComProviderOLD.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\TheCraftStarProvider.cs"}, {"SourceFile": "Models\\DataProviders\\Lexity\\LexityEntities.cs"}, {"SourceFile": "Models\\DataProviders\\ProviderFactory.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\DatafeedrProvider.cs"}, {"SourceFile": "Models\\DataProviders\\DataProviderException.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\GoogleBaseParser.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\GoogleBaseProvider.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\OpenCartProvider.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\OsCommerceProvider.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\StoreYaFeedProvider.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\VirtueMartProvider.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\WordPressProvider.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\WP_e_CommerceProvider.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\WP_WooCommerceProvider.cs"}, {"SourceFile": "Models\\DataProviders\\GoogleBaseProviders\\ZenCartProvider.cs"}, {"SourceFile": "Models\\DataProviders\\PinterestEntities\\Boards\\JsonBoards.cs"}, {"SourceFile": "Models\\DataProviders\\PinterestEntities\\Pins\\JsonPins.cs"}, {"SourceFile": "Models\\DataProviders\\ProductRawDataComparer.cs"}, {"SourceFile": "Models\\DataProviders\\ShopifyEntities\\CollectEntity.cs"}, {"SourceFile": "Models\\DataProviders\\ShopifyEntities\\SmartCollectionEntities.cs"}, {"SourceFile": "Models\\DataProviders\\SpecialCharactersHandler.cs"}, {"SourceFile": "Models\\DataProviders\\TwitterEntities\\Statuses\\JsonTwitterStatuses.cs"}, {"SourceFile": "Models\\DataProviders\\TwitterEntities\\Tweets\\JsonTwitterTweets.cs"}, {"SourceFile": "Models\\DataProviders\\TwitterEntities\\User\\JsonTwitterUser.cs"}, {"SourceFile": "Models\\DataProviders\\Wix\\WixAppInstance.cs"}, {"SourceFile": "Models\\DataProviders\\Wix\\WixInstanceEntity.cs"}, {"SourceFile": "Models\\DataProviders\\YextConnector.cs"}, {"SourceFile": "Models\\DataProviders\\YouTubeProvider.cs"}, {"SourceFile": "Models\\EmailProviders\\MailGunProvider.cs"}, {"SourceFile": "Models\\EmailProviders\\Postmark.cs"}, {"SourceFile": "Models\\ExitPop\\ExitPopEntities.cs"}, {"SourceFile": "Models\\ExternalApps\\ExternalAppsPricingManager.cs"}, {"SourceFile": "Models\\FbAds\\CrmDataRow.cs"}, {"SourceFile": "Models\\FbAds\\FbAdsBusinesses.cs"}, {"SourceFile": "Models\\FbAds\\FbAdsGrader.cs"}, {"SourceFile": "Models\\FbAds\\FbAdsInsightsClient.cs"}, {"SourceFile": "Models\\FbAds\\FbAdsSDK.cs"}, {"SourceFile": "Models\\FbAds\\FbApiClient.cs"}, {"SourceFile": "Models\\GA\\EntityUserLink.cs"}, {"SourceFile": "Models\\GA\\GAAccountStatusTypes.cs"}, {"SourceFile": "Models\\GA\\GaAnalyzer.cs"}, {"SourceFile": "Models\\GA\\GAConnectStatus.cs"}, {"SourceFile": "Models\\GA\\GADataExtractor.cs"}, {"SourceFile": "Models\\GA\\GaGenericReport.cs"}, {"SourceFile": "Models\\GA\\GaKeywordsAnalyzer.cs"}, {"SourceFile": "Models\\GA\\GaManager.cs"}, {"SourceFile": "Models\\GA\\GaProfileInfo.cs"}, {"SourceFile": "Models\\GA\\GaSimpleDataRequest.cs"}, {"SourceFile": "Models\\GA\\GaSimpleWrapper.cs"}, {"SourceFile": "Models\\GA\\Validations\\IGAValidation.cs"}, {"SourceFile": "Models\\GA\\Validations\\SourcesValidation.cs"}, {"SourceFile": "Models\\Import\\PostImportStep.cs"}, {"SourceFile": "Models\\Import\\PostImportModel.cs"}, {"SourceFile": "Models\\IpInfoDB\\IpInfoDBProvider.cs"}, {"SourceFile": "Models\\IpnDataHelper.cs"}, {"SourceFile": "Models\\MarketingTools\\MarketingToolsManager.cs"}, {"SourceFile": "Models\\MarketingTools\\MarketingToolsTypes.cs"}, {"SourceFile": "Models\\Marketplaces\\ShopifyConnector.cs"}, {"SourceFile": "Models\\Marketplaces\\ShopifyPlan.cs"}, {"SourceFile": "Models\\Marketplaces\\StorenvyConnector.cs"}, {"SourceFile": "Models\\OnPageAnalyzerModel.cs"}, {"SourceFile": "Models\\OnPageAnalyzerObjects.cs"}, {"SourceFile": "Models\\Payments\\AbstractPaymentAdapter.cs"}, {"SourceFile": "Models\\Payments\\BannersTotalAllowedByPlan.cs"}, {"SourceFile": "Models\\Payments\\BlueSnapPaymentAdapter.cs"}, {"SourceFile": "Models\\Payments\\ContractSettings.cs"}, {"SourceFile": "Models\\Payments\\IPNCallEntity.cs"}, {"SourceFile": "Models\\Payments\\PaymentAdapter.cs"}, {"SourceFile": "Models\\Payments\\PaymentAdapterFactory.cs"}, {"SourceFile": "Models\\Payments\\PaymentAuthorizationResponse.cs"}, {"SourceFile": "Models\\PaypalConnect.cs"}, {"SourceFile": "Models\\Paypal\\PaypalCustomButtonParser.cs"}, {"SourceFile": "Models\\Paypal\\PaypalIpnCall.cs"}, {"SourceFile": "Models\\Plimus\\BluesnapBatchOrder.cs"}, {"SourceFile": "Models\\Plimus\\BluesnapConfiguration.cs"}, {"SourceFile": "Models\\Plimus\\BluesnapLocalConfiguration.cs"}, {"SourceFile": "Models\\Plimus\\BluesnapShopper.cs"}, {"SourceFile": "Models\\Plimus\\BluesnapSubscription.cs"}, {"SourceFile": "Models\\Plimus\\IBluesnapConfiguration.cs"}, {"SourceFile": "Models\\Plimus\\Order.cs"}, {"SourceFile": "Models\\Plimus\\OrderToPlace.cs"}, {"SourceFile": "Models\\Plimus\\BlueSnapApi.cs"}, {"SourceFile": "Models\\Plimus\\Shopper.cs"}, {"SourceFile": "Models\\Plimus\\WebInfo.cs"}, {"SourceFile": "Models\\PriceRules\\TaxesManager.cs"}, {"SourceFile": "Models\\ProductFeedStatusesEnum.cs"}, {"SourceFile": "Models\\ReferAFriend\\RffDefaultTextsModel.cs"}, {"SourceFile": "Models\\ReferAFriend\\RffEmailsModel.cs"}, {"SourceFile": "Models\\ReferAFriend\\RffOrderModel.cs"}, {"SourceFile": "Models\\ReferAFriend\\RffStatsModel.cs"}, {"SourceFile": "Models\\ReferAFriend\\RffOfferModel.cs"}, {"SourceFile": "Models\\Reports\\CouponPopPerformaceModel.cs"}, {"SourceFile": "Models\\Reports\\PieChartData.cs"}, {"SourceFile": "Models\\Reports\\ReportData.cs"}, {"SourceFile": "Models\\Reports\\TrafficBoosterInitialStoreAnalysisModel.cs"}, {"SourceFile": "Models\\Reports\\TrafficBoosterPerformanceModel.cs"}, {"SourceFile": "Models\\RevenueRankManager.cs"}, {"SourceFile": "Models\\ShopAchivements\\Refactor_GASourceAchivements.cs"}, {"SourceFile": "Models\\ShopAchivements\\ShopAchivementsManager.cs"}, {"SourceFile": "Models\\ShopAttributes\\Attributes.cs"}, {"SourceFile": "Models\\ShopAttributes\\ShopAttributesManager.cs"}, {"SourceFile": "Models\\Shopify\\IDataTranslator.cs"}, {"SourceFile": "Models\\Shopify\\JsonDataTranslator.cs"}, {"SourceFile": "Models\\Shopify\\ShopifyAPIAuthorizer.cs"}, {"SourceFile": "Models\\Shopify\\ShopifyApiClient.cs"}, {"SourceFile": "Models\\Shopify\\ShopifyAuthorizationState.cs"}, {"SourceFile": "Models\\Shopify\\ShopifyCrawler.cs"}, {"SourceFile": "Models\\DataProviders\\ShopifyEntities\\ShopifyOrder.cs"}, {"SourceFile": "Models\\Shopify\\ShopifyDiscountsManager.cs"}, {"SourceFile": "Models\\Shopify\\ShopifyThemesManager.cs"}, {"SourceFile": "Models\\ShopManipulations\\DowngradeManager.cs"}, {"SourceFile": "Models\\ShopManipulations\\DowngradeQueueStatuses.cs"}, {"SourceFile": "Models\\ShopManipulations\\StorageManager.cs"}, {"SourceFile": "Models\\ShoppingFeed\\CustomFeed.cs"}, {"SourceFile": "Models\\ShoppingFeed\\CustomFeedsManager.cs"}, {"SourceFile": "Models\\ShoppingFeed\\FacebookRealEstateAdsFeedItem.cs"}, {"SourceFile": "Models\\ShoppingFeed\\FeedsManager.cs"}, {"SourceFile": "Models\\ShoppingFeed\\GoogleCDRMRealEstateFeedItem.cs"}, {"SourceFile": "Models\\ShoppingFeed\\ShoppingFeedItem.cs"}, {"SourceFile": "Models\\ShoppingFeed\\ShoppingFeedObject.cs"}, {"SourceFile": "Models\\ShopsRefactor\\AbstructShopRefactor.cs"}, {"SourceFile": "Models\\ShopsRefactor\\Refactor_ShopifyOnlineStorePromosions.cs"}, {"SourceFile": "Models\\ShopsRefactor\\ShopRefactorResults.cs"}, {"SourceFile": "Models\\ShopsRefactor\\DataToRefactor.cs"}, {"SourceFile": "Models\\ShopsRefactor\\IShopRefactor.cs"}, {"SourceFile": "Models\\ShopsRefactor\\ShopsRefactorManager.cs"}, {"SourceFile": "Models\\Sitemaps\\SitemapsParser.cs"}, {"SourceFile": "Models\\SlackIntegration\\SlackMessenger.cs"}, {"SourceFile": "Models\\Square\\ISquareConfigurations.cs"}, {"SourceFile": "Models\\Square\\SquareApiHelper.cs"}, {"SourceFile": "Models\\Square\\SquareConfigurations.cs"}, {"SourceFile": "Models\\Square\\SquareConnectorException.cs"}, {"SourceFile": "Models\\Square\\SquareLocalConfigurations.cs"}, {"SourceFile": "Models\\Square\\SquareSandBoxConfigurations.cs"}, {"SourceFile": "Models\\StoreyaError.cs"}, {"SourceFile": "Models\\StoreYaReports\\StoreYaDashboardData.cs"}, {"SourceFile": "Models\\StoreYaReports\\StoreYaDashboardManager.cs"}, {"SourceFile": "Models\\StoreYaWebResponse.cs"}, {"SourceFile": "Models\\Sync\\RawProductImageComparer.cs"}, {"SourceFile": "Models\\TbInternalTasks\\TbInternalTasksCategories.cs"}, {"SourceFile": "Models\\TbInternalTasks\\TbInternalTasksManager.cs"}, {"SourceFile": "Models\\TbInternalTasks\\TbInternalTasksStatuses.cs"}, {"SourceFile": "Models\\TbInternalTasks\\TbInternalTasksTypes.cs"}, {"SourceFile": "Models\\Tracking\\TrackingDbHelper.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AccountsFlowEvents.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdAccount.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdCampaign.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\Capper\\CapperAction.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\Capper\\CapperChecker.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\PaymentSystems.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdCampaignStatuses.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdResponse.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdServer.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdRequestData.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdwordAdGroupModel.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdwordAdModel.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdwordCampaignModel.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdWordsPixelStatuses.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\BannerModel.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\BannerTemplates\\Banner300x250Portrait.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\AdCampaignPerformance.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\DashboardPieFormatter.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\DispatchonClient.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\PerformanceAlertsManager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\ProductFeedManager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbAgreementsManager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbAgreementTypes.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbAppManager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbDashboardHelper.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TBFeeHistoryHelper.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbPerformanceRow2.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbReports2Manager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TrafficBoosterPricingModel.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TrafficChannels\\AccountTbBudget.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TrafficChannels\\TbChannelManager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbBigSpendersManager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbCampaignPerformanceManager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbCampaignsManager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbCampaignTypes.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbCampaignStatuses.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbDashboardDataSources.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbPaymentSystems.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbReportManager.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbSettingsHelper.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TbUtmCampaigns.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TrafficBoostersDbHelper.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TrafficBoosterStatuses.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TrafficChannelsTypes.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TrafficChannels\\TbChannelStatuses.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\TrafficChannels\\TbFeeHelper.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\Upgrades\\UpgradeEmailNewViewModel.cs"}, {"SourceFile": "Models\\TrafficBoosterModels\\Upgrades\\UpgradeEmailFullViewModel.cs"}, {"SourceFile": "Models\\TrustBadges\\TrustBadgesHelper.cs"}, {"SourceFile": "Models\\TrustBadges\\TrustBudgetSettings.cs"}, {"SourceFile": "Models\\Updown\\UpdownApi.cs"}, {"SourceFile": "Models\\UserIntelligence\\CreateUserJson.cs"}, {"SourceFile": "Models\\UserIntelligence\\EmailOctopus.cs"}, {"SourceFile": "Models\\UserIntelligence\\GetIntercomUserJson.cs"}, {"SourceFile": "Models\\UserIntelligence\\GetIntercomUserJsonNew.cs"}, {"SourceFile": "Models\\UserIntelligence\\IntercomAPI.cs"}, {"SourceFile": "Models\\UserIntelligence\\MadMimiAPI.cs"}, {"SourceFile": "Models\\UserIntelligence\\PostEventJson.cs"}, {"SourceFile": "Models\\UserTreatment\\RelationStatus.cs"}, {"SourceFile": "Models\\UserTreatment\\StatePicker.cs"}, {"SourceFile": "Models\\UserTreatment\\UserManager.cs"}, {"SourceFile": "Models\\WallaShopsManager.cs"}, {"SourceFile": "Models\\WebsiteAnalyser\\AbstractValidator.cs"}, {"SourceFile": "Models\\WebsiteAnalyser\\FanPageValidator.cs"}, {"SourceFile": "Models\\WebsiteAnalyser\\Helper.cs"}, {"SourceFile": "Models\\WebsiteAnalyser\\IValidator.cs"}, {"SourceFile": "Models\\WebsiteAnalyser\\LanguageDetectorMAnager.cs"}, {"SourceFile": "Models\\WebsiteAnalyser\\OnlineSeoValidator.cs"}, {"SourceFile": "Models\\WebsiteAnalyser\\SecurityValidator.cs"}, {"SourceFile": "Helpers\\BitmapImageUtils.cs"}, {"SourceFile": "Helpers\\BrowserHelper.cs"}, {"SourceFile": "Helpers\\ConfigurationHelper.cs"}, {"SourceFile": "Helpers\\DataHelper.cs"}, {"SourceFile": "Helpers\\EmailHelper.cs"}, {"SourceFile": "Helpers\\FbHelper.cs"}, {"SourceFile": "Helpers\\HttpHelper.cs"}, {"SourceFile": "Helpers\\HttpPostHelper.cs"}, {"SourceFile": "Helpers\\ImageProcessor.cs"}, {"SourceFile": "Helpers\\ImagesDownloader.cs"}, {"SourceFile": "Helpers\\ImageUtilities.cs"}, {"SourceFile": "Helpers\\Log4NetHelper.cs"}, {"SourceFile": "Helpers\\MD5Helper.cs"}, {"SourceFile": "Helpers\\PaypalHelper.cs"}, {"SourceFile": "Helpers\\BlueSnapHelper.cs"}, {"SourceFile": "Helpers\\RandomNumberHelper.cs"}, {"SourceFile": "Helpers\\StringFormatingExtention.cs"}, {"SourceFile": "Helpers\\TextManipulationHelper.cs"}, {"SourceFile": "Helpers\\UrlPathHelper.cs"}, {"SourceFile": "Models\\BackgroundTasks\\BackgroundManager.cs"}, {"SourceFile": "Models\\BackgroundTasks\\BackgroundTasksStatuses.cs"}, {"SourceFile": "Models\\BackgroundTasks\\BackgroundTasksTypes.cs"}, {"SourceFile": "Models\\BackgroundTasks\\IBgTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\InstagramStyleImagesCreator.cs"}, {"SourceFile": "Models\\BackgroundTasks\\PostTaskEmails.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\AbstructPrimaryImportTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\AbstructSyncTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\BgTaskFactory.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\eBay_SyncTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\ShopifyApi_SyncTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\ShopifyApi_PrimaryImportTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\MagentoCsv_SyncTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\MagentoApi_SyncTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\MagentoApi_PrimaryImportTask.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\MagentoApi_ImportProductStatuses.cs"}, {"SourceFile": "Models\\BackgroundTasks\\Tasks\\MagentoCsv_ImagesDownloadTask.cs"}, {"SourceFile": "Models\\DataProviders\\AbstructDataExtractorStrategy.cs"}, {"SourceFile": "Models\\DataProviders\\BaseUrlFinder.cs"}, {"SourceFile": "Models\\DataProviders\\DataProviderFactory.cs"}, {"SourceFile": "Models\\DataProviders\\DataSources.cs"}, {"SourceFile": "Models\\DataProviders\\IDataExtractorStrategy.cs"}, {"SourceFile": "Models\\DataProviders\\PrestaShopEntities\\Categories\\JsonPrestaShopCategories.cs"}, {"SourceFile": "Models\\DataProviders\\PrestaShopEntities\\Language\\JsonPrestaShopLanguage.cs"}, {"SourceFile": "Models\\DataProviders\\PrestaShopEntities\\PrestaShopCommon.cs"}, {"SourceFile": "Models\\DataProviders\\PrestaShopEntities\\PrestaShopProductData.cs"}, {"SourceFile": "Models\\DataProviders\\PrestaShopEntities\\Products\\JsonPrestaShopProducts.cs"}, {"SourceFile": "Models\\DataProviders\\PrestaShopEntities\\Product\\JsonPrestaShopProduct.cs"}, {"SourceFile": "Models\\DataProviders\\ShopifyApi.cs"}, {"SourceFile": "Models\\DataProviders\\ShopifyEntities\\ProductEntity.cs"}, {"SourceFile": "Models\\FbActions\\ActionsByOther.cs"}, {"SourceFile": "Models\\FbActions\\FbActionsMaganer.cs"}, {"SourceFile": "Api\\FbObjectTypes.cs"}, {"SourceFile": "Models\\Galleries\\IstagramApiHelper.cs"}, {"SourceFile": "Models\\Galleries\\IstagramDemoImage.cs"}, {"SourceFile": "Models\\Galleries\\IstagramDemoImagesHelper.cs"}, {"SourceFile": "Models\\Galleries\\InstagramJsonClasses.cs"}, {"SourceFile": "Models\\Import\\ImportManager.cs"}, {"SourceFile": "Models\\Layouts\\CustomViewValidator.cs"}, {"SourceFile": "Models\\Payments\\PlanTypes.cs"}, {"SourceFile": "Models\\PriceRules\\PriceRulesManager.cs"}, {"SourceFile": "Models\\ShopManipulations\\ShopManager.cs"}, {"SourceFile": "Models\\Sync\\SyncManager.cs"}, {"SourceFile": "Models\\Sync\\SyncScheduler.cs"}, {"SourceFile": "Models\\WebsiteAnalyser\\SocialPluginsValidator.cs"}, {"SourceFile": "Models\\WebsiteAnalyser\\ValidationManager.cs"}, {"SourceFile": "Models\\Wix\\IWixConfigurations.cs"}, {"SourceFile": "Models\\Wix\\WixApiHelper.cs"}, {"SourceFile": "Models\\Wix\\WixConfigurations.cs"}, {"SourceFile": "Models\\Wix\\WixLocalConfigurations.cs"}, {"SourceFile": "Models\\Wix\\WixDevConfigurations.cs"}, {"SourceFile": "Models\\Wix\\WixTransactionsHelper.cs"}, {"SourceFile": "Models\\Wix\\WixWebhookParser.cs"}, {"SourceFile": "Models\\WooCommerce\\WooCommerceHelper.cs"}, {"SourceFile": "Models\\WebstoreParser.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resource.Checkout.en.Designer.cs"}, {"SourceFile": "Properties\\Resource.Checkout.es.Designer.cs"}, {"SourceFile": "Properties\\Resource.Checkout.fr.Designer.cs"}, {"SourceFile": "Properties\\Resource.Layout.en.Designer.cs"}, {"SourceFile": "Properties\\Resource.Layout.es.Designer.cs"}, {"SourceFile": "Properties\\Resource.Layout.fr.Designer.cs"}, {"SourceFile": "Properties\\Resource.TbDashboard.en.Designer.cs"}, {"SourceFile": "Properties\\Resource.TbDashboard.es.Designer.cs"}, {"SourceFile": "Properties\\Resource.TbDashboard.fr.Designer.cs"}, {"SourceFile": "Properties\\Resource.TbSettings.es.Designer.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Resource.TbSettings.fr.Designer.cs"}, {"SourceFile": "Properties\\Resource.TbSettings.en.Designer.cs"}, {"SourceFile": "SelfTests\\ReferencesTestRun.cs"}, {"SourceFile": "SelfTests\\ReferencesTest.cs"}, {"SourceFile": "Utils\\ConsoleTable.cs"}, {"SourceFile": "Utils\\EnumUtil.cs"}, {"SourceFile": "Utils\\ScriptFlowLogger.cs"}, {"SourceFile": "Utils\\SelfTests.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\dev\\storeya\\packages\\AWSSDK.Core.3.7.400.79\\lib\\net45\\AWSSDK.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\AWSSDK.S3.3.7.411.7\\lib\\net45\\AWSSDK.S3.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\BouncyCastle.Cryptography.2.5.0\\lib\\net461\\BouncyCastle.Cryptography.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\createsend-dotnet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\CsvHelper.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\CTCT.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\EntityFramework.6.5.1\\lib\\net45\\EntityFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\EntityFramework.6.5.1\\lib\\net45\\EntityFramework.SqlServer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\HtmlAgilityPack.1.11.72\\lib\\Net45\\HtmlAgilityPack.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\jose-jwt.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\Kent.Boogaart.HelperTrinity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\Kent.Boogaart.KBCsv.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\log4net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\MailChimp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\nQuant.1.0.3\\lib\\net40\\nQuant.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\NReco.PdfGenerator.1.2.1\\lib\\net45\\NReco.PdfGenerator.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\Postmark.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\RestSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\System.Buffers.4.6.0\\lib\\net462\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.EnterpriseServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.IO.Compression.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.IO.Compression.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\System.Memory.4.6.0\\lib\\net462\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\System.Numerics.Vectors.4.6.0\\lib\\net462\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Runtime.Caching.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\dev\\storeya\\packages\\System.Runtime.CompilerServices.Unsafe.6.1.0\\lib\\net462\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ServiceModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\Debug\\Storeya.Core.dll", "OutputItemRelativePath": "Storeya.Core.dll"}, {"OutputItemFullPath": "C:\\dev\\storeya\\trunk\\Storeya.Core\\bin\\Debug\\Storeya.Core.pdb", "OutputItemRelativePath": "Storeya.Core.pdb"}], "CopyToOutputEntries": []}