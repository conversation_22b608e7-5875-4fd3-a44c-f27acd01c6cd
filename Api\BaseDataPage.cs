﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Api
{
    public class BaseDataPage
    {
        public List<FbActionEntity> WantedProducts { get; set; }
        public List<FbActionEntity> OwnedProducts { get; set; }
        public string FacebookPageID { get; set; }
        public string StoreLocale { get; set; }
        public string FacebookAppID { get; set; }
        public long CurrentFbProfileID { get; set; }
        public BaseDataPage()
        {
            this.FacebookPageID = "0";
            this.WantedProducts = new List<FbActionEntity>();
            this.OwnedProducts = new List<FbActionEntity>();
        }
    }
}
