﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
//using Storeya.Core.Helpers;

namespace Storeya.Core.Api
{
    public class CatalogPageData : BaseDataPage
    {
        public CatalogPageData()
        {
            this.CustomSettings = new CustomSettings();            
            this.Colors = new Colors();
            this.FacebookPageID = "0";
        }
        public int ShopID { get; set; }
        public string CustomPriceLabel { get; set; }
        public string MoreInfoLabel { get; set; }
        //public string ShopIdText
        //{
        //    get
        //    {
        //        return SequenceHelper.Encode(this.ShopID);
        //    }
        //}

        /// <summary>
        /// Collection of ProductEntity objects
        /// </summary>
        public List<ProductEntity> Products { get; set; }
        public void AddProduct(Product product)
        {
            ProductEntity entity = new ProductEntity(product);
            entity.RowNumber = this.Products.Count + 1;

            this.Products.Add(entity);
        }

        public CustomSettings CustomSettings { get; set; }
        public Colors Colors { get; set; }

        public IHtmlString GetFormatedPrice(string price)
        {
            decimal decimalPrice = 0;
            if (decimal.TryParse(price, out decimalPrice))
            {
                return new HtmlString(FormatPrice(decimalPrice, this.CustomSettings.CurrencySymbol, this.CustomPriceLabel, this.CustomSettings.PriceCurrencyPattern));
            }
            return new HtmlString(price);
        }

        public static string FormatPrice(decimal? price, string currencySymbol, string customLabel = null, string requiredPriceHtmlPattern = null)
        {
            price = price ?? 0;
            if (price == -1)
            {
                return "Contact us<span></span>";
            }
            else if (price == 0)
            {
                if (!string.IsNullOrEmpty(customLabel))
                {
                    return string.Format("{0}<span></span>", customLabel);
                }
                else
                {
                    return "View Price<span></span>";
                }                
            }

            string priceHtmlPattern;

            if (string.IsNullOrEmpty(requiredPriceHtmlPattern))
            {
                if (currencySymbol.Length <= 2)
                {
                    priceHtmlPattern = "{1}{0:#,0.<span>00</span>}";
                }
                else
                {
                    priceHtmlPattern = "{0:#,0.<span>00</span>} {1}";
                }
            }
            else
            {
                priceHtmlPattern = requiredPriceHtmlPattern;
            }

            string output = string.Format(priceHtmlPattern, price, currencySymbol);
            //output = output.Replace(".<span>00</span>", "<span></span>");

            return output;
        }

        public string GetSrcLikePlugin(string itemExternalUrl)
        {
            //
            //"//www.facebook.com/plugins/like.php?locale=" + this.StoreLocale + "&href=" + HttpUtility.UrlEncode(itemExternalUrl) +"&amp;layout=button_count&amp;show_faces=false&amp;action=like";   //locale=" + this.StoreLocale + "&layout=button_count&amp;
            string srcLikePlugin = "//www.facebook.com/plugins/like.php?locale=" + this.StoreLocale + "&layout=button_count&amp;&href=" + HttpUtility.UrlEncode(itemExternalUrl) + "&amp;show_faces=false&amp;action=like";
            return srcLikePlugin;
        }

        //public string GetFbProductPage(int productID, int shopID)
        //{
        //    string s = Utils.FbProductPageToShare(productID, Convert.ToInt64(this.FacebookPageID), SequenceHelper.Encode(shopID));

        //    if (HttpContext.Current.Request.Url.IsLoopback)
        //    {
        //        return "https://www.google.com/?" + HttpUtility.UrlEncode(s);
        //    }
        //    return s;
        //}

        public bool IncludeDynamicHeight { get; set; }

    }

    public class CustomSettings
    {
        public string BuyNowButtonText { get; set; }
        public string TaxesText { get; set; }

        public string CurrencySymbol { get; set; }

        public string FacebookShare { get; set; }

        public string FreeProductCustomLabel { get; set; }

        public string PriceCurrencyPattern { get; set; }

        //public string HeaderImage { get; set; }

        //later custom field can be supported
        //public string Custom1 { get; set; }
    }

    public class Colors
    {
        public string Headlines { get; set; }
        public string Price { get; set; }
        public string BuyNow { get; set; }
        public string CategoryOutline { get; set; }
        public string Background { get; set; }
    }

    public class Utils
    {
        public static string FbProductPageToShare(int productID, long? fbPageID, string shopID = null)
        {
            //HttpContext context = HttpContext.Current;

            string pattern = "https://www.storeya.com/shop/product?p={0}&id={1}&fp={2}";
            return string.Format(pattern, productID, shopID, fbPageID);
        }

    }
}
