﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;
using System.Web;

namespace Storeya.Core.Api
{
    public static class Extensions
    {
        public static string TruncateTo(this string input, int lenght)
        {
            if (input == null)
            {
                return "";
            }
            return TextManipulationHelper.Truncate(input, lenght);
        }

        public static string AsJsParameter(this string input)
        {
            return input.Replace("'", "\\'");
        }

        public static string AsUrlParam(this string input)
        {
            return HttpUtility.UrlEncode(input);
        }

    }
}
