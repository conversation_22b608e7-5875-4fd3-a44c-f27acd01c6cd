﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Storeya.Core.Models.TrafficBoosterModels;


namespace Storeya.Core.Api
{
    public enum GoggleCouponStatuses
    {
        Applied = 1,
        AlredyExists = 10,
        NotActiveTBAlready = 15
    }

    public enum GoggleCouponTypes
    {
        Regular = 1,
        Shopping = 2
    }

    public class GoggleCouponToApply
    {
        public int StoreYaCouponID { get; set; }
        public int? ShopID { get; set; }
        public string AWAccountID { get; set; }
        public string Coupon { get; set; }
        public long? Ocid { get; set; }
    }

    public static class GoogleCouponDBHelper
    {
        public static void FreeGoogleCouponOfCancelledTbs()
        {
            int free = 0;

            List<GoggleCoupon> couponsToFree = null;
            using (StoreYaEntities db = new StoreYaEntities())
            {
                couponsToFree = (from gc in db.GoggleCoupons.Where(x => !x.Status.HasValue && x.ShopID.HasValue && !x.AppliedAt.HasValue
                                                                                           && (x.CouponType == (int)GoggleCouponTypes.Shopping || x.CouponType == (int)GoggleCouponTypes.Regular))
                                 join tb in db.TrafficBoosters.Where(t => t.ShopID > 0 && t.Status == (byte)TB_AW_STATUS.CANCELED) on gc.ShopID equals tb.ShopID
                                 select gc).ToList();
            }

            if (couponsToFree.Any())
            {
                ConsoleAppHelper.WriteLog(string.Format("Total Google Coupons to free: {0}", couponsToFree.Count), 0);

                foreach (var couponToFree in couponsToFree)
                {
                    try
                    {
                        using (StoreYaEntities db = new StoreYaEntities())
                        {
                            GoggleCoupon coupon = db.GoggleCoupons.Single(x => x.ID == couponToFree.ID);
                            coupon.ShopID = null;

                            db.SaveChanges();

                            free++;
                        }
                    }
                    catch (Exception e)
                    {
                        ConsoleAppHelper.WriteError("Failed to free coupon.", e, couponToFree.ShopID ?? 0);
                    }
                }

                ConsoleAppHelper.WriteLog(string.Format("Succeded to free: {0}", free), 0);
            }
        }


        public static void UpdateGoogleCouponStatus(int shopId,string couponCode, int status)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            GoggleCoupon coupon = db.GoggleCoupons.Where(x => x.ShopID == shopId && x.Coupon.ToUpper() == couponCode.ToUpper()).SingleOrDefault();
            if (coupon != null)
            {
                coupon.Status = status;

                if (status == (int)GoggleCouponStatuses.AlredyExists)
                {
                    coupon.AppliedAt = new DateTime(2018, 1, 1);
                }
                else
                {
                    coupon.AppliedAt = DateTime.Now;
                }

                db.SaveChanges();
            }
        }

        //public static void UpdateGoogleCouponStatus(int id, int status)
        //{
        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    GoggleCoupon coupon = db.GoggleCoupons.Where(x => x.ID == id).SingleOrDefault();
        //    if (coupon != null)
        //    {
        //        coupon.Status = status;

        //        if (status == (int)GoggleCouponStatuses.AlredyExists)
        //        {
        //            coupon.AppliedAt = new DateTime(2018, 1, 1);
        //        }
        //        else
        //        {
        //            coupon.AppliedAt = DateTime.Now;
        //        }

        //        db.SaveChanges();
        //    }
        //}

        //public static int? AssignShopID(int shopID, int type)
        //{
        //    int? assignedCouponID = null;
        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    GoggleCoupon coupon = db.GoggleCoupons.Where(x => !x.ShopID.HasValue && x.CouponType == type && !x.Status.HasValue).FirstOrDefault();
        //    if (coupon != null)
        //    {
        //        coupon.ShopID = shopID;
        //        coupon.AssignShopIDAt = DateTime.Now;
        //        db.SaveChanges();
        //        assignedCouponID = coupon.ID;
        //    }
        //    else
        //    {
        //        string subject = string.Format("No {0} GoggleCoupons in DB - {1}", ((GoggleCouponTypes)type).ToString(), DateTime.Now.ToShortDateString());
        //        string body = "Please, add google coupons of the required type to Data Base.";
        //        EmailHelper.SendEmail("<EMAIL>", subject, body);
        //    }
        //    return assignedCouponID;
        //}

        //public static void AssignGoogleCoupon(int shopID, GoggleCouponTypes couponType)
        //{
        //    int? googleShoppingCouponID = GoogleCouponDBHelper.AssignShopID(shopID, (int)couponType);
        //    if (googleShoppingCouponID.HasValue)
        //    {
        //        ConsoleAppHelper.WriteLog(string.Format("{0} Google coupon is assigned. StoreYaCouponID: {1}", couponType.ToString(), googleShoppingCouponID.Value), shopID);
        //    }
        //    else
        //    {
        //        ConsoleAppHelper.WriteLog(string.Format("Failed to assign {0} Google coupon.", couponType.ToString()), shopID);
        //        //sb.Append(string.Format("{0} - Failed to assign {1} Google coupon.", shopID, couponType.ToString())).Append("<br/>");

        //        EmailHelper.SendEmail("<EMAIL>", string.Format("Failed to assign {0} Google coupon to ShopID {1}", couponType.ToString(), shopID), null);
        //    }
        //}
    }
}
