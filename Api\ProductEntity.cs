﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using Storeya.Core.Helpers;
using System.Text.RegularExpressions;


namespace Storeya.Core.Api
{
    public static class ProductEntityHelper
    {
        public static List<FbActionEntity> GetAllProductActions(int productID)
        {
            List<FbActionEntity> actions = new List<FbActionEntity>();

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var actionsFromDB = db.FbActions.Where(a => a.ObjectType == 1 && a.ObjectID == productID); //products actions
            foreach (var action in actionsFromDB)
            {
                FbActionEntity entity = new FbActionEntity()
                {
                    FbProfileID = action.FbProfileID,
                    FbActionID = action.FbActionID,
                    ObjectID = action.ObjectID,
                    ObjectType = (FbObjectTypes)action.ObjectType,
                    ActionType = (FbActionTypes)action.ActionType,
                    ActionDate = action.InsertedAt
                };

                entity.Name = GetName(entity.FbProfileID); //better to save name at FbActions record addition

                actions.Add(entity);
            }
            return actions;
        }

        private static string GetName(long fbProfileID)
        {
            string name = null;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            FbProfile fbProfile = db.FbProfiles.Where(a => a.FbProfileID == fbProfileID).FirstOrDefault();
            if (fbProfile != null)
            {
                name = fbProfile.UserName;
            }
            return name;
        }

        
    }

    public class ProductEntity
    {
        public int ID { get; set; }
        public string OriginalProductID { get; set; }
        /// <summary>
        /// Row number in the list of products. This property together with IsLastColumnInRow() method can be user to identify last product in the row.
        /// </summary>
        public int RowNumber { get; set; }
        public int ShopID { get; set; }

        /// <summary>
        /// Product name
        /// </summary>
        public string Name { get; set; }

        public List<ProductImageEntity> Images { get; set; }

        /// <summary>
        /// Description of the products that extracted from HtmlDescription but all Html tags removed
        /// </summary>
        public string Description { get; set; }
        internal string HtmlDescriptionString { get; set; }

        /// <summary>
        /// Description of the product from Magento
        /// </summary>
        public HtmlString HtmlDescription
        {
            get
            {
                return new HtmlString(HtmlDescriptionString);
            }
        }
        internal string ImagePath { private get; set; }
        public int CommentsAmount { get; set; }

        public string CategoryName { get; set; }
        public int CategoryID { get; set; }


        public decimal PriceDecimal { get; set; }

        public int? IsFeatured { get; set; }

        public int? Inventory { get; set; }

        public string Price
        {
            get { return Decimal.Round(this.PriceDecimal, 2).ToString(); }
        }

        internal string ExternalSiteUrl { get; set; }

        public ProductEntity(Product product)
        {
            this.ShopID = product.ShopID;
            this.ID = product.ID;

            this.OriginalProductID = product.OriginalProductID;

            this.Inventory = product.Inventory;

            this.Name = product.Name;
            this.Description = product.Description;
            this.HtmlDescriptionString = product.HtmlDescription;
            this.ImagePath = product.LocalImage;
            this.CommentsAmount = product.CommentsAmount ?? 0;
            this.PriceDecimal = product.Price ?? 0;
            this.IsFeatured = product.IsFeatured ?? 0;
            if ((product.DiscountPrice ?? 0) != 0)
            {
                this.PriceDecimal = product.DiscountPrice ?? 0;
            }

            this.CategoryID = product.CategoryID;
            this.ExternalSiteUrl = product.OutUrl;

            //if (product.Category!=null)
            //{
            //    this.CategoryName = product.Category.Name;
            //}


        }






        /// <summary>
        /// Method the calculates if the specific product is the last product in the row
        /// </summary>
        /// <param name="columnsInRow">Amount of products in the single row (amount of columns)</param>
        /// <returns></returns>
        public bool IsLastColumnInRow(int columnsInRow)
        {
            return (this.RowNumber % columnsInRow) == 0;
        }

        /// <summary>
        /// Product image
        /// </summary>
        public string Image
        {
            get
            {
                string imagePath = null;
                if (this != null)
                    imagePath = this.ImagePath;

                imagePath = ImagePathHelper.GetProductImageUrl(imagePath); 

                return imagePath;

            }
        }
        public string ImageAbsolutePath
        {
            get
            {
                if (this.Image.Contains("http://") || this.Image.Contains("https://"))
                    return this.Image;
                else
                    return HttpHelper.GetCurrentDomain() + this.Image;
            }
        }

        public string ProductPageUrlToPin
        {
            get
            {
                if (string.IsNullOrEmpty(this.ExternalSiteUrl))
                {
                    return this.InternalProductPageUrlAbsolute;
                }

                return this.ExternalSiteUrl;
            }
        }

        public string ProductPageUrlViaStoreya
        {
            get
            {
                if (string.IsNullOrEmpty(this.ExternalSiteUrl))
                {
                    return this.InternalProductPageUrlAbsolute;
                }

                if (IsSkipOutUrl(this.ShopID))
                {
                    //return url direct to extenal site
                    return this.ExternalSiteUrl;
                }

                string url = StoreyaProductOutUrl(this.ShopID, this.ID);
                return url;
            }
        }

        private bool IsCheckoutInStoreYa(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopPaymentSetting paymentSettings = db.ShopPaymentSettings.Where(sp => sp.ShopID == shopID).SingleOrDefault();
            if (paymentSettings != null && !string.IsNullOrEmpty(paymentSettings.PaypalEmail))
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// Url to the product page at magento shop
        /// </summary>
        public string ExternalUrl
        {
            // Final product page url

            get
            {

                if (string.IsNullOrEmpty(this.ExternalSiteUrl))
                {
                    return this.InternalProductPageUrlAbsolute;
                }

                if (IsSkipOutUrl(this.ShopID))
                {
                    //return url direct to extenal site
                    return this.ExternalSiteUrl;
                }

                string url = StoreyaOutUrl(this.ShopID, this.ID);
                return url;
            }
        }

        private static string StoreyaProductOutUrl(int shopID, int prodID)
        {
            string url = string.Format(HttpHelper.GetCurrectBaseUrl() + "out/product?p={0}&id={1}", prodID, SequenceHelper.Encode(shopID));
            return url;
        }

        private static string StoreyaOutUrl(int shopID, int prodID)
        {
            string url = string.Format(HttpHelper.GetCurrectBaseUrl() + "out/?p={0}&id={1}", prodID, SequenceHelper.Encode(shopID));
            if (HttpContext.Current.Request.IsLocal && FbHelper.IsFbCrawler())
            {
                //fb crawler should go to other link
                url = url.Replace("/out", "");
            }
            return url;
        }

        public string BuyNowUrl
        {
            // Url to go when Buy Now clicked
            get
            {
                //if (string.IsNullOrEmpty(this.ExternalSiteUrl))
                //{
                //    //will be used in mobile for standalone stores 
                //    
                //    string paypalPaymentLink = "/paypal/button?p={0}&s={1}";
                //    return string.Format(paypalPaymentLink, this.ID, this.ShopID);
                //}

                if (IsSkipOutUrl(this.ShopID))
                {
                    //return url direct to extenal site
                    return this.ExternalSiteUrl;
                }

                string url = StoreyaOutUrl(this.ShopID, this.ID);
                return url;
            }
        }

        /// <summary>
        /// Url to the product page inside Facebook
        /// </summary>
        public string InternalProductPageUrl
        {
            get
            {
                return CreateProductPageUrl(this.ShopID, this.ID);
                //string s = "/shop/product?p={0}&s={1}";
                //string url = string.Format(s, this.ID, this.ShopID);
                ////if (HttpContext.Current.Request.Url.IsLoopback)
                ////{
                ////    return "http://www.google.com/?" + HttpUtility.UrlEncode(url);
                ////}
                //return url;
            }
        }


        public static string CreateProductPageUrl(int shopID, int productID)
        {
            return CreateProductPageUrl(shopID, productID, 0);
        }

        public static string CreateProductPageUrl(int shopID, int productID, long fbPageID)
        {
            string s = "/shop/product?p={0}&id={1}&fp={2}&t=internal";
            string url = string.Format(s, productID, SequenceHelper.Encode(shopID), fbPageID);
            return url;
        }


        public string LinkToRelatedProduct(ProductEntity product)
        {
            if (ProductEntity.IsSkipOutUrl(this.ShopID))
            {
                return product.ExternalUrl;
            }
            else
            {
                return product.InternalProductPageUrl;
            }
        }


        public string InternalProductPageUrlAbsolute
        {
            get
            {
                if (ProductEntity.IsSkipOutUrl(this.ShopID))
                {
                    return this.ExternalSiteUrl;
                }

                string s = "/shop/product?p={0}&id={1}";
                string url = string.Format(s, this.ID, SequenceHelper.Encode(this.ShopID));

                url = HttpHelper.GetCurrectBaseUrl().Trim('/') + url;
                //if (HttpContext.Current.Request.Url.IsLoopback)
                //{
                //    return "http://www.google.com/?" + HttpUtility.UrlEncode(url);
                //}
                return url;
            }
        }

        public string InternalProductPageUrlAbsoluteEtsyAPI
        {
            get
            {
                string s = "/shop/product?p={0}&id={1}";
                string url = string.Format(s, this.OriginalProductID, SequenceHelper.Encode(this.ShopID));

                url = HttpHelper.GetCurrectBaseUrl().Trim('/') + url;
                //if (HttpContext.Current.Request.Url.IsLoopback)
                //{
                //    return "http://www.google.com/?" + HttpUtility.UrlEncode(url);
                //}
                return url;
            }
        }

        public static bool IsSkipOutUrl(int shopID)
        {
            string shopsToSkipResize = ConfigHelper.GetValue("ShopsToSkipOutUrl");
            return (!string.IsNullOrEmpty(shopsToSkipResize) && shopsToSkipResize.Split(',').Contains(shopID.ToString()));
        }

    }

    public class ProductImageEntity
    {
        public string MediaID { get; set; }
        public string ImageUrl { get; set; }

        //public string LocalUrl { get; set; }
        public string ExternalUrl { get; set; }
        public string LocalFile { get; set; }

        //public string ImageAbsolutePath
        //{
        //    get
        //    {
        //        return HttpHelper.GetCurrentDomain() + this.ImageUrl;
        //    }
        //}
    }

}
