﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using Storeya.Core.Helpers;

namespace Storeya.Core.Api
{
    public class ProductPageData: BaseDataPage
    {
        public int CurrentImageIndex { get; set; }
        public ProductEntity Product { get; set; }
        //public string FbAccessToken { get; set; }
        //public List<FbActionEntity> OtherOwners { get; set; }
        //public List<FbActionEntity> OtherWanters { get; set; }
        public List<FbActionEntity> FbActions { get; set; }

        public string ShopIdText
        {
            get
            {
                return SequenceHelper.Encode(this.Product.ShopID);
            }
        }
        public CustomSettings CustomSettings { get; set; }
        public Colors Colors { get; set; }

        public ProductPageData()
        {
            this.CustomSettings = new CustomSettings();
            this.Colors = new Colors();
            //OtherOwners = new List<FbActionEntity>();
            //OtherWanters = new List<FbActionEntity>();
            this.FbActions = new List<FbActionEntity>();
        }
        

        public IHtmlString GetFormatedPrice(string price)
        {
            decimal decimalPrice = 0;
            if (decimal.TryParse(price, out decimalPrice))
            {
                return new HtmlString(CatalogPageData.FormatPrice(decimalPrice, this.CustomSettings.CurrencySymbol, null, this.CustomSettings.PriceCurrencyPattern));
            }
            return new HtmlString(price);
        }

        public string PromotionAlert { get; set; }
    }
}
