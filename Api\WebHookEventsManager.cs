﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;
using System.Web.Script.Serialization;
using System.Net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Storeya.Core.Api
{
    public static class WebHookEventsManager
    {
        public static PostWebHooksEventsResponse PostWebHookEvent(int shopID, int userID, int appID, WebHookEventType eventType)
        {
            PostWebHooksEventsResponse parsedResponse = null;
            try
            {             
                WebHooksEventsPostModel eventToPost = new WebHooksEventsPostModel()
                {
                    EncodedUserID = SequenceHelper.Encode(userID),
                    EncodedShopID = SequenceHelper.Encode(shopID),
                    EventType = (int)eventType               
                };

                string internalApiKey = ConfigHelper.GetValue("StoreyaPrivateKey");
                string apiUrl = ConfigHelper.GetValue("Api_Url");

                WebClient wc = new WebClient();
                wc.Headers.Add("Authorization", internalApiKey);
                wc.Headers.Add("Content-Type", "application/json"); 
               
                JavaScriptSerializer js = new JavaScriptSerializer();
                dynamic response = wc.UploadString(apiUrl.Trim('/') + "/WebHooksEvents/" + SequenceHelper.Encode(appID), "POST", js.Serialize(eventToPost));
                parsedResponse = JsonConvert.DeserializeObject<PostWebHooksEventsResponse>(response);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Failed to post WebHookEvent to API server.", ex, shopID);
            }
            return parsedResponse;
        }

        public static PostWebHooksEventsResponse ReportToDeveloper(string url, int shopID, int appID, WebHookEventType eventType, string dataName, string dataValue)
        {
            PostWebHooksEventsResponse parsedResponse = new PostWebHooksEventsResponse();
            try
            {
                WebhookBaseModel eventToPost = new WebhookBaseModel()
                {
                    store_key = SequenceHelper.Encode(shopID),
                    event_type = eventType.ToString().ToLower(),
                    timestamp = "",
                    signature = "",
                    token = ""
                };

                JObject jo = JObject.FromObject(eventToPost);
                jo.Add(dataName, dataValue);
                string json = jo.ToString();

                WebClient wc = new WebClient();
                wc.Headers.Add("Content-Type", "application/json");

                JavaScriptSerializer js = new JavaScriptSerializer();
                dynamic response = wc.UploadString(url, "POST", json);
                parsedResponse.status = 200;
                try
                {
                    parsedResponse = JsonConvert.DeserializeObject<PostWebHooksEventsResponse>(response);
                }
                catch
                {
                    //response not always will come in proper format
                }
                
            }
            catch (Exception ex)
            {
                parsedResponse.status = 500;
                Log4NetLogger.Error("Failed to post WebHookEvent to API server.", ex, shopID);
            }
            return parsedResponse;
        }
    }

    public class WebHooksEventsPostModel
    {
        public string EncodedUserID;
        public string EncodedShopID;
        public int EventType;
    }
    public class PostWebHooksEventsResponse
    {
        public int status;
        public string id;
        public string error;
        public string responseString;
    }

    public class WebhookBaseModel
    {
        public string store_key { get; set; }
        public string event_type { get; set; }
        public string updated_at { get; set; }

        public string timestamp { get; set; }
        public string token { get; set; }
        public string signature { get; set; }
    }


}
