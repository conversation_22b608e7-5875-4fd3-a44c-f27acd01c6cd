﻿using Storeya.Core;
using Storeya.Core.Helpers;
using Storeya.Core.Models;
using Storeya.Core.Models.AuditTool;
using Storeya.Core.Models.DataProviders;
using Storeya.Core.Models.Marketplaces;
using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
//using Bugsnag;
using Storeya.Core.Models.AppStore;
using Storeya.Core.Models.GA;
using Storeya.Core.Models.Shopify;
using System.Data.Entity.Validation;
using Storeya.Core.Entities;
using Storeya.Core.Models.Sitemaps;
using Storeya.Core.Models.CRM;
using Storeya.Core.Models.ImageOptimizer;
using Storeya.Core.Models.ShoppingFeed;
using Storeya.Core.Utils;
using System.Drawing;
using Storeya.Core.Models.ProductDescriber;
using static System.Net.Mime.MediaTypeNames;
using Storeya.Core.Api;
using Newtonsoft.Json.Linq;
using System.Security.Policy;
using Storeya.Core.Models.OnPageAnalysis;
using System.Security.Cryptography;
using Storeya.Core.Models.Neto;
using static Storeya.Core.GlobalEFeedManager;
using Storeya.Core.Models.UserIntelligence;
using System.Diagnostics;

namespace BenchmarkHero
{
    class Program
    {
        static void Main(string[] args)
        {
            //var a = GetColorsInText("\r\n{PID:7088298950771,ShopId:479336,Name:Product title: Such a Catch Denim Shorts - Blue.,SiteData:,Intro:write a total of 90 words split into paragraphs describing the product.,Url:http://loveandlustlingerie.com/products/j11blsuchacatchdenimshortsblue,UrlText:product URL: http://loveandlustlingerie.com/products/j11blsuchacatchdenimshortsblue .,Description:description: Denim shorts with fishnet top trimming and fringe bottom details. 95% Cotton 5%Spandex Poly bag packaging. Packaging dimensions in: 13x10x1.,NewDescription:<p>These Such a Catch Denim Shorts are the perfect addition to your wardrobe. Crafted from 95% cotton and 5% spandex, they are designed to hug your curves in all the right places. The fishnet top trimming and fringe bottom details add a touch of romance to your look.</p><p>These shorts come packaged in a poly bag, making them easy to store and transport. With dimensions of 13x10x1, they are sure to fit in your bag or suitcase with ease.</p><p>These Such a Catch Denim Shorts are the perfect way to show off your curves and add a touch of romance to your look. Get yours today!</p>,EmphasePhrase:write the product description in a romantic tone.,TextStyle:Structure the output as html but do not add links.,Prefix:null,Query:write a total of 90 words split into paragraphs describing the product. write the product description in a romantic tone. Product title: Such a Catch Denim Shorts - Blue. product URL: http://loveandlustlingerie.com/products/j11blsuchacatchdenimshortsblue . description: Denim shorts with fishnet top trimming and fringe bottom details. 95% Cotton 5%Spandex Poly bag packaging. Packaging dimensions in: 13x10x1..  Structure the output as html but do not add links. ,Error:false,RetryCount:1}");
            //var b = GetColorsInText("<p>These Such a Catch Denim Shorts are the perfect addition to your wardrobe. Crafted from 95% cotton and 5% spandex, they are designed to hug your curves in all the right places. The fishnet top trimming and fringe bottom details add a touch of romance to your look.</p>\r\n<p>These shorts come packaged in a poly bag, making them easy to store and transport. With dimensions of 13x10x1, they are sure to fit in your bag or suitcase with ease.</p>\r\n<p>These Such a Catch Denim Shorts are the perfect way to show off your curves and add a touch of romance to your look. Get yours today!</p>");


            //Image image = Image.FromFile(@"c:\temp\temp.png");
            //CheckTransparency(image);
            //return;
            //Test();
            //return;
            //if (args[0]!= "StorageReport")
            //{
            //var bugsnag = new Bugsnag.Client(new Configuration("adb3a64648c7c7100c357c64966b4278"));
            //bugsnag.Notify(new Exception("Test error"));


            if (!string.IsNullOrEmpty(ConfigHelper.GetValue("IsDevelopment")))
            {

                ////SelfTests.GenearlTests();
                ////SelfTests.AWSTest();
                ////TreatBH(bugsnag);
                //string uploadPath = ImageUtilities.OptimizeFolder(@"C:\Temp\Origin\1", 0, out List<string> failedImages);
                //uploadPath = ImageUtilities.OptimizeFolder(@"C:\Temp\Origin\2", 50, out failedImages);
                //uploadPath = ImageUtilities.OptimizeFolder(@"C:\Temp\Origin\3", 90, out failedImages);
                //var devID = (ConfigHelper.GetValue("IsDevelopment") == "true") ? "2" : ConfigHelper.GetValue("IsDevelopment");
                if (ConfigHelper.GetValue("IsDevelopment") == "1")
                {
                    DevelopersSandBox.DevelopmentTest1();

                }
                else if (ConfigHelper.GetValue("IsDevelopment") == "3")
                {
                    BenchmarkImagesManager.ImageOptimizerTask(313967788);
                    return;

                    string shopPath = $@"C:\Temp\Origin\";
                    string uploadPath = ImageUtilities.OptimizeFolder(shopPath, 60, out List<string> failedImages);
                    var imageFile = ImageNamer.GetFileName("https://cdn.shopify.com/s/files/1/0788/3915/2944/files/sample1.webp?v=1755706656", out string extention, out string orignalImageName, "1"); //https://simplethia.com/cdn/shop/files/converting-hard-blue-t-shirt-show-your-kinky-religious-side-with-style-3354081.webp?v=**********&width=3840   https://cdn.shopify.com/s/files/1/0632/5600/2727/files/wavy-bob-4.webp?v=1741394609
                    string imagePath = $@"{shopPath}\{imageFile}";
                    Storeya.Core.Models.ImageOptimizer.ImageOptimizer.DownloadImage("https://cdn.shopify.com/s/files/1/0788/3915/2944/files/sample1.webp?v=1755706656", imagePath, 571465);
                    return;




                    LlmStatus llmStatus1 = LlmsFileCreator.CreateOrUpdateLlmsShopifyPage(9571713, out string fileUrl1, out string error1);
                    BenchmarksManager.CreateOrUpdateLLmPageTable(9571713, llmStatus1, error: error1);
                    if (fileUrl1 != null)
                    {
                        BenchmarksManager.UpdateLlmUrlInBH(971713, fileUrl1);
                    }



                    return;





      
                    var shApi01 = new ShopifyApiProvider("testsomeapp.myshopify.com", "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");
                    var imageIDss = shApi01.UploadProductImageGraphQL(9983301353766, shopPath + @"upload\" + "converting-hard-blue-t-shirt-show-your-kinky-religious-side-with-style-3354081-_1.webp", "webp test", 1, "ImageName - webP"); //"C:\\Users\\<USER>\\OneDrive\\Desktop\\ImportImageTest\\1-5c549da7-0080-48c6-9be2-1f83688e9752-_2.png"

                    return;



                    string fileName = $@"C:\Temp\Origin\testImage.png";
                    ImagesDownloader.Download_WithoutRescaleImage("https://simplethia.com/cdn/shop/files/converting-hard-blue-t-shirt-show-your-kinky-religious-side-with-style-3354081.webp?v=**********&width=3840", fileName, 0);

                    return;


                    var shopifyApi = ShopifyConnector.GetShopifyApiClient(313966713, (int)AppTypes.ProductDescriber);
                    var res = shopifyApi.UpdateProductSeo(9983301353766, "Image test product title", "Test image - MetaDescription");

                    BenchmarkImagesManager.ImageOptimizerTask(9561942);
                    return;

                    LlmsFileCreator.CreateOrUpdateLlmsShopifyPage(313967788, out string fileUrl, out string error);
                    return;
                    // RunBHForShop(313967788);
                    // return;
                    // //var response = HttpRequestResponseHelper.GetHttpWebResponseWithoutTryCatch("https://cbdtech.fr/llms.txt");
                    // //return;
                    //// LlmsFileCreator.CreateOrUpdateLlmsShopifyPage(9550590, out string fileUrl, out string error); //313967788
                    // return;

                    //RunBHForShop(313966713);
                    //return;

                    //var shopifyApi12 = ShopifyConnector.GetShopifyApiClient(313966713, (int)AppTypes.TrafficBooster);
                    //var ythuyhu = shopifyApi12.GetProductsCount(getOnlyPublished: false);
                    //var dede = shopifyApi12.GetProductsGraphQL(null);
                    //return;
                    //LlmsFileCreator.CreateLlmsShopifyPage(313966713); //9563693 313966713

                    //var shopifyApi = ShopifyConnector.GetShopifyApiClient(313966713, (int)AppTypes.ProductDescriber);

                    ////var prod = shopifyApi.GetProductsGraphQL(null);
                    //string res = shopifyApi.UpdateProductDescription(9983301353766, "Test image product + test PD1");
                    //return;


                    //var apiss = ShopifyConnector.GetShopifyApiClient(9561942, (int)AppTypes.BenchmarkHero);
                    //var testProducts = AuditToolData.ReadProducts(apiss, out List<Storeya.Core.Models.DataProviders.ShopifyEntities.ProductEntity> shopifyProducts);
                    ////ShopifyCrawler.GetContent(;
                    //return;



                    //var productsCount = api11.GetProductsCount(DateTime.Now.AddHours(-3), false);

                    //Program.RunBenchmarkAnnalyzation(9559017);
                    //return; 9556146

                    //var api = ShopifyConnector.GetShopifyApiClient(313966713, (int)AppTypes.BenchmarkHero);
                    //var api = ShopifyConnector.GetShopifyApiClient(9332021, (int)AppTypes.TrafficBooster);
                    //var api = ShopifyConnector.GetShopifyApiClient(9556421, (int)AppTypes.BenchmarkHero);
                    //var api = ShopifyConnector.GetShopifyApiClient(9556146, (int)AppTypes.TrafficBooster);

                    //var dateMin = DateTime.Today.AddDays(-3);
                    //var prWithFilter = api.GetProductsGraphQL(null, dateMin: dateMin, allActiveProducts: false);




                    //string domain = UrlPathHelper.GetDomainName("http://www.ogoodystore.myshopify.com/password");

                    //try
                    //{
                    //    HttpWebResponse webResponse = HttpRequestResponseHelper.GetHttpWebResponse("http://www.ogoodystore.myshopify.com/password", false);
                    //    if (webResponse != null)
                    //    {
                    //        var content = HttpRequestResponseHelper.GetContent(webResponse);
                    //    }
                    //}
                    //catch (Exception ex)
                    //{
                    //    Console.WriteLine(ex.ToString());
                    //}
                    //return;
                    ////var api = new ShopifyApiProvider("thegrabitstore.myshopify.com", "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");
                    //////var api = ShopifyConnector.GetShopifyApiClient(313966713, (int)AppTypes.BenchmarkHero);
                    ////AuditToolData.ReadProducts(api, out var shopifyProducts);
                    ////return;
                    //AuditToolData.GetData(9556421);
                    //return;
                    //BenchmarkImagesManager.RestoreOriginalImages(313966713);//, 14, false
                    //return;
                    //BenchmarkImagesManager.RunTestFlow(313966713, 60, true);
                    //return;
                    ////ShopifyApiProvider.SimpleTest("C:\\Dev\\Storeya\\trunk\\storeya\\Uploads\\Temp\\prod/313/966/313966713\\1-5c549da7-0080-48c6-9be2-1f83688e9752-2-94f79bd5-6d0a-4a4a-8dc2-047f9dc5b5f1-_1.png"); //"C:\\Dev\\StoreYa\\Web Site\\BackGroundProcessing\\storeya\\prod/313/966/313966713\\Upload\\1-5c549da7-0080-48c6-9be2-1f83688e9752-2-94f79bd5-6d0a-4a4a-8dc2-047f9dc5b5f1-_1.png"

                    ////return;
                    ////CheckDownloadingPicturesShopify();
                    ////return;
                    //var shApi01 = new ShopifyApiProvider("testsomeapp.myshopify.com", "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");
                    //shApi01.FileUpdate("gid://shopify/MediaImage/41753113035046", "newNam1e.jpg");
                    //return;
                    //var res = shApi01.ConvertMediaImageIdToImageId(9983301353766, 41512078541094);
                    ////return;
                    ////var createResults=shApi01.CreateProductMedia(9983301353766, "https://storage.googleapis.com/shopify-staged-uploads/tmp/81257496870/products/e9468cd5-4fdf-4d23-b266-f03f2724bf7d/ImageName", "newAlt");
                    //var imageIDs = shApi01.UploadProductImageGraphQL(9983301353766, "C://Users//user//OneDrive//Desktop//рабочий стол//colinfuze//photo_2022-04-10_09-27-11.jpg", "someAltText", 10, "crocodile"); //C:\\Users\\<USER>\\Downloads\\1-5c549da7-0080-48c6-9be2-1f83688e9752-_2.png

                    //dynamic createData = createResults.data.productCreateMedia.media[0];
                    //string mediaImageGid = createData.id.ToString();
                    //string mediaStatus = shApi01.GetProductMediaStatus(9983301353766, mediaImageGid);
                    //return;
                    //var imageIDss = shApi01.UploadProductImageGraphQL(8574950834470, "C://Users//user//OneDrive//Desktop//рабочий стол//colinfuze//photo_2022-04-10_09-27-11.jpg", "someAltText", 2, "ImageName"); //"C:\\Users\\<USER>\\OneDrive\\Desktop\\ImportImageTest\\1-5c549da7-0080-48c6-9be2-1f83688e9752-_2.png"
                    // var imageIDss = shApi01.UploadProductImageGraphQL(9983301353766, "C://Users//user//OneDrive//Desktop//ImportImageTest//1-5c549da7-0080-48c6-9be2-1f83688e9752-_2.png", "someAltText", 2, "ImageName"); //"C:\\Users\\<USER>\\OneDrive\\Desktop\\ImportImageTest\\1-5c549da7-0080-48c6-9be2-1f83688e9752-_2.png"
                    //return;

                    //BenchmarkImagesManager.RestoreImagesToShopify(313966713);
                    //return;

                    ////var api = new ShopifyApiProvider("44onceuponatime.myshopify.com", "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");

                    //////dynamic delResults = api.DeleteProductImage(9829749719361, 59215278965057);
                    //return;
                    //CheckDownloadingPicturesShopify();





                    //return;




                    //var shApi0 = new ShopifyApiProvider("testsomeapp.myshopify.com", "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");
                    //var imageID = shApi0.UploadProductImageGraphQL(8574950834470, "C://Users//user//OneDrive//Desktop//рабочий стол//colinfuze//photo_2022-04-10_09-27-11.jpg", "someAltText", 2, "crocodile");
                    //long imID = long.Parse(imageID.Split('/').Last());
                    ////var variants = new List<string>
                    ////{
                    ////    "gid://shopify/ProductVariant/46319988441382", "gid://shopify/ProductVariant/46319988506918", "gid://shopify/ProductVariant/46319988572454", "gid://shopify/ProductVariant/46319988572454"
                    ////};
                    //var variants = new List<string>
                    //{
                    //    "46319988441382", "46319988506918", "46319988572454"
                    //};
                    ////var status = BenchmarkImagesManager.ImageManagementGQL(313966713, variants, 8574950834470, "C://Users//user//OneDrive//Desktop//рабочий стол//colinfuze//photo_2022-04-10_09-27-11.jpg", "someALTtESXT", 2, "ORIGINALname", imID, out long? imageId);
                    ////var status = BenchmarkImagesManager.ImageManagement(313966713, variants, 8574950834470, "C://Users//user//OneDrive//Desktop//рабочий стол//colinfuze//photo_2022-04-10_09-27-11.jpg", "someALTtESXT", 2, "ORIGINALname", imID, out long? imageId);
                    //return;

                }
                else
                {
                    //ConsoleAppHelper.WriteLogWithDB($"--------------------------", 9470892);
                    //ConsoleAppHelper.WriteLogWithDB($"Start benchmarkimagesoptupload, Upload Images to Shopify - args:{string.Join(" ", args)}", 9470892);
                    //ConsoleAppHelper.WriteLogWithDB($"End benchmarkimagesoptdownload - Download all images.", 9470892);
                    //ConsoleAppHelper.WriteLogWithDB($"--------------------------", 9470892);
                    //RunBHForShop(9470892);
                    DevelopersSandBox.DevelopmentTest2();
                }

                Console.WriteLine("-----Done! Press Any Key to Exit.-----");
                return;
            }


            LauncherTriggeredCommand launcherTriggeredCommand = LauncherHelper.StartLogTriggeredCommand("BenchmarkHeroExeLog", args);
            try
            {
                var firstArg = ConsoleAppHelper.GetFirstArgument(args);
                if (firstArg == "rerun")
                {
                    var shopID = ConsoleAppHelper.GetShopId(args);
                    RunBHForShop(shopID ?? 0);
                    LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                    return;
                }
                if (firstArg == "test")
                {
                    TestDB();
                }
                if (firstArg == "pd")
                {
                    CompareDescriptionsForPD();
                    return;
                }

                if (firstArg == "reset")
                {
                    var shopID = ConsoleAppHelper.GetShopId(args);
                    BenchmarksManager.ResetResults(shopID.Value);
                    LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                    return;
                }
                else if (firstArg == "whois")
                {
                    var shopID = ConsoleAppHelper.GetShopId(args);
                    var db = DataHelper.GetStoreYaEntities();

                    BlackListHelper.LeadDetailsUpdateWhoisInfo(db.LeadDetails.Where(l => l.ShopID == shopID).Single().ID);
                    LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                    return;
                }

                else if (firstArg == "fix_orders_amount")
                {
                    var shopID = ConsoleAppHelper.GetShopId(args);
                    FixOrdersAmount(shopID);
                    LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                    return;
                }
                else if (firstArg == "fix_orders_amount")
                {
                    FixOrdersAmount();
                    LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                    return;
                }
                else if (firstArg == "fix_orders_amount_all_shops")
                {
                    FixOrdersAmount(null, true);
                    LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                    return;
                }
                else if (firstArg == "robots_test")
                {
                    AuditTool t = new AuditTool();
                    string domain = args[1].ToString();
                    string robotsTxtUrl = (domain + "/robots.txt");

                    try
                    {
                        HttpWebResponse webResponse = HttpRequestResponseHelper.GetHttpWebResponse(robotsTxtUrl, false);
                        if (webResponse != null
                            && webResponse.StatusCode != HttpStatusCode.NotFound
                            && webResponse.StatusCode != HttpStatusCode.NotAcceptable
                            && webResponse.StatusCode != HttpStatusCode.NotImplemented)
                        {
                            Console.WriteLine("RobotsTxtUrl: " + robotsTxtUrl);
                        }
                        LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.ToString());
                        LauncherHelper.EndFailedLogTriggeredCommand(launcherTriggeredCommand, ex);
                    }

                    return;
                }
                if (firstArg == "is_password_protected")
                {
                    string shopifyShopName = args[1].ToString();
                    AuditTool t = new AuditTool();
                    t.Data = new AuditToolData() { ShopifyShopName = shopifyShopName };
                    //t.CheckUrls();
                    bool websiteIsPasswordProtected = t.WebsiteIsPasswordProtected();
                    if (websiteIsPasswordProtected)
                        Console.WriteLine(shopifyShopName + "Is Password Protected site");
                    else
                    {
                        Console.WriteLine(shopifyShopName + "Is NOT Password Protected site");
                    }
                    LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                    return;
                }
                if (firstArg == "run_for_existing_clients")
                {
                    string filePathWithShopIds = args[1];
                    var shopIDs = File.ReadAllLines(filePathWithShopIds);
                    foreach (var shopString in shopIDs)
                    {
                        int shopid = Convert.ToInt32(shopString);
                        try
                        {
                            RunBHForShop(shopid);
                            LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(string.Format("Failed for shopID: {0}. Ex: {1}.", shopid, ex));
                            LauncherHelper.EndFailedLogTriggeredCommand(launcherTriggeredCommand, ex);
                        }
                    }
                    return;
                }
                if (firstArg == "benchmarkapi")
                {
                    Console.WriteLine("running BH Api calls");
                    var hostName = ConsoleAppHelper.GetConsoleArgument(args, "hostname:");
                    TreatBHApi(hostName);
                }
                else if (firstArg == "benchmarkapiwebhook")
                {
                    Console.WriteLine("running BH Api calls");
                    var hostName = ConsoleAppHelper.GetConsoleArgument(args, "hostname:");
                    var returnurl = ConsoleAppHelper.GetConsoleArgument(args, "returnurl:");
                    TreatBHApiWebHook(hostName, returnurl);
                }
                else if (firstArg == "benchmarkimagesoptdownload")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    ConsoleAppHelper.WriteLogWithDB($"--------------------------", shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"Start benchmarkimagesoptdownload - Download all images. args:{string.Join(" ", args)}", shopId.Value);
                    Console.WriteLine($"benchmarkimagesoptdownload {shopId}");
                    BenchmarkImagesManager.InitAllImagesOptimization(shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"End benchmarkimagesoptdownload - Download all images.", shopId.Value);

                }
                else if (firstArg == "benchmarkimagesopt")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    ConsoleAppHelper.WriteLogWithDB($"--------------------------", shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"Start benchmarkimagesopt - Optimize all images. args:{string.Join(" ", args)}", shopId.Value);
                    if (int.TryParse(ConsoleAppHelper.GetConsoleArgument(args, "quality:"), out int quality))
                    {
                        bool upload = true;
                        bool.TryParse(ConsoleAppHelper.GetConsoleArgument(args, "upload:"), out upload);
                        BenchmarkImagesManager.ExecuteAllImagesOptimization(shopId.Value, quality, upload);
                        ConsoleAppHelper.WriteLogWithDB($"End benchmarkimagesopt - Optimize all images.", shopId.Value);

                    }
                    else
                    {
                        Console.WriteLine("Missing quality argument");

                    }
                }
                else if (firstArg == "benchmarkimagesopttest")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    BenchmarkImagesManager.ImageOptimizerTask(shopId.Value, BenchmarkImagesManager.MAX_PRODUCTS_TO_REVIEW);
                }
                else if (firstArg == "benchmarkimagesoptrunagain")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    ConsoleAppHelper.WriteLogWithDB($"--------------------------", shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"Start benchmarkimagesoptrunagain, Run Optimize again - args:{string.Join(" ", args)}", shopId.Value);
                    BenchmarkImagesManager.ImageOptimizerTask(shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"End benchmarkimagesoptrunagain, Run Optimize again", shopId.Value);

                }
                else if (firstArg == "benchmarkimagesoptupload")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    ConsoleAppHelper.WriteLogWithDB($"--------------------------", shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"Start benchmarkimagesoptupload, Upload Images to Shopify - args:{string.Join(" ", args)}", shopId.Value);
                    BenchmarkImagesManager.UploadImagesToShopify(shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"End benchmarkimagesoptupload, Upload Images to Shopify", shopId.Value);
                }
                else if (firstArg == "benchmarkimagesoptrestore")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    ConsoleAppHelper.WriteLogWithDB($"--------------------------", shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"Start benchmarkimagesoptrestore, Run restore - args:{string.Join(" ", args)}", shopId.Value);
                    BenchmarkImagesManager.RestoreImagesToShopify(shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"End benchmarkimagesoptrestore,Run restore", shopId.Value);

                }
                else if (firstArg == "benchmarkimagesoptcleanup")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    ConsoleAppHelper.WriteLogWithDB($"--------------------------", shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"Start benchmarkimagesoptcleanup, Cleanup Image folder- args:{string.Join(" ", args)}", shopId.Value);
                    BenchmarkImagesManager.CleanUpImageFolder(shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"End benchmarkimagesoptcleanup, Cleanup Image folder", shopId.Value);
                }
                else if (firstArg == "benchmarkimagesoptrestoretooriginal")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    ConsoleAppHelper.WriteLogWithDB($"--------------------------", shopId.Value);
                    ConsoleAppHelper.WriteLogWithDB($"Start benchmarkimagesoptrestoretooriginal, Run restore From Original- args:{string.Join(" ", args)}", shopId.Value);
                    string sRestorepoint = ConsoleAppHelper.GetConsoleArgument(args, "restorepoint:");
                    int? restorepoint = null;
                    if (!string.IsNullOrEmpty(sRestorepoint))
                    {
                        restorepoint = int.Parse(sRestorepoint);
                    }
                    string sdontDeleteIfFailed = ConsoleAppHelper.GetConsoleArgument(args, "dontdelete:");
                    bool dontDeleteIfFailed = false;
                    if (!string.IsNullOrEmpty(sdontDeleteIfFailed) && sdontDeleteIfFailed == "1")
                    {
                        dontDeleteIfFailed = true;
                        Console.WriteLine("Are you sure this will leave duplicate images on site? enter 'y' if sure");
                        string sure = Console.ReadLine();
                        if (sure != "y")
                        {
                            return;
                        }
                    }
                    BenchmarkImagesManager.RestoreOriginalImages(shopId.Value, restorepoint, dontDeleteIfFailed);
                    ConsoleAppHelper.WriteLogWithDB($"End benchmarkimagesoptrestoretooriginal, Run restore From Original", shopId.Value);

                }
                //else if (firstArg == "biorestorecheck")
                //{
                //    int? shopId = ConsoleAppHelper.GetShopId(args);
                //    ConsoleAppHelper.WriteLogWithDB($"--------------------------", shopId.Value);
                //    int? restorepoint = ConsoleAppHelper.GetIntArgument(args, "restorepoint:");
                //    ConsoleAppHelper.WriteLog($"Start RestoreImagesToShopifyTester, Run restore test From Original- args:{string.Join(" ", args)}", shopId.Value);
                //    bool restoreFromOrigin = ConsoleAppHelper.GetBoolArgument(args, "fromorgin:", false);
                //    BenchmarkImagesManager.RestoreImagesToShopifyTester(shopId.Value, restoreFromOrigin, restorepoint);
                //    ConsoleAppHelper.WriteLog($"End RestoreImagesToShopifyTester, Run restore From Original", shopId.Value);
                //}
                else if (firstArg == "benchmarkimagesoptrestorealltooriginal")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    BenchmarkImagesManager.RestoreAllToOriginal(shopId.Value);
                }
                else if (firstArg == "storagereport")
                {
                    Console.Write("Running StorageReport");
                    //DevelopersSandBox.StorageReport();
                    BenchmarkImagesManager.StorageReport();
                }
                else if (firstArg == "optimizefolder")
                {
                    Console.Write("Running optimizefolder");
                    string shopPath = ConsoleAppHelper.GetConsoleArgument(args, "path:");
                    int quality = int.Parse(ConsoleAppHelper.GetConsoleArgument(args, "q:"));
                    //DevelopersSandBox.StorageReport();
                    string uploadPath = ImageUtilities.OptimizeFolder(shopPath, quality, out List<string> failedImages);
                    foreach (var item in failedImages)
                    {
                        Console.Write($"Failed to optimize:{item}");
                    }
                    Console.Write($"Running optimizefolder done results path:{uploadPath}");
                }

                else if (firstArg == "benchmarkimagesoptfulltest")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    if (!int.TryParse(ConsoleAppHelper.GetConsoleArgument(args, "quality:"), out int quality))
                    {
                        quality = 60;
                    }
                    if (shopId.HasValue)
                    {
                        bool upload = ConsoleAppHelper.GetBoolArgument(args, "upload:");
                        BenchmarkImagesManager.RunTestFlow(shopId.Value, quality, upload);
                        return;
                    }
                    string file = ConsoleAppHelper.GetFileName(args);
                    foreach (var row in File.ReadAllLines(file))
                    {
                        try
                        {
                            BenchmarkImagesManager.RunTestFlow(int.Parse(row), quality);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("ShopId:" + row + " Exception: " + ex);
                        }

                    }
                }
                else if (firstArg == "benchmarkimagesoptcleanall")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    if (!int.TryParse(ConsoleAppHelper.GetConsoleArgument(args, "days:"), out int daysBack))
                    {
                        daysBack = 7;
                    }
                    if (!int.TryParse(ConsoleAppHelper.GetConsoleArgument(args, "limit:"), out int limit))
                    {
                        limit = 3;
                    }
                    if (!int.TryParse(ConsoleAppHelper.GetConsoleArgument(args, "origindays:"), out int originDaysBack))
                    {
                        originDaysBack = 20;
                    }
                    string res = BenchmarkImagesManager.CleanUpAllAccounts("Main", daysBack, limit, true, shopId);
                    Console.WriteLine($"Remove Origin older then {originDaysBack} days");
                    res += BenchmarkImagesManager.CleanUpAllAccounts("Origin", originDaysBack, limit, false, shopId);
                    if (!string.IsNullOrEmpty(res))
                    {
                        EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, "Benchmark images optimization cleanup report", res);
                    }
                }
                else if (firstArg == "selftest")
                {
                    SelfTests.ShopifyTest();
                    SelfTests.GenearlTests();
                    SelfTests.AWSTest();
                }
                else if (firstArg == "downloadimage")
                {
                    try
                    {
                        string url = ConsoleAppHelper.GetConsoleArgument(args, "url:");
                        string path = ConsoleAppHelper.GetConsoleArgument(args, "path:");
                        ImageOptimizer.DownloadImage(url, path, 0);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.ToString());
                    }

                }
                else if (firstArg == "getcandidatesforemail")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    int? daysAgo = ConsoleAppHelper.GetIntArgument(args, "days:", 30);
                    int? daysToCheck = ConsoleAppHelper.GetIntArgument(args, "daystocheck:", 0);
                    bool trippelDates = ConsoleAppHelper.GetBoolArgument(args, "trippeldates:", true);
                    BenchmarkRepeatingCustomersManager brcm = new BenchmarkRepeatingCustomersManager(shopId, daysAgo.Value, daysToCheck.Value, liveMode: false, trippelDates);
                    brcm.CreateAListOfShopsWithCandidatesToBeNotified();
                }
                else if (firstArg == "checkcandidatestobenotifiedbyemail")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    bool liveMode = ConsoleAppHelper.GetBoolArgument(args, "live:", true);
                    int? daysAgo = ConsoleAppHelper.GetIntArgument(args, "days:", 30);
                    int? daysToCheck = ConsoleAppHelper.GetIntArgument(args, "daystocheck:", 1);
                    bool trippelDates = ConsoleAppHelper.GetBoolArgument(args, "trippeldates:", true);
                    BenchmarkRepeatingCustomersManager brcm = new BenchmarkRepeatingCustomersManager(shopId, daysAgo.Value, daysToCheck.Value, liveMode: liveMode, trippelDates);
                    brcm.CheckCustomersAndSendEmails();
                }
                else if (firstArg == "updateifbingpixelinstalled")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    bool demo = ConsoleAppHelper.GetBoolArgument(args, "demo:", true);
                    int? daysAgo = ConsoleAppHelper.GetIntArgument(args, "days:", 180);
                    BenchmarkRepeatingCustomersManager.UpdateIfBingPixelInstalled(shopId, demo, daysAgo.Value);
                }
                else if (firstArg == "createllm")
                {
                    int? shopId = ConsoleAppHelper.GetShopId(args);
                    LlmStatus llmStatus = LlmsFileCreator.CreateOrUpdateLlmsShopifyPage(shopId.Value, out string fileUrl, out string error);
                    BenchmarksManager.CreateOrUpdateLLmPageTable(shopId.Value, llmStatus, error: error);
                    if (fileUrl != null)
                    {
                        BenchmarksManager.UpdateLlmUrlInBH(shopId.Value, fileUrl);
                    }
                }
                else
                {
                    Console.WriteLine("running BH exe");
                    TreatBH();
                    //RunCCreator(bugsnag);

                    //CollectDataForLeadDetails(bugsnag);
                    LauncherHelper.EndLogTriggeredCommand(launcherTriggeredCommand);
                }
            }
            catch (Exception ex)
            {
                ShopDowngradeManager.HandleFatalError(ex, "BenchmarkHero");
                Console.WriteLine(ex.Message);
                Log4NetLogger.Error(ex.ToString());
                //bugsnag.Notify(new Exception("Fatal error", ex));
                LauncherHelper.EndFailedLogTriggeredCommand(launcherTriggeredCommand, ex);
            }
            //}
            //else
            //{
            //    DevelopersSandBox.StorageReport();
            //}
        }



        private static void CheckDownloadingPicturesShopify()
        {
            var shApi01 = new ShopifyApiProvider("testsomeapp.myshopify.com", "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");



            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("<table style='border-collapse: collapse; width: 100%;'>");
            stringBuilder.Append("<thead>");
            stringBuilder.Append("    <thead>       <tr>           <th style='border: 1px solid black; padding: 8px;'>Name</th>           <th style='border: 1px solid black; padding: 8px;'>Size</th>           <th style='border: 1px solid black; padding: 8px;'>Result</th>       </tr>   </thead>");
            stringBuilder.Append("<tbody>");

            List<string> folders = new List<string>();

            string folderPath1 = "C:\\Users\\<USER>\\OneDrive\\Desktop\\рабочий стол\\colinfuze";
            string folderPath2 = "C:\\Users\\<USER>\\OneDrive\\Pictures\\Screenshots";
            folders.Add(folderPath1);
            folders.Add(folderPath2);
            var failsCount = 0;
            var total = 0;
            long minSize = 5000000;
            long maxSize = 0;
            foreach (var folderPath in folders)
            {
                string[] files = Directory.GetFiles(folderPath, "*.*", SearchOption.TopDirectoryOnly)
                   .Where(file => file.EndsWith(".jpeg", StringComparison.OrdinalIgnoreCase) ||
                                  file.EndsWith(".png", StringComparison.OrdinalIgnoreCase) || file.EndsWith(".jpg", StringComparison.OrdinalIgnoreCase)).OrderByDescending(x => x.Length)
                   .ToArray();
                var counter = 0;
                var imagesAmount = files.Length;
                foreach (var file in files)
                {
                    counter++;
                    Console.WriteLine($"{counter}/{imagesAmount}");
                    total++;
                    FileInfo fi = new FileInfo(file);
                    string result = "uploaded";
                    try
                    {
                        shApi01.TryToUpload(8574950834470, file, fi);
                        if (fi.Length > maxSize)
                        {
                            maxSize = fi.Length;
                        }
                    }
                    catch
                    {
                        failsCount++;
                        if (fi.Length < minSize)
                        {
                            minSize = fi.Length;
                        }
                        result = "Failed to upload";
                    }

                    stringBuilder.Append("<tr>");
                    stringBuilder.Append($"  <th style='border: 1px solid black; padding: 8px;'>{fi.Name}</th>");
                    stringBuilder.Append($"  <th style='border: 1px solid black; padding: 8px;'>{fi.Length}</th>");
                    stringBuilder.Append($"  <th style='border: 1px solid black; padding: 8px;'>{result}</th>");
                    stringBuilder.Append("</tr>");
                }
            }

            stringBuilder.Append("</tbody> </table>");
            string minsizeThatFailed = null;
            if (failsCount > 0)
            {
                minsizeThatFailed = $", the size of the smallest file that failed to upload: {minSize}";
            }
            EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "Saving images to Shopify result", $"Total amount: {total}, maxSize: {maxSize}, failed: {failsCount}{minsizeThatFailed}. <br/>" + stringBuilder.ToString());

        }

        private static void CompareDescriptionsForPD()
        {
            var db = DataHelper.GetStoreYaEntities();
            var pds = db.ProductDescribersHistory.Where(s => s.ShopId == 479336 && s.Status == (int)ProductDescriberHistoryStatus.Updated).ToList();
            Console.WriteLine("Checking " + pds.Count);
            foreach (var item in pds)
            {
                var prev = item.PDProduct;
                var newDesc = item.GeneratedDescription;

                string[] prevColors = GetColorsInText(prev);
                string[] newColors = GetColorsInText(newDesc);

                bool hasSameColors = CheckIfSameColors(prevColors, newColors);

                File.AppendAllText(@"C:\temp\pdproblems.txt", $"{item.Id}, {item.ProductID}, {hasSameColors}" + Environment.NewLine);
            }
        }

        private static bool CheckIfSameColorsOld(string[] arr1, string[] arr2)
        {

            Array.Sort(arr1.Distinct().ToArray());
            Array.Sort(arr2.Distinct().ToArray());
            bool equal = true;

            if (arr2.Length != arr1.Length)
            {
                return false;
            }

            for (int i = 0; i < arr2.Length; i++)
            {
                if (arr1[i] != arr2[i])
                {
                    equal = false;
                    break;
                }
            }
            return equal;
        }
        private static bool CheckIfSameColors(string[] arr1, string[] arr2)
        {

            var prev = arr1.Distinct().ToArray();
            var newKeywords = arr2.Distinct().ToArray();
            if (prev.Length < arr1.Length)
            {
                return false;
            }

            for (int i = 0; i < newKeywords.Length; i++)
            {
                var k = newKeywords[i];
                bool found = false;
                for (int j = 0; j < prev.Length; j++)
                {
                    if (k == prev[j])
                    {
                        found = true;
                        break;
                    }
                }
                if (!found)
                {
                    return false;
                }
            }
            return true;
        }

        private static string[] GetColorsInText(string text)
        {
            string[] popularColors = { "Red", "Green", "Blue", "Yellow", "Purple", "Pink", "Orange", "Black", "White", "Gray"
                    , "Corset", "Skirt", "Gold", "Silver", "Harness", "Stocking", "Bikini", "Leather", "Swimwear", "Dress", "Costume", "Bunny", "Drawer" ,"Shorts",
                    "bralette", "Belinda", "Anastasia", "Leggings", "Diamond", "Rainbow"
            };
            List<string> matchedColors = new List<string>();

            foreach (string color in popularColors)
            {
                if (text.ToLower().Contains(color.ToLower()))
                {
                    matchedColors.Add(color);
                }
            }

            return matchedColors.ToArray();
        }




        private static int DownloadImages(string shopifyDomain, string token)
        {
            ShopifyApiProvider api = new ShopifyApiProvider(shopifyDomain, token);
            var products = api.GetProductsGraphQL(null);
            int count = 0;
            foreach (var item in products)
            {
                if (item.images.Length > 0)
                {
                    foreach (var img in item.images)
                    {
                        string imageUrl = img.src;
                        var imageFile = ImageNamer.GetFileName(imageUrl, out string ext, out string orignalImageName);
                        ImageOptimizer.DownloadImage(imageUrl, @"c:\temp\0_" + shopifyDomain.Replace(".myshopify.com", "") + @"\" + imageFile, 0);
                        count++;
                    }
                }
            }
            return count;
        }

        private static void TestDB()
        {
            Console.WriteLine(1);
            var db = new StoreYaEntities();
            Console.WriteLine(2);
        }

        private static ShopifyConnector GetShopifyClient(ShopifyConnectedShop connectedShop)
        {
            ConnectionData data = new ConnectionData() { StoreName = connectedShop.ShopifyShopName, Token = connectedShop.AppToken };
            ShopifyConnector client = new ShopifyConnector(data);
            return client;
        }

        private static void FixOrdersAmount(int? shopId = null, bool all = false)
        {
            DateTime dateOfVersionReleaseWithABug = new DateTime(2018, 01, 01);
            List<ShopifyConnectedShop> shops = null;

            using (StoreYaEntities db = new StoreYaEntities())
            {
                if (shopId.HasValue)
                {
                    shops = db.ShopifyConnectedShops.Where(x => x.ShopID == shopId).ToList();
                }
                else
                {
                    if (all)
                    {
                        shops = db.ShopifyConnectedShops.Where(x => (x.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster)
                                                            && x.InsertedAt > dateOfVersionReleaseWithABug
                                                            && x.PermissionsScope > 0
                                                            && x.ShopID > 0
                                                            )
                        .OrderBy(s => s.ShopID).ToList();
                    }
                    else
                    {
                        shops = db.ShopifyConnectedShops.Where(x => (x.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster || x.StoreyaAppTypeID == (int)Shopify_StoreyaApp.BenchmarkHero)
                                                            && x.InsertedAt > dateOfVersionReleaseWithABug
                                                            && x.PermissionsScope > 0
                                                            && x.ShopID > 0
                                                            && (!x.OrdersAmount.HasValue || x.OrdersAmount.Value == 0))
                    .OrderBy(s => s.ShopID).ToList();
                    }

                }
            }

            if (shops.Any())
            {
                Console.WriteLine(shops.Count + "accounts to check");
                foreach (var connectedShop in shops)
                {
                    Console.WriteLine("workding with " + connectedShop.ShopID);
                    int ordersCount = 0;

                    try
                    {
                        ShopifyConnector client = GetShopifyClient(connectedShop);
                        ordersCount = client.GetOrdersCount(DateTime.Now.AddDays(-30));
                    }
                    catch (Exception ex)
                    {
                        ConsoleAppHelper.WriteError("Failed on trying to access orders amount for shopID:  " + connectedShop.ShopID,
                            ex, connectedShop.ShopID);
                        continue;
                    }

                    if (ordersCount > 0)
                    {
                        try
                        {
                            using (StoreYaEntities db = new StoreYaEntities())
                            {
                                Console.WriteLine("Update orders for " + connectedShop.ShopID + " to " + ordersCount);
                                ShopifyConnectedShop shopToUpdate =
                                    db.ShopifyConnectedShops.Single(x => x.ID == connectedShop.ID);
                                if (ordersCount > 0)
                                {
                                    shopToUpdate.OrdersAmount = ordersCount;
                                    db.SaveChanges();
                                    Console.WriteLine("Done - Update orders for " + connectedShop.ShopID + " to " + ordersCount);
                                }

                            }
                        }
                        catch (Exception ex_2)
                        {
                            ConsoleAppHelper.WriteError("Failed on save orders amount for shopID:  " + connectedShop.ShopID,
                                ex_2, connectedShop.ShopID);
                        }
                    }
                }
            }
        }

        private static void Test()
        {
            ConnectionData data = new ConnectionData() { StoreName = "shoprosiegrace.myshopify.com", Token = "8174782aece4fefc940f78ab500c9940" };
            ShopifyConnector client = new ShopifyConnector(data);

            DateTime dateToStartFrom = DateTime.Now.AddDays(-60);
            int? orders_count = null;
            try
            {
                orders_count = client.GetOrdersCount(dateToStartFrom);
                int? MonthlyAmountOfOrders = orders_count / 2;
            }
            catch
            {

            }

            if (orders_count.HasValue && orders_count > 0)
            {
                List<ShopifyOrder> orders = null;
                try
                {
                    //request orders data only with required fields
                    string additionalRequestFilter = "&fields=order_number,order_status_url,total_line_items_price,total_price,currency,email,test";
                    orders = client.GetOrders(0, dateToStartFrom, null, false, additionalRequestFilter, 250);
                    if (orders.Count > 0)
                    {
                        string orderConfirmationPageUrl = orders[0].OrderStatusUrl;

                    }
                }
                catch
                {

                }
            }
        }

        //private static void CollectDataForLeadDetails(Client bugsnag)
        //{

        //    //TODO: Use BlackListHelper


        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    DateTime threeHoursAgo = DateTime.Now.AddHours(-3);
        //    DateTime two_daysAgo = DateTime.Now.AddDays(-2);
        //    //wait a day before collecting leads
        //    var leads = db.LeadDetails.Where(b => !b.Status.HasValue && b.InsertedAt >= two_daysAgo && b.InsertedAt <= threeHoursAgo).ToList();

        //    Console.WriteLine("");
        //    Console.WriteLine("Amount of Leads to coolect data " + leads.Count());

        //    StringBuilder sb = null;

        //    foreach (var job in leads)
        //    {
        //        ConsoleAppHelper.WriteLog("Collecting LeadDetails data for shop - " + job.ShopID, job.ShopID.Value);
        //        LeadDetailsResult run_result = null;
        //        try
        //        {
        //            if (HasActiveShopifyApp(job.ShopID.Value))
        //            {
        //                run_result = LeadDetailsProvider.ExtractShopifyLeadData(job);
        //            }
        //            else
        //            {
        //                GaConnectedData gaConnectedData = GetGaConnectedData(job.ShopID.Value);
        //                if (gaConnectedData != null)
        //                {
        //                    run_result = LeadDetailsProvider.ExtractLeadDataByGa(gaConnectedData.ShopID,
        //                        gaConnectedData.RefreshToken, gaConnectedData.ProfileID, gaConnectedData.WebsiteUrl);
        //                }
        //                else
        //                {
        //                    run_result = LeadDetailsProvider.ExtractGeneralLeadData(job.ShopID.Value, job.Url);
        //                }
        //                if (!job.MonthlyAmountOfOrders.HasValue)
        //                {
        //                    run_result.Data.MonthlyAmountOfOrders = SetOrdersCountFromShopify(job);
        //                }
        //            }

        //            if (run_result == null || !string.IsNullOrEmpty(run_result.Error))
        //            {
        //                if (sb == null)
        //                {
        //                    sb = new StringBuilder();
        //                }
        //                if (!ExcludeSendEmail(run_result.Error))
        //                {
        //                    var error = string.Format(
        //                    "ShopID: <a href='https://www.storeya.com/admin/shopdetails/{0}'>{0}</a> Error: {1}",
        //                    job.ShopID, run_result.Error);
        //                    sb.Append(error).Append("<br/>");
        //                    bugsnag.Notify(new Exception(error));
        //                }
        //            }

        //            List<ShopApp> apps = (job.ShopID != null ? AppStoreManager.GetShopApps(job.ShopID.Value) : null);
        //            job.InstalledApps = AppStoreManager.GetInstalledAppsCommaDelimited(apps);
        //            job.AcitiveApps = AppStoreManager.GetActiveAppsCommaDelimited(apps);
        //            string tb_app_name = Enum.GetName(typeof(AppTypes), (int)AppTypes.TrafficBooster);
        //            if (string.IsNullOrEmpty(job.AcitiveApps) ||
        //                (!string.IsNullOrEmpty(job.AcitiveApps) && !job.AcitiveApps.Contains(tb_app_name)))
        //            {
        //                List<TrafficBooster> cancelledApps =
        //                    TrafficBoostersDbHelper.GetRemovedAppsSettings(job.ShopID.Value);
        //                if (cancelledApps != null && cancelledApps.Count > 0 &&
        //                    cancelledApps.Where(x => x.PurchasedAmount > 0).Any())
        //                {
        //                    if (string.IsNullOrEmpty(job.AcitiveApps))
        //                    {
        //                        job.AcitiveApps = tb_app_name;
        //                    }
        //                    else
        //                    {
        //                        job.AcitiveApps = "," + tb_app_name;
        //                    }
        //                }
        //            }
        //            //BlackListHelper.LeadDetailsUpdateWhoisInfo(job.ID);
        //        }
        //        catch (Exception ex)
        //        {
        //            if (run_result == null)
        //            {
        //                run_result = new LeadDetailsResult();
        //                run_result.Error = ex.Message;
        //                if (sb == null)
        //                {
        //                    sb = new StringBuilder();
        //                }
        //                var error = string.Format(
        //                    "ShopID: <a href='https://www.storeya.com/admin/shopdetails/{0}'>{0}</a> Error: {1}",
        //                    job.ShopID, run_result.Error);
        //                sb.Append(error).Append("<br/>");
        //            }
        //            bugsnag.Notify(new Exception("Failed on Collecting LeadDetails data  for shoppID:  " + job.ShopID,
        //                ex));
        //            ConsoleAppHelper.WriteError("Failed on Collecting LeadDetails data  for shoppID:  " + job.ShopID,
        //                ex, job.ShopID.Value);
        //        }
        //        finally
        //        {
        //            try
        //            {
        //                LeadDetailsProvider.Update(job, run_result);
        //            }
        //            catch (Exception ex)
        //            {
        //                var error = string.Format("ShopID: <a href='https://www.storeya.com/admin/shopdetails/{0}'>{0}</a> DB update failed, Error: {1}", job.ShopID, ex.ToString());
        //                sb.Append(error).Append("<br/>");
        //            }

        //        }
        //    }
        //    if (sb != null && !string.IsNullOrEmpty(sb.ToString()))
        //    {
        //        EmailHelper.SendEmail("<EMAIL>", "Failed on Collecting LeadDetails data - " + DateTime.Now.ToShortDateString(), sb.ToString());
        //    }
        //}

        private static int? SetOrdersCountFromShopify(LeadDetail job)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopifyConnectedShop connectedShop = db.ShopifyConnectedShops.Where(cs => cs.ShopID == job.ShopID && cs.OrdersAmount.HasValue).OrderByDescending(cs => cs.StoreyaAppTypeID).FirstOrDefault();
            if (connectedShop != null)
            {
                if (connectedShop.OrdersAmount.Value > 0)
                {
                    return connectedShop.OrdersAmount;
                }
            }
            return null;
        }

        private static bool ExcludeSendEmail(string message)
        {
            return message.Contains("(401) Unauthorized");
        }

        private static List<string> Combine(string installedAppsString, string removedAppsString)
        {
            List<string> installed_and_removed = new List<string>();

            AddToUsedAppsList(installedAppsString, ref installed_and_removed);
            AddToUsedAppsList(removedAppsString, ref installed_and_removed);

            return installed_and_removed;
        }

        private static void AddToUsedAppsList(string appsString, ref List<string> installed_and_removed)
        {
            if (!string.IsNullOrEmpty(appsString))
            {
                if (appsString.Contains(","))
                {
                    string[] splitted = appsString.Split(',');
                    for (int i = 0; i < splitted.Length; i++)
                    {
                        if (!installed_and_removed.Where(x => x == splitted[i]).Any())
                            installed_and_removed.Add(splitted[i]);
                    }
                }
                else
                {
                    if (!installed_and_removed.Where(x => x == appsString).Any())
                        installed_and_removed.Add(appsString);
                }
            }
        }

        //public static bool IsShopify(int shopID)
        //{
        //        StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //        Shop shop = db.Shops.Where(s => s.ID == shopID).Single();
        //        User user = db.Users.Where(u => u.ID == shop.UserID).Single();

        //        if (user.OriginMarketplace == (int)Storeya.Core.OriginMarketplaces.Shopify
        //            || (shop.CatalogSourcePlatform.HasValue && shop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi))
        //        {
        //            return true;
        //        }

        //    return false;
        //}

        public static GaConnectedData GetGaConnectedData(int shopID)
        {
            GaConnectedData gaConnectedData = null;

            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            GAConnectedAccount gaConnectedAccount = db.GAConnectedAccounts.Where(g => g.ShopID == shopID).SingleOrDefault();
            if (gaConnectedAccount != null)
            {
                //Console.WriteLine("Working on shopID " + gaConnectedAccount.ShopID);
                GaProfileInfo gaProfile = GaManager.GetActiveProfileInfo(gaConnectedAccount.ShopID.Value);
                if (gaProfile != null)
                {
                    gaConnectedData = new GaConnectedData();
                    gaConnectedData.ShopID = shopID;
                    gaConnectedData.RefreshToken = gaConnectedAccount.RefreshToken;
                    gaConnectedData.ProfileID = gaProfile.ProfileID;
                    gaConnectedData.WebsiteUrl = gaProfile.WebsiteUrl;
                }
            }
            return gaConnectedData;
        }

        public static bool HasActiveShopifyApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            var apps = db.ShopifyConnectedShops.Where(a => a.ShopID == shopID && a.PermissionsScope == 1)
                .Select(a => a).ToList();
            if (apps.Count > 0)
            {
                return true;
            }

            return false;
        }

        //private static void RunCCreator(Client bugsnag)
        //{
        //    var jobsToRun = CCreator.GetJobsToRun();

        //    Console.WriteLine("");
        //    Console.WriteLine("Amount of CCreatorJobs to run " + jobsToRun.Count());

        //    StringBuilder sb = null;

        //    foreach (var job in jobsToRun)
        //    {
        //        ConsoleAppHelper.WriteLog("Running CCreator for shop - " + job.ShopID, job.ShopID.Value);

        //        try
        //        {
        //            CCreatorResult run_result = CCreator.ExtractData(job);
        //            if (run_result == null || !string.IsNullOrEmpty(run_result.Error))
        //            {
        //                if (sb == null)
        //                {
        //                    sb = new StringBuilder();
        //                }
        //                var error = string.Format("ShopID: <a href='https://www.storeya.com/admin/shopdetails/{0}'>{0}</a> Error: {1}", job.ShopID, run_result.Error);
        //                sb.Append(error).Append("<br/>");
        //                //bugsnag.Notify(new Exception(error));

        //                CCreator.UpdateJobsStatus(job, CCreatorJobStatus.Failed);
        //            }
        //            else
        //            {
        //                CCreator.SaveData(job, run_result);

        //                CCreator.UpdateJobsStatus(job, CCreatorJobStatus.DataCollected);
        //            }
        //        }
        //        catch (Exception ex)
        //        {
        //            ConsoleAppHelper.WriteError("Failed on ccreator job for shoppID:  " + job.ShopID, ex, job.ShopID.Value);
        //        }
        //    }

        //    if (sb != null && !string.IsNullOrEmpty(sb.ToString()))
        //    {
        //        EmailHelper.SendEmail("<EMAIL>", "Failed on CCreator - " + DateTime.Now.ToShortDateString(), sb.ToString());
        //    }
        //}

        private static void TreatBH()
        {

            var shopsToRun = BenchmarksManager.GetBenchmarksToRun();

            Console.WriteLine("");
            Console.WriteLine("Amount of benchmarks to run " + shopsToRun.Count());

            if (shopsToRun.Count() > 10)
            {
                EmailHelper.SendEmail("<EMAIL>", "BH queue is too long (>10)", "Please check https://bo.storeya.com/home/<USER>");
            }

            StringBuilder sb = null;

            foreach (var benchm in shopsToRun)
            {
                ConsoleAppHelper.WriteLog("create report for shop - " + benchm.ShopID, benchm.ShopID.Value);

                BHResult run_result = RunBHForShop(benchm);
                //if (runOK)
                //{
                //    SendReportReadyEmail(benchm.ShopID.Value);

                //    EmailHelper.SendEmail("<EMAIL>", "BH report is ready", @" <a href=""https://www.storeya.com/admin/shopdetails/""" + benchm.ShopID + "> shop details</a>");
                //}

                if (run_result == null || run_result.ReportIsCreated == false)
                {
                    if (sb == null)
                    {
                        sb = new StringBuilder();
                    }

                    string error_message = ((run_result != null) ? run_result.Error : null);
                    if (!string.IsNullOrEmpty(error_message))
                    {
                        var error = string.Format("ShopID: {0}  <a href='https://dev.storeya.com/benchmark?forceShopID={0}&admin=true'>BH data</a>  <a href='https://dev.storeya.com/benchmark/reset?forceShopID={0}'>RESET BH</a> <br/> Ex: {1}", EmailHelper.GetBoLinkHrefAndAM(benchm.ShopID.Value), error_message);
                        sb.Append(error).Append("<br/>");
                        //bugsnag.Notify(new Exception(error));
                    }
                }
            }
            if (sb != null && !string.IsNullOrEmpty(sb.ToString()))
            {
                EmailHelper.SendEmail("<EMAIL>", "Failed to create BH report - " + DateTime.Now.ToShortDateString(), sb.ToString());
            }
        }
        private static void TreatBHApiWebHook(string hostName, string returnUrl)
        {
            TreatBHApi(hostName, true);
            var benchmarkRes = BenchmarkApiManager.GetSettings(hostName);
            if (benchmarkRes != null)
            {
                string json = benchmarkRes.JsonResults;
                Dictionary<string, string> param = new Dictionary<string, string>();
                param.Add("", json);
                HttpPostHelper.Post(returnUrl, param);
            }
        }
        private static void TreatBHApi(string hostName = null, bool onlyMarketing = false)
        {

            var shopsToRun = BenchmarkApiManager.GetBenchmarkApisToRun(hostName);

            Console.WriteLine("");
            Console.WriteLine("Amount of benchmarks Api to run " + shopsToRun.Count());

            if (shopsToRun.Count() > 20)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "BH Api queue is too long (>20)", "Please check https://www.storeya.com/admin/benchmarksApi");
            }

            StringBuilder sb = null;

            foreach (var benchm in shopsToRun)
            {
                ConsoleAppHelper.WriteLog("create report for Host Name - " + benchm.HostName);
                Storeya.Core.Models.AuditTool.BHResult run_result = null;
                if (benchm.HostName.Contains("storeyabh."))
                {
                    var a = benchm.HostName.Split(".".ToCharArray());
                    if (a.Length > 2 && int.TryParse(a[2], out int delay))
                    {
                        System.Threading.Thread.Sleep(delay * 1000);
                    }
                    run_result = new Storeya.Core.Models.AuditTool.BHResult { ReportIsCreated = true };
                }
                else
                {
                    run_result = BenchmarkApiManager.RunAnnalyzation(benchm);
                }

                //if (runOK)
                //{
                //    SendReportReadyEmail(benchm.ShopID.Value);

                //    EmailHelper.SendEmail("<EMAIL>", "BH report is ready", @" <a href=""https://www.storeya.com/admin/shopdetails/""" + benchm.ShopID + "> shop details</a>");
                //}

                if (run_result == null || run_result.ReportIsCreated == false)
                {
                    if (sb == null)
                    {
                        sb = new StringBuilder();
                    }

                    string error_message = ((run_result != null) ? run_result.Error : null);
                    if (!string.IsNullOrEmpty(error_message))
                    {
                        string error = string.Format("Host name:{2} <a href='https://dev.storeya.com/Admin/BenchmarkApiResults?url={0}' >Results</a> <br/> Ex: {1}", benchm.HomePageUrl.UrlEncode(), error_message, benchm.HostName);
                        sb.Append(error).Append("<br/>");
                        //bugsnag.Notify(new Exception(error));
                    }
                }
                if (run_result != null)
                {
                    string resultsError = null;
                    if (run_result.ReportIsCreated == false)
                    {
                        resultsError = run_result.Error;
                    }
                    BenchmarkApiManager.SaveJsonResults(benchm.ID, resultsError, onlyMarketing);
                }

            }
            if (sb != null && !string.IsNullOrEmpty(sb.ToString()))
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Failed to create BH API report - " + DateTime.Now.ToShortDateString(), sb.ToString());
            }

        }

        internal static BHResult RunBHForShop(int shopID)
        {
            var db = DataHelper.GetStoreYaEntities();

            Benchmark b = db.Benchmarks.Where(bh => bh.ShopID == shopID).Single();

            return RunBHForShop(b);
        }

        private static BHResult RunBHForShop(Benchmark shop)
        {
            if (shop.BenchmarkDataSource == (int)BenchmarkDataSources.Shopify)
            {
                return RunBenchmarkAnnalyzation(shop.ShopID.Value);
            }
            else
            {
                return RunBenchmarkAnnalyzationGA(shop.ShopID.Value);
            }
        }

        private static BHResult RunBHApiForShop(BenchmarkApi benchmarkApi)
        {

            return RunBenchmarkAnnalyzation(benchmarkApi.ShopID.Value);

        }

        private static BHResult RunBenchmarkAnnalyzation(int shopID)
        {
            BHResult result = null;

            try
            {
                int statusToUpdate = (int)BenchmarkStatuses.Ready;
                ConsoleAppHelper.WriteLog("Working with shop " + shopID + " " + DateTime.Now);
                AuditTool t = new AuditTool(shopID);
                Console.WriteLine("Getting data. " + DateTime.Now.ToString());
                try
                {
                    t.Data = AuditToolData.GetData(shopID);
                }
                catch (WebException wEx)
                {
                    var webResponse = wEx.Response as System.Net.HttpWebResponse;
                    if (webResponse != null)
                    {
                        if (webResponse.StatusCode == HttpStatusCode.Unauthorized)
                        {
                            BenchmarksManager.SetStatus(shopID, BenchmarkStatuses.ShopifyUnauthorized);
                            return null;
                        }
                    }
                }
                catch (Exception ex)
                {
                    BenchmarksManager.SetStatus(shopID, BenchmarkStatuses.Error);
                    string dataError = ex.ToString();
                    if (t.Data != null && !string.IsNullOrEmpty(t.Data.Error))
                    {
                        dataError += " " + t.Data.Error;
                    }
                    return result = new BHResult() { ReportIsCreated = false, Error = "Failed on AuditToolData.GetData for " + shopID + " " + dataError };
                }

                t.SetMetaDataShopify();

                Console.WriteLine("Data received - calculating grades. " + DateTime.Now.ToString());
                t.CalcOverallValues();
                t.CalculateOrdersSources();
                //t.CalcTechnicalPerformance();
                t.CalcPurchaseExperience();
                t.CalcMarketingScore();
                t.CalcTrustworthyStore();
                t.PublicStore = t.CheckIfNotPublic();
                if (t.PublicStore)
                    t.CheckLLM();
                //t.CheckUrls();
                if (t.Data.HasGooglePixelOnThankYouPage == false)
                {
                    if (t.HasGoogleWithActiveBudget)
                    {
                        t.Data.HasGooglePixelOnThankYouPage = true;
                    }
                }
                if (t.Data.HasGoogleRemarketingCodeOnProductPage == false)
                {
                    if (t.HasGoogleWithActiveBudget)
                    {
                        t.Data.HasGoogleRemarketingCodeOnProductPage = true;
                    }
                }
                else
                {
                    TrafficBooster existingTbSettings = TrafficBoostersDbHelper.GetSettings(shopID);
                    if (existingTbSettings != null && TbSettingsHelper.IsDefaultBudgetActiveAndPaid(existingTbSettings))
                    {
                        t.Data.HasGoogleRemarketingCodeOnProductPage = true;
                    }
                }

                if (t.WebsiteIsPasswordProtected())
                {
                    statusToUpdate = (int)BenchmarkStatuses.WebsiteIsPasswordProtected;
                }

                BenchmarksManager.Update(shopID, t, statusToUpdate, isPublicStore: t.PublicStore);

                ConsoleAppHelper.WriteLog(" shop " + shopID + ". Done at " + DateTime.Now);

                result = new BHResult() { ReportIsCreated = true };

                if (!string.IsNullOrEmpty(t.Data.Error))
                {
                    EmailHelper.SendEmail("<EMAIL>", "Failed on AuditToolData.GetData for " + shopID + " " + DateTime.Now.ToShortDateString(), t.Data.Error);
                }

            }
            catch (Exception ex)
            {
                var exString = ex.ToString();
                if (ex is DbEntityValidationException)
                {
                    var newException = new FormattedDbEntityValidationException((DbEntityValidationException)ex);
                    exString = newException.ToString();
                }

                BenchmarksManager.SetStatus(shopID, BenchmarkStatuses.Error);
                ConsoleAppHelper.WriteLog("Failed RunBenchmarkAnnalyzation. " + exString, shopID);

                //Console.WriteLine(string.Format("ShopID: {0} Ex: {1}", shopID, ex.Message));
                //Log4NetLogger.Error("Failed on getting data for Benchmarks Hero tool.", ex, shopID);

                result = new BHResult() { ReportIsCreated = false, Error = exString };
            }
            return result;
        }


        private static BHResult RunBenchmarkAnnalyzationGA(int shopID)
        {
            BHResult result = null;

            try
            {
                ConsoleAppHelper.WriteLog("Working with shop " + shopID + " " + DateTime.Now);
                AuditTool t = new AuditTool();
                Console.WriteLine("Getting data. " + DateTime.Now.ToString());

                AuditToolGaData data = AuditToolData.GetDataWithGA_new(shopID);
                if (data != null)
                {
                    t.Data = new AuditToolData();
                    t.Data.HomePageUrl = data.HomePageUrl;
                    t.Data.HomePageHtml = data.HomePageHtml;
                    //t.Data.SiteMapUrl = data.SiteMapUrl;
                    t.Data.SiteMapUrls = data.SiteMapUrls;

                    t.GaData = data;
                    if (t.GaData != null)
                    {
                        t.Data.DataRangeInMonths = t.GaData.DataRangeInMonths;
                        if (t.GaData.Transactions != 0)
                            t.Results.AverageOrderAmount = (t.GaData.Revenue ?? 0) / t.GaData.Transactions;
                    }

                    t.SetMetaData();

                    Console.WriteLine("Data recieved - calculating grades. " + DateTime.Now.ToString());

                    //t.CalcOverallValues(); ga - no orders or products data 
                    t.CalcConversionRatesForGaData();
                    //t.CalcTechnicalPerformance();
                    t.CalcPurchaseExperience();
                    t.CalcMarketingScore();

                    if (t.Data.HasGaOnHomePage == false)
                        t.Data.HasGaOnHomePage = true;

                    if (t.Data.HasGooglePixelOnThankYouPage == false)
                    {
                        if (t.GaData.AdCost > 0)
                        {
                            t.Data.HasGooglePixelOnThankYouPage = true;
                        }
                        else
                        {
                            TrafficBooster existingTbSettings = TrafficBoostersDbHelper.GetSettings(shopID);
                            if (existingTbSettings != null && TbSettingsHelper.IsDefaultBudgetActiveAndPaid(existingTbSettings))
                            {
                                t.Data.HasGooglePixelOnThankYouPage = true;
                            }
                        }
                    }

                    if (t.Data.HasGoogleRemarketingCodeOnProductPage == false
                        && !string.IsNullOrEmpty(t.Data.HomePageHtml)
                        && AuditTool.HasRemarketingTag(t.Data.HomePageHtml))
                    {
                        t.Data.HasGoogleRemarketingCodeOnProductPage = true;
                    }
                    if (t.Data.HasGoogleRemarketingCodeOnProductPage == false || t.Data.HasGoogleMerchantCenterOnHomepage == false || t.Data.HasGooglePixelOnThankYouPage == false)
                    {
                        TrafficBooster existingTbSettings = TrafficBoostersDbHelper.GetSettings(shopID);
                        if (existingTbSettings != null && TbSettingsHelper.IsDefaultBudgetActiveAndPaid(existingTbSettings))
                        {
                            t.Data.HasGoogleRemarketingCodeOnProductPage = true;
                            t.Data.HasGoogleMerchantCenterOnHomepage = true;
                            t.Data.HasGooglePixelOnThankYouPage = true;
                        }
                    }


                    t.CalcTrustworthyStore();
                    t.CheckRobotsAndSitemapsUrls();
                    t.CheckUrls_GA();


                    BenchmarksManager.Update(shopID, t, (int)BenchmarkStatuses.Ready);

                    ConsoleAppHelper.WriteLog(" shop " + shopID + ". Done at " + DateTime.Now);

                    result = new BHResult() { ReportIsCreated = true };

                }
                else
                {
                    result = new BHResult() { ReportIsCreated = false, Error = "The Ga data is not valid, so BH report was not created." };
                    BenchmarksManager.SetStatus(shopID, BenchmarkStatuses.Error);
                }
            }
            catch (Exception ex)
            {
                BenchmarksManager.SetStatus(shopID, BenchmarkStatuses.Error);

                var exString = ex.ToString();
                if (ex is DbEntityValidationException)
                {
                    var newException = new FormattedDbEntityValidationException((DbEntityValidationException)ex);
                    exString = newException.ToString();
                }

                //BenchmarksManager.SetStatus(shopID, BenchmarkStatuses.Error);
                ConsoleAppHelper.WriteLog("Failed RunBenchmarkAnnalyzationGA. " + exString, shopID);

                //Console.WriteLine(string.Format("ShopID: {0} Ex: {1}", shopID, ex.Message));
                //Log4NetLogger.Error("Failed on getting data for Benchmarks Hero tool.", ex, shopID);

                result = new BHResult() { ReportIsCreated = false, Error = ex.ToString() };
            }
            return result;
        }

        private static void SendReportReadyEmail(int shopID)
        {
            string subject = "Your Benchmark Hero Report is Ready!";
            string template = "BenchmarkHero/report_is_ready.html";
            MailAddress address = EmailHelper.GetUserNameAndEmailAdressForEmail(shopID);
            object email_data = new { Name = address.DisplayName };
            SendEmail(address.Address, subject, template, email_data);
        }

        private static void SendEmail(string sendTo, string subject, string template, object email_data)
        {
            string html = GetHtml(template, email_data);
            EmailHelper.SendEmail(sendTo, subject, html);
        }

        public static string GetHtml(string template, object data)
        {
            string htmlTemplate = EmailHelper.GetTemplateContent(template);

            return htmlTemplate.FormatWith(data);
        }



    }

    public class BHResult
    {
        public bool ReportIsCreated { get; set; }
        public string Error { get; set; }
    }
}
