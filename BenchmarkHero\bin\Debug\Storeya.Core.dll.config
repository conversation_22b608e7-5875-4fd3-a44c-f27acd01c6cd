﻿<?xml version="1.0" encoding="utf-8"?>

<configuration>
	<configSections>
		
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
	<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 --></configSections>
	<connectionStrings>
		<!--<add name="ApplicationServices" connectionString="data source=.\SQLEXPRESS;Integrated Security=SSPI;AttachDBFilename=|DataDirectory|aspnetdb.mdf;User Instance=true" providerName="System.Data.SqlClient" />-->
		<add name="StoreYaEntities" connectionString="metadata=res://*/DbModels.StoreYaDB.csdl|res://*/DbModels.StoreYaDB.ssdl|res://*/DbModels.StoreYaDB.msl;provider=System.Data.SqlClient;provider connection string='data source=DESKTOP-LEDFE56;Initial Catalog=StoreYa;User ID=Storeyauser;Password=**********;MultipleActiveResultSets=True;App=EntityFramework'" providerName="System.Data.EntityClient" />
		<!--<add name="StoreYaEntities" connectionString="metadata=res://*/DbModels.StoreYaDB.csdl|res://*/DbModels.StoreYaDB.ssdl|res://*/DbModels.StoreYaDB.msl;provider=System.Data.SqlClient;provider connection string='data source=DESKTOP-41M93JU\SQLEXPRESS;Initial Catalog=StoreYa;User ID=olga;Password=*******;MultipleActiveResultSets=True;App=EntityFramework'" providerName="System.Data.EntityClient" />-->
		<!--<add name="StoreYaEntities" connectionString="metadata=res://*/StoreYaDB.csdl|res://*/StoreYaDB.ssdl|res://*/StoreYaDB.msl;provider=System.Data.SqlClient;provider connection string='Data Source=.\SQLEXPRESS;;Initial Catalog=StoreYa;User ID=sa;Password=********;MultipleActiveResultSets=True'" providerName="System.Data.EntityClient" />-->
		<!--<add name="StoreYaEntities" connectionString="metadata=res://*/StoreYaDB.csdl|res://*/StoreYaDB.ssdl|res://*/StoreYaDB.msl;provider=System.Data.SqlClient;provider connection string='Data Source=.\SQLEXPRESS;AttachDbFilename=&quot;C:\Program Files\Microsoft SQL Server\MSSQL.1\MSSQL\Data\StoreYa.mdf&quot;;Integrated Security=True;Connect Timeout=30;User Instance=True;MultipleActiveResultSets=True'" providerName="System.Data.EntityClient" />-->
		<!--<add name="StoreYaEntities" connectionString="metadata=res://*/StoreYaDB.csdl|res://*/StoreYaDB.ssdl|res://*/StoreYaDB.msl;provider=System.Data.SqlClient;provider connection string='Data Source=STOREYA-PC\SQLEXPRESS;Initial Catalog=StoreYa;User ID=olga;Password=*******;MultipleActiveResultSets=True'" providerName="System.Data.EntityClient" />-->
	</connectionStrings>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2" />
	</startup>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
			<parameters>
				<parameter value="mssqllocaldb" />
			</parameters>
		</defaultConnectionFactory>
		<providers>
			<provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
		</providers>
	</entityFramework>

</configuration>

