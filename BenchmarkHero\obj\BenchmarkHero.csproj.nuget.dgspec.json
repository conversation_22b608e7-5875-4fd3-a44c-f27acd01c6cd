{"format": 1, "restore": {"C:\\dev\\storeya\\trunk\\BenchmarkHero\\BenchmarkHero\\BenchmarkHero.csproj": {}}, "projects": {"C:\\dev\\storeya\\trunk\\BenchmarkHero\\BenchmarkHero\\BenchmarkHero.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\dev\\storeya\\trunk\\BenchmarkHero\\BenchmarkHero\\BenchmarkHero.csproj", "projectName": "BenchmarkHero", "projectPath": "C:\\dev\\storeya\\trunk\\BenchmarkHero\\BenchmarkHero\\BenchmarkHero.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\dev\\storeya\\trunk\\BenchmarkHero\\BenchmarkHero\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\dev\\storeya\\trunk\\BenchmarkHero\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {"C:\\dev\\storeya\\trunk\\Storeya.Core\\Storeya.Core.csproj": {"projectPath": "C:\\dev\\storeya\\trunk\\Storeya.Core\\Storeya.Core.csproj"}}}}}, "frameworks": {"net48": {"dependencies": {"Magick.NET-Q16-AnyCPU": {"target": "Package", "version": "[14.8.0, )"}, "SkiaSharp": {"target": "Package", "version": "[3.119.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "C:\\dev\\storeya\\trunk\\Storeya.Core\\Storeya.Core.csproj": {"restore": {"projectUniqueName": "C:\\dev\\storeya\\trunk\\Storeya.Core\\Storeya.Core.csproj", "projectName": "Storeya.Core", "projectPath": "C:\\dev\\storeya\\trunk\\Storeya.Core\\Storeya.Core.csproj", "frameworks": {"net48": {"projectReferences": {}}}}, "frameworks": {"net48": {}}}}}