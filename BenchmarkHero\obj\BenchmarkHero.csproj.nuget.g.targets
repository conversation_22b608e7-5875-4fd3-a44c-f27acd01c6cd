﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)skiasharp.nativeassets.win32\3.119.0\buildTransitive\net462\SkiaSharp.NativeAssets.Win32.targets" Condition="Exists('$(NuGetPackageRoot)skiasharp.nativeassets.win32\3.119.0\buildTransitive\net462\SkiaSharp.NativeAssets.Win32.targets')" />
    <Import Project="$(NuGetPackageRoot)skiasharp.nativeassets.macos\3.119.0\buildTransitive\net462\SkiaSharp.NativeAssets.macOS.targets" Condition="Exists('$(NuGetPackageRoot)skiasharp.nativeassets.macos\3.119.0\buildTransitive\net462\SkiaSharp.NativeAssets.macOS.targets')" />
    <Import Project="$(NuGetPackageRoot)magick.net-q16-anycpu\14.8.0\buildTransitive\netstandard20\Magick.NET-Q16-AnyCPU.targets" Condition="Exists('$(NuGetPackageRoot)magick.net-q16-anycpu\14.8.0\buildTransitive\netstandard20\Magick.NET-Q16-AnyCPU.targets')" />
  </ImportGroup>
</Project>