C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Magick.Native-Q16-arm64.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Magick.Native-Q16-x64.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Magick.Native-Q16-x86.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\bin\System.ValueTuple.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\BenchmarkHero.exe.config
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\BenchmarkHero.exe
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\BenchmarkHero.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Magick.NET-Q16-AnyCPU.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Magick.NET.Core.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\EntityFramework.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\EntityFramework.SqlServer.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Newtonsoft.Json.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\RestSharp.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Storeya.Core.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\log4net.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\CsvHelper.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\HtmlAgilityPack.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\MailChimp.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Postmark.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\BouncyCastle.Cryptography.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\createsend-dotnet.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\CTCT.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Kent.Boogaart.KBCsv.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\AWSSDK.S3.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\NReco.PdfGenerator.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\nQuant.Core.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\jose-jwt.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\ServiceStack.Text.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Hammock.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Kent.Boogaart.HelperTrinity.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\AWSSDK.Core.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Storeya.Core.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\Storeya.Core.dll.config
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\EntityFramework.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\EntityFramework.SqlServer.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\CsvHelper.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\CsvHelper.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\HtmlAgilityPack.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\HtmlAgilityPack.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\BouncyCastle.Cryptography.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\createsend-dotnet.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\CTCT.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\CTCT.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\AWSSDK.S3.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\AWSSDK.S3.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\NReco.PdfGenerator.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\nQuant.Core.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\ServiceStack.Text.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\AWSSDK.Core.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\AWSSDK.Core.xml
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\en\Storeya.Core.resources.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\es\Storeya.Core.resources.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\fr\Storeya.Core.resources.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\obj\Debug\BenchmarkHero.csproj.AssemblyReference.cache
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\obj\Debug\BenchmarkHero.csproj.CoreCompileInputs.cache
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\obj\Debug\BenchmarkHero.csproj.CopyComplete
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\obj\Debug\BenchmarkHero.exe
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\obj\Debug\BenchmarkHero.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\libSkiaSharp.dylib
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\x86\libSkiaSharp.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\x64\libSkiaSharp.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\arm64\libSkiaSharp.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\SkiaSharp.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\SkiaSharp.pdb
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\System.Buffers.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\System.Memory.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\System.Numerics.Vectors.dll
C:\dev\storeya\trunk\BenchmarkHero\BenchmarkHero\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
