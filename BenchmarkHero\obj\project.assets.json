{"version": 3, "targets": {".NETFramework,Version=v4.8": {"Magick.NET-Q16-AnyCPU/14.8.0": {"type": "package", "dependencies": {"Magick.NET.Core": "14.8.0"}, "compile": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard20/Magick.NET-Q16-AnyCPU.targets": {}}, "runtimeTargets": {"runtimes/linux-arm64/native/Magick.Native-Q16-arm64.dll.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/Magick.Native-Q16-x64.dll.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/Magick.Native-Q16-x64.dll.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/Magick.Native-Q16-arm64.dll.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/Magick.Native-Q16-x64.dll.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm64/native/Magick.Native-Q16-arm64.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Magick.Native-Q16-x64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Magick.Native-Q16-x86.dll": {"assetType": "native", "rid": "win-x86"}}}, "Magick.NET.Core/14.8.0": {"type": "package", "compile": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}}, "SkiaSharp/3.119.0": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0", "SkiaSharp.NativeAssets.macOS": "3.119.0", "System.Memory": "4.5.5"}, "compile": {"ref/net462/SkiaSharp.dll": {}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "Storeya.Core/1.0.0": {"type": "project", "compile": {"bin/placeholder/Storeya.Core.dll": {}}, "runtime": {"bin/placeholder/Storeya.Core.dll": {}}}}, ".NETFramework,Version=v4.8/win": {"Magick.NET-Q16-AnyCPU/14.8.0": {"type": "package", "dependencies": {"Magick.NET.Core": "14.8.0"}, "compile": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard20/Magick.NET-Q16-AnyCPU.targets": {}}}, "Magick.NET.Core/14.8.0": {"type": "package", "compile": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}}, "SkiaSharp/3.119.0": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0", "SkiaSharp.NativeAssets.macOS": "3.119.0", "System.Memory": "4.5.5"}, "compile": {"ref/net462/SkiaSharp.dll": {}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "Storeya.Core/1.0.0": {"type": "project", "compile": {"bin/placeholder/Storeya.Core.dll": {}}, "runtime": {"bin/placeholder/Storeya.Core.dll": {}}}}, ".NETFramework,Version=v4.8/win-arm64": {"Magick.NET-Q16-AnyCPU/14.8.0": {"type": "package", "dependencies": {"Magick.NET.Core": "14.8.0"}, "compile": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "native": {"runtimes/win-arm64/native/Magick.Native-Q16-arm64.dll": {}}, "build": {"buildTransitive/netstandard20/Magick.NET-Q16-AnyCPU.targets": {}}}, "Magick.NET.Core/14.8.0": {"type": "package", "compile": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}}, "SkiaSharp/3.119.0": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0", "SkiaSharp.NativeAssets.macOS": "3.119.0", "System.Memory": "4.5.5"}, "compile": {"ref/net462/SkiaSharp.dll": {}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "native": {"runtimes/win-arm64/native/libSkiaSharp.dll": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "Storeya.Core/1.0.0": {"type": "project", "compile": {"bin/placeholder/Storeya.Core.dll": {}}, "runtime": {"bin/placeholder/Storeya.Core.dll": {}}}}, ".NETFramework,Version=v4.8/win-x64": {"Magick.NET-Q16-AnyCPU/14.8.0": {"type": "package", "dependencies": {"Magick.NET.Core": "14.8.0"}, "compile": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "native": {"runtimes/win-x64/native/Magick.Native-Q16-x64.dll": {}}, "build": {"buildTransitive/netstandard20/Magick.NET-Q16-AnyCPU.targets": {}}}, "Magick.NET.Core/14.8.0": {"type": "package", "compile": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}}, "SkiaSharp/3.119.0": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0", "SkiaSharp.NativeAssets.macOS": "3.119.0", "System.Memory": "4.5.5"}, "compile": {"ref/net462/SkiaSharp.dll": {}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "native": {"runtimes/win-x64/native/libSkiaSharp.dll": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "Storeya.Core/1.0.0": {"type": "project", "compile": {"bin/placeholder/Storeya.Core.dll": {}}, "runtime": {"bin/placeholder/Storeya.Core.dll": {}}}}, ".NETFramework,Version=v4.8/win-x86": {"Magick.NET-Q16-AnyCPU/14.8.0": {"type": "package", "dependencies": {"Magick.NET.Core": "14.8.0"}, "compile": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"related": ".xml"}}, "native": {"runtimes/win-x86/native/Magick.Native-Q16-x86.dll": {}}, "build": {"buildTransitive/netstandard20/Magick.NET-Q16-AnyCPU.targets": {}}}, "Magick.NET.Core/14.8.0": {"type": "package", "compile": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard20/Magick.NET.Core.dll": {"related": ".xml"}}}, "SkiaSharp/3.119.0": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0", "SkiaSharp.NativeAssets.macOS": "3.119.0", "System.Memory": "4.5.5"}, "compile": {"ref/net462/SkiaSharp.dll": {}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "native": {"runtimes/win-x86/native/libSkiaSharp.dll": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "Storeya.Core/1.0.0": {"type": "project", "compile": {"bin/placeholder/Storeya.Core.dll": {}}, "runtime": {"bin/placeholder/Storeya.Core.dll": {}}}}}, "libraries": {"Magick.NET-Q16-AnyCPU/14.8.0": {"sha512": "8Qbn1X8A4HWb3PHu52FyNYJvrx04CEFO2VNCkyfo9Pax0LUYTwC8ZQh2OEzTQj4xDOE2jjzg5jygVdM/aenNkg==", "type": "package", "path": "magick.net-q16-anycpu/14.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Magick.NET.icon.png", "Notice.txt", "build/netstandard20/Magick.NET-Q16-AnyCPU.targets", "buildTransitive/netstandard20/Magick.NET-Q16-AnyCPU.targets", "docs/Readme.md", "lib/net8.0/Magick.NET-Q16-AnyCPU.dll", "lib/net8.0/Magick.NET-Q16-AnyCPU.xml", "lib/netstandard20/Magick.NET-Q16-AnyCPU.dll", "lib/netstandard20/Magick.NET-Q16-AnyCPU.xml", "magick.net-q16-anycpu.14.8.0.nupkg.sha512", "magick.net-q16-anycpu.nuspec", "runtimes/linux-arm64/native/Magick.Native-Q16-arm64.dll.so", "runtimes/linux-musl-x64/native/Magick.Native-Q16-x64.dll.so", "runtimes/linux-x64/native/Magick.Native-Q16-x64.dll.so", "runtimes/osx-arm64/native/Magick.Native-Q16-arm64.dll.dylib", "runtimes/osx-x64/native/Magick.Native-Q16-x64.dll.dylib", "runtimes/win-arm64/native/Magick.Native-Q16-arm64.dll", "runtimes/win-x64/native/Magick.Native-Q16-x64.dll", "runtimes/win-x86/native/Magick.Native-Q16-x86.dll"]}, "Magick.NET.Core/14.8.0": {"sha512": "H5mrY7/vF9bz+rzCXiJvWnE4IrFQXNxaMWikfrwejFIqtsyDBigaR3U4x6KOZjbO8lo2/dXTuqoYfKgwC+QpJg==", "type": "package", "path": "magick.net.core/14.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Copyright.txt", "Magick.NET.icon.png", "docs/Readme.md", "lib/net8.0/Magick.NET.Core.dll", "lib/net8.0/Magick.NET.Core.xml", "lib/netstandard20/Magick.NET.Core.dll", "lib/netstandard20/Magick.NET.Core.xml", "magick.net.core.14.8.0.nupkg.sha512", "magick.net.core.nuspec"]}, "SkiaSharp/3.119.0": {"sha512": "gR9yVoOta2Mc1Rxt15LD65AckfHMfwjIs/3kkD59C9bT2nYYISsE6uz3t4aMPNHA6CgsIL0Ssn+jE5OVilZ1yw==", "type": "package", "path": "skiasharp/3.119.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net8.0-android34.0/SkiaSharp.dll", "lib/net8.0-android34.0/SkiaSharp.pdb", "lib/net8.0-android34.0/SkiaSharp.xml", "lib/net8.0-ios17.0/SkiaSharp.dll", "lib/net8.0-ios17.0/SkiaSharp.pdb", "lib/net8.0-maccatalyst17.0/SkiaSharp.dll", "lib/net8.0-maccatalyst17.0/SkiaSharp.pdb", "lib/net8.0-macos14.0/SkiaSharp.dll", "lib/net8.0-macos14.0/SkiaSharp.pdb", "lib/net8.0-tizen7.0/SkiaSharp.dll", "lib/net8.0-tizen7.0/SkiaSharp.pdb", "lib/net8.0-tvos17.0/SkiaSharp.dll", "lib/net8.0-tvos17.0/SkiaSharp.pdb", "lib/net8.0-windows10.0.19041/SkiaSharp.dll", "lib/net8.0-windows10.0.19041/SkiaSharp.pdb", "lib/net8.0/SkiaSharp.dll", "lib/net8.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "ref/net462/SkiaSharp.dll", "ref/net6.0/SkiaSharp.dll", "ref/net8.0-android34.0/SkiaSharp.dll", "ref/net8.0-ios17.0/SkiaSharp.dll", "ref/net8.0-maccatalyst17.0/SkiaSharp.dll", "ref/net8.0-macos14.0/SkiaSharp.dll", "ref/net8.0-tizen7.0/SkiaSharp.dll", "ref/net8.0-tvos17.0/SkiaSharp.dll", "ref/net8.0-windows10.0.19041/SkiaSharp.dll", "ref/net8.0/SkiaSharp.dll", "ref/netstandard2.0/SkiaSharp.dll", "ref/netstandard2.1/SkiaSharp.dll", "skiasharp.3.119.0.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"sha512": "YE1vNn0Nyw2PWtv7hw1PYkKJO0itFiQp9vSqGppZUKzQJqwp28a2jgdCMPfYtOiR8KCnDgZqQoynqJRRaE2ZVg==", "type": "package", "path": "skiasharp.nativeassets.macos/3.119.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net8.0-macos14.0/SkiaSharp.NativeAssets.macOS.targets", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0-macos14.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.3.119.0.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"sha512": "IwC9yx36lOdXVT2DjgmWHl1qkVspfj8ctd4+li8CNnvqdfaTolXCOh6TLznURcPAvzatx9K/tLOB7zT6T8EA9w==", "type": "package", "path": "skiasharp.nativeassets.win32/3.119.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "icon.png", "lib/net462/_._", "lib/net6.0-windows10.0.19041/_._", "lib/net6.0/_._", "lib/net8.0-windows10.0.19041/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.3.119.0.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"sha512": "3TIsJhD1EiiT0w2CcDMN/iSSwnNnsrnbzeVHSKkaEgV85txMprmuO+Yq2AdSbeVGcg28pdNDTPK87tJhX7VFHw==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.5.3.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Storeya.Core/1.0.0": {"type": "project", "path": "../../Storeya.Core/Storeya.Core.csproj", "msbuildProject": "../../Storeya.Core/Storeya.Core.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["Magick.NET-Q16-AnyCPU >= 14.8.0", "SkiaSharp >= 3.119.0", "Storeya.Core >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\dev\\storeya\\trunk\\BenchmarkHero\\BenchmarkHero\\BenchmarkHero.csproj", "projectName": "BenchmarkHero", "projectPath": "C:\\dev\\storeya\\trunk\\BenchmarkHero\\BenchmarkHero\\BenchmarkHero.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\dev\\storeya\\trunk\\BenchmarkHero\\BenchmarkHero\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\dev\\storeya\\trunk\\BenchmarkHero\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {"C:\\dev\\storeya\\trunk\\Storeya.Core\\Storeya.Core.csproj": {"projectPath": "C:\\dev\\storeya\\trunk\\Storeya.Core\\Storeya.Core.csproj"}}}}}, "frameworks": {"net48": {"dependencies": {"Magick.NET-Q16-AnyCPU": {"target": "Package", "version": "[14.8.0, )"}, "SkiaSharp": {"target": "Package", "version": "[3.119.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}