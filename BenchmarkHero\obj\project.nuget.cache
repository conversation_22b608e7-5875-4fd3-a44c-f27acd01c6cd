{"version": 2, "dgSpecHash": "WM32zIe1E/y73VrhRS9GRN0ObdlaZqo2aomZRToc3mb8/BZoK743I9h29PIT+8eC9Tgm7lyKFlxY1nF5NLY7VQ==", "success": true, "projectFilePath": "C:\\dev\\storeya\\trunk\\BenchmarkHero\\BenchmarkHero\\BenchmarkHero.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\magick.net-q16-anycpu\\14.8.0\\magick.net-q16-anycpu.14.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\magick.net.core\\14.8.0\\magick.net.core.14.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\3.119.0\\skiasharp.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\3.119.0\\skiasharp.nativeassets.macos.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\3.119.0\\skiasharp.nativeassets.win32.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.5.3\\system.runtime.compilerservices.unsafe.4.5.3.nupkg.sha512"], "logs": []}