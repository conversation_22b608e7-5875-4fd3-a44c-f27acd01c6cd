//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class AISeoProductOptimizerHistory
    {
        public int Id { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public int Status { get; set; }
        public int ShopId { get; set; }
        public long ProductID { get; set; }
        public string Description { get; set; }
        public string GeneratedDescription { get; set; }
        public string Title { get; set; }
        public string GeneratedTitle { get; set; }
        public string MetaTitle { get; set; }
        public string MetaDescription { get; set; }
        public string GeneratedMetaTitle { get; set; }
        public string GeneratedMetaDescription { get; set; }
        public string TypeOfPrompt { get; set; }
        public Nullable<decimal> ApiResponseTime { get; set; }
        public string AIProduct { get; set; }
        public Nullable<int> RetryCount { get; set; }
        public string ProductUrl { get; set; }
    }
}
