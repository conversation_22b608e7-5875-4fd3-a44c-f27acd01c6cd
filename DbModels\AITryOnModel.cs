//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class AITryOnModel
    {
        public int Id { get; set; }
        public System.DateTime CreatedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public int ShopId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public string Status { get; set; }
        public Nullable<double> Accuracy { get; set; }
        public Nullable<int> TrainingProgress { get; set; }
        public Nullable<System.DateTime> LastUsed { get; set; }
        public Nullable<int> ImageCount { get; set; }
        public string Image { get; set; }
        public string Ethnicity { get; set; }
        public string AgeRange { get; set; }
        public string BodyType { get; set; }
        public string Height { get; set; }
        public string Gender { get; set; }
        public string HairColor { get; set; }
        public string EyeColor { get; set; }
        public bool UseAsBrandModel { get; set; }
    }
}
