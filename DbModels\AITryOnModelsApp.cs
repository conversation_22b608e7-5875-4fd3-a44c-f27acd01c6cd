//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class AITryOnModelsApp
    {
        public int Id { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> ShopId { get; set; }
        public int Status { get; set; }
        public string ShopifyShopName { get; set; }
        public string ShopUrl { get; set; }
        public string MetaTitle { get; set; }
        public string MetaDescription { get; set; }
        public Nullable<int> LeftAReview { get; set; }
        public Nullable<int> PaymentStatus { get; set; }
        public Nullable<double> TotalPaid { get; set; }
        public Nullable<System.DateTime> FirstPaymentAt { get; set; }
        public Nullable<System.DateTime> LastPaymentAt { get; set; }
        public Nullable<System.DateTime> NextPaymentAt { get; set; }
        public Nullable<int> CreditLimit { get; set; }
        public Nullable<int> CreditUsage { get; set; }
        public Nullable<int> PaymentPlanId { get; set; }
    }
}
