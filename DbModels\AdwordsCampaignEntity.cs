//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class AdwordsCampaignEntity
    {
        public int Id { get; set; }
        public int ShopId { get; set; }
        public Nullable<long> CampaignId { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> Status { get; set; }
        public int Type { get; set; }
        public string CampaignName { get; set; }
        public string EntityResourceName { get; set; }
        public string EntityStatus { get; set; }
        public string Value { get; set; }
        public Nullable<int> Clicks { get; set; }
        public Nullable<decimal> Cost { get; set; }
        public Nullable<decimal> Conversions { get; set; }
        public Nullable<decimal> CpaInUsd { get; set; }
        public Nullable<int> RemoveStatus { get; set; }
        public Nullable<decimal> AverageCpa { get; set; }
        public Nullable<decimal> Revenues { get; set; }
        public Nullable<decimal> Roas { get; set; }
        public Nullable<decimal> CampaignCost { get; set; }
    }
}
