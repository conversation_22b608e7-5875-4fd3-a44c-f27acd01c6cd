//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class AllOrder
    {
        public int ID { get; set; }
        public int ShopID { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public string Currency { get; set; }
        public Nullable<long> CustomerID { get; set; }
        public Nullable<System.DateTime> CustomerInsertedAt { get; set; }
        public string CustomerEmail { get; set; }
        public string CustomerFirstName { get; set; }
        public string CustomerLastName { get; set; }
        public Nullable<int> CustomerOrdersCount { get; set; }
        public Nullable<decimal> CustomerTotalSpend { get; set; }
        public Nullable<System.DateTime> CustomerUpdatedAt { get; set; }
        public string AllCoupons { get; set; }
        public Nullable<int> OriginalOrderID { get; set; }
        public string OrderName { get; set; }
        public Nullable<decimal> TotalDiscounts { get; set; }
        public Nullable<decimal> TotalItemsPrice { get; set; }
        public Nullable<decimal> TotalPrice { get; set; }
        public Nullable<decimal> TotalPriceUsd { get; set; }
        public Nullable<int> OurCouponUsed { get; set; }
        public Nullable<int> WidgetID { get; set; }
        public Nullable<int> AppTypeID { get; set; }
        public string LandingSite { get; set; }
    }
}
