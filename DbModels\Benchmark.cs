//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Benchmark
    {
        public int ID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<System.DateTime> RunAt { get; set; }
        public Nullable<int> ShopID { get; set; }
        public string HomePageUrl { get; set; }
        public string ShopifyShopName { get; set; }
        public string CanonicalUrl { get; set; }
        public string ProductPageUrl { get; set; }
        public string OrderConfirmationPageUrl { get; set; }
        public string ContactUsUrl { get; set; }
        public string ShippingInfoUrl { get; set; }
        public string ReturnPolicyUrl { get; set; }
        public Nullable<int> DataRangeInMonths { get; set; }
        public Nullable<int> HasFbPixelOnProductPage { get; set; }
        public Nullable<int> HasGaOnHomePage { get; set; }
        public Nullable<int> HasGoogleRemarketingCodeOnProductPage { get; set; }
        public Nullable<int> HasGooglePixelOnThankYouPage { get; set; }
        public Nullable<int> HasGoogleMerchantCenterOnHomepage { get; set; }
        public Nullable<int> HomePageHasReviews { get; set; }
        public Nullable<int> ProductPageHasReviews { get; set; }
        public Nullable<int> TrustBadges { get; set; }
        public Nullable<int> RedirectsToCanonical { get; set; }
        public Nullable<int> RedirectsFromHttpToHttps { get; set; }
        public Nullable<int> OrdersAmount { get; set; }
        public Nullable<int> ProductsAmount { get; set; }
        public Nullable<decimal> AverageOrderAmount { get; set; }
        public Nullable<decimal> Revenues { get; set; }
        public Nullable<decimal> PercentageOfDiscountedItems { get; set; }
        public Nullable<decimal> ReturningCustomers { get; set; }
        public Nullable<decimal> CustomerLifetimeValue { get; set; }
        public Nullable<int> DescriptionLenght { get; set; }
        public string DesktopPerformanceStrategy { get; set; }
        public Nullable<int> DesktopPerformanceScore { get; set; }
        public Nullable<int> DesktopPerformanceImagesAmount { get; set; }
        public Nullable<int> Status { get; set; }
        public Nullable<int> MobilePerformanceImagesReduction { get; set; }
        public Nullable<int> DesktopPerformanceImagesReduction { get; set; }
        public Nullable<int> HasSocialLinks { get; set; }
        public Nullable<int> HasChatOrMessenger { get; set; }
        public Nullable<int> OnSitePromotions { get; set; }
        public Nullable<int> MobilePerformanceScore { get; set; }
        public Nullable<int> MobilePerformanceImagesAmount { get; set; }
        public Nullable<int> BenchmarkDataSource { get; set; }
        public Nullable<int> OverallConversionRate { get; set; }
        public Nullable<int> GoogleOrganicConversionRate { get; set; }
        public Nullable<int> DesktopConversionRate { get; set; }
        public Nullable<int> MobileConversionRate { get; set; }
        public string ConversionByChannelData { get; set; }
        public Nullable<int> Sessions { get; set; }
        public Nullable<decimal> GaOverallConversionRate { get; set; }
        public Nullable<decimal> GaGoogleOrganicConversionRate { get; set; }
        public Nullable<decimal> GaDesktopConversionRate { get; set; }
        public Nullable<decimal> GaMobileConversionRate { get; set; }
        public Nullable<decimal> AdCost { get; set; }
        public Nullable<decimal> AdRevenue { get; set; }
        public Nullable<int> LandingPagesCount { get; set; }
        public string H1 { get; set; }
        public string H2 { get; set; }
        public string MetaTitle { get; set; }
        public string MetaDescription { get; set; }
        public string Images { get; set; }
        public string ImageTagsWithEmptyOrMissingAltAttribute { get; set; }
        public string DeprecatedHtmlTags { get; set; }
        public string SiteMapUrl { get; set; }
        public string RobotsTxtUrl { get; set; }
        public string FaviconIcoUrl { get; set; }
        public Nullable<int> ProductImagesCountFoundOnHP { get; set; }
        public string PreserveAdminValues { get; set; }
        public string LlmUrl { get; set; }
        public Nullable<int> HasBingPixelOnProductPage { get; set; }
    }
}
