//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class BenchmarkImagesOptimization
    {
        public int Id { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public int ShopId { get; set; }
        public int Status { get; set; }
        public int Step { get; set; }
        public Nullable<System.DateTime> StepStartedAt { get; set; }
        public Nullable<System.DateTime> StepEndedAt { get; set; }
        public Nullable<int> Progress { get; set; }
        public string MetaFileUrl { get; set; }
        public string SetQualityTo { get; set; }
        public Nullable<int> TotalImages { get; set; }
        public Nullable<int> TotalImagesToOptimize { get; set; }
        public Nullable<int> ImagesToResize { get; set; }
        public Nullable<int> ImagesToCompress { get; set; }
        public Nullable<int> ImagesMissingAltTag { get; set; }
        public string Results { get; set; }
        public Nullable<int> TotalProducts { get; set; }
        public Nullable<int> PreviewTotalImages { get; set; }
        public Nullable<int> PreviewTotalImagesToOptimize { get; set; }
        public Nullable<int> TotalUploadedImages { get; set; }
        public Nullable<int> TotalImagesSize { get; set; }
        public Nullable<int> AvgImagesSizeAfterOptimize { get; set; }
        public Nullable<int> TotalImagesSizeAfterOptimize { get; set; }
        public Nullable<int> PercentageSaved { get; set; }
        public Nullable<int> TimeSaved { get; set; }
        public Nullable<int> ExecuteCounter { get; set; }
        public Nullable<int> ExecuteCounterMax { get; set; }
        public Nullable<System.DateTime> LastRunEndedAt { get; set; }
    }
}
