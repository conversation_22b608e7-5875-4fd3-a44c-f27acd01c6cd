//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Charge
    {
        public int ID { get; set; }
        public int AppID { get; set; }
        public int ShopID { get; set; }
        public string RedirectUrl { get; set; }
        public int ChargeType { get; set; }
        public int ChargeDurationUnit { get; set; }
        public Nullable<int> TrialDurationUnit { get; set; }
        public Nullable<int> TrialDuration { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public int Status { get; set; }
        public string Label { get; set; }
        public string IPNRecipient { get; set; }
        public string SubscriptionID { get; set; }
        public decimal TrialAmount { get; set; }
        public decimal ChargeAmount { get; set; }
        public Nullable<int> StoreYaSubscriptionID { get; set; }
        public Nullable<int> PlanID { get; set; }
        public string PlanKey { get; set; }
        public string OTPOrderID { get; set; }
        public Nullable<decimal> ChargeAmountInLocalCurrency { get; set; }
    }
}
