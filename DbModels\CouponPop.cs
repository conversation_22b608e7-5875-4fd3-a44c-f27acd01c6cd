//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class CouponPop
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public string CouponTitle { get; set; }
        public string CouponDescription { get; set; }
        public string CouponCode { get; set; }
        public Nullable<byte> Goog_IsActive { get; set; }
        public Nullable<byte> Tw_IsActive { get; set; }
        public Nullable<byte> Email_IsActive { get; set; }
        public Nullable<byte> Linked_IsActive { get; set; }
        public Nullable<byte> Fb_isActive { get; set; }
        public string Fb_Url { get; set; }
        public string Goog_Url { get; set; }
        public string Tw_Url { get; set; }
        public string Linked_Url { get; set; }
        public string TabText { get; set; }
        public string TabColor { get; set; }
        public Nullable<byte> TabPlacement { get; set; }
        public string LangJoinList { get; set; }
        public string LangEnterEmail { get; set; }
        public string LandCodeWillBeShown { get; set; }
        public Nullable<byte> Status { get; set; }
        public Nullable<byte> Theme { get; set; }
        public Nullable<long> Likes { get; set; }
        public Nullable<System.DateTime> LikesCalculatedAt { get; set; }
        public Nullable<byte> OpenAutomatically { get; set; }
        public string Image { get; set; }
        public string Css { get; set; }
        public string MailChimpApiKey { get; set; }
        public string MailChimpListName { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<int> TabVerticalPos { get; set; }
        public Nullable<int> OpenAutomaticallyAfter { get; set; }
        public Nullable<byte> Instg_IsActive { get; set; }
        public string Instg_Account { get; set; }
        public Nullable<byte> ShowGreyPlatformsStrip_IsActive { get; set; }
        public string BackgroundImage { get; set; }
        public string TextColor { get; set; }
        public Nullable<int> UsesUniqueCoupons { get; set; }
        public Nullable<int> BackgroundImagePlacement { get; set; }
        public Nullable<int> EmailProvider { get; set; }
        public Nullable<int> NewVisitors { get; set; }
        public Nullable<int> ReturningVisitors { get; set; }
        public Nullable<int> Traffic { get; set; }
        public Nullable<int> FromDirectTraffic { get; set; }
        public Nullable<int> FromPaidCampaigns { get; set; }
        public Nullable<int> FromSearchEngines { get; set; }
        public Nullable<int> FromSocialMedia { get; set; }
        public Nullable<int> ShowTabMobile { get; set; }
        public string AdditionalHeaderHtml { get; set; }
        public Nullable<byte> DoubleOptIn { get; set; }
        public string Layout { get; set; }
        public string TitleFont { get; set; }
        public Nullable<int> TitleFontSize { get; set; }
        public string TitleColor { get; set; }
        public string FontColor { get; set; }
        public Nullable<int> DescriptionFontSize { get; set; }
        public Nullable<int> DescriptionWeight { get; set; }
        public string ButtonTextFont { get; set; }
        public Nullable<int> ButtonTextFontSize { get; set; }
        public Nullable<int> ButtonTextWeight { get; set; }
        public string TabTextFont { get; set; }
        public Nullable<int> TabTextFontSize { get; set; }
        public Nullable<int> TabTextWeight { get; set; }
        public Nullable<byte> OpenAutomaticallyMobile { get; set; }
        public Nullable<int> OpenAutomaticallyAfterMobile { get; set; }
        public Nullable<byte> SendWelcome { get; set; }
        public Nullable<int> HasSkipOn { get; set; }
        public Nullable<int> HasShowOn { get; set; }
        public string ButtonText { get; set; }
        public string ButtonColor { get; set; }
        public string EmailListID { get; set; }
        public string BackgroundColor { get; set; }
        public string DescriptionWeightString { get; set; }
        public string ButtonTextWeightString { get; set; }
        public Nullable<int> IsNotified { get; set; }
        public Nullable<System.DateTime> NotifiedAt { get; set; }
        public Nullable<int> Name_IsActive { get; set; }
        public Nullable<int> Phone_IsActive { get; set; }
        public Nullable<int> ImagePosition { get; set; }
        public string ButtonTextColor { get; set; }
        public string ContinueButtonText { get; set; }
        public string SuccessHeadline { get; set; }
        public string SuccessMessage { get; set; }
        public string LangEnterName { get; set; }
        public string LangEnterPhone { get; set; }
        public string DescriptionColor { get; set; }
        public string DescriptionFont { get; set; }
        public string NoThanksText { get; set; }
        public Nullable<int> CpVersion { get; set; }
        public string TabTextColor { get; set; }
    }
}
