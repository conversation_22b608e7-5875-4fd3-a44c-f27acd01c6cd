//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class CrmContact
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<System.DateTime> UserCreatedAt { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string IP { get; set; }
        public string Country { get; set; }
        public string PaymentPlans { get; set; }
        public string FullAddress { get; set; }
        public string Website { get; set; }
        public Nullable<int> LeadScore { get; set; }
        public string Apps { get; set; }
        public string AppsPerformance { get; set; }
        public string LastPaymentEvent { get; set; }
        public Nullable<System.DateTime> TrialStartedAt { get; set; }
        public Nullable<int> UploadStatus { get; set; }
        public string AdminUrl { get; set; }
        public string FbUrl { get; set; }
        public Nullable<int> DaysTillTrialEnds { get; set; }
        public string LeadType { get; set; }
        public Nullable<int> WebsiteViews { get; set; }
        public Nullable<int> TopPerformer { get; set; }
        public Nullable<int> LikesAmountWebSite { get; set; }
        public Nullable<int> LikesAmountFbPage { get; set; }
        public Nullable<int> IncentivesGenerated { get; set; }
        public Nullable<System.DateTime> IncentivesGeneratedUpdatedAt { get; set; }
        public Nullable<int> BingIndexedPage { get; set; }
    }
}
