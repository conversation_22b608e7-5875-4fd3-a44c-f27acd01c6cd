//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class CrmEvent
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<int> CrmUserID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<int> EventType { get; set; }
        public Nullable<int> RegStatus { get; set; }
        public string IpnEvent { get; set; }
        public Nullable<int> PaymentStatus { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<System.DateTime> TrialStartedAt { get; set; }
        public Nullable<int> UploadStatus { get; set; }
    }
}
