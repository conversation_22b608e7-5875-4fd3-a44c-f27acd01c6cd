//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ExitPop
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public string Text { get; set; }
        public string DiscountCode { get; set; }
        public Nullable<byte> ShowWhenVisitorLeaves { get; set; }
        public Nullable<byte> ShowOnlyOnceForEachVisit { get; set; }
        public Nullable<byte> DisplayReminderBarWithDiscountCode { get; set; }
        public string ColorBackground { get; set; }
        public string ColorText { get; set; }
        public string ColorCoupon { get; set; }
        public string ColorCouponText { get; set; }
        public string Image { get; set; }
        public string Css { get; set; }
        public Nullable<int> Status { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<int> UseImageAsBackground { get; set; }
        public Nullable<byte> Email_isActive { get; set; }
        public Nullable<byte> Name_isActive { get; set; }
        public string LangName { get; set; }
        public string LangEmail { get; set; }
        public string LangGetCodeButton { get; set; }
        public string GetCodeButtonColor { get; set; }
        public string GetCodeButtonTextColor { get; set; }
        public string MailChimpApiKey { get; set; }
        public string MailChimpListName { get; set; }
        public string AnimationType { get; set; }
        public Nullable<int> EmailProvider { get; set; }
        public Nullable<byte> DoubleOptIn { get; set; }
        public string EmailListID { get; set; }
        public Nullable<int> NewVisitors { get; set; }
        public Nullable<int> ReturningVisitors { get; set; }
        public Nullable<int> Traffic { get; set; }
        public Nullable<int> FromDirectTraffic { get; set; }
        public Nullable<int> FromPaidCampaigns { get; set; }
        public Nullable<int> FromSearchEngines { get; set; }
        public Nullable<int> FromSocialMedia { get; set; }
        public Nullable<int> IsNotified { get; set; }
        public Nullable<System.DateTime> NotifiedAt { get; set; }
    }
}
