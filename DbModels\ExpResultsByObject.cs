//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ExpResultsByObject
    {
        public int ID { get; set; }
        public Nullable<int> ExpID { get; set; }
        public Nullable<int> ObjID { get; set; }
        public Nullable<decimal> Spend { get; set; }
        public Nullable<decimal> Revenue { get; set; }
        public Nullable<decimal> SpendCompareTo { get; set; }
        public Nullable<decimal> RevenueCompareTo { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
    }
}
