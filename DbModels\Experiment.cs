//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Experiment
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
        public Nullable<System.DateTime> StartDate { get; set; }
        public Nullable<System.DateTime> EndDate { get; set; }
        public Nullable<int> ExpType { get; set; }
        public Nullable<int> DataLevel { get; set; }
        public string Filter { get; set; }
        public string FilterCompareTo { get; set; }
        public Nullable<int> Status { get; set; }
        public Nullable<int> ChannelType { get; set; }
        public Nullable<int> GroupId { get; set; }
    }
}
