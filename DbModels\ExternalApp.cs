//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ExternalApp
    {
        public string AppKey { get; set; }
        public string AppSecret { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public string AppAuthUrl { get; set; }
        public string AppName { get; set; }
        public string AppVersion { get; set; }
        public Nullable<System.DateTime> AppUpdateDate { get; set; }
        public string AppDescription { get; set; }
        public string AppIcon { get; set; }
        public Nullable<int> IsEmbedded { get; set; }
        public string AppUrlKey { get; set; }
        public string AppBenefits { get; set; }
        public string AppTitle { get; set; }
        public string AppPricingUrl { get; set; }
        public string AppSubTitle { get; set; }
        public string AppImages { get; set; }
        public Nullable<byte> PluginReady { get; set; }
        public Nullable<byte> ScriptsReady { get; set; }
        public Nullable<int> AppAuthor { get; set; }
        public Nullable<byte> SandBoxMode { get; set; }
        public Nullable<byte> Write { get; set; }
        public Nullable<byte> Read { get; set; }
        public Nullable<byte> Listen { get; set; }
        public Nullable<byte> DomManipulation { get; set; }
        public int ID { get; set; }
        public string BenefitsHtmlTemplatePath { get; set; }
        public Nullable<int> ShowChoosePlanNowButton { get; set; }
    }
}
