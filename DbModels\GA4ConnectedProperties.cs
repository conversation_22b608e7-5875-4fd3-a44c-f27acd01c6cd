//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class GA4ConnectedProperties
    {
        public int Id { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public int GAAccountId { get; set; }
        public Nullable<int> ShopId { get; set; }
        public int Status { get; set; }
        public string RegionCode { get; set; }
        public string CurrencyCode { get; set; }
        public string TimeZone { get; set; }
        public string AccountSummary { get; set; }
        public string Account { get; set; }
        public string AccountDisplayName { get; set; }
        public string Property { get; set; }
        public string DisplayName { get; set; }
        public string PropertyType { get; set; }
        public string ServiceLevel { get; set; }
        public string IndustryCategory { get; set; }
        public string DefaultUrl { get; set; }
        public string MeasurementId { get; set; }
        public Nullable<System.DateTime> PropertyCreatedAt { get; set; }
        public Nullable<System.DateTime> AudienceCreatedAt { get; set; }
    }
}
