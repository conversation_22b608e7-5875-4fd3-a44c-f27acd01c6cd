//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;

    public partial class GAConnectedAccountsStat
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public string Url { get; set; }
        public Nullable<int> GAAccountID { get; set; }
        public Nullable<int> Sessions { get; set; }
        public Nullable<int> Transactions { get; set; }
        public Nullable<decimal> Revenue { get; set; }
        public Nullable<int> GooglePaidSessions { get; set; }
        public Nullable<int> GooglePaidTransactions { get; set; }
        public Nullable<decimal> GooglePaidRevenue { get; set; }
        public Nullable<int> GoogleOrganicSessions { get; set; }
        public Nullable<int> GoogleOrganicTransactions { get; set; }
        public Nullable<decimal> GoogleOrganicRevenue { get; set; }
        public string GoogleOrganicWrongSources { get; set; }
        public string WrongSources { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> FacebookSessions { get; set; }
        public Nullable<int> FacebookTransactions { get; set; }
        public Nullable<decimal> FacebookRevenue { get; set; }
        public Nullable<System.DateTime> ShopRevenuesReportedAt { get; set; }
        public Nullable<decimal> RevenueInitially { get; set; }
        public Nullable<int> StryTransactions { get; set; }
        public Nullable<decimal> StryRevenue { get; set; }
        public Nullable<decimal> RevenuesInUSD { get; set; }
        public Nullable<int> BingSessions { get; set; }
        public Nullable<int> BingTransactions { get; set; }
        public Nullable<decimal> BingRevenue { get; set; }
    }
}
