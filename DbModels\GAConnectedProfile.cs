//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class GAConnectedProfile
    {
        public int ID { get; set; }
        public Nullable<int> GAAccountID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> AccountID { get; set; }
        public string AccountName { get; set; }
        public string WebPopertyID { get; set; }
        public string WebPopertyName { get; set; }
        public string ProfileID { get; set; }
        public string ProfileName { get; set; }
        public Nullable<int> Status { get; set; }
        public string GoalForCartID { get; set; }
        public string GoalForCartName { get; set; }
        public string GoalForPurchaseID { get; set; }
        public string GoalForPurchaseName { get; set; }
        public string WebsiteUrl { get; set; }
        public string IndustryVertical { get; set; }
        public string Filter { get; set; }
        public Nullable<int> GoalStatus { get; set; }
        public string Currency { get; set; }
        public string InternalWebPropertyID { get; set; }
    }
}
