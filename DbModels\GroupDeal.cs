//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class GroupDeal
    {
        public int ID { get; set; }
        public int ShopID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Image { get; set; }
        public string DiscountCode { get; set; }
        public Nullable<System.DateTime> StartDatetime { get; set; }
        public Nullable<System.DateTime> EndDatetime { get; set; }
        public Nullable<decimal> TimezoneShift { get; set; }
        public string Terms { get; set; }
        public Nullable<int> RequiredNumberOfParticipants { get; set; }
        public Nullable<int> Ordinal { get; set; }
        public Nullable<int> Status { get; set; }
        public Nullable<int> CheckNumberOfParticipants { get; set; }
        public int RequiredActionType { get; set; }
        public Nullable<int> TimeZoneID { get; set; }
        public Nullable<int> PostWhenBegin { get; set; }
        public string PostWhenBeginText { get; set; }
        public Nullable<int> PostWhenHalf { get; set; }
        public string PostWhenHalfText { get; set; }
        public Nullable<int> PostWhenEnd { get; set; }
        public string PostWhenEndText { get; set; }
        public Nullable<int> NotifyWhen { get; set; }
        public Nullable<int> ProductID { get; set; }
    }
}
