//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class GrowthHero
    {
        public int ID { get; set; }
        public int ShopID { get; set; }
        public Nullable<int> AccountScore { get; set; }
        public string AgeJson { get; set; }
        public string Gender<PERSON>son { get; set; }
        public string LoosingSegmentsJson { get; set; }
        public Nullable<decimal> WastedAmount3Month { get; set; }
        public Nullable<long> FbAccountID { get; set; }
        public string FbToken { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<int> Status { get; set; }
        public string MissingOpportunitiesJson { get; set; }
        public Nullable<decimal> TotalAccountBudget { get; set; }
        public Nullable<decimal> TotalAccountWastedBudget { get; set; }
        public Nullable<long> FbAdAccountOwnerID { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<System.DateTime> FromDate { get; set; }
        public Nullable<System.DateTime> ToDate { get; set; }
        public Nullable<int> NotificationStatus { get; set; }
        public Nullable<int> NotificationValue { get; set; }
        public string AccountName { get; set; }
        public Nullable<int> CampaignsCount { get; set; }
        public Nullable<int> AdSetsCount { get; set; }
        public Nullable<int> AdsCount { get; set; }
    }
}
