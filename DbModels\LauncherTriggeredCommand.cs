//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class LauncherTriggeredCommand
    {
        public int ID { get; set; }
        public Nullable<System.DateTime> Inserted { get; set; }
        public Nullable<System.DateTime> Updated { get; set; }
        public Nullable<System.DateTime> ExecuteStarted { get; set; }
        public Nullable<System.DateTime> ExecuteEnded { get; set; }
        public string InitiatedBy { get; set; }
        public string InitiatedByEmail { get; set; }
        public int CommandID { get; set; }
        public string EXEPath { get; set; }
        public string Args { get; set; }
        public int Status { get; set; }
        public string Results { get; set; }
        public string CommandName { get; set; }
        public Nullable<System.DateTime> NextRunAt { get; set; }
    }
}
