//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class MagentoConnectedShop
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public string oauth_consumer_key { get; set; }
        public string oauth_consumer_secret { get; set; }
        public string store_base_url { get; set; }
        public string oauth_verifier { get; set; }
        public string oauth_token { get; set; }
        public string oauth_token_secret { get; set; }
        public Nullable<System.DateTime> InsertredAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
    }
}
