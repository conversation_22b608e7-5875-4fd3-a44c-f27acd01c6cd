//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class MerchantsOrdersIpn
    {
        public int ID { get; set; }
        public string item_number { get; set; }
        public string item_name { get; set; }
        public string amount { get; set; }
        public string quantity { get; set; }
        public string first_name { get; set; }
        public string last_name { get; set; }
        public string payer_email { get; set; }
        public string receiver_email { get; set; }
        public string txn_id { get; set; }
        public string txn_type { get; set; }
        public string custom { get; set; }
        public string address_country { get; set; }
        public string address_city { get; set; }
        public string address_country_code { get; set; }
        public string address_name { get; set; }
        public string address_state { get; set; }
        public string address_status { get; set; }
        public string address_street { get; set; }
        public string address_zip { get; set; }
        public string contact_phone { get; set; }
        public string payer_id { get; set; }
        public string invoice { get; set; }
        public string mc_currency { get; set; }
        public string mc_gross { get; set; }
        public string mc_shipping { get; set; }
        public string memo { get; set; }
        public string num_cart_items { get; set; }
        public string payment_date { get; set; }
        public string payment_status { get; set; }
        public string payment_type { get; set; }
        public string pending_reason { get; set; }
        public string reason_code { get; set; }
        public string shipping { get; set; }
        public string shipping_method { get; set; }
        public string tax { get; set; }
        public string ItemNumbersList { get; set; }
    }
}
