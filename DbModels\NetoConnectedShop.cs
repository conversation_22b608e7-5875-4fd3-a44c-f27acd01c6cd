//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class NetoConnectedShop
    {
        public int ID { get; set; }
        public string MerchantId { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public int ShopID { get; set; }
        public int UserID { get; set; }
        public string PermissionsScope { get; set; }
        public Nullable<System.DateTime> OrdersUpdatedAt { get; set; }
        public string AppToken { get; set; }
        public int StoreyaAppTypeID { get; set; }
        public Nullable<long> PaymentPlan { get; set; }
        public Nullable<System.DateTime> PaymentUpdatedAt { get; set; }
        public Nullable<int> OrdersAmount { get; set; }
        public string Locale { get; set; }
        public Nullable<int> OrdersAmountInitially { get; set; }
        public string ExpiresAt { get; set; }
        public string RefreshToken { get; set; }
        public string SiteId { get; set; }
        public string Email { get; set; }
        public string StoreDomain { get; set; }
    }
}
