//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class PaymentsEventsHistory
    {
        public int ID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public int ShopId { get; set; }
        public Nullable<int> AgreeId { get; set; }
        public string CreatedBy { get; set; }
        public int Status { get; set; }
        public int Category { get; set; }
        public int EventType { get; set; }
        public string RefernceId { get; set; }
        public int TransactionType { get; set; }
        public Nullable<decimal> Amount { get; set; }
        public Nullable<System.DateTime> ChargeDate { get; set; }
        public int PaymentProvider { get; set; }
        public string Comments { get; set; }
        public string Data { get; set; }
    }
}
