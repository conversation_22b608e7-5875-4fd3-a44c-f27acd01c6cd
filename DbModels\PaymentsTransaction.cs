//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class PaymentsTransaction
    {
        public int ID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public int ShopId { get; set; }
        public string UpdatedBy { get; set; }
        public int Status { get; set; }
        public int TransactionType { get; set; }
        public string SubscriptionId { get; set; }
        public Nullable<decimal> PreviousAmount { get; set; }
        public Nullable<System.DateTime> previousChargeDate { get; set; }
        public Nullable<decimal> Amount { get; set; }
        public Nullable<System.DateTime> ChargeDate { get; set; }
        public Nullable<int> AgreeId { get; set; }
        public string Comments { get; set; }
        public Nullable<System.DateTime> ScheduledAt { get; set; }
        public Nullable<int> ActionType { get; set; }
        public Nullable<int> ParentId { get; set; }
        public Nullable<int> PaymentProvider { get; set; }
    }
}
