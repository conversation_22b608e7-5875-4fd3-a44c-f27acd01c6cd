//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class PlimusIpnCall
    {
        public int ID { get; set; }
        public string TransactionType { get; set; }
        public string TestMode { get; set; }
        public string ReferenceNumber { get; set; }
        public string OriginalReferenceNumber { get; set; }
        public string PaymentMethod { get; set; }
        public string CreditCardType { get; set; }
        public Nullable<System.DateTime> TransactionDate { get; set; }
        public Nullable<System.DateTime> UntilDate { get; set; }
        public string productId { get; set; }
        public string productName { get; set; }
        public string contractId { get; set; }
        public string contractName { get; set; }
        public string contractOwner { get; set; }
        public string contractPrice { get; set; }
        public Nullable<int> quantity { get; set; }
        public string currency { get; set; }
        public string coupon { get; set; }
        public string couponValue { get; set; }
        public string referrer { get; set; }
        public string accountId { get; set; }
        public string title { get; set; }
        public string firstName { get; set; }
        public string lastName { get; set; }
        public string company { get; set; }
        public string address1 { get; set; }
        public string address2 { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string country { get; set; }
        public string zipCode { get; set; }
        public string email { get; set; }
        public string workPhone { get; set; }
        public string extension { get; set; }
        public string mobilePhone { get; set; }
        public string homePhone { get; set; }
        public string faxNumber { get; set; }
        public string licenseKey { get; set; }
        public string shippingFirstName { get; set; }
        public string shippingLastName { get; set; }
        public string shippingAddress1 { get; set; }
        public string shippingAddress2 { get; set; }
        public string shippingCity { get; set; }
        public string shippingState { get; set; }
        public string shippingCountry { get; set; }
        public string shippingZipCode { get; set; }
        public string remoteAddress { get; set; }
        public string shippingMethod { get; set; }
        public string couponCode { get; set; }
        public string invoiceAmount { get; set; }
        public string invoiceInfoURL { get; set; }
        public int ShopID { get; set; }
        public string subscriptionId { get; set; }
        public Nullable<int> AppID { get; set; }
        public string CancelReason { get; set; }
        public string taxAmountUSD { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<int> AgreeID { get; set; }
        public string TaxRateUSD { get; set; }
        public Nullable<System.DateTime> NextPaymenDate { get; set; }
        public string NextPaymentAmount { get; set; }
        public string ChargeFrequency { get; set; }
        public string taxAmountInCurrency { get; set; }
        public string invoiceAmountInCurrency { get; set; }
        public string TransactionId { get; set; }
        public string cbStatus { get; set; }
        public string InternalTransactionType { get; set; }
        public Nullable<int> TransactionSubType { get; set; }
        public Nullable<int> PaymentAdapterType { get; set; }
    }
}
