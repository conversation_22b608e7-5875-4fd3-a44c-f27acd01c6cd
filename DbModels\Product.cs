//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Product
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string OutUrl { get; set; }
        public string ImageUrl { get; set; }
        public Nullable<decimal> Price { get; set; }
        public int CategoryID { get; set; }
        public int ShopID { get; set; }
        public string OriginalProductID { get; set; }
        public Nullable<int> SourceType { get; set; }
        public Nullable<int> Ordinal { get; set; }
        public Nullable<int> IsFeatured { get; set; }
        public Nullable<int> IsActive { get; set; }
        public string LocalImage { get; set; }
        public Nullable<int> OrdinalAtHomepage { get; set; }
        public Nullable<int> CommentsAmount { get; set; }
        public string HtmlDescription { get; set; }
        public Nullable<int> Disabled { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<decimal> DiscountPrice { get; set; }
        public string OriginalSecondaryID { get; set; }
        public Nullable<int> Inventory { get; set; }
    
        public virtual Shop Shop { get; set; }
    }
}
