//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ProductDescribersBulk
    {
        public int Id { get; set; }
        public System.DateTime Inserted { get; set; }
        public Nullable<System.DateTime> Updated { get; set; }
        public Nullable<System.DateTime> Started { get; set; }
        public Nullable<System.DateTime> Ended { get; set; }
        public int ShopId { get; set; }
        public string FilterType { get; set; }
        public string FilterName { get; set; }
        public string TypeOfPrompt { get; set; }
        public int Status { get; set; }
        public int Step { get; set; }
        public Nullable<int> Progress { get; set; }
        public Nullable<int> TotalProducts { get; set; }
        public string Comments { get; set; }
        public string FilterData { get; set; }
        public Nullable<int> Options { get; set; }
        public Nullable<int> FilterTotalProducts { get; set; }
    }
}
