//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ProductDescribersCredit
    {
        public int Id { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<int> ShopId { get; set; }
        public int Status { get; set; }
        public Nullable<long> ChargeId { get; set; }
        public Nullable<System.DateTime> ChargeAt { get; set; }
        public Nullable<System.DateTime> NextPaymentAt { get; set; }
        public Nullable<int> PaymentStatus { get; set; }
        public Nullable<int> BulksCreaditLimit { get; set; }
        public Nullable<int> BulksCreditUsage { get; set; }
        public Nullable<double> TotalPaid { get; set; }
        public Nullable<int> BulksCreaditAdded { get; set; }
        public Nullable<int> PaymentPlanId { get; set; }
    }
}
