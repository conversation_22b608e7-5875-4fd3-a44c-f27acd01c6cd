//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ProductsFeed
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<int> RunFrequencyInDays { get; set; }
        public Nullable<System.DateTime> CreatedAt { get; set; }
        public Nullable<System.DateTime> NextRunAt { get; set; }
        public Nullable<int> FeedType { get; set; }
        public Nullable<int> HasBestSellerMark { get; set; }
        public string FeedCreationError { get; set; }
        public string Path { get; set; }
        public Nullable<int> IgnoreVariants { get; set; }
        public string FilterBy { get; set; }
        public string TitleFormat { get; set; }
        public Nullable<int> ProductsAmount { get; set; }
        public Nullable<int> Status { get; set; }
        public Nullable<System.DateTime> AccessedAt { get; set; }
        public Nullable<int> ProductApproved { get; set; }
        public Nullable<int> ProductsRejected { get; set; }
        public Nullable<int> LimitImagesTo { get; set; }
        public string LabelProductInCategories { get; set; }
        public Nullable<int> InfoAboutShippingWeight { get; set; }
        public string Currencies { get; set; }
        public string PromotionName { get; set; }
        public string FilterBy1 { get; set; }
        public string FilterBy2 { get; set; }
        public string FilterBy3 { get; set; }
        public string SourceUrl { get; set; }
        public Nullable<int> SourceType { get; set; }
        public string Languages { get; set; }
        public Nullable<long> BulkID { get; set; }
    }
}
