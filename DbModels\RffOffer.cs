//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class RffOffer
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<int> CustomDesignID { get; set; }
        public string CompanyName { get; set; }
        public string Website { get; set; }
        public string Currency { get; set; }
        public Nullable<int> CustomerRewardType { get; set; }
        public Nullable<decimal> CustomerRewardAmount { get; set; }
        public string CustomerRewardCoupon { get; set; }
        public Nullable<bool> UseSameAmountForFriend { get; set; }
        public Nullable<int> FriendRewardType { get; set; }
        public Nullable<decimal> FriendRewardAmount { get; set; }
        public string FriendRewardCoupon { get; set; }
        public string FbTitle { get; set; }
        public string FbDescription { get; set; }
        public string FbImage { get; set; }
        public string TwMessage { get; set; }
        public string EmailSubject { get; set; }
        public string EmailH1 { get; set; }
        public string EmailH2 { get; set; }
        public string EmailH3 { get; set; }
        public string EmailH4 { get; set; }
        public string EmailButton { get; set; }
        public string EmailAboutTitle { get; set; }
        public string EmailAboutText { get; set; }
        public string AfterPopupH1 { get; set; }
        public string AfterPopupH2 { get; set; }
        public string AfterPopupLinkDescription { get; set; }
        public string AfterPopupLinkButton { get; set; }
        public string AfterPopupFbButtonCaption { get; set; }
        public string AfterPopupTwButtonCaption { get; set; }
        public string AfterPopupEmailButtonCaption { get; set; }
        public string EmailSharingH1 { get; set; }
        public string EmailSharingH2 { get; set; }
        public string EmailSharingButton { get; set; }
        public string ManuallyShareH1 { get; set; }
        public string ManuallyShareImDone { get; set; }
        public string ManuallyShareImButton { get; set; }
        public string ThankYouH1 { get; set; }
        public string ThankYouH2 { get; set; }
        public string ThankYouButton { get; set; }
        public string OfferPopupH1 { get; set; }
        public string OfferPopupH2 { get; set; }
        public string OfferPopupDescriptionTitle { get; set; }
        public string OfferPopupDescriptionText { get; set; }
        public string OfferPopupButton { get; set; }
        public Nullable<byte> Status { get; set; }
        public Nullable<int> Platform { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<int> RegistrationStep { get; set; }
        public string CustomCss { get; set; }
    }
}
