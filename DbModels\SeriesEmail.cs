//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class SeriesEmail
    {
        public int Id { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public int Status { get; set; }
        public string EmailTemplateModel { get; set; }
        public string EmailTemplate { get; set; }
        public string SendCondition { get; set; }
        public int SeriesId { get; set; }
        public int DaysToWaitAfter { get; set; }
        public int Position { get; set; }
        public string Comments { get; set; }
        public Nullable<int> TriggerType { get; set; }
        public Nullable<int> SeriesSystemEventId { get; set; }
    }
}
