//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class SeriesUser
    {
        public int Id { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public int Status { get; set; }
        public string Email { get; set; }
        public int UserID { get; set; }
        public Nullable<System.DateTime> ExistedAt { get; set; }
        public int SeriesId { get; set; }
        public Nullable<int> LastEmailIDSent { get; set; }
        public Nullable<int> LastEmailSentStatus { get; set; }
        public Nullable<System.DateTime> LastEmailSentDatetime { get; set; }
        public Nullable<System.DateTime> EntrySystemEventInsertedAt { get; set; }
        public string Comments { get; set; }
        public Nullable<int> EntrySystemEventId { get; set; }
        public Nullable<int> ExitSystemEventId { get; set; }
        public Nullable<int> GoalAchivedSystemEventId { get; set; }
        public Nullable<int> ShopId { get; set; }
        public string AdditionalEmails { get; set; }
        public string FromEmail { get; set; }
    }
}
