//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Shop
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Shop()
        {
            this.Categories = new HashSet<Category>();
            this.FbPages = new HashSet<FbPage>();
            this.Products = new HashSet<Product>();
        }
    
        public int ID { get; set; }
        public string Name { get; set; }
        public string LogoImage { get; set; }
        public string BaseUrl { get; set; }
        public Nullable<int> UserID { get; set; }
        public Nullable<long> FbPageID { get; set; }
        public string DesignHeaderImage { get; set; }
        public Nullable<int> DesignNumberOfProductsOnPage { get; set; }
        public string Color_MenuBackground { get; set; }
        public string Color_Headline { get; set; }
        public string Color_BuyNow { get; set; }
        public string Color_Countour { get; set; }
        public string Color_Background { get; set; }
        public Nullable<int> IsPublished { get; set; }
        public Nullable<int> IsPaid { get; set; }
        public string Currency { get; set; }
        public Nullable<int> CatalogSourcePlatform { get; set; }
        public Nullable<int> CatalogState { get; set; }
        public string SellerInfoShipmentAndPayment { get; set; }
        public string SellerInfoLocateShop { get; set; }
        public string SellerInfoSummary { get; set; }
        public string Lang_BuyNow { get; set; }
        public string Lang_AllProducts { get; set; }
        public string Lang_AllPrices { get; set; }
        public string Lang_Search { get; set; }
        public string Lang_Taxes { get; set; }
        public Nullable<int> IsShowTaxesNotIncluded { get; set; }
        public Nullable<int> LayoutID { get; set; }
        public Nullable<int> RegState { get; set; }
        public Nullable<int> IsDisabled { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<int> GalleryDisabled { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<byte> DisablePoweredBy { get; set; }
        public string PoweredByHtml { get; set; }
        public string HeaderLink { get; set; }
        public string ExternalJsScript { get; set; }
        public string StoreLocale { get; set; }
        public Nullable<int> AfterImportState { get; set; }
        public Nullable<byte> PluginReady { get; set; }
        public Nullable<byte> ScriptsReady { get; set; }
        public string ShopUrl { get; set; }
        public string PriceCurrencyPattern { get; set; }
        public Nullable<int> IndexedPagesAmount { get; set; }
        public string Lang_Price { get; set; }
        public string Lang_ProductInfo { get; set; }
        public string Lang_SharingShopTitle { get; set; }
        public string Lang_SharingShopDescription { get; set; }
        public string GACurrency { get; set; }
        public string VatID { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Category> Categories { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<FbPage> FbPages { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Product> Products { get; set; }
        public virtual User User { get; set; }
    }
}
