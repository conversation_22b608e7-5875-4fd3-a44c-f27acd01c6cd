//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ShopSubscription
    {
        public int ID { get; set; }
        public Nullable<int> ContractID { get; set; }
        public Nullable<int> BlueSnapSubscriptionID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> Status { get; set; }
        public int ShopID { get; set; }
        public string AppIDs { get; set; }
        public string PlanIDs { get; set; }
        public string ContractPrice { get; set; }
        public string OverridedContractPrice { get; set; }
        public Nullable<int> BlueSnapShopperID { get; set; }
        public string BlueSnapShopperCurrency { get; set; }
        public Nullable<System.DateTime> FirstBillingDate { get; set; }
        public Nullable<decimal> ContractAmount { get; set; }
        public Nullable<decimal> ContractAmountInLocalCurrency { get; set; }
        public string PaymentMethod { get; set; }
        public Nullable<int> PaymentAdapterType { get; set; }
        public string OriginalSubscriptionID { get; set; }
        public Nullable<int> AgreeID { get; set; }
        public Nullable<int> ActiveInBudget { get; set; }
        public Nullable<System.DateTime> NextPaymenDate { get; set; }
        public Nullable<decimal> NextPaymentAmount { get; set; }
        public string ChargeFrequency { get; set; }
        public Nullable<int> Autorenew { get; set; }
        public string BsStatus { get; set; }
        public Nullable<int> CustomContractType { get; set; }
        public string FsAccountID { get; set; }
        public string FsSubscriptionID { get; set; }
        public string FsContractID { get; set; }
        public Nullable<System.DateTime> LastChargeTriedAt { get; set; }
        public Nullable<int> LastChargeStatus { get; set; }
    }
}
