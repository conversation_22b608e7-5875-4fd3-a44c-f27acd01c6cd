//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ShopifyAppCharge
    {
        public int ID { get; set; }
        public Nullable<System.DateTime> ActivatedOn { get; set; }
        public string ApiClientID { get; set; }
        public Nullable<System.DateTime> BillingOn { get; set; }
        public Nullable<System.DateTime> CancelledOn { get; set; }
        public Nullable<System.DateTime> CreatedAt { get; set; }
        public Nullable<long> ChargeID { get; set; }
        public string Name { get; set; }
        public Nullable<decimal> Price { get; set; }
        public string ReturnUrl { get; set; }
        public string ChargeStatus { get; set; }
        public string Test { get; set; }
        public string Trial_days { get; set; }
        public Nullable<System.DateTime> TrialEndsOn { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public string DecoratedReturnUrl { get; set; }
        public string ConfirmationUrl { get; set; }
        public string ShopifyShopName { get; set; }
        public Nullable<int> StoreyaAppTypeID { get; set; }
    }
}
