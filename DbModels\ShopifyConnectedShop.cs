//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ShopifyConnectedShop
    {
        public string ShopifyShopName { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public int ShopID { get; set; }
        public int UserID { get; set; }
        public string CouponPopToken { get; set; }
        public Nullable<long> <PERSON><PERSON>t<PERSON> { get; set; }
        public string ScriptSrc { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> PermissionsScope { get; set; }
        public Nullable<System.DateTime> OrdersUpdatedAt { get; set; }
        public string AppToken { get; set; }
        public int StoreyaAppTypeID { get; set; }
        public Nullable<long> PaymentPlan { get; set; }
        public Nullable<System.DateTime> PaymentUpdatedAt { get; set; }
        public int ID { get; set; }
        public Nullable<int> OrdersAmount { get; set; }
        public string Locale { get; set; }
        public Nullable<int> OrdersAmountInitially { get; set; }
        public Nullable<int> EmbeddedType { get; set; }
        public string PermissionsList { get; set; }
    }
}
