//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ShopifyMerchant
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<int> AppTypeID { get; set; }
        public Nullable<long> ShopifyShopID { get; set; }
        public string Domain { get; set; }
        public string MyshopifyDomain { get; set; }
        public string Name { get; set; }
        public Nullable<System.DateTime> CreatedAt { get; set; }
        public string ShopOwner { get; set; }
        public string Email { get; set; }
        public string CustomerEmail { get; set; }
        public string PlanName { get; set; }
        public string PlanDisplayName { get; set; }
        public Nullable<int> TaxesIncluded { get; set; }
        public string Currency { get; set; }
        public string Latitude { get; set; }
        public string Longitude { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string CountryName { get; set; }
        public string Timezone { get; set; }
        public string City { get; set; }
        public string Address { get; set; }
        public string Zip { get; set; }
        public string Phone { get; set; }
        public Nullable<int> Status { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public string Locale { get; set; }
    }
}
