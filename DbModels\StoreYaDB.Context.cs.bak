﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class StoreYaEntities : DbContext
    {
        public StoreYaEntities()
            : base("name=StoreYaEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<AdServerUser> AdServerUsers { get; set; }
        public virtual DbSet<AdWordsCallsAd> AdWordsCallsAds { get; set; }
        public virtual DbSet<AdWordsCallsCampaign> AdWordsCallsCampaigns { get; set; }
        public virtual DbSet<Agency> Agencies { get; set; }
        public virtual DbSet<AllOrder> AllOrders { get; set; }
        public virtual DbSet<AppsForAppStore> AppsForAppStores { get; set; }
        public virtual DbSet<AppsInstallation> AppsInstallations { get; set; }
        public virtual DbSet<BackgroundDowngradeQueue> BackgroundDowngradeQueues { get; set; }
        public virtual DbSet<BackgroundTask> BackgroundTasks { get; set; }
        public virtual DbSet<Banner> Banners { get; set; }
        public virtual DbSet<BannerShopImage> BannerShopImages { get; set; }
        public virtual DbSet<BannersLight> BannersLights { get; set; }
        public virtual DbSet<BigCommerceConnectedShop> BigCommerceConnectedShops { get; set; }
        public virtual DbSet<CancellationFeedback> CancellationFeedbacks { get; set; }
        public virtual DbSet<Category> Categories { get; set; }
        public virtual DbSet<CCreatorCollection> CCreatorCollections { get; set; }
        public virtual DbSet<CCreatorCollect> CCreatorCollects { get; set; }
        public virtual DbSet<CCreatorJob> CCreatorJobs { get; set; }
        public virtual DbSet<CCreatorPattern> CCreatorPatterns { get; set; }
        public virtual DbSet<CCreatorProduct> CCreatorProducts { get; set; }
        public virtual DbSet<ChargeTask> ChargeTasks { get; set; }
        public virtual DbSet<CollectionProduct> CollectionProducts { get; set; }
        public virtual DbSet<ConnectedWeeblySite> ConnectedWeeblySites { get; set; }
        public virtual DbSet<ContactForm> ContactForms { get; set; }
        public virtual DbSet<ContentItem> ContentItems { get; set; }
        public virtual DbSet<ContentMix> ContentMixes { get; set; }
        public virtual DbSet<ContentMixesUser> ContentMixesUsers { get; set; }
        public virtual DbSet<CouponPopCode> CouponPopCodes { get; set; }
        public virtual DbSet<CouponPopEvent> CouponPopEvents { get; set; }
        public virtual DbSet<CouponPopInstallationDomain> CouponPopInstallationDomains { get; set; }
        public virtual DbSet<CouponPopStat> CouponPopStats { get; set; }
        public virtual DbSet<CouponPopView> CouponPopViews { get; set; }
        public virtual DbSet<CrmContact> CrmContacts { get; set; }
        public virtual DbSet<CrmEvent> CrmEvents { get; set; }
        public virtual DbSet<EbookUser> EbookUsers { get; set; }
        public virtual DbSet<EcwidConnectedShop> EcwidConnectedShops { get; set; }
        public virtual DbSet<EmailCampaignShop> EmailCampaignShops { get; set; }
        public virtual DbSet<EmailNotification> EmailNotifications { get; set; }
        public virtual DbSet<EmailProvider_ActiveCampaign> EmailProvider_ActiveCampaign { get; set; }
        public virtual DbSet<EmailProvider_Awerber> EmailProvider_Awerber { get; set; }
        public virtual DbSet<EmailProvider_CampaignMonitor> EmailProvider_CampaignMonitor { get; set; }
        public virtual DbSet<EmailProvider_GetResponse> EmailProvider_GetResponse { get; set; }
        public virtual DbSet<EmailProvider_Infusionsoft> EmailProvider_Infusionsoft { get; set; }
        public virtual DbSet<EmailProvider_Robly> EmailProvider_Robly { get; set; }
        public virtual DbSet<EmailTemplate> EmailTemplates { get; set; }
        public virtual DbSet<ePagesConnecttedShop> ePagesConnecttedShops { get; set; }
        public virtual DbSet<Event> Events { get; set; }
        public virtual DbSet<ExitPopCode> ExitPopCodes { get; set; }
        public virtual DbSet<ExitPopEvent> ExitPopEvents { get; set; }
        public virtual DbSet<ExitPopInstallationDomain> ExitPopInstallationDomains { get; set; }
        public virtual DbSet<ExitPop> ExitPops { get; set; }
        public virtual DbSet<ExitPopView> ExitPopViews { get; set; }
        public virtual DbSet<ExtAppsScript> ExtAppsScripts { get; set; }
        public virtual DbSet<ExtAppsToken> ExtAppsTokens { get; set; }
        public virtual DbSet<ExternalApp> ExternalApps { get; set; }
        public virtual DbSet<ExtPricingFeature> ExtPricingFeatures { get; set; }
        public virtual DbSet<ExtPricingPlanFeature> ExtPricingPlanFeatures { get; set; }
        public virtual DbSet<ExtPricingPlan> ExtPricingPlans { get; set; }
        public virtual DbSet<FanGate> FanGates { get; set; }
        public virtual DbSet<FbAction> FbActions { get; set; }
        public virtual DbSet<FbAdsBusiness> FbAdsBusinesses { get; set; }
        public virtual DbSet<FbAdsChangeLog> FbAdsChangeLogs { get; set; }
        public virtual DbSet<FbAdsToken> FbAdsTokens { get; set; }
        public virtual DbSet<FbGrader> FbGraders { get; set; }
        public virtual DbSet<FbPage> FbPages { get; set; }
        public virtual DbSet<FbPagesTab> FbPagesTabs { get; set; }
        public virtual DbSet<FbProfile> FbProfiles { get; set; }
        public virtual DbSet<FutureEmail> FutureEmails { get; set; }
        public virtual DbSet<GAConnectedAccount> GAConnectedAccounts { get; set; }
        public virtual DbSet<GaDashboardData> GaDashboardDatas { get; set; }
        public virtual DbSet<GaEvent> GaEvents { get; set; }
        public virtual DbSet<GalleryBlackList> GalleryBlackLists { get; set; }
        public virtual DbSet<GalleryProduct> GalleryProducts { get; set; }
        public virtual DbSet<GoggleCoupon> GoggleCoupons { get; set; }
        public virtual DbSet<GoogleAdwordsCoupon> GoogleAdwordsCoupons { get; set; }
        public virtual DbSet<GroupDeal> GroupDeals { get; set; }
        public virtual DbSet<GroupDealsTranslation> GroupDealsTranslations { get; set; }
        public virtual DbSet<GroupDealUser> GroupDealUsers { get; set; }
        public virtual DbSet<HootSuiteUser> HootSuiteUsers { get; set; }
        public virtual DbSet<InstagramTabSetting> InstagramTabSettings { get; set; }
        public virtual DbSet<InventoryChangesLog> InventoryChangesLogs { get; set; }
        public virtual DbSet<Layout> Layouts { get; set; }
        public virtual DbSet<LexityScript> LexityScripts { get; set; }
        public virtual DbSet<LexityStore> LexityStores { get; set; }
        public virtual DbSet<LikeBox> LikeBoxes { get; set; }
        public virtual DbSet<MagentoConnectedShop> MagentoConnectedShops { get; set; }
        public virtual DbSet<MerchantActivityLog> MerchantActivityLogs { get; set; }
        public virtual DbSet<MerchantLogin> MerchantLogins { get; set; }
        public virtual DbSet<MerchantsOrder> MerchantsOrders { get; set; }
        public virtual DbSet<MerchantsOrdersIpn> MerchantsOrdersIpns { get; set; }
        public virtual DbSet<MonopolYa> MonopolYas { get; set; }
        public virtual DbSet<PinterestSetting> PinterestSettings { get; set; }
        public virtual DbSet<PlatformExtendedForMagentoApi> PlatformExtendedForMagentoApis { get; set; }
        public virtual DbSet<PlimusContract> PlimusContracts { get; set; }
        public virtual DbSet<PoolCode> PoolCodes { get; set; }
        public virtual DbSet<PrestashopMerchant> PrestashopMerchants { get; set; }
        public virtual DbSet<PriceRanx> PriceRanges { get; set; }
        public virtual DbSet<PriceRule> PriceRules { get; set; }
        public virtual DbSet<ProductImage> ProductImages { get; set; }
        public virtual DbSet<Product> Products { get; set; }
        public virtual DbSet<Products_ImportStatus> Products_ImportStatus { get; set; }
        public virtual DbSet<ReportSubscriber> ReportSubscribers { get; set; }
        public virtual DbSet<RffEmailsUnsubscribed> RffEmailsUnsubscribeds { get; set; }
        public virtual DbSet<RffEvent> RffEvents { get; set; }
        public virtual DbSet<RffOffer> RffOffers { get; set; }
        public virtual DbSet<RffOrder> RffOrders { get; set; }
        public virtual DbSet<RffSharesViaEmail> RffSharesViaEmails { get; set; }
        public virtual DbSet<ScratchPromotion> ScratchPromotions { get; set; }
        public virtual DbSet<ScratchTranslation> ScratchTranslations { get; set; }
        public virtual DbSet<ShopAdmin> ShopAdmins { get; set; }
        public virtual DbSet<ShopContract> ShopContracts { get; set; }
        public virtual DbSet<ShopDetail> ShopDetails { get; set; }
        public virtual DbSet<ShopGallery> ShopGalleries { get; set; }
        public virtual DbSet<ShopifyAppCharge> ShopifyAppCharges { get; set; }
        public virtual DbSet<ShopifyConnectedShop> ShopifyConnectedShops { get; set; }
        public virtual DbSet<ShopLocalization> ShopLocalizations { get; set; }
        public virtual DbSet<ShopMarketingTool> ShopMarketingTools { get; set; }
        public virtual DbSet<ShopOfTheWeek> ShopOfTheWeeks { get; set; }
        public virtual DbSet<ShopPaymentSetting> ShopPaymentSettings { get; set; }
        public virtual DbSet<Shop> Shops { get; set; }
        public virtual DbSet<ShopScreenshoot> ShopScreenshoots { get; set; }
        public virtual DbSet<StorenvyConnectedShop> StorenvyConnectedShops { get; set; }
        public virtual DbSet<Sweepstake> Sweepstakes { get; set; }
        public virtual DbSet<SweepstakesTranslation> SweepstakesTranslations { get; set; }
        public virtual DbSet<SweepstakesUser> SweepstakesUsers { get; set; }
        public virtual DbSet<SyncSettings_Amazon> SyncSettings_Amazon { get; set; }
        public virtual DbSet<SyncSettings_CafepressApi> SyncSettings_CafepressApi { get; set; }
        public virtual DbSet<SyncSettings_Csv> SyncSettings_Csv { get; set; }
        public virtual DbSet<SyncSettings_eBay> SyncSettings_eBay { get; set; }
        public virtual DbSet<SyncSettings_EtsyApi> SyncSettings_EtsyApi { get; set; }
        public virtual DbSet<SyncSettings_GoogleBase> SyncSettings_GoogleBase { get; set; }
        public virtual DbSet<SyncSettings_PrestaShopApi> SyncSettings_PrestaShopApi { get; set; }
        public virtual DbSet<SyncSettings_ShopCOM> SyncSettings_ShopCOM { get; set; }
        public virtual DbSet<SyncSettings_SpreeCommerceApi> SyncSettings_SpreeCommerceApi { get; set; }
        public virtual DbSet<SyncSettings_TictailApi> SyncSettings_TictailApi { get; set; }
        public virtual DbSet<SyncSettings_TradeTubeR> SyncSettings_TradeTubeR { get; set; }
        public virtual DbSet<SyncSettings_WordPress> SyncSettings_WordPress { get; set; }
        public virtual DbSet<SyncSettings_ZazzleApi> SyncSettings_ZazzleApi { get; set; }
        public virtual DbSet<SyncSummary> SyncSummaries { get; set; }
        public virtual DbSet<SystemVariable> SystemVariables { get; set; }
        public virtual DbSet<TargetingRule> TargetingRules { get; set; }
        public virtual DbSet<TbAdCampaign> TbAdCampaigns { get; set; }
        public virtual DbSet<TbBigSpender> TbBigSpenders { get; set; }
        public virtual DbSet<TbPerformanceAlert> TbPerformanceAlerts { get; set; }
        public virtual DbSet<TbRffEmailsShared> TbRffEmailsShareds { get; set; }
        public virtual DbSet<TbRffReferedUser> TbRffReferedUsers { get; set; }
        public virtual DbSet<TbUpgradeCandidate> TbUpgradeCandidates { get; set; }
        public virtual DbSet<TeamKPIHistoryData> TeamKPIHistoryDatas { get; set; }
        public virtual DbSet<Testimonial> Testimonials { get; set; }
        public virtual DbSet<TrafficBoosterImpression> TrafficBoosterImpressions { get; set; }
        public virtual DbSet<TrafficBoosterImpressionsDaily> TrafficBoosterImpressionsDailies { get; set; }
        public virtual DbSet<TTApp> TTApps { get; set; }
        public virtual DbSet<TTAppsAlert> TTAppsAlerts { get; set; }
        public virtual DbSet<TwitterTab> TwitterTabs { get; set; }
        public virtual DbSet<UserPermission> UserPermissions { get; set; }
        public virtual DbSet<UsersFromShopCOM> UsersFromShopCOMs { get; set; }
        public virtual DbSet<UsersInMailGroup> UsersInMailGroups { get; set; }
        public virtual DbSet<UsersUnpublishedNotification> UsersUnpublishedNotifications { get; set; }
        public virtual DbSet<UsersUnsubscribed> UsersUnsubscribeds { get; set; }
        public virtual DbSet<Video> Videos { get; set; }
        public virtual DbSet<Visit> Visits { get; set; }
        public virtual DbSet<WixUser> WixUsers { get; set; }
        public virtual DbSet<WixWebhooksLog> WixWebhooksLogs { get; set; }
        public virtual DbSet<YextConnectedSite> YextConnectedSites { get; set; }
        public virtual DbSet<YouTubeTabSetting> YouTubeTabSettings { get; set; }
        public virtual DbSet<Benchmark> Benchmarks { get; set; }
        public virtual DbSet<LeadDetail> LeadDetails { get; set; }
        public virtual DbSet<GrowthHero> GrowthHeros { get; set; }
        public virtual DbSet<TbCampaign> TbCampaigns { get; set; }
        public virtual DbSet<TbAgreement> TbAgreements { get; set; }
        public virtual DbSet<ShopApp> ShopApps { get; set; }
        public virtual DbSet<SystemEvent> SystemEvents { get; set; }
        public virtual DbSet<ShopAttributesValue> ShopAttributesValues { get; set; }
        public virtual DbSet<ExpAdsStructure> ExpAdsStructures { get; set; }
        public virtual DbSet<Experiment> Experiments { get; set; }
        public virtual DbSet<ExpObject> ExpObjects { get; set; }
        public virtual DbSet<GAConnectedProfile> GAConnectedProfiles { get; set; }
        public virtual DbSet<ExpResult> ExpResults { get; set; }
        public virtual DbSet<ExpResultsByObject> ExpResultsByObjects { get; set; }
        public virtual DbSet<ExpGroup> ExpGroups { get; set; }
        public virtual DbSet<Referer> Referers { get; set; }
        public virtual DbSet<ExpAccount> ExpAccounts { get; set; }
        public virtual DbSet<WallaShopsProduct> WallaShopsProducts { get; set; }
        public virtual DbSet<TbInternalTask> TbInternalTasks { get; set; }
        public virtual DbSet<TbFacebookAdsSetup> TbFacebookAdsSetups { get; set; }
        public virtual DbSet<UsersReferedFrom> UsersReferedFroms { get; set; }
        public virtual DbSet<TrafficBooster> TrafficBoosters { get; set; }
        public virtual DbSet<TbAdditionalData> TbAdditionalDatas { get; set; }
        public virtual DbSet<LauncherCommand> LauncherCommands { get; set; }
        public virtual DbSet<LauncherTriggeredCommand> LauncherTriggeredCommands { get; set; }
        public virtual DbSet<Log4Net_Data> Log4Net_Data { get; set; }
        public virtual DbSet<LauncherCommandType> LauncherCommandTypes { get; set; }
        public virtual DbSet<PaypalConnectedUser> PaypalConnectedUsers { get; set; }
        public virtual DbSet<SyncSettings_ShopifyApi> SyncSettings_ShopifyApi { get; set; }
        public virtual DbSet<SyncSettings_SquareAPI> SyncSettings_SquareAPI { get; set; }
        public virtual DbSet<BlacklistedClient> BlacklistedClients { get; set; }
        public virtual DbSet<NetoConnectedShop> NetoConnectedShops { get; set; }
        public virtual DbSet<PaymentsTransaction> PaymentsTransactions { get; set; }
        public virtual DbSet<SquareConnectedShop> SquareConnectedShops { get; set; }
        public virtual DbSet<ShopSubscription> ShopSubscriptions { get; set; }
        public virtual DbSet<BenchmarkApi> BenchmarkApis { get; set; }
        public virtual DbSet<WixConnectedSite> WixConnectedSites { get; set; }
        public virtual DbSet<WallaShopsMonthlyData> WallaShopsMonthlyDatas { get; set; }
        public virtual DbSet<PlimusIpnCall> PlimusIpnCalls { get; set; }
        public virtual DbSet<ShopAchievement> ShopAchievements { get; set; }
        public virtual DbSet<ProductsFeed> ProductsFeeds { get; set; }
        public virtual DbSet<DashboardProData> DashboardProDatas { get; set; }
        public virtual DbSet<ShopPromotion> ShopPromotions { get; set; }
        public virtual DbSet<ShopPromotionsUsageHistory> ShopPromotionsUsageHistories { get; set; }
        public virtual DbSet<CurrencyRatesToUSD> CurrencyRatesToUSDs { get; set; }
        public virtual DbSet<AdCopy> AdCopies { get; set; }
        public virtual DbSet<ShopOnlinePromotion> ShopOnlinePromotions { get; set; }
        public virtual DbSet<AdwordsCampaignEntity> AdwordsCampaignEntities { get; set; }
        public virtual DbSet<EmailCampaign> EmailCampaigns { get; set; }
        public virtual DbSet<MCConnectedAccount> MCConnectedAccounts { get; set; }
        public virtual DbSet<MCConnectedSite> MCConnectedSites { get; set; }
        public virtual DbSet<GA4ConnectedProperties> GA4ConnectedProperties { get; set; }
        public virtual DbSet<ProductDescribersHistory> ProductDescribersHistory { get; set; }
        public virtual DbSet<GAConnectedAccountsStat> GAConnectedAccountsStats { get; set; }
        public virtual DbSet<GAConnectedAccountsStatsHistory> GAConnectedAccountsStatsHistories { get; set; }
        public virtual DbSet<ShopifyAppstoreReview> ShopifyAppstoreReviews { get; set; }
        public virtual DbSet<ProductDescribers> ProductDescribers { get; set; }
        public virtual DbSet<BoReminder> BoReminders { get; set; }
        public virtual DbSet<CouponPopEventsV2> CouponPopEventsV2 { get; set; }
        public virtual DbSet<CouponPopStatsV2> CouponPopStatsV2 { get; set; }
        public virtual DbSet<CouponPopViewsV2> CouponPopViewsV2 { get; set; }
        public virtual DbSet<FacebookB2BLeads> FacebookB2BLeads { get; set; }
        public virtual DbSet<SeriesEmail> SeriesEmails { get; set; }
        public virtual DbSet<SeriesUsersEvent> SeriesUsersEvents { get; set; }
        public virtual DbSet<SeriesUser> SeriesUsers { get; set; }
        public virtual DbSet<BenchmarkImagesOptimizationHistory> BenchmarkImagesOptimizationHistories { get; set; }
        public virtual DbSet<SeriesSystemEvent> SeriesSystemEvents { get; set; }
        public virtual DbSet<TbAccountTrafficChannel> TbAccountTrafficChannels { get; set; }
        public virtual DbSet<BenchmarkImagesOptimization> BenchmarkImagesOptimizations { get; set; }
        public virtual DbSet<LeadsForSale> LeadsForSales { get; set; }
        public virtual DbSet<EmailProvider_ConstantContact> EmailProvider_ConstantContact { get; set; }
        public virtual DbSet<Series> Series { get; set; }
        public virtual DbSet<SuperAdminRemark> SuperAdminRemarks { get; set; }
        public virtual DbSet<SyncSettings_MagentoApi> SyncSettings_MagentoApi { get; set; }
        public virtual DbSet<TbUserCreative> TbUserCreatives { get; set; }
        public virtual DbSet<ShopifyMerchant> ShopifyMerchants { get; set; }
        public virtual DbSet<TBReport> TBReports { get; set; }
        public virtual DbSet<TbReports2> TbReports2 { get; set; }
        public virtual DbSet<TrustBadge> TrustBadges { get; set; }
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<TBFeeHistory> TBFeeHistories { get; set; }
        public virtual DbSet<TbAdCampaignsPerformance> TbAdCampaignsPerformances { get; set; }
        public virtual DbSet<WixTransaction> WixTransactions { get; set; }
        public virtual DbSet<EmailsLog> EmailsLogs { get; set; }
        public virtual DbSet<DashboardAlert> DashboardAlerts { get; set; }
        public virtual DbSet<CouponPop> CouponPops { get; set; }
        public virtual DbSet<AISeoProductOptimizer> AISeoProductOptimizers { get; set; }
        public virtual DbSet<AISeoProductOptimizerHistory> AISeoProductOptimizerHistories { get; set; }
       // public virtual DbSet<SQLDataDashboard> SQLDataDashboards { get; set; }
        public virtual DbSet<SQLDataReport> SQLDataReports { get; set; }
      //  public virtual DbSet<SQLDataDashboardReport> SQLDataDashboardReports { get; set; }
    
        [DbFunction("StoreYaEntities", "Split")]
        public virtual IQueryable<Split_Result> Split(string @string, string delimiter)
        {
            var stringParameter = @string != null ?
                new ObjectParameter("String", @string) :
                new ObjectParameter("String", typeof(string));
    
            var delimiterParameter = delimiter != null ?
                new ObjectParameter("Delimiter", delimiter) :
                new ObjectParameter("Delimiter", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<Split_Result>("[StoreYaEntities].[Split](@String, @Delimiter)", stringParameter, delimiterParameter);
        }
    
        public virtual ObjectResult<string> f__proc_Shop_AllData_Delete(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("f__proc_Shop_AllData_Delete", shopIDParameter);
        }
    
        public virtual int proc_BackgroundTasks_Status_Update(Nullable<int> taskID, Nullable<int> newStatus)
        {
            var taskIDParameter = taskID.HasValue ?
                new ObjectParameter("taskID", taskID) :
                new ObjectParameter("taskID", typeof(int));
    
            var newStatusParameter = newStatus.HasValue ?
                new ObjectParameter("newStatus", newStatus) :
                new ObjectParameter("newStatus", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_BackgroundTasks_Status_Update", taskIDParameter, newStatusParameter);
        }
    
        public virtual int proc_Categories_Delete(Nullable<int> categoryID)
        {
            var categoryIDParameter = categoryID.HasValue ?
                new ObjectParameter("categoryID", categoryID) :
                new ObjectParameter("categoryID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Categories_Delete", categoryIDParameter);
        }
    
        public virtual int proc_Categories_MoveOrdinal_Update(Nullable<int> shopID, Nullable<int> movedCategoryID, Nullable<int> targetCategoryID, Nullable<int> above)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var movedCategoryIDParameter = movedCategoryID.HasValue ?
                new ObjectParameter("movedCategoryID", movedCategoryID) :
                new ObjectParameter("movedCategoryID", typeof(int));
    
            var targetCategoryIDParameter = targetCategoryID.HasValue ?
                new ObjectParameter("targetCategoryID", targetCategoryID) :
                new ObjectParameter("targetCategoryID", typeof(int));
    
            var aboveParameter = above.HasValue ?
                new ObjectParameter("above", above) :
                new ObjectParameter("above", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Categories_MoveOrdinal_Update", shopIDParameter, movedCategoryIDParameter, targetCategoryIDParameter, aboveParameter);
        }
    
        public virtual int proc_Categories_SetOrdinal_Update(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Categories_SetOrdinal_Update", shopIDParameter);
        }
    
        public virtual int proc_Categories_SetOrdinalFirstTime_Update(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Categories_SetOrdinalFirstTime_Update", shopIDParameter);
        }
    
        public virtual int proc_CategoriesWithoutProducts_Delete(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_CategoriesWithoutProducts_Delete", shopIDParameter);
        }
    
        public virtual int proc_Collection_Delete(Nullable<int> collectionID)
        {
            var collectionIDParameter = collectionID.HasValue ?
                new ObjectParameter("collectionID", collectionID) :
                new ObjectParameter("collectionID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Collection_Delete", collectionIDParameter);
        }
    
        public virtual int proc_CollectionProducts_MoveOrdinal_Update(Nullable<int> shopID, Nullable<int> collectionID, Nullable<int> movedProductsID, Nullable<int> targetProductsID, Nullable<int> above)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var collectionIDParameter = collectionID.HasValue ?
                new ObjectParameter("collectionID", collectionID) :
                new ObjectParameter("collectionID", typeof(int));
    
            var movedProductsIDParameter = movedProductsID.HasValue ?
                new ObjectParameter("movedProductsID", movedProductsID) :
                new ObjectParameter("movedProductsID", typeof(int));
    
            var targetProductsIDParameter = targetProductsID.HasValue ?
                new ObjectParameter("targetProductsID", targetProductsID) :
                new ObjectParameter("targetProductsID", typeof(int));
    
            var aboveParameter = above.HasValue ?
                new ObjectParameter("above", above) :
                new ObjectParameter("above", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_CollectionProducts_MoveOrdinal_Update", shopIDParameter, collectionIDParameter, movedProductsIDParameter, targetProductsIDParameter, aboveParameter);
        }
    
        public virtual int proc_CollectionProducts_SetOrdinal_Update(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_CollectionProducts_SetOrdinal_Update", shopIDParameter);
        }
    
        public virtual int proc_CollectionProducts_Upsert(Nullable<int> shopID, string sku, string collections)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var skuParameter = sku != null ?
                new ObjectParameter("sku", sku) :
                new ObjectParameter("sku", typeof(string));
    
            var collectionsParameter = collections != null ?
                new ObjectParameter("collections", collections) :
                new ObjectParameter("collections", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_CollectionProducts_Upsert", shopIDParameter, skuParameter, collectionsParameter);
        }
    
        public virtual int proc_Collections_MoveOrdinal_Update(Nullable<int> shopID, Nullable<int> movedCollectionID, Nullable<int> targetCollectionID, Nullable<int> above)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var movedCollectionIDParameter = movedCollectionID.HasValue ?
                new ObjectParameter("movedCollectionID", movedCollectionID) :
                new ObjectParameter("movedCollectionID", typeof(int));
    
            var targetCollectionIDParameter = targetCollectionID.HasValue ?
                new ObjectParameter("targetCollectionID", targetCollectionID) :
                new ObjectParameter("targetCollectionID", typeof(int));
    
            var aboveParameter = above.HasValue ?
                new ObjectParameter("above", above) :
                new ObjectParameter("above", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Collections_MoveOrdinal_Update", shopIDParameter, movedCollectionIDParameter, targetCollectionIDParameter, aboveParameter);
        }
    
        public virtual ObjectResult<proc_CouponPops_Stats_Select_Result> proc_CouponPops_Stats_Select(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_CouponPops_Stats_Select_Result>("proc_CouponPops_Stats_Select", shopIDParameter);
        }
    
        public virtual ObjectResult<proc_CouponPops_Stats2_Select_Result> proc_CouponPops_Stats2_Select(Nullable<int> shopID, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_CouponPops_Stats2_Select_Result>("proc_CouponPops_Stats2_Select", shopIDParameter, fromDateParameter, toDateParameter);
        }
    
        public virtual ObjectResult<proc_CouponPops_Stats3_Select_Result> proc_CouponPops_Stats3_Select(Nullable<int> shopID, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_CouponPops_Stats3_Select_Result>("proc_CouponPops_Stats3_Select", shopIDParameter, fromDateParameter, toDateParameter);
        }
    
        public virtual ObjectResult<proc_Events_TopProduct_Select_Result> proc_Events_TopProduct_Select(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_Events_TopProduct_Select_Result>("proc_Events_TopProduct_Select", shopIDParameter);
        }
    
        public virtual ObjectResult<proc_ExitPops_AllShops_Stats_Select_Result> proc_ExitPops_AllShops_Stats_Select(Nullable<int> daysBack)
        {
            var daysBackParameter = daysBack.HasValue ?
                new ObjectParameter("daysBack", daysBack) :
                new ObjectParameter("daysBack", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_ExitPops_AllShops_Stats_Select_Result>("proc_ExitPops_AllShops_Stats_Select", daysBackParameter);
        }
    
        public virtual ObjectResult<proc_ExitPops_Stats_Select_Result> proc_ExitPops_Stats_Select(Nullable<int> shopID, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_ExitPops_Stats_Select_Result>("proc_ExitPops_Stats_Select", shopIDParameter, fromDateParameter, toDateParameter);
        }
    
        public virtual ObjectResult<proc_Log4Net_Data_Agregated_Select_Result> proc_Log4Net_Data_Agregated_Select()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_Log4Net_Data_Agregated_Select_Result>("proc_Log4Net_Data_Agregated_Select");
        }
    
        public virtual int proc_Products_ApplyRule(Nullable<int> ruleID)
        {
            var ruleIDParameter = ruleID.HasValue ?
                new ObjectParameter("ruleID", ruleID) :
                new ObjectParameter("ruleID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_ApplyRule", ruleIDParameter);
        }
    
        public virtual int proc_Products_ByID_Update(Nullable<int> shopid, Nullable<int> productID, string sku, string name, string description, string htmlDescription, string imageUrl, Nullable<decimal> price, string localImage, Nullable<int> disabled, string outurl)
        {
            var shopidParameter = shopid.HasValue ?
                new ObjectParameter("shopid", shopid) :
                new ObjectParameter("shopid", typeof(int));
    
            var productIDParameter = productID.HasValue ?
                new ObjectParameter("productID", productID) :
                new ObjectParameter("productID", typeof(int));
    
            var skuParameter = sku != null ?
                new ObjectParameter("sku", sku) :
                new ObjectParameter("sku", typeof(string));
    
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            var descriptionParameter = description != null ?
                new ObjectParameter("description", description) :
                new ObjectParameter("description", typeof(string));
    
            var htmlDescriptionParameter = htmlDescription != null ?
                new ObjectParameter("htmlDescription", htmlDescription) :
                new ObjectParameter("htmlDescription", typeof(string));
    
            var imageUrlParameter = imageUrl != null ?
                new ObjectParameter("imageUrl", imageUrl) :
                new ObjectParameter("imageUrl", typeof(string));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var localImageParameter = localImage != null ?
                new ObjectParameter("localImage", localImage) :
                new ObjectParameter("localImage", typeof(string));
    
            var disabledParameter = disabled.HasValue ?
                new ObjectParameter("disabled", disabled) :
                new ObjectParameter("disabled", typeof(int));
    
            var outurlParameter = outurl != null ?
                new ObjectParameter("outurl", outurl) :
                new ObjectParameter("outurl", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_ByID_Update", shopidParameter, productIDParameter, skuParameter, nameParameter, descriptionParameter, htmlDescriptionParameter, imageUrlParameter, priceParameter, localImageParameter, disabledParameter, outurlParameter);
        }
    
        public virtual int proc_Products_BySku_Update(Nullable<int> productID, Nullable<int> shopid, string sku, string name, string description, string htmlDescription, string imageUrl, Nullable<decimal> price, string localImage, string outurl)
        {
            var productIDParameter = productID.HasValue ?
                new ObjectParameter("productID", productID) :
                new ObjectParameter("productID", typeof(int));
    
            var shopidParameter = shopid.HasValue ?
                new ObjectParameter("shopid", shopid) :
                new ObjectParameter("shopid", typeof(int));
    
            var skuParameter = sku != null ?
                new ObjectParameter("sku", sku) :
                new ObjectParameter("sku", typeof(string));
    
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            var descriptionParameter = description != null ?
                new ObjectParameter("description", description) :
                new ObjectParameter("description", typeof(string));
    
            var htmlDescriptionParameter = htmlDescription != null ?
                new ObjectParameter("htmlDescription", htmlDescription) :
                new ObjectParameter("htmlDescription", typeof(string));
    
            var imageUrlParameter = imageUrl != null ?
                new ObjectParameter("imageUrl", imageUrl) :
                new ObjectParameter("imageUrl", typeof(string));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var localImageParameter = localImage != null ?
                new ObjectParameter("localImage", localImage) :
                new ObjectParameter("localImage", typeof(string));
    
            var outurlParameter = outurl != null ?
                new ObjectParameter("outurl", outurl) :
                new ObjectParameter("outurl", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_BySku_Update", productIDParameter, shopidParameter, skuParameter, nameParameter, descriptionParameter, htmlDescriptionParameter, imageUrlParameter, priceParameter, localImageParameter, outurlParameter);
        }
    
        public virtual int proc_Products_BySku_Upsert(Nullable<int> shopid, string sku, string name, string description, string htmlDescription, string imageUrl, Nullable<decimal> price, string localImage, Nullable<int> disabled, string outurl)
        {
            var shopidParameter = shopid.HasValue ?
                new ObjectParameter("shopid", shopid) :
                new ObjectParameter("shopid", typeof(int));
    
            var skuParameter = sku != null ?
                new ObjectParameter("sku", sku) :
                new ObjectParameter("sku", typeof(string));
    
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            var descriptionParameter = description != null ?
                new ObjectParameter("description", description) :
                new ObjectParameter("description", typeof(string));
    
            var htmlDescriptionParameter = htmlDescription != null ?
                new ObjectParameter("htmlDescription", htmlDescription) :
                new ObjectParameter("htmlDescription", typeof(string));
    
            var imageUrlParameter = imageUrl != null ?
                new ObjectParameter("imageUrl", imageUrl) :
                new ObjectParameter("imageUrl", typeof(string));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var localImageParameter = localImage != null ?
                new ObjectParameter("localImage", localImage) :
                new ObjectParameter("localImage", typeof(string));
    
            var disabledParameter = disabled.HasValue ?
                new ObjectParameter("disabled", disabled) :
                new ObjectParameter("disabled", typeof(int));
    
            var outurlParameter = outurl != null ?
                new ObjectParameter("outurl", outurl) :
                new ObjectParameter("outurl", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_BySku_Upsert", shopidParameter, skuParameter, nameParameter, descriptionParameter, htmlDescriptionParameter, imageUrlParameter, priceParameter, localImageParameter, disabledParameter, outurlParameter);
        }
    
        public virtual int proc_Products_DisableDiscounts_Update(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_DisableDiscounts_Update", shopIDParameter);
        }
    
        public virtual int proc_Products_DisabledProducts_Delete(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_DisabledProducts_Delete", shopIDParameter);
        }
    
        public virtual ObjectResult<string> proc_Products_FixOutUrl_Update(Nullable<int> shopid, string fromBase, string toBase, string exclude)
        {
            var shopidParameter = shopid.HasValue ?
                new ObjectParameter("shopid", shopid) :
                new ObjectParameter("shopid", typeof(int));
    
            var fromBaseParameter = fromBase != null ?
                new ObjectParameter("fromBase", fromBase) :
                new ObjectParameter("fromBase", typeof(string));
    
            var toBaseParameter = toBase != null ?
                new ObjectParameter("toBase", toBase) :
                new ObjectParameter("toBase", typeof(string));
    
            var excludeParameter = exclude != null ?
                new ObjectParameter("exclude", exclude) :
                new ObjectParameter("exclude", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("proc_Products_FixOutUrl_Update", shopidParameter, fromBaseParameter, toBaseParameter, excludeParameter);
        }
    
        public virtual int proc_Products_ImportStatus_Insert(Nullable<int> taskID)
        {
            var taskIDParameter = taskID.HasValue ?
                new ObjectParameter("taskID", taskID) :
                new ObjectParameter("taskID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_ImportStatus_Insert", taskIDParameter);
        }
    
        public virtual int proc_Products_ImportStatus_SetStatus_Update(Nullable<int> productID, Nullable<int> taskID, Nullable<int> newStatus)
        {
            var productIDParameter = productID.HasValue ?
                new ObjectParameter("productID", productID) :
                new ObjectParameter("productID", typeof(int));
    
            var taskIDParameter = taskID.HasValue ?
                new ObjectParameter("taskID", taskID) :
                new ObjectParameter("taskID", typeof(int));
    
            var newStatusParameter = newStatus.HasValue ?
                new ObjectParameter("newStatus", newStatus) :
                new ObjectParameter("newStatus", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_ImportStatus_SetStatus_Update", productIDParameter, taskIDParameter, newStatusParameter);
        }
    
        public virtual int proc_Products_Insert(Nullable<int> shopid, string sku, string name, Nullable<int> categoryID)
        {
            var shopidParameter = shopid.HasValue ?
                new ObjectParameter("shopid", shopid) :
                new ObjectParameter("shopid", typeof(int));
    
            var skuParameter = sku != null ?
                new ObjectParameter("sku", sku) :
                new ObjectParameter("sku", typeof(string));
    
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            var categoryIDParameter = categoryID.HasValue ?
                new ObjectParameter("categoryID", categoryID) :
                new ObjectParameter("categoryID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_Insert", shopidParameter, skuParameter, nameParameter, categoryIDParameter);
        }
    
        public virtual int proc_Products_MoveOrdinal_Update(Nullable<int> shopID, Nullable<int> movedProductsID, Nullable<int> targetProductsID, Nullable<int> above)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var movedProductsIDParameter = movedProductsID.HasValue ?
                new ObjectParameter("movedProductsID", movedProductsID) :
                new ObjectParameter("movedProductsID", typeof(int));
    
            var targetProductsIDParameter = targetProductsID.HasValue ?
                new ObjectParameter("targetProductsID", targetProductsID) :
                new ObjectParameter("targetProductsID", typeof(int));
    
            var aboveParameter = above.HasValue ?
                new ObjectParameter("above", above) :
                new ObjectParameter("above", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_MoveOrdinal_Update", shopIDParameter, movedProductsIDParameter, targetProductsIDParameter, aboveParameter);
        }
    
        public virtual int proc_Products_MoveOrdinalAtHomepage_Update(Nullable<int> shopID, Nullable<int> movedProductsID, Nullable<int> targetProductsID, Nullable<int> above)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var movedProductsIDParameter = movedProductsID.HasValue ?
                new ObjectParameter("movedProductsID", movedProductsID) :
                new ObjectParameter("movedProductsID", typeof(int));
    
            var targetProductsIDParameter = targetProductsID.HasValue ?
                new ObjectParameter("targetProductsID", targetProductsID) :
                new ObjectParameter("targetProductsID", typeof(int));
    
            var aboveParameter = above.HasValue ?
                new ObjectParameter("above", above) :
                new ObjectParameter("above", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_MoveOrdinalAtHomepage_Update", shopIDParameter, movedProductsIDParameter, targetProductsIDParameter, aboveParameter);
        }
    
        public virtual int proc_Products_SetDisabled_Update(Nullable<int> shopid, Nullable<int> productID)
        {
            var shopidParameter = shopid.HasValue ?
                new ObjectParameter("shopid", shopid) :
                new ObjectParameter("shopid", typeof(int));
    
            var productIDParameter = productID.HasValue ?
                new ObjectParameter("productID", productID) :
                new ObjectParameter("productID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_SetDisabled_Update", shopidParameter, productIDParameter);
        }
    
        public virtual int proc_Products_SetOrdinal_Update(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Products_SetOrdinal_Update", shopIDParameter);
        }
    
        public virtual int proc_ReportSubscribers_Insert(Nullable<int> daysBack)
        {
            var daysBackParameter = daysBack.HasValue ?
                new ObjectParameter("daysBack", daysBack) :
                new ObjectParameter("daysBack", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_ReportSubscribers_Insert", daysBackParameter);
        }
    
        public virtual ObjectResult<proc_RffOrders_AllShops_Select_Result> proc_RffOrders_AllShops_Select(Nullable<int> daysBack)
        {
            var daysBackParameter = daysBack.HasValue ?
                new ObjectParameter("daysBack", daysBack) :
                new ObjectParameter("daysBack", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_RffOrders_AllShops_Select_Result>("proc_RffOrders_AllShops_Select", daysBackParameter);
        }
    
        public virtual ObjectResult<proc_RffOrders_AllShops_SelectByDate_Result> proc_RffOrders_AllShops_SelectByDate(Nullable<int> shopID, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_RffOrders_AllShops_SelectByDate_Result>("proc_RffOrders_AllShops_SelectByDate", shopIDParameter, fromDateParameter, toDateParameter);
        }
    
        public virtual ObjectResult<proc_RffOrders_ForReport_Select_Result> proc_RffOrders_ForReport_Select(Nullable<int> shopid, Nullable<int> daysBack)
        {
            var shopidParameter = shopid.HasValue ?
                new ObjectParameter("shopid", shopid) :
                new ObjectParameter("shopid", typeof(int));
    
            var daysBackParameter = daysBack.HasValue ?
                new ObjectParameter("daysBack", daysBack) :
                new ObjectParameter("daysBack", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_RffOrders_ForReport_Select_Result>("proc_RffOrders_ForReport_Select", shopidParameter, daysBackParameter);
        }
    
        public virtual int proc_ShopAdmins_SetNewCreatedUser_Update(Nullable<long> fbProfileID, Nullable<int> userID, Nullable<long> adminPermissions, Nullable<long> clientPermissions)
        {
            var fbProfileIDParameter = fbProfileID.HasValue ?
                new ObjectParameter("fbProfileID", fbProfileID) :
                new ObjectParameter("fbProfileID", typeof(long));
    
            var userIDParameter = userID.HasValue ?
                new ObjectParameter("userID", userID) :
                new ObjectParameter("userID", typeof(int));
    
            var adminPermissionsParameter = adminPermissions.HasValue ?
                new ObjectParameter("adminPermissions", adminPermissions) :
                new ObjectParameter("adminPermissions", typeof(long));
    
            var clientPermissionsParameter = clientPermissions.HasValue ?
                new ObjectParameter("clientPermissions", clientPermissions) :
                new ObjectParameter("clientPermissions", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_ShopAdmins_SetNewCreatedUser_Update", fbProfileIDParameter, userIDParameter, adminPermissionsParameter, clientPermissionsParameter);
        }
    
        public virtual int proc_Shops_AllData_Delete(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Shops_AllData_Delete", shopIDParameter);
        }
    
        public virtual int proc_Shops_CatalogStatus_Update(Nullable<int> shopID, Nullable<int> newState)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var newStateParameter = newState.HasValue ?
                new ObjectParameter("newState", newState) :
                new ObjectParameter("newState", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Shops_CatalogStatus_Update", shopIDParameter, newStateParameter);
        }
    
        public virtual int proc_Shops_Disable(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Shops_Disable", shopIDParameter);
        }
    
        public virtual int proc_Shops_DowngradeToFree_Update(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Shops_DowngradeToFree_Update", shopIDParameter);
        }
    
        public virtual int proc_Shops_Remove(Nullable<int> shopID)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Shops_Remove", shopIDParameter);
        }
    
        public virtual int proc_SyncSettings_Amazon_Update(Nullable<int> shopID, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_Amazon_Update", shopIDParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_Cafepress_Update(Nullable<int> shopID, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_Cafepress_Update", shopIDParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_Csv_Update(Nullable<int> shopID, string externalUrl, string uploadedFilePath, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var externalUrlParameter = externalUrl != null ?
                new ObjectParameter("externalUrl", externalUrl) :
                new ObjectParameter("externalUrl", typeof(string));
    
            var uploadedFilePathParameter = uploadedFilePath != null ?
                new ObjectParameter("uploadedFilePath", uploadedFilePath) :
                new ObjectParameter("uploadedFilePath", typeof(string));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_Csv_Update", shopIDParameter, externalUrlParameter, uploadedFilePathParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_eBay_Update(Nullable<int> shopID, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_eBay_Update", shopIDParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_Etsy_Update(Nullable<int> shopID, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_Etsy_Update", shopIDParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_GoogleBase_Update(Nullable<bool> isUpload, Nullable<int> shopID, string uploadedFilePath, Nullable<int> status)
        {
            var isUploadParameter = isUpload.HasValue ?
                new ObjectParameter("isUpload", isUpload) :
                new ObjectParameter("isUpload", typeof(bool));
    
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var uploadedFilePathParameter = uploadedFilePath != null ?
                new ObjectParameter("uploadedFilePath", uploadedFilePath) :
                new ObjectParameter("uploadedFilePath", typeof(string));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_GoogleBase_Update", isUploadParameter, shopIDParameter, uploadedFilePathParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_MagentoApi_Update(Nullable<int> shopID, string apiKey, string apiuser, string storeView, Nullable<int> status, Nullable<int> hideOutOffStock)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var apiKeyParameter = apiKey != null ?
                new ObjectParameter("apiKey", apiKey) :
                new ObjectParameter("apiKey", typeof(string));
    
            var apiuserParameter = apiuser != null ?
                new ObjectParameter("apiuser", apiuser) :
                new ObjectParameter("apiuser", typeof(string));
    
            var storeViewParameter = storeView != null ?
                new ObjectParameter("storeView", storeView) :
                new ObjectParameter("storeView", typeof(string));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            var hideOutOffStockParameter = hideOutOffStock.HasValue ?
                new ObjectParameter("hideOutOffStock", hideOutOffStock) :
                new ObjectParameter("hideOutOffStock", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_MagentoApi_Update", shopIDParameter, apiKeyParameter, apiuserParameter, storeViewParameter, statusParameter, hideOutOffStockParameter);
        }
    
        public virtual int proc_SyncSettings_PrestaShopApi_Update(Nullable<int> shopID, string apiKey, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var apiKeyParameter = apiKey != null ?
                new ObjectParameter("apiKey", apiKey) :
                new ObjectParameter("apiKey", typeof(string));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_PrestaShopApi_Update", shopIDParameter, apiKeyParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_SpreeCommerceApi_Update(Nullable<int> shopID, string apiKey, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var apiKeyParameter = apiKey != null ?
                new ObjectParameter("apiKey", apiKey) :
                new ObjectParameter("apiKey", typeof(string));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_SpreeCommerceApi_Update", shopIDParameter, apiKeyParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_TradeTubeR_Update(Nullable<int> shopID, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_TradeTubeR_Update", shopIDParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_WordPress_Update(Nullable<int> shopID, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_WordPress_Update", shopIDParameter, statusParameter);
        }
    
        public virtual int proc_SyncSettings_Zazzle_Update(Nullable<int> shopID, Nullable<int> status)
        {
            var shopIDParameter = shopID.HasValue ?
                new ObjectParameter("shopID", shopID) :
                new ObjectParameter("shopID", typeof(int));
    
            var statusParameter = status.HasValue ?
                new ObjectParameter("status", status) :
                new ObjectParameter("status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_SyncSettings_Zazzle_Update", shopIDParameter, statusParameter);
        }
    
        public virtual int proc_Users_AllData_Delete(Nullable<int> userID)
        {
            var userIDParameter = userID.HasValue ?
                new ObjectParameter("userID", userID) :
                new ObjectParameter("userID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Users_AllData_Delete", userIDParameter);
        }
    
        public virtual int proc_Users_Disable(Nullable<int> userID)
        {
            var userIDParameter = userID.HasValue ?
                new ObjectParameter("userID", userID) :
                new ObjectParameter("userID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Users_Disable", userIDParameter);
        }
    
        public virtual ObjectResult<proc_Users_ForEmailList_Select_Result> proc_Users_ForEmailList_Select(Nullable<System.DateTime> updatedFrom)
        {
            var updatedFromParameter = updatedFrom.HasValue ?
                new ObjectParameter("updatedFrom", updatedFrom) :
                new ObjectParameter("updatedFrom", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_Users_ForEmailList_Select_Result>("proc_Users_ForEmailList_Select", updatedFromParameter);
        }
    
        public virtual ObjectResult<proc_Visits_TopShops_Select_Result> proc_Visits_TopShops_Select()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<proc_Visits_TopShops_Select_Result>("proc_Visits_TopShops_Select");
        }
    
        public virtual int usp_InsertLeadDetails()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_InsertLeadDetails");
        }
    
        [DbFunction("StoreYaEntities", "IsThisValue")]
        public virtual IQueryable<Nullable<int>> IsThisValue(Nullable<int> expected, Nullable<int> actual)
        {
            var expectedParameter = expected.HasValue ?
                new ObjectParameter("expected", expected) :
                new ObjectParameter("expected", typeof(int));
    
            var actualParameter = actual.HasValue ?
                new ObjectParameter("actual", actual) :
                new ObjectParameter("actual", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<Nullable<int>>("[StoreYaEntities].[IsThisValue](@expected, @actual)", expectedParameter, actualParameter);
        }
    
        public virtual int proc_Insert_Log4Net(Nullable<System.DateTime> dATE, string tHREAD, string lEVEL, string lOGGER, string mESSAGE, string eXCEPTION, Nullable<int> sHOPID)
        {
            var dATEParameter = dATE.HasValue ?
                new ObjectParameter("DATE", dATE) :
                new ObjectParameter("DATE", typeof(System.DateTime));
    
            var tHREADParameter = tHREAD != null ?
                new ObjectParameter("THREAD", tHREAD) :
                new ObjectParameter("THREAD", typeof(string));
    
            var lEVELParameter = lEVEL != null ?
                new ObjectParameter("LEVEL", lEVEL) :
                new ObjectParameter("LEVEL", typeof(string));
    
            var lOGGERParameter = lOGGER != null ?
                new ObjectParameter("LOGGER", lOGGER) :
                new ObjectParameter("LOGGER", typeof(string));
    
            var mESSAGEParameter = mESSAGE != null ?
                new ObjectParameter("MESSAGE", mESSAGE) :
                new ObjectParameter("MESSAGE", typeof(string));
    
            var eXCEPTIONParameter = eXCEPTION != null ?
                new ObjectParameter("EXCEPTION", eXCEPTION) :
                new ObjectParameter("EXCEPTION", typeof(string));
    
            var sHOPIDParameter = sHOPID.HasValue ?
                new ObjectParameter("SHOPID", sHOPID) :
                new ObjectParameter("SHOPID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("proc_Insert_Log4Net", dATEParameter, tHREADParameter, lEVELParameter, lOGGERParameter, mESSAGEParameter, eXCEPTIONParameter, sHOPIDParameter);
        }
    }
}
