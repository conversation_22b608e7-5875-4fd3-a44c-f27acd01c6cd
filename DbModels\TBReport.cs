//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TBReport
    {
        public int ID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<System.DateTime> ReportDate { get; set; }
        public Nullable<int> CampaignType { get; set; }
        public Nullable<int> SiteSessions { get; set; }
        public Nullable<int> SiteCarts { get; set; }
        public Nullable<int> SitePurchases { get; set; }
        public Nullable<int> SiteTransactions { get; set; }
        public Nullable<decimal> SiteTransactionRevenue { get; set; }
        public Nullable<int> OurSessions { get; set; }
        public Nullable<int> OurCarts { get; set; }
        public Nullable<int> OurPurchases { get; set; }
        public Nullable<int> OurTransactions { get; set; }
        public Nullable<decimal> OurTransactionRevenue { get; set; }
        public Nullable<int> AwImpressions { get; set; }
        public Nullable<int> AwClicks { get; set; }
        public Nullable<decimal> AwCost { get; set; }
        public Nullable<int> AwCarts { get; set; }
        public Nullable<int> AwPurchases { get; set; }
        public Nullable<decimal> AwConversionValue { get; set; }
        public Nullable<int> TheirCpcClicks { get; set; }
        public Nullable<decimal> TheirCpcCost { get; set; }
        public Nullable<int> TheirCpcSessions { get; set; }
        public Nullable<int> TheirCpcTransactionss { get; set; }
        public Nullable<decimal> TheirCpcRevenue { get; set; }
        public Nullable<int> StrySegmentTransactions { get; set; }
        public Nullable<decimal> StrySegmentRevenue { get; set; }
        public Nullable<int> StrySegmentSessions { get; set; }
        public Nullable<decimal> AwAveragePosition { get; set; }
        public Nullable<decimal> AwPurchasesDec { get; set; }
        public Nullable<decimal> StrySegmentRevenueUSD { get; set; }
        public Nullable<int> PreserveStatus { get; set; }
        public Nullable<int> StrySegmentAllStoreyaTransactions { get; set; }
        public Nullable<decimal> ConversionValue14DaysWithoutLate { get; set; }
        public Nullable<decimal> ConversionValue14DaysIncludingLate { get; set; }
        public Nullable<decimal> AwConversionsValueInOriginCurrency { get; set; }
        public Nullable<decimal> AwCostInOriginCurrency { get; set; }
        public Nullable<decimal> StrySegmentAllStoreyaRevenues { get; set; }
        public Nullable<int> StryShopifyTransactions { get; set; }
        public Nullable<decimal> StryShopifyTransactionRevenue { get; set; }
        public Nullable<int> DisplayedTransactions { get; set; }
        public Nullable<decimal> DisplayedRevenue { get; set; }
        public Nullable<decimal> RealCost { get; set; }
        public Nullable<int> AllShopifyTransactions { get; set; }
        public Nullable<decimal> AllShopifyTransactionRevenue { get; set; }
        public Nullable<int> StryShopifyAllStoreyaTransactions { get; set; }
        public Nullable<decimal> StryShopifyAllStoreyaTransactionRevenue { get; set; }
    }
}
