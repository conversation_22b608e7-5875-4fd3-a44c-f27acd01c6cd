//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TbAccountTrafficChannel
    {
        public int ID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> ChannelType { get; set; }
        public string AccountID { get; set; }
        public Nullable<System.DateTime> LastPaidAt { get; set; }
        public Nullable<System.DateTime> PurchasedAt { get; set; }
        public Nullable<decimal> PurchasedBudget { get; set; }
        public Nullable<decimal> OurFee { get; set; }
        public Nullable<int> BudgetStatus { get; set; }
        public int ShopID { get; set; }
        public Nullable<decimal> BudgetLeft { get; set; }
        public Nullable<int> IsActive { get; set; }
        public Nullable<long> FbPageID { get; set; }
        public Nullable<long> FbPixelD { get; set; }
        public Nullable<int> Status { get; set; }
        public string RevenueCurrency { get; set; }
        public Nullable<int> AgreeID { get; set; }
        public string CostCurrency { get; set; }
        public Nullable<int> UpgradeNotificationStatus { get; set; }
        public Nullable<System.DateTime> UpgradeNotificationStatusUpdateAt { get; set; }
        public string AccountName { get; set; }
        public string FBBusinessManagerId { get; set; }
        public string FBInstagramAccountId { get; set; }
        public Nullable<decimal> PreviousPeriodROI { get; set; }
        public Nullable<decimal> CurrentPeriodROI { get; set; }
        public Nullable<decimal> AllTimeROI { get; set; }
        public Nullable<System.DateTime> PreviousPeriodROIUpdatedAt { get; set; }
        public Nullable<decimal> Last7DaysROI { get; set; }
        public Nullable<decimal> Last30DaysROI { get; set; }
        public Nullable<decimal> EstimatedLast30DaysROI { get; set; }
        public Nullable<long> BingUetTagID { get; set; }
    }
}
