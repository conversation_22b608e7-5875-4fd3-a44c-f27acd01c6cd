//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TbAdCampaignsPerformance
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<System.DateTime> ReportDate { get; set; }
        public Nullable<int> Source { get; set; }
        public Nullable<long> CampaignID { get; set; }
        public Nullable<int> Impressions { get; set; }
        public Nullable<int> Clicks { get; set; }
        public Nullable<decimal> Cost { get; set; }
        public Nullable<int> Conversions { get; set; }
        public Nullable<decimal> ConversionsValue { get; set; }
        public Nullable<int> MiniConversions { get; set; }
        public Nullable<decimal> AwAveragePosition { get; set; }
        public Nullable<decimal> ConversionsDec { get; set; }
        public Nullable<decimal> CostInOriginCurrency { get; set; }
        public Nullable<decimal> ConversionsValueInOriginCurrency { get; set; }
        public Nullable<decimal> RealCost { get; set; }
    }
}
