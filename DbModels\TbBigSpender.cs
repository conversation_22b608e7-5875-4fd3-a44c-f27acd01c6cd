//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TbBigSpender
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<int> ActionCategory { get; set; }
        public Nullable<System.DateTime> PlannedAt { get; set; }
        public Nullable<int> ActionReason { get; set; }
        public string CommunicationWay { get; set; }
        public Nullable<int> AssignedTo { get; set; }
        public Nullable<decimal> PreviousPeriodROI { get; set; }
        public Nullable<decimal> CurrentPeriodROI { get; set; }
        public Nullable<decimal> AllTimeROI { get; set; }
        public Nullable<System.DateTime> PreviousPeriodROIUpdatedAt { get; set; }
        public Nullable<System.DateTime> AssignedAt { get; set; }
        public Nullable<int> Status { get; set; }
        public Nullable<decimal> CurrentAllSiteRevenue { get; set; }
        public Nullable<decimal> CurrentOurGaRevenue { get; set; }
        public Nullable<int> CurrentAllSiteTransactions { get; set; }
        public Nullable<int> CurrentOurGaTransactions { get; set; }
        public Nullable<int> CurrentAwClicks { get; set; }
        public Nullable<decimal> CurrentAwCost { get; set; }
        public Nullable<int> CurrentAwTransactions { get; set; }
        public Nullable<decimal> CurrentAwRevenue { get; set; }
        public Nullable<decimal> Last7DaysROI { get; set; }
        public Nullable<decimal> Last30DaysROI { get; set; }
        public Nullable<int> ReviewStatus { get; set; }
        public Nullable<int> LimitedByBudget { get; set; }
        public Nullable<decimal> YesterdayROI { get; set; }
    }
}
