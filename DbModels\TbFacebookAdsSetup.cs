//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TbFacebookAdsSetup
    {
        public int ID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public string ChangedBy { get; set; }
        public int ShopID { get; set; }
        public Nullable<bool> Active { get; set; }
        public Nullable<int> ProgressStatus { get; set; }
        public string AccountID { get; set; }
        public string CallCategory { get; set; }
        public string Url1 { get; set; }
        public string Country { get; set; }
        public string Age { get; set; }
        public string Gender { get; set; }
        public Nullable<bool> FreeShipping { get; set; }
        public Nullable<int> PromotionType { get; set; }
        public Nullable<long> PromotionAmount { get; set; }
        public string OtherPromotion { get; set; }
        public string CouponCode { get; set; }
        public string Description { get; set; }
        public string FbToken { get; set; }
        public string FbAccountID { get; set; }
        public string AccountName { get; set; }
        public Nullable<int> CampaignsCount { get; set; }
        public Nullable<int> AdSetsCount { get; set; }
        public Nullable<int> AdsCount { get; set; }
        public Nullable<decimal> TotalAccountBudget { get; set; }
        public Nullable<decimal> TotalAccountWastedBudget { get; set; }
        public Nullable<int> AccountScore { get; set; }
        public string AgeJson { get; set; }
        public string GenderJson { get; set; }
        public Nullable<System.DateTime> FromDate { get; set; }
        public Nullable<System.DateTime> ToDate { get; set; }
        public string MissingOpportunitiesJson { get; set; }
        public Nullable<int> LookalikePurchasesDays { get; set; }
        public Nullable<int> LookalikeAddToCartDays { get; set; }
        public Nullable<int> PurchasesLastDays { get; set; }
        public Nullable<int> ExcludingLastDays { get; set; }
        public Nullable<int> AddedToCartDays { get; set; }
        public string PageToken { get; set; }
        public string CampaignText { get; set; }
        public string CampaignFrameImage { get; set; }
        public string CampaignText1 { get; set; }
        public string CampaignHeadLine { get; set; }
        public string FbeToken { get; set; }
        public Nullable<System.DateTime> LastCheckedAt { get; set; }
        public string Errors { get; set; }
        public string ScheduleAds { get; set; }
    }
}
