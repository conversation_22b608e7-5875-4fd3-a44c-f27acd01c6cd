//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TbInternalTask
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<int> Category { get; set; }
        public Nullable<int> TaskType { get; set; }
        public string TaskDescription { get; set; }
        public string Details { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> Status { get; set; }
        public Nullable<System.DateTime> HideUntil { get; set; }
        public Nullable<int> AssignedTo { get; set; }
        public Nullable<int> AttributeID { get; set; }
        public string NotHelpfulRemarks { get; set; }
        public string NotHelpfulType { get; set; }
        public Nullable<long> BatchId { get; set; }
        public Nullable<int> Priority { get; set; }
    }
}
