//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TbReports2
    {
        public int ID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<System.DateTime> ReportDate { get; set; }
        public Nullable<int> ChannelType { get; set; }
        public Nullable<int> GaOurSessions { get; set; }
        public Nullable<int> GaOurTransactions { get; set; }
        public Nullable<decimal> GaOurRevenue { get; set; }
        public Nullable<int> ChannelImpressions { get; set; }
        public Nullable<int> ChannelClicks { get; set; }
        public Nullable<decimal> ChannelCost { get; set; }
        public Nullable<int> ChannelTransactions { get; set; }
        public Nullable<decimal> ChannelRevenue { get; set; }
        public Nullable<int> StrySegmentSessions { get; set; }
        public Nullable<int> StrySegmentTransactions { get; set; }
        public Nullable<decimal> StrySegmentRevenue { get; set; }
        public Nullable<int> PreserveStatus { get; set; }
        public Nullable<decimal> ChannelCostInOriginCurrency { get; set; }
        public Nullable<decimal> ChannelRevenueInOriginCurrency { get; set; }
        public Nullable<decimal> RealCost { get; set; }
    }
}
