//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TbUpgradeCandidate
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<System.DateTime> IgnoreAlertCheckUntill { get; set; }
        public Nullable<int> AlertStatus { get; set; }
        public Nullable<int> PerformanceDataFields { get; set; }
        public Nullable<int> OurTransactions30 { get; set; }
        public Nullable<decimal> OurRevenue30 { get; set; }
        public Nullable<int> AwDsaCampaignStatus { get; set; }
        public Nullable<decimal> SiteRevenue30 { get; set; }
        public Nullable<int> SiteTransactions30 { get; set; }
        public Nullable<int> SiteSessions30 { get; set; }
        public string AwSearchImpressionsShare { get; set; }
        public Nullable<decimal> OurRevenueAll { get; set; }
        public Nullable<int> OurTransactionsAll { get; set; }
        public Nullable<int> OurSessionsAll { get; set; }
        public Nullable<int> StrySegmentTransactions30 { get; set; }
        public Nullable<int> StrySegmentSessions30 { get; set; }
        public Nullable<decimal> StrySegmentRevenue30 { get; set; }
    }
}
