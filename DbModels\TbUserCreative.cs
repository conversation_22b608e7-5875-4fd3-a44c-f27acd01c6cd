//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TbUserCreative
    {
        public int ID { get; set; }
        public Nullable<int> TbID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public string DsaAd_1_Description { get; set; }
        public Nullable<int> DsaAd_1_IsActive { get; set; }
        public string DsaAd_2_Description { get; set; }
        public Nullable<int> DsaAd_2_IsActive { get; set; }
        public string DsaAd_3_Description { get; set; }
        public Nullable<int> DsaAd_3_IsActive { get; set; }
        public string Callouts { get; set; }
        public string SearchAd_1_Headline_1 { get; set; }
        public string SearchAd_1_Headline_2 { get; set; }
        public string SearchAd_1_Description { get; set; }
        public Nullable<int> SearchAd_1_IsActive { get; set; }
        public string SearchAd_2_Headline_1 { get; set; }
        public string SearchAd_2_Headline_2 { get; set; }
        public string SearchAd_2_Description { get; set; }
        public Nullable<int> SearchAd_2_IsActive { get; set; }
        public string SearchAd_3_Headline_1 { get; set; }
        public string SearchAd_3_Headline_2 { get; set; }
        public string SearchAd_3_Description { get; set; }
        public Nullable<int> SearchAd_3_IsActive { get; set; }
        public string Keywords { get; set; }
        public string Keywords_SearchAd_1 { get; set; }
        public string Keywords_SearchAd_2 { get; set; }
        public string Keywords_SearchAd_3 { get; set; }
        public Nullable<System.DateTime> UpdatedByUserAt { get; set; }
        public Nullable<System.DateTime> UpdatedAtAdwordsAt { get; set; }
        public string SearchAd_1_URL { get; set; }
        public string SearchAd_2_URL { get; set; }
        public string SearchAd_3_URL { get; set; }
        public Nullable<System.DateTime> UpdatedOnAdwordsAt { get; set; }
        public string SearchAd_Headline_10 { get; set; }
        public string SearchAd_Headline_7 { get; set; }
        public string SearchAd_Headline_8 { get; set; }
        public string SearchAd_Headline_9 { get; set; }
        public string SearchAd_4_Description { get; set; }
        public string DsaAd_1_Description2 { get; set; }
        public string DsaAd_2_Description2 { get; set; }
        public string DsaAd_3_Description2 { get; set; }
    }
}
