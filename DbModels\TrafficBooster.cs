//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TrafficBooster
    {
        public int ID { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<int> TrafficCategoryID { get; set; }
        public Nullable<long> PurchasedAmount { get; set; }
        public Nullable<System.DateTime> PurchasedAt { get; set; }
        public Nullable<long> RecievedImpressions { get; set; }
        public Nullable<byte> Status { get; set; }
        public Nullable<System.DateTime> LastImpressionDatetime { get; set; }
        public Nullable<System.DateTime> DailyCapStartedAt { get; set; }
        public Nullable<long> DailyCap { get; set; }
        public Nullable<long> DailyCapRecieved { get; set; }
        public Nullable<byte> DailyCapStatus { get; set; }
        public string Url1 { get; set; }
        public string Url2 { get; set; }
        public string Url3 { get; set; }
        public string Url4 { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<System.DateTime> UpdatedBySystemAt { get; set; }
        public Nullable<int> TrafficMethod { get; set; }
        public string Country { get; set; }
        public string Age { get; set; }
        public string Gender { get; set; }
        public Nullable<int> BannerStatus { get; set; }
        public Nullable<int> AdwordsUploadStatus { get; set; }
        public Nullable<int> HasGAConnectedAccount { get; set; }
        public Nullable<System.DateTime> CancelledAt { get; set; }
        public Nullable<int> GAConnectedProfileID { get; set; }
        public string WebPropertyIDs { get; set; }
        public Nullable<int> SalesDriven { get; set; }
        public string TotalRevenues { get; set; }
        public Nullable<System.DateTime> LastPaymentDate { get; set; }
        public Nullable<int> AdWordsCouponApplied { get; set; }
        public string AdWordsAccount { get; set; }
        public string CallBussnessName { get; set; }
        public string CallCountryCode { get; set; }
        public string CallPhone { get; set; }
        public string CallDisplayUrl { get; set; }
        public string CallDescription1 { get; set; }
        public string CallDescription2 { get; set; }
        public string CallZipCode { get; set; }
        public Nullable<int> CallRadius { get; set; }
        public Nullable<int> CallRadiusUnits { get; set; }
        public string CallKeywords { get; set; }
        public string CallCategory { get; set; }
        public Nullable<int> PrimaryTrafficMethod { get; set; }
        public Nullable<int> CallCampaignStatus { get; set; }
        public Nullable<int> TrafficBoosterVersion { get; set; }
        public string AdWordsPixel { get; set; }
        public Nullable<int> AdWordsPixelInstalled { get; set; }
        public Nullable<int> PaymentSystem { get; set; }
        public Nullable<int> SalesDrivenSource { get; set; }
        public Nullable<int> TotalRevenuesSource { get; set; }
        public string AdKeywords { get; set; }
        public string LastPaymentEventType { get; set; }
        public Nullable<int> BudjetStatus { get; set; }
        public Nullable<int> AdCopyUseCustom { get; set; }
        public string AdCopyDescription1 { get; set; }
        public string AdCopyDescription2 { get; set; }
        public string SearchAdHeadline1 { get; set; }
        public string SearchAdHeadline2 { get; set; }
        public Nullable<int> IsServiceProvider { get; set; }
        public string SearchAdDisplayUrl { get; set; }
        public string SearchAdDescription { get; set; }
        public Nullable<int> GaConnectionStatus { get; set; }
        public Nullable<int> UpgradeNotificationStatus { get; set; }
        public Nullable<long> OverwritePurchsedAmount { get; set; }
        public Nullable<decimal> AccountBalance { get; set; }
        public Nullable<int> AdServerAdditionalVisits { get; set; }
        public Nullable<System.DateTime> AdServerAdditionalVisitsUntill { get; set; }
        public Nullable<System.DateTime> FirstUpgradedAt { get; set; }
        public Nullable<long> MerchantCenterAccountID { get; set; }
        public Nullable<System.DateTime> UpgradeNotificationStatusUpdateAt { get; set; }
        public Nullable<decimal> DesiredROAS { get; set; }
        public Nullable<int> BudgetDurationInDays { get; set; }
        public string MajorAccountChanges { get; set; }
        public Nullable<int> DashboardType { get; set; }
        public string MerchantCenterSharedWith { get; set; }
        public string DashboardCurrency { get; set; }
        public Nullable<long> AdWordsOcId { get; set; }
        public Nullable<System.DateTime> ReportedToFacebookAt { get; set; }
        public Nullable<int> DashboardShowConversionRate { get; set; }
        public Nullable<int> DashboardShowEstimatedROI { get; set; }
        public Nullable<int> AppStatus { get; set; }
        public string InvoiceAdditionalInfo { get; set; }
        public Nullable<int> AutoChargeOnDesiredRoas { get; set; }
        public Nullable<int> AutoChargeAfterMinimumDays { get; set; }
        public Nullable<decimal> LastPaymentRemainingBudget { get; set; }
        public Nullable<int> ShowLastPaymentRemainingBudget { get; set; }
        public Nullable<int> AdWordsRemarketingCodeStatus { get; set; }
    }
}
