//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class User
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public User()
        {
            this.Shops = new HashSet<Shop>();
        }
    
        public int ID { get; set; }
        public string Name { get; set; }
        public Nullable<long> FbProfileID { get; set; }
        public string Email { get; set; }
        public Nullable<int> RegistrationState { get; set; }
        public string BusinessName { get; set; }
        public string BusinessDescription { get; set; }
        public string Website { get; set; }
        public string ContactEmail { get; set; }
        public string Phone { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string ZipCode { get; set; }
        public Nullable<System.DateTime> InsertedAt { get; set; }
        public Nullable<int> AgencyID { get; set; }
        public Nullable<int> RefererID { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<byte> UserType { get; set; }
        public string Password { get; set; }
        public Nullable<byte> OriginMarketplace { get; set; }
        public string Email2 { get; set; }
        public Nullable<int> SignupIP { get; set; }
        public string SignupCountry { get; set; }
        public string SignupState { get; set; }
        public string Locale { get; set; }
        public Nullable<int> RevenueRank { get; set; }
        public Nullable<System.DateTime> RevenueRankReportedAt { get; set; }
        public string BillingCountry { get; set; }
        public Nullable<System.DateTime> LastVisitedBoAt { get; set; }
        public Nullable<int> RelationStatus { get; set; }
        public string MonthlyRevenue { get; set; }
        public Nullable<int> BoRole { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Shop> Shops { get; set; }
    }
}
