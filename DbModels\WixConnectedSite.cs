//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Storeya.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class WixConnectedSite
    {
        public int ID { get; set; }
        public string WixInstance { get; set; }
        public string WixSiteOwnerID { get; set; }
        public string WixUrl { get; set; }
        public string WixPlanID { get; set; }
        public System.DateTime InsertedAt { get; set; }
        public Nullable<int> ShopID { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public Nullable<int> Status { get; set; }
        public string ipAndPort { get; set; }
        public string Country { get; set; }
        public string WixSiteId { get; set; }
        public string ApiToken { get; set; }
        public string ApiRefreshToken { get; set; }
        public Nullable<int> OrdersCount { get; set; }
        public Nullable<int> ProductsCount { get; set; }
        public string ApiInstance { get; set; }
    }
}
