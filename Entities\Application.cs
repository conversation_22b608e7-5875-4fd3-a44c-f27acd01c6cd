﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Models;

namespace Storeya.Core.Entities
{
    public enum Shopify_StoreyaApp
    {
        FacebookShop = 0,
        CouponPop = 1,
        ExitPop = 2,
        Rff = 3,
        TrafficBooster = 4,
        BannerLite = 5,
        FacebookAdsGrader = 6,
        BenchmarkHero = 7,
        GrowthHero = 8,
        ProductDescriber = 9,
        TrustBadges = 10,
        AITryOnModel = 11,
        WhatsApp = 12,
    }

    public enum AppEmbeddedType
    { 
        Native = 0,
        Embedded =1,
        Both = 2
    }
    public class StoreyaAppHelper
    {
        public static Shopify_StoreyaApp? ConvertAppTypeToShopifyAppType(AppTypes type)
        {
            Shopify_StoreyaApp shopify_Storeya = (Shopify_StoreyaApp)GetStoreyaAppType((int)type);
            return shopify_Storeya;

            //Shopify_StoreyaApp? shopify_Storeya = null;
            //switch (type)
            //{
            //    case Storeya.Core.Models.AppTypes.FacebookShop:
            //        shopify_Storeya = Shopify_StoreyaApp.FacebookShop;
            //        break;
            //    case Storeya.Core.Models.AppTypes.CouponPop:
            //        shopify_Storeya = Shopify_StoreyaApp.CouponPop;
            //        break;
            //    case Storeya.Core.Models.AppTypes.ExitPop:
            //        shopify_Storeya = Shopify_StoreyaApp.ExitPop;
            //        break;
            //    case Storeya.Core.Models.AppTypes.RFF:
            //        shopify_Storeya = Shopify_StoreyaApp.Rff;
            //        break;
            //    case Storeya.Core.Models.AppTypes.TrafficBooster:
            //        shopify_Storeya = Shopify_StoreyaApp.TrafficBooster;
            //        break;
            //    case Storeya.Core.Models.AppTypes.FacebookAdsGrader:
            //        shopify_Storeya = Shopify_StoreyaApp.FacebookAdsGrader;
            //        break;
            //    case Storeya.Core.Models.AppTypes.BenchmarkHero:
            //        shopify_Storeya = Shopify_StoreyaApp.BenchmarkHero;
            //        break;
            //    case Storeya.Core.Models.AppTypes.GrowthHero:
            //        shopify_Storeya = Shopify_StoreyaApp.GrowthHero;
            //        break;
            //    case Storeya.Core.Models.AppTypes.ProductDescriber:
            //        shopify_Storeya = Shopify_StoreyaApp.ProductDescriber;
            //        break;
            //    case Storeya.Core.Models.AppTypes.TrustBadges:
            //        shopify_Storeya = Shopify_StoreyaApp.TrustBadges;
            //        break;
            //    case Storeya.Core.Models.AppTypes.AISeoProductOptimizer:
            //        shopify_Storeya = Shopify_StoreyaApp.AISeoProductOptimizer;
            //        break;
            //    case Storeya.Core.Models.AppTypes.WhatsApp:
            //        shopify_Storeya = Shopify_StoreyaApp.WhatsApp;
            //        break;
            //    default:
            //        shopify_Storeya = null;
            //        break;
            //}
            //return shopify_Storeya;
        }
        public static string GetAppName(int appType)
        {
            if (appType == (int)Shopify_StoreyaApp.CouponPop)
            {
                return "Coupon Pop";
            }
            else if (appType == (int)Shopify_StoreyaApp.ExitPop)
            {
                return "Exit Pop";
            }
            else if (appType == (int)Shopify_StoreyaApp.Rff)
            {
                return "Refer a Friend";
            }
            else if (appType == (int)Shopify_StoreyaApp.TrafficBooster)
            {
                return "Traffic Booster";
            }
            else if (appType == (int)Shopify_StoreyaApp.BannerLite)
            {
                return "Bunner Lite";
            }
            else if (appType == (int)Shopify_StoreyaApp.FacebookAdsGrader)
            {
                return "Facebook Ads Grader";
            }
            else if (appType == (int)Shopify_StoreyaApp.BenchmarkHero)
            {
                return "Benchmark Hero - Free Audit Tool";
            }
            else if (appType == (int)Shopify_StoreyaApp.GrowthHero)
            {
                return "Growth Hero";
            }
            else if (appType == (int)Shopify_StoreyaApp.ProductDescriber)
            {
                return "Product Describer";
            }
            else if (appType == (int)Shopify_StoreyaApp.FacebookShop)
            {
                return "Facebook Shop";
            }
            else if (appType == (int)Shopify_StoreyaApp.TrustBadges)
            {
                return "Trust Badges";
            }
            else if (appType == (int)Shopify_StoreyaApp.AITryOnModel)
            {
                return "AI Seo Product Optimizer";
            }
            else if (appType == (int)Shopify_StoreyaApp.WhatsApp)
            {
                return "Whatsapp Sales";
            }
            return "";
        }

        /// Returns the Shopify app type based on the provided app type
        public static int GetStoreyaAppType(int appType)
        {
            
            if (appType == (int)AppTypes.CouponPop)
            {
                return (int)Shopify_StoreyaApp.CouponPop;
            }
            else if (appType == (int)AppTypes.ExitPop)
            {
                return (int)Shopify_StoreyaApp.ExitPop;
            }
            else if (appType == (int)AppTypes.RFF)
            {
                return (int)Shopify_StoreyaApp.Rff;
            }
            else if (appType == (int)AppTypes.TrafficBooster)
            {
                return (int)Shopify_StoreyaApp.TrafficBooster;
            }
            else if (appType == (int)AppTypes.PoweredBanner)
            {
                return (int)Shopify_StoreyaApp.BannerLite;
            }
            else if (appType == (int)AppTypes.FacebookAdsGrader)
            {
                return (int)Shopify_StoreyaApp.FacebookAdsGrader;
            }
            else if (appType == (int)AppTypes.BenchmarkHero)
            {
                return (int)Shopify_StoreyaApp.BenchmarkHero;
            }
            else if (appType == (int)AppTypes.GrowthHero)
            {
                return (int)Shopify_StoreyaApp.GrowthHero;
            }
            else if (appType == (int)AppTypes.ProductDescriber)
            {
                return (int)Shopify_StoreyaApp.ProductDescriber;
            }
            else if (appType == (int)AppTypes.TrustBadges)
            {
                return (int)Shopify_StoreyaApp.TrustBadges;
            }
            else if (appType == (int)AppTypes.AITryOnModel)
            {
                return (int)Shopify_StoreyaApp.AITryOnModel;
            }
            else if (appType == (int)AppTypes.WhatsApp)
            {
                return (int)Shopify_StoreyaApp.WhatsApp;
            }
            else
            {
                return (int)Shopify_StoreyaApp.FacebookShop;
            }
        }

        public static int GetAppType(int shopifyAppType)
        {
            if (shopifyAppType == (int)Shopify_StoreyaApp.CouponPop)
            {
                return (int)AppTypes.CouponPop;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.ExitPop)
            {
                return (int)AppTypes.ExitPop;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.Rff)
            {
                return (int)AppTypes.RFF;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.TrafficBooster)
            {
                return (int)AppTypes.TrafficBooster;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.BannerLite)
            {
                return (int)AppTypes.PoweredBanner;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.FacebookAdsGrader)
            {
                return (int)AppTypes.FacebookAdsGrader;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.BenchmarkHero)
            {
                return (int)AppTypes.BenchmarkHero;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.GrowthHero)
            {
                return (int)AppTypes.GrowthHero;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.ProductDescriber)
            {
                return (int)AppTypes.ProductDescriber;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.TrustBadges)
            {
                return (int)AppTypes.TrustBadges;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.AITryOnModel)
            {
                return (int)AppTypes.AITryOnModel;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.WhatsApp)
            {
                return (int)AppTypes.WhatsApp;
            }
            else if (shopifyAppType == (int)Shopify_StoreyaApp.FacebookShop)
            {
                return (int)AppTypes.FacebookShop;
            }
            else
            {
                throw new Exception("Wrong app type - " + shopifyAppType);
            }
        }
    }
}
