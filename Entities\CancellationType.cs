﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Entities
{
    public enum CancellationType
    {
        None = 0,
        ReImport = 1,
        ImportFlow = 2,
        Marketing = 3,
        Design = 4,
        Checkout = 5,
        Other = 6,

        TooDifficult = 10,
        TooExpensive = 11,
        FeaturesMissing = 12,

        WantToPreventAutomaticRenewal = 20,
        CouldNotTrackVisits = 21,
        CouldNotTrackSales = 22,
        DidNotGetEnoughVisits = 23,
        DidNotGetEnoughSales = 24,

        BH_Feedback_Text = 31,
        BH_Feedback_Short = 30,
        WIX_Feedback_Text = 32,
        Dashboard_Heart_Feedback = 33,
        Dashboard_Review_Feedback = 34,

        //WIX_Feedback_Text = 41
        Product_Description_Wizard = 35,
    }


    public enum WebAppCancellationType
    {
        TooDifficult = 10,
        TooExpensive = 11,
        FeaturesMissing = 12,
        Other = 13
    }
}
