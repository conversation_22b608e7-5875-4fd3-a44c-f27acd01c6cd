﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Storeya.Core.Helpers;
using Storeya.Core.Models.BackgroundTasks;

namespace Storeya.Core
{
    public enum CatalogSourcePlatforms
    {
        None = 0,
        ShopifyApi = 3,
        WP_WooCommerce = 19,
        Wix = 36,
        MagentoApi = 2,
        Shift4Shop = 38,
        BigCommerce = 32,

        Csv = 1,   //MagentoCsv


        PrestaShop = 4,
        eBay = 5,
        WP_e_Commerce = 6,
        Amazon = 7,
        GoogleBase = 8,
        CoreCommerce = 9,
        OpenCart = 10,
        GoogleBaseUpload = 11,
        DataFeedr = 12,
        BrilliantRetail = 13,
        CubeCart = 14,
        VPASP = 15,
        CsCart = 16,
        Etsy = 17,
        AspDotNetStorefront = 18,

        nopCommerce = 20,
        osCommerce = 21,
        ZenCart = 22,
        TradeTubeR = 23,
        VirtueMart = 24,
        SHOP_COM = 25,
        StandAlone = 26,
        Cafepress = 27,
        Zazzle = 28,
        TheCraftStar = 29,
        Tictail = 30,
        SpreeCommerce = 31,

        <PERSON>cwid = 33,
        EasyStore = 34,
        Reverb = 35,

        Magento2 = 37,

        ePages = 39,
        Colombian = 40,
        Jumpseller = 41,
        SquareUp = 42,
        ZAP = 43,
        Neto = 44,
        eShopCoIL = 45,
        ShopWired = 46
    }

    public static class PlatformHandler
    {
        public static Dictionary<int, string> ActualCatalogPlatformDictionary()
        {
            Dictionary<int, string> allowedPlatforms = new Dictionary<int, string>();
            allowedPlatforms[(int)CatalogSourcePlatforms.None] = CatalogSourcePlatforms.None.ToString();
            allowedPlatforms[(int)CatalogSourcePlatforms.ShopifyApi] = CatalogSourcePlatforms.ShopifyApi.ToString();
            allowedPlatforms[(int)CatalogSourcePlatforms.WP_WooCommerce] = CatalogSourcePlatforms.WP_WooCommerce.ToString();
            allowedPlatforms[(int)CatalogSourcePlatforms.Wix] = CatalogSourcePlatforms.Wix.ToString();
            allowedPlatforms[(int)CatalogSourcePlatforms.MagentoApi] = CatalogSourcePlatforms.MagentoApi.ToString();
            allowedPlatforms[(int)CatalogSourcePlatforms.Shift4Shop] = CatalogSourcePlatforms.Shift4Shop.ToString();
            allowedPlatforms[(int)CatalogSourcePlatforms.BigCommerce] = CatalogSourcePlatforms.BigCommerce.ToString();
            return allowedPlatforms;
        }
        public static string GetPlatformName(CatalogSourcePlatforms platform)
        {
            string platformName = null;
            switch (platform)
            {
                case CatalogSourcePlatforms.None:
                case CatalogSourcePlatforms.GoogleBase:
                case CatalogSourcePlatforms.GoogleBaseUpload:
                case CatalogSourcePlatforms.StandAlone:
                    break;

                case CatalogSourcePlatforms.Csv:
                case CatalogSourcePlatforms.MagentoApi:
                case CatalogSourcePlatforms.Magento2:
                    platformName = "Magento";
                    break;

                case CatalogSourcePlatforms.ShopifyApi:
                    platformName = "Shopify";
                    break;

                case CatalogSourcePlatforms.WP_e_Commerce:
                    platformName = "WP eCommerce";
                    break;

                case CatalogSourcePlatforms.WP_WooCommerce:
                    platformName = "WooCommerce";
                    break;

                case CatalogSourcePlatforms.SHOP_COM:
                    platformName = "SHOP.COM";
                    break;


                default:
                    platformName = platform.ToString();
                    break;
            }

            return platformName;
        }


        public static string GetPlatformName(int? platformID)
        {
            if (platformID != null)
                return ((CatalogSourcePlatforms)platformID).ToString();

            return "";
        }

        public static bool IsExtendedGoogleBasePlatform(CatalogSourcePlatforms platform)
        {
            if (platform == CatalogSourcePlatforms.CoreCommerce
                || platform == CatalogSourcePlatforms.OpenCart
                || platform == CatalogSourcePlatforms.WP_e_Commerce
                || platform == CatalogSourcePlatforms.WP_WooCommerce
                || platform == CatalogSourcePlatforms.GoogleBase
                || platform == CatalogSourcePlatforms.GoogleBaseUpload
                || platform == CatalogSourcePlatforms.DataFeedr
                || platform == CatalogSourcePlatforms.CubeCart
                || platform == CatalogSourcePlatforms.BrilliantRetail
                || platform == CatalogSourcePlatforms.VPASP
                || platform == CatalogSourcePlatforms.CsCart
                || platform == CatalogSourcePlatforms.AspDotNetStorefront
                || platform == CatalogSourcePlatforms.nopCommerce
                || platform == CatalogSourcePlatforms.osCommerce
                || platform == CatalogSourcePlatforms.ZenCart
                || platform == CatalogSourcePlatforms.VirtueMart
                || platform == CatalogSourcePlatforms.TheCraftStar
                )
                return true;
            else return false;
        }

        public static List<Platform> GetPlatforms()
        {
            List<Platform> list = new List<Platform>();

            list.Add(new Platform() { ID = 0, Name = "- Select Platform -" });

            foreach (CatalogSourcePlatforms val in Enum.GetValues(typeof(CatalogSourcePlatforms)))
            {
                list.Add(new Platform() { ID = (int)val, Name = Enum.GetName(typeof(CatalogSourcePlatforms), val) + "(" + ((int)val).ToString() + ")" });
            }

            return list;
        }

        public static List<Platform> GetPlatformsForReferAFriend()
        {
            List<Platform> list = new List<Platform>();

            list.Add(new Platform() { ID = 0, Name = "- Select Platform -" });
            list.Add(new Platform() { ID = 100, Name = "Any site" });
            list.Add(new Platform() { ID = 34, Name = "EasyStore" });
            list.Add(new Platform() { ID = 2, Name = "Magento" });
            list.Add(new Platform() { ID = 3, Name = "Shopify" });
            list.Add(new Platform() { ID = 4, Name = "PrestaShop" });
            list.Add(new Platform() { ID = 10, Name = "OpenCart" });
            list.Add(new Platform() { ID = 19, Name = "WooCommerce" });


            return list;
        }

        public static string GetPlatformnameForReferAFriend(int platform = 0)
        {
            string platformName = "Any site";
            switch (platform)
            {
                case 2:
                    platformName = "Magento";
                    break;

                case 3:
                    platformName = "Shopify";
                    break;

                case 10:
                    platformName = "OpenCart";
                    break;

                case 19:
                    platformName = "WooCommerce";
                    break;

                default:
                    break;
            }
            return platformName;
        }

        public static BackgroundTasksTypes GetBackGroundTypeByPlatform(int platform)
        {
            switch ((CatalogSourcePlatforms)platform)
            {
                case CatalogSourcePlatforms.Amazon:
                    return BackgroundTasksTypes.Sync_Amazon;

                case CatalogSourcePlatforms.Cafepress:
                    return BackgroundTasksTypes.Sync_Cafepress;

                case CatalogSourcePlatforms.Csv:
                    return BackgroundTasksTypes.Sync_Csv;

                case CatalogSourcePlatforms.eBay:
                    return BackgroundTasksTypes.Sync_eBay;

                case CatalogSourcePlatforms.Etsy:
                    return BackgroundTasksTypes.Sync_Etsy;

                case CatalogSourcePlatforms.MagentoApi:
                    return BackgroundTasksTypes.Sync_MagentoApi;

                case CatalogSourcePlatforms.ShopifyApi:
                    return BackgroundTasksTypes.Sync_Shopify;

                case CatalogSourcePlatforms.PrestaShop:
                    return BackgroundTasksTypes.Sync_PrestaShop;

                case CatalogSourcePlatforms.CoreCommerce:
                case CatalogSourcePlatforms.DataFeedr:
                case CatalogSourcePlatforms.GoogleBase:
                case CatalogSourcePlatforms.GoogleBaseUpload:
                case CatalogSourcePlatforms.OpenCart:
                case CatalogSourcePlatforms.osCommerce:
                case CatalogSourcePlatforms.TheCraftStar:
                case CatalogSourcePlatforms.VirtueMart:
                case CatalogSourcePlatforms.WP_e_Commerce:
                case CatalogSourcePlatforms.WP_WooCommerce:
                case CatalogSourcePlatforms.ZenCart:
                case CatalogSourcePlatforms.BrilliantRetail:
                case CatalogSourcePlatforms.CubeCart:
                case CatalogSourcePlatforms.VPASP:
                case CatalogSourcePlatforms.CsCart:
                case CatalogSourcePlatforms.AspDotNetStorefront:
                case CatalogSourcePlatforms.nopCommerce:
                    return BackgroundTasksTypes.Sync_ExtendedGoogleBase;


                case CatalogSourcePlatforms.SHOP_COM:
                    return BackgroundTasksTypes.Sync_ShopCOM;

                case CatalogSourcePlatforms.Zazzle:
                    return BackgroundTasksTypes.Sync_Zazzle;

                case CatalogSourcePlatforms.Tictail:
                    return BackgroundTasksTypes.Sync_Tictail;

                case CatalogSourcePlatforms.SpreeCommerce:
                    return BackgroundTasksTypes.Sync_SpreeCommerce;

                case CatalogSourcePlatforms.Magento2:
                    return BackgroundTasksTypes.Sync_Magento2;
                default:
                    return BackgroundTasksTypes.None;
            }
        }


        public static bool IsMarketplace(CatalogSourcePlatforms platform)
        {
            switch (platform)
            {
                case CatalogSourcePlatforms.Amazon:
                case CatalogSourcePlatforms.Cafepress:
                case CatalogSourcePlatforms.DataFeedr:
                case CatalogSourcePlatforms.eBay:
                case CatalogSourcePlatforms.Etsy:
                case CatalogSourcePlatforms.SHOP_COM:
                case CatalogSourcePlatforms.TheCraftStar:
                case CatalogSourcePlatforms.Tictail:
                case CatalogSourcePlatforms.TradeTubeR:
                case CatalogSourcePlatforms.Zazzle:
                    return true;
                default:
                    return false;
            }
        }


        public static System.Collections.IEnumerable GetPlatformsForExitPop()
        {
            List<Platform> list = new List<Platform>();

            list.Add(new Platform() { ID = 0, Name = "Any site" });
            list.Add(new Platform() { ID = 32, Name = "Bigcommerce" });
            list.Add(new Platform() { ID = 34, Name = "EasyStore" });
            list.Add(new Platform() { ID = 2, Name = "Magento" });
            list.Add(new Platform() { ID = 10, Name = "OpenCart" });
            list.Add(new Platform() { ID = 21, Name = "osCommerce" });
            list.Add(new Platform() { ID = 4, Name = "Prestashop" });
            list.Add(new Platform() { ID = 3, Name = "Shopify" });
            list.Add(new Platform() { ID = 24, Name = "VirtueMart" });
            list.Add(new Platform() { ID = 19, Name = "WooCommerce / Wordpress" });
            list.Add(new Platform() { ID = 22, Name = "ZenCart" });



            return list;
        }

        public static System.Collections.IEnumerable GetPlatformsForCouponPop()
        {
            List<Platform> list = new List<Platform>();

            list.Add(new Platform() { ID = 0, Name = "Any site" });
            list.Add(new Platform() { ID = 32, Name = "Bigcommerce" });
            list.Add(new Platform() { ID = 34, Name = "EasyStore" });
            list.Add(new Platform() { ID = 2, Name = "Magento" });
            list.Add(new Platform() { ID = 10, Name = "OpenCart" });
            list.Add(new Platform() { ID = 21, Name = "osCommerce" });
            list.Add(new Platform() { ID = 4, Name = "Prestashop" });
            list.Add(new Platform() { ID = 3, Name = "Shopify" });
            list.Add(new Platform() { ID = 24, Name = "VirtueMart" });
            list.Add(new Platform() { ID = 19, Name = "WooCommerce / Wordpress" });
            list.Add(new Platform() { ID = 22, Name = "ZenCart" });



            return list;
        }

        public static int GetPlatform(Shop shop)
        {
            if (shop.CatalogSourcePlatform.HasValue)
            {
                if (shop.CatalogSourcePlatform.Value == (int)CatalogSourcePlatforms.WP_e_Commerce
                        || shop.CatalogSourcePlatform.Value == (int)CatalogSourcePlatforms.WP_WooCommerce)
                {
                    return (int)CatalogSourcePlatforms.WP_WooCommerce;
                }

                return shop.CatalogSourcePlatform.Value;
            }
            return 0;
        }

        public static int? GetCatalogSourcePlatformByUserOriginMarketplace(int userOriginMarketplace)
        {
            int? catalogSourcePlatform = null;

            OriginMarketplaces marketPlace = (OriginMarketplaces)userOriginMarketplace;
            switch (marketPlace)
            {
                case OriginMarketplaces.None:
                    break;
                case OriginMarketplaces.Lexity:
                    break;
                case OriginMarketplaces.Wix:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.Wix;
                    break;
                case OriginMarketplaces.Shopify:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.ShopifyApi;
                    break;
                case OriginMarketplaces.BigCommerce:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.BigCommerce;
                    break;
                case OriginMarketplaces.Storenvy:
                    break;
                case OriginMarketplaces.Tictail:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.Tictail;
                    break;
                case OriginMarketplaces.Prestashop:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.PrestaShop;
                    break;
                case OriginMarketplaces.WordPress:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.WP_WooCommerce;
                    break;
                case OriginMarketplaces.OpenCart:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.OpenCart;
                    break;
                case OriginMarketplaces.Magento:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.MagentoApi;
                    break;
                case OriginMarketplaces.Weebly:
                    break;
                case OriginMarketplaces.Ecwid:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.Ecwid;
                    break;
                case OriginMarketplaces.ePages:
                    catalogSourcePlatform = (int)CatalogSourcePlatforms.ePages;
                    break;
                case OriginMarketplaces.Yext:
                    break;
                default:
                    break;
            }

            return catalogSourcePlatform;
        }
    }

    public class Platform
    {
        public int ID { get; set; }
        public string Name { get; set; }
    }

}