﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Entities
{
    internal class DashboardNotifications
    {
    }
    public enum DashboardNotificationStatus
    {
        NotActive = 0,
        Active = 1,
        DisabledByUser = 2,
        Paid = 3,
        Expired = 4,
        Upgraded = 5,
    }
    public enum DashboardNotificationButtonType
    {
        NotActive = 0,
        Active = 1,
    }
    public enum DashboardNotificationAlertType
    {
        NoType = 0,
        Upgrade = 1,
        HolidayAlert = 2,
        FailedCharge = 3,
        UpgradeWithoutButton = 4,
        WarningWithoutDefaultButton = 5,
        NotificationWithoutDefaultButton = 6,
    }
    public enum DashboardNotificationAlertSubType
    {
        NoType = 0,
        GoodTbAWToFB = 1,
    }
    public class DashboardAlertDesign
    {
        public string ViewAlert { get; private set; }
        public string AlertType { get; private set; }
        public string Icon { get; private set; }
        public string Style { get; private set; }
        public string Color { get; private set; }
        public string AlertID { get; private set; }
        public DashboardAlertDesign(int? alertType, int? itemID = null)
        {
            Style = "";
            ViewAlert = "close-alert";
            AlertType = "alert";
            Icon = "heart-icon";
            AlertID = "id='0'";
            Color = "#1284C3";
            switch (alertType)
            {
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.NoType:
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.FailedCharge:
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.WarningWithoutDefaultButton:
                    ViewAlert = "minimze-alert";
                    AlertType = "alert alert-warning";
                    Icon = "notification-icon";
                    Color = "#e7594a";
                    break;
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade:
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.UpgradeWithoutButton:
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.HolidayAlert:
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.NotificationWithoutDefaultButton:
                    Icon = "notification-icon";
                    AlertID = $"id={itemID}";
                    break;
                default:
                    break;
            }
            if (alertType != null)
            {
                if (!Enum.IsDefined(typeof(Storeya.Core.Entities.DashboardNotificationAlertType), alertType))
                {
                    Style = "display: none";
                }
            }
        }
    }
}
