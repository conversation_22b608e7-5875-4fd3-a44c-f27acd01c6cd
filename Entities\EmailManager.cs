﻿
using Storeya.Core.Helpers;
using Storeya.Core.Models;
using Storeya.Core.Models.Account;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Entities
{
    public class ReccomendedApp
    {
        public string Href { get; set; }
        public string ImageHref { get; set; }
        public string Description { get; set; }
        public string AppTitle { get; set; }

        public ReccomendedApp(int appType, int? platform = null)
        {
            AppTypes app = (AppTypes)appType;
            switch (app)
            {
                case AppTypes.FacebookShop:
                    SetFbShop(platform);
                    break;

                case AppTypes.CouponPop:
                    SetCP();
                    break;

                case AppTypes.ExitPop:
                    SetEP(platform);
                    break;

                case AppTypes.TrafficBooster:
                    SetTB();
                    break;

                default:
                    break;
            }
        }

        private void SetEP(int? platform = null)
        {
            this.AppTitle = "Exit Pop";

            if (platform != null)
            {
                if (platform == (int)CatalogSourcePlatforms.PrestaShop)
                {
                    this.Description = "Double Your Sales Today, by Catching the 98% Visitors that Exit Your Store without Purchasing, and Convert Them Into Paying Customers Today.<br/>Save Your Lost Visitors!";
                }
            }
            else
            {
                this.Description = "98% of site visitors leave without making a purchase – Exit Pop helps you get them back, and defeat cart abandonment once and for all!";
            }

            this.Href = "https://www.storeya.com/public/exitpop?utm_source=welcome&utm_medium=apps&utm_campaign=ep&exu=1";
            this.ImageHref = "https://www.storeya.com/common/images/appstore/banners/EP.png";
        }

        private void SetFbShop(int? platform = null)
        {
            this.AppTitle = "Facebook";
            if (platform != null)
            {
                if (platform == (int)CatalogSourcePlatforms.PrestaShop)
                {
                    this.Description = "Import Your Store onto Facebook Automatically or Create a Facebook Store From Scratch. No Design or Coding Skills Required. Works with any Platform, Language and Currency.";
                }
            }
            else
            {
                this.Description = "Import your entire store to Facebook in just a few clicks! No design or coding skills are required to turn your Facebook Store into the ultimate sales boosting tool.";
            }

            this.Href = "https://www.storeya.com/public/facebook-shop?utm_source=welcome&utm_medium=apps&utm_campaign=fb&exu=1";
            this.ImageHref = "https://www.storeya.com/common/images/appstore/banners/FS.png";
        }

        private void SetCP()
        {
            this.AppTitle = "Coupon Pop";
            this.Description = "Increase your sales & gain new leads today by offering your visitors a coupon in exchange for following you on a social network, or for joining your mailing list.";
            this.Href = "https://www.storeya.com/public/couponpop?utm_source=welcome&utm_medium=apps&utm_campaign=cp&exu=1";
            this.ImageHref = "https://www.storeya.com/common/images/appstore/banners/CP.png";
        }

        private void SetPB()
        {
            this.AppTitle = "Power Banner";
            this.Description = "With Power Banner you can easily design attractive banners for your site, social networks, email, and paid campaigns. No coding or design skills are required!";
            this.Href = "https://www.storeya.com/public/powerbanner?utm_source=welcome&utm_medium=apps&utm_campaign=pb&exu=1";
            this.ImageHref = "https://www.storeya.com/common/images/apps/powerbanner-app-banner.png";
        }

        //private void SetRFF()
        //{
        //    this.AppTitle = "Refer a Friend";
        //    this.Description = "Easily launch your referral program by<br/>offering discounts and rewards to your<br/>customers and their friends via email,<br/>Facebook, and Twitter.";
        //    this.Href = "https://www.storeya.com/public/referafriend?utm_source=welcome&utm_medium=apps&utm_campaign=rff&exu=1";
        //    this.ImageHref = "https://www.storeya.com/common/images/apps/referafriend-app-banner.png"; 
        //}

        private void SetTB()
        {
            this.AppTitle = "Traffic Booster";
            this.Description = "The easiest way to drive targeted traffic!";
            this.Href = "https://www.storeya.com/public/trafficbooster?utm_source=welcome&utm_medium=apps&utm_campaign=tb&exu=1";
            this.ImageHref = "https://www.storeya.com/common/images/appstore/banners/TB.png";
        }
    }

    public class WelcomeContent
    {
        public string Name { get; set; }
        public string LogBackUsing { get; set; }

        public string LogBackUsing_Type { get; set; }
        public string LogBackUsing_Data { get; set; }
        public string LogBackUsing_Password { get; set; }

        public string App_1_Href { get; set; }
        public string App_1_ImageHref { get; set; }
        public string App_1_Description { get; set; }
        public string App_1_AppTitle { get; set; }

        public string App_2_Href { get; set; }
        public string App_2_ImageHref { get; set; }
        public string App_2_Description { get; set; }
        public string App_2_AppTitle { get; set; }

        public string WixDashboardUrl { get; set; }

        public string Custom1 { get; set; }

        public WelcomeContent(int appType, int? platform = null)
        {
            AppTypes app = (AppTypes)appType;
            switch (app)
            {
                case AppTypes.FacebookShop:
                    SetReccomendedApps_FB();
                    break;
                case AppTypes.CouponPop:
                    SetReccomendedApps_CP(platform);
                    break;

                case AppTypes.ExitPop:
                    SetReccomendedApps_EP();
                    break;

                case AppTypes.TrafficBooster:
                    SetReccomendedApps_TB();
                    break;

                default:
                    SetReccomendedApps_General();
                    break;
            }
        }

        private void SetReccomendedApps_TB()
        {
            ReccomendedApp fbShopApp = new ReccomendedApp((int)AppTypes.FacebookShop);
            SetFirstRecommendedApp(fbShopApp);

            ReccomendedApp couponPopApp = new ReccomendedApp((int)AppTypes.CouponPop);
            SetSecondRecommendedApp(couponPopApp);

        }

        private void SetReccomendedApps_FB()
        {
            ReccomendedApp couponPopApp = new ReccomendedApp((int)AppTypes.CouponPop);
            SetFirstRecommendedApp(couponPopApp);

            ReccomendedApp exitPopApp = new ReccomendedApp((int)AppTypes.ExitPop);
            SetSecondRecommendedApp(exitPopApp);
        }

        private void SetReccomendedApps_CP(int? platform = null)
        {
            ReccomendedApp fbShopApp = new ReccomendedApp((int)AppTypes.FacebookShop, platform);
            SetFirstRecommendedApp(fbShopApp);

            ReccomendedApp exitPopApp = new ReccomendedApp((int)AppTypes.ExitPop, platform);
            SetSecondRecommendedApp(exitPopApp);
        }

        private void SetReccomendedApps_EP()
        {
            ReccomendedApp fbShopApp = new ReccomendedApp((int)AppTypes.FacebookShop);
            SetFirstRecommendedApp(fbShopApp);

            ReccomendedApp couponPopApp = new ReccomendedApp((int)AppTypes.CouponPop);
            SetSecondRecommendedApp(couponPopApp);

        }

        private void SetReccomendedApps_General()
        {
            ReccomendedApp fbShopApp = new ReccomendedApp((int)AppTypes.FacebookShop);
            SetFirstRecommendedApp(fbShopApp);

            ReccomendedApp tbApp = new ReccomendedApp((int)AppTypes.TrafficBooster);
            SetSecondRecommendedApp(tbApp);
        }

        private void SetFirstRecommendedApp(ReccomendedApp app)
        {
            this.App_1_AppTitle = app.AppTitle;
            this.App_1_Description = app.Description;
            this.App_1_Href = app.Href;
            this.App_1_ImageHref = app.ImageHref;
        }

        private void SetSecondRecommendedApp(ReccomendedApp app)
        {
            this.App_2_AppTitle = app.AppTitle;
            this.App_2_Description = app.Description;
            this.App_2_Href = app.Href;
            this.App_2_ImageHref = app.ImageHref;
        }
    }

    public class WelcomeEmail
    {
        public string Content { get; set; }

        public string OpenBracket
        {
            get
            {
                return "{";
            }

        }

        public string CloseBracket
        {
            get
            {

                return "}";
            }

        }

    }

    public static class EmailManager
    {
        //public static void SendWelcomeEmailToCPUsers(User user)
        //{
        //    string emailHtml = GetWelcomeEmailContent(user, (int)AppTypes.CouponPop, "CouponPop\\");
        //    string tag = "Welcome to StoreYa.com! CP" + GetVersionForTag(user.ID);           
        //    EmailHelper.SendEmail(user.Email, "Welcome to StoreYa.com!", emailHtml, null, null, true, tag);
        //}
        //public static void SendWelcomeEmailToEPUsers(User user)
        //{
        //    string emailHtml = GetWelcomeEmailContent(user, (int)AppTypes.ExitPop, "ExitPop\\");
        //    string tag = "Welcome to StoreYa.com! EP" + GetVersionForTag(user.ID);
        //    EmailHelper.SendEmail(user.Email, "Welcome to StoreYa.com!", emailHtml, null, null, true, tag);
        //}

        //public static void SendWelcomeEmailToFbShopsUers(User user)
        //{
        //    string emailHtml = GetWelcomeEmailContent(user, (int)AppTypes.FacebookShop, "");
        //    string tag = "Welcome to StoreYa.com! General" + GetVersionForTag(user.ID);
        //    EmailHelper.SendEmail(user.Email, "Welcome to StoreYa.com!", emailHtml, null, null, true, tag);
        //}

        public static void SendGeneralWelcomeEmail(User user, int appTypeID, int? platform = null, string password = null)
        {
            string emailHtml = GetGeneralWelcomeEmailContent(user, appTypeID, platform, "welcome_08_09_2016", password);
            string tag = "CAMPID:welcome3A";
            EmailHelper.SendEmail(user.Email, "Welcome to StoreYa.com!", emailHtml, null, null, true, tag);
            //if (user.ID % 2 == 0)
            //{
            //    string emailHtml = GetGeneralWelcomeEmailContent(user, (int)AppTypes.None, null, "welcome_18_6_2015.html");//GetWelcomeEmailContent(user, (int)AppTypes.None, "");
            //    string tag = "CAMPID:welcome3A";
            //    EmailHelper.SendEmail(user.Email, "Welcome to StoreYa.com!", emailHtml, null, null, true, tag);
            //}
            //else
            //{
            //    string emailHtml = GetGeneralWelcomeEmailContent(user, (int)AppTypes.None, null, "welcome_1_6_2015.html");
            //    string tag = "CAMPID:welcome3B";
            //    EmailHelper.SendEmail(user.Email, "Welcome to StoreYa.com!", emailHtml, null, null, true, tag);
            //}   

            // to call an old a - b welcome
            //string emailHtml = GetWelcomeEmailContent(user, (int)AppTypes.None, "");
            //string tag = "CAMPID:WelcomeGeneral" + GetVersionLetter(user.ID); 
            //EmailHelper.SendEmail(user.Email, "Welcome to StoreYa.com!", emailHtml, null, null, true, tag);

        }

        public static void SendFbLeadWelcomeEmail(string emailTo, string name)
        {
            WelcomeContent model = new WelcomeContent((int)AppTypes.TrafficBooster);
            model.Name = name;
            string htmlTemplate = EmailHelper.GetTemplateContentFromResource("welcome_fb_lead");
            string emailHtml = htmlTemplate.FormatWith(model);
            EmailHelper.SendEmail(emailTo, "Welcome to StoreYa.com!", emailHtml, null, null, true, "FbLead_Welcome");
        }
        //public static void SendPrestaShopCouponPopWelcomeEmail(User user)
        //{
        //    string emailHtml = GetPrestaShopCouponPopWelcomeEmailContent(user, (int)AppTypes.CouponPop, "CouponPop\\");
        //    string tag = "Welcome to StoreYa.com! Cp_Prestashop"; 
        //    EmailHelper.SendEmail(user.Email, "Welcome to StoreYa.com!", emailHtml, null, null, true, tag);
        //}

        //private static string GetPrestaShopCouponPopWelcomeEmailContent(User user, int appType, string path)
        //{
        //    string htmlTemplate = EmailHelper.GetTemplateContent("welcome_layout.html");
        //    path = path + "welcome_prestashop.html";
        //    WelcomeEmail model = new WelcomeEmail();
        //    model.Content = GetContent(user, path, appType, (int)CatalogSourcePlatforms.PrestaShop);
        //    string emailTemplate = htmlTemplate.FormatWith(model);
        //    return emailTemplate;
        //}


        public static string GetEmailAboutPayPalSubscriptionCancellation(User user, string layoutPath = null, string contentPath = "paypal_subscription_cancellation", string custom1 = null)
        {
            //string htmlTemplate = EmailHelper.GetTemplateContent(layoutPath);
            string contentTemplate = EmailHelper.GetTemplateContentFromResource(contentPath);

            string content = contentTemplate.FormatWith(new { Name = (string.IsNullOrEmpty(user.Name) ? "There" : user.Name) });
            //string emailContent = htmlTemplate.FormatWith(new { Content = content, OpenBracket = "{", CloseBracket = "}" });
            return content;
        }

        public static string GetGeneralWelcomeEmailContent(User user, int appType, int? platform = null, string template = null, string password = null, Shop shop = null)
        {
            if (string.IsNullOrEmpty(template))
                template = "welcome_08_09_2016";
            string htmlTemplate = EmailHelper.GetTemplateContentFromResource(template);
            WelcomeContent model = new WelcomeContent(appType, platform);

            //if (user.FbProfileID == (int)LoginType.EmailPassword || user.FbProfileID == (int)LoginType.GoogleAuth)
            //{
            model.LogBackUsing_Type = "email address";
            model.LogBackUsing_Data = user.Email;
            if (!string.IsNullOrEmpty(password))
            {
                model.LogBackUsing_Password = string.Format("<span style=\"font-family:roboto, sans-serif; font-size:14px; font-weight:normal; color: #7b7b7b;line-height: 25px;\">Password: {0}</span><br />", password);
            }
            if (user.OriginMarketplace != null && user.OriginMarketplace == (int)OriginMarketplaces.Wix)
            {
                model.WixDashboardUrl = WixHelper.CreateDashboardDeepLink(shop.ID); // CreateDashboardUrlByStoreYaShopID(shop.ID);
            }
            //}
            //else
            //{
            //    model.LogBackUsing_Type = "Facebook profile";
            //    model.LogBackUsing_Data = string.Format("<a href=\"http://www.facebook.com/profile.php?id={0}\">{1}</a>", user.FbProfileID, user.Name);
            //}

            model.Name = string.IsNullOrEmpty(user.Name) ? "there" : user.Name;

            string emailTemplate = htmlTemplate.FormatWith(model);
            return emailTemplate;
        }
        //public static string GetWelcomeEmailContent(User user, int appType, string path)
        //{
        //    string htmlTemplate = EmailHelper.GetTemplateContent("welcome_layout.html");
        //    path = GetContentPath(user.ID, path, appType);
        //    WelcomeEmail model = new WelcomeEmail();
        //    model.Content = GetContent(user, path, appType);
        //    string emailTemplate = htmlTemplate.FormatWith(model);
        //    return emailTemplate;
        //}

        public static string GetEmailContent(User user, int appType, string layoutPath, string contentPath, string custom1 = null)
        {
            string htmlTemplate = EmailHelper.GetTemplateContent(layoutPath);

            string contentTemplate = EmailHelper.GetTemplateContent(contentPath);
            string logBackUsing = string.Format("email address {0}", user.Email);


            WelcomeContent contentModel = new WelcomeContent(appType);
            contentModel.Name = string.IsNullOrEmpty(user.Name) ? "there" : user.Name;
            contentModel.LogBackUsing = logBackUsing;

            if (!string.IsNullOrEmpty(custom1))
            {
                contentModel.Custom1 = custom1;
            }

            string content = contentTemplate.FormatWith(contentModel);

            WelcomeEmail emailModel = new WelcomeEmail();
            emailModel.Content = content;
            string emailContent = htmlTemplate.FormatWith(emailModel);
            return emailContent;
        }

        //private static string GetContent(User user, string path, int appType, int? platform = null)
        //{
        //    string contentTemplate = EmailHelper.GetTemplateContent(path);
        //    string logBackUsing = null;

        //    if (user.FbProfileID == (int)LoginType.EmailPassword || user.FbProfileID == (int)LoginType.GoogleAuth)
        //    {
        //        logBackUsing = string.Format("email address {0}", user.Email);
        //    }
        //    else
        //        logBackUsing = string.Format("Facebook profile <a href=\"http://www.facebook.com/profile.php?id={0}\">{1}</a>", user.FbProfileID, user.Name);

        //    WelcomeContent model = new WelcomeContent(appType, platform);
        //    model.Name = string.IsNullOrEmpty(user.Name) ? "there" : user.Name;
        //    model.LogBackUsing = logBackUsing;
        //    string content = contentTemplate.FormatWith(model);

        //    return content;
        //}

        //private static string GetContentPath(int userID, string path, int appType)
        //{
        //    //if (appType==(int)AppTypes.None)
        //    //    return path + "welcome_version_B.html";

        //    if (userID % 2 == 0)
        //    {
        //        path = path + "welcome_version_A.html";
        //    }
        //    else
        //    {
        //        path = path + "welcome_version_B.html";
        //    }
        //    return path;
        //}

        public static string GetVersionForTag(int userID)
        {
            string version = null;
            if (userID % 2 == 0)
            {
                version = "_version_A";
            }
            else
            {
                version = "_version_B";
            }
            return version;
        }

        public static string GetVersionLetter(int userID)
        {
            string version = null;
            if (userID % 2 == 0)
            {
                version = "A";
            }
            else
            {
                version = "B";
            }
            return version;
        }

    }
}
