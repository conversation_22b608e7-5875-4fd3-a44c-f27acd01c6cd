﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Entities.Facebook
{
    public class Application
    {
        public string name { get; set; }
        public string id { get; set; }
        public string @namespace { get; set; }
    }

    public class Datum
    {
        public string id { get; set; }
        public string image_url { get; set; }
        public string name { get; set; }
        public string link { get; set; }
        public Application application { get; set; }
        public bool is_permanent { get; set; }
        public int position { get; set; }
        public bool is_non_connection_landing_tab { get; set; }
    }

    public class Paging
    {
        public string next { get; set; }
    }

    public class RootObject
    {
        public List<Datum> data { get; set; }
        public Paging paging { get; set; }
    }
}
