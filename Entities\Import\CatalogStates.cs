﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Storeya.Core.Entities.Import
{
    public enum CatalogStates
    {
        //FeaturedProductsDownloaded = 0,
        //PricesUpdated = 10,
        //ImagesDownloaded = 20
        /*
            0 - init
            1- background task started
            2 - finished with many missing
            90 - at least 90% ready
            100 - 100% (we will probably not use it too much :-)))
         */
        InitFailed = -3,        
        InitAttempt = 3,        
        Initiated = 5,
        BackGroundTaskStarted = 10,
        TaskFinishedWithMissingImages = -10,
        MaximumErrorsExceeded = -5, //error durring import
        CatalogIsReady = 90,
        BrokenOutlinks = -90,
        ManuallySuspended = -100
    }
}