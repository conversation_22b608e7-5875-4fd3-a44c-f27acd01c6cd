﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Entities.Import
{
        public enum ImportStatuses
        {
            Success,
            SuccessWithWarnings,
            Failure,
            Running
        }

        public class ImportResponse
        {
            public ImportStatuses Status { get; set; }
            public List<ImportError> Errors { get; set; }
            public List<ImportWarning> Warnings { get; set; }
            public int ProductsAmount { get; set; }
            public int CategoriesAmount { get; set; }

            public ImportResponse()
            {
                this.Errors = new List<ImportError>();
                this.Warnings = new List<ImportWarning>();
                this.Status = ImportStatuses.Running;
            }
        }

        public class ImportWarning
        {
            public string Message { get; set; }
            public int RowNumber { get; set; }
        }

        public enum ImportErrorCodes
        {
            NoProductsToImport = 1,
            ThereAreProductsAlready = 2,
            ExceptionDuringReadingCsvFile = 3,
            FailedToInitDataExtructorStrategy = 4,
            MagentoApiFailure = 5,
            PrestaShopApiFailure = 6,
            eBayApiFailure = 7,
            BadAuthentication = 8,
            WordPressFailure = 9,
            WrongUrl = 10,
            NotWellFormedXml = 11,
            ModuleIsNotInstalled = 12,
            InvalidSellerName = 13,
            ShopifyApiFailure = 14,
            AmazonImportProductsFailure = 15,
            GoogleBaseImportProductsFailure = 16,
            NotWellFormedTabDelimitedTextFIle = 17,
            MaxCountMissingProductsExceeded = 18,
            EtsyApiFailure = 19,
            MissingPermissions = 20,
            TradeTubeRApiFailure = 21,
            UnknownGoogleBaseFormat = 22,
            MaxFileSizeExceeded = 23,
            CafepressApiFailure = 24,
            ZazzleApiFailure = 25,
            TictailApiFailure = 26,
            SpreeCommerceApiFailure = 27,
            DisabledFeed = 28,
            FileOrDirectoryNotFound = 404,         
        }

        public class ImportError
        {
            public string Exception { get; set; }
            public string Message { get; set; }
            public int Code { get; set; }
        }
}
