﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Storeya.Core.Helpers;

namespace Storeya.Core.Entities
{
    public enum ActionType
    {
        Sync = 1,
        ManuallyUpdate = 2,
        Downgrade = 3
    }

    public static class InventoryChangesTracker
    {
        public static void AddLog(int shopID, ActionType action)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            db.InventoryChangesLogs.Add(new InventoryChangesLog(){ ActionType  = (byte)action, ShopID  = shopID });
            try 
	        {	        
		        db.SaveChanges();    
	        }
	        catch (Exception ex)
	        {
				Log4NetLogger.Error("Failed to track action.", ex, shopID);
	        }                   
        }
    }
}
