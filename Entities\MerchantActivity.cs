﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;
using System.Net;


namespace Storeya.Core.Entities
{
    public enum Subject
    {
        Login = 1,

        Marketing_GroupDeal = 10,
        Marketing_InstaGallery = 11,
        Marketing_ScratchAndWin = 12,
        Marketing_FanGate = 13,
        Marketing_LikeBOx = 14, 
        Marketing_TwitterTab = 15,
        Marketing_PinterestTab = 16,
        Marketing_YouTubeTab = 17, 
        Marketing_InstagramTab = 18,

        SpecialOffer_AdRoll = 30, 
        SpecialOffer_GoogleAdwords = 31,
        SpecialOffer_oDesk = 32,
        SpecialOffer_Moz = 33,

        HotOrNot = 50,
        HotOrNot_Skiped = 51
    }

    public enum Action
    {
        Log_In = 1, 
        Tab_Installation = 2, 
        Claim_Coupon = 3 
    }

    public static class MerchantActivityManager
    {
        public static void AddLog(Shop shop, User user, Subject subject, Action action)
        {
            AddLog(shop, user, subject, (int) action);

        }

        public static void AddLog(Shop shop, User user, Subject subject, int action)
        {
            int shopID = 0;
            if (shop != null)
            {
                shopID = shop.ID;
            }

            //string ip = System.Web.HttpContext.Current.Request.UserHostAddress;
            //if (string.IsNullOrEmpty(ip))
            //    ip = System.Web.HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];

            //int intAddress = 0;
            //if (!string.IsNullOrEmpty(ip))
            //{
            //    IPAddress address;
            //    if (IPAddress.TryParse(ip, out address))
            //    {
            //        intAddress = BitConverter.ToInt32(address.GetAddressBytes(), 0);
            //    }
            //}

            int intAddress = HttpHelper.GetIP();

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            MerchantActivityLog recordToAdd = new MerchantActivityLog() { UserID = user.ID, ShopID = shopID, InsertedAt = DateTime.Now, IP = intAddress, Subject = (int)subject, Action = action };
            try
            {
                db.MerchantActivityLogs.Add(recordToAdd);
                db.SaveChanges();
            }
            catch (Exception)
            {
                Log4NetLogger.Error(string.Format("Failed to save log record for UserID: {0}", user.ID));
            }
        }
    }
}


