﻿using Storeya.Core.Models.Payments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Entities
{
    public class PaymentDetails
    {
        public int PlanID { get; set; }
        public int ShopID { get; set; }
        public int AppID { get; set; }
        public int? AgreeID { get; set; }
        public int Method { get; set; } //1 - annualy, 0 - monthly
        
        public string AffTracker { get; set; }
        public int ContractID { get; set; }

        public double CustomPrice { get; set; }        
        public double? ProratedPrice { get; set; }

        public string ItemName { get; set; }
        public int PaymentGroup { get; set; }

        public string Language { get; set; }
        public int? UserType { get; set; }

        public string Currency { get; set; }
        public PaymentAdapterTypes PaymentAdapterType { get; set; }
    }
}
