﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Entities
{
    [Flags]
    public enum Permissions
    {
        None = 0,
        NOT_USER_ID = (1 << 0), //we can use this number
        CustomizeAllowMenu = (1 << 1),
        CustomizeAllowThemes = ((1 << 2) | CustomizeAllowMenu),
        CustomizeAllowEditor = ((1 << 3) | CustomizeAllowMenu | CustomizeAllowThemes),

        SyncAllowScheduled = ((1 << 4)),
        SyncAllowSyncNow = ((1 << 5) | SyncAllowScheduled),

        MultiStoresAllowCreateNew = ((1 << 6)),

        PermissionAddAdmin = ((1 << 7)),
        PermissionAddClient = ((1 << 8) | PermissionAddAdmin),

        MultipleFanPages = ((1 << 9)),


        PlanFreemium = (CustomizeAllowMenu),
        PlanEconomy = (CustomizeAllowMenu | SyncAllowScheduled),
        PlanBusiness = (CustomizeAllowThemes | SyncAllowScheduled),
        PlanFirstClass = (CustomizeAllowThemes | SyncAllowSyncNow | PermissionAddAdmin | MultipleFanPages),
        PlanPrivateJet = (CustomizeAllowEditor | SyncAllowSyncNow | PermissionAddAdmin | MultiStoresAllowCreateNew | MultipleFanPages),
        //PlanAgency = (CustomizeAllowEditor | PermissionAddClient | MultiStoresAllowCreateNew),
        PlanAgencyClient = (SyncAllowSyncNow), //same to None
        PlanDefaultSet = (PlanFirstClass),
        PlanEtsyPro = (CustomizeAllowThemes | SyncAllowSyncNow | PermissionAddAdmin | MultipleFanPages),
        PlanSuit = (MultiStoresAllowCreateNew)
    }
}
