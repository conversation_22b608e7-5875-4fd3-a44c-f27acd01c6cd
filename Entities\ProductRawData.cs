﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;

namespace Storeya.Core.Entities
{
    public class ProductRawData
    {
        public ProductRawData()
        {
            this.Collections = new List<RawCollection>();
            this.Images = new List<RawProductImage>();
        }

        public int ShopID { get; set; }
        public string  EncodedShopID { get; set; }

        public string OriginalID { get; set; }
        public string OriginalSecondaryID { get; set; }
        public string[] OriginalCategoriesIDs { get; set; }
        public string Name { get; set; }
        public string Url { get; set; }
        public string Image { get; set; }
        public List<RawProductImage> Images { get; set; }
        public string Category { get; set; }
        public List<RawCategory> AllCategories { get; set; }
        public List<RawCollection> Collections { get; set; }
        public double Price { get; set; }
        public double? CompareToPrice { get; set; }

        public string Description { get; set; }
        public string DescriptionHtml { get; set; }
        public int SourceType { get; set; }
        //public string Visibility { get; set; }
        public DataChangeTypes Status { get; set; }
        public int Disabled { get; set; }

        public int Quntitiy { get; set; }
        public string Currency { get; set; }
        public string StoreName { get; set; }
        public string Availability { get; set; }
        public string MPN { get; set; }
        public string GTIN { get; set; }
        public string MetaTitle { get; set; }
        public string MetaDescription { get; set; }

        public string ShippingWeight { get; set; }
        public Shipping Shipping { get; set; }

        public string ProductTags { get; set; }
        public string ShortDescription
        {
            get
            {
                string shortDescription = TextManipulationHelper.StripTagsCharArray(this.Description);
                return TextManipulationHelper.Truncate(shortDescription, 1995);
            }
        }

        public string CleanHtmlDescription
        {
            get
            {
                string cleanHtmlDescription = TextManipulationHelper.RemoveImagesAndIframesAndScripts(this.Description);
                return cleanHtmlDescription;
            }
        }

        public string Brand { get; set; }
        public string Color { get; set; }
        public string Size { get; set; }

    }
    public class Shipping
    {
        public string country { get; set; }
        public string region { get; set; }
        public string service { get; set; }
        public string price { get; set; }
    }
    public enum DataChangeTypes
    {
        New,
        Updated,
        Deleted
    }

}

