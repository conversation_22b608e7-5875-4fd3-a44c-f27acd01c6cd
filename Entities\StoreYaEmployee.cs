﻿using OAuth;
using Storeya.Core.Helpers;
using Storeya.Core.Models.Charges;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;


namespace Storeya.Core.Entities
{
    //public enum StoreYaEmployee_Old
    //{
    //    Unassigned = -1,
    //    None = 0,
    //    Yariv = 1,
    //    Dori = 2,
    //    Rachel = 3,
    //    Yoni = 5,
    //    Reut = 6,
    //    Brigitta = 7
    //}

    //ID	Name	FbProfileID	Email
    //165691	Yariv Dror	596208132	<EMAIL>
    //260336	TestStore_Eyal	693517196	<EMAIL>
    //168909	Pasha	100000600319997	<EMAIL>
    //182800	Yariv Dror	100003017117837	<EMAIL>     - not in use from 12/20/2017 2:41:40 PM
    //255238	TestStore_Olga	100003703527989	avrora102012@gmail.com_test
    //322851	Reut	116768029230520	<EMAIL>
    //322854	Brigitta	10156501147754758	<EMAIL>
    //136295	Master D	10158275012730244	<EMAIL>
    //174125	Rachel Osher	10204882302406587	<EMAIL>
    //269667    Yoni       10156659853364741        <EMAIL>

    public enum StoreYaEmployee
    {
        Unassigned = 0,
        //None = 0,
        //Dori = 136295,
        //Yariv = 165691,
        Pasha = 159684,
        Rachel = 174125,
        //Olga = 255238,
        //Eyal = 260336,
        //Yoni = 269667,
        //Reut = 322851,
        //Brigitta = 322854,
        Mushon = 327017,
        Shlomit = 357110,
        Berk = 377063,
        //Hadar = 388656
        //Noam = 450129, 
        Yafit = 513179
    }
    public enum BoRole
    {
        None = 0,
        Manager = 1,
        Payments = 2,
        Dev = 3
    }
    public enum StoreYaNotEmployee
    {
        Reut = 322851,
        //Shlomit = 357110,
    }
    public class AssignedTo
    {
        public int ID { get; set; }
        public string Name { get; set; }
    }


    public class StoreYaEmployeeHelper
    {
        public static List<AssignedTo> GetAssignedToAndDefault()
        {
            var list = StoreYaEmployeeHelper.GetAssignedTo();
            list.Insert(0, new AssignedTo() { ID = 0, Name = "-- select --" });
            return list;
        }


        public static List<AssignedTo> GetAssignedTo()
        {
            List<AssignedTo> list = new List<AssignedTo>();

            foreach (StoreYaEmployee val in Enum.GetValues(typeof(StoreYaEmployee)))
            {
                if (val != StoreYaEmployee.Pasha
                    && val != StoreYaEmployee.Unassigned
                    //val != StoreYaEmployee.Pasha
                    //&& val != StoreYaEmployee.Dori
                    //&& val != StoreYaEmployee.Eyal2
                    //&& val != StoreYaEmployee.Olga
                    )
                {
                    list.Add(new AssignedTo() { ID = (int)val, Name = Enum.GetName(typeof(StoreYaEmployee), val) });
                }
            }

            return list.OrderByDescending(l => l.ID == 0).ThenBy(l => l.Name).ToList();
        }

        private static List<int> NotWorkingAnyMore()
        {
            List<int> ints = new List<int>
            {
                StoreYaNotEmployee.Reut.GetHashCode(),
                //StoreYaNotEmployee.Shlomit.GetHashCode()
            };
            return ints;
        }
        public static string GetmanagerNameIfExist(int shopID)
        {
            var db = Helpers.DataHelper.GetStoreYaEntities();
            TbBigSpender tbBigSpender = db.TbBigSpenders.Where(u => u.ShopID == shopID).SingleOrDefault();
            if (tbBigSpender != null)
            {
                return StoreYaEmployeeHelper.GetName(tbBigSpender.AssignedTo);
            }
            return null;
        }
        public static string GetName(int? userID)
        {
            if (userID.HasValue)
            {
                string name = null;

                if (userID >= 1 && userID <= 7)
                {
                    name = "OldUser_" + userID; //Enum.GetName(typeof(StoreYaEmployee_Old), userID.Value);
                }
                else
                {
                    try
                    {
                        name = Enum.GetName(typeof(StoreYaEmployee), userID.Value);
                    }
                    catch
                    {
                        name = "UNKNOWN_Employee";
                    }
                }

                //if (string.IsNullOrEmpty(name))
                //{
                //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
                //    User user = db.Users.Where(x => x.ID == userID).SingleOrDefault();
                //    if (user != null)
                //    {
                //        name = (!string.IsNullOrEmpty(user.Name) ? user.Name : user.Email);
                //    }
                //}

                return name;
            }

            return null;
        }

        public static int GetNewAssignedToForFbAccounts(int? shopID = null)
        {
            int? assignedTo = null;
            if (shopID != null)
            {
                assignedTo = GetLastManagerFromInternalTask(shopID.Value);
                if (assignedTo != null)
                {
                    return assignedTo.Value;
                }
            }
            Random random = new Random();
            var names = new List<int> {
                (int)StoreYaEmployee.Rachel,
                (int)StoreYaEmployee.Yafit,
                (int)StoreYaEmployee.Mushon,
                (int)StoreYaEmployee.Shlomit,
                (int)StoreYaEmployee.Berk};
            int index = random.Next(names.Count);
            assignedTo = names[index];
            return assignedTo.Value;
        }

        public static int GetNewAssignedToForBigSpender(int? shopID = null)
        {
            int? assignedTo = null;
            if (shopID != null)
            {
                assignedTo = GetLastManagerFromInternalTask(shopID.Value);
                if (assignedTo != null)
                {
                    return assignedTo.Value;
                }
            }
            var names = new List<int> {
                (int)StoreYaEmployee.Rachel,
                (int)StoreYaEmployee.Shlomit,
                (int)StoreYaEmployee.Yafit,
                (int)StoreYaEmployee.Mushon,
                (int)StoreYaEmployee.Berk};

            User user = ShopsHelper.GetUser(shopID ?? 0);
            if (user != null && user.RevenueRank > 50000)
            {
                //overwrite
                names = new List<int> {
                (int)StoreYaEmployee.Mushon,
                (int)StoreYaEmployee.Berk};
            }
            else if (user != null && user.RevenueRank > 10000)
            {
                names.Add((int)StoreYaEmployee.Berk);
                names.Add((int)StoreYaEmployee.Shlomit);
            }
            else
            {
                names.Add((int)StoreYaEmployee.Shlomit);
                names.Add((int)StoreYaEmployee.Yafit);
                names.Add((int)StoreYaEmployee.Rachel);
            }

            int index = RandomNumberHelper.Get(names.Count);
            assignedTo = names[index];
            return assignedTo.Value;
        }
        public static int? GetLastManagerFromInternalTask(int shopID)
        {
            var db = Helpers.DataHelper.GetStoreYaEntities();
            DateTime YearAgo = DateTime.Now.AddYears(-1);
            var lastYearTasks = db.TbInternalTasks.Where(x => x.ShopID == shopID && x.InsertedAt > YearAgo);
            var firstTask = lastYearTasks.OrderBy(x => x.ID).FirstOrDefault();
            DateTime daysAgo3 = DateTime.Now.AddDays(-3);
            if (firstTask == null || firstTask.InsertedAt > daysAgo3)
            {
                //choose random AM
                return null;
            }
            var lastTask = lastYearTasks.OrderByDescending(x => x.ID).FirstOrDefault();
            if (lastTask != null)
            {
                if (lastTask.AssignedTo.HasValue && NotWorkingAnyMore().Contains(lastTask.AssignedTo.Value))
                {
                    return null;
                }
                if (lastTask.AssignedTo.HasValue && !Enum.IsDefined(typeof(StoreYaEmployee), lastTask.AssignedTo.Value))
                {
                    return null;
                }
                return lastTask.AssignedTo;
            }
            return null;
        }

        //public static int GetNewAssignedHebrewForBigSpender()
        //{
        //    Random random = new Random();
        //    var names = new List<int> {
        //        (int)StoreYaEmployee.Mushon ,
        //        (int)StoreYaEmployee.Reut,
        //        (int)StoreYaEmployee.Shlomit};
        //    //(int)StoreYaEmployee.Noam };
        //    int index = random.Next(names.Count);
        //    int assignedTo = names[index];
        //    return assignedTo;
        //}

        private static List<AssignedTo> GetActiveAccountManagersForUnassignedTasks()
        {
            List<AssignedTo> list = new List<AssignedTo>();

            list.Add(CreateAssignedObject(StoreYaEmployee.Rachel));
            list.Add(CreateAssignedObject(StoreYaEmployee.Mushon));
            list.Add(CreateAssignedObject(StoreYaEmployee.Berk));
            list.Add(CreateAssignedObject(StoreYaEmployee.Yafit));
            list.Add(CreateAssignedObject(StoreYaEmployee.Shlomit));
            //list.Add(new AssignedTo() { ID = (int)StoreYaEmployee.Berk, Name = Enum.GetName(typeof(StoreYaEmployee), StoreYaEmployee.Berk) });
            //list.Add(new AssignedTo() { ID = (int)StoreYaEmployee.Hadar, Name = Enum.GetName(typeof(StoreYaEmployee), StoreYaEmployee.Hadar) });
            //list.Add(new AssignedTo() { ID = (int)StoreYaEmployee.Noam, Name = Enum.GetName(typeof(StoreYaEmployee), StoreYaEmployee.Noam) });
            //list.Add(new AssignedTo() { ID = (int)StoreYaEmployee.Reut, Name = Enum.GetName(typeof(StoreYaEmployee), StoreYaEmployee.Reut) });

            return list.OrderByDescending(l => l.ID == 0).ThenBy(l => l.Name).ToList();
        }

        private static AssignedTo CreateAssignedObject(StoreYaEmployee employee)
        {
            return new AssignedTo() { ID = (int)employee, Name = Enum.GetName(typeof(StoreYaEmployee), employee) };
        }

        //public static int SetAccountManagerRandomly()
        //{
        //    int assignedTo = 0;

        //    List<AssignedTo> activeMAnagers = GetActiveAccountManagers();

        //    Random random = new Random();
        //    var names = new List<int>();
        //    foreach (var activeMAnager in activeMAnagers)
        //    {
        //        names.Add(activeMAnager.ID);
        //    }
        //    int index = random.Next(names.Count);
        //    assignedTo = names[index];            

        //    return assignedTo;
        //}

        public static int GetAccountManagerByShopID(int shopID)
        {
            int assignedTo = 0;
            int? assignedToFromLastTask = GetLastManagerFromInternalTask(shopID);
            if (assignedToFromLastTask.HasValue)
            {
                return assignedToFromLastTask.Value;
            }
            List<AssignedTo> activeMAnagers = GetActiveAccountManagersForUnassignedTasks();

            int lastDigit = FindLastDigit(shopID);
            int activeMAnagersCount = activeMAnagers.Count();

            double coef = (double)10 / (double)activeMAnagersCount;

            int activeMAnagersIndex = 0;

            if (coef != 0)
            {
                activeMAnagersIndex = Convert.ToInt32(Math.Round((double)lastDigit / coef));

                if (activeMAnagersIndex > (activeMAnagersCount - 1))
                    activeMAnagersIndex = (activeMAnagersCount - 1);
            }

            assignedTo = activeMAnagers[activeMAnagersIndex].ID;

            return assignedTo;
        }

        private static int FindLastDigit(int n)
        {
            // return the last digit 
            return (n % 10);
        }

        //public static int GetSupportRandom()
        //{
        //    Random random = new Random();
        //    var names = new List<int> { (int)StoreYaEmployee.Reut, (int)StoreYaEmployee.Brigitta };
        //    int index = random.Next(names.Count);
        //    int assignedTo = names[index];
        //    return assignedTo;
        //}

        public static User GetDefaultDevAccount(User currentUser)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            User pasha = db.Users.Where(u => u.ID == (int)StoreYaEmployee.Pasha).SingleOrDefault();
            if (pasha == null)
            {
                return currentUser;
            }
            return pasha;
        }
        public static string GetAccountManagerEmail(int shopID, out string name, string defaultEmail = "<EMAIL>")
        {
            name = string.Empty;
            if (defaultEmail.Contains("@"))
            {
                name = defaultEmail.Split('@')[0];
            }
            int userId = GetOwnerFor(shopID);
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            User user = db.Users.Where(u => u.ID == userId).SingleOrDefault();
            if (user == null)
            {
                return defaultEmail;
            }
            name = user.Name;
            return user.Email;
        }
        public static long GetOwnerFBProfileId(int shopId)
        {
            int userId = GetOwnerFor(shopId);
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            User user = db.Users.Where(u => u.ID == userId).SingleOrDefault();
            if (user == null)
            {
                return 0;
            }
            return user.FbProfileID ?? 0;
        }
        public static int GetOwnerFor(int shopID)
        {
            int? assignedTo = null;

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            TbBigSpender tbBigSpender = db.TbBigSpenders.Where(u => u.ShopID == shopID).SingleOrDefault();
            if (tbBigSpender != null && tbBigSpender.AssignedTo > 10)
            {
                if (NotWorkingAnyMore().Contains(tbBigSpender.AssignedTo.Value)
                    || !Enum.IsDefined(typeof(StoreYaEmployee), tbBigSpender.AssignedTo.Value))
                {
                    assignedTo = GetNewAssignedToForBigSpender(shopID);
                    tbBigSpender.AssignedTo = assignedTo;
                    ConsoleAppHelper.WriteLogWithDB($"Account Manager was changed from {tbBigSpender.AssignedTo} to {(StoreYaEmployee)assignedTo}.");
                    db.SaveChanges();
                }
                else
                {
                    assignedTo = tbBigSpender.AssignedTo;
                }
            }
            else
            {
                assignedTo = StoreYaEmployeeHelper.GetAccountManagerByShopID(shopID);
            }

            return assignedTo.Value;
        }

        public static StoreYaEmployee GetShopAccountManager(int shopID)
        {
            var db = DataHelper.GetStoreYaEntities();
            var bs = db.TbBigSpenders.Where(b => b.ShopID == shopID).SingleOrDefault();
            if (bs != null && bs.AssignedTo != null && bs.AssignedTo != (int)StoreYaEmployee.Unassigned)
            {
                return (StoreYaEmployee)bs.AssignedTo;
            }
            return StoreYaEmployee.Unassigned;
        }

        public static StoreYaEmployee GetRandomOf(StoreYaEmployee[] empls)
        {
            //string[] Titles = { "Excellent", "Good", "Super", "REALLY GOOD DOCTOR!", "THANK YOU!", "THE BEST", "EXCELLENT PHYSICIAN", "EXCELLENT DOCTOR" };

            return empls[new Random().Next(0, empls.Length)];
        }
        public static bool IsPaymentsPageAllowed(User user)
        {
            if (IsDeveloper(user))
            {
                return true;
            }
            else if (IsPaymentUser(user))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public static bool IsPaymentsPageAllowed()
        {
            User user = System.Web.HttpContext.Current.Session["UserInfo"] as User;
            if (user == null) return false;
            return IsPaymentsPageAllowed(user);
        }
        public static bool IsPaymentUser(User user)
        {
            if (user.BoRole == (int)Storeya.Core.Entities.BoRole.Payments)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public static bool IsDeveloper(User user = null)
        {
            return IsDeveloper(out int? userId, user);
        }
        public static bool IsDevAdmin(User user = null)
        {
            return IsDeveloper(out int? userId, user);
        }
        public static bool IsDeveloper(out int? userId, User user = null)
        {
            userId = null;
            if (user == null)
            {
                user = System.Web.HttpContext.Current.Session["UserInfo"] as User;
                if (user == null) return false;
            }
            if (user.BoRole != null && user.BoRole == (int)Storeya.Core.Entities.BoRole.Dev)
            {
                userId = user.ID;
                return true;
            }
            return false;
        }
        public static bool IsStroreYaEmail(int userID)
        {
            var db = DataHelper.GetStoreYaEntities();
            var user = db.Users.Where(u => u.ID == userID).OrderByDescending(i => i.InsertedAt).FirstOrDefault();
            if (user != null)
            {
              
                if (user.Email != null && user.Email.ToLower().Contains("@storeya.com"))
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        public static bool IsStroreYaUser(string email)
        {
            var db = DataHelper.GetStoreYaEntities();
            var user = db.Users.Where(u => u.Email.ToLower() == email.ToLower()).OrderByDescending(i => i.InsertedAt).FirstOrDefault();
            if (user != null)
            {
                if (!string.IsNullOrEmpty(ConfigHelper.GetValue("BoAdmins")))
                {
                    if (ConfigHelper.GetValue("BoAdmins").Split(',').Contains(user.FbProfileID.ToString()))
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                string name = null;
                try
                {
                    name = Enum.GetName(typeof(StoreYaEmployee), user.ID);
                    if (!string.IsNullOrEmpty(name))
                    {
                        return true;
                    }
                }
                catch
                {

                }
                if (user.Email != null && user.Email.ToLower().Contains("@storeya.com"))
                {
                    return true;
                }
                return false;
            }
            return false;
        }

        public static void CreateBoAdminLoginTokenLink(int? id = null, int expireInHours = 24, bool local = false)
        {
            string url = "https://bo.storeya.com/access/LogIn?token=";
            if (local)
            {
                url = "https://bo.fluxas.com/access/LogIn?token=";
            }
            List<int> ids = Enum.GetValues(typeof(StoreYaEmployee)).Cast<int>().ToList();
            if (id.HasValue)
            {
                ids = new List<int> { id.Value };
            }
            var db = DataHelper.GetStoreYaEntities();
            var boUsers = db.Users.Where(i => ids.Contains(i.ID)).ToList();
            foreach (var user in boUsers)
            {
                var expire = DateTime.Now.AddHours(expireInHours).ToBinary();
                string tokenRow = $"uid={user.ID}&shopId=&returnUrl=/home&exp={expire}";

                var token = EncryptionHelper.EncryptAES(tokenRow);
                Console.WriteLine($"Email: {user.Email} , link: {url}{token.Base64Encode()} ");

            }

        }
    }
}
