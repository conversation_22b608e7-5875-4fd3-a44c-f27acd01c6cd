﻿using Storeya.Core.Helpers;
using Storeya.Core.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.Linq;
using System.Text;


namespace Storeya.Core.Entities
{
    public enum SystemEventTypes
    {

        //FbFlowSteps?
        //CpFlowSteps?
        None = 0,
        UserActivity = 1,
        AppStore = 2,
        Payments = 3,
        IPNCalls = 4,
        AdwordsCampaigns = 5,
        AccountManagement = 6,
        AdCopyCreate = 7,
        AdminActions = 9,
        AdminReport = 11,
        BHReport = 13,
        AccountFlow = 14,
    }

    public enum SystemEventActions
    {
        None = 0,
        CreateUser = 11,
        SaveDetails = 12,  //(required for CRM)
        CancelAccount = 13,
        DeleteShop = 14,   //(for multiple shops)
        LoginToBO = 15,
        GaAccountConnected = 16,
        FullListOfEmailsRequested = 17,
        ShopDomainSpecified = 18,
        TBCountryChange = 19,

        AppInstalled = 21,
        AppDisabled = 22,
        AppRemoved = 23,

        EmailSent = 25,

        GoPremium = 31,
        NewPaidAccount = 32,
        Recurring = 33,
        Cancellation = 34,
        SubscriptionChange = 35, //update used
        Other = 36,
        ChargeFailed = 37,
        OneTimeCharge = 38,
        Refund = 39,
        CampaignPaused = 40,
        CampaignsLoaded = 41,
        AddOrDuplicateExtension = 42,
        ShopifyAppInstallation = 43,
        DecreaseDailySpentTo20Days = 44,
        ChannelBudgetChanged = 45,
        Payments_TbUpdated = 50,
        AMChange = 60,
        AdCopyCreated = 70,
        InvoceEmail = 80,
        SubscriptionCancelEmail = 85,
        DashboardShowConversionRate = 86,
        ShowLastPaymentRemainingBudget = 87,
        AutoChargeOnDesiredRoas = 88,

        BenchmarkImagesOptDownloadAll = 90,
        BenchmarkImagesOptimizeAll = 91,
        BenchmarkImagesOptRestoreAll = 92,
        BenchmarkImagesOptRestoreAllOrigin = 93,
        BenchmarkImagesOptimizePage = 94,
        BenchmarkImagesOptimizeFinalReportPage = 95,
        BenchmarkReportView = 96,

        UpgradeEmailsSent = 110,
        CheckoutPage = 111,
        CheckoutACHPage = 112,
        OverwriteValueUpdated = 113,
        BudgetValueUpdated = 114,
        ThreeDsEnabled = 115,
        ThreeDsPass = 116,
        ThreeDsFailed = 117,
        AddedInternalTaskMcAccountReverify = 118,
        LastVisited = 119,
        ShoppingCampaignsUploaded = 120,
        FirstImpression = 121,
        FirstSale = 122,
<<<<<<< .mine
        UserRankUpdatedFromShopify = 123
||||||| .r35978
=======

        UserRankUpdatedFromShopify = 123,
>>>>>>> .r35988
    }

    public class SystemEventHelper
    {
        public static bool AddIfNotExists(int shopID, AppTypes appType, SystemEventTypes eventType, SystemEventActions action, string label = null, decimal? value = 1, int? daysToSkipIfExists = null, bool useValueToCount = false)
        {
            var se = GetLatest(shopID, appType, eventType, action, daysToSkipIfExists);
            if (se == null)
            {
                Add(shopID, (int)appType, eventType, action, label, value);
                return true;
            }
            if (useValueToCount)
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                se.Value += se.Value;
                db.SaveChanges();
            }
            return false;
        }
        public static void Add(int shopID, int? appID, SystemEventTypes eventType, SystemEventActions action, string label = null, decimal? value = 1)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(x => x.ID == shopID).SingleOrDefault();
            int userID = 0;
            if (shop != null)
            {
                userID = shop.UserID.Value;

            }
            Add(userID, shopID, appID, eventType, action, label, value);
        }


        public static void Add(int userID, int shopID, int? appID, SystemEventTypes eventType, SystemEventActions action, string label = null, decimal? value = 1, int? agreeID = null, int? aggregateInMiutus = null)
        {
            try
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                bool add = true;
                if (aggregateInMiutus.HasValue)
                {
                    DateTime dateTime = DateTime.Now.AddMinutes(-aggregateInMiutus.Value);
                    var sysEvents = db.SystemEvents.Where(e => e.UserID == userID && e.ShopID == shopID && e.EventType == (int)eventType && e.Action == (int)action && e.InsertedAt > dateTime);
                    if (sysEvents.Count() > 0)
                    {
                        var sysEvent = sysEvents.OrderByDescending(e => e.InsertedAt).First();
                        sysEvent.Label = $"Updated:{DateTime.Now}";
                        add = false;
                    }
                }
                if (add)
                {
                    db.SystemEvents.Add(new SystemEvent() { ShopID = shopID, AppID = appID, UserID = userID, EventType = (int)eventType, Action = (int)action, Label = label, Value = value, AgreeID = agreeID, InsertedAt = DateTime.Now });
                }

                db.SaveChanges();
            }
            catch (DbEntityValidationException e)
            {
                var newException = new FormattedDbEntityValidationException(e);
                Log4NetLogger.Error(string.Format("Failed to add SystemEvent. EventType: {0}, EventAction: {1}, Label: {2}, Value: {3}, App: {4}", eventType.ToString(), action.ToString(), label, value, (appID ?? 0)), newException, shopID);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to add SystemEvent. EventType: {0}, EventAction: {1}, Label: {2}, Value: {3}, App: {4}", eventType.ToString(), action.ToString(), label, value, (appID ?? 0)), ex, shopID);
            }
        }
        public static List<SystemEvent> Get(DateTime fromDate, DateTime toDate, AppTypes? appType, SystemEventTypes eventType)
        {
            int? appTypeValue = null;
            if (appType != null)
            {
                appTypeValue = (int)appType;
            }
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<SystemEvent> systemEvents = db.SystemEvents.Where(e => e.AppID == appTypeValue
                 && e.EventType == (int)eventType
                 && e.InsertedAt >= fromDate
                 && e.InsertedAt <= toDate
                ).OrderBy(e => e.ID).ToList();
            return systemEvents;
        }
        public static List<SystemEvent> Get(DateTime fromDate, DateTime toDate, AppTypes? appType, SystemEventTypes eventType, SystemEventActions? eventAction)
        {
            int? appTypeValue = null;
            if (appType != null)
            {
                appTypeValue = (int)appType;
            }

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<SystemEvent> systemEvents = db.SystemEvents.Where(e => e.AppID == appTypeValue
                 && e.EventType == (int)eventType
                 && e.Action == (int)eventAction.Value
                 && e.InsertedAt >= fromDate
                 && e.InsertedAt <= toDate
                ).OrderBy(e => e.ID).ToList();
            return systemEvents;
        }

        public static List<SystemEvent> Get(int lastEventWeCarredAbout, AppTypes appType, SystemEventTypes eventType)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<SystemEvent> systemEvents = db.SystemEvents.Where(e => e.AppID == (int)appType
                && e.EventType == (int)eventType
                && e.ID > lastEventWeCarredAbout
                ).OrderBy(e => e.ID).ToList();
            return systemEvents;
        }


        public static List<SystemEvent> Get(int startFromID, SystemEventTypes eventType, SystemEventActions eventAction)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<SystemEvent> systemEvents = db.SystemEvents.Where(e => e.EventType == (int)eventType
                && e.Action == (int)eventAction
                && e.ID > startFromID
                ).OrderBy(e => e.ID).ToList();
            return systemEvents;
        }


        public static List<SystemEvent> Get(int startFromID, AppTypes appType, SystemEventTypes eventType, SystemEventActions eventAction)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<SystemEvent> systemEvents = db.SystemEvents.Where(e => e.AppID == (int)appType
                && e.EventType == (int)eventType
                && e.Action == (int)eventAction
                && e.ID > startFromID
                ).OrderBy(e => e.ID).ToList();
            return systemEvents;
        }

        public static SystemEvent GetLatest(int shopId, AppTypes appType, SystemEventTypes eventType, SystemEventActions eventAction, int? daysToSkipIfExists = null)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            SystemEvent systemEvent = db.SystemEvents.Where(e => e.AppID == (int)appType
                && e.EventType == (int)eventType
                && e.Action == (int)eventAction
                && e.ShopID == shopId
                && ((daysToSkipIfExists.HasValue && e.InsertedAt.HasValue) ? DbFunctions.AddDays(e.InsertedAt.Value, daysToSkipIfExists.Value) > DateTime.Now : 1 == 1)
                ).OrderByDescending(e => e.ID).FirstOrDefault();
            return systemEvent;
        }
        public static SystemEvent Get(int Id)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            return db.SystemEvents.SingleOrDefault(e => e.ID == Id);
        }
        public static List<SystemEvent> GetLastMonthEvents()
        {
            DateTime month_ago = DateTime.Now.AddMonths(-1);
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<SystemEvent> systemEvents = db.SystemEvents.Where(e => e.InsertedAt > month_ago).OrderBy(e => e.ID).ToList(); ;
            return systemEvents;
        }

        public static SystemEvent SetAgreeId(int Id, int? agreeId = null)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            var systemEvent = db.SystemEvents.SingleOrDefault(e => e.ID == Id);
            if (systemEvent == null)
            {
                return null;
            }
            systemEvent.AgreeID = agreeId;
            db.SaveChanges();
            return systemEvent;
        }
        //public static int GetLastEventID()
        //{
        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    var eventID = db.SystemEvents.Max(e => e.ID);
        //    return eventID;
        //}

        //[Obsolete("Try not to use this. It's not optimized from DB point of view.")]
        //public static List<SystemEvent> GetEventsWithActionInDateRange(DateTime fromDate, DateTime to, SystemEventActions? action)
        //{
        //    int actions = (action.HasValue ? (int)action : 0);
        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    var events = db.SystemEvents.Where
        //              (e => (action == null ? 1 == 1 : e.Action == actions)
        //              && (e.InsertedAt <= to && e.InsertedAt > fromDate)).ToList();
        //    return events;
        //}
    }

}
