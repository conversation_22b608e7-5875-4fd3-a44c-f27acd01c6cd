﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;

namespace Storeya.Core.Entities.Tabs
{
    public class TabAppSettings
    {
        public static string TwitterAppID
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_TwitterAppID");
            }
        }
        public static string TwitterAppSecret
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_TwitterAppSecret");
            }
        }

        public static string PinterestAppID
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_PinterestAppID");
            }
        }
        public static string PinterestAppSecret
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_PinterestAppSecret");
            }
        }

        public static string YouTubeAppID
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_YouTubeAppID");
            }
        }
        public static string YouTubeAppSecret
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_YouTubeAppSecret");
            }
        }

        public static string InstagramAppID
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_InstagramAppID");
            }
        }
        public static string InstagramAppSecret
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_InstagramAppSecret");
            }
        }

        public static string FanGateAppID
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_FanGateAppID");
            }
        }
        public static string FanGateAppSecret
        {
            get
            {
                return ConfigHelper.GetValue("Tabs_FanGateAppSecret");
            }
        }
    }
    
}
