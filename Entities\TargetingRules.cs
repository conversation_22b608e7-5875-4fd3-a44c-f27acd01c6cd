﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Newtonsoft.Json;

using Storeya.Core.Helpers;
using Storeya.Core.Models.AppStore;

namespace Storeya.Core.Entities
{
    public enum TargetingRules
    {
        None = 0,
        Include = 1,
        Exclude = 2
    }

    public class TargetingRuleObject
    {
        public int TargetingType { get; set; }
        public string Value { get; set; }
    }

    public static class TargetingRulesManager
    {

        public static List<TargetingRule> GetAllTargetingRules(int shopID, int appType, int widgetID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<TargetingRule> existingTargetingRules = db.TargetingRules.Where(tr => tr.ShopID == shopID
                                       && tr.AppType == appType
                                       && tr.WidgetID == widgetID).ToList();

            return existingTargetingRules;
        }


        public static string SetTargetingRuleVauesByType(List<TargetingRule> rules, int targetingRuleType)
        {
            string targetingRuleValues = null;

            List<TargetingRule> targetingRulesOfType = rules.Where(tr => tr.TargetingType == targetingRuleType).ToList();

            if (targetingRulesOfType != null && targetingRulesOfType.Count > 0)
            {
                StringBuilder builder = new StringBuilder();
                foreach (var item in targetingRulesOfType)
                {
                    //string absolutePath = GetAbsolutePath(item.Value);
                    //builder.Append(absolutePath);
                    builder.Append(item.Value);
                    builder.Append('|');
                }
                targetingRuleValues = builder.ToString();
            }
            return targetingRuleValues;
        }

       
        
        
        public static string GetTargetingRuleVauesByType(int shopID, int appType, int widgetID, int targetingRuleType)
        {
            string targetingRuleValues = null;
            List<TargetingRule> existingTargetingRules = GetExistingTargetingRules(shopID, appType, widgetID, targetingRuleType);
            if (existingTargetingRules != null && existingTargetingRules.Count > 0)
            {
                StringBuilder builder = new StringBuilder();
                foreach (var item in existingTargetingRules)
                {
                    //string absolutePath = GetAbsolutePath(item.Value);
                    //builder.Append(absolutePath);
                    builder.Append(item.Value);
                    builder.Append('|');
                }
                targetingRuleValues = builder.ToString();
            }
            return targetingRuleValues;
        }


        public static string GetAbsolutePath(string url)
        {
            if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                Uri uri = new Uri(url);
                return uri.AbsolutePath;
            }

            return url;
        }
        
        public static string GetExistingTargetingRuleAsJson(int shopID, int appType, int widgetID, int? targetingRuleType = null)
        {
            string json = null;
            List<TargetingRule> existingTargetingRules = GetExistingTargetingRules(shopID, appType, widgetID, targetingRuleType);

            List<TargetingRuleObject> targetingRules = null;
            if (existingTargetingRules != null && existingTargetingRules.Count > 0)
            {
                targetingRules = new List<TargetingRuleObject>();
                foreach (var item in existingTargetingRules)
                {           
                    targetingRules.Add(new TargetingRuleObject() { TargetingType = (item.TargetingType ?? 0), Value = item.Value });
                }
                json = GetJson(targetingRules);
            }
            return json;
        }

        public static List<TargetingRule> GetExistingTargetingRules(string encodedShopID, int appType, int widgetID, int? targetingRuleType = null)
        {
            int shopID = SequenceHelper.Decode(encodedShopID);
            List<TargetingRule> existingTargetingRules = GetExistingTargetingRules(shopID, appType, widgetID, targetingRuleType);
            return existingTargetingRules;
        }

        public static List<TargetingRule> GetExistingTargetingRules(int shopID, int appType, int widgetID, int? targetingRuleType = null)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<TargetingRule> existingTargetingRules = db.TargetingRules.Where(tr => tr.ShopID == shopID
                                       && tr.AppType == appType
                                       && tr.WidgetID == widgetID).ToList();

            if (targetingRuleType != null)
            {
                existingTargetingRules = existingTargetingRules.Where(tr => tr.TargetingType == targetingRuleType.Value).ToList();
            } 

            return existingTargetingRules;
        }
        
        
        public static string GetJson(List<TargetingRuleObject> targetingRules)
        {
            string json = JsonConvert.SerializeObject(targetingRules);
            return json;
        }


        public static List<TargetingRuleObject> Parse(string json, int shopID, int appType, int widgetID)
        {
            List<TargetingRuleObject> targetingRules = null;
            try
            {
                targetingRules = ParseJson(json);
                if (targetingRules != null)
                {
                    targetingRules = AddHttpIfNeeded(targetingRules);                    
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to parse targetingRules for {0} ID: {1}", AppStoreManager.GetAppByID(appType).AppName, widgetID), ex, shopID);
            }
            return targetingRules;
        }


        public static void ParseAndSave(string json, int shopID, int appType, int widgetID)
        {
            List<TargetingRuleObject> targetingRules = null;
            try
            {
                targetingRules = ParseJson(json);          
                if (targetingRules != null)
                {
                    targetingRules = AddHttpIfNeeded(targetingRules);
                    SaveTargetingRulesInDB(targetingRules, shopID, appType, widgetID);
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to parse targetingRules for {0} ID: {1}", AppStoreManager.GetAppByID(appType).AppName, widgetID), ex, shopID);
            }
        }

        private static List<TargetingRuleObject> AddHttpIfNeeded(List<TargetingRuleObject> targetingRules)
        {
            foreach (var item in targetingRules)
            {
                item.Value = UrlPathHelper.BuildValidUri(item.Value);
            }
            return targetingRules;
        }


        public static void RemoveAllTargetingRules(int shopID, int appType, int widgetID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
    
            var targetingRulesToDelete = db.TargetingRules.Where(tr => tr.ShopID == shopID
                                       && tr.AppType == appType
                                       && tr.WidgetID == widgetID).ToList();

            foreach (var targetingRuleToDelete in targetingRulesToDelete)
            {
                db.TargetingRules.Remove(targetingRuleToDelete);
            }

            db.SaveChanges();
        }


        public static List<TargetingRuleObject> ParseJson(string json)
        {
            List<TargetingRuleObject> targetingRules = JsonConvert.DeserializeObject<List<TargetingRuleObject>>(json);
            return targetingRules;
        }

        public static void SaveTargetingRulesInDB(List<TargetingRuleObject> targetingRules, int shopID, int appType, int widgetID)
        {
            try
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                List<TargetingRule> existingTargetingRules = GetExistingTargetingRules(shopID, appType, widgetID);

                foreach (TargetingRuleObject item in targetingRules)
                {
                    if (existingTargetingRules == null || !existingTargetingRules.Where(t => t.TargetingType == item.TargetingType && t.Value == item.Value).Any())
                    {
                        TargetingRule newTargetingRule = new TargetingRule() { AppType = appType, ShopID = shopID, WidgetID = widgetID, TargetingType = item.TargetingType, Value = item.Value };
                        if (!string.IsNullOrEmpty(newTargetingRule.Value))
                        {
                            db.TargetingRules.Add(newTargetingRule);
                            db.SaveChanges();
                        }
                    }
                }

                if (existingTargetingRules != null && existingTargetingRules.Count > 0)
                {
                    foreach (TargetingRule item in existingTargetingRules)
                    {
                        if (!targetingRules.Where(tr => tr.TargetingType == item.TargetingType && tr.Value == item.Value).Any())
                        {
                            db.TargetingRules.Remove(item);
                            db.SaveChanges();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to save targetingRules for {0} ID: {1}", AppStoreManager.GetAppByID(appType).AppName, widgetID), ex, shopID);      
            }
        }
    }
}
