﻿using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.Linq;
using Storeya.Core.Helpers;

namespace Storeya.Core.Entities
{
    public class TbShop
    {
        public int ShopID { get; set; }
        public DateTime? LastPaymentDate { get; set; }
        public DateTime? CancelledAt { get; set; }
        public DateTime? PaidTill
        {
            get
            {
                if (this.CancelledAt != null && this.LastPaymentDate != null)
                {
                    if (this.LastPaymentDate > this.CancelledAt)
                    {
                        EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Cancellation date is early then last payment date.", $"Please check {EmailHelper.GetBoLinkHref(this.ShopID)}");
                    }
                    return new DateTime(Math.Min(this.CancelledAt.Value.Ticks, this.LastPaymentDate.Value.AddMonths(1).Ticks));
                }

                return this.LastPaymentDate.Value.AddMonths(1);
            }
        }
        //public long Plan { get; set; }
        public string AdWordsAccountID { get; set; }

        public decimal? CouponValue { get; set; }
        public bool AutoCharge { get; set; }

        public static List<TbShop> ActiveTBs(int shopID = 0, int minShopID = 0)
        {
            var apps = TrafficBoostersDbHelper.GetDefaultBudgetActive().ToList();
            if (shopID > 0)
            {
                apps = apps.Where(c => c.ShopID == shopID).ToList();
            }
            else if (minShopID > 0)
            {
                apps = apps.Where(c => c.ShopID > shopID).ToList();
            }
            List<TbShop> shops = apps.AsEnumerable().Select(tb => new TbShop()
            {
                AdWordsAccountID = tb.AdWordsAccount,
                LastPaymentDate = TbSettingsHelper.GetLastBillingDate(tb),
                ShopID = tb.ShopID.Value,
                //Plan = ((tb.OverwritePurchsedAmount != null && tb.OverwritePurchsedAmount.Value != 0) ? tb.OverwritePurchsedAmount.Value : (tb.PurchasedAmount ?? 0)),
                CancelledAt = tb.CancelledAt,
                AutoCharge = tb.AutoChargeOnDesiredRoas.HasValue && tb.AutoChargeOnDesiredRoas == 1 ? true : false,
                CouponValue = TbSettingsHelper.GetCouponValue(tb),
                AccountBudget = TbSettingsHelper.GetBillingAmount(tb)
                //CouponValue =  TbSettingsHelper. //ChangeBudgetIfHasCoupon(tb),
                //AccountBudget = TbSettingsHelper.GetBillingAmount(tb)

            }).OrderBy(s => s.ShopID).ToList();

            return shops;
        }
        public decimal AccountBudget { get; set; }

    }
}
