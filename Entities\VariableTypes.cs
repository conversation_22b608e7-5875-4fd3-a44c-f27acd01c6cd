﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Entities
{
    //naming [event name]_[id user]_[action performed]

    public enum VariableTypes
    {
        TB_Payment_Change = 1,
        //TB_Initial_Report = 2,
        CP_Awaiting_For_EmailsList = 3,
        GA_Initial_Stats = 4,
        CRM_Events = 5,
        TB_Last_Payment_Event_Update = 6,
        GaAccountConnected_AccountID_BidModified = 7,
        User_SignupLocation_Updated = 8,
        ShopDomainSpecified = 9,
        CampaignsLoaded_EventID_DissaprovedAdsValidated = 10,
        //TB_AddOrDuplicateExtensions = 11,
        TbUpgraded_EventID_ShopUpgraded = 12,
        TB_Last_GaAccountConnected_Initial_Stats = 13,
        //TB_Country_Change = 14,
        ShopifyInstallation_EventID_PriceExtensionCreated = 15,
        //DecreaseDailySpentTo20Days = 16,
        IpnIDAddedToChartMogulApi = 17,
        ShopifyInstallation_EventID_CdrmCandidateValidated = 18,
        CampaignsLoaded_EventID_CdrmCandidateValidated = 19,
        TB_Last_GaAccountConnected_Fix_Stats = 20,
        ChannelBudgetChanged_EventID_AWUpdated = 21,
        CampaignsLoaded_EventID_AdCopyGenerated = 22,
        Email_Customer_Invoice = 23,
        Email_Customer_Invoice_From_Admin = 24,
        PaymentTransactions_Related_Action = 25,
        Email_Customer_Cancel_Subscription = 26,
        PaymentFailed = 27,
        ShoppingCampaignsUploaded = 28,
        First_Impression_Events = 29,
        First_Sale_Events = 30,

        BHUsers_ShopID_CheckedToSendToSales = 31,
        TBUsers_TbID_CheckedToSendToSales = 32,
        ZendeskCommentDateInSeconds = 33,
        ShopifyAppInstalled_ShopifyAppID_UserRankUpdated = 34,
        Increase_Traffic_Sale_Events = 35,
        PDShopifyReview = 36,
        LeadesForSales_ID_CheckForVideoCandidates = 37,
        LeadesForSales_TbID_NotPaidTbCheck = 38,
        CPShopifyReview = 39,
        BHShopifyReview = 40,
        TBShopifyReview = 41,
        MCFeedsListItemsCreated = 42,
        ShopifyConnectedShops_ID_CollectData = 43,
        SystemEvent_ID_SubscribeWebhook = 44
    }
}
