﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;
using System.Text.RegularExpressions;
using System.Net;

namespace Storeya.Core.Entities.WebsiteAnalyser
{
    public enum ResultStatuses
    {
        Ok,
        Warning,
        Error
    }

    public class ValidationRule
    {
        public string GroupName { get; set; }
    }

    public class ValidationResult
    {
        //ValidationRule Rule {get; set;}
        public ResultStatuses Status { get; set; }
        public string GroupName { get; set; }
        public int GroupID { get; set; }

        public string OutputHtml { get; set; }
        public string Value { get; set; }
    }

    public class Website
    {
        public Website(string url, string content)
        {
            this.Content = content;
            this.Url = url;
        }

        public Website(string url) 
        {
            string content = GetPageContent(url);
            this.Content = content;
            this.Url = url;
        }

        public string Domain { get; set; }
        public string Url { get; set; }
        public string Content { get; set; }

        public static string GetPageContent(string url)
        {
            try
            {
                WebClient client = new WebClient();
                return client.DownloadString(url);
            }
            catch (Exception ex)
            {
                return "Error:: " + ex.ToString();
            }
        }
    }
}
