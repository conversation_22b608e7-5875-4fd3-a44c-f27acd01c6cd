﻿using Amazon.S3.Model;
using Storeya.Core.Models.AdWords;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Storeya.Core.Models.DataProviders.SquareApiProvider;

namespace Storeya.Core.Helpers
{
    public class AIShopInfoHelper
    {
        public enum AIShopInfo_STATUS
        {
            INITIATED = 1,
            LAUNCHED = 2,
            INPROGRES = 3,
            DONE = 4,
            FAILED = 5
        }
        public string URL { get; set; }
        private StoreYaEntities db { get; set; }
        public AIShopInfoHelper(string url)
        {
            URL = url.ToLower();
            db = DataHelper.GetStoreYaEntities();
        }
        public static List<AIShopInfo> GetPendingAndInitiatedShops()
        {
            var db = DataHelper.GetStoreYaEntities();
            return db.AIShopInfoes.Where(x => x.Status == (int)AIShopInfo_STATUS.INITIATED || x.Status == (int)AIShopInfo_STATUS.INPROGRES || x.Status == (int)AIShopInfo_STATUS.LAUNCHED).ToList();
        }

        public AIShopInfo GeFromDB()
        {
            var infos = db.AIShopInfoes.Where(x => x.Domain == URL).Select(x => x).OrderByDescending(x => x.ID);
            if (infos != null)
            {
                if (infos.Count() == 1)
                {
                    return infos.Single();
                }
                else if (infos.Count() > 1)
                {
                    AIShopInfo firstItem = null;

                    //get first and remove duplicated
                    foreach (var item in infos)
                    {
                        if (firstItem == null)
                        {
                            firstItem = item;
                        }
                        else
                        {
                            item.Domain = item.Domain + "_Duplication";
                        }
                    }
                    //remove duplicates
                    db.SaveChanges();
                    return firstItem;
                }
            }
            return null;
        }

        public AIShopInfo GeAiInfoIfExistInDB(out bool failedToGet)
        {
            failedToGet = false;
            try
            {
                AIShopInfo shopInfo = db.AIShopInfoes.Where(x => x.Domain == URL).SingleOrDefault();
                return shopInfo;
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "Failed to get AIShopInfo", $"Url: {URL}" + " " + ex.ToString());
                failedToGet = true;
            }
            return null;
        }
        public static AIShopInfo GetExistingAiInfoIfExistOrCreateNew(string url, bool renameIfmultipulRecordsExists = true)
        {
            AIShopInfoHelper aIShopInfoHelper = new AIShopInfoHelper(url);
            var shopInfo = aIShopInfoHelper.GeFromDB();
            if (shopInfo == null)
            {
                shopInfo = aIShopInfoHelper.CreateNewAIShopInfo();
            }
            return shopInfo;

            //AIShopInfoHelper aIShopInfoHelper = new AIShopInfoHelper(url);
            //AIShopInfo shopInfo = aIShopInfoHelper.GeAiInfoIfExistInDB(out bool failedToGet);
            //if (shopInfo == null && failedToGet)
            //{
            //    if (aIShopInfoHelper.HasMoreThanOneRecord() && renameIfmultipulRecordsExists)
            //    {
            //        aIShopInfoHelper.RenameExistiongRecords();
            //    }
            //}
            //if (shopInfo == null)
            //{
            //    shopInfo = aIShopInfoHelper.CreateNewAIShopInfo();
            //}
            //return shopInfo;
        }

        public AIShopInfo CreateNewAIShopInfo()
        {
            AIShopInfo aIShopInfo = new AIShopInfo();
            aIShopInfo.Domain = URL;
            aIShopInfo.Status = (int)AIShopInfo_STATUS.INITIATED;
            aIShopInfo.InsertedAt = DateTime.Now;
            db.AIShopInfoes.Add(aIShopInfo);
            db.SaveChanges();
            return aIShopInfo;
        }

        private void RenameExistiongRecords()
        {
            foreach (var item in db.AIShopInfoes.Where(x => x.Domain == URL).ToList())
            {
                item.Domain = item.Domain + "_Duplication";
            }
            db.SaveChanges();
        }

        public bool HasMoreThanOneRecord()
        {
            int counter = db.AIShopInfoes.Where(x => x.Domain == URL).Count();
            return counter > 1;
        }


        public static AIShopInfo UpdateStatusToInitiatedAndResetResult(AIShopInfo shopInfo)
        {
            try
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                AIShopInfo shopInfoToChange = db.AIShopInfoes.Where(x => x.ID == shopInfo.ID).SingleOrDefault();
                if (shopInfoToChange == null)
                {
                    shopInfoToChange = db.AIShopInfoes.Where(x => x.Domain == shopInfo.Domain).Single();
                }
                shopInfoToChange.Status = (int)AIShopInfo_STATUS.INITIATED;
                shopInfoToChange.UpdatedAt = DateTime.Now;
                shopInfoToChange.Keywords = null;
                shopInfoToChange.TargetGender = null;
                shopInfoToChange.TargetCountries = null;
                shopInfoToChange.SiteSummary = null;
                shopInfoToChange.TargetAges = null;
                db.SaveChanges();
                return shopInfoToChange;
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "UpdateStatusToInitiated failed", $"URL: {shopInfo.Domain}, ID: {shopInfo.ID} {ex.ToString()}");
            }
            return shopInfo;
        }

        public static void UpdateStatusIfObjectNotMissingLink(AIShopInfo aIShop, AIShopInfo_STATUS status)
        {
            var db = DataHelper.GetStoreYaEntities();
            aIShop.Status = (int)status;
            db.SaveChanges();
            Console.WriteLine($"Status was changed to {status.ToString()}");
        }
    }
}
