using System.Collections.Generic;

using Storeya.Core.Entities;

namespace Storeya.Core.Helpers
{
    public class AdExtractorManager
    {
        public void UpdateLastSystemVariablesEvents()
        {
            int lastAdCopyGenerated = SystemVarHelper.GetValue(VariableTypes.CampaignsLoaded_EventID_AdCopyGenerated);
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<SystemEvent> systemEvents = SystemEventHelper.Get(lastAdCopyGenerated, SystemEventTypes.UserActivity, SystemEventActions.ShopDomainSpecified);

        }
    }
}