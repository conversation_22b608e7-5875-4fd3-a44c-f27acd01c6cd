﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Policy;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.UI;
using HtmlAgilityPack;
using Storeya.Core.Entities;
using Storeya.Core.Models.AdWords;
using Storeya.Core.Models.DataProviders;

namespace Storeya.Core.Helpers
{
    public class AdExtractorManager
    {
        public void UpdateLastSystemVariablesEvents()
        {
            int lastAdCopyGenerated = SystemVarHelper.GetValue(VariableTypes.CampaignsLoaded_EventID_AdCopyGenerated);
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<SystemEvent> systemEvents = SystemEventHelper.Get(lastAdCopyGenerated, SystemEventTypes.UserActivity, SystemEventActions.ShopDomainSpecified);

        }

        public class SitelinksCrawlerService
        {
            private SitelinksConfigurationModel _configuration;
            private IHtmlLoaderService _htmlLoaderService;
            private BaseParserCr _linksParser;

            private string _url;
            private string _html;

            public SitelinksCrawlerService(string url)
            {
                _url = url;
                _configuration = SitelinksConfigurationModel.GetDefault();
                _linksParser = new LinksParser(url);
                _htmlLoaderService = new HtmlLoaderService(url);
            }

            public SitelinksCrawlerService(string url, SitelinksConfigurationModel config)
            {
                _url = url;
                _configuration = config ?? SitelinksConfigurationModel.GetDefault();
                _linksParser = new LinksParser(url);
                _htmlLoaderService = new HtmlLoaderService(url);
            }

            public SitelinksCrawlerService(string url, string html, SitelinksConfigurationModel config = null) : this(url, config)
            {
                _html = html;
            }

            public SitelinksCrawlingModel StartCrawling()
            {
                var crawlingResult = new SitelinksCrawlingModel();

                if (MarketplaceUrlHelper.IsMarketplace(_url))
                {
                    return crawlingResult;
                }

                if (String.IsNullOrEmpty(_html))
                {
                    _htmlLoaderService.Url = _url;

                    _html = _htmlLoaderService.Load();
                }

                _linksParser.Url = _url;
                _linksParser.Html = _html;

                //var appTopLinks = _linksParser.Parse() as LinksParseModel;

                LinksParseModel appTopLinks = null;
                if (_linksParser.Html.Contains("X-Wix-Meta-Site-Id"))
                {
                    appTopLinks = _linksParser.ParseWix() as LinksParseModel;
                    if (appTopLinks.Values == null)
                    {
                        appTopLinks = _linksParser.Parse() as LinksParseModel;
                    }
                }
                else
                {
                    appTopLinks = _linksParser.Parse() as LinksParseModel;
                }


                if (appTopLinks == null)
                {
                    return crawlingResult;
                }

                appTopLinks.Values = appTopLinks.Values.Take(_configuration.Top).ToList();

                //remove special characters
                appTopLinks.Values.ToList().ForEach(value =>
                {
                    value.Title = value.Title.Replace("&nbsp", " ");
                    value.Title = Regex.Replace(value.Title, @"[^a-zA-Z0-9\s'&$£]", "");
                    value.Title = Regex.Replace(value.Title, @"\s+", " ");
                    value.Title = CultureInfo.CurrentCulture.TextInfo.ToTitleCase(value.Title).Trim(' ');
                });


                //remove only numbers
                appTopLinks.Values.Where(link =>
                {
                    return !Regex.IsMatch(link.Title, @"[a-zA-Z]");

                }).ToList().ForEach(item => appTopLinks.Values.Remove(item));

                //remove negative from Titles
                appTopLinks.Values.Where(link =>
                {
                    var negativeTitles = _configuration.NegativeKeywords.Where(k => char.IsUpper(k[0])).Select(k => k);
                    return negativeTitles.Any(negativeKeyword => link.Title.ContainsIgnoreCase(negativeKeyword));

                }).ToList().ForEach(item => appTopLinks.Values.Remove(item));

                //remove negative from links
                appTopLinks.Values.Where(link =>
                {
                    var nagetiveForLinks = _configuration.NegativeKeywords.Where(k => !char.IsUpper(k[0])).Select(k => k);
                    return nagetiveForLinks.Any(negativeKeyword => link.Url.ContainsIgnoreCase(negativeKeyword));
                }).ToList().ForEach(item => appTopLinks.Values.Remove(item));

                //remove long values
                appTopLinks.Values.Where(link =>
                {
                    return String.IsNullOrWhiteSpace(link.Title) || link.Title.Length > 25 || link.Title.Length < 3;
                }).ToList().ForEach(item => appTopLinks.Values.Remove(item));

                //remove duplicates
                appTopLinks.Values = appTopLinks.Values.GroupBy(p => p.Title).Select(g => g.First()).ToList();

                Console.Write(string.Format("Found {0} sitelinks to be added", appTopLinks.Values.Count));

                crawlingResult.ParsingResults = new[] { appTopLinks };

                return crawlingResult;
            }
        }



        public static class ContainsIgnoreCaseExtention
        {
            public static bool ContainsIgnoreCase(this string source, string value)
            {
                return ContainsIgnoreCase(source, value, CultureInfo.InvariantCulture);
            }

            public static bool ContainsIgnoreCase(this string source, string value, CultureInfo culture)
            {
                return culture.CompareInfo.IndexOf(source, value, CompareOptions.IgnoreCase) >= 0;
            }
        }

        public class LinksParseModel : BaseParseModel
        {
            public new ICollection<LinkModel> Values { get; set; }
        }

        public class LinkModel
        {
            public string Url { get; set; }

            public string Title { get; set; }
        }
        public class BaseParseModel
        {
            public ICollection<string> Values { get; set; }

            public string Url { get; set; }
        }
        public class SitelinksCrawlingModel //: BaseCrawlingModel
        {
            public ICollection<LinksParseModel> ParsingResults { get; set; }

        }
        public class SitelinksConfigurationModel
        {
            public IEnumerable<string> NegativeKeywords { get; set; }

            public int Top { get; set; }

            public static SitelinksConfigurationModel GetDefault()
            {
                var config = new SitelinksConfigurationModel
                {
                    Top = 30,
                    NegativeKeywords = new[]
                    {
"About",
"Account",
"Affiliate",
"All prices are",
"Blog",
"Cart",
"Check out",
"Checkout",
"collections/frontpage",
"Compare",
"Contact",
"Continue",
"Email",
"etsy",
"Home",
"I want in",
"ILI Inc.",
"Info",
"javascript",
"Log in",
"Login",
"Login",
"Password",
"Policy",
"register",
"Return",
"rss",
"Search",
"Service",
"Shop now",
"Shop Our Instagram",
"Sign in",
"Sign up",
"Termes & Conditions",
"Terms",
"Text",
"Username",
"View More",
"Size ",
"mailto",
"tel:",
"Wishlist",
"Read More",
"Details",
"Select Options",
"Sitemap",
"Select",
"Facebook",
"Linkedin",
"Admin",
"Help",
"Sizing Chart",
"Size Chart",
"Sold Out",
"English",
"Spanish",
"German",
"French",
"Italian ",
"Tweet",
"/product/",
"Basket",
"/cart",
"Mail Support",
"/basket",
"email",
"News",
"Franais",
"Etina",
"Espaol",
"Italiano",
"Deutsch",
"Nederlands",
"Slovenina",
"Bosanski",
"Hrvatski",
"Español",
"Slovenčina",
"Ελληνικά",
"Српски",
"한국어",
"中文",
"简体",
"‏العربية‏",
"हिंदी",
"Afrikaans",
"Tiếng Việt",
"Magyar",
"Dansk",
"Bahasa Indonesia",
"Gaeilge",
"Bahasa Melayu",
"Português",
"Русский",
"ภาษาไทย",
"Suomi",
"Svenska",
"日本語",
"currency",
"Currency",
"Register",
"create_account",
"createaccount",
"Privacy",
"/products/",
"Domov",
"Items",
"contact-us",
"contactus",
"Click Here",
"Here",
"Amazon",
"English",
"Orders",
"Order Status",
"thank-you-page",
"Quotes",
"Pay Invoices",
"Favourites",
"Resolution Centre",
"Edit My Address Book",
"Products",
"Shop"
                }
                };

                return config;
            }
        }
        public class HtmlLoaderService : IHtmlLoaderService
        {
            public string Url { get; set; }

            public HtmlLoaderService(string url)
            {
                Url = url;
            }

            public string Load()
            {
                try
                {
                    var s = ProviderFactory.GetResponseText(Url);
                    return FormatingHelper.ReplaceSpecialHtmlSymbols(s);

                    //HttpWebRequest http = (HttpWebRequest)WebRequest.Create(Url);
                    //http.UseDefaultCredentials = true;
                    //http.UserAgent =
                    //    @"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36";
                    //HttpWebResponse response = (HttpWebResponse)http.GetResponse();
                    //using (StreamReader sr = new StreamReader(response.GetResponseStream()))
                    //{
                    //    var html = sr.ReadToEnd();
                    //    return FormatingHelper.ReplaceSpecialHtmlSymbols(html);
                    //}
                }
                catch (Exception ex)
                {
                    var e = ex;
                    return "";
                }

            }
        }
        public interface IHtmlLoaderService
        {
            string Url { get; set; }

            string Load();
        }
        public class LinksParser : BaseParserCr
        {
            public LinksParser()
            {

            }

            public LinksParser(string url)
            {
                this.Url = url;
            }

            public LinksParser(string url, string html)
                : base(url, html)
            {
            }


            public override BaseParseModel ParseWix()
            {
                var titleModel = new LinksParseModel();
                titleModel.Url = this.Url;

                this.HtmlDocument.LoadHtml(this.Html);

                var documentNode = this.HtmlDocument.DocumentNode;

                var scripts = this.HtmlDocument.DocumentNode.Descendants()
                                 .Where(n => n.Name == "script").ToList();
                //.First().InnerText;
                foreach (var script in scripts)
                {
                    string script_content = script.InnerText;

                    if (!string.IsNullOrEmpty(script_content) && script_content.Contains("\"pageList\":{\""))
                    {
                        string from = ",\"pageList\":{\"pages\":[";
                        int index_1 = script_content.IndexOf(from);

                        string cutted = script_content.Remove(0, index_1 + from.Length);

                        string to = "],\"mainPageId\"";
                        int index_2 = cutted.IndexOf(to);

                        string json_string = cutted.Remove(index_2);

                        string json_string_1 = "{\"pages\":[" + json_string + "]}";

                        dynamic parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(json_string_1);

                        foreach (var item in parsedJson.pages)
                        {
                            if (titleModel.Values == null)
                                titleModel.Values = new List<LinkModel>();

                            LinkModel linkData = new LinkModel() { Title = item.title, Url = titleModel.Url.TrimEnd('/') + "/" + item.pageUriSEO };

                            if (!titleModel.Values.Where(x => x.Url == linkData.Url).Any())
                                titleModel.Values.Add(linkData);
                        }
                    }
                }

                return titleModel;
            }

            public override BaseParseModel Parse()
            {
                this.HtmlDocument.LoadHtml(this.Html);

                var documentNode = this.GetElementToParse();

                var anchorNodes = documentNode.SelectNodes("//a");

                var titleModel = new LinksParseModel();
                titleModel.Url = this.Url;
                if (anchorNodes == null)
                {
                    titleModel.Values = new List<LinkModel>();
                    return titleModel;
                }

                var uri = new Uri(Url);
                var homeUri = uri.Scheme + "://" + uri.Authority;
                var domain = uri.Authority.Replace("www.", "");

                var links =
                    anchorNodes.Select(a =>
                    {
                        var urlAttr = a.Attributes.FirstOrDefault(
                            attr => attr.Name.Equals("href", StringComparison.InvariantCultureIgnoreCase));
                        var url = urlAttr != null ? urlAttr.Value.ToLower().Trim(' ').TrimEnd('/') : null;
                        return new LinkModel
                        {
                            Url = url,
                            Title = a.InnerText
                        };
                    }).ToList();

                links.Where(link => String.IsNullOrEmpty(link.Url) || link.Url.StartsWith("#")).ToList().ForEach(link => links.Remove(link));

                links = links.Distinct(new LinksEqualityComparer()).Select(link =>
                {
                    if (link.Url.StartsWith("http://", StringComparison.InvariantCultureIgnoreCase) ||
                        link.Url.StartsWith("https://", StringComparison.InvariantCultureIgnoreCase))
                    {
                        return link;
                    }
                    else
                    {
                        StringBuilder sb = new StringBuilder();
                        if (link.Url.StartsWith("/"))
                        {
                            sb.Append(homeUri);
                            sb.Append(link.Url);
                        }
                        else
                        {
                            sb.Append(homeUri);
                            sb.Append("/");
                            sb.Append(link.Url);
                        }

                        link.Url = sb.ToString(); //homeUri.AppendPathSegments(link.Url).ToString();
                        return link;
                    }
                }).ToList();

                links.Where(link => !link.Url.ContainsIgnoreCase(domain)).ToList().ForEach(link => links.Remove(link));

                titleModel.Values = links.Distinct(new LinksEqualityComparer()).ToList();

                return titleModel;
            }
        }
        public abstract class BaseParserCr
        {
            protected readonly HtmlDocument HtmlDocument;

            public string Url { get; set; }
            public string Html { get; set; }

            protected BaseParserCr()
            {
                HtmlDocument = new HtmlDocument();
                HtmlDocument.OptionFixNestedTags = true;
            }

            protected BaseParserCr(string url) : this()
            {
                Url = url;
            }

            protected BaseParserCr(string url, string html) : this(url)
            {
                Html = html;
                HtmlDocument.LoadHtml(Html);
            }

            public abstract BaseParseModel Parse();
            public abstract BaseParseModel ParseWix();

            protected virtual HtmlNode GetElementToParse()
            {
                HtmlDocument.LoadHtml(Html);
                var documentNode = HtmlDocument.DocumentNode;

                documentNode.Descendants().Where(n =>
                    String.Equals(n.Name, "script", StringComparison.InvariantCultureIgnoreCase) ||
                    String.Equals(n.Name, "style", StringComparison.InvariantCultureIgnoreCase) ||
                    String.Equals(n.Name, "meta", StringComparison.InvariantCultureIgnoreCase) ||
                    String.Equals(n.Name, "link", StringComparison.InvariantCultureIgnoreCase))
                    .ToList().ForEach(n => n.Remove());

                documentNode.Descendants().Where(n =>
                    String.Equals(n.Name, "img", StringComparison.InvariantCultureIgnoreCase))
                    .ToList().ForEach(n => n.Remove());

                documentNode.Descendants()
                    .Where(n => n.NodeType == HtmlNodeType.Comment)
                    .ToList()
                    .ForEach(n => n.Remove());

                return documentNode;
            }
        }
        public static class FormatingHelper
        {
            public static string ToTitleCase(string str)
            {
                return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(str.ToLower());
            }
            public static string ReplaceSpecialHtmlSymbols(string encodedHtml)
            {
                string text = encodedHtml;

                //HTML and XHTML processors must support the five special characters listed in the table below:

                if (encodedHtml.Contains("&#34;"))        //  "       &#34;  or &#034; or  &quot;      double quotes
                    text = text.Replace("&#34;", "\"");
                if (encodedHtml.Contains("&#034;"))
                    text = text.Replace("&#034;", "\"");
                if (encodedHtml.Contains("&quot;"))
                    text = text.Replace("&quot;", "\"");

                if (encodedHtml.Contains("&#39;"))        //  '       &#39; or   &apos;          apostrophe
                    text = text.Replace("&#39;", "'");

                if (encodedHtml.Contains("&#039;"))        //  '       &#39; or   &apos;          apostrophe
                    text = text.Replace("&#039;", "'");

                if (encodedHtml.Contains("&apos;"))
                    text = text.Replace("&apos;", "'");

                if (encodedHtml.Contains("&#38;"))        // &        &#38;  or &#038; or &amp;    ampersand
                    text = text.Replace("&#38;", "&");
                if (encodedHtml.Contains("&#038;"))
                    text = text.Replace("&#038;", "&");
                if (encodedHtml.Contains("&amp;"))
                    text = text.Replace("&amp;", "&");

                if (encodedHtml.Contains("&#60;"))        // <       &#60; or &lt;              	less-than
                    text = text.Replace("&#60;", "<");
                if (encodedHtml.Contains("&lt;"))
                    text = text.Replace("&lt;", "<");

                if (encodedHtml.Contains("&#62;"))         //  >       &#62;  or &gt;                greater-than
                    text = text.Replace("&#62;", ">");
                if (encodedHtml.Contains("&gt;"))
                    text = text.Replace("&gt;", ">");


                // ISO 8859-1 Symbols

                if (encodedHtml.Contains("&#134;"))        //   &#160;  or &nbsp;     non-breaking space
                    text = text.Replace("&#134;", "å");

                if (encodedHtml.Contains("&#160;"))        //   &#160;  or &nbsp;     non-breaking space
                    text = text.Replace("&#160;", " ");
                if (encodedHtml.Contains("&nbsp;"))
                    text = text.Replace("&nbsp;", " ");

                if (encodedHtml.Contains("&#161;"))   //¡	&#161;	&iexcl;	inverted exclamation mark
                    text = text.Replace("&#161;", "¡");
                if (encodedHtml.Contains("&iexcl;"))
                    text = text.Replace("&iexcl;", "¡");

                if (encodedHtml.Contains("&#162;"))    //¢	&#162;	&cent;	cent
                    text = text.Replace("&#162;", "¢");
                if (encodedHtml.Contains("&cent;"))
                    text = text.Replace("&cent;", "¢");

                if (encodedHtml.Contains("&#163;"))    //£	&#163;	&pound;	pound
                    text = text.Replace("&#163;", "£");
                if (encodedHtml.Contains("&pound;"))
                    text = text.Replace("&pound;", "£");

                if (encodedHtml.Contains("&#8362;"))    //₪ shekel
                    text = text.Replace("&#8362;", "₪");


                if (encodedHtml.Contains("&#164;"))     //¤	&#164;	&curren;	currency
                    text = text.Replace("&#164;", "¤");
                if (encodedHtml.Contains("&curren;"))
                    text = text.Replace("&curren;", "¤");

                if (encodedHtml.Contains("&#165;"))    //¥	&#165;	&yen;	yen
                    text = text.Replace("&#165;", "¥");
                if (encodedHtml.Contains("&yen;"))
                    text = text.Replace("&yen;", "¥");

                if (encodedHtml.Contains("&#166;"))     // ¦	&#166;	&brvbar;	broken vertical bar
                    text = text.Replace("&#166;", "¦");
                if (encodedHtml.Contains("&brvbar;"))
                    text = text.Replace("&brvbar;", "¦");

                if (encodedHtml.Contains("&#167;"))     // §	&#167;	&sect;	section
                    text = text.Replace("&#167;", "§");
                if (encodedHtml.Contains("&sect;"))
                    text = text.Replace("&sect;", "§");

                if (encodedHtml.Contains("&#168;"))     // ¨	&#168;	&uml;	spacing diaeresis
                    text = text.Replace("&#168;", "¨");
                if (encodedHtml.Contains("&uml;"))
                    text = text.Replace("&uml;", "¨");

                if (encodedHtml.Contains("&#169;"))     // ©	&#169;	&copy;	copyright
                    text = text.Replace("&#169;", "©");
                if (encodedHtml.Contains("&copy;"))
                    text = text.Replace("&copy;", "©");

                if (encodedHtml.Contains("&#170;"))     // ª	&#170;	&ordf;	feminine ordinal indicator
                    text = text.Replace("&#170;", "ª");
                if (encodedHtml.Contains("&ordf;"))
                    text = text.Replace("&ordf;", "ª");

                if (encodedHtml.Contains("&#171;"))     //  «	&#171;	&laquo;	angle quotation mark (left)
                    text = text.Replace("&#171;", "«");
                if (encodedHtml.Contains("&laquo;"))
                    text = text.Replace("&laquo;", "«");

                if (encodedHtml.Contains("&#172;"))   // ¬	&#172;	&not;	negation
                    text = text.Replace("&#172;", "¬");
                if (encodedHtml.Contains("&not;"))
                    text = text.Replace("&not;", "¬");

                if (encodedHtml.Contains("&#173;"))     //	&#173;	&shy;	soft hyphen 
                    text = text.Replace("&#173;", "");
                if (encodedHtml.Contains("&shy;"))
                    text = text.Replace("&shy;", "");

                if (encodedHtml.Contains("&#174;"))     // ®	&#174;	&reg;	registered trademark
                    text = text.Replace("&#174;", "®");
                if (encodedHtml.Contains("&reg;"))
                    text = text.Replace("&reg;", "®");

                if (encodedHtml.Contains("&#175;"))     // ¯	&#175;	&macr;	spacing macron
                    text = text.Replace("&#175;", "¯");
                if (encodedHtml.Contains("&macr;"))
                    text = text.Replace("&macr;", "¯");

                if (encodedHtml.Contains("&#176;"))     // °	&#176;	&deg;	degree
                    text = text.Replace("&#176;", "°");
                if (encodedHtml.Contains("&deg;"))
                    text = text.Replace("&deg;", "°");

                if (encodedHtml.Contains("&#177;"))     // ±	&#177;	&plusmn;	plus-or-minus
                    text = text.Replace("&#177;", "±");
                if (encodedHtml.Contains("&plusmn;"))
                    text = text.Replace("&plusmn;", "±");

                if (encodedHtml.Contains("&#178;"))     // ²	&#178;	&sup2;	superscript 2
                    text = text.Replace("&#178;", "²");
                if (encodedHtml.Contains("&sup2;"))
                    text = text.Replace("&sup2;", "²");

                if (encodedHtml.Contains("&#179;"))     // ³	&#179;	&sup3;	superscript 3
                    text = text.Replace("&#179;", "³");
                if (encodedHtml.Contains("&sup3;"))
                    text = text.Replace("&sup3;", "³");

                if (encodedHtml.Contains("&#180;"))     // ´	&#180;	&acute;	spacing acute
                    text = text.Replace("&#180;", "´");
                if (encodedHtml.Contains("&acute;"))
                    text = text.Replace("&acute;", "´");

                if (encodedHtml.Contains("&#181;"))     // µ	&#181;	&micro;	micro
                    text = text.Replace("&#181;", "µ");
                if (encodedHtml.Contains("&micro;"))
                    text = text.Replace("&micro;", "µ");

                if (encodedHtml.Contains("&#182;"))     // ¶	&#182;	&para;	paragraph
                    text = text.Replace("&#182;", "¶");
                if (encodedHtml.Contains("&para;"))
                    text = text.Replace("&para;", "¶");

                if (encodedHtml.Contains("&#183;"))     // ·	&#183;	&middot;	middle dot
                    text = text.Replace("&#183;", "·");
                if (encodedHtml.Contains("&middot;"))
                    text = text.Replace("&middot;", "·");

                if (encodedHtml.Contains("&#184;"))     // ¸	&#184;	&cedil;	spacing cedilla
                    text = text.Replace("&#184;", "¸");
                if (encodedHtml.Contains("&cedil;"))
                    text = text.Replace("&cedil;", "¸");

                if (encodedHtml.Contains("&#185;"))     // ¹	&#185;	&sup1;	superscript 1
                    text = text.Replace("&#185;", "¹");
                if (encodedHtml.Contains("&sup1;"))
                    text = text.Replace("&sup1;", "¹");

                if (encodedHtml.Contains("&#186;"))    // º	&#186;	&ordm;	masculine ordinal indicator
                    text = text.Replace("&#186;", "º");
                if (encodedHtml.Contains("&ordm;"))
                    text = text.Replace("&ordm;", "º");

                if (encodedHtml.Contains("&#187;"))     // »	&#187;	&raquo;	angle quotation mark (right)
                    text = text.Replace("&#187;", "»");
                if (encodedHtml.Contains("&raquo;"))
                    text = text.Replace("&raquo;", "»");

                if (encodedHtml.Contains("&#188;"))     // ¼	&#188;	&frac14;	fraction 1/4
                    text = text.Replace("&#188;", "¼");
                if (encodedHtml.Contains("&frac14;"))
                    text = text.Replace("&frac14;", "¼");

                if (encodedHtml.Contains("&#189;"))     // ½	&#189;	&frac12;	fraction 1/2
                    text = text.Replace("&#189;", "½");
                if (encodedHtml.Contains("&frac12;"))
                    text = text.Replace("&frac12;", "½");

                if (encodedHtml.Contains("&#190;"))     // ¾	&#190;	&frac34;	fraction 3/4
                    text = text.Replace("&#190;", "¾");
                if (encodedHtml.Contains("&frac34;"))
                    text = text.Replace("&frac34;", "¾");

                if (encodedHtml.Contains("&#191;"))     // ¿	&#191;	&iquest;	inverted question mark
                    text = text.Replace("&#191;", "¿");
                if (encodedHtml.Contains("&iquest;"))
                    text = text.Replace("&iquest;", "¿");

                if (encodedHtml.Contains("&#215;"))     // ×	&#215;	&times;	multiplication
                    text = text.Replace("&#215;", "×");
                if (encodedHtml.Contains("&times;"))
                    text = text.Replace("&times;", "×");

                if (encodedHtml.Contains("&#247;"))     // ÷	&#247;	&divide;	division
                    text = text.Replace("&#247;", "÷");
                if (encodedHtml.Contains("&divide;"))
                    text = text.Replace("&divide;", "÷");


                //  ISO 8859-1 Characters

                if (encodedHtml.Contains("&#192;"))     // À	&#192;	&Agrave;	capital a, grave accent
                    text = text.Replace("&#192;", "À");
                if (encodedHtml.Contains("&Agrave;"))
                    text = text.Replace("&Agrave;", "À");

                if (encodedHtml.Contains("&#193;"))     // Á	&#193;	&Aacute;	capital a, acute accent
                    text = text.Replace("&#193;", "Á");
                if (encodedHtml.Contains("&Aacute;"))
                    text = text.Replace("&Aacute;", "Á");

                if (encodedHtml.Contains("&#194;"))     // Â	&#194;	&Acirc;	capital a, circumflex accent
                    text = text.Replace("&#194;", "Â");
                if (encodedHtml.Contains("&Acirc;"))
                    text = text.Replace("&Acirc;", "Â");

                if (encodedHtml.Contains("&#195;"))     // Ã	&#195;	&Atilde;	capital a, tilde
                    text = text.Replace("&#195;", "Ã");
                if (encodedHtml.Contains("&Atilde;"))
                    text = text.Replace("&Atilde;", "Ã");

                if (encodedHtml.Contains("&#196;"))     // Ä	&#196;	&Auml;	capital a, umlaut mark
                    text = text.Replace("&#196;", "Ä");
                if (encodedHtml.Contains("&Auml;"))
                    text = text.Replace("&Auml;", "Ä");

                if (encodedHtml.Contains("&#197;"))     // Å	&#197;	&Aring;	capital a, ring
                    text = text.Replace("&#197;", "Å");
                if (encodedHtml.Contains("&Aring;"))
                    text = text.Replace("&Aring;", "Å");

                if (encodedHtml.Contains("&#198;"))     // Æ	&#198;	&AElig;	capital ae
                    text = text.Replace("&#198;", "Æ");
                if (encodedHtml.Contains("&AElig;"))
                    text = text.Replace("&AElig;", "Æ");

                if (encodedHtml.Contains("&#199;"))     // Ç	&#199;	&Ccedil;	capital c, cedilla
                    text = text.Replace("&#199;", "Ç");
                if (encodedHtml.Contains("&Ccedil;"))
                    text = text.Replace("&Ccedil;", "Ç");

                if (encodedHtml.Contains("&#200;"))     // È	&#200;	&Egrave;	capital e, grave accent
                    text = text.Replace("&#200;", "È");
                if (encodedHtml.Contains("&Egrave;"))
                    text = text.Replace("&Egrave;", "È");

                if (encodedHtml.Contains("&#201;"))     // É	&#201;	&Eacute;	capital e, acute accent
                    text = text.Replace("&#201;", "É");
                if (encodedHtml.Contains("&Eacute;"))
                    text = text.Replace("&Eacute;", "É");

                if (encodedHtml.Contains("&#202;"))    // Ê	&#202;	&Ecirc;	capital e, circumflex accent
                    text = text.Replace("&#202;", "Ê");
                if (encodedHtml.Contains("&Ecirc;"))
                    text = text.Replace("&Ecirc;", "Ê");

                if (encodedHtml.Contains("&#203;"))     // Ë	&#203;	&Euml;	capital e, umlaut mark
                    text = text.Replace("&#203;", "Ë");
                if (encodedHtml.Contains("&Euml;"))
                    text = text.Replace("&Euml;", "Ë");

                if (encodedHtml.Contains("&#204;"))     // Ì	&#204;	&Igrave;	capital i, grave accent
                    text = text.Replace("&#204;", "Ì");
                if (encodedHtml.Contains("&Igrave;"))
                    text = text.Replace("&Igrave;", "Ì");

                if (encodedHtml.Contains("&#205;"))     // Í	&#205;	&Iacute;	capital i, acute accent
                    text = text.Replace("&#205;", "Í");
                if (encodedHtml.Contains("&Iacute;"))
                    text = text.Replace("&Iacute;", "Í");

                if (encodedHtml.Contains("&#206;"))     // Î	&#206;	&Icirc;	capital i, circumflex accent
                    text = text.Replace("&#206;", "Î");
                if (encodedHtml.Contains("&Icirc;"))
                    text = text.Replace("&Icirc;", "Î");

                if (encodedHtml.Contains("&#207;"))     // Ï	&#207;	&Iuml;	capital i, umlaut mark
                    text = text.Replace("&#207;", "Ï");
                if (encodedHtml.Contains("&Iuml;"))
                    text = text.Replace("&Iuml;", "Ï");

                if (encodedHtml.Contains("&#208;"))     // Ð	&#208;	&ETH;	capital eth, Icelandic
                    text = text.Replace("&#208;", "Ð");
                if (encodedHtml.Contains("&ETH;"))
                    text = text.Replace("&ETH;", "Ð");

                if (encodedHtml.Contains("&#209;"))     // Ñ	&#209;	&Ntilde;	capital n, tilde
                    text = text.Replace("&#209;", "Ñ");
                if (encodedHtml.Contains("&Ntilde;"))
                    text = text.Replace("&Ntilde;", "Ñ");

                if (encodedHtml.Contains("&#210;"))     // Ò	&#210;	&Ograve;	capital o, grave accent
                    text = text.Replace("&#210;", "Ò");
                if (encodedHtml.Contains("&Ograve;"))
                    text = text.Replace("&Ograve;", "Ò");

                if (encodedHtml.Contains("&#211;"))     // Ó	&#211;	&Oacute;	capital o, acute accent
                    text = text.Replace("&#211;", "Ó");
                if (encodedHtml.Contains("&Oacute;"))
                    text = text.Replace("&Oacute;", "Ó");

                if (encodedHtml.Contains("&#212;"))     // Ô	&#212;	&Ocirc;	capital o, circumflex accent
                    text = text.Replace("&#212;", "Ô");
                if (encodedHtml.Contains("&Ocirc;"))
                    text = text.Replace("&Ocirc;", "Ô");

                if (encodedHtml.Contains("&#213;"))    // Õ	&#213;	&Otilde;	capital o, tilde
                    text = text.Replace("&#213;", "Õ");
                if (encodedHtml.Contains("&Otilde;"))
                    text = text.Replace("&Otilde;", "Õ");

                if (encodedHtml.Contains("&#214;"))    // Ö	&#214;	&Ouml;	capital o, umlaut mark
                    text = text.Replace("&#214;", "Ö");
                if (encodedHtml.Contains("&Ouml;"))
                    text = text.Replace("&Ouml;", "Ö");

                if (encodedHtml.Contains("&#216;"))     // Ø	&#216;	&Oslash;	capital o, slash
                    text = text.Replace("&#216;", "Ø");
                if (encodedHtml.Contains("&Oslash;"))
                    text = text.Replace("&Oslash;", "Ø");

                if (encodedHtml.Contains("&#217;"))     // Ù	&#217;	&Ugrave;	capital u, grave accent
                    text = text.Replace("&#217;", "Ù");
                if (encodedHtml.Contains("&Ugrave;"))
                    text = text.Replace("&Ugrave;", "Ù");

                if (encodedHtml.Contains("&#218;"))     // Ú	&#218;	&Uacute;	capital u, acute accent
                    text = text.Replace("&#218;", "Ú");
                if (encodedHtml.Contains("&Uacute;"))
                    text = text.Replace("&Uacute;", "Ú");

                if (encodedHtml.Contains("&#219;"))     // Û	&#219;	&Ucirc;	capital u, circumflex accent
                    text = text.Replace("&#219;", "Û");
                if (encodedHtml.Contains("&Ucirc;"))
                    text = text.Replace("&Ucirc;", "Û");

                if (encodedHtml.Contains("&#220;"))     // Ü	&#220;	&Uuml;	capital u, umlaut mark
                    text = text.Replace("&#220;", "Ü");
                if (encodedHtml.Contains("&Uuml;"))
                    text = text.Replace("&Uuml;", "Ü");

                if (encodedHtml.Contains("&#221;"))     // Ý	&#221;	&Yacute;	capital y, acute accent
                    text = text.Replace("&#221;", "Ý");
                if (encodedHtml.Contains("&Yacute;"))
                    text = text.Replace("&Yacute;", "Ý");

                if (encodedHtml.Contains("&#222;"))     // Þ	&#222;	&THORN;	capital THORN, Icelandic
                    text = text.Replace("&#222;", "Þ");
                if (encodedHtml.Contains("&THORN;"))
                    text = text.Replace("&THORN;", "Þ");

                if (encodedHtml.Contains("&#223;"))     // ß	&#223;	&szlig;	small sharp s, German
                    text = text.Replace("&#223;", "ß");
                if (encodedHtml.Contains("&szlig;"))
                    text = text.Replace("&szlig;", "ß");

                if (encodedHtml.Contains("&#224;"))     // à	&#224;	&agrave;	small a, grave accent
                    text = text.Replace("&#224;", "à");
                if (encodedHtml.Contains("&agrave;"))
                    text = text.Replace("&agrave;", "à");

                if (encodedHtml.Contains("&#225;"))     // á	&#225;	&aacute;	small a, acute accent
                    text = text.Replace("&#225;", "á");
                if (encodedHtml.Contains("&aacute;"))
                    text = text.Replace("&aacute;", "á");

                if (encodedHtml.Contains("&#226;"))     // â	&#226;	&acirc;	small a, circumflex accent
                    text = text.Replace("&#226;", "â");
                if (encodedHtml.Contains("&acirc;"))
                    text = text.Replace("&acirc;", "â");

                if (encodedHtml.Contains("&#227;"))     // ã	&#227;	&atilde;	small a, tilde
                    text = text.Replace("&#227;", "ã");
                if (encodedHtml.Contains("&atilde;"))
                    text = text.Replace("&atilde;", "ã");

                if (encodedHtml.Contains("&#228;"))     // ä	&#228;	&auml;	small a, umlaut mark
                    text = text.Replace("&#228;", "ä");
                if (encodedHtml.Contains("&auml;"))
                    text = text.Replace("&auml;", "ä");

                if (encodedHtml.Contains("&#229;"))     // å	&#229;	&aring;	small a, ring
                    text = text.Replace("&#229;", "å");
                if (encodedHtml.Contains("&aring;"))
                    text = text.Replace("&aring;", "å");

                if (encodedHtml.Contains("&#230;"))     // æ	&#230;	&aelig;	small ae
                    text = text.Replace("&#230;", "æ");
                if (encodedHtml.Contains("&aelig;"))
                    text = text.Replace("&aelig;", "æ");

                if (encodedHtml.Contains("&#508;"))     // æ	&#230;	&aelig;	small ae
                    text = text.Replace("&#508;", "Ǽ");
                if (encodedHtml.Contains("&#509;"))
                    text = text.Replace("&#509;", "ǽ");

                if (encodedHtml.Contains("&#231;"))    // ç	&#231;	&ccedil;	small c, cedilla
                    text = text.Replace("&#231;", "ç");
                if (encodedHtml.Contains("&ccedil;"))
                    text = text.Replace("&ccedil;", "ç");

                if (encodedHtml.Contains("&#232;"))     // è	&#232;	&egrave;	small e, grave accent
                    text = text.Replace("&#232;", "è");
                if (encodedHtml.Contains("&egrave;"))
                    text = text.Replace("&egrave;", "è");

                if (encodedHtml.Contains("&#233;"))     // é	&#233;	&eacute;	small e, acute accent
                    text = text.Replace("&#233;", "é");
                if (encodedHtml.Contains("&eacute;"))
                    text = text.Replace("&eacute;", "é");

                if (encodedHtml.Contains("&#234;"))     // ê	&#234;	&ecirc;	small e, circumflex accent
                    text = text.Replace("&#234;", "ê");
                if (encodedHtml.Contains("&ecirc;"))
                    text = text.Replace("&ecirc;", "ê");

                if (encodedHtml.Contains("&#235;"))     // ë	&#235;	&euml;	small e, umlaut mark
                    text = text.Replace("&#235;", "ë");
                if (encodedHtml.Contains("&euml;"))
                    text = text.Replace("&euml;", "ë");

                if (encodedHtml.Contains("&#236;"))     // ì	&#236;	&igrave;	small i, grave accent
                    text = text.Replace("&#236;", "ì");
                if (encodedHtml.Contains("&igrave;"))
                    text = text.Replace("&igrave;", "ì");

                if (encodedHtml.Contains("&#237;"))     // í	&#237;	&iacute;	small i, acute accent
                    text = text.Replace("&#237;", "í");
                if (encodedHtml.Contains("&iacute;"))
                    text = text.Replace("&iacute;", "í");

                if (encodedHtml.Contains("&#238;"))     // î	&#238;	&icirc;	small i, circumflex accent
                    text = text.Replace("&#238;", "î");
                if (encodedHtml.Contains("&icirc;"))
                    text = text.Replace("&icirc;", "î");

                if (encodedHtml.Contains("&#239;"))     // ï	&#239;	&iuml;	small i, umlaut mark
                    text = text.Replace("&#239;", "ï");
                if (encodedHtml.Contains("&iuml;"))
                    text = text.Replace("&iuml;", "ï");

                if (encodedHtml.Contains("&#240;"))    // ð	&#240;	&eth;	small eth, Icelandic
                    text = text.Replace("&#240;", "ð");
                if (encodedHtml.Contains("&eth;"))
                    text = text.Replace("&eth;", "ð");

                if (encodedHtml.Contains("&#241;"))     // ñ	&#241;	&ntilde;	small n, tilde
                    text = text.Replace("&#241;", "ñ");
                if (encodedHtml.Contains("&ntilde;"))
                    text = text.Replace("&ntilde;", "ñ");

                if (encodedHtml.Contains("&#242;"))     // ò	&#242;	&ograve;	small o, grave accent
                    text = text.Replace("&#242;", "ò");
                if (encodedHtml.Contains("&ograve;"))
                    text = text.Replace("&ograve;", "ò");

                if (encodedHtml.Contains("&#243;"))     // ó	&#243;	&oacute;	small o, acute accent
                    text = text.Replace("&#243;", "ó");
                if (encodedHtml.Contains("&oacute;"))
                    text = text.Replace("&oacute;", "ó");

                if (encodedHtml.Contains("&#244;"))     // ô	&#244;	&ocirc;	small o, circumflex accent
                    text = text.Replace("&#244;", "ô");
                if (encodedHtml.Contains("&ocirc;"))
                    text = text.Replace("&ocirc;", "ô");

                if (encodedHtml.Contains("&#245;"))     // õ	&#245;	&otilde;	small o, tilde
                    text = text.Replace("&#245;", "õ");
                if (encodedHtml.Contains("&otilde;"))
                    text = text.Replace("&otilde;", "õ");

                if (encodedHtml.Contains("&#246;"))     // ö	&#246;	&ouml;	small o, umlaut mark
                    text = text.Replace("&#246;", "ö");
                if (encodedHtml.Contains("&ouml;"))
                    text = text.Replace("&ouml;", "ö");

                if (encodedHtml.Contains("&#248;"))   // ø	&#248;	&oslash;	small o, slash
                    text = text.Replace("&#248;", "ø");
                if (encodedHtml.Contains("&oslash;"))
                    text = text.Replace("&oslash;", "ø");

                if (encodedHtml.Contains("&#332;"))
                    text = text.Replace("&#332;", "Ō");

                if (encodedHtml.Contains("&#333;"))
                    text = text.Replace("&#333;", "ō");

                if (encodedHtml.Contains("&#334;"))
                    text = text.Replace("&#334;", "Ŏ");

                if (encodedHtml.Contains("&#335;"))
                    text = text.Replace("&#335;", "ŏ");

                if (encodedHtml.Contains("&#336;"))
                    text = text.Replace("&#336;", "Ő");

                if (encodedHtml.Contains("&#337;"))
                    text = text.Replace("&#337;", "ő");

                if (encodedHtml.Contains("&#510;"))
                    text = text.Replace("&#510;", "Ǿ");

                if (encodedHtml.Contains("&#511;"))
                    text = text.Replace("&#511;", "ǿ");

                if (encodedHtml.Contains("&#256;"))
                    text = text.Replace("&#256;", "Ā");

                if (encodedHtml.Contains("&#257;"))
                    text = text.Replace("&#257;", "ā");

                if (encodedHtml.Contains("&#258;"))
                    text = text.Replace("&#258;", "Ă");

                if (encodedHtml.Contains("&#259;"))
                    text = text.Replace("&#259;", "ă");

                if (encodedHtml.Contains("&#260"))
                    text = text.Replace("&#260", "Ą");

                if (encodedHtml.Contains("&#261;"))
                    text = text.Replace("&#261;", "ą");

                if (encodedHtml.Contains("&#478;"))
                    text = text.Replace("&#478;", "Ǟ");

                if (encodedHtml.Contains("&#479;"))
                    text = text.Replace("&#479;", "ǟ");

                if (encodedHtml.Contains("&#506;"))
                    text = text.Replace("&#506;", "Ǻ");

                if (encodedHtml.Contains("&#507;"))
                    text = text.Replace("&#507;", "ǻ");


                if (encodedHtml.Contains("&#249;"))     // ù	&#249;	&ugrave;	small u, grave accent
                    text = text.Replace("&#249;", "ù");
                if (encodedHtml.Contains("&ugrave;"))
                    text = text.Replace("&ugrave;", "ù");

                if (encodedHtml.Contains("&#250;"))     // ú	&#250;	&uacute;	small u, acute accent
                    text = text.Replace("&#250;", "ú");
                if (encodedHtml.Contains("&uacute;"))
                    text = text.Replace("&uacute;", "ú");

                if (encodedHtml.Contains("&#251;"))     // û	&#251;	&ucirc;	small u, circumflex accent
                    text = text.Replace("&#251;", "û");
                if (encodedHtml.Contains("&ucirc;"))
                    text = text.Replace("&ucirc;", "û");

                if (encodedHtml.Contains("&#252;"))    // ü	&#252;	&uuml;	small u, umlaut mark
                    text = text.Replace("&#252;", "ü");
                if (encodedHtml.Contains("&uuml;"))
                    text = text.Replace("&uuml;", "ü");

                if (encodedHtml.Contains("&#253;"))     // ý	&#253;	&yacute;	small y, acute accent
                    text = text.Replace("&#253;", "ý");
                if (encodedHtml.Contains("&yacute;"))
                    text = text.Replace("&yacute;", "ý");

                if (encodedHtml.Contains("&#254;"))     // þ	&#254;	&thorn;	small thorn, Icelandic
                    text = text.Replace("&#254;", "þ");
                if (encodedHtml.Contains("&thorn;"))
                    text = text.Replace("&thorn;", "þ");

                if (encodedHtml.Contains("&#255;"))     // ÿ	&#255;	&yuml;	small y, umlaut mark
                    text = text.Replace("&#255;", "ÿ");
                if (encodedHtml.Contains("&yuml;"))
                    text = text.Replace("&yuml;", "ÿ");


                // Other Entities Supported by HTML

                if (encodedHtml.Contains("&#338;"))  // Œ	&#338;	&OElig;	capital ligature OE
                    text = text.Replace("&#338;", "Œ");
                if (encodedHtml.Contains("&OElig;"))
                    text = text.Replace("&OElig;", "Œ");

                if (encodedHtml.Contains("&#339;"))     // œ	&#339;	&oelig;	small ligature oe
                    text = text.Replace("&#339;", "œ");
                if (encodedHtml.Contains("&oelig;"))
                    text = text.Replace("&oelig;", "œ");

                if (encodedHtml.Contains("&#352;"))     // Š	&#352;	&Scaron;	capital S with caron
                    text = text.Replace("&#352;", "Š");
                if (encodedHtml.Contains("&Scaron;"))
                    text = text.Replace("&Scaron;", "Š");

                if (encodedHtml.Contains("&#353;"))   // š	&#353;	&scaron;	small S with caron
                    text = text.Replace("&#353;", "š");
                if (encodedHtml.Contains("&scaron;"))
                    text = text.Replace("&scaron;", "š");

                if (encodedHtml.Contains("&#376;"))      // Ÿ	&#376;	&Yuml;	capital Y with diaeres
                    text = text.Replace("&#376;", "Ÿ");
                if (encodedHtml.Contains("&Yuml;"))
                    text = text.Replace("&Yuml;", "Ÿ");

                if (encodedHtml.Contains("&#402;"))     // ƒ	&#402;	&fnof;	f with hook
                    text = text.Replace("&#402;", "ƒ");
                if (encodedHtml.Contains("&fnof;"))
                    text = text.Replace("&fnof;", "ƒ");

                if (encodedHtml.Contains("&#710;"))     // ˆ	&#710;	&circ;	modifier letter circumflex accent
                    text = text.Replace("&#710;", "ˆ");
                if (encodedHtml.Contains("&circ;"))
                    text = text.Replace("&circ;", "ˆ");

                if (encodedHtml.Contains("&#732;"))     // ˜	&#732;	&tilde;	small tilde
                    text = text.Replace("&#732;", "˜");
                if (encodedHtml.Contains("&tilde;"))
                    text = text.Replace("&tilde;", "˜");

                if (encodedHtml.Contains("&#8194;")) //	&#8194;	&ensp;	en space
                    text = text.Replace("&#8194;", " ");
                if (encodedHtml.Contains("&ensp;"))
                    text = text.Replace("&ensp;", " ");

                if (encodedHtml.Contains("&#8195;")) // &#8195;	&emsp;	em space
                    text = text.Replace("&#8195;", " ");
                if (encodedHtml.Contains("&emsp;"))
                    text = text.Replace("&emsp;", " ");

                if (encodedHtml.Contains("&#8201;")) // 	&#8201;	&thinsp;	thin space
                    text = text.Replace("&#8201;", " ");
                if (encodedHtml.Contains("&thinsp;"))
                    text = text.Replace("&thinsp;", " ");

                if (encodedHtml.Contains("&#8204;"))//	&#8204;	&zwnj;	zero width non-joiner   Несоединяющий символ нулевой длины
                    text = text.Replace("&#8204;", "");
                if (encodedHtml.Contains("&zwnj;"))
                    text = text.Replace("&zwnj;", "");

                if (encodedHtml.Contains("&#8205;"))  // &#8205;	&zwj;	zero width joiner Соединяющий символ нулевой длины
                    text = text.Replace("&#8205;", "");
                if (encodedHtml.Contains("&zwj;"))
                    text = text.Replace("&zwj;", "");

                if (encodedHtml.Contains("&#8211;"))       // –	&#8211;	&ndash;	en dash
                    text = text.Replace("&#8211;", "–");
                if (encodedHtml.Contains("&ndash;"))
                    text = text.Replace("&ndash;", "–");

                if (encodedHtml.Contains("&#8212;"))      // —	&#8212;	&mdash;	em dash
                    text = text.Replace("&#8212;", "—");
                if (encodedHtml.Contains("&mdash;"))
                    text = text.Replace("&mdash;", "—");


                if (encodedHtml.Contains("&#8216;"))      // ‘	&#8216;	&lsquo;	left single quotation mark
                    text = text.Replace("&#8216;", "‘");
                if (encodedHtml.Contains("&lsquo;"))
                    text = text.Replace("&lsquo;", "‘");

                if (encodedHtml.Contains("&#8217;"))      // ’	&#8217;	&rsquo;	right single quotation mark
                    text = text.Replace("&#8217;", "’");
                if (encodedHtml.Contains("&rsquo;"))
                    text = text.Replace("&rsquo;", "’");

                if (encodedHtml.Contains("&#8218;"))//‚	&#8218;	&sbquo;	single low-9 quotation mark
                    text = text.Replace("&#8218;", "‚");
                if (encodedHtml.Contains("&sbquo;"))
                    text = text.Replace("&sbquo;", "‚");

                if (encodedHtml.Contains("&#8220;"))      // “	&#8220;	&ldquo;	left double quotation mark
                    text = text.Replace("&#8220;", "“");
                if (encodedHtml.Contains("&ldquo;"))
                    text = text.Replace("&ldquo;", "“");

                if (encodedHtml.Contains("&#8221;"))      // ”	&#8221;	&rdquo;	right double quotation mark
                    text = text.Replace("&#8221;", "”");
                if (encodedHtml.Contains("&rdquo;"))
                    text = text.Replace("&rdquo;", "”");

                if (encodedHtml.Contains("&#8222;"))  //„	&#8222;	&bdquo;	double low-9 quotation mark
                    text = text.Replace("&#8222;", "„");
                if (encodedHtml.Contains("&bdquo;"))
                    text = text.Replace("&bdquo;", "„");

                if (encodedHtml.Contains("&#8224;")) //†	&#8224;	&dagger;	dagger
                    text = text.Replace("&#8224;", "†");
                if (encodedHtml.Contains("&dagger;"))
                    text = text.Replace("&dagger;", "†");

                if (encodedHtml.Contains("&#8225;"))  // ‡	&#8225;	&Dagger;	double dagger
                    text = text.Replace("&#8225;", "‡");
                if (encodedHtml.Contains("&Dagger;"))
                    text = text.Replace("&Dagger;", "‡");

                if (encodedHtml.Contains("&#8226;"))  //•	&#8226;	&bull;	bullet
                    text = text.Replace("&#8226;", "•");
                if (encodedHtml.Contains("&bull;"))
                    text = text.Replace("&bull;", "•");

                if (encodedHtml.Contains("&#8230;"))//…	&#8230;	&hellip;	horizontal ellipsis
                    text = text.Replace("&#8230;", "…");
                if (encodedHtml.Contains("&hellip;"))
                    text = text.Replace("&hellip;", "…");

                if (encodedHtml.Contains("&#8240;"))//‰	&#8240;	&permil;	per mille 
                    text = text.Replace("&#8240;", "‰");
                if (encodedHtml.Contains("&permil;"))
                    text = text.Replace("&permil;", "‰");

                if (encodedHtml.Contains("&#8242;"))//′	&#8242;	&prime;	minutes
                    text = text.Replace("&#8242;", "′");
                if (encodedHtml.Contains("&prime;"))
                    text = text.Replace("&prime;", "′");

                if (encodedHtml.Contains("&#8243;"))//″	&#8243;	&Prime;	seconds
                    text = text.Replace("&#8243;", "″");
                if (encodedHtml.Contains("&Prime;"))
                    text = text.Replace("&Prime;", "″");

                if (encodedHtml.Contains("&#8249;"))//‹	&#8249;	&lsaquo;	single left angle quotation
                    text = text.Replace("&#8249;", "‹");
                if (encodedHtml.Contains("&lsaquo;"))
                    text = text.Replace("&lsaquo;", "‹");

                if (encodedHtml.Contains("&#8250;"))//›	&#8250;	&rsaquo;	single right angle quotation
                    text = text.Replace("&#8250;", "›");
                if (encodedHtml.Contains("&rsaquo;"))
                    text = text.Replace("&rsaquo;", "›");

                if (encodedHtml.Contains("&#8254;"))//‾	&#8254;	&oline;	overline
                    text = text.Replace("&#8254;", "‾");
                if (encodedHtml.Contains("&oline;"))
                    text = text.Replace("&oline;", "‾");

                if (encodedHtml.Contains("&#8364;"))//€	&#8364;	&euro;	euro
                    text = text.Replace("&#8364;", "€");
                if (encodedHtml.Contains("&euro;"))
                    text = text.Replace("&euro;", "€");

                if (encodedHtml.Contains("&#8482;"))// ™	&#8482; or &#153;	&trade;	trademark
                    text = text.Replace("&#8482;", "™");
                if (encodedHtml.Contains("&#153;"))
                    text = text.Replace("&#153;", "™");
                if (encodedHtml.Contains("&trade;"))
                    text = text.Replace("&trade;", "™");

                if (encodedHtml.Contains("&#8592;"))//←	&#8592;	&larr;	left arrow
                    text = text.Replace("&#8592;", "←");
                if (encodedHtml.Contains("&larr;"))
                    text = text.Replace("&larr;", "←");

                if (encodedHtml.Contains("&#8593;"))//↑	&#8593;	&uarr;	up arrow
                    text = text.Replace("&#8593;", "↑");
                if (encodedHtml.Contains("&uarr;"))
                    text = text.Replace("&uarr;", "↑");

                if (encodedHtml.Contains("&#8594;"))//→	&#8594;	&rarr;	right arrow
                    text = text.Replace("&#8594;", "→");
                if (encodedHtml.Contains("&rarr;"))
                    text = text.Replace("&rarr;", "→");

                if (encodedHtml.Contains("&#8595;"))//↓	&#8595;	&darr;	down arrow
                    text = text.Replace("&#8595;", "↓");
                if (encodedHtml.Contains("&darr;"))
                    text = text.Replace("&darr;", "↓");

                if (encodedHtml.Contains("&#8596;"))//↔	&#8596;	&harr;	left right arrow
                    text = text.Replace("&#8596;", "↔");
                if (encodedHtml.Contains("&harr;"))
                    text = text.Replace("&harr;", "↔");

                if (encodedHtml.Contains("&#8629;"))//↵	&#8629;	&crarr;	carriage return arrow
                    text = text.Replace("&#8629;", "↵");
                if (encodedHtml.Contains("&crarr;"))
                    text = text.Replace("&crarr;", "↵");

                if (encodedHtml.Contains("&#8968;"))//⌈	&#8968;	&lceil;	left ceiling
                    text = text.Replace("&#8968;", "⌈");
                if (encodedHtml.Contains("&lceil;"))
                    text = text.Replace("&lceil;", "⌈");

                if (encodedHtml.Contains("&#8969;"))//⌉	&#8969;	&rceil;	right ceiling
                    text = text.Replace("&#8969;", "⌉");
                if (encodedHtml.Contains("&rceil;"))
                    text = text.Replace("&rceil;", "⌉");

                if (encodedHtml.Contains("&#8970;"))//⌊	&#8970;	&lfloor;	left floor
                    text = text.Replace("&#8970;", "⌊");
                if (encodedHtml.Contains("&lfloor;"))
                    text = text.Replace("&lfloor;", "⌊");

                if (encodedHtml.Contains("&#8971;"))//⌋	&#8971;	&rfloor;	right floor
                    text = text.Replace("&#8971;", "⌋");
                if (encodedHtml.Contains("&rfloor;"))
                    text = text.Replace("&rfloor;", "⌋");

                if (encodedHtml.Contains("&#9674;"))//◊	&#9674;	&loz;	lozenge
                    text = text.Replace("&#9674;", "◊");
                if (encodedHtml.Contains("&loz;"))
                    text = text.Replace("&loz;", "◊");

                if (encodedHtml.Contains("&#9824;"))//♠	&#9824;	&spades;	spade
                    text = text.Replace("&#9824;", "♠");
                if (encodedHtml.Contains("&spades;"))
                    text = text.Replace("&spades;", "♠");

                if (encodedHtml.Contains("&#9827;"))//♣	&#9827;	&clubs;	club
                    text = text.Replace("&#9827;", "♣");
                if (encodedHtml.Contains("&clubs;"))
                    text = text.Replace("&clubs;", "♣");

                if (encodedHtml.Contains("#9829;"))//♥	&#9829;	&hearts;	heart
                    text = text.Replace("#9829;", "♥");
                if (encodedHtml.Contains("&hearts;"))
                    text = text.Replace("&hearts;", "♥");

                if (encodedHtml.Contains("&#9830;"))//♦	&#9830;	&diams;	diamond
                    text = text.Replace("&#9830;", "♦");
                if (encodedHtml.Contains("&diams;"))
                    text = text.Replace("&diams;", "♦");

                //  Math Symbols Supported by HTML...  &#8...;

                // Greek Letters Supported by HTML...  &#9..;


                return text;
            }

        }
    }
}