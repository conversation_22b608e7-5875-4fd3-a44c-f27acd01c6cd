﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Storeya.Core.Helpers
{
    public class ApiUtilsHelper
    {
        public static string CreateSHA256(string str)
        {
            using (SHA1Managed crypt = new SHA1Managed())
            {
                string hash = String.Empty;
                byte[] crypto = crypt.ComputeHash(Encoding.UTF8.GetBytes(str), 0, Encoding.UTF8.GetByteCount(str));

                foreach (byte bit in crypto)
                {
                    hash += bit.ToString("x2");
                }
                return hash;
            }
        }

        public static string CreateMD5(string str)
        {
            using (MD5 md5Hash = MD5.Create())
            {
                // Convert the input string to a byte array and compute the hash. 
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(str));

                // Create a new Stringbuilder to collect the bytes 
                // and create a string.
                StringBuilder sBuilder = new StringBuilder();

                // Loop through each byte of the hashed data  
                // and format each one as a hexadecimal string. 
                for (int i = 0; i < data.Length; i++)
                {
                    sBuilder.Append(data[i].ToString("x2"));
                }

                // Return the hexadecimal string. 
                return sBuilder.ToString();
            }
        }

        public static string GenerateCodeHash(string appkey)
        {
            string salt = "WeAreTheChamions";
            salt += DateTime.Now.ToLocalTime();

            return CreateSHA256(salt + appkey);

        }

        public static string GenerateAppTokenHash(string appkey, string shopKey)
        {
            string salt = DateTime.Now.ToShortDateString();
            return CreateSHA256(salt + appkey + shopKey);
        }

        public static string GenerateAppSecret(string AppKey)
        {
            string salt = "Shhhh, SeCrEt!";
            return "secretkey_" + CreateSHA256(salt + AppKey);
        }

        public static string GenerateAppKey()
        {
            int KeyLength = 16, i;
            string Key = "";

            Random rand = new Random();

            for (i = 0; i < KeyLength; i++)
            {
                Key += rand.Next(0, 9).ToString();
            }

            return Key;
        }

        public static string CreateSignature(string appSecret, string encodedShopID, double time)
        {
            return CreateMD5(appSecret + encodedShopID + time.ToString());
        }

        public static byte[] GetBytes(string str)
        {
            byte[] bytes = new byte[str.Length * sizeof(char)];
            System.Buffer.BlockCopy(str.ToCharArray(), 0, bytes, 0, bytes.Length);
            return bytes;
        }
    }
}
