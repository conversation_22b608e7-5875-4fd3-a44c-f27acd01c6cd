﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using System.Web.Script.Serialization;

namespace Storeya.Core.Helpers
{
    public class Auth
    {
        public StoreYaApiAuthResponse Response;

        public Auth(string appkey, string appsecret, string appcode)
        {
            JavaScriptSerializer js = new JavaScriptSerializer();

            string data = js.Serialize(new AuthRequest() {
                appkey = appkey,
                appcode = appcode,
                appsecret = appsecret
            });

            HttpWebResponse response = Utils.SendJsonRequest("POST", ConfigurationManager.AppSettings["apiurl"] + "/Token", data, ""); 
            
            if (response != null)
            {
                Stream responseStream = responseStream = response.GetResponseStream();
                StreamReader Reader = new StreamReader(responseStream, Encoding.Default);
                Response = js.Deserialize<StoreYaApiAuthResponse>(Reader.ReadToEnd());
            }
        }

        public static bool ValidateStoreyaOrigin(string appsecret, string shopkey, string timestamp, string signature)
        {
            string str = appsecret + shopkey + timestamp;

            using (MD5 md5Hash = MD5.Create())
            {
                // Convert the input string to a byte array and compute the hash. 
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(str));

                // Create a new Stringbuilder to collect the bytes 
                // and create a string.
                StringBuilder sBuilder = new StringBuilder();

                // Loop through each byte of the hashed data  
                // and format each one as a hexadecimal string. 
                for (int i = 0; i < data.Length; i++)
                {
                    sBuilder.Append(data[i].ToString("x2"));
                }

                // Return the hexadecimal string. 
                return sBuilder.ToString() == signature;
            }
        }

    }

    public class AuthRequest
    {
        public string appkey;
        public string appsecret;
        public string appcode;
    }

    public class StoreYaApiAuthResponse
    {
        public string access_token;
        public Dictionary<string, dynamic> Shop;
    }
}