﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Web.Script.Serialization;

namespace Storeya.Core.Helpers
{
    public static class Scripts
    {       
        private static string _apiurl = ConfigHelper.GetValue("Api_Url").TrimEnd('/');   
        public static ScriptsResponse GetScripts(string shopkey, string token)
        {
            string url = String.Format(_apiurl + "/shops/{0}/scripts", shopkey);

            return Deserialize(ApiUtils.SendJsonRequest("GET", url, "", token));
        }

        public static ScriptsResponse GetScript(string id, string shopkey, string token)
        {
            string url = String.Format(_apiurl + "/shops/{0}/scripts/{1}", shopkey, id);

            return Deserialize( ApiUtils.SendJsonRequest("GET", url, "", token));
        }

        public static ScriptsResponse AddScriptCode(string script, string shopkey, string token)
        {
            string data = String.Format("{{\"code\":\"{0}\"}}", script);
            string url = String.Format(_apiurl + "/shops/{0}/scripts", shopkey);

            return Deserialize(ApiUtils.SendJsonRequest("POST", url, data, token));
        }

        public static ScriptsResponse AddScriptUrl(string script, string shopkey, string token)
        {
            string data = String.Format("{{\"url\":\"{0}\"}}", script);
            string url = String.Format(_apiurl + "/shops/{0}/scripts", shopkey);

            return Deserialize(ApiUtils.SendJsonRequest("POST", url, data, token));
        }

        public static ScriptsResponse UpdateScriptCode(string id, string script, string shopkey, string token)
        {
            string data = String.Format("{{\"code\":\"{0}\"}}", script);
            string url = String.Format(_apiurl + "/shops/{0}/scripts/{1}", shopkey, id);

            return Deserialize(ApiUtils.SendJsonRequest("PUT", url, data, token));
        }

        public static ScriptsResponse UpdateScriptUrl(string id, string script, string shopkey, string token)
        {
            string data = String.Format("{{\"url\":\"{0}\"}}", script);
            string url = String.Format(_apiurl + "/shops/{0}/scripts/{1}", shopkey, id);

            return Deserialize(ApiUtils.SendJsonRequest("PUT", url, data, token));
        }

        public static ScriptsResponse DeleteScript(string id, string shopkey, string token)
        {
            string url = String.Format(_apiurl + "/shops/{0}/scripts/{1}", shopkey, id);

            return Deserialize(ApiUtils.SendJsonRequest("DELETE", url, "", token));
        }

        public static ScriptsResponse Deserialize(HttpWebResponse response)
        {

            JavaScriptSerializer js = new JavaScriptSerializer();

            if (response != null)
            {
                Stream responseStream = responseStream = response.GetResponseStream();
                StreamReader Reader = new StreamReader(responseStream, Encoding.Default);
                return js.Deserialize<ScriptsResponse>(Reader.ReadToEnd());
            }

            return null;

        }

    }

    public class ScriptsResponse
    {
        public int status;
        public List<ScriptObject> Objects;
        public string error;
    }
    public class ScriptObject
    {
        public string id;
        public string content;
        public string type;  
    }


}