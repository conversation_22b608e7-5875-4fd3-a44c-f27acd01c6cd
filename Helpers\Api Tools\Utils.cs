﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;

namespace Storeya.Core.Helpers
{
    public static class ApiUtils
    {
        public static HttpWebResponse SendJsonRequest(string method, string url, string body, string token)
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = method.ToUpper();
            request.ContentType = "application/json";
            request.Headers.Add("Authorization",token);

            if (!String.IsNullOrEmpty(body))
            {
                byte[] data = System.Text.Encoding.UTF8.GetBytes(body);
                request.ContentLength = data.Length;
                Stream dataStream = request.GetRequestStream();

                dataStream.Write(data, 0, data.Length);
                dataStream.Close();
            }

            return (HttpWebResponse)request.GetResponse();
        }
    }
}