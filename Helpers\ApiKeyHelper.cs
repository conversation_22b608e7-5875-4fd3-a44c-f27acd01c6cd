﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Text;
using System.Web;

namespace Storeya.Core.Helpers
{
    public static class ApiKeyHelper
    {
        public static string GetApiKey(int shopID, int widgetID)
        {
            string shopIDEncoded = SequenceHelper.Encode(shopID);
            string widgetIDEncoded = SequenceHelper.Encode(widgetID);

            //string apiKey = ApiUtilsHelper.CreateSHA256(shopIdEncoded + ((DateTime)shop.InsertedAt).ToFileTimeUtc().ToString());

            string tokenPattern = "s={0}&w={1}";
            string apiKey = PasswordHelper.EncodeToken(string.Format(tokenPattern, shopIDEncoded, widgetIDEncoded));
            return apiKey;
        }



        public static StoreYaData ParseApiKey(string apiKey)
        {
            StoreYaData data = null;

            try
            {
                string decodedApiKey = PasswordHelper.DecodeToken(apiKey);
                NameValueCollection tokenValue = HttpUtility.ParseQueryString(decodedApiKey);

                string encodedShopID = tokenValue["s"];
                int shopID = SequenceHelper.Decode(encodedShopID);

                string encodedWidgetID = tokenValue["w"];
                int widgetID = SequenceHelper.Decode(encodedWidgetID);

                data = new StoreYaData() { ShopID = shopID, WidgetID = widgetID };
            }
            catch (Exception)
            {

            }

            return data;
        }
    }

    public class StoreYaData
    {
        public int ShopID { get; set; }
        public int WidgetID { get; set; }
    }
}
