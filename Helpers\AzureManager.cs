﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.WindowsAzure.Storage.Blob;
using Microsoft.WindowsAzure.Storage.Auth;
using System.Net;

namespace Storeya.Core.Helpers
{
    public class AzureManager
    {

        public static void UploadFileToAzure(string filePath)
        {
            string uploadFolder = ConfigHelper.GetValue("ProductImages.Path");
            string localImagePath = filePath.Substring(filePath.IndexOf(uploadFolder));
            
            UploadFileToAzure(filePath, localImagePath);
        }

        public static void UploadFileToAzure(string filePath, string blobName)
        {
            var blockBlob = GetBlobContainer(blobName);

            // Create or overwrite the "myblob" blob with contents from a local file.
            using (var fileStream = System.IO.File.OpenRead(filePath))
            {
                blockBlob.UploadFromStream(fileStream);
            }
        }

        public static void UploadFileFromWebToAzure(string url, string blobName)
        {
            var blockBlob = GetBlobContainer(blobName);

            // Create or overwrite the "myblob" blob with contents from a local file.
            using (var client = new WebClient())
            {
                blockBlob.UploadFromStream(client.OpenRead(url));
            }
        }

        private static CloudBlockBlob GetBlobContainer(string blobName)
        {
            var credentials = new StorageCredentials("sstatic",
                                        "3Dfic16CV2Mg2ucIkT5e2uzr2ZTMvKyAzAW0UukAJ3i5RP77VLZocRxPDNB5iia2S62723heE2q7s40Up59ycQ==");
            var client = new CloudBlobClient(new Uri("http://sstatic.blob.core.windows.net/"), credentials);

            // Retrieve a reference to a container. (You need to create one using the mangement portal, or call container.CreateIfNotExists())
            var container = client.GetContainerReference("products");

            // Retrieve reference to a blob named "myfile.gif".
            var blockBlob = container.GetBlockBlobReference(blobName);
            return blockBlob;
        }


        public static void ListFiles()
        {
            var credentials = new StorageCredentials("sstatic",
                            "3Dfic16CV2Mg2ucIkT5e2uzr2ZTMvKyAzAW0UukAJ3i5RP77VLZocRxPDNB5iia2S62723heE2q7s40Up59ycQ==");
            var client = new CloudBlobClient(new Uri("http://sstatic.blob.core.windows.net/"), credentials);

            // Retrieve a reference to a container. (You need to create one using the mangement portal, or call container.CreateIfNotExists())
            var container = client.GetContainerReference("products");

            container.GetDirectoryReference("uploads/736").Container.Delete();

            foreach (var item in container.ListBlobs("uploads/736/"))
            {

                var blockBlob = container.GetBlockBlobReference(item.Uri.PathAndQuery);
                blockBlob.Delete();

            }
            
            ListData(container);
            
        }

        private static void ListData(CloudBlobContainer container)
        {
            try
            {
                // Loop over items within the container and output the length and URI.
                foreach (IListBlobItem item in container.ListBlobs(null, false))
                {
                    if (item.GetType() == typeof(CloudBlockBlob))
                    {
                        CloudBlockBlob blob = (CloudBlockBlob)item;

                        Console.WriteLine("Block blob of length {0}: {1}", blob.Properties.Length, blob.Uri);

                    }
                    else if (item.GetType() == typeof(CloudPageBlob))
                    {
                        CloudPageBlob pageBlob = (CloudPageBlob)item;

                        Console.WriteLine("Page blob of length {0}: {1}", pageBlob.Properties.Length, pageBlob.Uri);

                    }
                    else if (item.GetType() == typeof(CloudBlobDirectory))
                    {
                        CloudBlobDirectory directory = (CloudBlobDirectory)item;

                        Console.WriteLine("Directory: {0}", directory.Uri);
                    }
                }
            }
            catch
            {
                throw;
            }
        }

    }
}
