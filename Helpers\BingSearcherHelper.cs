﻿
using Bing;
using Newtonsoft.Json;
using RestSharp;
using RestSharp.Deserializers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Authentication;
using System.Text;
using System.Web;
using RestSharp.Authenticators;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models.TrafficBoosterModels.TrafficChannels;

namespace Storeya.Core.Helpers
{
    // Used to return search results including relevant headers
    struct SearchResult
    {
        public String jsonResult;
        public Dictionary<String, String> relevantHeaders;
    }

    public class BingSearcherHelper
    {
        string _apiKey = ConfigHelper.GetValue("BingApiKey");
        private readonly RestClient _client;

        public BingSearcherHelper()
        {
            _client = new RestClient();
            _client.Authenticator = new HttpBasicAuthenticator(_apiKey, _apiKey);
            _client.AddHandler("application/json", new DynamicJsonDeserializer());
        }



        public RestRequest BuildBingRequestWithMicrosoftCognitiveServices(string query)
        {

            //Request URL https://api.cognitive.microsoft.com/bing/v5.0/search[?q][&count][&offset][&mkt][&safesearch]

            //https://api.cognitive.microsoft.com/bing/v5.0/search?q=site:concept-15.com

            var request = new RestRequest
            {
                Resource = "https://api.cognitive.microsoft.com/bing/v7.0/search",
                Method = Method.GET,
                RequestFormat = DataFormat.Json
            };

            request.AddHeader("Ocp-Apim-Subscription-Key", _apiKey);

            request.AddParameter(new Parameter
            {
                Name = "q",
                Value = string.Format("{0}", query.Trim()),
                Type = ParameterType.GetOrPost
            });

            return request;
        }


        public long GetIndexedResultsCount(string query)
        {
            //MicrosoftCognitiveServicesBingSearchRoot bingSearchResults = SearchNew(string.Format("site:{0}", query));

            MicrosoftCognitiveServicesBingSearchRoot bingSearchResults = SearchNew_2(string.Format("site:{0}", query));
            long indexed = (bingSearchResults == null) ? 0 : (bingSearchResults.webPages == null ? 0 : bingSearchResults.webPages.totalEstimatedMatches);
            return indexed;
        }


        public MicrosoftCognitiveServicesBingSearchRoot SearchNew_2(string searchQuery)
        {
            string bing_api_version = ConfigHelper.GetValue("BingApiVersion", "v7.0");
            string uriBase = string.Format("https://api.cognitive.microsoft.com/bing/{0}/search", bing_api_version);

            var uriQuery = uriBase + "?q=" + Uri.EscapeDataString(searchQuery);

            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            WebRequest request = HttpWebRequest.Create(uriQuery);
            request.Headers["Ocp-Apim-Subscription-Key"] = _apiKey;

            try
            {
                HttpWebResponse response = (HttpWebResponse)request.GetResponseAsync().Result;
                string json = new StreamReader(response.GetResponseStream()).ReadToEnd();

                // Create result object for return
                var searchResult = new SearchResult()
                {
                    jsonResult = json,
                    relevantHeaders = new Dictionary<String, String>()
                };

                return searchResult.jsonResult == null ? null : JsonConvert.DeserializeObject<MicrosoftCognitiveServicesBingSearchRoot>(searchResult.jsonResult);

            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("Request Error Message: {0}. ", ex.ToString()));
            }

        }

        public MicrosoftCognitiveServicesBingSearchRoot SearchNew(string query)
        {
            var request = BuildBingRequestWithMicrosoftCognitiveServices(query);
            var response = _client.Execute<dynamic>(request);
            if (response.Data != null && response.Data.statusCode == "401")
            {
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                    throw new AuthenticationException("Api Key is not correct");

                throw new Exception(string.Format("Request Error Message: {0}. Content: {1}.", response.ErrorMessage, response.Content));
            }

            return response.Data == null ? null : JsonConvert.DeserializeObject<MicrosoftCognitiveServicesBingSearchRoot>(response.Data.ToString());
        }

        public static string SaveUetIDInDB(long? uetID, int shopID)
        {
            if (uetID == null)
            {
                return "Error! There is no BingUetID. Nothing was saved!";
            }
            else
            {
                var db = Storeya.Core.Helpers.DataHelper.GetStoreYaEntities();
                var tChannel = TbChannelManager.GetChannelsFromDB(shopID).Where(x => x.ChannelType == TrafficChannelsTypes.Bing.GetHashCode()).SingleOrDefault();
                if (tChannel == null)
                {
                    return "Error! There is no TbAccountTrafficChannels. Nothing was saved!";
                }
                tChannel.BingUetTagID = uetID;
                db.SaveChanges();
                return "<br/> Please reload the page";
            }
        }


        //public RestRequest BuildBingRequest(string query, int resultsCount)
        //{
        //    var request = new RestRequest
        //    {
        //        Resource = "https://api.datamarket.azure.com/Bing/SearchWeb/v1/Web",
        //        Method = Method.GET,
        //        RequestFormat = DataFormat.Json
        //    };

        //    if (resultsCount > 0)
        //    {
        //        request.AddParameter(new Parameter
        //        {
        //            Name = "$top",
        //            Value = resultsCount,
        //            Type = ParameterType.GetOrPost
        //        });
        //    }

        //    request.AddParameter(new Parameter
        //    {
        //        Name = "Query",
        //        Value = string.Format("'{0}'", query.Trim()),
        //        Type = ParameterType.GetOrPost
        //    });

        //    return request;
        //}


        //public IEnumerable<BingSearch.Result> Search(string query, int resultsCount)
        //{
        //    var request = BuildBingRequest(query, resultsCount);
        //    var response = _client.Execute<dynamic>(request);
        //    if (response.ResponseStatus == ResponseStatus.Error)
        //    {
        //        if (response.StatusCode == HttpStatusCode.Unauthorized)
        //            throw new AuthenticationException("Api Key is not correct");

        //        throw new Exception(string.Format("Request Error Message: {0}. Content: {1}.", response.ErrorMessage, response.Content));
        //    }

        //    return response.Data == null ? null : JsonConvert.DeserializeObject<BingSearch>(response.Data.d.ToString()).Results;
        //}

    }


    public class DynamicJsonDeserializer : IDeserializer
    {
        public string RootElement { get; set; }
        public string Namespace { get; set; }
        public string DateFormat { get; set; }

        public T Deserialize<T>(IRestResponse response)
        {
            return JsonConvert.DeserializeObject<dynamic>(response.Content);
        }
    }

    public class BingSearch
    {
        public List<Result> Results { get; set; }

        public class Result
        {
            public Metadata __Metadata { get; set; }
            public string ID { get; set; }
            public string Title { get; set; }
            public string Description { get; set; }
            public string DisplayUrl { get; set; }
            public string Url { get; set; }
        }

        public class Metadata
        {
            public string Uri { get; set; }
            public string Type { get; set; }
        }
    }

    public class WebPageData
    {
        public string id { get; set; }
        public string name { get; set; }
        public string url { get; set; }
        public string displayUrl { get; set; }
        public string snippet { get; set; }
        public string dateLastCrawled { get; set; }
    }

    public class WebPages
    {
        public string webSearchUrl { get; set; }
        public long totalEstimatedMatches { get; set; }
        public List<WebPageData> value { get; set; }
    }

    public class SearchItemData
    {
        public string id { get; set; }
    }

    public class Item
    {
        public string answerType { get; set; }
        public int resultIndex { get; set; }
        public SearchItemData value { get; set; }
    }

    public class Mainline
    {
        public List<Item> items { get; set; }
    }

    public class RankingResponse
    {
        public Mainline mainline { get; set; }
    }

    public class MicrosoftCognitiveServicesBingSearchRoot
    {
        public string _type { get; set; }
        public WebPages webPages { get; set; }
        public RankingResponse rankingResponse { get; set; }
    }
}
