﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Drawing;

namespace Storeya.Core.Helpers
{
    public static class BitmapImageUtils
    {
        //public static BitmapImage LoadBitmapImage(string imageName)
        //{
        //    FileStream fs = File.Open(imageName, FileMode.Open);
        //    Bitmap bitmap = new Bitmap(fs);
        //    return Convert(bitmap);
        //}

        //public static Bitmap LoadAndColorizeBitmapImage(string imageName, Color color)
        //{
        //    //FileStream fs = File.Open(imageName, FileMode.Open);
        //    //using (fs)
        //    //{
        //        Bitmap bitmap = new Bitmap(fs);
        //        ColorizeBitmap(bitmap, color);
        //        return bitmap;
        //    //}
        //}

        public static Color Colorize(Color originalColor, Color color)
        {
            int strength = (originalColor.R + originalColor.G + originalColor.B);
            return
                Color.FromArgb(
                    originalColor.A,
                    System.Convert.ToByte(color.R * strength / 255 / 3),
                    System.Convert.ToByte(color.G * strength / 255 / 3),
                    System.Convert.ToByte(color.B * strength / 255 / 3));
        }

        public static Color ToMediaColor(this System.Drawing.Color color)
        {
            return
                Color.FromArgb(color.A, color.R, color.G, color.B);
        }

        public static System.Drawing.Color ToSystemColor(this Color color)
        {
            return
                System.Drawing.Color.FromArgb(color.A, color.R, color.G, color.B);
        }

        //public static BitmapImage Convert(Bitmap bitmap)
        //{
        //    MemoryStream memoryStream = new MemoryStream();
        //    bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
        //    BitmapImage bitmapImage = new BitmapImage();

        //    bitmapImage.BeginInit();
        //    bitmapImage.StreamSource = new MemoryStream(memoryStream.ToArray());
        //    bitmapImage.EndInit();
        //    return bitmapImage;
        //}

        //public static Bitmap Convert(BitmapImage bitmap)
        //{
        //    MemoryStream memoryStream = new MemoryStream();
        //    BitmapEncoder bitmapEncoder = new BmpBitmapEncoder();
        //    bitmapEncoder.Frames.Add(BitmapFrame.Create(memoryStream));
        //    bitmapEncoder.Save(memoryStream);
        //    return new Bitmap(memoryStream);
        //}

        public static Bitmap ColorizeBitmap(Bitmap bitmap, Color tintColor)
        {
            for (int x = 0; x < bitmap.Width; x++)
            {
                for (int y = 0; y < bitmap.Height; y++)
                {
                    Color clearPixel = bitmap.GetPixel(x, y).ToMediaColor();
                    Color tintedPixel = Colorize(clearPixel, tintColor);

                    bitmap.SetPixel(x, y, tintedPixel.ToSystemColor());
                }
            }

            return bitmap;
        }
    }
}
