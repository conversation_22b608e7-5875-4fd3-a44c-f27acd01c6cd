﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class BlackListHelper
    {
        public static void LeadDetailsUpdateWhoisInfo(int id)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            var leadDetails = db.LeadDetails.Single(x => x.ID == id);

            var whoisInfo = GetWhoisInfo(leadDetails.Url);

            leadDetails.DomainCountry = whoisInfo.RegistrationCountry;
            leadDetails.DomainRegistrationDate = whoisInfo.RegistrationDate;

            db.SaveChanges();
        }

        public static WhoisInfo GetWhoisInfo(string url, int? version = null)
        {

            WhoisInfo info = null;
            if (version == 1)
            {
                info = GetWhoisInfoUsingOldAPI(url);
            }
            else if (version == 2)
            {
                info = WhoIsAPI.GetWhoisInfo(url);
            }
            else
            {
                info = WhoIsAPI.GetWhoisInfo(url);
                if (info == null || info.RegistrationDate == null)
                {
                    info = GetWhoisInfoUsingOldAPI(url);
                }
            }
            if (info == null)
            {
                info = new WhoisInfo();
            }
            info.Error = "";


            if (info.RegistrationDate == null)
            {
                info.Error += Environment.NewLine + "Date is missing";
            }

            return info;
        }
        private static WhoisInfo GetWhoisInfoUsingOldAPI(string url)
        {
            var info = new WhoisInfo();

            dynamic whoisInfo = getWhoisInfo(new Uri(url).Host);
            var registryData = whoisInfo.WhoisRecord;
            if (whoisInfo.WhoisRecord.registryData != null)
            {
                registryData = whoisInfo.WhoisRecord.registryData;
            }


            info.Error = whoisInfo.WhoisRecord.dataError;
            info.RegistrationCountry = registryData?.registrant?.countryCode;

            var ageObj = whoisInfo.WhoisRecord.estimatedDomainAge;

            if (registryData != null && (registryData.createdDate != null || registryData.createdDateNormalized != null))
            {
                DateTime regDate;
                if (DateTime.TryParse(registryData.createdDate.ToString(), out regDate))
                {
                    info.RegistrationDate = regDate;
                }
            }

            if (info.RegistrationDate == null && registryData != null && registryData.createdDateNormalized != null)
            {
                DateTime regDate;
                if (DateTime.TryParse(registryData.createdDateNormalized.ToString(), out regDate))
                {
                    info.RegistrationDate = regDate;
                }
            }
            if (info.RegistrationDate == null && ageObj != null)
            {
                int age = 0;
                int.TryParse(ageObj.ToString(), out age);
                age = Math.Abs(age);
                info.RegistrationDate = DateTime.Now.AddDays(-age);
            }
            return info;
        }
        private static dynamic getWhoisInfo(string url)
        {
            //<EMAIL> whoisxmlapi1234
            string key = ConfigHelper.GetValue("WhoisAPIKey", "at_SRemx7ZX5QdoI3FPQTyTcRJJalYFD");//  "at_0sWhAohG1sTObZp1V3ZehKYI9eqSW";
            var whoisUrl = $"https://www.whoisxmlapi.com/whoisserver/WhoisService?apiKey={key}&domainName={url}&outputFormat=JSON";

            //        https://sgdeliandgrocer.com.sg/
            if (url.Contains("myshopify"))
            {
                return null;
            }

            HttpWebRequest request = HttpRequestResponseHelper.SetRequest(whoisUrl, "GET");
            var response = HttpRequestResponseHelper.GetResponseContent(request);
            return JsonConvert.DeserializeObject(response);
        }

        //public static void Test()
        //{
        //    //prepare test data
        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    var leadDetail = new LeadDetail() { Url = "https://kimpersiancattery.com/" };
        //    db.LeadDetails.Add(leadDetail);
        //    db.SaveChanges();

        //    LeadDetailsUpdateWhoisInfo(leadDetail.ID);

        //    db.Entry(leadDetail).Reload();

        //    //Assert.AreEqual("PANAMA", leadDetail.DomainCountry);

        //    //clean
        //    db.LeadDetails.Remove(leadDetail);
        //    db.SaveChanges();
        //}

        public class WhoisInfo
        {
            public string RegistrationCountry { get; set; }
            public DateTime? RegistrationDate { get; set; }
            public string Error { get; set; }
        }

        //public static bool IsNameInBlackList(string userName)
        //{
        //    if (string.IsNullOrEmpty(userName))
        //    {
        //        return false;
        //    }
        //    List<string> blackUserNames = new List<string>() { "Mellissa Warren" };
        //    return CheckIfListed(userName, blackUserNames);
        //}

        //private static bool CheckIfListed(string value, List<string> BlackList)
        //{
        //    return BlackList.Any(c => value.ToLower().Contains(c.ToLower()));
        //}
    }
}
