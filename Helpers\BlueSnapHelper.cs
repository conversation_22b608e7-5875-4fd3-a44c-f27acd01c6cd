﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using Storeya.Core.Entities;
using System.Web;
using Storeya.Core.Models.AppStore;
using Storeya.Core.Models.Payments;
using Storeya.Core.Models;
using Storeya.Core.Models.Plimus;
using Storeya.Core.Models.Account;
using Storeya.Core.Models.TrafficBoosterModels;
using System.Web.Util;
using Storeya.Core.Models.FastSpring;

namespace Storeya.Core.Helpers
{

    public class BluesnapHelper : IDisposable
    {
        private readonly StoreYaEntities db;
        private readonly BlueSnapApi bs;
        public BluesnapHelper()
        {
            db = DataHelper.GetStoreYaEntities();
            bs = new BlueSnapApi();
        }
        public static string GetContractName(int planId, int appId)
        {
            string contractDesc = "";
            string appName = AppStoreManager.GetAppName(appId);
            contractDesc += " " + appName;
            switch (planId)
            {
                case (int)PlanTypes.Economy:
                    if (appId == (int)AppTypes.TrafficBooster)
                        contractDesc += " Basic";
                    else
                    {
                        contractDesc += " Economy";
                    }
                    break;
                case (int)PlanTypes.FirstClass_2:
                    if (appId == (int)AppTypes.TrafficBooster)
                        contractDesc += " Standard";
                    else
                    {
                        contractDesc += " First Class";
                    }
                    break;
                case (int)PlanTypes.PrivateJet:
                    if (appId == (int)AppTypes.TrafficBooster)
                        contractDesc += " Pro";
                    else
                    {
                        contractDesc += " Private Jet";
                    }
                    break;
                case (int)PlanTypes.CustomPlan:
                    contractDesc += " Deluxe";
                    break;
                default:
                    break;
            }

            return $"{contractDesc} Plan:";
        }

        public static string GetContractPaymentDescription(int planId, int appId, int contMethod, decimal amount, decimal trialAmount, string currencySymbol, int? type = null)
        {
            string description = "";
            string period = "every month";
            if (contMethod == 1)
            {
                period = "every year";
            }
            TranslationHelper translationHelper = new TranslationHelper(TranslationHelper.PagesType.Checkout);

            if (appId == (int)AppTypes.TrafficBooster)
            {
                if (translationHelper.SupportMultiLanguage)
                {
                    if (planId == PlanTypes.CustomPlan.GetHashCode()
                        || (planId == PlanTypes.Economy.GetHashCode() && ConfigHelper.GetBoolValue("FirstPlanWithoutDiscount")))
                    {
                        //This XXX (i.e.$1,000) is a recurring monthly payment

                        description = string.Format(translationHelper.GetString("String31"), GetPriceToShow(amount), currencySymbol);
                    }
                    else
                    {
                        var percentage = 33;
                        if (type == (int)UserTypes.PayPalUS)
                        {
                            description = string.Format(translationHelper.GetString("String32"), GetPriceToShow(trialAmount), GetPriceToShow(amount), currencySymbol, period, percentage);
                        }
                        else
                        {
                            description = string.Format(translationHelper.GetString("String33"), GetPriceToShow(trialAmount), GetPriceToShow(amount), currencySymbol, period, percentage);
                        }
                    }
                }
                else
                {
                    if (planId == PlanTypes.CustomPlan.GetHashCode()
                       || (planId == PlanTypes.Economy.GetHashCode() && ConfigHelper.GetBoolValue("FirstPlanWithoutDiscount")))
                    {
                        //This XXX (i.e.$1,000) is a recurring monthly payment

                        description = string.Format("This {1}{0} is a recurring monthly payment", GetPriceToShow(amount), currencySymbol);
                    }
                    else
                    {
                        var percentage = 33;
                        if (type == (int)UserTypes.PayPalUS)
                        {
                            description = string.Format("{4}% discount on the first 2 months: {2}{0} then {2}{1} {3} (you can cancel any time).", GetPriceToShow(trialAmount), GetPriceToShow(amount), currencySymbol, period, percentage);
                        }
                        else
                        {
                            description = string.Format("{4}% discount on the first month: {2}{0} then {2}{1} {3} (you can cancel any time).", GetPriceToShow(trialAmount), GetPriceToShow(amount), currencySymbol, period, percentage);
                        }
                    }
                }
            }
            else
            {
                if (ConractSettingsHelper.HasTrial(planId, contMethod, appId))
                {
                    description = string.Format("First charge (after 14 days): {2}{0} then {2}{1} {3}", GetPriceToShow(trialAmount != 0 ? trialAmount : amount), GetPriceToShow(amount), currencySymbol, period);
                }
                else
                {
                    period = "monthly";
                    if (contMethod == 1)
                    {
                        period = "annually";
                    }
                    description = string.Format("Billed {2}: {1}{0}", GetPriceToShow(amount), currencySymbol, period);
                }
            }            
            return description;
        }

        public static string GetPriceToShow(decimal amount)
        {
            string totalAmountToShowString = (amount % 1) == 0
                ? String.Format("{0:n0}", amount)
                : String.Format("{0:n}", amount);

            return totalAmountToShowString;

        }


        public static ContractSettings GetContract(int planID, int contMethod, int appID)
        {
            ContractSettings settings = new ContractSettings();

            if (appID == (int)AppTypes.TrafficBooster)
            {
                settings.ContractID = GetContractIDForTrafficBooster(planID);
                settings.Price = ConractSettingsHelper.GetTrafficBoosterContractPrice(planID);
                settings.TrialPrice = ConractSettingsHelper.GetTrafficBoosterContractTrialPrice(planID);
                settings.HasTrial = false;
                settings.Method = 0;
            }
            else
            {
                //other internal apps with same prices
                settings.ContractID = GetNotTBContractID(contMethod, appID);
                settings.Price = ConractSettingsHelper.GetContractPrice(planID, contMethod, appID);
                settings.HasTrial = ConractSettingsHelper.HasTrial(planID, contMethod, appID);
                settings.Method = contMethod;
            }

            return settings;
        }

        public static int GetProratedOneTimeContact(string country)
        {
            IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
            if (!string.IsNullOrEmpty(country) && country.ToUpper() != "US")
            {
                return bluesnapConfiguration.TBOneTimePaymentDeluxeEU;
            }
            else
            {
                return bluesnapConfiguration.TBOneTimePaymentDeluxeCA;
            }
        }
        public static int GetContractIDForTrafficBooster(int planID)
        {
            IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
            //bool useCaOnlyContracts = UseCaOnlyContracts(countryCode, state);
            int contractID = bluesnapConfiguration.TBEconomyCA;
            switch (planID)
            {
                case (int)PlanTypes.Economy:
                    contractID = bluesnapConfiguration.TBEconomyCA;
                    //if (useCaOnlyContracts)
                    //{
                    //    contractID = bluesnapConfiguration.TBEconomyCA;
                    //}
                    //else
                    //{
                    //    contractID = bluesnapConfiguration.TBEconomy;
                    //}
                    break;
                case (int)PlanTypes.FirstClass_2:
                    contractID = bluesnapConfiguration.TBFirstClassCA;
                    //if (useCaOnlyContracts)
                    //{
                    //    contractID = bluesnapConfiguration.TBFirstClassCA;
                    //}
                    //else
                    //{
                    //    contractID = bluesnapConfiguration.TBFirstClass;
                    //}
                    break;
                case (int)PlanTypes.PrivateJet:
                    contractID = bluesnapConfiguration.TBPrivateJetCA;
                    //if (useCaOnlyContracts)
                    //{
                    //    contractID = bluesnapConfiguration.TBPrivateJetCA;
                    //}
                    //else
                    //{
                    //    contractID = bluesnapConfiguration.TBPrivateJet;
                    //}
                    break;
                case (int)PlanTypes.CustomPlan:
                    contractID = bluesnapConfiguration.TBDeluxeCA;
                    //if (useCaOnlyContracts)
                    //{
                    //    contractID = bluesnapConfiguration.TBDeluxeCA;
                    //}
                    //else
                    //{
                    //    contractID = bluesnapConfiguration.TBDeluxe;
                    //}
                    break;
                default:
                    break;
            }
            return contractID;
        }


        public static int GetFixedContract(string contractIDString, string country)
        {
            if (!string.IsNullOrEmpty(country))
            {
                country = country.ToUpper();
            }
            int contractID = Convert.ToInt32(contractIDString);
            IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
            if (!string.IsNullOrEmpty(country) && country != "US")
            {
                if (contractID == (int)bluesnapConfiguration.TBDeluxeCA)
                {
                    return bluesnapConfiguration.TBDeluxeEU;
                }
                else if (contractID == bluesnapConfiguration.TBEconomyCA)
                {
                    return bluesnapConfiguration.TBEconomyEU;
                }
                else if (contractID == bluesnapConfiguration.TBFirstClassCA)
                {
                    return bluesnapConfiguration.TBFirstClassEU;
                }
                else if (contractID == bluesnapConfiguration.TBPrivateJetCA)
                {
                    return bluesnapConfiguration.TBPrivateJetEU;
                }
                else if (contractID == bluesnapConfiguration.TBOneTimePaymentDeluxeCA)
                {
                    return bluesnapConfiguration.TBOneTimePaymentDeluxeEU;
                }
                else if (contractID == bluesnapConfiguration.MonthlyUS)
                {
                    return bluesnapConfiguration.Monthly;
                }
                else if (contractID == bluesnapConfiguration.YearlyNoTrialUS)
                {
                    return bluesnapConfiguration.YearlyNoTrial;
                }
                else if (contractID == bluesnapConfiguration.YearlyWithTrialUS)
                {
                    return bluesnapConfiguration.YearlyWithTrial;
                }
                else if (contractID == bluesnapConfiguration.WeeklyContractUS)
                {
                    return bluesnapConfiguration.WeeklyContractEU;
                }
            }
            else if (country == "US")
            {
                if (contractID == (int)bluesnapConfiguration.TBDeluxeEU)
                {
                    return bluesnapConfiguration.TBDeluxeCA;
                }
                else if (contractID == bluesnapConfiguration.TBEconomyEU)
                {
                    return bluesnapConfiguration.TBEconomyCA;
                }
                else if (contractID == bluesnapConfiguration.TBFirstClassEU)
                {
                    return bluesnapConfiguration.TBFirstClassCA;
                }
                else if (contractID == bluesnapConfiguration.TBPrivateJetEU)
                {
                    return bluesnapConfiguration.TBPrivateJetCA;
                }
                else if (contractID == bluesnapConfiguration.TBOneTimePaymentDeluxeEU)
                {
                    return bluesnapConfiguration.TBOneTimePaymentDeluxeCA;
                }
                else if (contractID == bluesnapConfiguration.Monthly)
                {
                    return bluesnapConfiguration.MonthlyUS;
                }
                else if (contractID == bluesnapConfiguration.YearlyNoTrial)
                {
                    return bluesnapConfiguration.YearlyNoTrialUS;
                }
                else if (contractID == bluesnapConfiguration.YearlyWithTrial)
                {
                    return bluesnapConfiguration.YearlyWithTrialUS;
                }
                else if (contractID == bluesnapConfiguration.WeeklyContractEU)
                {
                    return bluesnapConfiguration.WeeklyContractUS;
                }
            }
            //no need to change
            return contractID;
        }

        public static List<int> GetList_TBSubscriptionsContracts()
        {
            IBluesnapConfiguration bs = new BlueSnapApi().BluesnapConfiguration;
            List<int> contracts = new List<int>();
            contracts.Add(bs.TBDeluxeCA);
            contracts.Add(bs.TBEconomyCA);
            contracts.Add(bs.TBFirstClassCA);
            contracts.Add(bs.TBPrivateJetCA);

            return contracts;
        }

        public static List<int> GetList_TBOldContracts()
        {
            IBluesnapConfiguration bs = new BlueSnapApi().BluesnapConfiguration;
            List<int> contracts = new List<int>();
            contracts.Add(3282820);
            contracts.Add(3294570);
            contracts.Add(3355732);
            contracts.Add(3374740);
            contracts.Add(3226403);
            return contracts;
        }

        //public static int Get25DealContractID(string countryCode = null, string state = null)
        //{
        //    IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
        //    bool useCaOnlyContracts = UseCaOnlyContracts(countryCode, state);
        //    int contractID = bluesnapConfiguration.TBFirstClass25;
        //    if (useCaOnlyContracts)
        //    {
        //        contractID = bluesnapConfiguration.TBFirstClass25CA;
        //    }
        //    return contractID;
        //}


        //public static int Get50DealContractID(int shopID = 0, string countryCode = null, string state = null)
        //{
        //    IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
        //    bool useCaOnlyContracts = UseCaOnlyContracts(countryCode, state);

        //    int contractID = bluesnapConfiguration.PremiumTB50OffFirstMonth;
        //    if (useCaOnlyContracts)
        //    {
        //        contractID = bluesnapConfiguration.PremiumTB50OffFirstMonthCA;
        //    }

        //    return contractID;
        //}


        //private static bool UseCaOnlyContracts(string countryCode, string state)
        //{
        //    if (countryCode != null && state != null && countryCode.ToUpper() == "US" && state.ToUpper() == "CA")
        //    {
        //        return true;
        //    }
        //    return false;
        //}

        public static bool HasTrial(int contractID)
        {
            IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
            if (contractID == bluesnapConfiguration.YearlyWithTrial)
            {
                return true;
            }
            return false;
        }

        private static string GetContractName(int planID, int contMethod, int appID)
        {
            return "StoreYa Services";
        }

        private static bool IsSandbox()
        {
            string url = ConfigHelper.GetValue("Plimus_Url");
            return url.Contains("sandbox");
        }

        private static int GetNotTBContractID(int contMethod, int appID)
        {
            //by default get US contracts so tax can be calculated
            IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
            int contractID = 0;
            bool isWebApp = AppStoreManager.IsWebApp(appID);
            if (contMethod == 1)
            {
                if (isWebApp)
                {
                    contractID = bluesnapConfiguration.YearlyNoTrialUS;
                }
                else
                {
                    contractID = bluesnapConfiguration.YearlyWithTrialUS;
                }
            }
            else
            {
                //Monthly Plan 
                contractID = bluesnapConfiguration.MonthlyUS;
            }
            return contractID;
        }

        //private static int GetContractIDForSandbox(int contMethod, int appID, int? planID = null)
        //{
        //    //if (appID == (int)AppTypes.TrafficBooster && planID.HasValue && planID.Value == (int)PlanTypes.CustomPlan)
        //    //{
        //    //    return 2282951;
        //    //}

        //    //2151720	StoreYa Services - Monthly Plan 
        //    //2151722	StoreYa Services - Annual Plan without trial
        //    //2151724	StoreYa Services - Annual Plan
        //    //3245816   Etsy

        //    int contractID = 0;
        //    bool isWebApp = AppStoreManager.IsWebApp(appID);
        //    if (contMethod == 1)
        //    {
        //        if (isWebApp)
        //        {
        //            contractID = 2151722;
        //        }
        //        else
        //        {
        //            contractID = 2467303; //with trial for FB shop
        //        }
        //    }
        //    else if (contMethod == 2)
        //    {
        //        contractID = 2151724; //use yearly for sandbox
        //        ////contractID = 3245816;
        //        //throw new NotImplementedException();
        //    }
        //    else
        //    {
        //        //Monthly Plan 
        //        contractID = 2151720;
        //    }



        //    return contractID;
        //}

        public static int ReverseMethodID(int contractID, int appID)
        {
            //2151722 and 2151724 sandbox contracts
            IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
       
            int contMethod = 0;
            bool isWebApp = AppStoreManager.IsWebApp(appID);
            if (contractID == bluesnapConfiguration.YearlyNoTrial
                || contractID == bluesnapConfiguration.YearlyWithTrial
                || contractID == bluesnapConfiguration.YearlyNoTrialUS
                || contractID == bluesnapConfiguration.YearlyWithTrialUS
                || contractID == (int)FastSpringApiProvider.Contracts.YearlyNoTrialUS
                || contractID == (int)FastSpringApiProvider.Contracts.YearlyWithTrialUS
                )
            {
                contMethod = 1;
            }
            else
            {
                //Monthly Plan 
                contMethod = 0;
            }

            return contMethod;
        }

        public static OverridedPricesData GetPayPalPriceInGbp(int planId, int appId)
        {
            OverridedPricesData gbpData = new OverridedPricesData();
            if (appId == (int)AppTypes.TrafficBooster)
            {
                switch (planId)
                {
                    case (int)PlanTypes.Economy:
                        gbpData.InitialChargeAmount = 84;                   //Basic ( 250-500 visitors)
                        gbpData.RecurringChargeAmount = 140;
                        break;
                    case (int)PlanTypes.FirstClass_2:
                        gbpData.InitialChargeAmount = 228;                 //Standard (700-1,400 visitors)
                        gbpData.RecurringChargeAmount = 380;
                        break;
                    case (int)PlanTypes.PrivateJet:
                        gbpData.InitialChargeAmount = 690;                // Pro(£780 / month = 2, 000 - 4, 000 visitors)
                        gbpData.RecurringChargeAmount = 1150;
                        break;

                    //case (int)PlanTypes.CustomPlan:
                    //    break;
                    default:
                        break;
                }
            }
            else
            {
                throw new Exception("No PayPal Price In Gbp for appID" + appId);
            }
            return gbpData;
        }


        public static OverridedPricesData GetPayPalPriceInUSD(int planId, int appId)
        {
            OverridedPricesData gbpData = new OverridedPricesData();
            if (appId == (int)AppTypes.TrafficBooster)
            {
                switch (planId)
                {
                    case (int)PlanTypes.Economy:
                        gbpData.InitialChargeAmount = 120;                   //Basic ( 250-500 visitors)
                        gbpData.RecurringChargeAmount = 180;
                        break;
                    case (int)PlanTypes.FirstClass_2:
                        gbpData.InitialChargeAmount = 335;                 //Standard (700-1,400 visitors)
                        gbpData.RecurringChargeAmount = 500;
                        break;
                    case (int)PlanTypes.PrivateJet:
                        gbpData.InitialChargeAmount = 1000;                // Pro(£780 / month = 2, 000 - 4, 000 visitors)
                        gbpData.RecurringChargeAmount = 1500;
                        break;

                    //case (int)PlanTypes.CustomPlan:
                    //    break;
                    default:
                        break;
                }
            }
            else
            {
                throw new Exception("No PayPal Price In USD for appID" + appId);
            }
            return gbpData;
        }
        public bool UpdateBlueSnapSubscription(string subscriptionID, decimal? amount, DateTime nextChargeDate, int shopID, long? contractID = null, string currency = "USD")
        {
            string chargeDate = nextChargeDate.ToString("dd-MMM-yy");
            double? dAmount = null;
            if (amount.HasValue)
            {
                dAmount = (double)amount.Value;
            }
            return bs.UpdateSubscription(subscriptionID, dAmount, chargeDate, shopID, contractID, currency);
        }
        public void Dispose()
        {
            db.Dispose();
        }
    }

    public class OverridedPricesData
    {
        public decimal? InitialChargeAmount { get; set; }
        public decimal? RecurringChargeAmount { get; set; }
    }





}


