﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class BoReminderHelper
    {
        public static void SaveNewReminder(int shopID, string reminderSubject, string reminderText, DateTime reminderDate, int reminderType)
        {
            BoReminder boReminder = new BoReminder()
            {
                InsertedAt = DateTime.Now,
                ShopID = shopID,
                ReminderTitle = reminderSubject,
                ReminderText = reminderText,
                RemindAt = reminderDate,
                Type = reminderType
            };
            var db = DataHelper.GetStoreYaEntities();
            db.BoReminders.Add(boReminder);
            db.SaveChanges();

        }
    }
    public enum BoReminderTypes
    {
        InternalTask = 1,
        Email = 2,
    }
}
