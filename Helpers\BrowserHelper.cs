﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace Storeya.Core.Helpers
{
    public static class BrowserHelper
    {
        private static string[] mobileDevices = new string[] { "iphone", "ppc", "windows ce", "blackberry", "ios", "android", "opera mini", "mobile", "palm", "portable", "opera mobi", "ipad" };
        private static string[] appleDevices = new string[] { "ios", "iphone", "ipad"};

        public static bool IsMobileDevice()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.UserAgent != null)
            {
                return IsMobileDevice(HttpContext.Current.Request.UserAgent);
            }

            return false;
        }

        public static bool IsMobileDevice(string userAgent)
        {
            //if (userAgent.ToLower().Contains("ipad"))
            //{
            //    return false; //treat iPad as not mobile
            //}

            if (!string.IsNullOrEmpty(userAgent))
            {
                userAgent = userAgent.ToLower();
                return mobileDevices.Any(x => userAgent.Contains(x));
            }
            return false;
        }


        public static bool IsAppleDevice()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.UserAgent != null)
            {
                string userAgent = HttpContext.Current.Request.UserAgent.ToLower();
                return appleDevices.Any(x => userAgent.Contains(x));
            }
            return false;
        }
    }
}
