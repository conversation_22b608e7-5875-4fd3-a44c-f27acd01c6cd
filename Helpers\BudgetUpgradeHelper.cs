﻿using Storeya.Core.Models.TrafficBoosterModels.Upgrades;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class BudgetUpgradeHelper
    {
        public static UpgradeRecommendations GetDefaultUpgradeRecommendations(decimal currentPlan, out int economyBudget, out int recommendedBudget, out int premiumBudget)
        {
            UpgradeRecommendations upgradeRecommendations = new UpgradeRecommendations();
            if (currentPlan <= 250)
            {
                economyBudget = 300;
                recommendedBudget = 400;
                premiumBudget = 500;
            }
            else if (currentPlan < 499)
            {
                economyBudget = 750;
                recommendedBudget = 900;
                premiumBudget = 1000;
            }
            else if (currentPlan <= 749)
            {
                economyBudget = 1000;
                recommendedBudget = 1250;
                premiumBudget = 1500;
            }
            else if (currentPlan <= 999)
            {
                economyBudget = 1500;
                recommendedBudget = 1750;
                premiumBudget = 2000;
            }
            else
            {
                economyBudget = ((int)(currentPlan / 500)) * 500 + 1000;
                premiumBudget = economyBudget + 500;
                recommendedBudget = (economyBudget + premiumBudget) / 2;
            }


            upgradeRecommendations.EconomyBudget = NumbersFormater.FormatBigNumber(economyBudget);
            upgradeRecommendations.RecommendedBudget = NumbersFormater.FormatBigNumber(recommendedBudget);
            upgradeRecommendations.PremiumBudget = NumbersFormater.FormatBigNumber(premiumBudget);
            return upgradeRecommendations;
        }
        public static UpgradeRecommendations GetDefaultHolidaysRecommendations(decimal currentPlan, out int economyBudget, out int recommendedBudget, out int premiumBudget)
        {
            UpgradeRecommendations upgradeRecommendations = new UpgradeRecommendations();
            decimal economy = currentPlan * 1.5M; //2.5M
            decimal recommended = currentPlan * 2M; //3M
            decimal premium = currentPlan * 2.5M; //3.5M
            int roundNumber = 100;
            if (currentPlan <= 500)
            {
                roundNumber = 50;
            }
            economyBudget = (int)Math.Ceiling(economy / roundNumber) * roundNumber;
            recommendedBudget = (int)Math.Ceiling(recommended / roundNumber) * roundNumber;
            premiumBudget = (int)Math.Ceiling(premium / roundNumber) * roundNumber;
            upgradeRecommendations.EconomyBudget = NumbersFormater.FormatBigNumber(economyBudget);
            upgradeRecommendations.RecommendedBudget = NumbersFormater.FormatBigNumber(recommendedBudget);
            upgradeRecommendations.PremiumBudget = NumbersFormater.FormatBigNumber(premiumBudget);
            return upgradeRecommendations;
        }
    }


    public class UpgradeRecommendations
    {
        public string EconomyBudget { get; set; }
        public string RecommendedBudget { get; set; }
        public string PremiumBudget { get; set; }
        public bool UsingPrediction { get; set; }
        public string EconomyExpectedRevenue { get; set; }
        public string RecommendedExpectedRevenue { get; set; }
        public string PremiumExpectedRevenue { get; set; }

    }
}
