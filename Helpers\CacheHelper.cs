﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Runtime.Caching;

namespace Storeya.Core.Helpers
{
    public static class CacheHelper
    {
        public static void SaveTocache(string cacheKey, object savedItem, DateTime absoluteExpiration)
        {
            MemoryCache.Default.Add(cacheKey, savedItem, absoluteExpiration);
        }

        public static T GetFromCache<T>(string cacheKey) where T : class
        {
            return MemoryCache.Default[cacheKey] as T;
        }

        public static void RemoveFromCache(string cacheKey)
        {
            MemoryCache.Default.Remove(cacheKey);
        }

        public static bool IsIncache(string cacheKey)
        {
            return MemoryCache.Default[cacheKey] != null;
        }

        public static void ClearWebAllCache(string controller = null)
        {
            bool clearAllCache = true;
            if (!string.IsNullOrEmpty(controller))
            {
                clearAllCache = false;
                controller = controller.ToLower();
            }

            var runtimeType = typeof(System.Web.Caching.Cache);

            var ci = runtimeType.GetProperty(
               "InternalCache",
               System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

            var cache = ci.GetValue(HttpRuntime.Cache) as System.Web.Caching.CacheStoreProvider;
            var enumerator = cache.GetEnumerator();
            List<string> keys = new List<string>();
            while (enumerator.MoveNext())
            {
                keys.Add(enumerator.Key.ToString());
            }
            foreach (string key in keys)
            {
                if (clearAllCache)
                {
                    cache.Remove(key);
                }
                else
                {
                    if (key.ToLower().Contains(controller))
                    {
                        cache.Remove(key);
                    }
                }
            }
        }
    }
}




