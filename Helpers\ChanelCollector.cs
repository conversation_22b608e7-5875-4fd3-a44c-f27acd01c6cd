﻿using Storeya.Core.Models.FbAds;
using Storeya.Core.Models.ShoppingFeed;
using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public static class ChanelCollector
    {
        public static List<AwCampaignPerformanceData> ToDailyCampaignSummary(List<AwCampaignPerformanceData> results)
        {
            var dailyData = from d in results
                            group d by new { d.OriginalCampaignID, d.ReportDate, d.Campaign } into g
                            select new AwCampaignPerformanceData()
                            {
                                Clicks = g.Sum(r => r.Clicks),
                                ReportDate = g.Key.ReportDate,
                                Campaign = g.Key.Campaign,
                                Transactions = g.Sum(r => Convert.ToInt32(r.Transactions)),
                                TransactionsValue = g.Sum(r => r.TransactionsValue),
                                Cost = g.Sum(r => r.Cost),
                                OriginTransactionsValue = g.Sum(r => r.OriginTransactionsValue),
                                OriginCost = g.Sum(r => r.OriginCost),
                                Impressions = g.Sum(r => r.Impressions),
                                OriginalCampaignID = g.Key.OriginalCampaignID,
                                CampaignStatus = (int)AdCampaignStatuses.UNKNOWN
                            };

            return dailyData.ToList();
        }

        public static List<AwCampaignPerformanceData> ToDailyCampaignSummaryFB(List<InsightResult> results)
        {
            var dailyData = from d in results
                            group d by new { d.ID, d.DateStart, d.CampaignName } into g
                            select new AwCampaignPerformanceData()
                            {
                                Clicks = g.Sum(r => r.Clicks),
                                ReportDate = DateTime.Parse(g.Key.DateStart),
                                Campaign = g.Key.CampaignName,
                                Transactions = g.Sum(r => Convert.ToInt32(r.Conversions)),
                                TransactionsValue = g.Sum(r => r.ConversionsValue),
                                OriginTransactionsValue = g.Sum(r => r.OriginConversionsValue),
                                Cost = g.Sum(r => r.Spend),
                                Impressions = g.Sum(r => r.Impressions),
                                OriginalCampaignID = g.Key.ID,
                                CampaignStatus = (int)AdCampaignStatuses.UNKNOWN
                            };

            return dailyData.ToList();
        }


        public static void UpdateChannelData(DateTime from, DateTime to, int shopID, string accountID, List<AwCampaignPerformanceData> channelData, TrafficChannelsTypes trafficChannelsType, List<DateTime> datesToPreserve)
        {
            TBFeeHistoryManager feeManager = new TBFeeHistoryManager(shopID, trafficChannelsType);
            var reportDate = from.Date;
            while (reportDate <= to.Date)
            {
                if (datesToPreserve.Contains(reportDate))
                {
                    Console.WriteLine("Skip TbAdsCampaigns data update as row preserved - " + reportDate);
                }
                else
                {
                    var fbData = channelData.Where(d => d.ReportDate == reportDate).ToList();
                    var performanceDataInDB = AdCampaignsManager.GetCampaignsData(trafficChannelsType, shopID, reportDate);
                    if (IsAdsPerformanceDifferent(fbData, performanceDataInDB))
                    {
                        SaveEachCampaignPerformance(fbData, reportDate, accountID, shopID, trafficChannelsType, feeManager);
                    }
                }
                reportDate = reportDate.AddDays(1);
            }
        }

        public static void SaveEachCampaignPerformance(List<AwCampaignPerformanceData> allData, DateTime reportDate, string accountID, int shopID, TrafficChannelsTypes trafficChannelsType, TBFeeHistoryManager feeManager = null)
        {
          
            if (allData != null)
            {     
                foreach (var row in allData)
                {
                    AdCampaignsManager.UpsertCampaign(trafficChannelsType, accountID, shopID, row.OriginalCampaignID, row.Campaign, null, row.DailyBudget ?? 0, (int)row.CampaignStatus);

                    decimal? realCost = null;
                    decimal cost = row.Cost ?? 0;
                    if (feeManager != null && feeManager.HasActiveFee)
                    {
                        decimal calculatedCost = feeManager.CalculateCostForDate(row.ReportDate, row.Cost ?? 0);
                        if (calculatedCost != cost)
                        {
                            realCost = cost;
                            cost = calculatedCost;
                        }
                    }
                    AdCampaignsManager.UpsertPerformanceAll(trafficChannelsType, shopID, row.OriginalCampaignID, reportDate, row.Impressions ?? 0, row.Clicks ?? 0, cost, row.Transactions ?? 0, row.TransactionsValue ?? 0, 0, realCost: realCost);
                }
            }
        }

        private static bool IsAdsPerformanceDifferent(List<AwCampaignPerformanceData> channelData, List<AdCampaign> performanceDataInDB)
        {
            //TODO?: run real check
            return true;
        }
    }
}
