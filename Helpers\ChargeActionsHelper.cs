﻿using Storeya.Core.Models;
using Storeya.Core.Models.Charges;
using Storeya.Core.Models.Plimus;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Storeya.Core.Helpers
{
    public class ChargeActionsHelper
    {
        private static SubscriptionModel _subscription;
        public static void ProcessActions(SubscriptionModel subscription, ChargeActions ca)
        {
            _subscription = subscription;

            ChargeActionsHelper.ProcessFutureTask(ca);
            ChargeActionsHelper.UpdateContract(ca);
            ChargeActionsHelper.ProcessOneTimePayment(ca);
        }

        public static void ProcessFutureTask(ChargeActions ca) 
        {
            StoreYaEntities db = new StoreYaEntities();

            ChargeTask ct = new ChargeTask()
            {
                ChargeID = ca.Charge.ID,
                Status = 0, // 0 - opened, 1 - complete, 2 - failed
                ExecDate = (DateTime)ca.NextCalculationDate
            };

            db.ChargeTasks.AddObject(ct);

            db.SaveChanges();
        }

        public static void UpdateContract(ChargeActions ca)
        {
            string shopperID = "";
            string currency = "";
            decimal amount = 0M;

            BlueSnapApiWrapper api = new BlueSnapApiWrapper();
            PlaceOrderResponse response = api.CreateNewSubscription(shopperID, currency, amount, _subscription.ShopID );
            //response.SubscriptionID

            //TODO: care about existing subscription
        
        }

        public static void ProcessOneTimePayment(ChargeActions ca)
        {
            throw new NotImplementedException();
        }
    }
}
