﻿using Amazon.Runtime.Internal.Transform;
using CTCT.Components.EventSpot;
using HtmlAgilityPack;
using Newtonsoft.Json.Linq;
using OAuth;
using Storeya.Core.Entities;
using Storeya.Core.Models;
using Storeya.Core.Models.AdWords;
using Storeya.Core.Models.ChatGPT;
using Storeya.Core.Models.TbInternalTasks;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

using static Storeya.Core.Models.DataProviders.SquareApiProvider;
using static Storeya.Core.Models.FbAds.FbAdsSDK.CustomAudiencesResponse;
using static Storeya.Core.Models.GA.GA4Helper;
using static Storeya.Core.Models.ShopAttributes.Attributes.Accounts;
using static System.Net.Mime.MediaTypeNames;

namespace Storeya.Core.Helpers
{
    public class ChatGPTStorePageParser
    {
        private string _homePageUrl;
        private string _domain;
        private string _homePageUrlClean;
        private HttpClient _httpClient;
        //internal const string SaveDirectory = "C:\\TEMP\\SiteParcer";
        private Storeya.Core.Models.AuditTool.AuditTool _auditTool;
        private bool _fixUrl;
        private bool _saveResult;
        private string _testFoder;

        internal Dictionary<string, string> _infoPages { get; set; }
        public Dictionary<string, string> GetInfoPages()
        {
            return _infoPages;
        }
        public ChatGPTStorePageParser(string homePageUrl, bool fixUrl = true, bool saveResult = false, string testFolder = null)
        {
            _homePageUrl = homePageUrl;
            _httpClient = new HttpClient();
            _auditTool = new Storeya.Core.Models.AuditTool.AuditTool();
            _fixUrl = fixUrl;
            _saveResult = saveResult;
            _testFoder = testFolder;
            _domain = AdCopyExtractorViewModel.GetCleanHost(homePageUrl);
        }
        private string GetMarketplacePageResponce(bool readFromFile = false)
        {
            UrlResponse site_data = null;
            if (_fixUrl)
            {
                try
                {
                    site_data = MarketplaceUrlHelper.GetSiteUrl(_homePageUrl);
                }
                catch
                {
                    site_data = null;
                }
            }
            else
            {
                site_data = new UrlResponse() { URL = _homePageUrl };
            }


            if (site_data == null || site_data.URL == null)
            {
                return null;
            }
            else
            {
                Uri uri = new Uri(site_data.URL);
                string homePage = $"{uri.Scheme}://{uri.Host}";
                _domain = uri.Host;
                _homePageUrlClean = homePage;
                if (readFromFile)
                {
                    return File.ReadAllText(_testFoder + "\\" + _domain + "\\homePage.html");
                }
                return HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(homePage, allowAutoRedirect: true);
            }

        }
        public static void FillInAiShopInfo(List<AIShopInfo> aIShopsInfoes, string forcedPointedModel = null)
        {
            using (DataDogApiClient dataDogApiClient = new DataDogApiClient())
            {
                foreach (var aIShop in aIShopsInfoes)
                {
                    try
                    {
                        AIShopInfoHelper.UpdateStatusIfObjectNotMissingLink(aIShop, AIShopInfoHelper.AIShopInfo_STATUS.INPROGRES);
                        string info = GetShopInfoChatGPTResponce(aIShop.Domain, fixUrl: false, forcedPointedModel: forcedPointedModel);

                        UpdateShopInfo(info, aIShop);
                        dataDogApiClient.ReportCountValue("archivemanager.aishopinfo.success", 1);
                    }
                    catch (Exception ex)
                    {
                        AIShopInfoHelper.UpdateStatusIfObjectNotMissingLink(aIShop, AIShopInfoHelper.AIShopInfo_STATUS.FAILED);
                        Log4NetLogger.ErrorWithDB($"DOMAIN: {aIShop.Domain}", ex, (int)Log4NetLogger.SpecialShopIDs.FailedToGetAIShopInfo);
                        dataDogApiClient.ReportCountValue("archivemanager.aishopinfo.failed", 1);
                    }

                }
            }
        }

        private static void UpdateShopInfo(string info, AIShopInfo aIShop, bool save = true)
        {

            JContainer jObject = JObject.Parse(info);
            List<string> infoList = info.Split('\n').ToList();
            aIShop.TargetCountries = GetTaregtCountries(jObject["Countries"]);
            Console.WriteLine("TargetCountries:" + aIShop.TargetCountries);
            aIShop.TargetAges = GetAges(jObject["Ages"]);
            Console.WriteLine("TargetAges:" + aIShop.TargetAges);
            aIShop.TargetGender = GetGenderString(jObject["Gender"]);
            Console.WriteLine("TargetGender:" + aIShop.TargetGender);
            aIShop.Keywords = GetValuesListString(jObject["Keywords"]);
            if (aIShop.Keywords == null || aIShop.Keywords.Length < 4)
            {
                throw new Exception("Keywords are missing");
            }
            Console.WriteLine("Keywords:" + aIShop.Keywords);
            aIShop.SiteSummary = GetValuesListString(jObject["Summary"]);
            Console.WriteLine("SiteSummary:" + aIShop.SiteSummary);
            aIShop.UpdatedAt = DateTime.Now;
            if (save)
            {
                aIShop.Status = (int)AIShopInfoHelper.AIShopInfo_STATUS.DONE;
                var db = DataHelper.GetStoreYaEntities();
                db.SaveChanges();
                Console.WriteLine("Saved. Status done");
            }
        }

        private static string GetGenderString(JToken infoJson)
        {
            if (infoJson != null)
            {
                string info = infoJson.ToString().ToLower();
                if (HasInfoValue(info, "male"))
                {
                    return "Male";
                }
                if (info.Contains("female"))
                {
                    return "Female";
                }
                if (info.Contains("both"))
                {
                    return "Both";
                }
                if (info.Contains("male"))
                {
                    return "Male";
                }
            }
            return null;
        }
        private static bool HasInfoValue(string info, string value)
        {
            if (info.Contains($" {value}") || info.Contains($"'{value}'") || info.Contains($"\"{value}\""))
            {
                return true;
            }
            return false;
        }
        private static string GetAges(JToken info)
        {
            //info = GetClearInfoLine(lineNumber, info);
            if (info != null)
            {
                return GetAgesString(info.ToString());
            }
            return null;
        }
        private static string GetTaregtCountries(JToken info)
        {
            //if (info != null && info.Length > 0)
            //{
            //    info = GetClearInfoLine(lineNumber, info).Trim();
            //    char delimeter = IdentifyDelimeter(info);
            //    var splittedInfo = info.Split(delimeter);

            //    List<string> countries = new List<string>();
            //    foreach (var country in splittedInfo)
            //    {
            //        var countryClearText = country.Trim(' ', '"', '\'');
            //        string countyCode = GetCountryCode(countryClearText);
            //        if (countyCode != null)
            //        {
            //            countries.Add($"\"{countyCode}\"");
            //        }
            //    }
            //    if (countries.Count > 0)
            //    {
            //        string result = "[" + string.Join(",", countries) + "]";
            //        return result;
            //    }
            //}
            if (info != null)
            {
                List<string> countries = new List<string>();
                foreach (var token in info)
                {
                    if (token != null)
                    {
                        var countryClearText = token.ToString().Trim(' ', '"', '\'');
                        string countyCode = GetCountryCode(countryClearText);
                        if (countyCode != null)
                        {
                            countries.Add($"\"{countyCode}\"");
                        }
                    }
                }
                if (countries.Count > 0)
                {
                    var priority = new List<string> { "\"WORLDWIDE\"", "\"EUROPE\"" };
                    var reordered = priority
                        .Where(p => countries.Contains(p))
                        .Concat(countries.Where(c => !priority.Contains(c)))
                        .ToList();
                    string result = "[" + string.Join(",", reordered) + "]";
                    return result;
                }
            }
            return null;
        }

        private static string GetCountryCode(string country)
        {
            country = country.ToUpper();
            string code = CountriesHelper.IdentifyCountryCodeByCountryString(country);
            if (code != null)
            {
                return code;
            }
            else
            {
                return CountriesHelper.TryToIdentifyCountryCode(country);
            }
        }

        private static string GetValuesListString(JToken info)
        {
            if (info != null)
            {
                return info.ToString().Replace("\r\n", "");
            }
            //info = GetClearInfoLine(lineNumber, info);
            //if (clearSpaces == true)
            //{
            //    info = info.Replace(" ", "");
            //}
            //if (info.Length <= 1)
            //{
            //    return null;
            //}
            //var splittedInfo = info.Split(splitBy);
            //if (splittedInfo.Length > 0)
            //{
            //    string result = "[";
            //    int length = splittedInfo.Length;
            //    int counter = 0;
            //    foreach (var splitted in splittedInfo)
            //    {
            //        var clearText = splitted.Trim(' ', '"', '\'');
            //        counter++;
            //        result += $"\"{splitted}\"";
            //        if (counter < length)
            //        {
            //            result += ",";
            //        }
            //    }
            //    result += "]";
            //    return result;
            //}
            return null;
        }
        private static string GetAgesString(string info)
        {
            List<string> allRanges = null;
            (int? minAge, int? maxAge) = GetMinMaxValues(info);
            if (minAge != null && maxAge != null)
            {
                allRanges = GetAgesRangesList(minAge, maxAge);
            }
            if (allRanges == null || allRanges.Count == 0)
            {
                allRanges = GetAllAgesRangesList();
                //EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "No age range found", $"Min: {minAge}, Max: {maxAge}. All ages ranges were selected. Info: {info}");
            }
            return "[" + string.Join(",", allRanges) + "]";
        }
        private static List<string> GetAllAgesRangesList()
        {
            return GetAgesRangesList(0, 100);
        }
        private static List<string> GetAgesRangesList(int? minAge, int? maxAge)
        {
            List<string> result = new List<string>();
            var allRanges = TrafficBoosterSetups.GetAges().Select(x => x.Name);
            foreach (var range in allRanges)
            {
                if (range.Contains("65"))
                {
                    if (maxAge >= 65)
                    {
                        result.Add($"\"{range}\"");
                    }
                }
                else
                {
                    var ages = range.Split('-');
                    int min = int.Parse(ages[0]);
                    int max = int.Parse(ages[1]);
                    if ((minAge <= min && min <= maxAge)
                        || (minAge <= max && max <= maxAge)
                        || (min <= minAge && minAge <= max)
                        || (min <= maxAge && maxAge <= max))
                    {
                        result.Add($"\"{range}\"");
                    }
                }
            }
            return result;
        }

        public static (int? min, int? max) GetMinMaxValues(string info)
        {
            List<int> numbers = new List<int>();
            foreach (Match match in Regex.Matches(info, @"\d+"))
            {
                numbers.Add(int.Parse(match.Value));
            }
            if (numbers.Count > 0)
            {
                int min = numbers.Min();
                int max = numbers.Max();
                Console.WriteLine($"Min: {min}, Max: {max}");
                return (min, max);
            }
            return (null, null);
        }
        private static string GetClearInfoLine(int lineNumber, string info)
        {
            return info.Replace($"{lineNumber}.", "").Trim(' ', '"', '\'');
        }
        public static string GetShopInfoChatGPTResponce(string domain, bool fixUrl = true, string forcedPointedModel = null)
        {
            return GetShopInfoChatGPTResponce(domain, out string promt, fixUrl, forcedPointedModel: forcedPointedModel);
        }
        private static string GetShopInfoChatGPTResponce(string domain, out string prompt, bool fixUrl = true, string forcedPointedModel = null, string testFolder = null, bool saveResult = false)
        {
            ChatGPTStorePageParser pageParcer = new ChatGPTStorePageParser(homePageUrl: "https://" + domain, fixUrl, saveResult: saveResult, testFolder: testFolder);
            var pagesLinks = pageParcer.GetShopInformationTexts();
            int textsLength = pageParcer._infoPages?.Sum(x => x.Value?.Length) ?? 0;
            if (pagesLinks.Count == 0 || !pagesLinks.Where(x => x.Value != "failed").Any() || textsLength < 100)
            {
                throw new Exception("Failed to get shop info from pages.");
            }
            prompt = pageParcer.GetChatGptPromt();
            Console.WriteLine($"ChatGPT prompt: {prompt.Substring(0, Math.Min(300, prompt.Length))}");

            object schem = ChatGPTStructuredModel.GetAIShopInfoStructuredModel();
            var responce = ChatGPTManager.GetGptString(prompt, forcedPointedModel: forcedPointedModel, schem: schem).Trim();
            if (responce.StartsWith("\n"))
            {
                responce = responce.TrimStart('\n').Trim(); ;
            }
            Console.WriteLine($"ChatGPT responce: {responce.Substring(0, Math.Min(300, responce.Length))}");
            return responce;
        }

        public static void GetChatGptPromtResults(List<string> urls, string testFolder)
        {
            StringBuilder result = new StringBuilder();
            result.AppendLine($"url\tpromt\tCountries\tAges\tGender\tKeywords\tSummary\tCategory");
            StringBuilder unchangeableValue = new StringBuilder();
            unchangeableValue.AppendLine($"url\tCountries\tAges\tGender\tCategory");
            StringBuilder changeableValue = new StringBuilder();
            changeableValue.AppendLine($"url\tKeywords\tSummary");
            foreach (var url in urls)
            {

                try
                {
                    var responce = GetShopInfoChatGPTResponce(url, out string promt, testFolder: testFolder);
                    AIShopInfoHelper aIShopInfoHelper = new AIShopInfoHelper(url);
                    AIShopInfo aIShop = aIShopInfoHelper.GeFromDB();
                    if (aIShop == null)
                    {
                        aIShop = aIShopInfoHelper.CreateNewAIShopInfo();
                    }
                    UpdateShopInfo(responce, aIShop);
                    string category = GetShopCategoryName(aIShop);
                    string promtText = CleanForGoogleSheets(promt);


                    result.AppendLine($"{url}\t{promtText}\t{aIShop.TargetCountries}\t{aIShop.TargetAges}\t{aIShop.TargetGender}\t{aIShop.Keywords}\t{aIShop.SiteSummary}\t{category}");
                    unchangeableValue.AppendLine($"{url}\t{aIShop.TargetCountries}\t{aIShop.TargetAges}\t{aIShop.TargetGender}\t{category}");
                    changeableValue.AppendLine($"{url}\t{aIShop.Keywords}\t{aIShop.SiteSummary}");

                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error fetching information pages: {ex.Message}");
                    continue;
                }
            }
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            File.WriteAllText(testFolder + $"\\gptPrompts{timestamp}.csv", result.ToString());
            File.WriteAllText(testFolder + $"\\gptPromptsUnchangeable{timestamp}.csv", unchangeableValue.ToString());
            File.WriteAllText(testFolder + $"\\gptPromptsChangeable{timestamp}.csv", changeableValue.ToString());
        }
        public static string GetShopCategoryName(AIShopInfo aIShop)
        {
            int? categoryID = GetShopCategory(aIShop);
            if (categoryID != null)
            {
                var category = TbCategoriesHelper.GetOnlineStoreCategories().FirstOrDefault(x => x.ID == categoryID);
                if (category != null)
                {
                    return category.Name;
                }
            }
            return null;
        }
        public static int? GetShopCategory(AIShopInfo aIShop)
        {
            if (aIShop == null || aIShop.SiteSummary == null)
            {
                return null;
            }
            string categoriesString = GetCategoriesString();
            string prompt = GetGPTPromptForGettingCategory(aIShop, categoriesString);
            var gptResponce = ChatGPTManager.GetGptString(prompt, forcedPointedModel: "gpt-4").Trim();
            int? result = ExtractCategoryFromResponse(gptResponce);
            return result;
        }

        private static int? TryToMatchCategory(List<string> allNumbers)
        {
            var categories = TbCategoriesHelper.GetOnlineStoreCategories();
            foreach (var number in allNumbers)
            {
                int num = int.Parse(number);
                var cat = categories.FirstOrDefault(x => x.ID == num);
                if (cat != null)
                {
                    return num;
                }
            }
            return null;
        }

        private static string GetGPTPromptForGettingCategory(AIShopInfo aIShop, string categoriesString)
        {
            string prompt = "You are tasked with identifying the most appropriate target category for a website based on provided information. Here's what you need to do:\r\n" +
      "\r\n" +
      "First, review the following list of categories and their corresponding numbers:\r\n" +
      "\r\n" +
      "<categories>\r\n" +
      $"{categoriesString}\r\n" +
      "</categories>\r\n" +
      "\r\nNow, consider the following keywords related to the website:\r\n" +
      "\r\n" +
      "<keywords>\r\n" +
      $"{aIShop.Keywords}\r\n" +
      "</keywords>\r\n" +
      "\r\n" +
      "And here's a brief summary of the website's content:\r\n" +
      "\r\n" +
      "<summary>\r\n" +
      $"{aIShop.SiteSummary}\r\n" +
      "</summary>\r\n" +
      "\r\n" +
      "Your task is to analyze the keywords and summary, and determine which category from the list best fits the website's content. Follow these steps:\r\n" +
      "\r\n" +
      "1. Carefully read through the keywords and summary.\r\n" +
      "2. Compare the information to each category in the list.\r\n" +
      "3. Identify which category most closely matches the website's content based on the keywords and summary.\r\n" +
      "4. Select the most appropriate category.\r\n" +
      "\r\n" +
      "Important notes:\r\n" +
      "- Focus on the main theme or purpose of the website.\r\n" +
      "- If multiple categories seem relevant, choose the one that best represents the primary focus of the site.\r\n" +
      "- If no category seems to fit perfectly, choose the closest match or the \"Other\" category if truly nothing else fits.\r\n" +
      "\r\n" +
      "Provide your answer as follows:\r\n" +
      "1. Write only the number corresponding to the chosen category.\r\n" +
      "\r\n" +
      "Write your response in the following format:\r\n" +
      "\r\n" +
      "<category_number>\r\n" +
      "</category_number>";
            return prompt;
        }

        private static string GetCategoriesString()
        {
            var categories = TbCategoriesHelper.GetOnlineStoreCategories();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("[");
            var counter = 0;
            var total = categories.Count;
            foreach (var cat in categories)
            {
                counter++;
                stringBuilder.Append($"{cat.Name}: {cat.ID}");
                if (counter < total)
                {
                    stringBuilder.Append(", ");
                }
            }
            stringBuilder.Append("]");
            var categoriesString = stringBuilder.ToString();
            return categoriesString;
        }

        private static string CleanForGoogleSheets(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            input = input.Replace("\t", " ").Replace("\n", " ").Replace("\r", "").Replace("5️⃣", "50").Replace("4️⃣", "40").Replace("3️⃣", "30")
                .Replace("2️⃣", "20").Replace("1️⃣", "10").Replace("⚠️", "").Replace("💡", "");

            return input;
        }
        //public static void AnaliseShopsFromTheList(List<string> urls)
        //{
        //    StringBuilder result = new StringBuilder();
        //    result.Append("URL" + "\t");
        //    foreach (var pageItem in PagesSearchWords.GetInfoPages())
        //    {
        //        result.Append(pageItem.Key + "\t");
        //    }
        //    foreach (var url in urls)
        //    {
        //        ChatGPTStorePageParser pageParcer = new ChatGPTStorePageParser(url);
        //        var pagesLinks = pageParcer.GetShopInformationTexts();
        //        result.AppendLine();
        //        result.Append(url + "\t");
        //        foreach (var pageItem in pagesLinks)
        //        {
        //            result.Append(pageItem.Value + "\t");
        //        }
        //    }
        //    File.WriteAllText(ChatGPTStorePageParser.SaveDirectory + "\\output.csv", result.ToString());
        //}
        public string GetInformationString(string delimeter = ";", bool makeTitlesBolt = false)
        {
            StringBuilder infoPages = new StringBuilder();
            foreach (var info in _infoPages)
            {
                if (info.Value != null && info.Value.Length > 0)
                {
                    string openBolt = null;
                    string closeBolt = null;
                    if (makeTitlesBolt)
                    {
                        openBolt = "<b>";
                        closeBolt = "</b>";
                    }
                    infoPages.Append(openBolt + info.Key + closeBolt + ": " + info.Value + $"{delimeter} ");
                }
            }
            return infoPages.ToString();
        }
        public string GetChatGptPromt()
        {
            string info = GetInformationString();

            string prompt =
            "Analyze the provided store information and return a structured JSON response. \n" +
            "⚠️ **IMPORTANT:** Use ONLY the given information. If a category has no clear data, return an **empty array []** (not a guess).\n\n" +

            "1️⃣ **Countries** (array):\n" +
            "- If the store targets specific countries, return their codes. Example: [\"BE\",\"BG\"]\n" +
            "- If it is worldwide, return [\"Worldwide\"], if it targets all of Europe, return [\"Europe\"]. If there is any information about the store’s original country (such as city, country name, or currency), include the country code as well — for example: [\"Worldwide\",\"US\"] or [\"Europe\",\"DE\"].\n" +
            "- If no information is available, return [].\n\n" +

            "2️⃣ **Ages** (array):\n" +
            "- Provide only the given age range(s) from: [\"18-24\", \"25-34\", \"35-44\", \"45-54\", \"55-64\", \"65 or more\"].\n" +
            "- If the information is unclear, make a confident and well-reasoned assumption based on typical market positioning, product type, and visual style.\n" +
            "- Avoid returning an empty array — always make your best guess based on available cues.\n" +
            "- If the store appeals to multiple age groups, list all relevant ranges.\n" +

            "3️⃣ **Gender** (string):\n" +
            "- If no specific gender is mentioned, return \"both\".\n\n" +


            "4️⃣ **Keywords** (array):  \r\n" +
            "- Imagine you are a PPC expert running a highly targeted ad campaign for this store.  \r\n" +
            "- Generate **5 unique long-tail keywords** (2-4 words each) that are **highly relevant to the store’s niche, products, and audience**.  \r\n- **Avoid generic terms** like \"sale\", \"delivery\", \"fast shipping\", \"best price\", or broad industry terms like \"auto parts\".  \r\n" +
            "- Instead, use **specific product attributes, unique selling points, and detailed customer intent phrases**.  \r\n" +
            "- Do not include branded words.  \r\n" +
            "- Output only the keyword phrases as an array.  " +


            "5️⃣ **Summary** (array, exactly 2 items):\n" +
            "- Summarize what makes this store unique.\n" +

            "⚠️ **IMPORTANT:** DO NOT guess or assume missing data. If unsure, return an empty array []!\n\n" +

            "💡 **Example Correct Response:**\n" +
            "{\n" +
            "  \"Countries\": [],\n" +
            "  \"Ages\": [],\n" +
            "  \"Gender\": \"both\",\n" +
            "  \"Keywords\": [],\n" +
            "  \"Summary\": []\n" +
            "}\n\n" +

            $"Analyze this store: {info}";
            Console.WriteLine($"ChatGPT promt");
            //string prompt = " Answer the below questions: " +
            //    "1 What's their country's target audience? Give me only country code (all of Europe - 'Europe', world - 'Worldwide', but first specify origin country code (if exist));" +
            //    "2 What's the age of their target audience? Give me only range(or ranges): 18-24, 35-44, 45-54, 55-64, 65 or more;" +
            //    "3 What's the gender of their target audience: female, male or both? If there is no gender info - return 'both';" +
            //    "4 If you were a PPC expert advertising this site in Google Ads, what 5 long tail keywords would you use? Provided phrases of 2-4 keywords that are Non branded and general not general like sale or delivery. Give only phrases split by ';';" +
            //    "5. 2 lines that summarize what is unique about the store, spitted by ';';" +
            //    $"Use next shop info show site: {info} "; //old promt with keywords
            //Console.WriteLine($"old promt with keywords");
            //string prompt = "For the store which info is provided below, Answer the below questions: " +
            //        "1 What's their country's target audience? Give me only country code (all of Europe - 'Europe', world - 'Worldwide', but first specify origin country code (if exists));" +
            //        "2 What's the age of their target audience? Give me only range(or ranges): 18-24, 25-34, 35-44, 45-54, 55-64, 65 or more;" +
            //        "3 What's the gender of their target audience: female, male or both? If there is no gender info - return 'both';" +
            //        "4 If you were a PPC expert advertising this site in Google Ads, what 5 long tail keywords would you use? Provided phrases of 2-4 keywords that are Non branded and general not general like sale or delivery. Give only phrases split by ';';" +
            //        "5. 2 lines that summarize what is unique about the store, split by ';';" +
            //        $"Use next shop info show site: {info} "; //Yariv's promt
            //Console.WriteLine($"Yariv's promt");
            return prompt;
        }
        public Dictionary<string, string> GetShopInformationTexts(bool stopIfLongTextIsFound = true)
        {
            Dictionary<string, string> keyValuePairs = new Dictionary<string, string>();
            try
            {
                _infoPages = new Dictionary<string, string>();
                string htmlContent = null;
                bool readFromFile = false;
                if (_testFoder != null && !_saveResult)
                {
                    readFromFile = true;
                }
                htmlContent = GetMarketplacePageResponce(readFromFile);

                if (htmlContent == null)
                {
                    throw new Exception("Failed to get HTML content");
                }
                var metaDescription = AdCopyExtractor.GetDescriptionFromHtml(htmlContent);
                _infoPages.Add("meta description", metaDescription);
                var collectionsTitles = GetCollectionLinksTitles(htmlContent);
                _infoPages.Add("links titles on the home page", collectionsTitles);
                string addressFromHomepage = TryToGetAddressFromHomepage(htmlContent);
                if (addressFromHomepage != null)
                {
                    _infoPages.Add("Address from homepage", addressFromHomepage);
                }
                if (_saveResult && _testFoder != null)
                {
                    SaveHtmlToFile(htmlContent, "homePage");
                    SaveHtmlToFile(metaDescription, "metaDescription");
                }
                if (htmlContent != null)
                {
                    bool getOnlyPriorityPage = false;
                    foreach (var page in PagesSearchWords.GetInfoPages())
                    {
                        //if (_infoPages["shipping"] != null && page.Key == "contactUs")
                        //{
                        //    continue;
                        //}
                        if (getOnlyPriorityPage && !PagesSearchWords.IsPriorityPage(page.Key))
                        {
                            continue;
                        }
                        List<string> searchWords = PagesSearchWords.GetSearchWordsByPageName(page.Key);
                        string infoLink = GetLinkByWordsList(htmlContent, searchWords);
                        if (infoLink != null && !infoLink.Contains("http") && !infoLink.Contains(_homePageUrlClean))
                        {
                            infoLink = _homePageUrlClean + infoLink;
                        }
                        if (infoLink != null)
                        {
                            string text = GetHtmlCleanText(infoLink, page.Key, _saveResult);
                            _infoPages.Add(page.Key, text);
                            keyValuePairs.Add(page.Key, infoLink);
                            if ((text.Length > 1500 && page.Key != "shipping") && stopIfLongTextIsFound)
                            {
                                getOnlyPriorityPage = true;
                                continue;
                            }
                        }
                        else
                        {
                            _infoPages.Add(page.Key, null);
                            keyValuePairs.Add(page.Key, " ");
                        }

                    }


                    int totalLength = _infoPages.Where(x => x.Value != null).Sum(x => x.Value.Length);
                    if (totalLength < 4000)
                    {
                        var text = HtmlCleaner.CleanHtml(htmlContent);
                        if (text != null && text.Length > 300)
                        {
                            _infoPages.Add("homePage", text);
                            keyValuePairs.Add("homePage", " ");
                        }
                    }

                }

                return keyValuePairs;

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching information pages: {ex.Message}");
                if (keyValuePairs.Count == 0)
                {
                    foreach (var page in PagesSearchWords.GetInfoPages())
                    {
                        keyValuePairs.Add(page.Key, "failed");
                    }
                }
                return keyValuePairs;
            }
        }

        private string TryToGetAddressFromHomepage(string htmlContent)
        {
            try
            {
                string pattern = @"address:\s*([A-Za-z0-9 ,:-]+)";

                Match match = Regex.Match(htmlContent, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }
            catch
            {

            }
            return null;
        }

        private string GetCollectionLinksTitles(string htmlContent)
        {
            List<string> searchFor = new List<string>() { "collections/" };
            var links = _auditTool.FindDesiredLinks(htmlContent, searchFor, getTitles: true);
            if (links != null && links.Count > 0)
            {
                return string.Join(",", links.ToArray());
            }
            return null;
        }

        public string GetHtmlCleanText(string aboutUsLink, string pageName, bool saveResult, bool doNotDeleteImages = false)
        {
            string rawHtml = null;
            if (_testFoder != null)
            {
                rawHtml = File.ReadAllText(_testFoder + "\\" + _domain + "\\" + pageName + "Raw.html");
            }
            else
            {
                rawHtml = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(aboutUsLink);
            }

            if (rawHtml != null && saveResult && _testFoder != null)
            {
                SaveHtmlToFile(rawHtml, pageName + "Raw");
            }
            var text = HtmlCleaner.CleanHtml(rawHtml, doNotDeleteImages);
            if (text != null && saveResult && _testFoder != null)
            {
                SaveHtmlToFile(text, pageName + "Text");
            }
            return text;
        }

        private string GetLinkByWordsList(string htmlContent, List<string> words)
        {
            List<string> links = _auditTool.FindDesiredLinks(htmlContent, words);
            if (links != null && links.Count > 0)
            {
                foreach (var word in words)
                {
                    var link = links.Where(x => x.Contains(word)).FirstOrDefault();
                    if (link != null)
                    {
                        return link;
                    }
                }
                return links[0];
            }
            return null;
        }

        private void SaveHtmlToFile(string htmlContent, string fileName)
        {
            try
            {
                string fullDirectory = _testFoder + "\\" + _domain;
                if (!Directory.Exists(fullDirectory))
                {
                    Directory.CreateDirectory(fullDirectory);
                }
                string filePath = Path.Combine(fullDirectory, $"{fileName}.html");
                File.WriteAllText(filePath, htmlContent);
                Console.WriteLine($"HTML saved to: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving HTML file: {ex.Message}");
            }
        }
        private static char IdentifyDelimeter(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return ',';
            }

            int commaCount = input.Count(c => c == ',');
            int semicolonCount = input.Count(c => c == ';');

            if (commaCount > semicolonCount)
            {
                return ',';
            }
            else if (semicolonCount > commaCount)
            {
                return ';';
            }
            else
            {
                return ',';
            }
        }
        private static int? ExtractCategoryFromResponse(string gptResponce)
        {
            if (string.IsNullOrEmpty(gptResponce))
            {
                return null;
            }
            var regex = new Regex(@"\d+");
            var matches = regex.Matches(gptResponce);
            var allNumbers = matches.Cast<Match>().Select(m => m.Value).ToList();
            if (allNumbers != null && allNumbers.Count > 0)
            {
                return TryToMatchCategory(allNumbers);
            }
            return null;
        }

        public static void RunEXETests()
        {
            string testFolder = Path.GetFullPath(Path.Combine(Directory.GetCurrentDirectory(), @"..\..\..\GPTTests\"));
            List<string> urls = ReadURLsFromFile(testFolder + "urls.txt");
            ChatGPTStorePageParser.GetChatGptPromtResults(urls, testFolder);
        }
        private static List<string> ReadURLsFromFile(string filePath)
        {
            List<string> urls = new List<string>();

            try
            {
                var lines = File.ReadAllLines(filePath);
                foreach (var line in lines)
                {
                    urls.Add(line);
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., file not found, access denied)
                Console.WriteLine($"Error reading shop IDs: {ex.Message}");
            }

            return urls;
        }
    }
    internal class PagesSearchWords
    {
        public static readonly List<string> AboutUs = new List<string> { "about-us", "about us", "aboutus", "about" };
        public static readonly List<string> ContactUs = new List<string> { "contact" };
        public static readonly List<string> FAQ = new List<string> { "faq" };
        public static readonly List<string> Help = new List<string> { "help" };
        public static readonly List<string> Support = new List<string> { "support" };
        public static readonly List<string> Shipping = new List<string> { "shipping-information", "shipping-policy", "shipping" };
        public static Dictionary<string, string> GetInfoPages()
        {
            return new Dictionary<string, string>()
            {
                {"shipping", null },
                { "aboutUs", null },
                {"contactUs", null },
                {"faq", null },
                { "help", null},
                {"support", null},

             };
        }
        public static bool IsPriorityPage(string name)
        {
            switch (name)
            {
                case "shipping":
                case "contactUs":
                    return true;
                default:
                    return false;
            }
        }
        internal static List<string> GetSearchWordsByPageName(string key)
        {
            switch (key)
            {
                case "aboutUs":
                    return PagesSearchWords.AboutUs;
                case "contactUs":
                    return PagesSearchWords.ContactUs;
                case "faq":
                    return PagesSearchWords.FAQ;
                case "help":
                    return PagesSearchWords.Help;
                case "support":
                    return PagesSearchWords.Support;
                case "shipping":
                    return PagesSearchWords.Shipping;
                default: return null;
            }
        }
    }


    public class HtmlCleaner
    {
        private static string ExtractTextFromBody(string html, bool doNotDeleteImages = false)
        {
            List<string> tagsToRemove = new List<string>()
            {
                "head",
                "script",
                "style",
                "iframe",
                "a>",
                "a ",
                "header",
                "img",
                "svg",
                "form",
                "button",
                "footer"
            };
            if (doNotDeleteImages)
            {
                tagsToRemove.Remove("img");
                tagsToRemove.Remove("a>");
                tagsToRemove.Remove("a ");
            }
            foreach (var tag in tagsToRemove)
            {
                html = RemoveTagByRegex(html, tag);
            }
            return html;
        }

        private static string RemoveTagByRegex(string html, string tag)
        {
            string closeTag = tag.Replace(" ", "").Replace(">", "");
            html = Regex.Replace(html, $"<{tag}[\\s\\S]*?</{closeTag}>", "", RegexOptions.IgnoreCase);
            //html = html.Replace($"<{tag}>", "").Replace($"</{closeTag}>", "");
            return html;
        }

        private static string RemoveAllTags(string html)
        {
            html = Regex.Replace(html, "<[^>]+>", " ");

            html = Regex.Replace(html, "\\s+", " ").Trim();
            return html;
        }
        public static string CleanHtml(string htmlContent, bool doNotDeleteImages = false)
        {
            //htmlContent = RemoveUpToLastElement(htmlContent, "<footer");
            htmlContent = ExtractTextFromBody(htmlContent, doNotDeleteImages);
            if (!doNotDeleteImages)
            {
                htmlContent = RemoveHiddenTexts(htmlContent);
                htmlContent = RemoveAllTags(htmlContent);
            }
            return htmlContent;

            //foreach (var node in htmlDoc.DocumentNode.Descendants().ToList())
            //{
            //    //if (node.Name == "script" || node.Name == "style" || node.Name == "img" || node.Name == "a" || node.Name == "head" || node.Name == "footer")
            //    //{
            //    //    if (node.InnerText.Contains("response during"))
            //    //    {
            //    //        Console.WriteLine("Don't delete");
            //    //    }
            //    //    node.Remove();
            //    //}
            //    if (node.Name != "div" && node.Name != "p" && node.Name != "main" && node.Name != "strong"
            //        && node.Name != "body" && node.Name != "section" && node.Name != "#text" && node.Name != "html"
            //        && node.Name != "ul" && node.Name != "li")
            //    {
            //        if (node.InnerText.Contains("buy one get one 75% off sale ends tomorrow"))
            //        {
            //            Console.WriteLine("delete");
            //        }
            //        if (node.InnerText.Contains("48 hours"))
            //        {
            //            Console.WriteLine("Don't delete");
            //        }
            //        node.Remove();
            //    }
            //    string currentText = node.InnerText;

            //    if (currentText != null)
            //    {
            //        if (currentText.ToLower().Contains("cart updated") || currentText.ToLower().Contains("checkout") || currentText.ToLower().Contains("view cart"))
            //        {
            //            var length = string.Join(" ", currentText.Split(new[] { '\r', '\n', '\t' }, StringSplitOptions.RemoveEmptyEntries)).Replace(" ", "").Length;
            //            if (length < 60)
            //            {
            //                node.Remove();
            //            }
            //        }
            //    }

            //}

            //var text = htmlDoc.DocumentNode.InnerHtml.Trim();

            //return string.Join(" ", text.Split(new[] { '\r', '\n', '\t' }, StringSplitOptions.RemoveEmptyEntries));
        }

        private static string RemoveHiddenTexts(string htmlContent)
        {
            try
            {
                var htmlDoc = new HtmlDocument();
                htmlDoc.LoadHtml(htmlContent);
                //foreach (var node in htmlDoc.DocumentNode.Descendants().ToList())
                //{
                //    if (node.Name == "section")
                //    {
                //        if (node.InnerText.Contains("Spend over $60 and receive free shipping!"))
                //        {
                //            Console.WriteLine("error");
                //        }
                //    }
                //}
                var txt = htmlDoc.DocumentNode.InnerText;
                return string.Join(" ", txt.Split(new[] { '\r', '\n', '\t' }, StringSplitOptions.RemoveEmptyEntries));
            }
            catch
            {
                Console.WriteLine("Html is in not correct format");
                return htmlContent;
            }
        }

        private static string RemoveUpToLastElement(string input, string element)
        {
            int index = input.LastIndexOf(element, StringComparison.OrdinalIgnoreCase);
            return index >= 0 ? input.Substring(index) : input;
        }


        private static string CheckIfWeAreMissingSomething(string textAfterCleaning, List<string> listPhrasesToFind)
        {
            string missingPhrases = "";
            foreach (var phrase in listPhrasesToFind)
            {
                if (!textAfterCleaning.Contains(phrase))
                {
                    missingPhrases += phrase + ",";
                }
            }
            return missingPhrases;
        }
    }
}
