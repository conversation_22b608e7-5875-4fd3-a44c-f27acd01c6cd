﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Amazon.S3.IO;
using System.IO;

namespace Storeya.Core.Helpers
{
    public class S3File
    {
        public string Url { get; set; }
        public string Name { get; set; }
    }

    public class CloudManager
    {
        public static void Backup(string fileName, int historyToKeep, string settingName = null)
        {

            var bucketName = ConfigHelper.GetValue("S3BucketForImages", "storeya-dev");
            var awsKey = ConfigHelper.GetValue("AWSAccessKey");
            if (string.IsNullOrEmpty(awsKey))
            {
                return;
            }
            string uploadFolder = ConfigHelper.GetValue(settingName == null ? "ProductImages.Path" : settingName);
            string bucketKey = fileName.Substring(fileName.IndexOf(uploadFolder));
            bucketKey = bucketKey.Replace(@"\", @"/");

            CloudManager c = new CloudManager(bucketName);
            string file = Path.GetFileNameWithoutExtension(fileName);
            string extention = Path.GetExtension(fileName);
            string path = string.Format("http://s3.amazonaws.com/{0}/{1}", bucketName, bucketKey.Substring(0, bucketKey.Length - file.Length - extention.Length));
            string destinationFile = string.Empty;

            for (int i = historyToKeep; i > 1; i--)
            {
                string sourceFile = string.Format("{0}{1}_{2}{3}", path, file, i - 1, extention);
                destinationFile = string.Format("{0}{1}_{2}{3}", path, file, i, extention);
                c.CopyFile(sourceFile, destinationFile, true, settingName);
            }
            destinationFile = string.Format("{0}{1}_{2}{3}", path, file, 1, extention);
            fileName = string.Format("{0}{1}{2}", path, file, extention);
            ConsoleAppHelper.WriteLog("Cloud Backup file Name" + fileName);
            c.CopyFile(fileName, destinationFile, true, settingName);
        }

        public static void Copy(string sourceFile, string destinationFile, bool ignoreExceptions, string settingName = null)
        {
            var bucketName = ConfigHelper.GetValue("S3BucketForImages", "storeya-dev");
            var awsKey = ConfigHelper.GetValue("AWSAccessKey");
            if (string.IsNullOrEmpty(awsKey))
            {
                return;
            }
            CloudManager c = new CloudManager(bucketName);
            c.CopyFile(sourceFile, destinationFile, ignoreExceptions, settingName);
        }

        public static void Upload(string fileName, int historyToKeep = 0, string settingName = null, bool forceSettingName = false)
        {
            var bucketName = ConfigHelper.GetValue("S3BucketForImages", "storeya-dev");
            var awsKey = ConfigHelper.GetValue("AWSAccessKey");
            if (string.IsNullOrEmpty(awsKey))
            {
                return;
            }
            if (historyToKeep > 0)
            {
                Backup(fileName, historyToKeep);
            }
            CloudManager c = new CloudManager(bucketName);
            c.UploadFile(fileName, settingName, forceSettingName);
        }
        public static void UploadFileText(string filename, string text)
        {
            var bucketName = ConfigHelper.GetValue("S3BucketForImages", "storeya-dev");
            var awsKey = ConfigHelper.GetValue("AWSAccessKey");
            if (string.IsNullOrEmpty(awsKey))
            {
                return;
            }
            CloudManager c = new CloudManager(bucketName);
            c.UploadText(filename, text);
        }

        public static List<string> GetFolders()
        {
            List<string> folders = new List<string>();

            var bucketName = ConfigHelper.GetValue("S3BucketForImages", "storeya-dev");
            var awsKey = ConfigHelper.GetValue("AWSAccessKey");
            if (string.IsNullOrEmpty(awsKey))
            {
                return null;
            }
            CloudManager c = new CloudManager(bucketName);

            TransferUtility utility = new TransferUtility();
            S3DirectoryInfo directoryInfo = new S3DirectoryInfo(utility.S3Client, "storeya-dev");
            S3DirectoryInfo[] directories = directoryInfo.GetDirectories();

            foreach (var directory in directories)
            {
                folders.Add(directory.Name);
            }
            return folders;
        }

        private string _bucketName;





        public CloudManager(string bucketName)
        {
            _bucketName = bucketName;
        }

        public static bool FolderExists(string folderPath)
        {
            var bucketName = ConfigHelper.GetValue("S3BucketForImages", "storeya-dev");
            var awsKey = ConfigHelper.GetValue("AWSAccessKey");
            if (string.IsNullOrEmpty(awsKey))
            {
                return false;
            }
            TransferUtility utility = new TransferUtility();
            S3DirectoryInfo directoryInfo = new S3DirectoryInfo(utility.S3Client, bucketName, folderPath);
            bool bucketExists = directoryInfo.Exists;
            return bucketExists;
        }

        public static void CreateFolder(string folderName)
        {
            var bucketName = ConfigHelper.GetValue("S3BucketForImages", "storeya-dev");
            var awsKey = ConfigHelper.GetValue("AWSAccessKey");
            if (string.IsNullOrEmpty(awsKey))
            {
                return;
            }
            TransferUtility utility = new TransferUtility();
            S3DirectoryInfo directoryInfo = new S3DirectoryInfo(utility.S3Client, bucketName, folderName);
            directoryInfo.Create();
        }

        public static List<S3File> GetFiles(string dir)
        {
            List<S3File> files = null;
            var bucketName = ConfigHelper.GetValue("S3BucketForImages", "storeya-dev");
            var awsKey = ConfigHelper.GetValue("AWSAccessKey");
            if (string.IsNullOrEmpty(awsKey))
            {
                return null;
            }
            TransferUtility utility = new TransferUtility();

            S3DirectoryInfo s3DirectoryInfo = new Amazon.S3.IO.S3DirectoryInfo(utility.S3Client, bucketName, dir);
            S3FileInfo[] filesInfo = s3DirectoryInfo.GetFiles();

            if (filesInfo != null && filesInfo.Length > 0)
            {
                files = new List<S3File>();

                for (int i = 0; i < filesInfo.Length; i++)
                {
                    string url = string.Format("{0}/{1}/{2}", ImagePathHelper.GetServerPath(), dir, filesInfo[i].Name);

                    files.Add(new S3File() { Name = filesInfo[i].Name, Url = url });
                }
            }
            return files;
        }


        private void UploadFile(string fileName, string settingName = null, bool forceSettingName = false)
        {
            string uploadFolder = ConfigHelper.GetValue(settingName == null ? "ProductImages.Path" : settingName);
            if (forceSettingName)
            {
                uploadFolder = settingName;
            }
            string bucketKey = fileName.Substring(fileName.IndexOf(uploadFolder));
            bucketKey = bucketKey.Replace(@"\", @"/");

            var uploadRequest = new TransferUtilityUploadRequest
            {
                FilePath = fileName,
                BucketName = _bucketName,
                CannedACL = S3CannedACL.PublicRead,
                Key = bucketKey
            };


            TransferUtility utility = new TransferUtility();

            utility.Upload(uploadRequest);
            // Log4NetLogger.InfoWithDB("Image was saved on S3", null, (int)Log4NetLogger.SpecialShopIDs.ImageLog);

        }
        private void UploadText(string fileName, string text, string settingName = null)
        {
            string uploadFolder = ConfigHelper.GetValue(settingName == null ? "ProductImages.Path" : settingName);
            string bucketKey = fileName.Substring(fileName.IndexOf(uploadFolder));
            bucketKey = bucketKey.Replace(@"\", @"/");

            var uploadRequest = new TransferUtilityUploadRequest
            {
                InputStream = new MemoryStream(Encoding.UTF8.GetBytes(text)),
                BucketName = _bucketName,
                CannedACL = S3CannedACL.PublicRead,
                Key = bucketKey,
                ContentType = "text/plain; charset=utf-8",
            };


            TransferUtility utility = new TransferUtility();

            utility.Upload(uploadRequest);
        }

        private CopyObjectResponse CopyFile(string sourceFile, string destinationFile, bool ignoreExceptions, string settingName = null)
        {
            string uploadFolder = ConfigHelper.GetValue(settingName == null ? "ProductImages.Path" : settingName);
            string bucketKey = sourceFile.Substring(sourceFile.IndexOf(uploadFolder));
            bucketKey = bucketKey.Replace(@"\", @"/");
            string destinationKey = destinationFile.Substring(destinationFile.IndexOf(uploadFolder));
            destinationFile = destinationFile.Replace(@"\", @"/");


            var copyRequest = new CopyObjectRequest
            {
                DestinationBucket = _bucketName,
                SourceBucket = _bucketName,
                CannedACL = S3CannedACL.PublicRead,
                SourceKey = bucketKey,
                DestinationKey = destinationKey
            };
            //http://s3.amazonaws.com/strys3/Uploads/feeds/0/Tracking.txt

            TransferUtility utility = new TransferUtility();
            try
            {
                CopyObjectResponse res = utility.S3Client.CopyObject(copyRequest);
                return res;
            }
            catch (Exception ex)
            {
                if (ignoreExceptions)
                {
                    return null;
                }
                throw ex;
            }
        }
    }
}
