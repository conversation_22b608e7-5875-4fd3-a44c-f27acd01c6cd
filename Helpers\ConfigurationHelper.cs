﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Configuration;

namespace Storeya.Core.Helpers
{
    public class ConfigHelper
    {

        public static string GetValue(string key, string defaultValue = "", string section = null)
        {
            if (string.IsNullOrEmpty(section))
            {
                if (ConfigurationManager.AppSettings[key] != null)
                {
                    return ConfigurationManager.AppSettings[key].ToString();
                }
                return defaultValue;
            }
            System.Collections.Hashtable sec = (System.Collections.Hashtable)ConfigurationManager.GetSection(section);
            if (sec == null)
            {
                return null;
            }
            if (sec[key] != null)
            {
                return sec[key].ToString();
            }
            return defaultValue;
        }

        public static bool GetBoolValue(string key, bool defaultValue = false)
        {
            if (ConfigurationManager.AppSettings[key] != null)
            {
                return bool.Parse(ConfigurationManager.AppSettings[key]);
            }
            return defaultValue;
        }

        public static int? GetIntValue(string key, int? defaultValue = null)
        {
            if (ConfigurationManager.AppSettings[key] != null)
            {
                try
                {
                    return Convert.ToInt32(ConfigurationManager.AppSettings[key]);
                }
                catch (Exception)
                {

                }
            }
            return defaultValue;
        }
        public static bool IsSuperAdminByID(int userID, out User user)
        {
            if (userID == 0)
            {
                user = null;
                return false;
            }
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var currentUser = db.Users.Single(u => u.ID == userID);
            user = currentUser;
            return IsSuperAdmin(currentUser);
        }
        public static bool IsSuperAdmin(User user)
        {
            if (user == null)
            {
                return false;
            }
            if (user.BoRole != null && user.BoRole > 0)
            {
                return true;
            }
            else
            {
                if (ConfigurationManager.AppSettings["BoAdmins"] != null)
                {
                    return IsSuperAdmin(user.FbProfileID ?? 0);
                }
                else
                {
                    return false;
                }
            }
        }
        public static bool IsSuperAdmin(long fbProfileID)
        {
            string superAdmins = ConfigHelper.GetValue("BoAdmins");
            if (superAdmins.Split(',').Contains(fbProfileID.ToString()))
            {
                return true;
            }
            return false;
        }

        public static int GetDefaultThemeID()
        {
            string themeID = ConfigHelper.GetValue("DefaultThemeID");
            if (!string.IsNullOrEmpty(ConfigHelper.GetValue("DefaultThemeID")))
            {
                return Convert.ToInt16(themeID);
            }
            return 0;
        }

        public static bool IsResizeImages(int shopID)
        {
            string shopsToSkipResize = ConfigHelper.GetValue("ShopsToSkipImagesResize");
            return (!string.IsNullOrEmpty(shopsToSkipResize) && shopsToSkipResize.Split(',').Contains(shopID.ToString()));
        }


        public static bool IsValueInList(string key, string value)
        {
            string configValue = ConfigHelper.GetValue(key);
            if (string.IsNullOrEmpty(configValue))
                return false;
            if (configValue.Split(',').Contains(value))
            {
                return true;
            }
            return false;
        }


    }
}