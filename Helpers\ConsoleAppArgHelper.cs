﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon.Runtime.Internal;

namespace Storeya.Core.Helpers
{
    public class ConsoleAppArgHelper
    {
        public Dictionary<string, string> ParametersList { get; set; }

        public void Add(string parameter, string description)
        {
            if (ParametersList == null)
                ParametersList = new Dictionary<string, string>();

            ParametersList.Add(parameter, description);
        }

        public void Search(string parameter)
        {
            if (!string.IsNullOrEmpty(parameter))
            {
                foreach (KeyValuePair<string, string> entry in this.ParametersList)
                {
                    string key = entry.Key;
                    if (key.Contains(parameter) || entry.Value.Contains(parameter))
                    {
                        Console.ForegroundColor = ConsoleColor.Green;
                        Console.Write("[" + key + "]");
                        Console.ResetColor();

                        Console.Write(" ");

                        for (int i = 0; i < entry.Value.Length; i++)
                        {
                            if (entry.Value[i] == '[')
                            {
                                Console.ForegroundColor = ConsoleColor.Green;
                            }

                            Console.Write(entry.Value[i]);

                            if (entry.Value[i] == ']')
                            {
                                Console.ResetColor();
                            }
                        }

                        Console.Write(Environment.NewLine);

                    }

                    
                }
                Console.Write("\n\n\n");
            }
        }

        private void ShowAll()
        {
            this.ParametersList.OrderBy(obj => obj.Key).ToDictionary(obj => obj.Key, obj => obj.Value);

            foreach (KeyValuePair<string, string> entry in ParametersList)
            {
                Search(entry.Key);
            }
        }


        public static void ShowHelp(string[] args, Dictionary<string, string> argOptions)
        {
            ConsoleAppArgHelper helper = new ConsoleAppArgHelper();
            helper.ParametersList = argOptions;


            string arg1 = GetArgumentOfTheRequiredIndex(args, 1);
            if (!string.IsNullOrEmpty(arg1))
            {
                helper.Search(arg1);
            }
            else
            {
                helper.ShowAll();
            }
        }

        private static string GetArgumentOfTheRequiredIndex(string[] args, int index)
        {
            if (args != null && args.Length > index)
            {
                return args[index].ToString();
            }
            return null;
        }
    }
}
