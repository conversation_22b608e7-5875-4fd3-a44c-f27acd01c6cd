﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Helpers
{
    public class ConsoleAppHelper
    {
        public static string GetFirstArgument(string[] args)
        {
            if (args != null && args.Length > 0)
            {
                return args[0].ToString();
            }
            return null;
        }
        public static void WriteLog(string message, int shopID = 0)
        {
            Log4NetLogger.Info(message, shopID);
            var time = DateTime.Now.TimeOfDay;
            Console.WriteLine(String.Format("{0:t}", time).Substring(0, 8) + " " + message + " ShopID:" + shopID);
        }
        public static void WriteWarning(string message, int shopID = 0, Exception ex = null)
        {
            Log4NetLogger.Info(message, ex, shopID);
            ConsoleColor cc = Console.ForegroundColor;
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine(message);
            Console.ForegroundColor = cc;
        }
        public static void WriteError(string message, Exception ex, int shopID = 0)
        {
            Log4NetLogger.Error(message, ex, shopID);
            ConsoleColor cc = Console.ForegroundColor;
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine(message);
            Console.ForegroundColor = cc;
        }

        public static void WriteLogWithDB(string message, int? shopID = 0)
        {
            Log4NetLogger.InfoWithDB(message, null, shopID);
            var time = DateTime.Now.TimeOfDay;
            Console.WriteLine(String.Format("{0:t}", time).Substring(0, 8) + " " + message + " ShopID:" + shopID);
        }

        public static void WriteLogDebugWithDB(string message, int? shopID = 0)
        {
            Log4NetLogger.DebugWithDB(message, null, shopID);
            var time = DateTime.Now.TimeOfDay;
            Console.WriteLine(String.Format("{0:t}", time).Substring(0, 8) + " " + message + " ShopID:" + shopID);
        }
        public static void WriteErrorWithDB(string message, Exception ex, int shopID = 0)
        {
            Log4NetLogger.ErrorWithDB(message, ex, shopID);
            ConsoleColor cc = Console.ForegroundColor;
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine(message);
            Console.ForegroundColor = cc;
        }

        public static int GetShopIdFrom(string[] args, int index)
        {
            int shopid = 0;
            if (args.Length > index)
            {
                int.TryParse(args[index].Replace("shopid:", ""), out shopid);
            }
            return shopid;
        }

        public static string GetConsoleArgument(string[] args, string name)
        {
            if (args.Length > 0)
            {
                for (int i = 0; i < args.Length; i++)
                {
                    if (args[i].Contains(name))
                    {
                        return args[i].Replace(name, "");
                    }
                }

            }
            return null;
        }
        public static List<string> GetConsoleArgumentList(string[] args, string name, char seperator = ',')
        {
            List<string> list = new List<string>();
            if (args.Length > 0)
            {
                for (int i = 0; i < args.Length; i++)
                {
                    if (args[i].Contains(name))
                    {
                        string ret = args[i].Replace(name, "");
                        if (ret.Contains(seperator))
                        {
                            var a = ret.Split(new char[] { seperator }, StringSplitOptions.RemoveEmptyEntries);
                            foreach (var item in a)
                            {
                                list.Add(item);
                            }
                        }
                        else
                        {
                            list.Add(ret);
                        }
                        return list;
                    }
                }

            }
            return list;
        }
        public static bool HasArgument(string[] args, string param)
        {
            if (args.Length > 0)
            {
                for (int i = 0; i < args.Length; i++)
                {
                    if (args[i].ToLower() == param.ToLower())
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public static int? GetShopId(string[] args)
        {
            foreach (var arg in args)
            {
                var ar = arg.ToLower();
                if (ar.StartsWith("shopid:"))
                {
                    string shopID_string = ar.Replace("shopid:", "");
                    int shopID = Convert.ToInt32(shopID_string);
                    return shopID;
                }
            }

            Console.WriteLine("ShopID parameter wasn't found.");
            return null;
        }
        public static long? GetLongArgument(string[] args, string name, long? defaultValue = null)
        {
            name = name.Replace(":", "");
            foreach (var arg in args)
            {
                var ar = arg.ToLower();
                if (ar.StartsWith($"{name}:"))
                {
                    string int_string = ar.Replace($"{name}:", "");
                    long v = Convert.ToInt64(int_string);
                    return v;
                }
            }

            Console.WriteLine(name + " parameter wasn't found.");
            return defaultValue;
        }
        public static int? GetIntArgument(string[] args, string name, int? defaultValue = null)
        {
            name = name.Replace(":", "");
            foreach (var arg in args)
            {
                var ar = arg.ToLower();
                if (ar.StartsWith($"{name}:"))
                {
                    string int_string = ar.Replace($"{name}:", "");
                    int shopID = Convert.ToInt32(int_string);
                    return shopID;
                }
            }

            Console.WriteLine(name + " parameter wasn't found.");
            return defaultValue;
        }
        public static bool GetBoolArgument(string[] args, string name, bool defaultValue = false)
        {
            try
            {
                name = name.Replace(":", "");
                foreach (var arg in args)
                {
                    var ar = arg.ToLower();
                    if (ar.StartsWith($"{name.Replace(":", "")}:"))
                    {
                        string b_string = ar.Replace($"{name}:", "");
                        bool b = Convert.ToBoolean(b_string);
                        return b;
                    }
                }
                Console.WriteLine(name + " parameter wasn't found.");
            }
            catch
            {


            }
            return defaultValue;
        }
        public static int? GetAccountId(string[] args)
        {
            foreach (var arg in args)
            {
                if (arg.StartsWith("accountid:"))
                {
                    string accountIdString = arg.Replace("accountid:", "");
                    int accountId = Convert.ToInt32(accountIdString);
                    return accountId;
                }
            }

            Console.WriteLine("ShopID parameter wasn't found.");
            return null;
        }

        public static string GetFileName(string[] args)
        {
            foreach (var arg in args)
            {
                if (arg.StartsWith("file:"))
                {
                    return arg.Replace("file:", "");
                }
            }

            Console.WriteLine("File parameter wasn't found.");

            return null;
        }
        public static string GetArgumentByIndex(string[] args, int index)
        {
            if (args != null && args.Length > index)
            {
                return args[index].ToString();
            }
            return null;
        }

    }
}
