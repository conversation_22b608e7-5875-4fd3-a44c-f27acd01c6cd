﻿using Amazon.Runtime.Internal.Transform;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace Storeya.Core.Helpers
{
    public class CountriesHelper
    {
        static List<Country> _list;
        //static  Dictionary<string, string> _dic;

        static CountriesHelper()
        {
            FillList();
        }

        public static List<Country> GetCountries()
        {
            return _list;
        }

        public static HtmlString GetCountriesHtml(string selectedValues, string languageCode = null)
        {
            StringBuilder sb = new StringBuilder();

            if (!string.IsNullOrEmpty(languageCode) && languageCode == "es")
            {
                sb.Append("<select autocomplete=\"off\"  id=\"countries\" name=\"Country\" value=\"@Model.Settings.Country\" data-placeholder=\"Elige país...\" class=\"chosen-select chosen_drop_select\" multiple=multiple>");
            }
            else if (!string.IsNullOrEmpty(languageCode) && languageCode == "fr")
            {
                sb.Append("<select autocomplete=\"off\"  id=\"countries\" name=\"Country\" value=\"@Model.Settings.Country\" data-placeholder=\"Choisissez un pays...\" class=\"chosen-select chosen_drop_select\" multiple=multiple>");
            }
            else
            {
                sb.Append("<select autocomplete=\"off\" id=\"countries\" name=\"Country\" value=\"@Model.Settings.Country\" data-placeholder=\"Choose country...\" class=\"chosen-select chosen_drop_select\" multiple=multiple>");
            }

            string[] selected_countries = null;

            if (!string.IsNullOrEmpty(selectedValues))
            {
                selected_countries = selectedValues.Split(',');
            }

            foreach (var item in _list)
            {
                string selected = ((selected_countries != null && selected_countries.Contains(item.Code)) ? "selected" : "");

                sb.Append(string.Format("<option value=\"{0}\" {2}>{1}</option>", item.Code, item.Name, selected));
            }

            sb.Append("</select>");

            HtmlString countries_html = new HtmlString(sb.ToString());
            return countries_html;
        }

        public static string GetCountryName(string code)
        {
            Country country = _list.Find(c => c.Code == code);
            if (country != null)
            {
                return country.Name;
            }

            return "US";
        }
        public static string IdentifyCountryCodeByCountryString(string countryText)
        {
            Country country = _list.Find(c => c.Code == countryText);
            if (country != null)
            {
                return country.Code;
            }
            foreach (var item in _list)
            {
                if (countryText.ToLower().Contains(item.Name.ToLower()))
                {
                    country = item;
                }
            }
            if (country != null)
            {
                return country.Code;
            }
            return null;
        }
        public class Country
        {
            public string Name { get; set; }
            public string Code { get; set; }
        }

        public static bool UsesEnglishLanguage(string code)
        {
            if (code == "AE" || // United Arab Emirates
                code == "AU" || // AUSTRALIA
                code == "BZ" || // BELIZE
                code == "CA" || // CANADA
                code == "IE" || // IRELAND
                code == "JM" || // JAMAICA
                code == "NZ" || // NEW ZEALAND
                code == "PH" || // PHILIPPINES
                code == "ZA" || // SOUTH AFRICA
                code == "TT" || // TRINIDAD AND TOBAGO
                code == "GB" || // UNITED KINGDOM
                code == "US" || // UNITED STATES
                code == "ZW")   // ZIMBABWE
                return true;
            else
                return false;
        }

        public static string GetAdwordsLanguageCodeByCountryCode(string code)
        {
            string language = "en";
            switch (code)
            {
                case "DZ": // ALGERIA
                case "BH": // Bahrain
                case "EG": // EGYPT 
                case "IQ": // IRAQ
                case "JO": // JORDAN
                case "KW": // KUWAIT
                case "LB": // LEBANON
                case "LY": // LIBYAN ARAB JAMAHIRIYA
                case "MA": // MOROCCO
                case "OM": // OMAN
                case "QA": // QATAR
                case "SA": // SAUDI ARABIA
                case "SY": // SYRIAN ARAB REPUBLIC
                case "TN": // TUNISIA
                case "AE": // UNITED ARAB EMIRATES
                case "YE": // YEMEN  
                    language = "ar";
                    break;

                case "BG": // BULGARIA 
                    language = "bg";
                    break;

                case "CN": // CHINA
                    language = "zh_CN";  //Chinese (simplified)	zh_CN  Chinese (traditional)	zh_TW
                    break;

                case "HR": // CROATIA 
                    language = "hr";
                    break;


                case "CZ": // CZECH REPUBLIC
                    language = "cs";
                    break;

                case "DK": // DENMARK
                    language = "da";
                    break;

                //case "BE": // BELGIUM
                case "NL": // NETHERLANDS
                    language = "nl";
                    break;

                case "AU": // AUSTRALIA
                case "BZ": // BELIZE
                case "CA": // CANADA
                case "IE": // IRELAND
                case "JM": // JAMAICA
                case "NZ": // NEW ZEALAND
                case "PH": // PHILIPPINES
                case "ZA": // SOUTH AFRICA
                case "TT": // TRINIDAD AND TOBAGO
                case "GB": // UNITED KINGDOM
                case "US": // UNITED STATES
                case "ZW": // ZIMBABWE
                    language = "en";
                    break;

                case "EE": // ESTONIA
                    language = "et";
                    break;

                case "FI":  // FINLAND
                    language = "fi";
                    break;

                case "FR": //FRANCE
                //case "CA": // CANADA
                case "BE": // BELGIUM
                //case "LU": // LUXEMBOURG
                case "MC": // MONACO
                case "CH": // SWITZERLAND 
                    language = "fr";
                    break;

                case "AT": // AUSTRIA
                case "DE": // GERMANY
                case "LI": // LIECHTENSTEIN
                case "LU": // LUXEMBOURG
                    //  case "CH": // SWITZERLAND 
                    language = "de";
                    break;

                case "GR": //GREECE
                    language = "el";
                    break;

                case "IL": //ISRAEL
                    language = "iw";
                    break;

                case "IN": //INDIA
                    //language = "hi";
                    language = "en";
                    break;

                case "HU": // HUNGARY
                    language = "hu";
                    break;

                case "IS": // ICELAND
                    language = "is";
                    break;

                case "ID": // INDONESIA
                    language = "id";
                    break;

                case "IT": //ITALY
                    language = "it";
                    break;

                case "JP": // JAPAN
                    language = "ja";
                    break;

                case "KP":  // KOREA, DEMOCRATIC PEOPLE'S REPUBLIC OF
                case "KR":  // KOREA, REPUBLIC OF
                    language = "ko";
                    break;

                case "LV": //LATVIA
                    language = "lv";
                    break;

                case "LT": //LITHUANIA
                    language = "lt";
                    break;

                case "BN": //BRUNEI DARUSSALAM
                case "MY": //MALAYSIA
                    language = "ms";
                    break;

                case "NO": //NORWAY;
                    language = "no";
                    break;

                case "PL": //POLAND
                    language = "pl";
                    break;

                case "PT":  //PORTUGAL
                case "BR": //BRAZIL
                    language = "pt";
                    break;

                case "RO": //ROMANIA
                    language = "ro";
                    break;

                case "RU": //RUSSIAN FEDERATION
                case "BY": //Belarus
                case "KZ": //Kazakhstan
                    language = "ru";
                    break;

                case "RS": //SERBIA
                    language = "sr";
                    break;

                case "SK": // SLOVAKIA
                    language = "sk";
                    break;

                case "SI": //SLOVENIA
                    language = "sl";
                    break;

                case "AR": //SPAIN
                case "ES": //SPAIN
                case "BO": //BOLIVIA, PLURINATIONAL STATE OF
                case "CL": //CHILE
                case "CO": //COLOMBIA
                case "CR": //COSTA RICA
                case "DO": //DOMINICAN REPUBLIC
                case "EC": // ECUADOR
                case "SV": //EL SALVADOR
                case "GT": // GUATEMALA
                case "HN": //HONDURAS
                case "MX": //MEXICO
                case "NI": //NICARAGUA
                case "PA": //PANAMA
                case "PY": //PARAGUAY
                case "PE": //PERU
                case "PR": //PUERTO RICO
                case "UY": //URUGUAY
                case "VE": //VENEZUELA, BOLIVARIAN REPUBLIC OF
                    language = "es";
                    break;

                case "SE":  //SWEDEN
                    //case "FI": //FINLAND
                    language = "sv";
                    break;

                case "TH": //THAILAND
                    language = "th";
                    break;

                case "TR": //TURKEY
                    language = "tr";
                    break;

                case "UA": //UKRAINE
                    language = "uk";
                    break;

                case "PK": //PAKISTAN
                    language = "ur";
                    break;

                case "VN": //VIETNAM", Code = "VN" });
                    language = "vi";
                    break;

                default:
                    break;
            }
            return language;
        }

        public static double GetAvgUtcOffsetByCountryCode(string code)
        {
            double UTC_offset = 0;

            switch (code)
            {
                case "AS":
                case "NU":
                case "UM":
                    UTC_offset = -11;
                    break;

                case "CK":
                case "PF":
                    UTC_offset = -10;
                    break;

                case "PN":
                    UTC_offset = -8;
                    break;

                case "US":
                    UTC_offset = -7.5;
                    break;

                case "MX":
                    UTC_offset = -6.5;
                    break;

                case "BZ":
                case "CA"://-04:00  to -08:00
                case "CR":
                case "GT":
                case "HN":
                case "HI":
                case "SV":
                    UTC_offset = -6;
                    break;


                case "EC":
                    UTC_offset = -5.5; //-05:00  to -06:00
                    break;

                case "BS":
                case "CL"://-04:00  to -06:00
                case "CO":
                case "CU":
                case "HT":
                case "JM":
                case "KY":
                case "PA":
                case "PE":
                    UTC_offset = -5;
                    break;

                case "MS":
                    UTC_offset = -4.5;
                    break;

                case "AG":
                case "AI":
                case "AW":
                case "BB":
                case "BL":
                case "BM":
                case "BO":
                case "BQ":
                case "CW":
                case "DM":
                case "DO":
                case "GD":
                case "GP":
                case "GY":
                case "KN":
                case "LC":
                case "MF":
                case "MQ":
                case "PR":
                case "PY":
                case "SX":
                case "TC":
                case "TT":
                case "VC":
                case "VE":
                case "VG":
                case "VI":
                    UTC_offset = -4;
                    break;

                case "BR":
                    UTC_offset = -3.5; //-02:00  to -05:00
                    break;

                case "AR":
                case "FK":
                case "GF":
                case "PM":
                case "SR":
                case "UY":
                    UTC_offset = -3;
                    break;

                case "GL":
                case "GS":
                    UTC_offset = -2; //+00:00   to -04:00
                    break;

                case "CV":
                    UTC_offset = -1;
                    break;

                case "PT":
                    UTC_offset = -0.5;
                    break;


                case "BF":
                case "CI":
                case "EH":
                case "FO":
                case "GB":
                case "GG":
                case "GH":
                case "GM":
                case "GN":
                case "GW":
                case "IE":
                case "IM":
                case "IS":
                case "JE":
                case "LR":
                case "MA":
                case "ML":
                case "MR":
                case "SH":
                case "SL":
                case "SN":
                case "ST":
                case "TG":
                    UTC_offset = 0;
                    break;

                case "ES":
                    UTC_offset = 0.5;
                    break;

                case "AD":
                case "AL":
                case "AO":
                case "AT":
                case "BA":
                case "BE":
                case "BJ":
                case "CF":
                case "CG":
                case "CH":
                case "CM":
                case "CZ":
                case "DE":
                case "DK":
                case "DZ":
                case "FR":
                case "GA":
                case "GI":
                case "GQ":
                case "HR":
                case "HU":
                case "IT":
                case "LI":
                case "LU":
                case "MC":
                case "ME":
                case "MK":
                case "MT":
                case "NA":
                case "NE":
                case "NG":
                case "NL":
                case "NO":
                case "PL":
                case "RS":
                case "SE":
                case "SI":
                case "SJ":
                case "SK":
                case "SM":
                case "TD":
                case "TN":
                case "VA":
                    UTC_offset = 1;
                    break;

                case "CD":
                    UTC_offset = 1.5;
                    break;

                case "AX":
                case "BG":
                case "BI":
                case "BW":
                case "CY":
                case "EE":
                case "EG":
                case "FI":
                case "GR":
                case "IL":
                case "JO":
                case "LB":
                case "LS":
                case "LT":
                case "LV":
                case "LY":
                case "MD":
                case "MW":
                case "MZ":
                case "PS":
                case "RO":
                case "RW":
                case "SY":
                case "SZ":
                case "TR":
                case "UA":
                case "ZA":
                case "ZM":
                case "ZV":
                    UTC_offset = 2;
                    break;

                case "BY":
                case "DJ":
                case "ER":
                case "ET":
                case "IQ":
                case "KE":
                case "KM":
                case "KW":
                case "MG":
                case "QA":
                case "SA":
                case "SD":
                case "SO":
                case "SS":
                case "TZ":
                case "UG":
                case "YE":
                case "YT":
                    UTC_offset = 3;
                    break;

                case "IR":
                    UTC_offset = 3.5;
                    break;

                case "AE":
                case "AM":
                case "AQ":  // from -04:00  to +12:00
                case "AZ":
                case "GE":
                case "MU":
                case "OM":
                case "RE":
                case "SC":
                    UTC_offset = 4;
                    break;

                case "AF":
                    UTC_offset = 4.5; //+04:30
                    break;

                case "MV":
                case "PK":
                case "TF":
                case "TJ":
                case "TM":
                case "UZ":
                    UTC_offset = 5;
                    break;

                case "IN":
                case "KZ":
                case "LK":
                case "NP":  // +5.45
                    UTC_offset = 5.5;
                    break;

                case "BD":
                case "BT":
                case "IO":
                case "KG":
                    UTC_offset = 6;
                    break;

                case "CC":
                case "MM":
                    UTC_offset = 6.5;
                    break;

                case "CN":
                case "CX":
                case "KH":
                case "LA":
                case "RU":   //+02:00 to +12:00
                case "TH":
                case "VN":
                    UTC_offset = 7;  //+06:00 to +08:00
                    break;

                case "MN":
                    UTC_offset = 7.5;
                    break;

                case "BN":
                case "HK":
                case "ID"://+07:00 to +09:00
                case "MO":
                case "MY":
                case "PH":
                case "SG":
                case "TW":
                    UTC_offset = 8;
                    break;

                case "KP":
                    UTC_offset = 8.5;
                    break;

                case "JP":
                case "KR":
                case "PW":
                case "TL":
                    UTC_offset = 9;
                    break;

                case "AU":
                    UTC_offset = 9.5;  //+08:00 to +11:00
                    break;

                case "GU":
                case "MP":
                    UTC_offset = 10;
                    break;

                case "FM":
                case "PG":
                    UTC_offset = 10.5;  //+10:00 to +11:00
                    break;


                case "NC":
                case "NF":
                case "SB":
                    UTC_offset = 11;
                    break;

                case "FJ":
                case "MH":
                case "NR":
                case "NZ":
                case "TV":
                case "VU":
                case "WF":
                    UTC_offset = 12;
                    break;

                case "KI"://+12:00 to +14:00
                case "TK":
                case "TO":
                case "WS":
                    UTC_offset = 13;
                    break;

                default:
                    break;
            }
            return UTC_offset;
        }


        public static List<Country> FillList()
        {
            //fill countries
            _list = new List<Country>();
            _list.Add(new Country() { Name = "EUROPE", Code = "EUROPE" });
            _list.Add(new Country() { Name = "WORLDWIDE", Code = "WORLDWIDE" });
            _list.Add(new Country() { Name = "UNITED STATES", Code = "US" });
            _list.Add(new Country() { Name = "AFGHANISTAN", Code = "AF" });
            _list.Add(new Country() { Name = "ALAND ISLANDS", Code = "AX" });
            _list.Add(new Country() { Name = "ALBANIA", Code = "AL" });
            _list.Add(new Country() { Name = "ALGERIA", Code = "DZ" });
            _list.Add(new Country() { Name = "AMERICAN SAMOA", Code = "AS" });
            _list.Add(new Country() { Name = "ANDORRA", Code = "AD" });
            _list.Add(new Country() { Name = "ANGOLA", Code = "AO" });
            _list.Add(new Country() { Name = "ANGUILLA", Code = "AI" });
            _list.Add(new Country() { Name = "ANTARCTICA", Code = "AQ" });
            _list.Add(new Country() { Name = "ANTIGUA AND BARBUDA", Code = "AG" });
            _list.Add(new Country() { Name = "ARGENTINA", Code = "AR" });
            _list.Add(new Country() { Name = "ARMENIA", Code = "AM" });
            _list.Add(new Country() { Name = "ARUBA", Code = "AW" });
            _list.Add(new Country() { Name = "AUSTRALIA", Code = "AU" });
            _list.Add(new Country() { Name = "AUSTRIA", Code = "AT" });
            _list.Add(new Country() { Name = "AZERBAIJAN", Code = "AZ" });
            _list.Add(new Country() { Name = "BAHAMAS", Code = "BS" });
            _list.Add(new Country() { Name = "BAHRAIN", Code = "BH" });
            _list.Add(new Country() { Name = "BANGLADESH", Code = "BD" });
            _list.Add(new Country() { Name = "BARBADOS", Code = "BB" });
            _list.Add(new Country() { Name = "BELARUS", Code = "BY" });
            _list.Add(new Country() { Name = "BELGIUM", Code = "BE" });
            _list.Add(new Country() { Name = "BELIZE", Code = "BZ" });
            _list.Add(new Country() { Name = "BENIN", Code = "BJ" });
            _list.Add(new Country() { Name = "BERMUDA", Code = "BM" });
            _list.Add(new Country() { Name = "BHUTAN", Code = "BT" });
            _list.Add(new Country() { Name = "BOLIVIA, PLURINATIONAL STATE OF", Code = "BO" });
            _list.Add(new Country() { Name = "BONAIRE, SAINT EUSTATIUS AND SABA", Code = "BQ" });
            _list.Add(new Country() { Name = "BOSNIA AND HERZEGOVINA", Code = "BA" });
            _list.Add(new Country() { Name = "BOTSWANA", Code = "BW" });
            _list.Add(new Country() { Name = "BOUVET ISLAND", Code = "BV" });
            _list.Add(new Country() { Name = "BRAZIL", Code = "BR" });
            _list.Add(new Country() { Name = "BRITISH INDIAN OCEAN TERRITORY", Code = "IO" });
            _list.Add(new Country() { Name = "BRUNEI DARUSSALAM", Code = "BN" });
            _list.Add(new Country() { Name = "BULGARIA", Code = "BG" });
            _list.Add(new Country() { Name = "BURKINA FASO", Code = "BF" });
            _list.Add(new Country() { Name = "BURUNDI", Code = "BI" });
            _list.Add(new Country() { Name = "CAMBODIA", Code = "KH" });
            _list.Add(new Country() { Name = "CAMEROON", Code = "CM" });
            _list.Add(new Country() { Name = "CANADA", Code = "CA" });
            _list.Add(new Country() { Name = "CAPE VERDE", Code = "CV" });
            _list.Add(new Country() { Name = "CAYMAN ISLANDS", Code = "KY" });
            _list.Add(new Country() { Name = "CENTRAL AFRICAN REPUBLIC", Code = "CF" });
            _list.Add(new Country() { Name = "CHAD", Code = "TD" });
            _list.Add(new Country() { Name = "CHILE", Code = "CL" });
            _list.Add(new Country() { Name = "CHINA", Code = "CN" });
            _list.Add(new Country() { Name = "CHRISTMAS ISLAND", Code = "CX" });
            _list.Add(new Country() { Name = "COCOS (KEELING) ISLANDS", Code = "CC" });
            _list.Add(new Country() { Name = "COLOMBIA", Code = "CO" });
            _list.Add(new Country() { Name = "COMOROS", Code = "KM" });
            _list.Add(new Country() { Name = "CONGO", Code = "CG" });
            _list.Add(new Country() { Name = "CONGO, THE DEMOCRATIC REPUBLIC OF THE", Code = "CD" });
            _list.Add(new Country() { Name = "COOK ISLANDS", Code = "CK" });
            _list.Add(new Country() { Name = "COSTA RICA", Code = "CR" });
            _list.Add(new Country() { Name = "COTE D'IVOIRE", Code = "CI" });
            _list.Add(new Country() { Name = "CROATIA", Code = "HR" });
            _list.Add(new Country() { Name = "CUBA", Code = "CU" });
            _list.Add(new Country() { Name = "CURACAO", Code = "CW" });
            _list.Add(new Country() { Name = "CYPRUS", Code = "CY" });
            _list.Add(new Country() { Name = "CZECH REPUBLIC", Code = "CZ" });
            _list.Add(new Country() { Name = "DENMARK", Code = "DK" });
            _list.Add(new Country() { Name = "DJIBOUTI", Code = "DJ" });
            _list.Add(new Country() { Name = "DOMINICA", Code = "DM" });
            _list.Add(new Country() { Name = "DOMINICAN REPUBLIC", Code = "DO" });
            _list.Add(new Country() { Name = "ECUADOR", Code = "EC" });
            _list.Add(new Country() { Name = "EGYPT", Code = "EG" });
            _list.Add(new Country() { Name = "EL SALVADOR", Code = "SV" });
            _list.Add(new Country() { Name = "EQUATORIAL GUINEA", Code = "GQ" });
            _list.Add(new Country() { Name = "ERITREA", Code = "ER" });
            _list.Add(new Country() { Name = "ESTONIA", Code = "EE" });
            _list.Add(new Country() { Name = "ETHIOPIA", Code = "ET" });
            _list.Add(new Country() { Name = "FALKLAND ISLANDS (MALVINAS)", Code = "FK" });
            _list.Add(new Country() { Name = "FAROE ISLANDS", Code = "FO" });
            _list.Add(new Country() { Name = "FIJI", Code = "FJ" });
            _list.Add(new Country() { Name = "FINLAND", Code = "FI" });
            _list.Add(new Country() { Name = "FRANCE", Code = "FR" });
            _list.Add(new Country() { Name = "FRENCH GUIANA", Code = "GF" });
            _list.Add(new Country() { Name = "FRENCH POLYNESIA", Code = "PF" });
            _list.Add(new Country() { Name = "FRENCH SOUTHERN TERRITORIES", Code = "TF" });
            _list.Add(new Country() { Name = "GABON", Code = "GA" });
            _list.Add(new Country() { Name = "GAMBIA", Code = "GM" });
            _list.Add(new Country() { Name = "GEORGIA", Code = "GE" });
            _list.Add(new Country() { Name = "GERMANY", Code = "DE" });
            _list.Add(new Country() { Name = "GHANA", Code = "GH" });
            _list.Add(new Country() { Name = "GIBRALTAR", Code = "GI" });
            _list.Add(new Country() { Name = "GREECE", Code = "GR" });
            _list.Add(new Country() { Name = "GREENLAND", Code = "GL" });
            _list.Add(new Country() { Name = "GRENADA", Code = "GD" });
            _list.Add(new Country() { Name = "GUADELOUPE", Code = "GP" });
            _list.Add(new Country() { Name = "GUAM", Code = "GU" });
            _list.Add(new Country() { Name = "GUATEMALA", Code = "GT" });
            _list.Add(new Country() { Name = "GUERNSEY", Code = "GG" });
            _list.Add(new Country() { Name = "GUINEA", Code = "GN" });
            _list.Add(new Country() { Name = "GUINEA-BISSAU", Code = "GW" });
            _list.Add(new Country() { Name = "GUYANA", Code = "GY" });
            _list.Add(new Country() { Name = "HAITI", Code = "HT" });
            _list.Add(new Country() { Name = "HEARD ISLAND AND MCDONALD ISLANDS", Code = "HM" });
            //_list.Add(new Country() { Name = "HOLY SEE (VATICAN CITY STATE)", Code = "VA" });
            _list.Add(new Country() { Name = "HONDURAS", Code = "HN" });
            _list.Add(new Country() { Name = "HONG KONG", Code = "HK" });
            _list.Add(new Country() { Name = "HUNGARY", Code = "HU" });
            _list.Add(new Country() { Name = "ICELAND", Code = "IS" });
            _list.Add(new Country() { Name = "INDIA", Code = "IN" });
            _list.Add(new Country() { Name = "INDONESIA", Code = "ID" });
            _list.Add(new Country() { Name = "IRAN, ISLAMIC REPUBLIC OF", Code = "IR" });
            _list.Add(new Country() { Name = "IRAQ", Code = "IQ" });
            _list.Add(new Country() { Name = "IRELAND", Code = "IE" });
            _list.Add(new Country() { Name = "ISLE OF MAN", Code = "IM" });
            _list.Add(new Country() { Name = "ISRAEL", Code = "IL" });
            _list.Add(new Country() { Name = "ITALY", Code = "IT" });
            _list.Add(new Country() { Name = "JAMAICA", Code = "JM" });
            _list.Add(new Country() { Name = "JAPAN", Code = "JP" });
            _list.Add(new Country() { Name = "JERSEY", Code = "JE" });
            _list.Add(new Country() { Name = "JORDAN", Code = "JO" });
            _list.Add(new Country() { Name = "KAZAKHSTAN", Code = "KZ" });
            _list.Add(new Country() { Name = "KENYA", Code = "KE" });
            _list.Add(new Country() { Name = "KIRIBATI", Code = "KI" });
            _list.Add(new Country() { Name = "KOREA, DEMOCRATIC PEOPLE'S REPUBLIC OF", Code = "KP" });
            _list.Add(new Country() { Name = "KOREA, REPUBLIC OF", Code = "KR" });
            _list.Add(new Country() { Name = "KUWAIT", Code = "KW" });
            _list.Add(new Country() { Name = "KYRGYZSTAN", Code = "KG" });
            _list.Add(new Country() { Name = "LAO PEOPLE'S DEMOCRATIC REPUBLIC", Code = "LA" });
            _list.Add(new Country() { Name = "LATVIA", Code = "LV" });
            _list.Add(new Country() { Name = "LEBANON", Code = "LB" });
            _list.Add(new Country() { Name = "LESOTHO", Code = "LS" });
            _list.Add(new Country() { Name = "LIBERIA", Code = "LR" });
            _list.Add(new Country() { Name = "LIBYAN ARAB JAMAHIRIYA", Code = "LY" });
            _list.Add(new Country() { Name = "LIECHTENSTEIN", Code = "LI" });
            _list.Add(new Country() { Name = "LITHUANIA", Code = "LT" });
            _list.Add(new Country() { Name = "LUXEMBOURG", Code = "LU" });
            _list.Add(new Country() { Name = "MACAO", Code = "MO" });
            _list.Add(new Country() { Name = "MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF", Code = "MK" });
            _list.Add(new Country() { Name = "MADAGASCAR", Code = "MG" });
            _list.Add(new Country() { Name = "MALAWI", Code = "MW" });
            _list.Add(new Country() { Name = "MALAYSIA", Code = "MY" });
            _list.Add(new Country() { Name = "MALDIVES", Code = "MV" });
            _list.Add(new Country() { Name = "MALI", Code = "ML" });
            _list.Add(new Country() { Name = "MALTA", Code = "MT" });
            _list.Add(new Country() { Name = "MARSHALL ISLANDS", Code = "MH" });
            _list.Add(new Country() { Name = "MARTINIQUE", Code = "MQ" });
            _list.Add(new Country() { Name = "MAURITANIA", Code = "MR" });
            _list.Add(new Country() { Name = "MAURITIUS", Code = "MU" });
            _list.Add(new Country() { Name = "MAYOTTE", Code = "YT" });
            _list.Add(new Country() { Name = "MEXICO", Code = "MX" });
            _list.Add(new Country() { Name = "MICRONESIA, FEDERATED STATES OF", Code = "FM" });
            _list.Add(new Country() { Name = "MOLDOVA, REPUBLIC OF", Code = "MD" });
            _list.Add(new Country() { Name = "MONACO", Code = "MC" });
            _list.Add(new Country() { Name = "MONGOLIA", Code = "MN" });
            _list.Add(new Country() { Name = "MONTENEGRO", Code = "ME" });
            _list.Add(new Country() { Name = "MONTSERRAT", Code = "MS" });
            _list.Add(new Country() { Name = "MOROCCO", Code = "MA" });
            _list.Add(new Country() { Name = "MOZAMBIQUE", Code = "MZ" });
            _list.Add(new Country() { Name = "MYANMAR", Code = "MM" });
            _list.Add(new Country() { Name = "NAMIBIA", Code = "NA" });
            _list.Add(new Country() { Name = "NAURU", Code = "NR" });
            _list.Add(new Country() { Name = "NEPAL", Code = "NP" });
            _list.Add(new Country() { Name = "NETHERLANDS", Code = "NL" });
            _list.Add(new Country() { Name = "NEW CALEDONIA", Code = "NC" });
            _list.Add(new Country() { Name = "NEW ZEALAND", Code = "NZ" });
            _list.Add(new Country() { Name = "NICARAGUA", Code = "NI" });
            _list.Add(new Country() { Name = "NIGER", Code = "NE" });
            _list.Add(new Country() { Name = "NIGERIA", Code = "NG" });
            _list.Add(new Country() { Name = "NIUE", Code = "NU" });
            _list.Add(new Country() { Name = "NORFOLK ISLAND", Code = "NF" });
            _list.Add(new Country() { Name = "NORTHERN MARIANA ISLANDS", Code = "MP" });
            _list.Add(new Country() { Name = "NORWAY", Code = "NO" });
            _list.Add(new Country() { Name = "OMAN", Code = "OM" });
            _list.Add(new Country() { Name = "PAKISTAN", Code = "PK" });
            _list.Add(new Country() { Name = "PALAU", Code = "PW" });
            _list.Add(new Country() { Name = "PALESTINIAN TERRITORY, OCCUPIED", Code = "PS" });
            _list.Add(new Country() { Name = "PANAMA", Code = "PA" });
            _list.Add(new Country() { Name = "PAPUA NEW GUINEA", Code = "PG" });
            _list.Add(new Country() { Name = "PARAGUAY", Code = "PY" });
            _list.Add(new Country() { Name = "PERU", Code = "PE" });
            _list.Add(new Country() { Name = "PHILIPPINES", Code = "PH" });
            _list.Add(new Country() { Name = "PITCAIRN", Code = "PN" });
            _list.Add(new Country() { Name = "POLAND", Code = "PL" });
            _list.Add(new Country() { Name = "PORTUGAL", Code = "PT" });
            _list.Add(new Country() { Name = "PUERTO RICO", Code = "PR" });
            _list.Add(new Country() { Name = "QATAR", Code = "QA" });
            _list.Add(new Country() { Name = "REUNION", Code = "RE" });
            _list.Add(new Country() { Name = "ROMANIA", Code = "RO" });
            _list.Add(new Country() { Name = "RUSSIAN FEDERATION", Code = "RU" });
            _list.Add(new Country() { Name = "RWANDA", Code = "RW" });
            _list.Add(new Country() { Name = "SAINT BARTHELEMY", Code = "BL" });
            _list.Add(new Country() { Name = "SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA", Code = "SH" });
            _list.Add(new Country() { Name = "SAINT KITTS AND NEVIS", Code = "KN" });
            _list.Add(new Country() { Name = "SAINT LUCIA", Code = "LC" });
            _list.Add(new Country() { Name = "SAINT MARTIN (FRENCH PART)", Code = "MF" });
            _list.Add(new Country() { Name = "SAINT PIERRE AND MIQUELON", Code = "PM" });
            _list.Add(new Country() { Name = "SAINT VINCENT AND THE GRENADINES", Code = "VC" });
            _list.Add(new Country() { Name = "SAMOA", Code = "WS" });
            _list.Add(new Country() { Name = "SAN MARINO", Code = "SM" });
            _list.Add(new Country() { Name = "SAO TOME AND PRINCIPE", Code = "ST" });
            _list.Add(new Country() { Name = "SAUDI ARABIA", Code = "SA" });
            _list.Add(new Country() { Name = "SENEGAL", Code = "SN" });
            _list.Add(new Country() { Name = "SERBIA", Code = "RS" });
            _list.Add(new Country() { Name = "SEYCHELLES", Code = "SC" });
            _list.Add(new Country() { Name = "SIERRA LEONE", Code = "SL" });
            _list.Add(new Country() { Name = "SINGAPORE", Code = "SG" });
            _list.Add(new Country() { Name = "SINT MAARTEN (DUTCH PART)", Code = "SX" });
            _list.Add(new Country() { Name = "SLOVAKIA", Code = "SK" });
            _list.Add(new Country() { Name = "SLOVENIA", Code = "SI" });
            _list.Add(new Country() { Name = "SOLOMON ISLANDS", Code = "SB" });
            _list.Add(new Country() { Name = "SOMALIA", Code = "SO" });
            _list.Add(new Country() { Name = "SOUTH AFRICA", Code = "ZA" });
            _list.Add(new Country() { Name = "SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS", Code = "GS" });
            _list.Add(new Country() { Name = "SPAIN", Code = "ES" });
            _list.Add(new Country() { Name = "SRI LANKA", Code = "LK" });
            _list.Add(new Country() { Name = "SUDAN", Code = "SD" });
            _list.Add(new Country() { Name = "SURINAME", Code = "SR" });
            _list.Add(new Country() { Name = "SVALBARD AND JAN MAYEN", Code = "SJ" });
            _list.Add(new Country() { Name = "SWAZILAND", Code = "SZ" });
            _list.Add(new Country() { Name = "SWEDEN", Code = "SE" });
            _list.Add(new Country() { Name = "SWITZERLAND", Code = "CH" });
            _list.Add(new Country() { Name = "SYRIAN ARAB REPUBLIC", Code = "SY" });
            _list.Add(new Country() { Name = "TAIWAN, PROVINCE OF CHINA", Code = "TW" });
            _list.Add(new Country() { Name = "TAJIKISTAN", Code = "TJ" });
            _list.Add(new Country() { Name = "TANZANIA, UNITED REPUBLIC OF", Code = "TZ" });
            _list.Add(new Country() { Name = "THAILAND", Code = "TH" });
            _list.Add(new Country() { Name = "TIMOR-LESTE", Code = "TL" });
            _list.Add(new Country() { Name = "TOGO", Code = "TG" });
            _list.Add(new Country() { Name = "TOKELAU", Code = "TK" });
            _list.Add(new Country() { Name = "TONGA", Code = "TO" });
            _list.Add(new Country() { Name = "TRINIDAD AND TOBAGO", Code = "TT" });
            _list.Add(new Country() { Name = "TUNISIA", Code = "TN" });
            _list.Add(new Country() { Name = "TURKEY", Code = "TR" });
            _list.Add(new Country() { Name = "TURKMENISTAN", Code = "TM" });
            _list.Add(new Country() { Name = "TURKS AND CAICOS ISLANDS", Code = "TC" });
            _list.Add(new Country() { Name = "TUVALU", Code = "TV" });
            _list.Add(new Country() { Name = "UGANDA", Code = "UG" });
            _list.Add(new Country() { Name = "UKRAINE", Code = "UA" });
            _list.Add(new Country() { Name = "UNITED ARAB EMIRATES", Code = "AE" });
            _list.Add(new Country() { Name = "UNITED KINGDOM", Code = "GB" });

            //_list.Add(new Country() { Name = "UNITED STATES MINOR OUTLYING ISLANDS", Code = "UM" });
            _list.Add(new Country() { Name = "URUGUAY", Code = "UY" });
            _list.Add(new Country() { Name = "UZBEKISTAN", Code = "UZ" });
            _list.Add(new Country() { Name = "VANUATU", Code = "VU" });
            //_list.Add(new Country() { Name = "VATICAN CITY STATE", Code = "see HOLY SEE" });
            _list.Add(new Country() { Name = "VENEZUELA, BOLIVARIAN REPUBLIC OF", Code = "VE" });
            _list.Add(new Country() { Name = "VIETNAM", Code = "VN" });
            _list.Add(new Country() { Name = "VIRGIN ISLANDS, BRITISH", Code = "VG" });
            _list.Add(new Country() { Name = "VIRGIN ISLANDS, U.S.", Code = "VI" });
            _list.Add(new Country() { Name = "WALLIS AND FUTUNA", Code = "WF" });
            _list.Add(new Country() { Name = "WESTERN SAHARA", Code = "EH" });
            _list.Add(new Country() { Name = "YEMEN", Code = "YE" });
            _list.Add(new Country() { Name = "ZAMBIA", Code = "ZM" });
            _list.Add(new Country() { Name = "ZIMBABWE", Code = "ZW" });

            return _list;
        }


        public static List<string> ListOfIsraelCities()
        {
            List<string> cities = new List<string>();
            cities.Add("אשדוד");
            cities.Add("אשקלון");
            cities.Add("באר שבע");
            cities.Add("דימונה");
            cities.Add("אילת");
            cities.Add("סדרות");
            cities.Add("חדרה");
            cities.Add("חיפה");
            cities.Add("טירת כרמל ");
            cities.Add("אום אל-פחם");
            cities.Add("בנימינה");
            cities.Add("קרית אתא");
            cities.Add("פרדס חנה - כרכור");
            cities.Add("בית שמש");
            cities.Add("ירושלים");
            cities.Add("בית דגן");
            cities.Add("הוד השרון");
            cities.Add("כפר סבא");
            cities.Add("לוד");
            cities.Add("מכמורת");
            cities.Add("נס ציונה");
            cities.Add("נתניה");
            cities.Add("נורדיה");
            cities.Add("פתח תקווה");
            cities.Add("רעננה");
            cities.Add("רמלה");
            cities.Add("רחובות");
            cities.Add("ראשון לציון");
            cities.Add("ראש העין");
            cities.Add("יבנה");
            cities.Add("יהוד");
            cities.Add("בת ים");
            cities.Add("חולון");
            cities.Add("באר יעקב");
            cities.Add("מודיעין");
            cities.Add("קרית אונו");
            cities.Add("בני ברק");
            cities.Add("גבעתיים");
            cities.Add("הרצליה");
            cities.Add("אור יהודה");
            cities.Add("רמת גן");
            cities.Add("רמת השרון");
            cities.Add("תל אביב יפו");
            cities.Add("תל אביב");
            cities.Add("עכו");
            cities.Add("אפולה");
            cities.Add("חצור הגלילית");
            cities.Add("כרמיאל");
            cities.Add("מגדל");
            cities.Add("מגדל העמק");
            cities.Add("נהריה");
            cities.Add("נצרת");
            cities.Add("נוף הגליל");
            cities.Add("קרית שמונה");
            cities.Add("טבריה");
            cities.Add("יוקנעם עילית");
            cities.Add("צפת");
            cities.Add("בית שאן");
            cities.Add("כברי");
            return cities;
        }

        public static List<string> GetListOfUsStates()
        {
            List<string> states = new List<string>
            {
                "AL",
                "AK",
                "AZ",
                "AR",
                "CA",
                "CO",
                "CT",
                "DE",
                "FL",
                "GA",
                "HI",
                "ID",
                "IL",
                "IN",
                "IA",
                "KS",
                "KY",
                "LA",
                "ME",
                "MD",
                "MA",
                "MI",
                "MN",
                "MS",
                "MO",
                "MT",
                "NE",
                "NV",
                "NH",
                "NJ",
                "NM",
                "NY",
                "NC",
                "ND",
                "OH",
                "OK",
                "OR",
                "PA",
                "RI",
                "SC",
                "SD",
                "TN",
                "TX",
                "UT",
                "VT",
                "VA",
                "WA",
                "WV",
                "WI",
                "WY"
            };
            return states;

        }
        public static string GetMostImportantCountyFromTheList(List<string> countries)
        {
            if (countries.Contains("US"))
            {
                return "US";
            }
            if (countries.Contains("GB"))
            {
                return "GB";
            }
            if (countries.Contains("AU"))
            {
                return "AU";
            }
            return countries[0];
        }

        public static string TryToIdentifyCountryCode(string country)
        {
            CountriesIdentifyDictionsry countriesIdentifyDictionsry = new CountriesIdentifyDictionsry();
            string res = null;
            if (countriesIdentifyDictionsry.CountryDictionary.Where(x => x.Key == country).Any())
            {
                res = countriesIdentifyDictionsry.CountryDictionary[country];
            }
            else if (country.ToLower().Contains("worldwide"))
            {
                res = countriesIdentifyDictionsry.CountryDictionary["WORLDWIDE"];
            }
            else if (country.ToLower().Contains("urope"))
            {
                res = countriesIdentifyDictionsry.CountryDictionary["EUROPE"];
            }
            else if (country.ToLower().Contains("is not ") || country.ToLower().Contains(" no info") || country.ToLower().Contains("i'm sorry"))
            {
                return null;
            }
            //else if (country.Length > 70)
            //{
            //    return null;
            //}
            else
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "Can't identify county while getting AIShopInfo", $"Please add to the dictionary: country: {country}");
            }
            return res;
        }
    }
    internal class CountriesIdentifyDictionsry
    {
        public Dictionary<string, string> CountryDictionary { get; set; }
        public CountriesIdentifyDictionsry()
        {
            CountryDictionary = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                { "USA", "US" },
                {"CON-US","US" },
                {"UNITED STATES","US" },
                {"UNITED STATES OF AMERICA","US"},
                { "AMERICA", "US" },
                { "UNITED KINGDOM", "GB" },
                { "UK", "GB" },
                { "ENGLAND", "GB" }
            };
        }
    }

}
