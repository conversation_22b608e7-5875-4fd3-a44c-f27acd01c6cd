﻿using Newtonsoft.Json;
using Storeya.Core.Models;
using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Web;

namespace Storeya.Core.Helpers
{
    public class CurrencyHelper
    {
        static List<Currency> _list;
        static CurrencyHelper()
        {
            FillList();
        }


        public static decimal CalculateConvertedValue(List<ConvertedInUSDCurrencyInfo> convertedInUSDCurrencyInfo, string currency, decimal valueToConvert, int shopID, string toCurrency = "USD")
        {
            decimal calculatedValue = valueToConvert;
            if (convertedInUSDCurrencyInfo != null && convertedInUSDCurrencyInfo.Where(x => x.Currency == currency).Any())
            {
                ConvertedInUSDCurrencyInfo cur_info = convertedInUSDCurrencyInfo.Where(x => x.Currency == currency).FirstOrDefault();
                calculatedValue = cur_info.ConvertedValueInUSD.ToDecimal() * valueToConvert;
            }
            else
            {
                calculatedValue = GetConvertedValueInUSD(shopID, currency, valueToConvert, toCurrency);
            }
            return calculatedValue;
        }

        public static decimal GetConvertedValueInUSD(int shopID, string currency, decimal valueToConvert, string toCurrency = "USD")
        {
            decimal convertedValue = valueToConvert;
            try
            {
                //BlueSnapApi api = new BlueSnapApi();
                //double convertedConversionsValue_in_usd = api.ConvertedToUSD(valueToConvert.ToDouble(), currency, shopID, toCurrency);
                double convertedConversionsValue_in_usd = ConvertUsingExternalApi(valueToConvert.ToDouble(), currency, shopID, toCurrency);
                convertedValue = convertedConversionsValue_in_usd.ToDecimal();
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed to get converted value in " + toCurrency + " for " + currency, ex, shopID);
            }
            return convertedValue;
        }

        public static double GetConvertedValueInUSD(string currency, decimal valueToConvert, int shopID = 0, string toCurrency = "USD")
        {
            double convertedValue_in_usd = 0;
            try
            {
                Console.WriteLine("Extract currency rate for " + currency);
                //BlueSnapApi api = new BlueSnapApi();
                //convertedValue_in_usd = api.ConvertedToUSD(valueToConvert.ToDouble(), currency, shopID, toCurrency);
                convertedValue_in_usd = ConvertUsingExternalApi(valueToConvert.ToDouble(), currency, shopID, toCurrency);
                
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed to get converted value in " + toCurrency + " for " + currency, ex, shopID);
            }
            return convertedValue_in_usd;
        }


        private static double GetConversioRateToUsd(string currency, string baseCurrency = "usd")
        {
            //source - https://github.com/fawazahmed0/exchange-api?tab=readme-ov-file

            string _endpoint = string.Format("https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/{0}.json", baseCurrency.ToLower());
            string _fallbackEndpoint = string.Format("https://latest.currency-api.pages.dev/v1/currencies/{0}.json", baseCurrency.ToLower()); ;

            if (string.IsNullOrEmpty(currency) || string.IsNullOrEmpty(baseCurrency))
            {
                throw new MissingFieldException("Both currencies should be provided");
            }

            currency = currency.ToLower();
            baseCurrency = baseCurrency.ToLower();

            try
            {
                if (currency == baseCurrency)
                {
                    return 1;
                }

                HttpClient client = new HttpClient();
                HttpResponseMessage response;
                try
                {
                    response = client.GetAsync(_endpoint).Result;
                }
                catch
                {
                    //use fallback url
                    response = client.GetAsync(_fallbackEndpoint).Result;
                }
                response.EnsureSuccessStatusCode();


                string jsonResponse = response.Content.ReadAsStringAsync().Result;
                dynamic exchangeRates = JsonConvert.DeserializeObject<dynamic>(jsonResponse);

                double conversionRate = GetConversionRate(exchangeRates, baseCurrency.ToLower(), currency);

                return conversionRate;
            }
            catch (Exception ex)
            {
                throw new Exception("Error fetching data from the API", ex);
            }

        }

        public static double ConvertUsingExternalApi(double amountInClientsCurrency, string shopperCurrency, int shopIDtoLog, string toCurrency = "usd")
        {
                double conversionRate = GetConversioRateToUsd(shopperCurrency, toCurrency); 
                double convertedAmount = amountInClientsCurrency / conversionRate;

                // LogShopID(shopIDtoLog);

                return convertedAmount;
        }
        private static double GetConversionRate(dynamic exchangeRates, string toCurrency, string fromCurrency)
        {
            try
            {
                // Access the desired currency rate dynamically from the "usd" object
                return (double)exchangeRates[toCurrency][fromCurrency];
            }
            catch (Exception)
            {
                throw new ArgumentException($"Currency {fromCurrency} not found.");
            }
        }

        public static List<ConvertedInUSDCurrencyInfo> GetConvertedInUSDCurrencyInfoForValue_1_UsingAPI(List<string> currencies)
        {
            List<ConvertedInUSDCurrencyInfo> convertedInUSDCurrencyInfo = null;
            if (currencies != null && currencies.Count > 0)
            {
                foreach (var currency in currencies)
                {
                    double convertedValue_in_usd = GetConvertedValueInUSD(currency, 1);

                    if (convertedValue_in_usd != 0)
                    {
                        ConvertedInUSDCurrencyInfo convertedInUSD_cur_info = new ConvertedInUSDCurrencyInfo();
                        convertedInUSD_cur_info.Currency = currency;
                        convertedInUSD_cur_info.ConvertedValueInUSD = convertedValue_in_usd;

                        if (convertedInUSDCurrencyInfo == null)
                            convertedInUSDCurrencyInfo = new List<ConvertedInUSDCurrencyInfo>();

                        convertedInUSDCurrencyInfo.Add(convertedInUSD_cur_info);
                    }
                }
            }

            return convertedInUSDCurrencyInfo;
        }

        public static bool GetChannelCurrencyRate(TrafficChannelsTypes channelType, int shopId, out decimal costCurrencyRate, out decimal revenueCurrencyRate, string fromCurrency = "USD")
        {
            costCurrencyRate = 1M;
            revenueCurrencyRate = 1M;
            if (CurrencyHelper.GetChannelCurrency(channelType, shopId, out string costCurrency, out string revenueCurrency))
            {
                costCurrencyRate = CalculateConvertedValue(null, costCurrency, 1M, shopId, fromCurrency);
                if (costCurrency == revenueCurrency)
                {
                    revenueCurrencyRate = costCurrencyRate;
                }
                else
                {
                    revenueCurrencyRate = CalculateConvertedValue(null, revenueCurrency, 1M, shopId, fromCurrency);
                }
                return true;
            }
            return false;
        }

        public static bool GetChannelCurrency(TrafficChannelsTypes channelType, int shopId, out string costCurrency, out string revenueCurrency)
        {
            costCurrency = null;
            revenueCurrency = null;
            try
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                TbAccountTrafficChannel tbAccountTrafficChannels = db.TbAccountTrafficChannels.Where(c => c.ShopID == shopId && c.ChannelType == (int)channelType).FirstOrDefault();
                if (tbAccountTrafficChannels == null)
                {
                    return false;
                }
                costCurrency = tbAccountTrafficChannels.CostCurrency;
                revenueCurrency = tbAccountTrafficChannels.RevenueCurrency;
                if (string.IsNullOrEmpty(costCurrency) && string.IsNullOrEmpty(revenueCurrency))
                {
                    return false;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        //public static decimal GetCurrencyRate(Shop shop)
        //{
        //    return GetCurrencyRate(shop.Currency, shop.ID);
        //}

        //public static decimal GetCurrencyRate(string currency, int shopId)
        //{
        //    if (string.IsNullOrEmpty(currency) || currency == "USD")
        //    {
        //        return 1;
        //    }

        //    List<string> currencies = new List<string>() { currency };
        //    List<ConvertedInUSDCurrencyInfo> currencyRates =
        //        TbBigSpendersManager.GetConvertedInUSDCurrencyInfoForValue_1(currencies);
        //    decimal currencyRate = CurrencyHelper.GetCurrencyRate(currencyRates, currency, shopId);
        //    return currencyRate;
        //}

        public static decimal GetCurrencyRate(List<ConvertedInUSDCurrencyInfo> currencyRates, Shop shop, string currency = null)
        {
            if (string.IsNullOrEmpty(currency))
            {
                currency = shop.Currency;
            }
            return GetCurrencyRate(currencyRates, currency, shop.ID);
        }

        public static decimal GetCurrencyRate(List<ConvertedInUSDCurrencyInfo> currencyRates, int shopId)
        {
            Shop shop = DataHelper.GetStoreYaEntities().Shops.SingleOrDefault(s => s.ID == shopId);
            return GetCurrencyRate(currencyRates, shop);
        }

        public static decimal GetCurrencyRate(List<ConvertedInUSDCurrencyInfo> currencyRates, string currency, int shopId)
        {
            if (string.IsNullOrEmpty(currency) || currency == "USD")
            {
                return 1;
            }
            decimal currencyRate = 1;
            if (currencyRates != null)
            {
                var currencyRateQuery = currencyRates.Where(r => r.Currency == currency);
                if (currencyRateQuery.Any())
                {
                    currencyRate = currencyRateQuery.Single().ConvertedValueInUSD.ToDecimal();
                }
                else
                {
                    currencyRate = CalculateConvertedValue(currencyRates, currency, 1M, shopId);
                }
            }
            else
            {
                currencyRate = CalculateConvertedValue(currencyRates, currency, 1M, shopId);
            }
            return currencyRate;
        }

        public static List<Currency> GetCurencies()
        {
            return _list;
        }

        public static decimal GetCurrencyRate(int shopId, string currency = null)
        {
            Shop shop = DataHelper.GetStoreYaEntities().Shops.SingleOrDefault(s => s.ID == shopId);
            if (shop == null)
            {
                throw new Exception("GetCurrencyRate failed no shop with Id " + shopId);
            }
            return GetCurrencyRate(shop, currency);
        }

        public static decimal GetCurrencyRate(Shop shop, string currency = null)
        {
            decimal currencyRate = 1;
            if (string.IsNullOrEmpty(currency))
            {
                currency = shop.Currency;
            }
            if (!string.IsNullOrEmpty(currency) && currency != "USD")
            {
                List<ConvertedInUSDCurrencyInfo> currencyRates = GetConvertedInUSDCurrencyInfoForValue_1_UsingAPI(new List<string>() { currency });
                currencyRate = GetCurrencyRate(currencyRates, shop, currency);
            }
            return currencyRate;
        }

        public static string GetSymbolByCode(string code)
        {
            if (string.IsNullOrEmpty(code))
            {
                return "$";
            }
            else if ((from c in _list where c.Code == code select c).Any())
            {
                var currency = (from c in _list where c.Code == code select c).First();
                if (!string.IsNullOrEmpty(currency.Symbol))
                {
                    return currency.Symbol;
                }
                return currency.Code;
            }
            else
            {
                return "$";
            }
        }

        public static void FillList()
        {
            //fill countries
            _list = new List<Currency>();

            _list.Add(new Currency() { FullName = "  Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;" });
            _list.Add(new Currency() { FullName = "  United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "United States", CountryOnOriginalLanguage = "United States" });
            //_list.Add(new Currency() { FullName = " Abkhazia, Abkhazian   apsar"});
            _list.Add(new Currency() { FullName = " Abkhazia, Russian ruble - RUB", Code = "RUB", Symbol = "р.", CountryOnEN = "Abkhazia", CountryOnOriginalLanguage = "Аҧсны́" });
            _list.Add(new Currency() { FullName = " Afghanistan, Afghan afghani - AFN", Code = "AFN", Symbol = "؋", CountryOnEN = "Afghanistan" });
            _list.Add(new Currency() { FullName = " Akrotiri and Dhekelia, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Akrotiri and Dhekelia" });
            _list.Add(new Currency() { FullName = " Albania, Albanian lek - ALL", Code = "ALL", Symbol = "L", CountryOnEN = "Albania" });
            //_list.Add(new Currency() { FullName = " Alderney, Alderney pound", Symbol = "£", HtmlCharRef = "&#163;"});
            _list.Add(new Currency() { FullName = " Alderney, British pound - GBP", Code = "GBP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Alderney" });
            _list.Add(new Currency() { FullName = " Alderney, Guernsey pound - GGP", Code = "GGP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Alderney" });
            _list.Add(new Currency() { FullName = " Algeria, Algerian dinar - DZD", Code = "DZD", Symbol = "د.ج", CountryOnEN = "Algeria" });
            _list.Add(new Currency() { FullName = " Andorra, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Andorra" });
            _list.Add(new Currency() { FullName = " Angola, Angolan kwanza - AOA", Code = "AOA", Symbol = "Kz", CountryOnEN = "Angola" });
            _list.Add(new Currency() { FullName = " Anguilla, East Caribbean dollar - XCD", Code = "XCD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Anguilla" });
            _list.Add(new Currency() { FullName = " Antigua and Barbuda, East Caribbean dollar", Code = "XCD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Antigua and Barbuda" });
            _list.Add(new Currency() { FullName = " Argentina, Argentine peso - ARS", Code = "ARS", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Argentina" });
            _list.Add(new Currency() { FullName = " Armenia, Armenian dram - AMD", Code = "AMD", CountryOnEN = "Armenia" });
            _list.Add(new Currency() { FullName = " Aruba, Aruban florin - AWG", Code = "AWG", Symbol = "ƒ", HtmlCharRef = "&#402;", CountryOnEN = "Aruba" });
            //_list.Add(new Currency() { FullName = " Ascension Island, Ascension pound", Symbol = "£", HtmlCharRef = "&#163;"});
            _list.Add(new Currency() { FullName = " Ascension Island, Saint Helena pound - SHP", Code = "SHP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Ascension Island" });
            _list.Add(new Currency() { FullName = " Australia, Australian dollar - AUD", Code = "AUD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Australia", CountryOnOriginalLanguage = "Australia" });
            _list.Add(new Currency() { FullName = " Austria, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Austria", CountryOnOriginalLanguage = "Österreich" });
            _list.Add(new Currency() { FullName = " Azerbaijan, Azerbaijani manat - AZN", Code = "AZN", CountryOnEN = "Azerbaijan", CountryOnOriginalLanguage = "Azərbaycan" });
            _list.Add(new Currency() { FullName = " Bahamas, The, Bahamian dollar - BSD", Code = "BSD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Bahamas" });
            _list.Add(new Currency() { FullName = " Bahrain, Bahraini dinar - BHD", Code = "BHD", Symbol = "BHD", CountryOnEN = "Bahrain" }); //".د.ب"
            _list.Add(new Currency() { FullName = " Bangladesh, Bangladeshi taka - BDT", Code = "BDT", Symbol = "৳", CountryOnEN = "Bangladesh" });
            _list.Add(new Currency() { FullName = " Barbados, Barbadian dollar - BBD", Code = "BBD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Barbados" });
            _list.Add(new Currency() { FullName = " Belarus, Belarusian ruble - BYR", Code = "BYR", Symbol = "Br", CountryOnEN = "Belarus", CountryOnOriginalLanguage = "Беларусь" });
            _list.Add(new Currency() { FullName = " Belgium, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Belgium" });
            _list.Add(new Currency() { FullName = " Belize, Belize dollar - BZD", Code = "BZD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Belize" });
            _list.Add(new Currency() { FullName = " Benin, West African CFA franc - XOF", Code = "XOF", Symbol = "Fr", CountryOnEN = "Benin" });
            _list.Add(new Currency() { FullName = " Bermuda, Bermudian dollar - BMD", Code = "BMD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Bermuda" });
            _list.Add(new Currency() { FullName = " Bhutan, Bhutanese ngultrum - BTN", Code = "BTN", Symbol = "Nu.", CountryOnEN = "Bhutan" });
            _list.Add(new Currency() { FullName = " Bhutan, Indian rupee - INR", Code = "INR", CountryOnEN = "Bhutan" });
            _list.Add(new Currency() { FullName = " Bolivia, Bolivian boliviano - BOB", Code = "BOB", Symbol = "Bs.", CountryOnEN = "Bolivia" });
            _list.Add(new Currency() { FullName = " Bonaire, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Bonaire" });
            _list.Add(new Currency() { FullName = " Bosnia and Herzegovina - BAM", Code = "BAM", Symbol = "KM", CountryOnEN = "Bosnia and Herzegovina" });
            _list.Add(new Currency() { FullName = " Botswana, Botswana pula - BWP", Code = "BWP", Symbol = "P", CountryOnEN = "Botswana" });
            _list.Add(new Currency() { FullName = " Brazil, Brazilian real - BRL", Code = "BRL", Symbol = "R$", HtmlCharRef = "R&#36;", CountryOnEN = "Brazil", CountryOnOriginalLanguage = "Brasil" });
            _list.Add(new Currency() { FullName = " British Indian Ocean Territory - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "British Indian Ocean Territory" });
            //_list.Add(new Currency() { FullName = " British Virgin Islands, British Virgin Islands dollar", Symbol = "$", HtmlCharRef = "&#36;"});
            _list.Add(new Currency() { FullName = " British Virgin Islands - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "British Virgin Islands" });
            _list.Add(new Currency() { FullName = " Brunei, Brunei dollar - BND", Code = "BND", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Brunei" });
            _list.Add(new Currency() { FullName = " Brunei, Singapore dollar - SGD", Code = "SGD", Symbol = "S$", HtmlCharRef = "S&#36;", CountryOnEN = "Brunei" });
            _list.Add(new Currency() { FullName = " Bulgaria, Bulgarian lev - BGN", Code = "BGN", Symbol = "лв.", CountryOnEN = "Bulgaria", CountryOnOriginalLanguage = "България" });
            _list.Add(new Currency() { FullName = " Burkina Faso, West African CFA franc - XOF", Code = "XOF", Symbol = "Fr", CountryOnEN = "Burkina Faso" });
            _list.Add(new Currency() { FullName = " Burma, Burmese kyat - MMK", Code = "MMK", Symbol = "Ks", CountryOnEN = "Burma" });
            _list.Add(new Currency() { FullName = " Burundi, Burundian franc - BIF", Code = "BIF", Symbol = "Fr", CountryOnEN = "Burundi" });
            _list.Add(new Currency() { FullName = " Cambodia, Cambodian riel - KHR", Code = "KHR", Symbol = "៛", CountryOnEN = "Cambodia" });
            _list.Add(new Currency() { FullName = " Cameroon, Central African CFA franc - XAF", Code = "XAF", Symbol = "Fr", CountryOnEN = "Cameroon" });
            _list.Add(new Currency() { FullName = " Canada, Canadian dollar - CAD", Code = "CAD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Canada" });
            _list.Add(new Currency() { FullName = " Cape Verde, Cape Verdean escudo - CVE", Code = "CVE", CountryOnEN = "Cape Verde" });
            _list.Add(new Currency() { FullName = " Cayman Islands, Cayman Islands dollar - KYD", Code = "KYD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Cayman Islands" });
            _list.Add(new Currency() { FullName = " Central African CFA franc - XAF", Code = "XAF", Symbol = "Fr" });
            _list.Add(new Currency() { FullName = " Chad, Central African CFA franc - XAF", Code = "XAF", Symbol = "Fr", CountryOnEN = "Chad" });
            _list.Add(new Currency() { FullName = " Chile, Chilean peso - CLP", Code = "CLP", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Chile" });
            _list.Add(new Currency() { FullName = " China, People's Republic of - CNY", Code = "CNY", Symbol = "¥", HtmlCharRef = "&#165;", CountryOnEN = "China" });
            _list.Add(new Currency() { FullName = " Cocos (Keeling) Islands - AUD", Code = "AUD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Cocos (Keeling) Islands" });
            _list.Add(new Currency() { FullName = " Colombia, Colombian peso - COP", Code = "COP", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Colombia" });
            _list.Add(new Currency() { FullName = " Comoros, Comorian franc - KMF", Code = "KMF", Symbol = "Fr", CountryOnEN = "Comoros" });
            _list.Add(new Currency() { FullName = " Congo, Democratic Republic of the - CDF", Code = "CDF", Symbol = "Fr", CountryOnEN = "Congo" });
            _list.Add(new Currency() { FullName = " Congo, Republic of the - XAF", Code = "XAF", Symbol = "Fr", CountryOnEN = "Congo" });
            _list.Add(new Currency() { FullName = " Cook Islands, New Zealand dollar - NZD", Code = "NZD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Cook Islands" });
            //_list.Add(new Currency() { FullName = " Cook Islands, Cook Islands dollar", Symbol = "$", HtmlCharRef = "&#36;"});
            _list.Add(new Currency() { FullName = " Costa Rica, Costa Rican colón - CRC", Code = "CRC", Symbol = "₡", CountryOnEN = "Costa Rica" });
            _list.Add(new Currency() { FullName = " Côte d'Ivoire, West African CFA franc - XOF", Code = "XOF", Symbol = "Fr", CountryOnEN = "Côte d'Ivoire" });
            _list.Add(new Currency() { FullName = " Croatia, Croatian kuna - HRK", Code = "HRK", Symbol = "kn", CountryOnEN = "Croatia" });
            _list.Add(new Currency() { FullName = " Cuba, Cuban convertible peso - CUC", Code = "CUC", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Cuba" });
            _list.Add(new Currency() { FullName = " Cuba, Cuban peso - CUP", Code = "CUP", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Cuba" });
            _list.Add(new Currency() { FullName = " Curaçao, Netherlands Antillean guilder - ANG", Code = "ANG", Symbol = "ƒ", HtmlCharRef = "&#402;", CountryOnEN = "Curaçao" });
            _list.Add(new Currency() { FullName = " Cyprus, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Cyprus" });
            _list.Add(new Currency() { FullName = " Czech Republic, Czech koruna - CZK", Code = "CZK", Symbol = "Kč", CountryOnEN = "Czech Republic" });
            _list.Add(new Currency() { FullName = " Denmark, Danish krone - DKK", Code = "DKK", Symbol = "kr ", CountryOnEN = "Denmark", CountryOnOriginalLanguage = "Danmark" });
            _list.Add(new Currency() { FullName = " Djibouti, Djiboutian franc - DJF", Code = "DJF", Symbol = "Fr", CountryOnEN = "Djibouti" });
            _list.Add(new Currency() { FullName = " Dominica, East Caribbean dollar - XCD", Code = "XCD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Dominica" });
            _list.Add(new Currency() { FullName = " Dominican Republic, Dominican peso - DOP", Code = "DOP", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Dominican Republic" });
            _list.Add(new Currency() { FullName = " East Timor, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "East Timor" });
            //_list.Add(new Currency() { FullName = " East Timor"  });
            _list.Add(new Currency() { FullName = " Ecuador, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Ecuador" });
            //_list.Add(new Currency() { FullName = " Ecuador" });
            _list.Add(new Currency() { FullName = " Egypt, Egyptian pound - EGP", Code = "EGP", CountryOnEN = "Egypt" });
            _list.Add(new Currency() { FullName = " El Salvador, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "El Salvador" });
            _list.Add(new Currency() { FullName = " Equatorial Guinea - XAF", Code = "XAF", Symbol = "Fr", CountryOnEN = "Equatorial Guinea" });
            _list.Add(new Currency() { FullName = " Eritrea, Eritrean nakfa - ERN", Code = "ERN", Symbol = "Nfk", CountryOnEN = "Eritrea" });
            _list.Add(new Currency() { FullName = " Estonia, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Estonia" });
            _list.Add(new Currency() { FullName = " Ethiopia, Ethiopian birr - ETB", Code = "ETB", Symbol = "Br", CountryOnEN = "Ethiopia" });
            _list.Add(new Currency() { FullName = " Falkland Islands, Falkland Islands pound - FKP", Code = "FKP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Falkland Islands" });
            _list.Add(new Currency() { FullName = " Faroe Islands, Danish krone - DKK", Code = "DKK", Symbol = "kr ", CountryOnEN = "Faroe Islands" });
            //_list.Add(new Currency() { FullName = " Faroe Islands, Faroese króna", Symbol = "kr" });
            _list.Add(new Currency() { FullName = " Fiji, Fijian dollar - FJD", Code = "FJD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Fiji" });
            _list.Add(new Currency() { FullName = " Finland, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Finland" });
            _list.Add(new Currency() { FullName = " France, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "France" });
            _list.Add(new Currency() { FullName = " French Polynesia, CFP franc - XPF", Code = "XPF", Symbol = "Fr", CountryOnEN = "French Polynesia" });
            _list.Add(new Currency() { FullName = " Gabon, Central African CFA franc - XAF", Code = "XAF", Symbol = "Fr", CountryOnEN = "Gabon" });
            _list.Add(new Currency() { FullName = " Gambia, The, Gambian dalasi - GMD", Code = "GMD", Symbol = "D", CountryOnEN = "Gambia" });
            _list.Add(new Currency() { FullName = " Georgia, Georgian lari - GEL", Code = "GEL", Symbol = "ლ", CountryOnEN = "Georgia" });
            _list.Add(new Currency() { FullName = " Germany, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Germany", CountryOnOriginalLanguage = "Deutschland" });
            _list.Add(new Currency() { FullName = " Ghana, Ghana cedi - GHS", Code = "GHS", Symbol = "₵", CountryOnEN = "Ghana" });
            _list.Add(new Currency() { FullName = " Gibraltar, Gibraltar pound - GIP", Code = "GIP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Gibraltar" });
            _list.Add(new Currency() { FullName = " Greece, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Greece" });
            _list.Add(new Currency() { FullName = " Grenada, East Caribbean dollar - XCD", Code = "XCD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Grenada" });
            _list.Add(new Currency() { FullName = " Guatemala, Guatemalan quetzal - GTQ", Code = "GTQ", Symbol = "Q", CountryOnEN = "Guatemala" });
            _list.Add(new Currency() { FullName = " Guernsey, British pound - GBP", Code = "GBP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Guernsey" });
            _list.Add(new Currency() { FullName = " Guernsey, Guernsey pound", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Guernsey" });
            _list.Add(new Currency() { FullName = " Guinea, Guinean franc - GNF", Code = "GNF", Symbol = "Fr", CountryOnEN = "Guinea" });
            _list.Add(new Currency() { FullName = " Guinea-Bissau, West African CFA franc - XOF", Code = "XOF", Symbol = "Fr", CountryOnEN = "Guinea-Bissau" });
            _list.Add(new Currency() { FullName = " Guyana, Guyanese dollar - GYD", Code = "GYD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Guyana" });
            _list.Add(new Currency() { FullName = " Haiti, Haitian gourde - HTG", Code = "HTG", Symbol = "G", CountryOnEN = "Haiti" });
            _list.Add(new Currency() { FullName = " Honduras, Honduran lempira - HNL", Code = "HNL", Symbol = "L", CountryOnEN = "Honduras" });
            _list.Add(new Currency() { FullName = " Hong Kong, Hong Kong dollar - HKD", Code = "HKD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Hong Kong" });
            _list.Add(new Currency() { FullName = " Hungary, Hungarian forint - HUF", Code = "HUF", Symbol = " Ft", CountryOnEN = "Hungary" });
            _list.Add(new Currency() { FullName = " Iceland, Icelandic króna - ISK", Code = "ISK", Symbol = "kr ", CountryOnEN = "Iceland" });
            _list.Add(new Currency() { FullName = " India, Indian rupee - INR", Code = "INR", Symbol = "₹", CountryOnEN = "India" });
            _list.Add(new Currency() { FullName = " Indonesia, Indonesian rupiah - IDR", Code = "IDR", Symbol = "Rp", CountryOnEN = "Indonesia" });
            _list.Add(new Currency() { FullName = " Iran, Iranian rial - IRR", Code = "IRR", Symbol = "﷼", CountryOnEN = "Iran" });
            _list.Add(new Currency() { FullName = " Iraq, Iraqi dinar - IQD", Code = "IQD", Symbol = "ع.د", CountryOnEN = "Iraq" });
            _list.Add(new Currency() { FullName = " Ireland, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Ireland" });
            _list.Add(new Currency() { FullName = " Isle of Man, British pound - GBP", Code = "GBP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Isle of Man" });
            _list.Add(new Currency() { FullName = " Isle of Man, Manx pound - IMP", Code = "IMP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Isle of Man" });
            _list.Add(new Currency() { FullName = " Israel, Israeli new shekel - ILS", Code = "ILS", Symbol = "₪", CountryOnEN = "Israel", CountryOnOriginalLanguage = "ישראל" });
            _list.Add(new Currency() { FullName = " Italy, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Italy", CountryOnOriginalLanguage = "Italiana" });
            _list.Add(new Currency() { FullName = " Jamaica, Jamaican dollar - JMD", Code = "JMD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Jamaica" });
            _list.Add(new Currency() { FullName = " Japan, Japanese yen - JPY", Code = "JPY", Symbol = "¥", HtmlCharRef = "&#165;", CountryOnEN = "Japan" });
            _list.Add(new Currency() { FullName = " Jersey, British pound - GBP", Code = "GBP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Jersey" });
            _list.Add(new Currency() { FullName = " Jersey, Jersey pound - JEP", Code = "JEP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Jersey" });
            _list.Add(new Currency() { FullName = " Jordan, Jordanian dinar - JOD", Code = "JOD", Symbol = "د.ا", CountryOnEN = "Jordan" });
            _list.Add(new Currency() { FullName = " Kazakhstan, Kazakhstani tenge - KZT", Code = "KZT", Symbol = "₸", CountryOnEN = "Kazakhstan" });
            _list.Add(new Currency() { FullName = " Kenya, Kenyan shilling - KES", Code = "KES", Symbol = "Sh", CountryOnEN = "Kenya" });
            _list.Add(new Currency() { FullName = " Kiribati, Australian dollar - AUD", Code = "AUD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Kiribati" });
            //_list.Add(new Currency() { FullName = " Kiribati, Kiribati dollar", Symbol = "$", HtmlCharRef = "&#36;"});
            _list.Add(new Currency() { FullName = " Korea, North, North Korean won - KPW", Code = "KPW", Symbol = "₩", CountryOnEN = "North Korea" });
            _list.Add(new Currency() { FullName = " Korea, South, South Korean won - KRW", Code = "KRW", Symbol = "₩", CountryOnEN = "South Korea" });
            _list.Add(new Currency() { FullName = " Kosovo, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Kosovo" });
            _list.Add(new Currency() { FullName = " Kuwait, Kuwaiti dinar - KWD", Code = "KWD", Symbol = "د.ك", CountryOnEN = "Kuwait" });
            _list.Add(new Currency() { FullName = " Kyrgyzstan, Kyrgyzstani som - KGS", Code = "KGS", Symbol = "сом", CountryOnEN = "Kyrgyzstan" });
            _list.Add(new Currency() { FullName = " Laos, Lao kip - LAK", Code = "LAK", Symbol = "₭", CountryOnEN = "Laos" });
            _list.Add(new Currency() { FullName = " Latvia, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Latvia", CountryOnOriginalLanguage = "Latvijas" });
            _list.Add(new Currency() { FullName = " Latvia, Latvian lats - LVL", Code = "LVL", Symbol = "Ls", CountryOnEN = "Latvia", CountryOnOriginalLanguage = "Latvijas" });
            _list.Add(new Currency() { FullName = " Lebanon, Lebanese pound - LBP", Code = "LBP", Symbol = "ل.ل", CountryOnEN = "Lebanon" });
            _list.Add(new Currency() { FullName = " Lesotho, Lesotho loti - LSL", Code = "LSL", Symbol = "L", CountryOnEN = "Lesotho" });
            _list.Add(new Currency() { FullName = " Lesotho, South African rand - ZAR", Code = "ZAR", Symbol = "R", CountryOnEN = "Lesotho" });
            _list.Add(new Currency() { FullName = " Liberia, Liberian dollar - LRD", Code = "LRD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Liberia" });
            _list.Add(new Currency() { FullName = " Libya, Libyan dinar - LYD", Code = "LYD", Symbol = "ل.د", CountryOnEN = "Libya" });
            _list.Add(new Currency() { FullName = " Liechtenstein, Swiss franc - CHF", Code = "CHF", Symbol = "CHF", CountryOnEN = "Liechtenstein" });
            _list.Add(new Currency() { FullName = " Lithuania, Lithuanian litas - LTL", Code = "LTL", Symbol = "Lt.", CountryOnEN = "Lithuania" });
            _list.Add(new Currency() { FullName = " Luxembourg, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Luxembourg" });
            _list.Add(new Currency() { FullName = " Macau, Macanese pataca - MOP", Code = "MOP", Symbol = "P", CountryOnEN = "Macau" });
            _list.Add(new Currency() { FullName = " Macedonia, Republic of - MKD", Code = "MKD", Symbol = "ден", CountryOnEN = "Macedonia" });
            _list.Add(new Currency() { FullName = " Madagascar, Malagasy ariary - MGA", Code = "MGA", Symbol = "Ar", CountryOnEN = "Madagascar" });
            _list.Add(new Currency() { FullName = " Malawi, Malawian kwacha - MWK", Code = "MWK", Symbol = "MK", CountryOnEN = "Malawi" });
            _list.Add(new Currency() { FullName = " Malaysia, Malaysian ringgit - MYR", Code = "MYR", Symbol = "RM", CountryOnEN = "Malaysia" });
            _list.Add(new Currency() { FullName = " Maldives, Maldivian rufiyaa - MVR", Code = "MVR", Symbol = "MVR", CountryOnEN = "Maldives" });
            _list.Add(new Currency() { FullName = " Mali, West African CFA franc - XOF", Code = "XOF", Symbol = "Fr", CountryOnEN = "Mali" });
            _list.Add(new Currency() { FullName = " Malta, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Malta" });
            _list.Add(new Currency() { FullName = " Marshall Islands, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Marshall Islands" });
            _list.Add(new Currency() { FullName = " Mauritania, Mauritanian ouguiya - MRO", Code = "MRO", Symbol = "UM", CountryOnEN = "Mauritania" });
            _list.Add(new Currency() { FullName = " Mauritius, Mauritian rupee - MUR", Code = "MUR", Symbol = "₨", CountryOnEN = "Mauritius" });
            _list.Add(new Currency() { FullName = " Mexico, Mexican peso - MXN", Code = "MXN", Symbol = "MX$", CountryOnEN = "Mexico" });
            //_list.Add(new Currency() { FullName = " Micronesia, Micronesian dollar", Symbol = "$", HtmlCharRef = "&#36;"});
            _list.Add(new Currency() { FullName = " Micronesia, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Micronesia" });
            _list.Add(new Currency() { FullName = " Moldova, Moldovan leu - MDL", Code = "MDL", Symbol = "L", CountryOnEN = "Moldova" });
            _list.Add(new Currency() { FullName = " Monaco, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Monaco" });
            _list.Add(new Currency() { FullName = " Mongolia, Mongolian tögrög - MNT", Code = "MNT", Symbol = "₮", CountryOnEN = "Mongolia" });
            _list.Add(new Currency() { FullName = " Montenegro, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Montenegro" });
            _list.Add(new Currency() { FullName = " Montserrat, East Caribbean dollar - XCD", Code = "XCD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Montserrat" });
            _list.Add(new Currency() { FullName = " Morocco, Moroccan dirham - MAD", Code = "MAD", Symbol = "د.م.", CountryOnEN = "Morocco" });
            _list.Add(new Currency() { FullName = " Mozambique, Mozambican metical - MZN", Code = "MZN", Symbol = "MT", CountryOnEN = "Mozambique" });
            _list.Add(new Currency() { FullName = " Nagorno-Karabakh Republic - AMD", Code = "AMD", Symbol = "դր.", CountryOnEN = "Nagorno-Karabakh" });
            //_list.Add(new Currency() { FullName = " Nagorno-Karabakh Republic, Nagorno-Karabakh dram", Symbol = "դր." });
            _list.Add(new Currency() { FullName = " Namibia, Namibian dollar - NAD", Code = "NAD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Namibia" });
            _list.Add(new Currency() { FullName = " Namibia, South African rand - ZAR", Code = "ZAR", Symbol = "R", CountryOnEN = "Namibia" });
            _list.Add(new Currency() { FullName = " Nauru, Australian dollar - AUD", Code = "AUD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Nauru" });
            //_list.Add(new Currency() { FullName = " Nauru, Nauruan dollar", Symbol = "$", HtmlCharRef = "&#36;"});
            _list.Add(new Currency() { FullName = " Nepal, Nepalese rupee - NPR", Code = "NPR", Symbol = "₨", CountryOnEN = "Nepal" });
            _list.Add(new Currency() { FullName = " Netherlands, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Netherlands", CountryOnOriginalLanguage = "Nederland" });
            _list.Add(new Currency() { FullName = " New Caledonia, CFP franc - XPF", Code = "XPF", Symbol = "Fr", CountryOnEN = "New Caledonia" });
            _list.Add(new Currency() { FullName = " New Zealand, New Zealand dollar - NZD", Code = "NZD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "New Zealand" });
            _list.Add(new Currency() { FullName = " Nicaragua, Nicaraguan córdoba - NIO", Code = "NIO", Symbol = "C$", CountryOnEN = "Nicaragua" });
            _list.Add(new Currency() { FullName = " Niger, West African CFA franc - XOF", Code = "XOF", Symbol = "Fr", CountryOnEN = "Niger" });
            _list.Add(new Currency() { FullName = " Nigeria, Nigerian naira - NGN", Code = "NGN", Symbol = "₦", CountryOnEN = "Nigeria", CountryOnOriginalLanguage = "Nijeriya" });
            _list.Add(new Currency() { FullName = " Niue, New Zealand dollar - NZD", Code = "NZD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Niue" });
            //_list.Add(new Currency() { FullName = " Niue, Niue dollar", Symbol = "$", HtmlCharRef = "&#36;"});
            _list.Add(new Currency() { FullName = " Northern Cyprus, Turkish lira - TRY", Code = "TRY", CountryOnEN = "Northern Cyprus" });
            _list.Add(new Currency() { FullName = " Norway, Norwegian krone - NOK", Code = "NOK", Symbol = "kr ", CountryOnEN = "Norway" });
            _list.Add(new Currency() { FullName = " Oman, Omani rial - OMR", Code = "OMR", Symbol = "ر.ع.", CountryOnEN = "Oman" });
            _list.Add(new Currency() { FullName = " Pakistan, Pakistani rupee - PKR", Code = "PKR", Symbol = "₨", CountryOnEN = "Pakistan" });
            //_list.Add(new Currency() { FullName = " Palau, Palauan dollar", Symbol = "$", HtmlCharRef = "&#36;"});
            _list.Add(new Currency() { FullName = " Palau, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Palau" });
            _list.Add(new Currency() { FullName = " Palestine, Israeli new shekel - ILS", Code = "ILS", Symbol = "₪", CountryOnEN = "Palestine" });
            _list.Add(new Currency() { FullName = " Palestine, Jordanian dinar - JOD", Code = "JOD", Symbol = "د.ا", CountryOnEN = "Palestine" });
            _list.Add(new Currency() { FullName = " Panama, Panamanian balboa - PAB", Code = "PAB", Symbol = "B/.", CountryOnEN = "Panama" });
            _list.Add(new Currency() { FullName = " Panama, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Panama" });
            _list.Add(new Currency() { FullName = " Papua New Guinea - PGK", Code = "PGK", Symbol = "K", CountryOnEN = "Papua New Guinea" });
            _list.Add(new Currency() { FullName = " Paraguay, Paraguayan guaraní - PYG", Code = "PYG", Symbol = "₲", CountryOnEN = "Paraguay" });
            _list.Add(new Currency() { FullName = " Peru, Peruvian nuevo sol - PEN", Code = "PEN", Symbol = "S/.", CountryOnEN = "Peru" });
            _list.Add(new Currency() { FullName = " Philippines, Philippine peso - PHP", Code = "PHP", Symbol = "₱", CountryOnEN = "Philippines" });
            _list.Add(new Currency() { FullName = " Pitcairn Islands, New Zealand dollar - NZD", Code = "NZD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Pitcairn Islands" });
            // _list.Add(new Currency() { FullName = " Pitcairn Islands, Pitcairn Islands dollar", Symbol = "$", HtmlCharRef = "&#36;"});
            _list.Add(new Currency() { FullName = " Poland, Polish złoty - PLN", Code = "PLN", Symbol = "zł", CountryOnEN = "Poland", CountryOnOriginalLanguage = "Polska" });
            _list.Add(new Currency() { FullName = " Portugal, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Portugal", CountryOnOriginalLanguage = "Portuguesa" });
            _list.Add(new Currency() { FullName = " Qatar, Qatari riyal - QAR", Code = "QAR", Symbol = "ر.ق", CountryOnEN = "Qatar" });
            _list.Add(new Currency() { FullName = " Romania, Romanian leu - RON", Code = "RON", Symbol = "lei", CountryOnEN = "Romania", CountryOnOriginalLanguage = "România" });
            _list.Add(new Currency() { FullName = " Russia, Russian ruble - RUB", Code = "RUB", Symbol = "руб.", CountryOnEN = "Russia", CountryOnOriginalLanguage = "Россия" });
            _list.Add(new Currency() { FullName = " Rwanda, Rwandan franc - RWF", Code = "RWF", Symbol = "Fr", CountryOnEN = "Rwanda" });
            _list.Add(new Currency() { FullName = " Saba, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Saba" });
            _list.Add(new Currency() { FullName = " Sahrawi Republic, Algerian dinar - DZD", Code = "DZD", Symbol = "د.ج", CountryOnEN = "Sahrawi Republic" });
            _list.Add(new Currency() { FullName = " Sahrawi Republic - MRO", Code = "MRO", Symbol = "UM", CountryOnEN = "Sahrawi Republic" });
            _list.Add(new Currency() { FullName = " Sahrawi Republic, Moroccan dirham - MAD", Code = "MAD", Symbol = "د. م.", CountryOnEN = "Sahrawi Republic" });
            //_list.Add(new Currency() { FullName = " Sahrawi Republic, Sahrawi peseta", Symbol = "Ptas" });
            _list.Add(new Currency() { FullName = " Saint Helena, Saint Helena pound - SHP", Code = "SHP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Saint Helena" });
            _list.Add(new Currency() { FullName = " Saint Kitts and Nevis - XCD", Code = "XCD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Saint Kitts and Nevis" });
            _list.Add(new Currency() { FullName = " Saint Lucia, East Caribbean dollar - XCD", Code = "XCD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Saint Lucia" });
            _list.Add(new Currency() { FullName = " Saint Vincent and the Grenadines - XCD", Code = "XCD", Symbol = "$", HtmlCharRef = "&#36;" });
            _list.Add(new Currency() { FullName = " Samoa, Samoan tālā - WST", Code = "WST", Symbol = "T", CountryOnEN = "Samoa" });
            _list.Add(new Currency() { FullName = " San Marino, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "San Marino" });
            _list.Add(new Currency() { FullName = " São Tomé and Príncipe dobra - STD", Code = "STD", Symbol = "Db", CountryOnEN = "São Tomé and Príncipe dobra" });
            _list.Add(new Currency() { FullName = " Saudi Arabia, Saudi riyal - SAR", Code = "SAR", Symbol = "ر.س", CountryOnEN = "Saudi Arabia" });
            _list.Add(new Currency() { FullName = " Senegal, West African CFA franc - XOF", Code = "XOF", Symbol = "Fr", CountryOnEN = "Senegal" });
            _list.Add(new Currency() { FullName = " Serbia, Serbian dinar - RSD", Code = "RSD", CountryOnEN = "Serbia" });
            _list.Add(new Currency() { FullName = " Seychelles, Seychellois rupee - SCR", Code = "SCR", Symbol = "₨", CountryOnEN = "Seychelles" });
            _list.Add(new Currency() { FullName = " Sierra Leone, Sierra Leonean leone - SLL", Code = "SLL", Symbol = "Le", CountryOnEN = "Sierra Leone" });
            _list.Add(new Currency() { FullName = " Singapore, Brunei dollar - BND", Code = "BND", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Singapore" });
            _list.Add(new Currency() { FullName = " Singapore, Singapore dollar - SGD", Code = "SGD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Singapore" });
            _list.Add(new Currency() { FullName = " Sint Eustatius, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Sint Eustatius" });
            _list.Add(new Currency() { FullName = " Sint Maarten - ANG", Code = "ANG", Symbol = "ƒ", HtmlCharRef = "&#402;", CountryOnEN = "Sint Maarten" });
            _list.Add(new Currency() { FullName = " Slovakia, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Slovakia", CountryOnOriginalLanguage = "Slovenská" });
            _list.Add(new Currency() { FullName = " Slovenia, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Slovenia", CountryOnOriginalLanguage = "Slovenija" });
            _list.Add(new Currency() { FullName = " Solomon Islands, Solomon Islands dollar - SBD", Code = "SBD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Solomon Islands" });
            _list.Add(new Currency() { FullName = " Somalia, Somali shilling - SOS", Code = "SOS", Symbol = "Sh", CountryOnEN = "Somalia" });
            //_list.Add(new Currency() { FullName = " Somaliland, Somaliland shilling", Symbol = "Sh" });
            _list.Add(new Currency() { FullName = " South Africa, South African rand - ZAR", Code = "ZAR", Symbol = "R", CountryOnEN = "South Africa" });
            _list.Add(new Currency() { FullName = " South Georgia & South Sandwich Islands - GBP", Code = "GBP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "South Georgia & South Sandwich Islands" });
            // _list.Add(new Currency() { FullName = " South Georgia and the South Sandwich Islands, South Georgia and the South Sandwich Islands pound", Symbol = "£", HtmlCharRef = "&#163;"});
            _list.Add(new Currency() { FullName = " South Ossetia, Russian ruble - RUB", Code = "RUB", Symbol = "р.", CountryOnEN = "South Ossetia" });
            _list.Add(new Currency() { FullName = " Spain, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Spain", CountryOnOriginalLanguage = "España" });
            _list.Add(new Currency() { FullName = " South Sudan, South Sudanese pound - SSP", Code = "SSP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "South Sudan" });
            _list.Add(new Currency() { FullName = " Sri Lanka, Sri Lankan rupee - LKR", Code = "LKR", Symbol = "Rs", CountryOnEN = "Sri Lanka" });
            _list.Add(new Currency() { FullName = " Sudan, Sudanese pound - SDG", Code = "SDG", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Sudan" });
            _list.Add(new Currency() { FullName = " Suriname, Surinamese dollar - SRD", Code = "SRD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Suriname" });
            _list.Add(new Currency() { FullName = " Swaziland, Swazi lilangeni - SZL", Code = "SZL", Symbol = "L", CountryOnEN = "Swaziland", CountryOnOriginalLanguage = "Umbuso weSwatini" });
            _list.Add(new Currency() { FullName = " Sweden, Swedish krona - SEK", Code = "SEK", Symbol = "kr ", CountryOnEN = "Sweden", CountryOnOriginalLanguage = "Konungariket Sverige" });
            _list.Add(new Currency() { FullName = " Switzerland, Swiss franc - CHF", Code = "CHF", Symbol = "CHF", CountryOnEN = "Switzerland" });
            _list.Add(new Currency() { FullName = " Syria, Syrian pound - SYP", Code = "SYP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Syria" });
            _list.Add(new Currency() { FullName = " Taiwan, New Taiwan dollar - TWD", Code = "TWD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Taiwan" });
            _list.Add(new Currency() { FullName = " Tajikistan, Tajikistani somoni - TJS", Code = "TJS", Symbol = "ЅМ", CountryOnEN = "Tajikistan" });
            _list.Add(new Currency() { FullName = " Tanzania, Tanzanian shilling - TZS", Code = "TZS", Symbol = "Sh", CountryOnEN = "Tanzania" });
            _list.Add(new Currency() { FullName = " Thailand, Thai baht - THB", Code = "THB", Symbol = "฿", CountryOnEN = "Thailand" });
            _list.Add(new Currency() { FullName = " Togo, West African CFA franc - XOF", Code = "XOF", Symbol = "Fr", CountryOnEN = "Togo" });
            _list.Add(new Currency() { FullName = " Tonga, Tongan paʻanga - TOP", Code = "TOP", Symbol = "T$", CountryOnEN = "Tonga" });
            _list.Add(new Currency() { FullName = " Transnistria, Transnistrian ruble - PRB", Code = "PRB", Symbol = "р.", CountryOnEN = "Transnistria" });
            _list.Add(new Currency() { FullName = " Trinidad and Tobago - TTD", Code = "TTD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Trinidad and Tobago" });
            _list.Add(new Currency() { FullName = " Tristan da Cunha, Saint Helena pound - SHP", Code = "SHP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Tristan da Cunha" });
            //_list.Add(new Currency() { FullName = " Tristan da Cunha, Tristan da Cunha pound", Symbol = "£", HtmlCharRef = "&#163;"});
            _list.Add(new Currency() { FullName = " Tunisia, Tunisian dinar - TND", Code = "TND", Symbol = "د.ت", CountryOnEN = "Tunisia" });
            _list.Add(new Currency() { FullName = " Turkey, Turkish lira - TRY", Code = "TRY", CountryOnEN = "Turkey" });
            _list.Add(new Currency() { FullName = " Turkmenistan, Turkmenistan manat - TMT", Code = "TMT", Symbol = "m", CountryOnEN = "Turkmenistan" });
            _list.Add(new Currency() { FullName = " Turks and Caicos Islands - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Turks and Caicos Islands" });
            _list.Add(new Currency() { FullName = " Tuvalu, Australian dollar - AUD", Code = "AUD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Tuvalu" });
            //_list.Add(new Currency() { FullName = " Tuvalu, Tuvaluan dollar", Symbol = "$", HtmlCharRef = "&#36;"});
            _list.Add(new Currency() { FullName = " Uganda, Ugandan shilling - UGX", Code = "UGX", Symbol = "Sh", CountryOnEN = "Uganda" });
            _list.Add(new Currency() { FullName = " Ukraine, Ukrainian hryvnia - UAH", Code = "UAH", Symbol = "₴", CountryOnEN = "Ukraine", CountryOnOriginalLanguage = "Україна" });
            _list.Add(new Currency() { FullName = " United Arab Emirates - AED", Code = "AED", Symbol = "AED", CountryOnEN = "United Arab Emirates" });
            _list.Add(new Currency() { FullName = " United Kingdom, British pound - GBP", Code = "GBP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "United Kingdom", CountryOnOriginalLanguage = "United Kingdom" });
            _list.Add(new Currency() { FullName = " United States - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "United States", CountryOnOriginalLanguage = "United States" });
            _list.Add(new Currency() { FullName = " Uruguay, Uruguayan peso - UYU", Code = "UYU", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Uruguay" });
            _list.Add(new Currency() { FullName = " Uzbekistan, Uzbekistani som - UZS", Code = "UZS", Symbol = "лв.", CountryOnEN = "Uzbekistan" });
            _list.Add(new Currency() { FullName = " Vanuatu, Vanuatu vatu - VUV", Code = "VUV", Symbol = "Vt", CountryOnEN = "Vanuatu" });
            _list.Add(new Currency() { FullName = " Vatican City, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Vatican City" });
            _list.Add(new Currency() { FullName = " Venezuela, Venezuelan bolívar - VEF", Code = "VEF", Symbol = "Bs F", CountryOnEN = "Venezuela" });
            _list.Add(new Currency() { FullName = " Vietnam, Vietnamese đồng - VND", Code = "VND", Symbol = "₫", CountryOnEN = "Vietnam" });
            _list.Add(new Currency() { FullName = " Wallis and Futuna, CFP franc - XPF", Code = "XPF", Symbol = "Fr", CountryOnEN = "Wallis and Futuna" });
            _list.Add(new Currency() { FullName = " Yemen, Yemeni rial - YER", Code = "YER", Symbol = "﷼", CountryOnEN = "Yemen" });
            _list.Add(new Currency() { FullName = " Zambia, Zambian kwacha - ZMW", Code = "ZMW", Symbol = "ZK", CountryOnEN = "Zambia" });
            _list.Add(new Currency() { FullName = " Zimbabwe, Botswana pula - BWP", Code = "BWP", Symbol = "P", CountryOnEN = "Zimbabwe" });
            _list.Add(new Currency() { FullName = " Zimbabwe, British pound - GBP", Code = "GBP", Symbol = "£", HtmlCharRef = "&#163;", CountryOnEN = "Zimbabwe" });
            _list.Add(new Currency() { FullName = " Zimbabwe, Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;", CountryOnEN = "Zimbabwe" });
            _list.Add(new Currency() { FullName = " Zimbabwe, South African rand - ZAR", Code = "ZAR", Symbol = "R", CountryOnEN = "Zimbabwe" });
            _list.Add(new Currency() { FullName = " Zimbabwe, United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;", CountryOnEN = "Zimbabwe" });
            _list.Add(new Currency() { FullName = "  Euro - EUR", Code = "EUR", Symbol = "€", HtmlCharRef = "&#8364;" });
            _list.Add(new Currency() { FullName = "  British pound - GBP", Code = "GBP", Symbol = "£", HtmlCharRef = "&#163;" });
            _list.Add(new Currency() { FullName = "  United States dollar - USD", Code = "USD", Symbol = "$", HtmlCharRef = "&#36;" });


        }

        public static void FillList_Short()
        {
            //fill countries
            _list = new List<Currency>();
            _list.Add(new Currency() { FullName = "U.S. Dollar - USD", Code = "USD" });
            _list.Add(new Currency() { FullName = "Euro - EUR", Code = "EUR" });
            _list.Add(new Currency() { FullName = "Canada, Dollars - CAD", Code = "CAD" });
            _list.Add(new Currency() { FullName = "China, Yuan Renminbi - CNY", Code = "CNY" });
            _list.Add(new Currency() { FullName = "Hong Kong, Dollars - HKD", Code = "HKD" });
            _list.Add(new Currency() { FullName = "Israel, New Shekels - ILS", Code = "ILS" });
            _list.Add(new Currency() { FullName = "Japan, Yen - JPY", Code = "JPY" });
            _list.Add(new Currency() { FullName = "Russia, Rubles - RUB", Code = "RUB" });
            _list.Add(new Currency() { FullName = "Singapore Dollar - SGD", Code = "SGD" });
            _list.Add(new Currency() { FullName = "United Kingdom, Pounds - GBP", Code = "GBP" });
        }

        internal static List<ParsedCurrency> GetPatternsForKnownCountries()
        {
            List<ParsedCurrency> list = new List<ParsedCurrency>();

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"USD\d+(.\d{1,2})" });//USD19.99 USD\\d+(.\\d{1,2})
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"USD[[ ]]{1,1}\d+(.\d{1,2})" });//USD 19.99 USD[[:blank:]]{1,1}\\d+(.\\d{1,2})

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"USD\d+(.\d{1,2})" });//19.99USD \\d+(.\\d{1,2})USD
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})[ ]{1,1}USD" });//19.99 USD \\d+(.\\d{1,2})[[:blank:]]{1,1}USD

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"US\$\d+(.\d{1,2})" });//US$19.99 
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"US&#36;\d+(.\d{1,2})" });//US$19.99 

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"US\$[[ ]]{1,1}\d+(.\d{1,2})" });//US$ 19.99                                                                                                   
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"US&#36;[[ ]]{1,1}\d+(.\d{1,2})" });//US$ 19.99     

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"US[[ ]]{1,1}\$\d+(.\d{1,2})" });//US $19.99
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"US[[ ]]{1,1}&#36;\d+(.\d{1,2})" });//US $19.99

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})[[ ]]{1,1}US\$" });//19.99 US$
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})[[ ]]{1,1}US&#36;" });//19.99 US$

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})US\$" }); //19.99US$
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})US&#36;" }); //19.99US$

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})[[ ]]{1,1}US" });//19.99 US
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})US" });//19.99US            

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\$\d+(.\d{1,2})" }); //$19.95
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"&#36;\d+(.\d{1,2})" }); //$19.95

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\$[[ ]]{1,1}\d+(.\d{1,2})" }); //$ 19.95
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"&#36;[[ ]]{1,1}\d+(.\d{1,2})" }); //$ 19.95

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})\$" }); //19.95$ 
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})&#36;" }); //19.95$ 

            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})[[ ]]{1,1}\$" }); //19.95 $ 
            list.Add(new ParsedCurrency() { FullName = "United States dollar - USD", Code = "USD", PricePattern = @"\d+(.\d{1,2})[[ ]]{1,1}&#36;" }); //19.95 $ 
            return list;
        }

        public static List<string> GetBestKnownCurrencies()
        {
            List<string> best_known_currencies = new List<string>
            {
                "AUD",
                "EUR",
                "GBP"
            };
            return best_known_currencies;
        }
    }

    public class Currency
    {
        public string Code { get; set; }
        public string FullName { get; set; }
        public string Symbol { get; set; }

        public string HtmlCharRef { get; set; }
        public int ParsedMatchesOnHomePage { get; set; }
        public string CountryOnEN { get; set; }
        public string CountryOnOriginalLanguage { get; set; }

        public int Priority { get; set; }
    }

    public class ParsedCurrency
    {
        public string Code { get; set; }
        public string FullName { get; set; }
        //public string CountryOnEN { get; set; }
        public string PricePattern { get; set; }
        //public string Symbol { get; set; }
        //public string HtmlCharRef { get; set; }
        public int MatchesCount { get; set; }


    }
}