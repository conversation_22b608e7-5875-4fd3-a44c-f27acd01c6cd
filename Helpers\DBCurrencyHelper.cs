﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class DBCurrencyHelper
    {

        //public static decimal CurrencyRateToUsd(Shop shop, string currency = null)//should be at least shopID or currency 
        //{
        //    decimal currencyRate = 1;
        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    if (currency == "USD")
        //    {
        //        return 1;
        //    }
        //    else if (currency == null)
        //    {
        //        if (shop.Currency == null)
        //        {
        //            return 1;
        //        }
        //        currency = shop.Currency;
        //    }
        //    currencyRate = db.CurrencyRatesToUSDs.Where(x => x.Currency == currency).Select(x => x.ConversionRate).FirstOrDefault().Value;
        //    return currencyRate;
        //}

        //public static decimal GetCurrencyRateFromCurrencyToCurrency(string fromCurrency, string toCurrency)
        //{
        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    if (fromCurrency==null)
        //    {
        //        fromCurrency = "USD";
        //    }
        //    if (toCurrency == null)
        //    {
        //        toCurrency = "USD";
        //    }
        //    decimal? fromCurrencyRateToUSD= db.CurrencyRatesToUSDs.Where(x => x.Currency == fromCurrency).Select(x => x.ConversionRate).FirstOrDefault().Value;
        //    decimal? toCurrencyRateToUSD= db.CurrencyRatesToUSDs.Where(x => x.Currency == toCurrency).Select(x => x.ConversionRate).FirstOrDefault().Value;
        //    if(fromCurrencyRateToUSD!=null && toCurrencyRateToUSD!=null && toCurrencyRateToUSD != 0)
        //    {
        //        return fromCurrencyRateToUSD.Value / toCurrencyRateToUSD.Value;
        //    }
        //    else
        //    {
        //        return 1;
        //    }
        //}
        //public static decimal GetConvertedValueInUSD(string currency, decimal valueToConvert)
        //{
        //    decimal currencyRateToUsd = CurrencyRateToUsd(0,currency);
        //    return currencyRateToUsd * valueToConvert;

        //}

        private static decimal GetRateFromDB(string currency)
        {
            if (string.IsNullOrEmpty(currency) || currency == "USD")
            {
                return 1;
            }
            Console.WriteLine("Get currency rate from DB.");
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var rateInDb = db.CurrencyRatesToUSDs.Where(x => x.Currency == currency).Select(x => x.ConversionRate).SingleOrDefault();
            if (rateInDb == null)
            {
                return 1;
            }
            return rateInDb.Value;
        }
        public static decimal GetValueInCurrency(string currensy, decimal valueInUSD)
        {
            decimal currencyRateToUSD = GetRateFromDB(currensy);
            if (currencyRateToUSD != 0)
            {
                return valueInUSD / currencyRateToUSD;
            }
            else
            {
                return valueInUSD;
            }
        }
        //should be at least shopID or currency 
        //public static decimal CurrencyRateToUsd(int? shopId = null, string currency = null)
        //{
        //    if (currency == null)
        //    {
        //        //shop should be supplied
        //        StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //        Shop shop = db.Shops.SingleOrDefault(s => s.ID == shopId);
        //        if (shop == null)
        //        {
        //            throw new Exception("CurrencyRateToUsd failed no shop with Id " + shopId);
        //        }
        //        currency = shop.Currency;
        //    }

        //    var rateFromDb = GetRateFromDB(currency);
        //    return rateFromDb;
        //}

        public static decimal CurrencyRateToUsd(string currency)
        {
            var rateFromDb = GetRateFromDB(currency);
            return rateFromDb;
        }
        public static decimal? GetRevenuesInUSD(string currency, decimal? revenue)
        {
            decimal currencyRateToUsd = GetRateFromDB(currency);
            if (revenue.HasValue)
            {
                return revenue * currencyRateToUsd;
            }
            else
            {
                return null;
            }
        }
        public static CurrencyRatesToUSD GetCurrencyModelByCurrency(string currency)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            CurrencyRatesToUSD currencyRateModel = db.CurrencyRatesToUSDs.Where(x => x.Currency == currency).SingleOrDefault();
            return currencyRateModel;
        }
        public static List<CurrencyRatesToUSD> GetCurrencyModelsByCurrencies(List<string> currencies)
        {
            List<CurrencyRatesToUSD> currenciesRatesModel = new List<CurrencyRatesToUSD>();
            List<string> cur = new List<string>();
            foreach (var currency in currencies)
            {
                if (!cur.Contains(currency) && currency != null)
                {
                    currenciesRatesModel.Add(GetCurrencyModelByCurrency(currency));
                    cur.Add(currency);
                }

            }
            return currenciesRatesModel;
        }
        public static string GetConversionsValueWithCurrencySymbol(string currency, string conversionsValue)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            string currencySymbol = db.CurrencyRatesToUSDs.Where(x => x.Currency == currency).Select(x => x.CurrencySymbol).SingleOrDefault();
            if (currencySymbol != null && currencySymbol != "")
            {
                return currencySymbol + conversionsValue;
            }
            else
            {
                return conversionsValue + " " + currency;
            }
        }
        public static string GetConversionsValueWithCurrencySymbol(string currency, decimal conversionsValueInUSD)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var currencyData = db.CurrencyRatesToUSDs.Where(x => x.Currency == currency).SingleOrDefault();
            decimal conversionsValue = conversionsValueInUSD;
            string symbol = null;
            if (currencyData.ConversionRate != null && currencyData.ConversionRate != 0)
            {
                conversionsValue = conversionsValueInUSD / currencyData.ConversionRate.Value;
                symbol = currencyData.CurrencySymbol;
            }
            string formatedConversionValue = NumbersFormater.FormatBigNumber(conversionsValue);
            if (symbol != null && symbol != "")
            {
                return symbol + formatedConversionValue;
            }
            else
            {
                return formatedConversionValue + " " + currency;
            }
        }
    }
}
