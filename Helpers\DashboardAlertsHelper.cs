﻿using Storeya.Core.Models.GA;
using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.PeerToPeer;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using static System.Net.Mime.MediaTypeNames;

namespace Storeya.Core.Helpers
{
    public class DashboardAlertsHelper
    {
        public static List<DashboardAlert> GetUpgradeDashboardAlertsForTheLatestPeriod(int shopId, DateTime fromDate)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            return db.DashboardAlerts.Where(d => d.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade && d.InsertedAt >= fromDate && d.ShopID == shopId).ToList();
        }
        public static List<DashboardAlert> GetActiveUpgradesAndHolidaysDashboardAlertsForTheShop(int shopId)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            return db.DashboardAlerts.Where(d => d.Status == (int)Storeya.Core.Entities.DashboardNotificationStatus.Active && d.ValidUntil >= DateTime.Now && d.ShopID == shopId &&
            (d.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade || d.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.UpgradeWithoutButton
            || d.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.HolidayAlert)).ToList();
        }
        public static bool IsUpgradeType(int type)
        {
            switch (type)
            {
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade:
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.HolidayAlert:
                case (int)Storeya.Core.Entities.DashboardNotificationAlertType.UpgradeWithoutButton:
                    return true;
                default:
                    return false;
            }
        }
        public static bool AddActiveUpgradeDashboardAlert(int shopID, string title, string text, DateTime? validUntil = null)
        {
            var alerts = GetActiveUpgradesAndHolidaysDashboardAlertsForTheShop(shopID);
            if (alerts.Any())
            {
                Console.WriteLine($"The shop with shopID={shopID} already has an active alert. There was no alert added");
                return false;
            }
            else
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                if (validUntil == null)
                {
                    validUntil = DateTime.Now.AddDays(7);
                }
                DashboardAlert dashboardAlert = new DashboardAlert();
                dashboardAlert.InsertedAt = DateTime.Now;
                dashboardAlert.ValidUntil = validUntil;
                dashboardAlert.Status = (int)Storeya.Core.Entities.DashboardNotificationStatus.Active;
                dashboardAlert.AlertType = (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade;
                dashboardAlert.ShopID = shopID;
                dashboardAlert.AlertTitle = title;
                dashboardAlert.AlertText = text;
                dashboardAlert.ClicksCount = 0;
                db.DashboardAlerts.Add(dashboardAlert);
                db.SaveChanges();
                return true;
            }

        }

        public static string DisableHolidayAlert(int shopID, bool alsoUpgrade = false, int? newStatus = null)
        {
            List<int> alertTypes = new List<int>()
            {
                (int)Storeya.Core.Entities.DashboardNotificationAlertType.HolidayAlert
            };
            if (alsoUpgrade)
            {
                alertTypes = new List<int>()
                {
                    (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade,
                    (int)Storeya.Core.Entities.DashboardNotificationAlertType.UpgradeWithoutButton,
                    (int)Storeya.Core.Entities.DashboardNotificationAlertType.HolidayAlert
                };
            }
            if (newStatus == null)
            {
                newStatus = (int)Storeya.Core.Entities.DashboardNotificationStatus.Upgraded;
            }
            return DashboardAlertsHelper.ChangeDashboardAlertStatus(shopID, alertTypes, (int)Storeya.Core.Entities.DashboardNotificationStatus.Active,
                newStatus.Value);
        }

        public static string ChangeDashboardAlertStatus(int shopID, List<int> alertTypes, int oldAlertStatus, int newAlertStatus, DateTime? fromDate = null)
        {
            try
            {
                if (fromDate == null)
                {
                    fromDate = DateTime.Today.AddDays(-15);
                }
                List<DashboardAlert> alerts = GetShopAlertsByTypesAndStatus(shopID, alertTypes, oldAlertStatus, fromDate);
                if (alerts != null && alerts.Count > 0)
                {

                    StringBuilder result = new StringBuilder();
                    result.AppendLine($"Dashboard alerts status for shop with id={shopID} have been changed from" +
                        $" {Enum.GetName(typeof(Storeya.Core.Entities.DashboardNotificationStatus), oldAlertStatus)} to {Enum.GetName(typeof(Storeya.Core.Entities.DashboardNotificationStatus), newAlertStatus)}.");
                    result.AppendLine("The list of alert IDs: ");

                    StoreYaEntities db = DataHelper.GetStoreYaEntities();
                    foreach (var alert in alerts)
                    {
                        var dashboardAlert = db.DashboardAlerts.Where(x => x.ID == alert.ID).SingleOrDefault();
                        dashboardAlert.Status = newAlertStatus;
                        dashboardAlert.UpdatedAt = DateTime.Now;
                        result.Append($"{dashboardAlert.ID}, ");
                    }
                    db.SaveChanges();
                    var res = result.ToString();
                    ConsoleAppHelper.WriteLogWithDB(res, shopID);
                    //EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Alerts Type was changed", res);
                    return res;
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Error in method ChangeDashboardAlertStatus", ex.ToString());
            }
            return null;

        }
        public static List<DashboardAlert> GetShopAlertsByTypesAndStatus(int shopID, List<int> alertTypes, int status, DateTime? fromDate = null)
        {
            if (fromDate == null)
            {
                fromDate = DateTime.Today.AddDays(-15);
            }
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var alerts = db.DashboardAlerts.Where(x => x.ShopID == shopID && x.InsertedAt >= fromDate && x.Status == status).ToList();
            List<DashboardAlert> dashboardAlerts = new List<DashboardAlert>();
            foreach (var type in alertTypes)
            {
                dashboardAlerts.AddRange(alerts.Where(x => x.AlertType == type).ToList());
            }
            return dashboardAlerts;
        }
        public static List<DashboardAlert> GetAllActiveHolidaysAndUpgradeAlerts()
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var activeHolidayAndUpgradeAlerts = db.DashboardAlerts.Where(x => x.Status == (int)Storeya.Core.Entities.DashboardNotificationStatus.Active &&
            (x.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.HolidayAlert || x.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade)).ToList();
            return activeHolidayAndUpgradeAlerts;
        }
        public static void CheckIfThereIsMoreThanOneActiveUpgradeAndHolidayAlert()
        {
            try
            {
                var activeHolidayAndUpgradeAlerts = GetAllActiveHolidaysAndUpgradeAlerts();
                var shops = activeHolidayAndUpgradeAlerts.GroupBy(x => x.ShopID).Where(x => x.Count() > 1).Select(x => x.Key).ToList();
                if (shops.Any())
                {
                    StringBuilder result = new StringBuilder();
                    result.AppendLine("Some shops have more than one active alert! Here is a list of shopIDs: ");

                    foreach (var shop in shops)
                    {
                        result.Append($"{shop}, ");
                    }
                    string res = result.ToString().Replace("\r\n", "<br>");
                    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "More than one active dashboard alert!", res);
                }

            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Error in method CheckIfThereIsMoreThanOneActiveUpgradeAndHolidayAlert", ex.ToString());
            }
        }

        public static int? AddUpgradeDashboardAlert(out int? idExistingNotification, int shopID, string text, string title, int status, int? validDays = 10, int? alertType = (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade, string additionalCode = null)
        {
            try
            {
                DateTime now = DateTime.Now;
                if (additionalCode == null)
                {
                    text += $"<br/><br/><small>Publish date: {now.ToString("dd MMM yyy")} </small>";
                }
                else
                {
                    additionalCode += $"<br/><br/><small>Publish date: {now.ToString("dd MMM yyy")} </small>";
                }
                var db = DataHelper.GetStoreYaEntities();
                var dbalert = DashboardAlertsHelper.GetActiveUpgradesAndHolidaysDashboardAlertsForTheShop(shopID);
                //var dbalert = db.DashboardAlerts.Where(x => x.ShopID == shopID && x.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade && x.Status == (int)Storeya.Core.Entities.DashboardNotificationStatus.Active && x.ValidUntil >= now);
                var activeAlertCount = dbalert.Count();
                if (activeAlertCount == 1)
                {
                    idExistingNotification = dbalert.FirstOrDefault().ID;
                }
                else if (activeAlertCount > 1)
                {
                    idExistingNotification = -2; //-2 means more than one active notification
                }
                else
                {
                    idExistingNotification = null;
                }
                DashboardAlert dashboardAlert = new DashboardAlert();
                dashboardAlert.ShopID = shopID;
                dashboardAlert.AlertAdditionalCode = additionalCode;
                dashboardAlert.AlertText = text;
                dashboardAlert.AlertTitle = title;
                dashboardAlert.Status = status;
                dashboardAlert.AlertType = alertType.Value;
                dashboardAlert.InsertedAt = now;
                dashboardAlert.ValidUntil = now.AddDays(validDays.Value);
                dashboardAlert.ClicksCount = 0;
                db.DashboardAlerts.Add(dashboardAlert);
                db.SaveChanges();
                return (dashboardAlert.ID);

            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, $"Can't add DashboardAlert for ShopID={shopID}", ex.ToString());
                idExistingNotification = null;
                return null;
            }
        }
        public static bool UpdateAlertClicksCount(int id, string userName)
        {
            var db = DataHelper.GetStoreYaEntities();
            var alert = db.DashboardAlerts.SingleOrDefault(a => a.ID == id);
            if (alert == null)
            {
                return false;
            }
            alert.UpdatedAt = DateTime.Now;
            int clicks = alert.ClicksCount.HasValue ? alert.ClicksCount.Value : 0;
            alert.ClicksCount = clicks + 1;
            db.SaveChanges();
            Log4NetLogger.Info($"{userName} clicked on notification button. {alert.ClicksCount} times", alert.ShopID.Value);
            return true;
        }

        public static bool UpdateAlertDisabledByUser(int id, string userName)
        {
            var db = DataHelper.GetStoreYaEntities();
            DashboardAlert ad = db.DashboardAlerts.Where(t => t.ID == id).SingleOrDefault();
            if (ad == null)
            {
                return false;
            }
            ad.Status = (int)Storeya.Core.Entities.DashboardNotificationStatus.DisabledByUser;
            db.SaveChanges();
            Log4NetLogger.Info(userName + " ignored update notification", ad.ShopID.Value);
            return true;
        }
        public static bool CreateDashboardAlertIfItDoesNotExist(int shopID, string title, string body, int alertType, bool active, int validUntilInDays = 7, string additionalCode = null)
        {
            return CreateDashboardAlertIfItDoesNotExist(shopID, title, body, alertType, active, out int dashboardAlertId, validUntilInDays, additionalCode);
        }
        public static bool CreateDashboardAlertIfItDoesNotExist(int shopID, string title, string body, int alertType, bool active, out int dashboardAlertId, int validUntilInDays = 7, string additionalCode = null, int? subType = null)
        {
            dashboardAlertId = 0;
            DateTime now = DateTime.Now;
            if (additionalCode == null)
            {
                body += $"<br/><br/><small>Publish date: {now.ToString("dd MMM yyy")} </small>";
            }
            else
            {
                additionalCode += $"<br/><br/><small>Publish date: {now.ToString("dd MMM yyy")} </small>";
            }
            string type = Enum.GetName(typeof(Storeya.Core.Entities.DashboardNotificationAlertType), alertType);
            var db = DataHelper.GetStoreYaEntities();
            if (db.DashboardAlerts.Where(t => t.ShopID == shopID && t.AlertType == alertType
            && t.Status == (int)Storeya.Core.Entities.DashboardNotificationStatus.Active && t.ValidUntil > DateTime.Now).Any())
            {
                Log4NetLogger.Info($"A dashboard alert has not been added. This shop already has a dashboard alert of type {type}.", shopID);
                return false;
            }
            else
            {
                DashboardAlert dashboardAlert = new DashboardAlert();
                dashboardAlert.ShopID = shopID;
                dashboardAlert.AlertText = body;
                dashboardAlert.AlertTitle = title;
                dashboardAlert.AlertType = alertType;
                dashboardAlert.InsertedAt = now;
                dashboardAlert.ValidUntil = now.AddDays(validUntilInDays);
                dashboardAlert.ClicksCount = 0;
                dashboardAlert.SubType = subType;
                if (active)
                {
                    dashboardAlert.Status = (int)Storeya.Core.Entities.DashboardNotificationStatus.Active;
                }
                db.DashboardAlerts.Add(dashboardAlert);
                db.SaveChanges();
                dashboardAlertId = dashboardAlert.ID;
                return true;
            }
        }
        public static bool CheckIfThereIsActiveAlertlWithTypeAndSubtype(int shopID, int? type, int? subType)
        {
            var db = DataHelper.GetStoreYaEntities();
            int status = (int)Storeya.Core.Entities.DashboardNotificationStatus.Active;
            return db.DashboardAlerts.Where(x => x.ShopID == shopID && x.Status == status && x.AlertType == type && x.SubType == subType).Any();
        }
        public static void ChangeActiveAlertsIfExpired()
        {
            try
            {
                DateTime now = DateTime.Now;
                var db = DataHelper.GetStoreYaEntities();
                var expired = db.DashboardAlerts.Where(x => (x.ValidUntil < now) && x.Status == (int)Storeya.Core.Entities.DashboardNotificationStatus.Active).ToArray();
                foreach (var alert in expired)
                {
                    alert.Status = (int)Storeya.Core.Entities.DashboardNotificationStatus.Expired;
                    alert.UpdatedAt = DateTime.Now;
                    ConsoleAppHelper.WriteLog($"Dashboard alert with id={alert.ID} expired", alert.ShopID.Value);
                }
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Error in method ChangeActiveAlertsIfPaidOrExpired", ex.ToString());
            }
        }
        public static string ChangeActiveAlertsIfPaid(int shopID)
        {
            List<int> alertTypes = new List<int>() {
                (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade,
                (int)Storeya.Core.Entities.DashboardNotificationAlertType.UpgradeWithoutButton,
                (int)Storeya.Core.Entities.DashboardNotificationAlertType.FailedCharge
            };
            int oldAlertStatus = (int)Storeya.Core.Entities.DashboardNotificationStatus.Active;
            int newAlertStatus = (int)Storeya.Core.Entities.DashboardNotificationStatus.Paid;
            DateTime fromDate = DateTime.Today.AddDays(-100);
            return ChangeDashboardAlertStatus(shopID, alertTypes, oldAlertStatus, newAlertStatus, fromDate);
        }
        public static void CheckAllActiveUpgradeEmailsAndDisableIfAlreadyPaid()
        {
            DateTime now = DateTime.Now;
            var db = DataHelper.GetStoreYaEntities();
            var potentialPaid = db.DashboardAlerts.Where(x => x.Status == (int)Storeya.Core.Entities.DashboardNotificationStatus.Active &&
            (x.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.Upgrade || x.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.UpgradeWithoutButton || x.AlertType == (int)Storeya.Core.Entities.DashboardNotificationAlertType.FailedCharge)).ToArray();
            var counter = 0;
            var totalAmount = potentialPaid.Count();
            foreach (var alert in potentialPaid)
            {
                counter++;
                Console.WriteLine($"Progress {counter}/{totalAmount}");
                var tbSettings = TrafficBoostersDbHelper.GetSettings(alert.ShopID.Value);
                if (tbSettings != null)
                {
                    var lastPaymentDate = tbSettings.LastPaymentDate;
                    if (lastPaymentDate.HasValue && lastPaymentDate <= alert.ValidUntil && alert.InsertedAt < lastPaymentDate)
                    {
                        alert.Status = (int)Storeya.Core.Entities.DashboardNotificationStatus.Paid;
                        alert.UpdatedAt = DateTime.Now;
                        ConsoleAppHelper.WriteLog($"Dashboard alert with id={alert.ID} has been disabled because a payment has been made", alert.ShopID.Value);
                    }
                }
            }
            db.SaveChanges();
        }
        public static void UpdateAlertsStatusByTypeAndShopID(int shopID, int? type, int newStatus)
        {
            var db = DataHelper.GetStoreYaEntities();
            var alertsToChangeStatus = db.DashboardAlerts.Where(x => x.ShopID == shopID && x.AlertType == type).ToArray();
            foreach (var alert in alertsToChangeStatus)
            {
                if (alert.Status != (int)Storeya.Core.Entities.DashboardNotificationStatus.Active && newStatus != (int)Storeya.Core.Entities.DashboardNotificationStatus.Active)
                {
                    continue; //do not update not active status to another not active
                }
                if (alert.Status != newStatus)
                {
                    alert.Status = newStatus;
                    alert.UpdatedAt = DateTime.Now;
                }
            }
            db.SaveChanges();
        }

        public static bool CheckIfPaidLately(int shopID, int type, int subType, int minutesAgo = 60)
        {
            DateTime paidFrom = DateTime.Now.AddMinutes(-minutesAgo);
            var db = DataHelper.GetStoreYaEntities();
            return db.DashboardAlerts.Where(x => x.ShopID == shopID && x.AlertType == type && x.SubType == subType && x.Status == (int)Storeya.Core.Entities.DashboardNotificationStatus.Paid && x.UpdatedAt >= paidFrom).Any();
        }
        public static string GetCustomButtontextForAdditionalCodeField(string url, string name)
        {
            return $"Button URL:{url}_{name}";
        }
        public static bool IsButtonAdditionalCode(string alertAdditionalCode)
        {
            if (alertAdditionalCode == null || !alertAdditionalCode.StartsWith("Button URL:"))
            {
                return false;
            }
            return true;
        }
        public static string[] GetUrlAsFirstAndNameAsSecondFromAdditionalCode(string alertAdditionalCode)
        {
            if (!IsButtonAdditionalCode(alertAdditionalCode))
            {
                return null;
            }
            var textToGetButton = alertAdditionalCode.Replace("Button URL:", "");
            var parts = textToGetButton.Split('_');
            if (parts.Length > 0)
            {
                return parts;
            }
            else
            {
                return null;
            }
        }
    }
}
