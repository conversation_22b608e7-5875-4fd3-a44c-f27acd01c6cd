﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class DashboardAlertsNotifications

    //the format of the file should be:
    //AlertTitle: title
    //AlertText: text
    //AlertType: type(int)
    //Status: days(int)
    //ValidUntilInDays: days(int)

    {
        public static AlertsNotificationsDTO ReadAlertDataFromTheFile(string filePath)
        {
            AlertsNotificationsDTO alertsNotificationsDTO = new AlertsNotificationsDTO();
            alertsNotificationsDTO.ShopIds = new List<int>();
            string[] lines = File.ReadAllLines(filePath);
            StringBuilder sbFailes = new StringBuilder();
            foreach (string line in lines)
            {
                if (line.Contains("AlertTitle: "))
                {
                    alertsNotificationsDTO.AlertTitle = line.Replace("AlertTitle: ", "");
                    Console.WriteLine($"Title {alertsNotificationsDTO.AlertTitle}");
                    continue;
                }
                else if (line.Contains("AlertText: "))
                {
                    alertsNotificationsDTO.AlertText = line.Replace("AlertText: ", "");
                    Console.WriteLine($"AlertText {alertsNotificationsDTO.AlertText}");
                    continue;
                }
                else if (line.Contains("AlertType: "))
                {
                    if (int.TryParse(line.Replace("AlertType: ", ""), out int type))
                    {
                        alertsNotificationsDTO.AlertType = type;
                        Console.WriteLine($"AlertType {alertsNotificationsDTO.AlertType}");
                    }
                    else
                    {
                        Console.WriteLine($"Error! Can't identify {line}");
                        return null;
                    }
                    continue;
                }
                //else if (line.Contains("Status: "))
                //{
                //    if (int.TryParse(line.Replace("Status: ", ""), out int status))
                //    {
                //        alertsNotificationsDTO.Status = status;
                //        Console.WriteLine($"Status {alertsNotificationsDTO.Status}");
                //    }
                //    else
                //    {
                //        Console.WriteLine($"Error! Can't identify {line}");
                //        return null;
                //    }
                //    continue;
                //}
                else if (line.Contains("ValidUntilInDays: "))
                {
                    if (int.TryParse(line.Replace("ValidUntilInDays: ", ""), out int days))
                    {
                        alertsNotificationsDTO.ValidUntilInDays = days;
                        Console.WriteLine($"ValidUntilInDays {alertsNotificationsDTO.ValidUntilInDays}");
                    }
                    else
                    {
                        Console.WriteLine($"Error! Can't identify {line}");
                        return null;
                    }
                    continue;
                }

                if (int.TryParse(line, out int number))
                {
                    alertsNotificationsDTO.ShopIds.Add(number);

                }
                else
                {
                    Console.WriteLine($"Can't identify line: {line}");
                    return null;
                }
            }
            return alertsNotificationsDTO;

        }
        public static void ReadAlertDataFromTextAndCreateAlerts(string filePath)
        {
            AlertsNotificationsDTO dto = ReadAlertDataFromTheFile(filePath);
            Console.WriteLine("Are you ok with such Subject, texts...? yes / no?");
            string answer = Console.ReadLine();
            if (answer != null && answer.ToLower() == "yes")
            {
                foreach (var shopId in dto.ShopIds)
                {
                    if (dto.ValidUntilInDays == null)
                    {
                        dto.ValidUntilInDays = 7;
                    }
                    Console.WriteLine($"Creating an alert for {shopId}");
                    DashboardAlertsHelper.CreateDashboardAlertIfItDoesNotExist(shopId, dto.AlertTitle, dto.AlertText, dto.AlertType, active: true, dto.ValidUntilInDays.Value);
                }
            }
            Console.WriteLine($"Finished!");
        }
    }
    public class AlertsNotificationsDTO
    {
        public List<int> ShopIds { get; set; }
        public string AlertTitle { get; set; }
        public string AlertText { get; set; }
        public int AlertType { get; set; }
        //public int Status { get; set; }
        public int? ValidUntilInDays { get; set; }
    }
}
