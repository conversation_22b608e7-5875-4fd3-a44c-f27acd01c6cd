﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;
using System.Text;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Infrastructure;
using static Storeya.Core.Models.ShopAttributes.Attributes;
using System.Data.Common;
using Storeya.Core.Models.OnPageAnalysis;
using System.Text.RegularExpressions;
using System.Web.UI.WebControls;
//using eBayAdapter.eBayFindingService;
//using Ez.Newsletter.MagentoApi;
//using PostmarkDotNet.Model;
//using Bugsnag.Payload;

namespace Storeya.Core.Helpers
{
    public static class DataHelper
    {
        private static StoreYaEntities _entities;

        public static void ReloadStoreYaEntities()
        {
            if (_entities != null)
            {
                try
                {
                    _entities.Database.Connection.Close();
                }
                catch
                {

                }
                _entities.Dispose();
            }
            _entities = new StoreYaEntities();
        }

        public static StoreYaEntities GetStoreYaEntities()
        {
            if (HttpContext.Current == null)
            {
                if (_entities == null)
                    _entities = new StoreYaEntities();
                return _entities;
            }

            ////The ObjectContext class is not thread safe.
            ////https://cgeers.wordpress.com/2009/02/21/entity-framework-objectcontext/#aspnet

            string objectContextKey = HttpContext.Current.GetHashCode().ToString("x");
            if (!HttpContext.Current.Items.Contains(objectContextKey))
            {
                HttpContext.Current.Items.Add(objectContextKey, new StoreYaEntities());
            }
            return HttpContext.Current.Items[objectContextKey] as StoreYaEntities;
        }

        #region not tested
        private static StoreYaEntities GetStoreYaEntitiesUNCOMMITTED()
        {
            string objectContextKey = HttpContext.Current.GetHashCode().ToString("UNCOMMITTED");
            if (!HttpContext.Current.Items.Contains(objectContextKey))
            {
                HttpContext.Current.Items.Add(objectContextKey, CreateStoreYaEntities());
            }
            return HttpContext.Current.Items[objectContextKey] as StoreYaEntities;
        }

        private static StoreYaEntities CreateStoreYaEntities()
        {
            StoreYaEntities db = new StoreYaEntities();

            db.Database.Connection.Open();
            var con = (SqlConnection)(System.Data.Common.DbConnection)db.Database.Connection;
            SqlCommand cmd = new SqlCommand("set transaction isolation level read uncommitted", con);
            cmd.ExecuteNonQuery();

            return db;
        }
        #endregion

        public static void RunStoredProc(string spName, object[] parameters)
        {
            //StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //db.Refresh(RefreshMode.StoreWins, db.Users);
            //db.Refresh(RefreshMode.StoreWins, db.Shops);
            using (StoreYaEntities db = new StoreYaEntities())
            {
                db.Database.ExecuteSqlCommand("exec " + spName + " {0}", parameters);
                db.SaveChanges();
            }
        }

        //public static List<ExternalApp> GetExternalAppsFromCache()
        //{
        //    List<ExternalApp> apps = CacheHelper.GetFromCache<List<ExternalApp>>("ExternalApps");

        //    if (apps == null || apps.Count == 0)
        //    {
        //        _entities = GetStoreYaEntities();
        //        List<ExternalApp> externalApps = _entities.ExternalApps.ToList();
        //        CacheHelper.SaveTocache("ExternalApps", externalApps, DateTime.Now.AddDays(1));
        //        apps = CacheHelper.GetFromCache<List<ExternalApp>>("ExternalApps");
        //    }

        //    return apps;
        //}

        //public static void RefreshExternalAppsInCache()
        //{
        //    List<ExternalApp> apps = CacheHelper.GetFromCache<List<ExternalApp>>("ExternalApps");
        //    if (apps != null)
        //    {
        //        CacheHelper.RemoveFromCache("ExternalApps");
        //    }
        //    _entities = GetStoreYaEntities();
        //    List<ExternalApp> externalApps = _entities.ExternalApps.ToList();
        //    CacheHelper.SaveTocache("ExternalApps", externalApps, DateTime.Now.AddDays(1));
        //}
        public static string ExecuteRawSQLToJson(string selectQuery)
        {
            using (var db = new StoreYaEntities())
            {
                using (var command = db.Database.Connection.CreateCommand())
                {
                    DataTable dataTable = new DataTable();
                    command.CommandText = selectQuery;
                    command.CommandType = CommandType.Text;
                    command.Connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        dataTable.Load(reader);
                        var result = dataTable.ToJson();
                        Console.WriteLine(result);
                        return result;
                    }
                }
            }
        }

        public static string GetRawHtmlTable(string reportName, string selectQuery, out int count, int top = 1000, bool addColumnsCheckox = false, bool addTotal = false)
        {

            return GetRawHtmlTable(reportName, selectQuery, out count, out int shopId, out bool sameShop, top, null, addColumnsCheckox, addTotal);
        }
        public static string GetRawHtmlTable(string reportName, string selectQuery, out int count, out int shopId, out bool sameShop, int top = 1000, string hostname = null, bool addColumnsCheckox = false, bool addTotal = false)
        {
            Dictionary<string, string> reports = new Dictionary<string, string>() { { reportName, selectQuery } };
            return GetRawHtmlTable(reports, out count, out shopId, out sameShop, out reportName, top, hostname, addColumnsCheckox, addTotal);
        }
        public static string GetRawHtmlTable(Dictionary<string, string> reports, out int count, out int shopId, out bool sameShop, out string reportName, int top = 1000, string hostname = null, bool addColumnsCheckox = true, bool addTotal = false)
        {
            count = 0;
            shopId = 0;
            ValidateSQL(reports);
            AddSelectTopIfItNecessary(reports, top);

            var db = GetStoreYaEntities();
            string results = string.Empty;
            DataTable dt = GetDatatable(reports, out reportName);
            //using (var command = db.Database.Connection.CreateCommand())
            //{
            //    DataTable dataTable = new DataTable();
            //    command.CommandText = selectQuery;
            //    command.CommandType = CommandType.Text;
            //    command.Connection.Open();
            //    using (var reader = command.ExecuteReader())
            //    {
            //        dataTable.Load(reader);
            //        count = dataTable.Rows.Count;
            //        reportName = TrimTrailingSpaces(reportName);
            //        results = ToHtmlTable(dataTable, reportName, out shopId, out sameShop, hostname, addColumnsCheckox);
            //    }
            //    command.Connection.Close();
            //}
            count = dt.Rows.Count;

            reportName = TrimTrailingSpaces(reportName);
            results = ToHtmlTable(dt, reportName, out shopId, out sameShop, hostname, addColumnsCheckox);
            if (addTotal)
            {
                results = ToHtmlTable(AddTotalRowToTop(dt), reportName, out shopId, out sameShop, hostname, false) + "<br/>" + results;
            }
            return results;
        }
        public static DataTable AddTotalRowToTop(DataTable table)
        {
            if (table == null || table.Rows.Count == 0)
                return new DataTable();

            DataTable dtTotal = new DataTable();
            dtTotal.Columns.Add("Totals", typeof(string));

            // Identify numeric columns
            var numericColumns = new List<(string Name, Type DataType, int Index)>();
            List<string> excludeColumnNames = new List<string>() { "shopid", "id", "status", "Totals" };
            for (int i = 0; i < table.Columns.Count; i++)
            {
                Type type = table.Columns[i].DataType;
                if (!table.Columns[i].ColumnName.ToLower().EndsWith("id") &&
                    !excludeColumnNames.Contains(table.Columns[i].ColumnName.ToLower()))
                {
                    if (type == typeof(int) || type == typeof(decimal) || type == typeof(double))
                    {
                        dtTotal.Columns.Add(table.Columns[i].ColumnName, type);
                        numericColumns.Add((table.Columns[i].ColumnName, type, i));
                    }
                }
            }

            // Add one row with total values
            DataRow totalRow = dtTotal.NewRow();
            totalRow["Totals"] = $"Rows Count: {table.Rows.Count} / <b>Sum:</b>";

            foreach (var col in numericColumns)
            {

                object sum = null;
                if (col.DataType == typeof(int))
                {
                    int total = 0;
                    foreach (DataRow row in table.Rows)
                        if (!row.IsNull(col.Index)) total += Convert.ToInt32(row[col.Index]);
                    sum = total;
                }
                else if (col.DataType == typeof(decimal))
                {
                    decimal total = 0;
                    foreach (DataRow row in table.Rows)
                        if (!row.IsNull(col.Index)) total += Convert.ToDecimal(row[col.Index]);
                    sum = total;
                }
                else if (col.DataType == typeof(double))
                {
                    double total = 0;
                    foreach (DataRow row in table.Rows)
                        if (!row.IsNull(col.Index)) total += Convert.ToDouble(row[col.Index]);
                    sum = total;
                }

                totalRow[col.Name] = sum ?? DBNull.Value;
            }

            dtTotal.Rows.Add(totalRow);
            return dtTotal;
        }


        public static bool HasResults(Dictionary<string, string> reports)
        {
            ValidateSQL(reports);
            DataTable dt = GetDatatable(reports, out string reportName);
            return HasResults(dt);
        }
        private static bool HasResults(DataTable dataTable)
        {
            return dataTable.Rows.Count > 0;
        }
        private static DataTable GetDatatable(Dictionary<string, string> reports, out string reportName)
        {
            DataTable t = null;
            var db = GetStoreYaEntities();
            List<DataTable> dataTables = new List<DataTable>();
            foreach (var report in reports)
            {
                string c = report.Value;
                using (var command = db.Database.Connection.CreateCommand())
                {
                    DataTable dataTable = new DataTable();
                    command.CommandText = c;
                    command.CommandType = CommandType.Text;
                    command.Connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        dataTable.Load(reader);
                        dataTables.Add(dataTable);

                    }
                    command.Connection.Close();
                }
            }
            if (reports.Count > 1)
            {
                t = ConcatenateTablesForDashboardReport(dataTables);
                reportName = "";
                foreach (var report in reports)
                {
                    reportName += report.Key + "+";
                }
                reportName = reportName.Substring(0, reportName.Length - 1);
            }
            else
            {
                t = dataTables[0];
                reportName = reports.ElementAt(0).Key;
            }
            return t;

        }

        private static void AddSelectTopIfItNecessary(Dictionary<string, string> reports, int top)
        {
            var keys = new List<string>(reports.Keys);
            foreach (var key in keys)
            {
                string selectQuery = reports[key];

                if (!selectQuery.ToLower().Contains("top("))
                {
                    if (selectQuery.ToLower().StartsWith("with"))
                    {
                        string keyword = "SELECT";
                        int lastIndex = selectQuery.LastIndexOf(keyword, StringComparison.OrdinalIgnoreCase);
                        string beforeLastSelect = selectQuery.Substring(0, lastIndex);
                        string selectQueryWithTop = beforeLastSelect + selectQuery.Substring(lastIndex, keyword.Length)
                 + $" TOP {top} " + selectQuery.Substring(lastIndex + keyword.Length);
                        reports[key] = selectQueryWithTop;
                    }
                    else
                    {
                        selectQuery = selectQuery.Substring(6);
                        reports[key] = string.Format("Select TOP({1}) {0}", selectQuery, top);
                    }
                }
            }
        }
        private static void ValidateSQL(Dictionary<string, string> reports)
        {
            List<string> notAllowed = new List<string>()
            {
                "delete ",
                "insert ",
                "truncate ",
                "drop ",
                "update "
            };
            foreach (var report in reports)
            {
                string selectQuery = report.Value;
                if (!selectQuery.ToLower().StartsWith("select"))
                {
                    if (selectQuery.ToLower().StartsWith("with"))
                    {
                        string first30Chars = selectQuery.Length > 30 ? selectQuery.Substring(0, 30) : selectQuery;
                        if (!first30Chars.ToLower().Contains("select"))
                        {
                            throw new Exception("Sql is not allowed, Only Select queries are allowd!");
                        }
                    }
                    else
                    {
                        throw new Exception("Sql is not allowed, Only Select queries are allowd!");
                    }
                }
                foreach (var item in notAllowed)
                {
                    if (selectQuery.ToLower().Contains(item))
                    {
                        throw new Exception("Sql is not allowed, Only Select queries are allowd!");
                    }
                }
            }
        }

        public static string TrimTrailingSpaces(string input)
        {
            int endIndex = input.Length - 1;

            while (endIndex >= 0 && input[endIndex] == ' ')
            {
                endIndex--;
            }

            return input.Substring(0, endIndex + 1);
        }

        private static string GetSimpleDataType(Type t)
        {

            if (t == typeof(string))
                return "string";
            if (t == typeof(int) || t == typeof(long) || t == typeof(short) || t == typeof(byte) || t == typeof(float) || t == typeof(double) || t == typeof(decimal))
            {
                return "number";
            }
            if (t == typeof(bool))
            {
                return "boolean";
            }
            if (t == typeof(DateTime))
            {
                return "date";
            }
            if (t == typeof(TimeSpan))
            {
                return "datetime";
            }
            return "string";
        }
        public static string ToHtmlTable(DataTable dt, string tableId, out int shopId, out bool sameShop, string hostname = null, bool addColumnsCheckox = false)
        {
            if (string.IsNullOrEmpty(hostname))
            {
                hostname = "bo.storeya.com";
            }
            shopId = 0;
            sameShop = false;
            HashSet<int> shopsIds = new HashSet<int>();
            if (dt.Rows.Count == 0) return ""; // enter code here

            StringBuilder builder = new StringBuilder();
            builder.Append("<table border='1px' cellpadding='5' cellspacing='0'id='" + tableId + "'");
            builder.Append("style='border: solid 1px Silver; font-size: x-small;'>");
            builder.Append("<thead><tr align='left' valign='top'>");
            foreach (DataColumn c in dt.Columns)
            {
                if (c.ColumnName.StartsWith("__"))
                {
                    continue;
                }
                builder.Append("<th align='left' valign='top' data-type='" + GetSimpleDataType(c.DataType) + "'><b>");
                if (addColumnsCheckox)
                {
                    builder.Append("<input type='checkbox' id='" + c.ColumnName + "' name='" + c.ColumnName + "' value='0' checked />");
                }
                builder.Append(c.ColumnName);
                builder.Append("</b></th>");
                ////'string', 'number', 'boolean', 'date', 'datetime'
                //builder.Append("<th align='left' valign='top' data-type='" + GetSimpleDataType(c.DataType) + "'><b>");
                //builder.Append(c.ColumnName);
                //builder.Append("</b></th>");
            }
            builder.Append("</tr></thead><tbody>");
            string dataChecked = "";
            if (addColumnsCheckox)
            {
                dataChecked = "data-checked='true'";
            }
            foreach (DataRow r in dt.Rows)
            {
                string row = "";
                string isDisabled = "";
                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    if (dt.Columns[i].ColumnName.StartsWith("__"))
                    {
                        continue;
                    }
                    string graphDateType = GetSimpleDataType(dt.Columns[i].DataType);
                    row = $"{row}<td align='left' valign='top' data-type='" + graphDateType + "' " + dataChecked + " >";

                    if (dt.Columns[i].ColumnName.ToLower() == "shopid" && r[dt.Columns[i].ColumnName].ToString() != "")
                    {
                        shopId = int.Parse(r[dt.Columns[i].ColumnName].ToString());
                        if (!shopsIds.Contains(shopId))
                        {
                            shopsIds.Add(shopId);
                        }
                        //builder.Append(r[dt.Columns[i].ColumnName]);
                        row = $"{row}<a target=\"_blank\" href=\"https://{hostname}/shop/details/{r[dt.Columns[i].ColumnName]}\" >{r[dt.Columns[i].ColumnName]}</a>";
                    }
                    else if (dt.Columns[i].ColumnName.ToLower() == "shopidadmin")
                    {
                        row = $"{row}<a target=\"_blank\" href=\"https://{hostname}/shop/details/{r[dt.Columns[i].ColumnName]}\" >{r[dt.Columns[i].ColumnName]}</a>";
                    }
                    else if (dt.Columns[i].ColumnName.ToLower() == "userid")
                    {
                        string userId = r[dt.Columns[i].ColumnName].ToString();
                        row = $"{row}<a target=\"_blank\" href=\"https://{hostname}/shop/UserShops?userId={r[dt.Columns[i].ColumnName]}\" >{r[dt.Columns[i].ColumnName]}</a>"; // users/Shops?userId=
                    }
                    else if (dt.Columns[i].ColumnName.ToLower() == "isdisabled")
                    {
                        if (r[dt.Columns[i].ColumnName].ToString() == "1")
                        {
                            isDisabled = "style=\"background-color:#ebd7da;\"";
                        }
                        row = $"{row}{r[dt.Columns[i].ColumnName]}";
                    }
                    else if (dt.Columns[i].ColumnName.ToLower() == "fbprofileid")
                    {
                        if (r[dt.Columns[i].ColumnName].ToString() == "-1")
                        {
                            isDisabled = "style=\"background-color:#ebd7da;\"";
                        }
                        row = $"{row}{r[dt.Columns[i].ColumnName]}";
                    }
                    else
                    {
                        if (graphDateType == "date")
                        {
                            row = $"{row}{r[dt.Columns[i].ColumnName]:yyyy-MM-dd}";
                        }
                        else if (graphDateType == "datetime")
                        {
                            row = $"{row}{r[dt.Columns[i].ColumnName]:yyyy-MM-dd HH:mm:ss}";
                        }
                        else
                        {
                            row = $"{row}{r[dt.Columns[i].ColumnName]}";
                        }

                    }
                    row = $"{row}</td>";
                }
                builder.Append($"<tr {isDisabled} align='left' valign='top'>{row}</tr>");
            }
            builder.Append("</tbody></table>");
            if (shopsIds.Count == 1)
            {
                sameShop = true;
            }
            return builder.ToString();
        }
        //public static string GetSqlString(IQueryable quiery)
        //{
        //    return ((ObjectQuery)quiery).ToTraceString();
        //}

        public static string CheckDatabaseTables(StoreYaEntities db, bool sendEmail = true)
        {
            StringBuilder result = new StringBuilder();
            StringBuilder resultForEmail = new StringBuilder();
            var metadata = ((IObjectContextAdapter)db).ObjectContext.MetadataWorkspace;
            var tables = metadata.GetItemCollection(DataSpace.SSpace).Where(x => x.BuiltInTypeKind == BuiltInTypeKind.EntityType).ToList();
            foreach (var table in tables)
            {
                string tableName = ((System.Data.Entity.Core.Metadata.Edm.EdmType)table).Name;
                //Console.WriteLine($"{tableName} checking...");
                //var columnsEntity1 = ((System.Data.Entity.Core.Metadata.Edm.EntityType)table).Properties.ToList();
                List<DataFields> columnsEntity = ((System.Data.Entity.Core.Metadata.Edm.EntityType)table).Properties.Select(x => new DataFields { Name = x.Name, DataType = x.PrimitiveType.Name.ToString(), MaxLength = x.MaxLength }).ToList();
                List<DataFields> columnsTable = GetColumnsFromDataBase(db, tableName, out string err);
                if (err != null)
                {
                    result.AppendLine(err);
                    result.Append("");
                    resultForEmail.Append(err + "<br/>");
                }
                try
                {
                    string errors = GetCompareColumnsInDataAndEntity(columnsEntity, columnsTable);
                    if (errors != null)
                    {
                        result.AppendLine($"In the table {tableName} " + errors);
                        result.Append("");
                        resultForEmail.Append($"In the table {tableName} {errors}<br/>");
                    }
                }
                catch (Exception ex)
                {
                    string error = $"Tabel: {tableName}" + ex.Message;
                    result.AppendLine(error);
                    result.Append("");
                    resultForEmail.Append(error + "<br/>");
                }
            }
            if (result.Length > 0)
            {
                if (sendEmail)
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "There are problems with the database", resultForEmail.ToString());
                    return result.ToString();
                }
                else
                {
                    return resultForEmail.ToString();
                }

            }
            else
            {
                return null;
            }
        }

        private static List<DataFields> GetColumnsFromDataBase(StoreYaEntities db, string tableName, out string error)
        {
            string request = $"select sc.name,typ.name as Type,sc.max_length,sc.system_type_id from sys.columns sc join sys.tables st ON sc.object_id=st.object_id and st.name='{tableName}' join sys.types typ ON sc.user_type_id=typ.user_type_id";
            List<DataFields> dataFields = new List<DataFields>();
            try
            {
                List<object[]> rows = GetRowsArrayFromDataBase(db, request);
                foreach (var row in rows)
                {
                    dataFields.Add(DataFields.GetDataFieldFromRow(row));
                }
                error = null;
                return dataFields;
            }
            catch (Exception ex)
            {
                error = ex.Message;
            }
            return null;

        }

        private static List<object[]> GetRowsArrayFromDataBase(StoreYaEntities db, string request)
        {
            using (var command = db.Database.Connection.CreateCommand())
            {
                DataTable dataTable = new DataTable();
                command.CommandType = CommandType.Text;
                command.Connection.Open();
                command.CommandText = request;
                var reader = command.ExecuteReader();
                dataTable.Load(reader);
                command.Connection.Close();
                return dataTable.Rows.Cast<DataRow>().Select(x => x.ItemArray).ToList();
            }
        }

        private static string GetCompareColumnsInDataAndEntity(List<DataFields> columnsEntity, List<DataFields> columnsData)
        {
            string result = "";
            int countEntity = columnsEntity.Count();
            int countData = columnsData.Count();
            List<DataFields> firstTable;
            List<DataFields> secondTable;
            string location;
            string comparisonLocation;
            string theColumnNamesAreDifferentOrAColumnIsMissing = "";
            if (countEntity > countData || countEntity == countData)
            {
                location = "SQL table";
                comparisonLocation = "entities";
                firstTable = columnsEntity;
                secondTable = columnsData;
            }
            else
            {
                location = "entities";
                comparisonLocation = "SQL table";
                firstTable = columnsData;
                secondTable = columnsEntity;
            }
            foreach (var column in firstTable)
            {
                bool bothDictionaryHaveKey = true;
                var columnFromSecondTable = secondTable.Where(x => x.Name == column.Name).SingleOrDefault();
                if (columnFromSecondTable == null)
                {
                    theColumnNamesAreDifferentOrAColumnIsMissing += $" {column.Name};";
                    bothDictionaryHaveKey = false;
                }

                if (bothDictionaryHaveKey)
                {
                    if (column.DataType != columnFromSecondTable.DataType)
                    {
                        result += $"In column {column.Name} dataTypes are different {location}:{columnFromSecondTable.DataType} {comparisonLocation}:{column.DataType}. ";
                    }
                    if (column.DataType.Contains("char"))
                    {
                        if (column.MaxLength != columnFromSecondTable.MaxLength && column.MaxLength <= 100000 && columnFromSecondTable.MaxLength <= 100000)
                        {
                            result += $"In column {column.Name} lengths are different {location}:{columnFromSecondTable.MaxLength} {comparisonLocation}:{column.MaxLength}. ";
                        }
                    }
                }
            }

            if (theColumnNamesAreDifferentOrAColumnIsMissing != "")
            {
                result += $"column names are different or column is missing:{theColumnNamesAreDifferentOrAColumnIsMissing}. Where not found-{location}.";
            }

            if (result == "")
            {
                result = null;
            }
            return result;



        }
        private static string GetSingularName(string plural)
        {
            string singular = null;
            int length = plural.Length;
            if (plural[length - 1] == 's')
            {
                if (plural[length - 3] == 'i' && plural[length - 2] == 'e')
                {
                    char[] charPlural = { 'i', 'e', 's' };
                    plural = plural.TrimEnd(charPlural);
                    singular = plural + "y";
                }
                else if (plural[length - 2] == 'e' && plural[length - 3] != 'r' && plural[length - 3] != 'u' && plural[length - 3] != 'g' && plural[length - 3] != 'c' && plural[length - 3] != 'p')
                {
                    char[] charPlural = { 'e', 's' };
                    singular = plural.TrimEnd(charPlural);
                }
                else
                {
                    singular = plural.TrimEnd('s');
                }
            }
            else
            {
                singular = plural;
            }
            return singular;

        }

        //public static void TestRequest()
        //{
        //    var db = DataHelper.GetStoreYaEntities();
        //    var reportName = "name";
        //    int count = 0;
        //    string results = null;
        //    List<string> com = new List<string>()
        //    {
        //        "select TOP 3 InsertedAt, shopid from TrafficBoosters order by Id desc",
        //        "select TOP 4 InsertedAt, contractPrice from PlimusIpnCalls order by Id desc"

        //    };
        //    List<DataTable> dataTables = new List<DataTable>();
        //    foreach (var c in com)
        //    {
        //        using (var command = db.Database.Connection.CreateCommand())
        //        {
        //            DataTable dataTable = new DataTable();
        //            command.CommandText = c;
        //            command.CommandType = CommandType.Text;
        //            command.Connection.Open();
        //            using (var reader = command.ExecuteReader())
        //            {
        //                dataTable.Load(reader);
        //                dataTables.Add(dataTable);

        //            }
        //            command.Connection.Close();
        //        }
        //    }
        //    DataTable t = ConcatenateTablesForDashboardReport(dataTables);
        //}
        public static DataTable ConcatenateTablesForDashboardReport(List<DataTable> dataTables) //Concatenation
        {
            DataTable dt = new DataTable();
            List<DataColumn> dataColumns = new List<DataColumn>();
            bool firstTable = true;
            foreach (var dataTable in dataTables)
            {
                AddColumnsFromDataTable(dataTable, dataColumns, firstTable);
                if (dataColumns == null)
                {
                    return null;
                }
                firstTable = false;
            }
            CheckDataColumnsNames(dataColumns);
            AddDataColumnsToDataTable(dt, dataColumns);
            AddDataRowsFromDataTables(dt, dataTables);
            return dt;
        }

        private static void CheckDataColumnsNames(List<DataColumn> dataColumns)
        {
            List<string> names = dataColumns.Select(x => x.ColumnName).ToList();
            var notUniqueNames = "";
            var listOfCheckedNames = new List<string>();
            foreach (var name in names)
            {
                if (listOfCheckedNames.Contains(name))
                {
                    continue;
                }
                if (names.Where(x => x == name).Count() > 1)
                {
                    notUniqueNames += name + "; ";
                }
                listOfCheckedNames.Add(name);
            }
            if (notUniqueNames.Length > 0)
            {
                throw new Exception($"Names of the columns should be unique. Not unique columns names: {notUniqueNames}");
            }
        }

        private static void AddDataColumnsToDataTable(DataTable dt, List<DataColumn> dataColumns)
        {
            foreach (var c in dataColumns)
            {
                dt.Columns.Add(c.ColumnName, c.DataType);
            }
        }

        private static void AddDataRowsFromDataTables(DataTable dt, List<DataTable> dataTables)
        {
            Dictionary<string, List<object>> keyValuePairs = GetMainTableObjectsDictionary(dataTables);
            dt.Rows.Clear();
            foreach (var keyValue in keyValuePairs)
            {
                object[] line = new object[] { keyValue.Key }.Concat(keyValue.Value).ToArray();
                dt.LoadDataRow(line, fAcceptChanges: false);
                //dt.AcceptChanges();
            }

        }

        private static Dictionary<string, List<object>> GetMainTableObjectsDictionary(List<DataTable> dataTables)
        {
            Dictionary<string, List<object>> mainTable = null;
            foreach (var dataTable in dataTables)
            {
                Dictionary<string, List<object>> rowsFromTable = GetRowsDictionaryFromDataTable(dataTable);
                if (mainTable == null)
                {
                    mainTable = rowsFromTable;
                }
                else
                {
                    AddRowsToMainTable(mainTable, rowsFromTable);
                }
            }
            return mainTable;
        }

        private static void AddRowsToMainTable(Dictionary<string, List<object>> mainTable, Dictionary<string, List<object>> rowsFromTable)
        {
            var valuesCount = rowsFromTable.Values.FirstOrDefault().Count();
            List<object> emptyList = new List<object>();
            for (var i = 0; i < valuesCount; i++)
            {
                emptyList.Add(0);
            }
            var mainTableColumnCount = mainTable.Values.FirstOrDefault().Count();
            foreach (var row in rowsFromTable)
            {
                if (mainTable.ContainsKey(row.Key))
                {
                    mainTable[row.Key].AddRange(row.Value);
                }
            }
            foreach (var row1 in mainTable)
            {
                if (row1.Value.Count == mainTableColumnCount)
                {
                    mainTable[row1.Key].AddRange(emptyList);
                }
            }
        }

        private static Dictionary<string, List<object>> GetRowsDictionaryFromDataTable(DataTable dataTable)
        {
            Dictionary<string, List<object>> keyValuePairs = new Dictionary<string, List<object>>();
            var rowsCount = dataTable.Rows.Count;
            for (int i = 0; i < rowsCount; i++)
            {
                var row = dataTable.Rows[i];
                string key = null;
                List<object> values = new List<object>();
                var elementCounter = 0;
                foreach (var item in row.ItemArray)
                {
                    if (elementCounter == 0)
                    {
                        key = item.ToString();
                    }
                    else
                    {
                        values.Add(item);
                    }
                    elementCounter++;
                }
                keyValuePairs.Add(key, values);
            }
            return keyValuePairs;
        }

        //private static void GetRowsWithFixedDateTime(DataTable dataTable)
        //{
        //    List<DataRow> rows = new List<DataRow>();
        //    var amount = dataTable.Rows.Count;
        //    for (int i = 0; i < amount; i++)
        //    {
        //        rows.Add((DataRow)dataTable.Rows[i]);
        //       // dataTable.Rows[i].ItemArray[0] = DateTime.Parse(dataTable.Rows[i].ItemArray[0].ToString()).Date;
        //    }
        //}


        private static void AddColumnsFromDataTable(DataTable dataTable, List<DataColumn> dataColumns, bool firstTable)
        {

            List<DataColumn> dtCols = GetDataColumnsFromDataTable(dataTable, getAll: false, firstTable);
            if (dtCols == null)
            {
                dataColumns = null;
            }
            else
            {
                dataColumns.AddRange(dtCols);
            }
        }
        private static List<DataColumn> GetDataColumnsFromDataTable(DataTable dataTable, bool getAll = true, bool getFirstColumnAsWell = true)
        {
            List<DataColumn> dataColumns = new List<DataColumn>();
            var amount = dataTable.Columns.Count;
            for (int i = 0; i < amount; i++)
            {
                var value = dataTable.Columns[i];
                if (getAll)
                {
                    dataColumns.Add(value);
                }
                else
                {
                    //if (i == 0)
                    //{
                    //    var name= value.ColumnName.ToLower();
                    //    if (!value.ColumnName.Contains("at")) //(value!= typeof(DateTime))
                    //    {
                    //        return null;
                    //    }
                    //}
                    if (i == 0 && getFirstColumnAsWell)
                    {
                        dataColumns.Add(value);
                    }
                    else if (i != 0)
                    {
                        dataColumns.Add(value);
                    }
                }
            }
            return dataColumns;
        }
    }

    public class DataFields
    {
        public string Name { get; set; }
        public int? MaxLength { get; set; }
        public string DataType { get; set; }
        private static readonly Dictionary<int, string> typeMappings = new Dictionary<int, string>
        {
          { 34, "System.Byte[]" },
          { 35, "System.String" },
          { 36, "System.Guid" },
          { 48, "System.Byte" },
          { 52, "System.Int16" },
          { 56, "System.Int32" },
          { 58, "System.DateTime" },
          { 59, "System.DateTime" },
          { 60, "System.DateTime" },
          { 61, "System.DateTime" },
          { 62, "System.Double" },
          { 98, "System.Int16" },
          { 99, "System.Int32" },
          { 104, "System.Boolean" },
          { 106, "System.Decimal" },
          { 122, "System.DateTime" },
          { 127, "System.Int64" },
          { 128, "System.Object" },
          { 129, "System.String" },
          { 130, "System.String" },
          { 165, "System.Byte[]" },
          { 167, "System.String" },
          { 173, "System.DateTime" },
          { 175, "System.String" },
          { 189, "System.Decimal" },
          { 231, "System.String" },
          { 239, "System.String" }
        };
        public static DataFields GetDataFieldFromRow(object[] row)
        {
            try
            {
                DataFields dataFields = new DataFields();
                dataFields.Name = row[0].ToString();
                //int typeID = Convert.ToInt16(row[1].ToString());
                int length = Convert.ToInt32(row[2].ToString());
                dataFields.DataType = row[1].ToString();
                if (dataFields.DataType.Contains("varchar") && length == -1)
                {
                    dataFields.DataType += "(max)";
                }
                dataFields.MaxLength = GetMaxLengthInSymbolsCount(row[1].ToString(), length);
                return dataFields;
            }
            catch
            {
                return null;
            }


        }

        private static int GetMaxLengthInSymbolsCount(string dataType, int length)
        {
            int divider = IsNvarchar(dataType) ? 2 : 1;
            return length / divider;
        }

        private static string ConvertFromTypeIDToDataType(int typeID)
        {
            return typeMappings[typeID];
        }
        private static bool IsNvarchar(string type)
        {
            if (type == "nvarchar" || type == "nchar")
            {
                return true;
            }
            return false;
        }
        private static bool IsNvarchar(int typeID)
        {
            switch (typeID)
            {
                case 129:
                case 231:
                case 239:
                    return true;
                default:
                    return false;
            }
        }
    }

}