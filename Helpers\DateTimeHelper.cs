﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Helpers
{
    public static class DateTimeHelper
    {
        public static DateTime GetNextPaymentDay(DateTime? firstBillingDate, DateTime? checkDate = null)
        {
            checkDate = (checkDate == null) ? DateTime.Today.Date : checkDate;

            if (firstBillingDate == checkDate) { return ((DateTime)firstBillingDate).AddMonths(1); }
            DateTime nextBillingDate = (DateTime)firstBillingDate;
            while (nextBillingDate < checkDate)
            {
                nextBillingDate = nextBillingDate.AddMonths(1);
            }
            return nextBillingDate;
        }

        public static DateTime EndOfDay(this DateTime date)
        {
            return new DateTime(date.Year, date.Month, date.Day, 23, 59, 59, 999);
            //return date.Date.AddDays(1).AddTicks(-1);
        }

        public static DateTime StartOfDay(this DateTime date)
        {
            return new DateTime(date.Year, date.Month, date.Day, 0, 0, 0, 0);
        }

        public static int MonthsPass(DateTime from, DateTime to)
        {
            return MonthDifference(to, from) - 1;
        }
        public static int MonthDifference(DateTime lValue, DateTime rValue)
        {
            return (lValue.Month - rValue.Month) + 12 * (lValue.Year - rValue.Year);
        }

        public static int ToUnixDatetime(this DateTime datetime)
        {
            return (Int32)(datetime.Subtract(new DateTime(1970, 1, 1))).TotalSeconds;
        }
        public static string ChangeDateTimeFormat(this string datetime,string newFormat)
        {
            if (DateTime.TryParse(datetime, out DateTime result))
            { 
                return result.ToString(newFormat);
            }
            return datetime;
        }
        
        public static DateTime FromUnixDatetime(long unixTimeStamp)
        {
            // Unix timestamp is seconds past epoch
            System.DateTime dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc);
            dtDateTime = dtDateTime.AddSeconds(unixTimeStamp).ToLocalTime();
            return dtDateTime;
        }

        public static Dictionary<string, DateTime> GetYearsMonths(DateTime from, DateTime to)
        {
            TimeSpan ts = to - from ;
            Dictionary<string, DateTime> data = new Dictionary<string, DateTime>();
            if (ts.Days == 0)
            {
                string key = $"{from.Year}-{from.Month}";
                if (!data.ContainsKey(key))
                {
                    data.Add(key, new DateTime(from.Year, from.Month, 1));
                }
            }
            for (int i = 0; i < ts.Days; i++)
            {
                string key = $"{from.Year}-{from.Month}";
                if (!data.ContainsKey(key))
                {
                    data.Add(key, new DateTime(from.Year, from.Month, 1));
                }
                from = from.AddDays(1);
            }
            return data;
        }

    }

}
