﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net.Mail;
using System.IO;
using System.Net.Mime;
using Storeya.Core.Models.Account;
using Storeya.Core.Models.EmailProviders;
using Storeya.Core.Entities;
using Storeya.Core.Models.TrafficBoosterModels;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using System.Net.PeerToPeer;
using System.Text;
using Storeya.Core.Models.Payments;
using Storeya.Core.Models.TrafficBoosterModels.Upgrades;
using Storeya.Core.Models;

namespace Storeya.Core.Helpers
{
    public class EmailHelper
    {
        public const string DEV_RC_EMAIL = "<EMAIL>";
        public const string DEV_EMAIL = "<EMAIL>";
        public const string DEV_FB_EMAIL = "<EMAIL>";
        public const string SUPPORT_EMAIL = "<EMAIL>";
        public const string SALES_EMAIL = "<EMAIL>";
        public const string DEV_Dan_EMAIL = "<EMAIL>";
        public const string DEV_FB_R_EMAIL = "<EMAIL>,<EMAIL>";
        public static void SendTextEmail(string toAddress, string subject, string body)
        {
            SendEmail(toAddress, subject, body, null, null, false);
        }

        public static bool IsBacklistedForEmails(User user)
        {
            if (user != null)
            {
                //if (user.UserType == (int)UserTypes.GrowthHero)
                //{
                //    return true;
                //}

                if (user.Email != null
                    && (user.Email.Contains("@shopify.com") ||
                        user.Email.Contains("testing")))
                {
                    return true;
                }
            }
            return false;
        }
        public static bool? SendEmailNoException(string toAddress, string subject, string body, MailAddress fromAddress = null, List<Attachment> attachments = null, bool isHtml = true, string tag = null, DateTime? activityDateTime = null, string cc = null, int shopId = 0)
        {
            try
            {
                return SendEmail(toAddress, subject, body, fromAddress, attachments, isHtml, tag, activityDateTime, cc);
            }
            catch (Exception ex)
            {

                ConsoleAppHelper.WriteErrorWithDB($"{subject}", new Exception(body, ex), shopId);
            }
            return false;
        }
        public static bool? SendEmail(string toAddress, string subject, string body, MailAddress fromAddress = null, List<Attachment> attachments = null, bool isHtml = true, string tag = null, DateTime? activityDateTime = null, string cc = null)
        {
            bool? sent = null;
            bool overrideEmail = !string.IsNullOrEmpty(ConfigHelper.GetValue("overrideEmail"));
            string commandLine = Environment.CommandLine;
            if (overrideEmail || toAddress.ToLower() == DEV_EMAIL || (toAddress.ToLower().Contains("dev+") && toAddress.ToLower().Contains("@storeya.com")))
            {
                if (commandLine.ToLower().Contains("w3wp.exe"))
                {

                    body = $"{body}<br/><p style=\"font-size:10px;color:#999;width:100%;text-align:left\"><b>Url:</b>{HttpHelper.GetPathAndQueryl()}</p>";
                }
                else
                {
                    body = $"{body}<br/><p style=\"font-size:10px;color:#999;width:100%;text-align:left\"><b>CommandLine:</b>{commandLine}</p>";
                }

            }
            var message = new MailMessage()
            {
                Subject = subject,
                Body = body,
                IsBodyHtml = isHtml
            };

            if (attachments != null && attachments.Count > 0)
            {
                foreach (var attachment in attachments)
                {
                    message.Attachments.Add(attachment);
                }
            }

            if (toAddress != null)
            {
                if (toAddress.Contains("DONT_SEND_") && (!string.IsNullOrEmpty(ConfigHelper.GetValue("supportEmail"))))
                {

                    MailAddress to = new MailAddress(ConfigHelper.GetValue("supportEmail"), "Instead of " + toAddress);
                    message.To.Add(to);

                }
                else if (overrideEmail)
                {
                    MailAddress to = new MailAddress(ConfigHelper.GetValue("overrideEmail"), "Instead of " + toAddress);
                    message.To.Add(to);
                    cc = null;
                }
                else
                {
                    message.To.Add(toAddress);
                }

                if (!string.IsNullOrEmpty(cc))
                {
                    message.CC.Add(cc);
                }
            }
            else
            {
                Log4NetLogger.Info(string.Format("Can not send email: toAddress = null. Subject: {0}", subject));
                return false;
            }


            if (ConfigHelper.GetBoolValue("Email_AddBccToArchive"))
            {
                message.Bcc.Add("<EMAIL>");
            }

            if (fromAddress != null)
            {
                message.From = fromAddress;
            }

            string emailProviders = ConfigHelper.GetValue("EmailProvider");
            string[] splitted = emailProviders.Split(',');
            Random random = new Random();
            string emailProvider = splitted[random.Next(splitted.Length)];

            if (emailProvider == "MailGun")
            {
                string toEmail = message.To[0].Address;
                if (string.IsNullOrEmpty(tag) && toEmail != null)
                {
                    if (toEmail == DEV_EMAIL)
                    {
                        tag = "ToDevEmail";
                    }
                    else if (toEmail.Contains("@storeya.com"))
                    {
                        tag = "ToStoreYa";
                    }
                }


                MailGunProvider mailGunProvider = new MailGunProvider();
                sent = mailGunProvider.SendEmail(message.To[0], fromAddress, message, tag, activityDateTime);
                if (fromAddress == null)
                {
                    message.From = new MailAddress("<EMAIL>");
                }
            }
            else if (emailProvider == "PostMark")
            {
                Postmark postMarkProvider = new Postmark();
                postMarkProvider.SendMessage(message.To[0], fromAddress, message, tag, activityDateTime);
            }
            else
            {
                var client = GetSmtpClient(message.To[0].Address, fromAddress);// new SmtpClient();
                client.EnableSsl = true;
                if (ConfigHelper.GetBoolValue("CancelEmailAsync"))
                {
                    client.Send(message);
                }
                else
                {
                    Object state = null;
                    client.SendAsync(message, state);
                }

            }
            if (!commandLine.ToLower().Contains("elmahmonitor.exe"))
            {
                AddEmailTrackingToData(message);
            }
            return sent;
        }
        public static int? GetFirstShopIDByEmailAdress(string email, out bool multipulShops, bool ignoreStoreYa = true)
        {
            multipulShops = false;
            bool check = true;
            if (ignoreStoreYa && email.ToLower().Contains("@storeya.com"))
            {
                check = false;
            }
            if (check)
            {
                var db = DataHelper.GetStoreYaEntities();
                var user = db.Users.Where(s => s.Email == email).ToList();
                if (user.Count == 0)
                {
                    user = db.Users.Where(s => s.Email2 == email).ToList();
                }
                if (user != null && user.Count() > 0)
                {
                    var userId = 0;
                    if (user.Any(x => x.FbProfileID > -1))
                    {
                        userId = user.Where(x => x.FbProfileID > -1).OrderByDescending(o => o.InsertedAt).First().ID;
                    }
                    else
                    {
                        userId = user.OrderByDescending(o => o.InsertedAt).First().ID;
                    }
                    List<Shop> shops = db.Shops.Where(s => s.UserID == userId).OrderByDescending(i => i.InsertedAt).ToList();
                    if (shops.Count == 0)
                    {
                        return null;
                    }
                    else
                    {
                        Shop shop = shops.First();
                        if (shops.Count > 1)
                        {
                            multipulShops = true;
                        }
                        int? shopID = null;
                        if (shop != null)
                        {
                            shopID = shop.ID;
                        }
                        return shopID;
                    }
                }
            }
            return null;
        }
        public static void AddEmailTrackingToData(MailMessage message)
        {
            try
            {
                bool devEmail = CheckIfEmailToDev(message.To.ToString().Replace("\"", "").Replace("<", "").Replace(">", ""));
                if (!devEmail)
                {
                    SimpleEmailDTO simpleEmailDTO = EmailTrackingHelper.GetSimpleEmailDTOFromMailMessage(message);
                    if (simpleEmailDTO.ShopID != null)
                    {
                        EmailTrackingHelper.CreateAndSaveEmailLogData(simpleEmailDTO);
                    }
                }
            }
            catch (Exception ex)
            {
                SendEmail(EmailHelper.DEV_EMAIL, "Failed to AddEmailTrackingToData.", $"Subject: {message.Subject}, toAddress {message.To}. {ex.ToString()}");
            }
        }
        public static bool CheckIfEmailToDev(string to)
        {
            bool devEmail = false;
            if (to.Contains("+"))
            {
                string emailUsersType = null;
                Regex regex = new Regex(@"\+(.*)@");
                var v = regex.Match(to);
                emailUsersType = "+" + v.Groups[1].ToString();
                to = to.Replace(emailUsersType, "");
            }
            if (to.ToLower().Contains("<EMAIL>"))
            {
                devEmail = true;
            }
            return devEmail;
        }
        public static string GetTemplateContent(string templateName, string layout, object model)
        {
            string contentTemplate = GetTemplateContent(templateName);
            contentTemplate = contentTemplate.FormatWith(model);

            string layoutContent = GetTemplateContent(layout);

            EmailLayoutModel m = new EmailLayoutModel() { Content = contentTemplate };
            string fullContent = layoutContent.FormatWith(m);

            return fullContent;
        }
        public static string GetTemplateContentFromResource(string templateName, string layout, object model)
        {
            string contentTemplate = GetTemplateContentFromResource(templateName);
            if (model != null)
            {
                contentTemplate = contentTemplate.FormatWith(model);
            }
            string layoutContent = "";
            try
            {
                layoutContent = GetTemplateContent(layout);
            }
            catch
            {

                layoutContent = GetTemplateContentFromResource(layout);
            }


            EmailLayoutModel m = new EmailLayoutModel() { Content = contentTemplate };
            string fullContent = layoutContent.FormatWith(m);

            return fullContent;
        }
        public static string GetTemplateContentFromResource(string templateName)
        {
            return Storeya.Core.Properties.Resources.ResourceManager.GetString(templateName, Storeya.Core.Properties.Resources.Culture);
        }
        public static string GetTemplateContent(string templateName)
        {
            string path = ConfigHelper.GetValue("emailTemplatesPath");
            if (string.IsNullOrEmpty(path))
            {
                //for web app get it from relative path
                path = HttpContext.Current.Server.MapPath("/EmailTemplates");
            }

            string fileName = templateName;
            if (!fileName.EndsWith(".html"))
            {
                fileName += ".txt";
            }
            string ePath = Path.Combine(path, fileName);
            string emailTempalte = File.ReadAllText(ePath);
            try
            {
                Log4NetLogger.InfoWithDB("Email Template : " + ePath, null, -66666);
            }
            catch
            {
            }
            return emailTempalte;
        }

        public static string GetTemplateContent_2(string templateName)
        {

            if (!string.IsNullOrEmpty(templateName))
            {
                templateName = templateName.Replace("\\EmailTemplates", "");

                string path = ConfigHelper.GetValue("emailTemplatesPath");
                if (string.IsNullOrEmpty(path))
                {
                    //for web app get it from relative path
                    path = HttpContext.Current.Server.MapPath("/EmailTemplates");
                }

                string file = path + templateName;
                string emailTempalte = File.ReadAllText(file);
                try
                {
                    Log4NetLogger.InfoWithDB("Email Template 2 :" + file, null, -66666);
                }
                catch
                {
                }
                //string emailTempalte = File.ReadAllText(templateName);
                return emailTempalte;
            }
            return null;
        }

        public static bool IsValidEmail(string email, bool checkFreeSpaces = false)
        {
            try
            {
                if (checkFreeSpaces)
                {
                    if (email.Trim().Contains(" "))
                    {
                        return false;
                    }
                }
                string emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";

                // Use Regex.IsMatch to validate the email format
                if (Regex.IsMatch(email, emailPattern, RegexOptions.IgnoreCase))
                {
                    var addr = new System.Net.Mail.MailAddress(email);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private static SmtpClient GetSmtpClient(string toAddress, MailAddress from = null)
        {
            if (toAddress == ConfigHelper.GetValue("supportEmail")
                //|| toAddress == ConfigHelper.GetValue("overrideEmail")
                || (from != null && !string.IsNullOrEmpty(from.Address) && from.Address.ToLower() == "<EMAIL>")
                )
            {
                //Use <NAME_EMAIL> email for internal emails
                SmtpClient mailer = new SmtpClient();
                mailer.Host = "smtp.gmail.com";
                mailer.Port = 587;
                mailer.Credentials = new System.Net.NetworkCredential("<EMAIL>", "no-reply1234_+");

                return mailer;
            }

            if (from != null && !string.IsNullOrEmpty(from.Address) && from.Address == ConfigHelper.GetValue("EmailFrom_Eyal"))
            {
                SmtpClient mailer = new SmtpClient("smtp.gmail.com", 587);
                mailer.Credentials = new System.Net.NetworkCredential(from.Address, ConfigHelper.GetValue("EmailFromCredentials_Eyal"));
                return mailer;
            }
            if (from != null && !string.IsNullOrEmpty(from.Address) && !string.IsNullOrEmpty(ConfigHelper.GetValue(from.Address.ToLower())))
            {
                SmtpClient mailer = new SmtpClient("smtp.gmail.com", 587);
                mailer.Credentials = new System.Net.NetworkCredential(from.Address, ConfigHelper.GetValue(from.Address.ToLower()));
                return mailer;
            }

            return new SmtpClient();
        }



        public static string AdWordsAccountLink(int shopID)
        {
            var s = "https://bo.storeya.com/go/awxccount?shopid=" + shopID + Environment.NewLine;
            var html = $"<a href=\"https://bo.storeya.com/go/awaccount?shopid={shopID}\">AdWords account</a>";
            return html;
        }



        public static string GetBoLinkHref(int shopID, string action = "Details", string highlightText = null)
        {
            var html = $"<a href=\"https://bo.storeya.com/Shop/{action}/{shopID}\" >{shopID}</a>";
            if (highlightText != null)
            {
                html = $"<a href=\"https://bo.storeya.com/Shop/{action}/{shopID}#:~:text={highlightText}\" >{shopID}</a>";
            }

            return html;
        }
        public static string GetBoLinkHrefAndAMEmail(int shopID, out string accountManager, out string accountManagerEmail, string action = "Details", string highlightText = null)
        {
            var html = GetBoLinkHref(shopID, action, highlightText);
            accountManager = null;
            accountManagerEmail = null;
            var db = DataHelper.GetStoreYaEntities();
            var bs = db.TbBigSpenders.Where(b => b.ShopID == shopID).SingleOrDefault();
            if (bs != null && bs.AssignedTo != null && bs.AssignedTo != (int)StoreYaEmployee.Unassigned)
            {
                StoreYaEmployee am = (StoreYaEmployee)bs.AssignedTo;
                accountManager = am.ToString();
                var user = db.Users.SingleOrDefault(u => u.ID == bs.AssignedTo);
                if (user != null)
                {
                    accountManagerEmail = user.Email;
                }
            }
            html = html + $" <b>{accountManager}</b>";

            var tb = TrafficBoostersDbHelper.GetSettings(shopID);
            if (tb != null && !string.IsNullOrEmpty(tb.MajorAccountChanges))
            {
                html += " Remarks:" + tb.MajorAccountChanges;
            }
            return html;
        }

        public static string GetBoLinkHrefAndAM(int shopID, out string accountManager, string action = "Details", string highlightText = null)
        {
            var html = GetBoLinkHref(shopID, action, highlightText);
            accountManager = null;
            var db = DataHelper.GetStoreYaEntities();
            var bs = db.TbBigSpenders.Where(b => b.ShopID == shopID).SingleOrDefault();
            if (bs != null && bs.AssignedTo != null && bs.AssignedTo != (int)StoreYaEmployee.Unassigned)
            {
                StoreYaEmployee am = (StoreYaEmployee)bs.AssignedTo;
                accountManager = am.ToString();
            }
            html = html + $" <b>{accountManager}</b>";

            var tb = TrafficBoostersDbHelper.GetSettings(shopID);
            if (tb != null && !string.IsNullOrEmpty(tb.MajorAccountChanges))
            {
                html += " Remarks:" + tb.MajorAccountChanges;
            }
            return html;
        }
        public static string GetBoLinkHrefAndAM(int shopID, string seperator = " ")
        {
            var html = GetBoLinkHref(shopID);


            var db = DataHelper.GetStoreYaEntities();
            var bs = db.TbBigSpenders.Where(b => b.ShopID == shopID).SingleOrDefault();
            if (bs != null && bs.AssignedTo != null && bs.AssignedTo != (int)StoreYaEmployee.Unassigned)
            {
                html += seperator + (StoreYaEmployee)bs.AssignedTo;
            }
            else
            {
                //if no AM check for comment
                var tb = TrafficBoostersDbHelper.GetSettings(shopID);
                if (tb != null && !string.IsNullOrEmpty(tb.MajorAccountChanges))
                {
                    html += seperator + "Remarks:" + tb.MajorAccountChanges;
                }
            }
            return html + seperator;
        }

        public static string GetBoLinkHrefAndAMandRemarks(int shopID)
        {

            var html = GetBoLinkHref(shopID);

            var db = DataHelper.GetStoreYaEntities();
            var bs = db.TbBigSpenders.Where(b => b.ShopID == shopID).SingleOrDefault();
            if (bs != null && bs.AssignedTo != null && bs.AssignedTo != (int)StoreYaEmployee.Unassigned)
            {
                html += " " + (StoreYaEmployee)bs.AssignedTo;
            }

            //if no AM check for comment
            var tb = TrafficBoostersDbHelper.GetSettings(shopID);
            if (tb != null && !string.IsNullOrEmpty(tb.MajorAccountChanges))
            {
                html += " " + "Remarks:" + tb.MajorAccountChanges;
            }

            return html;
        }
        public static string AdminLinkHref(int shopID)
        {
            var html = $"<a href=\"https://bo.storeya.com/Shop/Details/{shopID}\">{shopID}</a>";
            return html;
        }



        public static void SendEmailAboutUpgrade(string to, string name, string subject, string template, int shopID)
        {
            try
            {
                string emailTempalte = EmailHelper.GetTemplateContent(template);
                EmailHelper.SendEmail(to, subject, emailTempalte.FormatWith(new { Name = name }));
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to send email about TB Upgrade to email: {0}.", to), ex, shopID);

            }
        }
        public class EmailLayoutModel
        {
            public string Content { get; set; }
        }

        public static MailAddress GetUserNameAndEmailAdressForEmail(int shopID, string userName = null, string email = null)
        {
            return GetUserNameAndEmailsAdressesForEmail(shopID, out string secondEmail, userName, email);
        }
        public static MailAddress GetUserNameAndEmailsAdressesForEmail(int shopID, out string secondEmail, string userName = null, string email = null)
        {
            secondEmail = null;
            User user = null;
            var db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(x => x.ID == shopID).Single();
            if (userName == null && email == null)
            {
                user = db.Users.Where(x => x.ID == shop.UserID).Single();
            }
            else
            {
                user = new User() { Name = userName, Email = email };
            }
            var name = ExtractFirstName(user.Name, shop.Name);
            MailAddress address = new MailAddress(user.Email2 ?? user.Email, name);
            MailAddress address2 = new MailAddress(user.Email ?? user.Email2, name);
            if (address.Address != address2.Address)
            {
                secondEmail = address2.Address;
            }
            return address;
        }

        public static string GetNameFromIpnCall(int shopID, out string firstName)
        {
            firstName = null;
            var db = DataHelper.GetStoreYaEntities();
            var ipns = db.PlimusIpnCalls.Where(r => r.ShopID == shopID).FirstOrDefault();
            if (ipns != null)
            {
                firstName = (ipns.firstName == null && ipns.firstName == "") ? null : ipns.firstName.ToTitleCase();
                string lastName = (ipns.lastName == null && ipns.lastName == "") ? null : ipns.lastName.ToTitleCase();
                var name = firstName + " " + lastName;
                if (CheckNameValid(shopID, name))
                {
                    return name;
                }
                else
                {
                    firstName = null;
                    return null;
                }
            }
            return null;
        }
        public static bool CheckNameValid(int shopID, string name)
        {
            var db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(x => x.ID == shopID).Single();
            string nameFromShop = shop.Name;
            return CheckNameValid(name, nameFromShop);
        }

        public static bool CheckNameValid(string name, string nameFromShop = null)
        {
            if (nameFromShop != null)
            {
                bool userNameIsACompanyName = CheckIfUserNameIsShopName(name, nameFromShop);
                if (userNameIsACompanyName)
                {
                    return false;
                }
            }
            if (name == null
                || name.ToLower().Contains("wixuser") || name.Contains("@"))
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        public static bool CheckIfUserNameIsShopName(string userName, string shopName)
        {
            if (shopName == null || userName == null)
            {
                return false;
            }
            var splitted = userName.Split(' ');
            if (splitted != null && splitted.Contains("LTD"))
            {
                return true;
            }
            var wordsInName = userName.Split(' ');
            foreach (var word in wordsInName)
            {
                if (!shopName.Contains(word))
                {
                    return false;
                }
            }
            return true;
        }

        public static string ExtractFirstName(string userName, string shopName = null)
        {

            var name = "";
            string[] splitted = null;
            if (userName != null)
            {
                splitted = userName.Split(' ');
            }
            if (splitted != null && (splitted[0].ToLower() == "mr" || splitted[0].ToLower() == "mrs"
                || splitted[0].ToLower() == "ms" || splitted[0].ToLower() == "miss" || splitted[0].ToLower() == "dr"))
            {
                name = userName;
            }
            else if (!CheckNameValid(userName, shopName))
            {
                name = "There";
            }
            else if (splitted != null && splitted.Length > 0)
            {
                if (splitted[0].Length > 1)
                {
                    if (splitted[0].Length == 2)
                    {
                        name = splitted[0];
                    }
                    else
                    {
                        name = splitted[0].ToTitleCase();
                    }
                }
                else
                {
                    name = "There";
                }
            }
            else
            {
                name = "There";
            }
            return name;
        }

        public static string ExtractLastName(string fullName)
        {
            if (fullName != null)
            {
                string[] splitted = fullName.Split(' ');
                if (splitted.Length > 1)
                {
                    return splitted[splitted.Length - 1];
                }
            }
            return null;
        }
        public static string GetPaymentLink(int shopID, double recommendedBudget, int? agreeID = null, string textOftheLink = "this link")
        {
            string url = GetPaymentURL(shopID, recommendedBudget, agreeID);
            return $"<a href=\"{url}\">{textOftheLink}</a>";
        }
        public static string GetPaymentURL(int shopID, double recommendedBudget, int? agreeID = null)
        {
            AbstractPaymentAdapter abstractPaymentAdapter = new BlueSnapPaymentAdapter();
            PaymentDetails details = new PaymentDetails()
            {
                PlanID = PlanTypes.CustomPlan.GetHashCode(),
                AppID = AppTypes.TrafficBooster.GetHashCode(),
                ShopID = shopID,
                Method = 0,
                CustomPrice = recommendedBudget,
                AgreeID = agreeID,
            };
            return abstractPaymentAdapter.GetStoreYaChekoutNewPage(details, forcedStoreYaDomain: true);
        }
        public static Attachment CreateAttachement(string text)
        {
            MemoryStream memoryStream = new MemoryStream();
            byte[] contentAsBytes = Encoding.UTF8.GetBytes(text);
            memoryStream.Write(contentAsBytes, 0, contentAsBytes.Length);

            // Set the position to the beginning of the stream.
            memoryStream.Seek(0, SeekOrigin.Begin);

            // Create attachment
            ContentType contentType = new ContentType();
            contentType.MediaType = MediaTypeNames.Application.Octet;
            contentType.Name = "attachemtn.csv";
            Attachment attachment = new Attachment(memoryStream, contentType);
            return attachment;
        }
        public static string ReturnTextForEmailIfDashboardAlertExist(int shopId, int? idOfAddedAlertOnDashboard = null, int? idExistingNotification = null, int? alertType = null, string prediction = null)
        {
            string text = null;
            int? upgradesAlerts = idOfAddedAlertOnDashboard;
            if (idOfAddedAlertOnDashboard == null)
            {
                var alertsInLast30minutes = DashboardAlertsHelper.GetUpgradeDashboardAlertsForTheLatestPeriod(shopId, DateTime.Now.AddMinutes(-30));
                if (alertsInLast30minutes != null)
                {
                    upgradesAlerts = alertsInLast30minutes.Where(x => x.Status == (int)Storeya.Core.Entities.DashboardNotificationStatus.NotActive).Select(c => c.ID).SingleOrDefault();
                    upgradesAlerts = upgradesAlerts == 0 ? null : upgradesAlerts;
                }
            }
            if (idExistingNotification == null && idOfAddedAlertOnDashboard == null)
            {
                var activeAlerts = DashboardAlertsHelper.GetActiveUpgradesAndHolidaysDashboardAlertsForTheShop(shopId);
                if (activeAlerts != null && activeAlerts.Count() == 1)
                {
                    idExistingNotification = activeAlerts.First().ID;
                }
                else if (activeAlerts != null && activeAlerts.Count() > 1)
                {
                    idExistingNotification = -2; //-2 means more than one active notification
                }
            }

            string dashboardAlertLink = $"https://bo.storeya.com/Dashboard/EditDashboardAlerts?ID={upgradesAlerts}";
            if (alertType != null)
            {
                var toForNewEmaiLink = DateTime.Today.ToString("yyyy-MM-dd");
                dashboardAlertLink = $"https://bo.storeya.com/emails/UpgradeEmail?shopid={shopId}&to={toForNewEmaiLink}&budgetStatus={alertType}&hasAgreement=false{prediction}";
            }

            if (idExistingNotification != null)
            {
                if (idExistingNotification == -2)
                {
                    text = $"You already have more than one dashboard notification active. Check which alerts should be disabled <a href='https://bo.storeya.com/dashboard/DashboardAlerts?shopID={shopId}&status={(int)Storeya.Core.Entities.DashboardNotificationStatus.Active}'>LINK</a><br>";
                }
                else
                {
                    text = $"You already have an active dashboard notification for this shop <a href='https://bo.storeya.com/Dashboard/EditDashboardAlerts?ID={idExistingNotification}'>LINK</a>. Before adding a new alert (see the link below) please disable this one.<br>";
                }
            }
            if (upgradesAlerts != null)
            {
                text += $"CONSIDER TO PUBLISH DASHBOARD ALERT - <a href='{dashboardAlertLink}' target='_blank'>[LINK] <span class=\"c-deep-blue-500 mdi mdi-window-open\"></span></a><br>";
            }
            return text;
        }

    }

    public class EmailTemplateModel
    {
        public string Email { get; set; }
        public string Name { get; set; }
        public string Guide { get; set; }
        public EmailTemplateModel(User user)
        {
            SetData(user);
        }
        public EmailTemplateModel(int shopID)
        {
            var db = DataHelper.GetStoreYaEntities();
            User user = (from s in db.Shops
                         join u in db.Users
                                    on s.UserID equals u.ID
                         where s.ID == shopID
                         select u).SingleOrDefault();
            SetData(user);
        }
        private void SetData(User user)
        {
            Email = user.Email;
            Name = EmailHelper.ExtractFirstName(user.Name);
        }
    }
}