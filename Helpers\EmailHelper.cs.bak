﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net.Mail;
using System.IO;
using System.Net.Mime;
using Storeya.Core.Models.Account;
using Storeya.Core.Models.EmailProviders;
using Storeya.Core.Entities;
using Storeya.Core.Models.TrafficBoosterModels;
using System.Text.RegularExpressions;


namespace Storeya.Core.Helpers
{
    public class EmailHelper
    {
        public const string DEV_EMAIL = "<EMAIL>";
        public const string DEV_FB_EMAIL = "<EMAIL>";
        public const string SUPPORT_EMAIL = "<EMAIL>";
        public static void SendTextEmail(string toAddress, string subject, string body)
        {
            SendEmail(toAddress, subject, body, null, null, false);
        }

        public static bool IsBacklistedForEmails(User user)
        {
            if (user != null)
            {
                //if (user.UserType == (int)UserTypes.GrowthHero)
                //{
                //    return true;
                //}

                if (user.Email != null
                    && (user.Email.Contains("@shopify.com") ||
                        user.Email.Contains("testing")))
                {
                    return true;
                }
            }
            return false;
        }
        public static bool? SendEmail(string toAddress, string subject, string body, MailAddress fromAddress = null, List<Attachment> attachments = null, bool isHtml = true, string tag = null, DateTime? activityDateTime = null, string cc = null)
        {
            bool? sent = null;
            bool overrideEmail = !string.IsNullOrEmpty(ConfigHelper.GetValue("overrideEmail"));
            if (overrideEmail || toAddress.ToLower() == DEV_EMAIL || toAddress.ToLower() == DEV_FB_EMAIL)
            {
                string commandLine = Environment.CommandLine;
                if (!commandLine.Contains("w3wp.exe"))
                {
                    body = $"{body}<br/><p style=\"font-size:10px;color:#999;width:100%;text-align:left\"><b>CommandLine:</b>{commandLine}</p>";
                }
            }
            var message = new MailMessage()
            {
                Subject = subject,
                Body = body,
                IsBodyHtml = isHtml
            };

            if (attachments != null && attachments.Count > 0)
            {
                foreach (var attachment in attachments)
                {
                    message.Attachments.Add(attachment);
                }
            }

            if (toAddress != null)
            {
                if (toAddress.Contains("DONT_SEND_") && (!string.IsNullOrEmpty(ConfigHelper.GetValue("supportEmail"))))
                {

                    MailAddress to = new MailAddress(ConfigHelper.GetValue("supportEmail"), "Instead of " + toAddress);
                    message.To.Add(to);

                }
                else if (overrideEmail)
                {
                    MailAddress to = new MailAddress(ConfigHelper.GetValue("overrideEmail"), "Instead of " + toAddress);
                    message.To.Add(to);
                    cc = null;
                }
                else
                {
                    message.To.Add(toAddress);
                }

                if (!string.IsNullOrEmpty(cc))
                {
                    message.CC.Add(cc);
                }
            }
            else
            {
                Log4NetLogger.Info(string.Format("Can not send email: toAddress = null. Subject: {0}", subject));
                return false;
            }


            if (ConfigHelper.GetBoolValue("Email_AddBccToArchive"))
            {
                message.Bcc.Add("<EMAIL>");
            }

            if (fromAddress != null)
            {
                message.From = fromAddress;
            }

            string emailProviders = ConfigHelper.GetValue("EmailProvider");
            string[] splitted = emailProviders.Split(',');
            Random random = new Random();
            string emailProvider = splitted[random.Next(splitted.Length)];

            if (emailProvider == "MailGun")
            {
                MailGunProvider mailGunProvider = new MailGunProvider();
                sent = mailGunProvider.SendEmail(message.To[0], fromAddress, message, tag, activityDateTime);
            }
            else if (emailProvider == "PostMark")
            {
                Postmark postMarkProvider = new Postmark();
                postMarkProvider.SendMessage(message.To[0], fromAddress, message, tag, activityDateTime);
            }
            else
            {
                var client = GetSmtpClient(message.To[0].Address, fromAddress);// new SmtpClient();
                client.EnableSsl = true;
                if (ConfigHelper.GetBoolValue("CancelEmailAsync"))
                {
                    client.Send(message);
                }
                else
                {
                    Object state = null;
                    client.SendAsync(message, state);
                }

            }
            AddEmailTrackingToData(message);
            return sent;
        }
        public static int? GetFirstShopIDByEmailAdress(string email)
        {
            var db = DataHelper.GetStoreYaEntities();
            var user = db.Users.Where(s => s.Email.ToLower() == email.ToLower() || s.Email2.ToLower() == email.ToLower()).OrderByDescending(o => o.InsertedAt).ToList();
            if (user != null && user.Count() > 0)
            {
                var userId = 0;
                if (user.Any(x => x.FbProfileID > -1))
                {
                    userId = user.Where(x => x.FbProfileID > -1).First().ID;
                }
                else
                {
                    userId = user.First().ID;
                }
                int? shopID = db.Shops.Where(s => s.UserID == userId).OrderByDescending(i => i.InsertedAt).Select(x=>x.ID).First();
                return shopID;
            }
            return null;
        }
        public static void AddEmailTrackingToData(MailMessage message)
        {
            try
            {
                SimpleEmailDTO simpleEmailDTO = EmailTrackingHelper.GetSimpleEmailDTOFromMailMessage(message);
                bool devEmail = CheckIfEmailToDev(simpleEmailDTO.EmailTo);
                if (!devEmail || simpleEmailDTO.ShopID != null)
                {
                    EmailTrackingHelper.CreateAndSaveEmailLogData(simpleEmailDTO);
                }
            }
            catch (Exception ex)
            {
                SendEmail(EmailHelper.DEV_EMAIL, "Failed to AddEmailTrackingToData.", $"Subject: {message.Subject}, toAddress {message.To}. {ex.ToString()}");
            }
        }
        public static bool CheckIfEmailToDev(string to)
        {
            bool devEmail = false;
            if (to.Contains("+"))
            {
                string emailUsersType = null;
                Regex regex = new Regex(@"\+(.*)@");
                var v = regex.Match(to);
                emailUsersType = "+" + v.Groups[1].ToString();
                to = to.Replace(emailUsersType, "");
            }
            if (to.ToLower().Contains("<EMAIL>"))
            {
                devEmail = true;
            }
            return devEmail;
        }
        public static string GetTemplateContent(string templateName, string layout, object model)
        {
            string contentTemplate = GetTemplateContent(templateName);
            contentTemplate = contentTemplate.FormatWith(model);

            string layoutContent = GetTemplateContent(layout);

            EmailLayoutModel m = new EmailLayoutModel() { Content = contentTemplate };
            string fullContent = layoutContent.FormatWith(m);

            return fullContent;
        }
        public static string GetTemplateContentFromResource(string templateName, string layout, object model)
        {
            string contentTemplate = GetTemplateContentFromResource(templateName);
            if (model != null)
            {
                contentTemplate = contentTemplate.FormatWith(model);
            }
            string layoutContent = "";
            try
            {
                layoutContent = GetTemplateContent(layout);
            }
            catch
            {

                layoutContent = GetTemplateContentFromResource(layout);
            }


            EmailLayoutModel m = new EmailLayoutModel() { Content = contentTemplate };
            string fullContent = layoutContent.FormatWith(m);

            return fullContent;
        }
        public static string GetTemplateContentFromResource(string templateName)
        {
            return Storeya.Core.Properties.Resources.ResourceManager.GetString(templateName, Storeya.Core.Properties.Resources.Culture);
        }
        public static string GetTemplateContent(string templateName)
        {
            string path = ConfigHelper.GetValue("emailTemplatesPath");
            if (string.IsNullOrEmpty(path))
            {
                //for web app get it from relative path
                path = HttpContext.Current.Server.MapPath("/EmailTemplates");
            }

            string fileName = templateName;
            if (!fileName.EndsWith(".html"))
            {
                fileName += ".txt";
            }
            string ePath = Path.Combine(path, fileName);
            string emailTempalte = File.ReadAllText(ePath);
            try
            {
                Log4NetLogger.InfoWithDB("Email Template : " + ePath, null, -66666);
            }
            catch
            {
            }
            return emailTempalte;
        }

        public static string GetTemplateContent_2(string templateName)
        {

            if (!string.IsNullOrEmpty(templateName))
            {
                templateName = templateName.Replace("\\EmailTemplates", "");

                string path = ConfigHelper.GetValue("emailTemplatesPath");
                if (string.IsNullOrEmpty(path))
                {
                    //for web app get it from relative path
                    path = HttpContext.Current.Server.MapPath("/EmailTemplates");
                }

                string file = path + templateName;
                string emailTempalte = File.ReadAllText(file);
                try
                {
                    Log4NetLogger.InfoWithDB("Email Template 2 :" + file, null, -66666);
                }
                catch
                {
                }
                //string emailTempalte = File.ReadAllText(templateName);
                return emailTempalte;
            }
            return null;
        }

        public static bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private static SmtpClient GetSmtpClient(string toAddress, MailAddress from = null)
        {
            if (toAddress == ConfigHelper.GetValue("supportEmail")
                //|| toAddress == ConfigHelper.GetValue("overrideEmail")
                )
            {
                //Use <NAME_EMAIL> email for internal emails
                SmtpClient mailer = new SmtpClient();
                mailer.Host = "smtp.gmail.com";
                mailer.Port = 587;
                mailer.Credentials = new System.Net.NetworkCredential("<EMAIL>", "no-reply1234_+");

                return mailer;
            }

            if (from != null && !string.IsNullOrEmpty(from.Address) && from.Address == ConfigHelper.GetValue("EmailFrom_Eyal"))
            {
                SmtpClient mailer = new SmtpClient("smtp.gmail.com", 587);
                mailer.Credentials = new System.Net.NetworkCredential(from.Address, ConfigHelper.GetValue("EmailFromCredentials_Eyal"));
                return mailer;
            }

            return new SmtpClient();
        }



        public static string AdWordsAccountLink(int shopID)
        {
            var s = "https://bo.storeya.com/go/awxccount?shopid=" + shopID + Environment.NewLine;
            var html = $"<a href=\"https://bo.storeya.com/go/awaccount?shopid={shopID}\">AdWords account</a>";
            return html;
        }


        //public static string ShopDetailsAdminLink(int shopID)
        //{
        //    var s = "https://dev.storeya.com/admin/shopdetailsnew/" + shopID;
        //    return s;
        //}
        public static string GetBoLinkHref(int shopID, string action = "Details", string highlightText = null)
        {
            var html = $"<a href=\"https://bo.storeya.com/Shop/{action}/{shopID}\" >{shopID}</a>";
            if (highlightText != null)
            {
                html = $"<a href=\"https://bo.storeya.com/Shop/{action}/{shopID}#:~:text={highlightText}\" >{shopID}</a>";
            }

            return html;
        }
        public static string GetBoLinkHrefAndAM(int shopID, out string accountManager, string action = "Details", string highlightText = null)
        {
            var html = GetBoLinkHref(shopID, action, highlightText);
            accountManager = null;
            var db = DataHelper.GetStoreYaEntities();
            var bs = db.TbBigSpenders.Where(b => b.ShopID == shopID).SingleOrDefault();
            if (bs != null && bs.AssignedTo != null && bs.AssignedTo != (int)StoreYaEmployee.Unassigned)
            {
                StoreYaEmployee am = (StoreYaEmployee)bs.AssignedTo;
                accountManager = am.ToString();
            }
            html = html + $" <b>{accountManager}</b>";

            var tb = TrafficBoostersDbHelper.GetSettings(shopID);
            if (tb != null && !string.IsNullOrEmpty(tb.MajorAccountChanges))
            {
                html += " Remarks:" + tb.MajorAccountChanges;
            }
            return html;
        }
        public static string GetBoLinkHrefAndAM(int shopID)
        {
            var html = GetBoLinkHref(shopID);
            // $"<a href=\"https://dev.storeya.com/admin/shopdetailsnew/{shopID}\">{shopID}</a>";

            var db = DataHelper.GetStoreYaEntities();
            var bs = db.TbBigSpenders.Where(b => b.ShopID == shopID).SingleOrDefault();
            if (bs != null && bs.AssignedTo != null && bs.AssignedTo != (int)StoreYaEmployee.Unassigned)
            {
                html += " " + (StoreYaEmployee)bs.AssignedTo;
            }
            else
            {
                //if no AM check for comment
                var tb = TrafficBoostersDbHelper.GetSettings(shopID);
                if (tb != null && !string.IsNullOrEmpty(tb.MajorAccountChanges))
                {
                    html += " Remarks:" + tb.MajorAccountChanges;
                }

            }

            return html;
        }
        public static string AdminLinkHref(int shopID)
        {
            var html = $"<a href=\"https://dev.storeya.com/admin/shopdetailsnew/{shopID}\">{shopID}</a>";
            return html;
        }



        public static void SendEmailAboutUpgrade(string to, string name, string subject, string template, int shopID)
        {
            try
            {
                string emailTempalte = EmailHelper.GetTemplateContent(template);
                EmailHelper.SendEmail(to, subject, emailTempalte.FormatWith(new { Name = name }));
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to send email about TB Upgrade to email: {0}.", to), ex, shopID);

            }
        }
        public class EmailLayoutModel
        {
            public string Content { get; set; }
        }

        public static MailAddress GetUserNameForEmail(int shopID)
        {

            var db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(x => x.ID == shopID).Single();
            User user = db.Users.Where(x => x.ID == shop.UserID).Single();

            var name = user.Name ?? "There";
            string[] splitted = name.Split(' ');
            if (splitted.Length > 1)
            {
                name = splitted[0];
            }

            MailAddress address = new MailAddress(user.Email2 ?? user.Email, name);

            return address;
        }


        public static string ExtractFirstName(string userName)
        {
            var name = userName ?? "There";
            string[] splitted = name.Split(' ');
            if (splitted.Length > 1)
            {
                name = splitted[0];
            }

            return name;
        }

        public static string ExtractLastName(string fullName)
        {
            if (fullName != null)
            {
                string[] splitted = fullName.Split(' ');
                if (splitted.Length > 1)
                {
                    return splitted[splitted.Length - 1];
                }
            }
            return null;
        }



    }


}