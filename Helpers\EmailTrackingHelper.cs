﻿using Amazon;
using Amazon.Auth.AccessControlPolicy;
using log4net.Layout;
using Newtonsoft.Json.Linq;
using PostmarkDotNet.Model;
using Storeya.Core.Entities;
using Storeya.Core.Models.Payments;
using Storeya.Core.Models;
using Storeya.Core.Models.TrafficBoosterModels.Upgrades;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.ServiceModel.Configuration;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using static Storeya.Core.Helpers.EmailHelper;
using Storeya.Core.Models.EmailsSeriesSystem;
using Storeya.Core.Models.CRM;
using static Storeya.Core.Models.StoreyaErrors;
using System.Data.Entity.Validation;

namespace Storeya.Core.Helpers
{
    public class SimpleEmailDTO
    {
        public string Subject { get; set; }
        public string Body { get; set; }
        public int? ShopID { get; set; }
        public string EmailFrom { get; set; }
        public string EmailTo { get; set; }
        public string EmailID { get; set; }
        public int? Type { get; set; }
        public DateTime? EmailDate { get; set; }
        public bool MultipulShops { get; set; }
        private List<int> otherUsersShops { get; set; }
        public List<int> OtherUserShops
        {
            get
            {
                if (!MultipulShops || ShopID == null)
                {
                    return null;
                }
                if (otherUsersShops == null)
                {
                    var db = DataHelper.GetStoreYaEntities();
                    var mainShop = db.Shops.Where(x => x.ID == ShopID.Value).First();
                    var shopIDs = db.Shops.Where(s => s.UserID == mainShop.UserID && s.ID != ShopID.Value && (!s.IsDisabled.HasValue || s.IsDisabled != 1)).Select(s => s.ID).ToList();
                    otherUsersShops = shopIDs;
                }
                return otherUsersShops;
            }
        }
    }

    public enum EmailTypes
    {
        Undefined = 0,
        BudgetExhausted = 1,
        Budget90Percentage = 2,
        ClientLetter = 3,
        ThirdUpgradeEmail = 4,
        AskForReview = 5,
        FailedCharges = 6,
        OutgoingUpgradeEmail = 7,
        Communication = 8,
        EmailWithPaymentLink = 9,
        MCInvintation = 10
    }
    public enum EmailSources
    {
        Undefined = 0,
        StoreYaAdwordsAPI = 1,
        ElmahMonitor = 2,
        backuper = 3,
        ArchiveManager = 4,
        GmailManager = 5,
        StoreYaWeb = 6,

    }
    public enum EmailDirections
    {
        Undefined = 0,
        Send = 1,
        Received = 2,
    }
    public class EmailTrackingHelper
    {
        private StoreYaEntities _db { get; set; }
        public string[] KeysToDefineTheType
        {
            get
            {
                return (new string[] { "BudgetExhausted", "Budget90Percentage", "Your Store's SEO Stats", "3d UPGRADE EMAIL",
                    "Important Message regarding","Recurring Payment Failure","Recurring Payment Failed","Your StoreYa Account was Paused","Google Ads Account is Going to be Cancelled" });
            }
        }
        public EmailsLog EmailsLog { get; set; }

        public EmailTrackingHelper()
        {
            _db = DataHelper.GetStoreYaEntities();
        }
        public static SimpleEmailDTO GetSimpleEmailDTOFromMailMessage(MailMessage message)
        {
            SimpleEmailDTO simpleEmailDTO = new SimpleEmailDTO();
            simpleEmailDTO.Subject = message.Subject;
            simpleEmailDTO.EmailFrom = message.From.ToString().Replace("\"", "").Replace("<", "").Replace(">", ""); ;
            simpleEmailDTO.EmailTo = message.To.ToString().Replace("\"", "").Replace("<", "").Replace(">", "");
            simpleEmailDTO.ShopID = TryToGetShopIDFromMessage(message); //-1 - more than one shop
            if (simpleEmailDTO.ShopID == null)
            {
                simpleEmailDTO.ShopID = EmailHelper.GetFirstShopIDByEmailAdress(simpleEmailDTO.EmailTo, out bool multipulShops);
                simpleEmailDTO.MultipulShops = multipulShops;
            }
            simpleEmailDTO.EmailDate = null;
            simpleEmailDTO.Type = null;
            return simpleEmailDTO;
        }

        public static int? CreateAndSaveEmailLogData(SimpleEmailDTO simpleEmailDTO, int direction = (int)EmailDirections.Send, List<int?> upgraidEmailsShopIDs = null)
        {
            EmailsLog emailsLog = new EmailsLog();
            //getting data from simpleEmailDTO  
            emailsLog.Subject = simpleEmailDTO.Subject;
            emailsLog.EmailFrom = simpleEmailDTO.EmailFrom;
            emailsLog.EmailTo = simpleEmailDTO.EmailTo;
            emailsLog.EmailID = simpleEmailDTO.EmailID;
            emailsLog.ShopID = simpleEmailDTO.ShopID;
            bool potentialUpgradeEmail = false;
            if (emailsLog.ShopID != null && upgraidEmailsShopIDs != null && upgraidEmailsShopIDs.Count() > 0)
            {
                if (upgraidEmailsShopIDs.Contains(emailsLog.ShopID))
                {
                    potentialUpgradeEmail = true;
                }
                else
                {
                    if (simpleEmailDTO.OtherUserShops != null)
                    {
                        foreach (var shopId in simpleEmailDTO.OtherUserShops)
                        {
                            if (upgraidEmailsShopIDs.Contains(shopId))
                            {
                                potentialUpgradeEmail = true;
                                emailsLog.ShopID = shopId;
                                simpleEmailDTO.MultipulShops = false;
                            }
                        }
                    }
                }
                if (direction == EmailDirections.Received.GetHashCode())
                {
                    int? id = SystemEventHelper.Add(emailsLog.ShopID.Value, null, SystemEventTypes.UserActivity, SystemEventActions.EmailRecived);
                    EmailsSeriesHelper.EmailWasRecivedExitAllSeries(emailsLog.ShopID.Value, id);
                }
            }
            EmailTrackingHelper emailTrackingHelper = new EmailTrackingHelper();
            emailsLog.EmailType = emailTrackingHelper.GetEmailType(simpleEmailDTO, direction, potentialUpgradeEmail);
            if (emailsLog.EmailType == (int)EmailTypes.Undefined && simpleEmailDTO.Type != null)
            {
                emailsLog.EmailType = simpleEmailDTO.Type;
            }
            string source = emailTrackingHelper.GetEmailSourceByCommandLineOrURL(out bool isUrlSource);
            if (source.ToLower().Contains("gmailmanager.exeaddmanagerlabel"))
            {
                if (direction == (int)EmailDirections.Received)
                {
                    source = "Emma's inbox";
                }
                else
                {
                    source = "Emma's outbox";
                }
            }
            if ((simpleEmailDTO.MultipulShops || emailsLog.ShopID == null) && source != null)
            {
                int? shopIDFromShource = TryToGetShopIDFromSource(source, isUrlSource);
                if (shopIDFromShource != null)
                {
                    if (simpleEmailDTO.OtherUserShops != null && !simpleEmailDTO.OtherUserShops.Contains(shopIDFromShource.Value) && shopIDFromShource != emailsLog.ShopID && emailsLog.ShopID != null)
                    {
                        EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "Multipul shops email logs issue", $"ShopID from source: {shopIDFromShource}, source: {source}. Such shopId was not found in the client's shops. Client's  ShopID is {emailsLog.ShopID} (the client has a few shops). The log was added for shopID from source - {source}");
                    }
                    emailsLog.ShopID = shopIDFromShource.Value;
                    simpleEmailDTO.MultipulShops = false;
                    source += "ShopID from source";
                }
            }

            emailsLog.EmailSource = source;
            emailsLog.Direction = direction;
            emailsLog.InsertedAt = simpleEmailDTO.EmailDate;
            emailsLog.UpdatedAt = DateTime.Now;
            //saving the data  
            List<int> shopsWithLogs = new List<int>();
            if (emailsLog.ShopID != null)
            {
                shopsWithLogs.Add(emailsLog.ShopID.Value);
            }
            emailTrackingHelper.EmailsLog = emailsLog;

            emailTrackingHelper.SaveEmailLogDataToDB();
            //save log for other user's shops 

            if (simpleEmailDTO.OtherUserShops != null)
            {
                foreach (var shopID in simpleEmailDTO.OtherUserShops)
                {
                    shopsWithLogs.Add(shopID);
                    emailsLog.ShopID = shopID;
                    emailTrackingHelper.EmailsLog = emailsLog;
                    emailTrackingHelper.SaveEmailLogDataToDB();
                }
            }
            //if (shopsWithLogs.Count > 1)
            //{
            //var shopsForLog = string.Join(", ", shopsWithLogs);
            //EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "Temporary email. Multipul shops email logs.", $"Please check email logs with subject: {emailsLog.Subject} for shops from the list: {shopsForLog}");
            //}
            return emailsLog.EmailType;
        }

        private static int? TryToGetShopIDFromSource(string source, bool isUrlSource)
        {
            string pattern = @"(?<=shopid:)\d+";
            if (isUrlSource)
            {
                var parthAndQueryl = HttpHelper.GetPathAndQueryl();
                if (parthAndQueryl != null)
                {
                    source = parthAndQueryl.ToLower().Replace(" ", "");
                }
                pattern = @"(?<=shopid=)\d+";
            }
            try
            {
                string sourceText = source.ToLower().Replace(" ", "");
                if (sourceText.Contains("shopid"))
                {

                    Match match = Regex.Match(sourceText, pattern);
                    if (match.Success)
                    {
                        string shopIdFromSource = match.Value;
                        int shopID = Int32.Parse(shopIdFromSource);
                        return shopID;
                    }
                }
            }
            catch { }
            return null;
        }

        public int GetEmailType(SimpleEmailDTO simpleEmailDTO, int? direction = null, bool potentialUpgradeEmail = false)
        {
            string subject = simpleEmailDTO.Subject;
            string sKeyResult = this.KeysToDefineTheType.FirstOrDefault<string>(s => subject.Contains(s));
            if (sKeyResult != null)
            {
                switch (sKeyResult)  //"Your Store's SEO Stats", "3d UPGRADE EMAIL"
                {
                    case "BudgetExhausted":
                        return (int)EmailTypes.BudgetExhausted;
                    case "Budget90Percentage":
                        return (int)EmailTypes.Budget90Percentage;
                    case "3d UPGRADE EMAIL":
                        return (int)EmailTypes.ThirdUpgradeEmail;
                    case "Your Store's SEO Stats":
                        return (int)EmailTypes.AskForReview;
                    case "Important Message regarding":
                        return (int)EmailTypes.FailedCharges;
                    case "Recurring Payment Failure":
                        return (int)EmailTypes.FailedCharges;
                    case "Recurring Payment Failed":
                        return (int)EmailTypes.FailedCharges;
                    case "Your StoreYa Account was Paused":
                        return (int)EmailTypes.FailedCharges;
                    case "Google Ads Account is Going to be Cancelled":
                        return (int)EmailTypes.FailedCharges;
                    default:
                        return (int)EmailTypes.Undefined;
                }
            }
            var type = (int)EmailTypes.Undefined;
            if (direction != null && direction == (int)EmailDirections.Received)
            {
                type = TryToIdentifyRecievedEmail(simpleEmailDTO);
            }
            if (direction != null && direction == (int)EmailDirections.Send)
            {
                type = TryToIdentifyOutgongUpgradeEmail(simpleEmailDTO, potentialUpgradeEmail);
            }
            if (type == (int)EmailTypes.Undefined && subject.Contains("Re:"))
            {
                return (int)EmailTypes.Communication;
            }
            return type;
        }

        private int TryToIdentifyRecievedEmail(SimpleEmailDTO simpleEmailDTO)
        {
            if (simpleEmailDTO != null)
            {
                if (simpleEmailDTO.Subject != null && simpleEmailDTO.Subject.Contains("Google Merchant Center") && !simpleEmailDTO.Subject.ToLower().Contains("logo")
                    && !simpleEmailDTO.Subject.ToLower().Contains("click") && !simpleEmailDTO.Subject.ToLower().Contains("discount") && !simpleEmailDTO.Subject.ToLower().Contains("shopify")
                     && !simpleEmailDTO.Subject.ToLower().Contains("important") && !simpleEmailDTO.Subject.ToLower().Contains("warning") && !simpleEmailDTO.Subject.ToLower().Contains("getting")
                     && !simpleEmailDTO.Subject.ToLower().Contains("version") && !simpleEmailDTO.Subject.ToLower().Contains(" feed ") && !simpleEmailDTO.Subject.ToLower().Contains(" feed.") && !simpleEmailDTO.Subject.ToLower().Contains(" feed,"))
                {
                    return (int)EmailTypes.MCInvintation;
                }
                return (int)EmailTypes.Undefined;
            }
            throw new NotImplementedException();
        }

        public int TryToIdentifyOutgongUpgradeEmail(SimpleEmailDTO simpleEmailDTO, bool potentialUpgradeEmail)
        {
            string subject = simpleEmailDTO.Subject;
            EmailData emailData = new EmailData();
            List<string> subjects = emailData.Subject;
            subjects.Add(", we managed to generate ");
            string textWithoutSpaces = subject.Replace(" ", "").ToLower();
            if (!subject.Contains("Re:"))
            {
                foreach (string s in subjects)
                {
                    if (s == ", we managed to generate ")
                    {
                        if (subject.Contains(s))
                        {
                            return (int)EmailTypes.OutgoingUpgradeEmail;
                        }
                    }
                    else
                    {
                        string subjectWithoutSpaces = s.Replace(" ", "").ToLower();
                        if (subjectWithoutSpaces == textWithoutSpaces)
                        {
                            return (int)EmailTypes.OutgoingUpgradeEmail;
                        }
                    }
                }
            }
            if (CheckIfPaymnentLinkExist(simpleEmailDTO.Body, simpleEmailDTO.ShopID))
            {
                if (potentialUpgradeEmail)
                {
                    return (int)EmailTypes.OutgoingUpgradeEmail;
                }
                else
                {
                    return (int)EmailTypes.EmailWithPaymentLink;
                }
            }

            return (int)EmailTypes.Undefined;
        }

        private bool CheckIfPaymnentLinkExist(string body, int? shopID)
        {
            //AbstractPaymentAdapter abstractPaymentAdapter = new BlueSnapPaymentAdapter();
            //PaymentDetails details = new PaymentDetails()
            //{
            //    PlanID = PlanTypes.CustomPlan.GetHashCode(),
            //    AppID = AppTypes.TrafficBooster.GetHashCode(),
            //    ShopID = shopID ?? 0,
            //    Method = 0,
            //};
            //var linkWithoutSumm = abstractPaymentAdapter.GetStoreYaChekoutNewPage(details, forcedStoreYaDomain: true);
            //if (shopID != null && shopID != -1) //if we have shopID
            //{
            //    linkWithoutSumm = linkWithoutSumm.Split(new string[] { "item=" }, StringSplitOptions.None)[0];
            //}
            //else
            //{
            //    linkWithoutSumm = linkWithoutSumm.Split(new string[] { "shopId=" }, StringSplitOptions.None)[0];
            //}

            if (body != null && body.ToLower().Contains("/public/checkout"))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public string GetEmailSourceByCommandLineOrURL(out bool isUrlSource)
        {
            isUrlSource = false;
            string source = null;
            if (HttpContext.Current == null)
            {
                source = Environment.CommandLine.Replace("\"", "");
                isUrlSource = true;
            }
            else
            {
                source = HttpContext.Current.Request.Url.Host;
            }
            if (source == null)
            {
                return "Undefined";
            }
            else
            {
                var arr = source.Split('\\');
                int lastElement = arr.Length - 1;
                string sKeyResult = arr[lastElement].Replace(" ", "");
                return sKeyResult;
            }
        }
        public static int? TryToGetShopIDFromMessage(MailMessage message)
        {
            int? shopID = null;
            string[] texts = new string[] { message.Subject, message.Body };
            foreach (string text in texts)
            {
                shopID = TryToGetShopIDFromText(text);
                if (shopID != null)
                {
                    break;
                }
            }
            return shopID; //-1 - more than one shop
        }
        public static int? TryToGetShopIDFromText(string text)
        {
            int? id = null;
            text = text.ToLower();
            string[] textsBeforeID = new string[] { "shopid=", "shop/details/","/shopdetails/","/shopdetailsnew/",
                "shop: ", "shop:", " shopid ", " shop " };
            foreach (string textBefore in textsBeforeID)
            {
                if (text.Contains(textBefore))
                {
                    id = TryParceShopIDAfterShopidText(text, textBefore);
                    if (id != null)
                    {
                        break;
                    }
                }
            }
            return id; //-1 - more than one shop
        }
        public static int? TryParceShopIDAfterShopidText(string text, string textBeforeID)
        {
            string shopID = null;
            int? id = null;
            string[] separators = { textBeforeID };
            var splitedTextByTextBeforeID = text.Split(separators, StringSplitOptions.None);
            splitedTextByTextBeforeID = splitedTextByTextBeforeID.Skip(1).ToArray();
            if (splitedTextByTextBeforeID.Length > 0)
            {
                HashSet<int> shopIDs = new HashSet<int>();
                foreach (var splitedText in splitedTextByTextBeforeID)
                {
                    shopID = splitedText;
                    shopID = shopID.Split('<')[0];
                    shopID = shopID.Split('>')[0];
                    shopID = shopID.Replace("\"", "").Replace("'", "").Replace(">", "").Replace("<", "");
                    shopID = shopID.Split('&')[0];
                    shopID = shopID.Split(' ')[0];
                    shopID = shopID.Split('/')[0];
                    if (shopID.Length < 15)
                    {
                        try
                        {
                            id = int.Parse(shopID);
                            shopIDs.Add(id.Value);
                        }
                        catch
                        {
                            continue;
                        }
                    }
                }
                if (shopIDs.Count == 0)
                {
                    return null;
                }
                else if (shopIDs.Count == 1)
                {
                    return shopIDs.ToArray()[0];
                }
                else
                {
                    return -1; //more than one shop
                }

            }
            return null;

        }
        public void SaveEmailLogDataToDB()
        {
            if (EmailsLog.InsertedAt == null)
            {
                EmailsLog.InsertedAt = DateTime.Now;
            }
            _db.EmailsLogs.Add(EmailsLog);
            try
            {
                _db.SaveChanges();
            }
            catch (DbEntityValidationException e)
            {
                var newException = new FormattedDbEntityValidationException(e);
                Log4NetLogger.ErrorWithDB(string.Format("Failed to update EmailsLog. Shop ID : {0}", EmailsLog.ShopID.ToString()), newException, EmailsLog.ShopID);
                throw newException;
            }
        }
        public static List<EmailsLog> GetLastDaysEmailLogs(int? shopID = null, int daysBack = 2, EmailDirections? emailDirection = null, bool withEmailIdOnly = false)
        {
            var db = DataHelper.GetStoreYaEntities();
            var emailsYoungerThan = DateTime.Today.AddDays(-daysBack);
            var query = db.EmailsLogs.Where(x => x.InsertedAt >= emailsYoungerThan);
            if (emailDirection != null)
            {
                query = query.Where(x => x.Direction == (int)emailDirection);
            }
            if (withEmailIdOnly)
            {
                query = query.Where(x => x.EmailID != null);
            }
            if (shopID != null)
            {
                query = query.Where(x => x.ShopID == shopID);
            }
            return query.ToList();
        }
        public static bool CheckIfEmailWithSameSubjectWasSendInLastDays(int? shopID, string subject)
        {
            if (shopID == null)
            {
                return false;
            }
            List<EmailsLog> last2DaysEmails = GetLastDaysEmailLogs(shopID);
            if (last2DaysEmails.Where(x => x.Direction == (int)EmailDirections.Send && x.Subject == subject).Any())
            {
                return true;
            }
            return false;
        }
    }
}
