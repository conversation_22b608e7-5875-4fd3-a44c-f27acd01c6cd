﻿using System.IO;
using System.Text;
using System.Security.Cryptography;
using System;
using System.Linq;
using System.Collections.Generic;

namespace Storeya.Core.Helpers
{
    public static class EncryptionHelper
    {

        public static string GenerateJwtToken(string shopDomain, string userId, int appType, string appSecret, int expireInHours = 96)
        {
            DateTime expire = DateTime.Now.AddHours(expireInHours);

            // Define the claims
            var claims = $@"
            {{
                ""iss"": ""https://{shopDomain}/admin"",
                ""dest"": ""https://{shopDomain}"",
                ""aud"": ""591da5e9097a0601a311e9cbac1837cf"",
                ""sub"": ""{userId}"",
                ""exp"": {ToUnixTime(expire)},
                ""nbf"": {ToUnixTime(expire)},
                ""iat"": {ToUnixTime(expire)},
                ""jti"": ""{Guid.NewGuid()}"",
                ""sid"": ""{Guid.NewGuid()}"",
                ""sil"": ""{Guid.NewGuid()}""
            }}";

            // Generate the token
            var token = Base64Encode(claims);

            string prefix = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"; //"{\"alg\": \"HS256\",\"typ\": \"JWT\"}".Base64Decode();
            var hmac = ComputeHMAC(prefix, token, appType, appSecret);

            return $"{prefix}.{token}.{hmac}";
        }

        private static long ToUnixTime(DateTime dateTime)
        {
            return ((DateTimeOffset)dateTime).ToUnixTimeSeconds();
        }

        private static string Base64Encode(string text)
        {
            var plainTextBytes = Encoding.UTF8.GetBytes(text);
            return Convert.ToBase64String(plainTextBytes);
        }

        private static string ComputeHMAC(string prefix, string token, int appType, string appSecret)
        {


            string res = EncryptionHelper.EncryptHmacSha256($"{prefix}.{token}", appSecret);
            return res;
        }
        public static string RandomString(int length)
        {
            Random random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            return new string(Enumerable.Repeat(chars, length)
              .Select(s => s[random.Next(s.Length)]).ToArray());
        }
        public static void GeneratKeys(out string privateKey, out string publicKey, int keylength = 3096)
        {
            var rsa = new RSACryptoServiceProvider(keylength);
            privateKey = rsa.ToXmlString(true);
            publicKey = rsa.ToXmlString(false);
        }

        public static string Decrypt(string data)
        {
            string privateKey = ConfigHelper.GetValue("LoginEncryptionKey");//contain the privatekey
            if (string.IsNullOrEmpty(privateKey))
            {
                return null;
            }
            data = Base64UrlDecode(data);
            data = data.Replace(" ", "+");
            byte[] dataArray = Convert.FromBase64String(data);
            UnicodeEncoding _encoder = new UnicodeEncoding();
            var rsa = new RSACryptoServiceProvider();
            rsa.FromXmlString(privateKey);
            var decryptedByte = rsa.Decrypt(dataArray, false);
            return _encoder.GetString(decryptedByte);
        }

        public static string Encrypt(string data)
        {
            string publicKey = ConfigHelper.GetValue("LoginEncryptionKey");//contain the privatekey
            if (string.IsNullOrEmpty(publicKey))
            {
                return null;
            }
            UnicodeEncoding _encoder = new UnicodeEncoding();
            var rsa = new RSACryptoServiceProvider();
            rsa.FromXmlString(publicKey);
            var dataToEncrypt = _encoder.GetBytes(data);
            var encryptedByteArray = rsa.Encrypt(dataToEncrypt, false).ToArray();
            var sb = Convert.ToBase64String(encryptedByteArray);
            return Base64UrlEncode(sb);
        }

        public static string EncryptRFC(string clearText)
        {
            string EncryptionKey = ConfigHelper.GetValue("LoginEncryptionKey", "StoreYaDefaultLoginEncryptionKey") + DateTime.Now.ToString("ddMMyyyy");

            byte[] clearBytes = Encoding.Unicode.GetBytes(clearText);
            using (Aes encryptor = Aes.Create())
            {
                Rfc2898DeriveBytes pdb = new Rfc2898DeriveBytes(EncryptionKey, new byte[] { 0x49, 0x76, 0x61, 0x6e, 0x20, 0x4d, 0x65, 0x64, 0x76, 0x65, 0x64, 0x65, 0x76 });
                encryptor.Key = pdb.GetBytes(32);
                encryptor.IV = pdb.GetBytes(16);
                encryptor.Padding = PaddingMode.PKCS7;
                using (MemoryStream ms = new MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, encryptor.CreateEncryptor(), CryptoStreamMode.Write))
                    {
                        cs.Write(clearBytes, 0, clearBytes.Length);
                        cs.Close();
                    }
                    clearText = Convert.ToBase64String(ms.ToArray());
                }
            }
            return Base64UrlEncode(clearText);
        }

        public static string DecryptRFC(string cipherText)
        {
            cipherText = Base64UrlDecode(cipherText);
            string EncryptionKey = ConfigHelper.GetValue("LoginEncryptionKey", "StoreYaDefaultLoginEncryptionKey") + DateTime.Now.ToString("ddMMyyyy");
            cipherText = cipherText.Replace(" ", "+");
            byte[] cipherBytes = Convert.FromBase64String(cipherText);
            using (Aes encryptor = Aes.Create())
            {
                Rfc2898DeriveBytes pdb = new Rfc2898DeriveBytes(EncryptionKey, new byte[] { 0x49, 0x76, 0x61, 0x6e, 0x20, 0x4d, 0x65, 0x64, 0x76, 0x65, 0x64, 0x65, 0x76 });
                encryptor.Key = pdb.GetBytes(32);
                encryptor.IV = pdb.GetBytes(16);
                encryptor.Padding = PaddingMode.PKCS7;
                using (MemoryStream ms = new MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, encryptor.CreateDecryptor(), CryptoStreamMode.Write))
                    {
                        cs.Write(cipherBytes, 0, cipherBytes.Length);
                        cs.Close();
                    }
                    cipherText = Encoding.Unicode.GetString(ms.ToArray());
                }
            }
            return cipherText;
        }

        public static string Base64UrlEncode(string value)
        {
            var bytes = Encoding.UTF8.GetBytes(value);
            var s = Convert.ToBase64String(bytes); // Regular base64 encoder
            s = s.Split('=')[0]; // Remove any trailing '='s
            s = s.Replace('+', '-'); // 62nd char of encoding
            s = s.Replace('/', '_'); // 63rd char of encoding
            return s;
        }

        public static string Base64UrlDecode(string value)
        {
            var s = value;
            s = s.Replace('-', '+'); // 62nd char of encoding
            s = s.Replace('_', '/'); // 63rd char of encoding
            switch (s.Length % 4) // Pad with trailing '='s
            {
                case 0:
                    break; // No pad chars in this case
                case 2:
                    s += "==";
                    break; // Two pad chars
                case 3:
                    s += "=";
                    break; // One pad char
                default:
                    throw new Exception("Illegal base64 url string!");
            }

            var bytes = Convert.FromBase64String(s); // Standard base64 decoder
            return Encoding.UTF8.GetString(bytes);
        }

        public static string GeneratAESKey()
        {
            using (Aes aes = Aes.Create())
            {
                return Convert.ToBase64String(aes.Key);
            }
        }

        public static string GeneratAESKeyIV(byte[] key)
        {
            AesCryptoServiceProvider aesCryptoServiceProvider = new AesCryptoServiceProvider();
            aesCryptoServiceProvider.Key = key;
            aesCryptoServiceProvider.GenerateIV();
            return Convert.ToBase64String(aesCryptoServiceProvider.IV);

        }
        public static string EncryptAES(string plainText, string publicKey = null)
        {
            if (string.IsNullOrEmpty(plainText))
            {
                return plainText;
            }
            if (string.IsNullOrEmpty(publicKey))
            {
                publicKey = ConfigHelper.GetValue("AESEncryptionKey");
            }
            byte[] Key = Convert.FromBase64String(publicKey);
            string iv = GeneratAESKeyIV(Key);
            byte[] IV = Convert.FromBase64String(iv);
            // Check arguments.
            if (plainText == null || plainText.Length <= 0)
                throw new ArgumentNullException("plainText");
            if (Key == null || Key.Length <= 0)
                throw new ArgumentNullException("Key");
            if (IV == null || IV.Length <= 0)
                throw new ArgumentNullException("IV");
            byte[] encrypted;

            // Create an Aes object
            // with the specified key and IV.
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Key;
                aesAlg.IV = IV;

                // Create an encryptor to perform the stream transform.
                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                // Create the streams used for encryption.
                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {
                            //Write all data to the stream.
                            swEncrypt.Write(plainText);
                        }
                        encrypted = msEncrypt.ToArray();
                    }
                }
            }
            // Return the encrypted bytes from the memory stream.
            return Convert.ToBase64String(encrypted) + "&" + iv;
        }

        public static string DecryptAES(string text, string publicKey = null)
        {
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }
            if (!text.Contains("&"))
            {
                throw new Exception("Text is in the wrong format");
            }
            if (string.IsNullOrEmpty(publicKey))
            {
                publicKey = ConfigHelper.GetValue("AESEncryptionKey");
            }
            byte[] Key = Convert.FromBase64String(publicKey);

            var e = text.Split(new char[] { '&' });

            byte[] IV = Convert.FromBase64String(e[1]);
            byte[] cipherText = Convert.FromBase64String(e[0]);
            // Check arguments.
            if (cipherText == null || cipherText.Length <= 0)
                throw new ArgumentNullException("cipherText");
            if (Key == null || Key.Length <= 0)
                throw new ArgumentNullException("Key");
            if (IV == null || IV.Length <= 0)
                throw new ArgumentNullException("IV");

            // Declare the string used to hold
            // the decrypted text.
            string plaintext = null;

            // Create an Aes object
            // with the specified key and IV.
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Key;
                aesAlg.IV = IV;

                // Create a decryptor to perform the stream transform.
                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                // Create the streams used for decryption.
                using (MemoryStream msDecrypt = new MemoryStream(cipherText))
                {
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                        {

                            // Read the decrypted bytes from the decrypting stream
                            // and place them in a string.
                            plaintext = srDecrypt.ReadToEnd();
                        }
                    }
                }
            }

            return plaintext;
        }

        public static string EncryptHmacSha256(string data, string key)
        {
            byte[] keyBytes = Encoding.UTF8.GetBytes(key);
            byte[] dataBytes = Encoding.UTF8.GetBytes(data);

            using (HMACSHA256 hmac = new HMACSHA256(keyBytes))
            {
                byte[] hashBytes = hmac.ComputeHash(dataBytes);
                return Convert.ToBase64String(hashBytes).Replace('+', '-')
            .Replace('/', '_')
            .TrimEnd('=');
            }
        }

        public static string DecodeJwtPayload(string jwt, bool replaceExcessiveEscaping = false)
        {
            // Split the JWT to get its payload
            string[] parts = jwt.Split('.');
            if (parts.Length != 3)
            {
                throw new ArgumentException("The token does not appear to be a valid JWT", nameof(jwt));
            }

            string payload = parts[1];
            string decoded = Base64UrlDecode(payload);
            if (replaceExcessiveEscaping)
            {
                decoded = decoded.Replace("\\", "").Replace("\"{", "{").Replace("}\"", "}");
            }
            return decoded;
        }
        public static string EncodeJwtPayload(string payload)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(payload);
            string base64 = Convert.ToBase64String(bytes);
            string encoded = base64.Replace('+', '-').Replace('/', '_').Replace("=", "");
            return encoded;
        }
    }
}