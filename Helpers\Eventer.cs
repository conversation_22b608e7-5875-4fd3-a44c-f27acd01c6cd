﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Storeya.Core.Entities;

namespace Storeya.Core.Helpers
{
    public class Eventer
    {
        public static bool ReportEvent(int shopID, EventCategory category, string action, string label, int? value = 1)
        {
            return ReportEvent(category, action, label, value, shopID);
        }
        private static bool ReportEvent(EventCategory category, string action, string label, int? value = 1, int shopID = 0)
        {
            if (!string.IsNullOrEmpty(label) && label.Length > 49)
            {
                label = label.Substring(0, 49);
            }
            try
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                db.GaEvents.Add(new GaEvent() { ShopID = shopID, Action = action, Category = category.ToString(), Label = label, EventValue = value, InsertedAt = DateTime.Now });
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                if (ConfigHelper.GetBoolValue("DebugEvents"))
                {
                    Log4NetLogger.Error(string.Format("Failed to save Event in DB: {0},{1},{2},{3}.", category, action, label, value), ex, (int)ReservedForLoggingShops.EventsLogging);
                }
                return false;
            }

            return true;
        }
    }

    public enum EventCategory
    {
        Account = 1,
        BackgroundTasks = 2,
        Adwords = 9,
        Payments = 10,
        ReferredPayments = 20,
        CouponPop = 200,
        ExitPop = 300,
        Rff = 400,
        FbShop = 500,

        AppStore = 600,
        IPN = 700,
        IPNAV = 701,
        TrafficBooster = 800,
        BunnerLite = 801,
        FacebookAdsGrader = 802,
        BenchmarkHero = 803,
        GrowthHero = 804,
        ProductDescriber = 805,
        TrustBadges = 806,
        AISeoProductOptimizer = 807,
        WhatsApp = 808,

        //Registration = 1,
        //Testimonials = 2,

        //Payments = 3,
        //Offers = 4,
        //ApiCalls = 5,
        //Warnings = 6,
        //PostImportSteps = 7,
        //MonopolYa = 8,
        //ScratchAndWinPromotion = 10,
        //InstaGallery = 11,
        //GroupDeal = 12,
        //RffInstallationStep = 13,

        //LikeBox = 100,
        //FanGate = 101,
        //TwitterTab = 102,
        //InstaTab = 103,
        //YoutubeTab = 104,
        //PintrestTab = 105,

        //CouponPop = 200,
        //CpShopifyInstalls = 201,

        //ExitPop = 210,
        //EpShopifyInstalls = 211,

        //PluginInstalled = 250,

        //StandAloneStore = 300,
    }
}
