﻿using Storeya.Core.Helpers;
using Storeya.Core.Models.Experimenter;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class ExperimentsHelper
    {


        public static string GetAccountName(TrafficBooster tb)
        {
            var url = tb.Url1;
            if (Uri.IsWellFormedUriString(tb.Url1, UriKind.Absolute))
            {
                url = new Uri(tb.Url1).Host;
            }
            return tb.ShopID + "_" + url;
        }


        public static void CollectFor(ExpDefinition exp, IExperimentDataSource experimentDataSource,bool doOnCompleteActions = true)
        {
            CollectionResults resAll = new CollectionResults();

            List<long> accountIDs = exp.AdObjects.Select(a => a.AccountID).Distinct().ToList();

            foreach (var accountID in accountIDs)
            {
                var sourceType = EXPERIMENT_DATASOURCES.GOOGLEADS;
                switch (sourceType)
                {
                    case EXPERIMENT_DATASOURCES.GOOGLEADS:
                        var res1 = experimentDataSource.CollectAdsData(accountID, exp, doOnCompleteActions);
                        resAll.CurrentResults.AddRange(res1.CurrentResults);
                        resAll.PreviousResults.AddRange(res1.PreviousResults);
                        break;
                    case EXPERIMENT_DATASOURCES.FACEBOOKADS:
                        break;
                    default:
                        break;
                }
            }

            ExperimentsHelper.SaveResultsToDb(exp, resAll.CurrentResults, resAll.PreviousResults);
        }

        public static void UpdateExperimentStatus(int id, EXPERIMENT_STATUS experimentStatus)
        {
            var db = DataHelper.GetStoreYaEntities();
            var exp = db.Experiments.Where(e => e.ID == id).SingleOrDefault();
            if (exp == null)
            {
                throw new Exception("Cannot Find Experiment With Id:" + id);
            }
            exp.Status = experimentStatus.GetHashCode();
            db.SaveChanges();
        }

        public static Experiment GetExperiment(int id)
        {
            var db = DataHelper.GetStoreYaEntities();
            var exp = db.Experiments.Where(e => e.ID == id).SingleOrDefault();
            if (exp == null)
            {
                throw new Exception("Cannot Find Experiment With Id:" + id);
            }
            return exp;
        }

        public static EXPERIMENT_STATUS GetExperimentStatus(int id)
        {
            var db = DataHelper.GetStoreYaEntities();
            var exp = db.Experiments.Where(e => e.ID == id).SingleOrDefault();
            if (exp == null)
            {
                throw new Exception("Cannot Find Experiment With Id:" + id);
            }
            EXPERIMENT_STATUS status = (EXPERIMENT_STATUS)exp.Status;
            return status;
        }

        private static void SaveResultsToDb(ExpDefinition exp, List<ExpResultRow> results, List<ExpResultRow> resultsCompareTo)
        {
            using (var db = new StoreYaEntities())
            {
                var expRow = db.ExpResults.Where(e => e.ExpID == exp.ID).SingleOrDefault();
                if (expRow == null)
                {
                    var newRes = new ExpResult();
                    newRes.ExpID = exp.ID;
                    newRes.Spend = results.Sum(r => r.Spend);
                    newRes.Revenue = results.Sum(r => r.Revenue);
                    newRes.SpendCompareTo = resultsCompareTo.Sum(r => r.Spend);
                    newRes.RevenueCompareTo = resultsCompareTo.Sum(r => r.Revenue);
                    newRes.InsertedAt = DateTime.Now;

                    db.ExpResults.Add(newRes);
                    db.SaveChanges();
                }
                else
                {
                    expRow.Spend = results.Sum(r => r.Spend);
                    expRow.Revenue = results.Sum(r => r.Revenue);
                    expRow.SpendCompareTo = resultsCompareTo.Sum(r => r.Spend);
                    expRow.RevenueCompareTo = resultsCompareTo.Sum(r => r.Revenue);
                    expRow.UpdatedAt = DateTime.Now;
                    db.SaveChanges();
                }

                List<ExpResultRow> uniqueObject = GetObjects(results, resultsCompareTo);
                UpdateInternalObjectID(exp.ID, uniqueObject, db);


                foreach (var obj in uniqueObject)
                {
                    if (obj.InternalObjectID == 0)
                    {
                        continue;
                    }
                    ExpResultRow prevSum = new ExpResultRow();
                    var prevResults = resultsCompareTo.Where(r => r.CampaignID == obj.CampaignID && r.AdGroupID == obj.AdGroupID && r.AdID == obj.AdID).Select(r => r);
                    if (prevResults.Any())
                    {
                        prevSum.Spend = prevResults.Sum(r => r.Spend);
                        prevSum.Revenue = prevResults.Sum(r => r.Revenue);
                    }

                    SaveObjectResultsToDb(exp.ID, obj, prevSum, db);
                }
            }
        }

        private static void UpdateInternalObjectID(int expID, List<ExpResultRow> uniqueObject, StoreYaEntities db)
        {
            var objs = db.ExpAdsStructures.Where(e => e.ExpID == expID).ToList();
            foreach (var obj in uniqueObject)
            {
                var objRecord = objs.Where(o => o.CampaignID == obj.CampaignID && o.AdGroupID == obj.AdGroupID && o.AdID == obj.AdID).SingleOrDefault();
                if (objRecord != null)
                {
                    obj.InternalObjectID = objRecord.ID;
                }
            }
        }

        private static void SaveObjectResultsToDb(int expID, ExpResultRow obj, ExpResultRow prevSum, StoreYaEntities db)
        {
            var expRow = db.ExpResultsByObjects.Where(e => e.ExpID == expID && e.ObjID == obj.InternalObjectID).SingleOrDefault();
            if (expRow == null)
            {
                var newRes = new ExpResultsByObject();
                newRes.ExpID = expID;
                newRes.ObjID = obj.InternalObjectID;
                newRes.Spend = obj.Spend;
                newRes.Revenue = obj.Revenue;
                newRes.SpendCompareTo = prevSum.Spend;
                newRes.RevenueCompareTo = prevSum.Revenue;
                newRes.InsertedAt = DateTime.Now;

                db.ExpResultsByObjects.Add(newRes);
                db.SaveChanges();
            }
            else
            {
                expRow.Spend = obj.Spend;
                expRow.Revenue = obj.Revenue;
                expRow.SpendCompareTo = prevSum.Spend;
                expRow.RevenueCompareTo = prevSum.Revenue;
                expRow.UpdatedAt = DateTime.Now;

                db.SaveChanges();
            }
        }

        private static List<ExpResultRow> GetObjects(List<ExpResultRow> results, List<ExpResultRow> resultsCompareTo)
        {
            var currentResults = (from r in results
                                  select new ExpResultRow() { CampaignID = r.CampaignID, AdGroupID = r.AdGroupID, AdID = r.AdID, Revenue = r.Revenue, Spend = r.Spend })
                                 .Union((from r in resultsCompareTo
                                         select new ExpResultRow() { CampaignID = r.CampaignID, AdGroupID = r.AdGroupID, AdID = r.AdID })).ToList();
            //currentResults = currentResults.GroupBy(s=> new { }).Select(s=sbyte.);

            List<ExpResultRow> uniqueObjects = (from r in currentResults
                                                group r by new { r.CampaignID, r.AdGroupID, r.AdID } into g
                                                select new ExpResultRow() { CampaignID = g.Key.CampaignID, AdGroupID = g.Key.AdGroupID, AdID = g.Key.AdID, Revenue = g.Sum(r => r.Revenue), Spend = g.Sum(r => r.Spend) }).ToList();
            return uniqueObjects;
        }


        public static List<ExpDefinition> GetExperimentsToRun(int? expID = null)
        {
            return GetExperimentsToRun(null, expID);
        }

        public static List<ExpDefinition> GetExperimentsToRun(EXPERIMENT_DATASOURCES? dataSource = null, int? expID = null)
        {
            List<ExpDefinition> exps = new List<ExpDefinition>();
            var db = DataHelper.GetStoreYaEntities();
            List<Experiment> experirmrnts = null;
            if (expID.HasValue)
            {
                experirmrnts = db.Experiments.Where(s => s.Status == (int)EXPERIMENT_STATUS.ACTIVE && s.ID == expID).ToList();
            }
            else
            {
                experirmrnts = db.Experiments.Where(s => s.Status == (int)EXPERIMENT_STATUS.ACTIVE && s.ChannelType == (int)dataSource).ToList();
            }

            foreach (var e in experirmrnts)
            {
                ExpDefinition exp = new ExpDefinition();
                exp.ID = e.ID;
                exp.StartDate = e.StartDate.Value;
                exp.EndDate = e.EndDate.Value;
                exp.ExperimentType = (EXPERIMENT_TYPES)(e.ExpType ?? EXPERIMENT_TYPES.AA.GetHashCode());
                exp.DataLevel = (EXPERIMENT_DATALEVELS)(e.DataLevel ?? 1);
                exp.ChannelType = (EXPERIMENT_DATASOURCES)(e.ChannelType);
                exp.AdObjects = new List<ExpAdsStructureRow>();
                exp.AdObjectsCompareTo = new List<ExpAdsStructureRow>();
                foreach (var obj in db.ExpObjects.Where(a => a.ExpID == e.ID))
                {
                    var adStruct = db.ExpAdsStructures.Where(s => s.ID == obj.ObjectID).Single();
                    var filter = new ExpAdsStructureRow()
                    {
                        AccountID = adStruct.AccountID ?? 0,
                        CampaignID = adStruct.CampaignID ?? 0,
                        AdGroupID = adStruct.AdGroupID ?? 0,
                        AdID = adStruct.AdID ?? 0
                    };

                    if (obj.FilterType == FILTER_TYPES.TEST_GROUP.GetHashCode())
                    {
                        exp.AdObjects.Add(filter);
                    }
                    else
                    {
                        exp.AdObjectsCompareTo.Add(filter);
                    }

                }

                exps.Add(exp);
            }

            return exps;
        }

        public static ExpGroup GetGroup(int id)
        {
            var db = DataHelper.GetStoreYaEntities();
            var expGroup = db.ExpGroups.Where(g => g.ID == id).SingleOrDefault();
            if (expGroup == null)
            {
                return new ExpGroup()
                {
                    Name = string.Empty
                };
            }
            return expGroup;            
        }

        public static int ManageGroup(string name, string userName)
        {
            var db = DataHelper.GetStoreYaEntities();
            var expGroup = db.ExpGroups.Where(g => g.Name.ToLower() == name.ToLower().Trim()).SingleOrDefault();
            if (expGroup == null)
            {
                ExpGroup newExpGroup = new ExpGroup()
                {
                    CreatedBy = userName,
                    InsertedAt = DateTime.Now,
                    Name = name.Trim(),
                    Status = 1
                };
                db.ExpGroups.Add(newExpGroup);
                db.SaveChanges();
            }
            return expGroup.ID;
        }



        public static void RemoveExperiment(int id)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                var exp = db.Experiments.Where(e => e.ID == id).SingleOrDefault();
                if (exp == null)
                {
                    throw new Exception("Cannot Find Experiment With Id:" + id);
                }
                db.Experiments.Remove(exp);
                var expAdsStructures = db.ExpAdsStructures.Where(e => e.ExpID == id);
                if (expAdsStructures != null && expAdsStructures.Count() > 0)
                {
                    db.ExpAdsStructures.RemoveRange(expAdsStructures);
                }
                var expObjects = db.ExpObjects.Where(e => e.ExpID == id);
                if (expObjects != null && expObjects.Count() > 0)
                {
                    db.ExpObjects.RemoveRange(expObjects);
                }
                var expAccounts = db.ExpAccounts.Where(e => e.ExpID == id);
                if (expAccounts != null && expAccounts.Count() > 0)
                {
                    db.ExpAccounts.RemoveRange(expAccounts);
                }
                db.SaveChanges();
            }
            catch (Exception ex)
            {

                throw ex;
            }



            return;
        }
    }
}

