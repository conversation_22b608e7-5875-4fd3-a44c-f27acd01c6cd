﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Web;
using System.Globalization;
using System.IO;
using System.Text.RegularExpressions;
using System.Collections;
using System.Dynamic;

namespace Storeya.Core.Helpers
{
    public static class ExtentionsHelper
    {
        private const string TD_STYLE = "style='width:50%;font-size: 10pt; font-weight: normal; border: 1pt solid black'";
        public static int RoundOff(this int i, int roundUpTo = 10)
        {
            decimal d = roundUpTo;
            return ((int)Math.Round(i / d)) * roundUpTo;
        }


        public static long RoundOff(this long i, int roundUpTo = 10)
        {
            decimal d = roundUpTo;
            return ((long)Math.Round(i / d)) * roundUpTo;
        }

        public static string ToHtmlTable<T>(this T classObject)
        {
            return ToHtmlTable<T>(classObject, "");
        }
        public static object GetPropertyValueByName<T>(this T classObject, string propertyName)
        {
            if (propertyName.Contains("."))
            {
                string[] names = propertyName.ToLower().Split('.');

                var prop = classObject.GetType().GetProperties().Where(p => p.Name.ToLower().Equals(names[0])).SingleOrDefault();
                var subObject = prop.GetValue(classObject);
                if (subObject == null)
                {
                    return null;
                }
                var subProp = subObject.GetType().GetProperties().Where(p => p.Name.ToLower().Equals(names[1])).SingleOrDefault();
                if (subProp == null)
                {
                    return null;
                }
                return subProp.GetValue(subObject);
            }
            else
            {
                var prop = classObject.GetType().GetProperties().Where(p => p.Name.ToLower().Equals(propertyName.ToLower())).SingleOrDefault();
                return prop.GetValue(classObject);
            }
            //return null;
        }
        public static string GetPropertyNameByValue<T>(this T classObject, object valueToFind)
        {
            if (classObject == null || valueToFind == null)
                return null;

            // Get all properties of the object
            var properties = classObject.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

            // Loop through the properties to find one with the matching value
            foreach (var property in properties)
            {
                var propertyValue = property.GetValue(classObject);

                // Compare the values (use Equals to handle different types)
                if (propertyValue != null && propertyValue.Equals(valueToFind))
                {
                    return property.Name;
                }
            }

            return null; // Return null if no matching property is found
        }

        public static string ToHtmlTable<T>(this T classObject, string header)
        {
            var ret = string.Empty;
            try
            {
                return classObject == null
                    ? ret
                    : "<table><tr><th>" + (header ?? classObject.GetType().Name) + "<th><th></th></tr><tr><td " + TD_STYLE + "><b>Property</b></td><td " + TD_STYLE + "><b>Value</b></td></tr>" +
                      classObject.GetType()
                          .GetProperties()
                          .Aggregate(ret,
                              (current, prop) =>
                                  current + (prop.GetValue(classObject, null) == null ? string.Empty : ("<tr><td " + TD_STYLE + "><b>" + prop.Name.Trim()
                                               + ":</b></td><td " + TD_STYLE + ">" +
                                             ((prop.PropertyType.IsArray ? MergeArray(prop.GetValue(classObject, null), prop) : Convert.ToString(prop.GetValue(classObject, null)).Trim()))
                                                 + "</td></tr>"))) + "</table>";

                //(Convert.ToString(prop.GetValue(classObject, null)).Length <= 300
                //                                ? Convert.ToString(prop.GetValue(classObject, null))
                //                                : Convert.ToString(prop.GetValue(classObject, null)).Substring(0, 300) + "...") 
            }
            catch (Exception ex)
            {
                ret = ex.Message;
            }
            return ret;
        }

        public static string MergeArray(object classObject, PropertyInfo prop)
        {
            var arr = string.Empty;
            //prop.GetType().GetProperties().Aggregate(arr, (a, v) => a + "," + Convert.ToString(v.GetValue(classObject, null)));
            return arr;
        }
        static string ToUppercaseFirst(this string s)
        {
            if (string.IsNullOrEmpty(s))
            {
                return string.Empty;
            }
            s = s.ToLower();
            return char.ToUpper(s[0]) + s.Substring(1);
        }

        public static Dictionary<int, string> ToDictionary(this Enum e)
        {
            Dictionary<int, string> dic = new Dictionary<int, string>();
            foreach (var item in Enum.GetValues(e.GetType()))
            {
                dic.Add(item.GetHashCode(), item.ToString().ToUppercaseFirst().Replace("_", " "));
            }
            return dic;
        }
        public static string PrettyPrint(this Enum e)
        {
            string s = string.Empty;
            foreach (var item in Enum.GetValues(e.GetType()))
            {
                s = string.Format("{0} | {1}:{2}", s, item.GetHashCode(), item.ToString());
            }
            return s;
        }

        public static string ToHtmlTable<T>(this List<T> listOfClassObjects, TableMetaData tableMetaData = null, int maxValueLength = 1000)
        {
            var ret = string.Empty;

            return listOfClassObjects == null || !listOfClassObjects.Any()
                ? ret
                : "<table>" +
                  listOfClassObjects.First().GetType().GetProperties().Select(p => p.Name).ToList().ToColumnHeaders() +
                  listOfClassObjects.Aggregate(ret, (current, t) => current + t.ToHtmlTableRow(maxValueLength: maxValueLength, metaData: tableMetaData)) +
                  "</table>";
        }

        public static string ToColumnHeaders<T>(this List<T> listOfProperties)
        {
            var ret = string.Empty;

            return listOfProperties == null || !listOfProperties.Any()
                ? ret
                : "<tr>" +
                  listOfProperties.Aggregate(ret,
                      (current, propValue) =>
                          current +
                          ("<th style='font-size: 11pt; font-weight: bold; border: 1pt solid black'>" +
                           (Convert.ToString(propValue).Trim().Length <= 100
                               ? Convert.ToString(propValue)
                               : Convert.ToString(propValue).Substring(0, 100)) + "</th>")) +
                  "</tr>";
        }
        public class TableMetaData
        {
            public TableMetaData()
            {
                EnumsMetaDate = new Dictionary<string, Enum>();
            }
            public Dictionary<string, Enum> EnumsMetaDate { get; set; }


        }
        public static string ToHtmlTableRow<T>(this T classObject, bool noStyle = false, string style = null, int maxValueLength = 1000, TableMetaData metaData = null)
        {
            var ret = string.Empty;
            if (string.IsNullOrEmpty(style))
            {
                style = "style='font-size: 11pt; font-weight: normal; border: 1pt solid black'";
            }
            if (noStyle)
            {
                style = string.Empty;
            }
            return classObject == null
                ? ret
                : "<tr>" +
                  classObject.GetType()
                      .GetProperties()
                      .Aggregate(ret,
                          (current, prop) =>
                              current + ("<td " + style + ">" +
                                         (Convert.ToString(prop.GetValue(classObject, null)).Length <= maxValueLength
                                             ? ConvertToValue(prop.GetValue(classObject, null), prop.Name, metaData)
                                             : Convert.ToString(prop.GetValue(classObject, null)).Substring(0, maxValueLength) +
                                               "...") +
                                         "</td>")) + "</tr>";
        }
        public static void ToCSV<T>(this IEnumerable<T> list, string filePath, int maxValueLength = 1000, string seprator = ",")
        {
            var sb = new StringBuilder();
            if (list.Count() == 0)
            {
                File.WriteAllText(filePath, sb.ToString());
                return;
            }
            string co = list.First().GetType().GetProperties().Select(p => p.Name).ToList().ToCSVHeaders(seprator);
            sb.Append(co);
            foreach (var item in list)
            {
                string ro = ToCSVRow<T>(item, maxValueLength, seprator);
                sb.Append(ro);
            }
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            File.WriteAllText(filePath, sb.ToString());

        }
        public static List<System.Net.Mail.Attachment> ToCSVAttatchment<T>(this IEnumerable<T> list, string filePath, int maxValueLength = 1000, string seprator = ",")
        {
            list.ToCSV(filePath, maxValueLength, seprator);
            Stream stream = new MemoryStream(File.ReadAllBytes(filePath));

            System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(stream, Path.GetFileName(filePath));
            List<System.Net.Mail.Attachment> attachments = new List<System.Net.Mail.Attachment>() {
                            attachment
                        };
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            return attachments;
        }
        private static string ToCSVHeaders<T>(this List<T> listOfProperties, string seprator = ",")
        {
            var ret = string.Empty;

            return listOfProperties == null || !listOfProperties.Any()
                ? ret
                :
                  listOfProperties.Aggregate(ret,
                      (current, propValue) =>
                          current +
                          (
                           (Convert.ToString(propValue).Trim().Length <= 100
                               ? Convert.ToString(propValue)
                               : Convert.ToString(propValue).Substring(0, 100)) + seprator)) +
                  Environment.NewLine;
        }

        private static string ToCSVRow<T>(this T classObject, int maxValueLength = 1000, string seprator = ",")
        {
            var ret = string.Empty;

            return classObject == null
                ? ret
                : classObject.GetType()
                      .GetProperties()
                      .Aggregate(ret,
                          (current, prop) =>
                              current + (Convert.ToString(prop.GetValue(classObject, null)).Length <= maxValueLength
                                             ? ConvertToValue(prop.GetValue(classObject, null), prop.Name, null)
                                             : Convert.ToString(prop.GetValue(classObject, null)).Substring(0, maxValueLength) +
                                               "...") + seprator) + Environment.NewLine;
        }

        private static string ConvertToValue(object value, string propertyName, TableMetaData metaData)
        {
            if (metaData != null)
            {
                propertyName = propertyName.ToLower();
                try
                {
                    if (metaData.EnumsMetaDate.ContainsKey(propertyName))
                    {
                        Enum v = metaData.EnumsMetaDate[propertyName];
                        int hashCode = int.Parse(Convert.ToString(value));
                        return Enum.GetName(v.GetType(), hashCode);
                    }
                    if (propertyName == "shopid")
                    {
                        return EmailHelper.AdminLinkHref(int.Parse(Convert.ToString(value)));
                    }
                }
                catch
                {
                }
            }

            return Convert.ToString(value);
        }
        public static string EnumToNice(this Enum val)
        {
            return string.Concat(val.ToString().Select(x => Char.IsUpper(x) ? " " + x : x.ToString())).TrimStart(' ');
        }
        public static string ListToHtmlTable<T>(this IEnumerable<T> list)
        {
            if(list.Count() == 0)
                return string.Empty;
            var sb = new StringBuilder();
            string co = list.First().GetType().GetProperties().Select(p => p.Name).ToList().ToColumnHeaders();
            sb.Append("<table>" + co);
            foreach (var item in list)
            {
                string ro = ToHtmlTableRow<T>(item);
                sb.Append(ro);
            }
            sb.Append("</table>");
            return sb.ToString();
        }
        public static string ToUpperEnum(this string s)
        {
            string res = "";
            foreach (var c in s)
            {
                if (char.IsUpper(c))
                {
                    res += "_";
                }
                res += c.ToString();
            }
            return res.ToUpper().TrimStart("_".ToCharArray());
        }
        public static string ToPretyPrint<T>(this T classObject)
        {
            var ret = string.Empty;
            return classObject == null
                ? ret :
                  classObject.GetType()
                      .GetProperties()
                      .Aggregate(ret, (current, prop) =>
                               current + (Convert.ToString(prop.Name).Trim() + "=" +
                                          (Convert.ToString(prop.GetValue(classObject, null)).Length <= 1000
                                              ? Convert.ToString(prop.GetValue(classObject, null))
                                              : Convert.ToString(prop.GetValue(classObject, null)).Substring(0, 1000) +
                                                "...")
                                          + ",") + "\n\r" + Environment.NewLine);
        }

        public static string ToJson<T>(this IEnumerable<T> list)
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(list);
        }
        public static T Clone<T>(this T obj, bool ignoreIfNull = false) where T : class
        {
            return obj.ToJson().FromJson<T>();
        }
        public static string ToFormattedJson<T>(this T obj, bool ignoreIfNull = false)
        {
            return obj.ToJson(ignoreIfNull, Formatting.Indented);
        }
        public static string ToJson<T>(this T obj, bool ignoreIfNull = false, Formatting format = Formatting.None)
        {
            if (ignoreIfNull)
            {
                return Newtonsoft.Json.JsonConvert.SerializeObject(obj, format, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            return Newtonsoft.Json.JsonConvert.SerializeObject(obj, format);
        }

        public static T FromJson<T>(this string json, bool ignoreIfNull = false) where T : class
        {
            if (ignoreIfNull)
            {
                return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(json, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(json);
        }
        public static bool DynamicHasProperty(ExpandoObject expando, string propertyName)
        {
            IDictionary<string, object> dictionary = expando;

            // Check if the property name exists in the dictionary
            return dictionary.ContainsKey(propertyName);
        }
        public static List<dynamic> FromCSV(this string csvData, string delimiter = ",")
        {

            List<dynamic> records = new List<dynamic>();

            string[] lines = csvData.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            if (lines.Length < 2)
            {
                // Handle the case when there are not enough lines in the CSV data.
                return records;
            }
            char del = delimiter.ToCharArray()[0];
            string[] headers = lines[0].Split(del);
            for (int i = 1; i < lines.Length; i++)
            {
                string[] values = ParseCsvLine(lines[i], del);
                if (values.Length != headers.Length)
                {
                    // Handle the case when the number of values doesn't match the number of headers.
                    continue;
                }

                dynamic record = new ExpandoObject();
                for (int j = 0; j < headers.Length; j++)
                {
                    string header = headers[j].Trim();
                    string value = values[j].Trim();
                    ((IDictionary<string, object>)record)[header] = value;
                }
                records.Add(record);
            }

            return records;


        }

        static string[] ParseCsvLine(string line, char delemiter)
        {
            List<string> values = new List<string>();
            bool inQuotes = false;
            string current = "";
            foreach (char c in line)
            {
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == delemiter && !inQuotes)
                {
                    values.Add(current);
                    current = "";
                }
                else
                {
                    current += c;
                }
            }

            values.Add(current); // Add the last value
            return values.ToArray();
        }
        public static bool IsValidEmail(this string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return false;
            }

            // Regular expression pattern to match a valid email address
            string emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";

            // Use Regex.IsMatch to validate the email format
            return Regex.IsMatch(email, emailPattern, RegexOptions.IgnoreCase);
        }
        public static string StripEmojiCharacters(this string inputValue)
        {
            return System.Text.RegularExpressions.Regex.Replace(inputValue, @"\p{Cs}", "");
        }
        public static int EmojiCharactersLength(this string inputValue)
        {
            var m = System.Text.RegularExpressions.Regex.Matches(inputValue, @"\p{Cs}");
            if (m == null) return 0;
            return m.Count;

        }
        public static long ToJSDateTime(this DateTime date)
        {
            return Convert.ToInt64(date.Subtract(new DateTime(1970, 1, 1)).TotalMilliseconds);
        }

        public static DateTime FromJSDateTime(this long jSDateTime)
        {
            return new DateTime(1970, 01, 01).AddMilliseconds(jSDateTime);
        }
        public static string RemoveReturningChar(this string inputValue, char charToCheck)
        {
            int count = inputValue.Count(c => c.Equals(charToCheck));
            if (count > 1)
            {
                string b = string.Empty;
                var a = inputValue.Split(new char[] { charToCheck });
                for (int i = 0; i < a.Length; i++)
                {
                    if (i == a.Length - 1)
                    {
                        b = b + charToCheck + " " + a[i];
                    }
                    else
                    {
                        b = b + a[i];
                    }

                }
                return b;
            }
            return inputValue;
        }

        public static int LengthUTF8(this string s)
        {
            return Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(s)).Length;
        }
        public static string SubString2(this string target, int start, int end, bool add3Dots = false)
        {
            if (target == null)
            {
                return null;
            }
            if (target.Length > end)
            {
                if (add3Dots)
                {
                    return target.Substring(start, end) + "...";
                }
                return target.Substring(start, end);
            }
            return target;
        }

        public static int GetRandomFromTicks(this DateTime target, int returnLength = 4)
        {
            string ticks = target.Ticks.ToString();
            return int.Parse(ticks.Substring(ticks.Length - returnLength));
        }
        public static T MergeChanges<T>(this T source, T target, List<string> changeTracking)
        {
            List<PropertyInfo> targetProperties = target.GetType().GetProperties().ToList();
            string sourcePropertyName = string.Empty;
            foreach (var sourceProperty in source.GetType().GetProperties())
            {
                object s = sourceProperty.GetValue(source, null);
                sourcePropertyName = sourceProperty.Name;
                PropertyInfo targetProperty = targetProperties.Single(p => p.Name == sourcePropertyName);
                object t = targetProperty.GetValue(target, null);
                sourcePropertyName = sourceProperty.Name;
                try
                {
                    string oldValForLog = "";
                    string newValForLog = "";
                    if (t != null)
                    {
                        if (t.ToString().Contains("System.Data.Entity")
                           || t.ToString().Contains("System.Collections"))
                        {
                            continue;
                        }
                    }
                    if (s == null && t != null)
                    {
                        oldValForLog = HttpUtility.HtmlEncode(t);
                        changeTracking.Add(string.Format("Property:{0}, Old Value:{1}, New Value:{2}</br>", sourcePropertyName, oldValForLog, ""));
                        targetProperty.SetValue(target, null);
                    }
                    else if (s != null)
                    {
                        if (!s.Equals(t))
                        {
                            newValForLog = HttpUtility.HtmlEncode(s);
                            if (t != null)
                            {
                                if (!s.ToString().Equals(t.ToString()))
                                {
                                    oldValForLog = HttpUtility.HtmlEncode(t);
                                    changeTracking.Add(string.Format("Property:{0}, Old Value:{1}, New Value:{2}</br>", sourcePropertyName, oldValForLog, newValForLog));
                                    targetProperty.SetValue(target, s);
                                }
                            }
                            else
                            {
                                changeTracking.Add(string.Format("Property:{0}, Old Value:{1}, New Value:{2}</br>", sourcePropertyName, oldValForLog, newValForLog));
                                targetProperty.SetValue(target, s);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Issue Handling Property Name :" + sourcePropertyName, ex);
                }
            }
            return target;
        }

        public static T MergeChanges<T>(this T target, object source)
        {
            List<PropertyInfo> targetProperties = target.GetType().GetProperties().ToList();
            string sourcePropertyName = string.Empty;
            foreach (var sourceProperty in source.GetType().GetProperties())
            {
                object s = sourceProperty.GetValue(source, null);
                PropertyInfo targetProperty = targetProperties.SingleOrDefault(p => p.Name == sourceProperty.Name);
                if (targetProperty != null)
                {
                    object t = targetProperty.GetValue(target, null);
                    sourcePropertyName = sourceProperty.Name;
                    try
                    {
                        if (s == null && t != null)
                        {

                            targetProperty.SetValue(target, null);
                        }
                        else if (s != null)
                        {
                            if (!s.Equals(t))
                            {
                                if (t != null)
                                {
                                    if (!s.ToString().Equals(t.ToString()))
                                    {
                                        targetProperty.SetValue(target, s);
                                    }
                                }
                                else
                                {
                                    targetProperty.SetValue(target, s);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Issue Handling Property Name :" + sourcePropertyName, ex);
                    }
                }
            }
            return target;
        }

        private static readonly HashSet<char> DefaultNonWordCharacters = new HashSet<char> { ',', '.', ':', ';' };
        public static string TruncateAtWord(this string value, int length, HashSet<char> nonWordCharacters = null)
        {
            if (value == null)
            {
                throw new ArgumentNullException("value");
            }

            if (length < 0)
            {
                throw new ArgumentException("Negative values not allowed.", "length");
            }

            if (nonWordCharacters == null)
            {
                nonWordCharacters = DefaultNonWordCharacters;
            }

            if (length >= value.Length)
            {
                return value;
            }
            int end = length;

            for (int i = end; i > 0; i--)
            {
                if (value[i].IsWhitespace())
                {
                    break;
                }
                if (nonWordCharacters.Contains(value[i]) && (value.Length == i + 1 || value[i + 1] == ' '))
                {
                    //Removing a character that isn't whitespace but not part 
                    //of the word either (ie ".") given that the character is 
                    //followed by whitespace or the end of the string makes it
                    //possible to include the word, so we do that.
                    break;
                }
                end--;
            }
            if (end == 0)
            {
                //If the first word is longer than the length we favor 
                //returning it as cropped over returning nothing at all.
                end = length;
            }
            return value.Substring(0, end);
        }

        private static bool IsWhitespace(this char character)
        {
            return character == ' ' || character == 'n' || character == 't';
        }
        public static int WordCount(this string s)
        {
            string[] words = s.Split(Convert.ToChar(" "));
            return words.Count();
        }
        public static int WordCount(this string s, string wordToCount)
        {
            string[] words = s.Split(Convert.ToChar(" "));
            return words.Where(x => x == wordToCount).Count();
        }
        public static string UrlEncode(this string url)
        {
            return System.Web.HttpUtility.UrlEncode(url);
        }

        public static bool ContainsWithWildCard(this string inputValue, string pattern)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(inputValue, RegexHelper.WildCardToRegular(pattern));
        }
        public static string ConvertEncodingToUtf8(this string inputValue, int codePage)
        {
            Encoding wind1252 = Encoding.GetEncoding(codePage);
            Encoding utf8 = Encoding.UTF8;
            byte[] wind1252Bytes = wind1252.GetBytes(inputValue);
            byte[] utf8Bytes = Encoding.Convert(wind1252, utf8, wind1252Bytes);
            return Encoding.UTF8.GetString(utf8Bytes);
        }

        public static string ToTitleCase(this string inputValue, bool onlyIfAllUpperCase = false)
        {
            if (string.IsNullOrEmpty(inputValue))
            {
                return inputValue;
            }
            if (onlyIfAllUpperCase)
            {
                if (inputValue.All(c => char.IsUpper(c) || char.IsWhiteSpace(c)))
                {
                    return CultureInfo.InvariantCulture.TextInfo.ToTitleCase(inputValue.ToLower());
                }
                return inputValue;
            }
            return CultureInfo.InvariantCulture.TextInfo.ToTitleCase(inputValue.ToLower());
        }

        public static string GetSubstringBetweenStrings(this string inputValue, string a, string b)
        {
            try
            {
                return inputValue.SubString2((inputValue.IndexOf(a) + a.Length), (inputValue.IndexOf(b) - inputValue.IndexOf(a) - a.Length));
            }
            catch
            {
                return null;
            }

        }
        public static List<string> GetSubstringsBetweenStrings(this string inputValue, string a, string b)
        {
            List<string> res = RegexHelper.GetAllGroupValues($"{a}(.*?)\\{b}", inputValue);
            List<string> nres = new List<string>();
            foreach (var item in res)
            {
                nres.Add(item.GetSubstringBetweenStrings(a, b));
            }
            return nres;
        }

        public static bool IsValuehInRange(this decimal amount, decimal amount2, decimal precentageToCheck = 0.1M, decimal rangeToCheck = 0)
        {
            decimal maxAmount = Math.Max(amount, amount2);
            decimal diff = Math.Abs(amount - amount2);
            //change is more than 10% and amount if > $6 
            if (rangeToCheck == 0)
            {
                if ((diff / maxAmount) > precentageToCheck)
                {
                    return false;
                }
            }
            else
            {

                if ((diff / maxAmount) > precentageToCheck && Math.Abs(amount - amount2) > rangeToCheck)
                {
                    return false;
                }
            }
            return true;
        }
        public static bool IsLargerThen(this double amount, double amount2, double precentageToCheck = 0.1D)
        {
            if (amount > amount2)
            {
                double diff = amount - amount2;
                //change is more than 10% and amount if > $6 

                if ((diff / amount) > precentageToCheck)
                {
                    return true;
                }
            }
            return false;
        }
        public static bool IsLargerThen(this decimal amount, decimal amount2, decimal precentageToCheck = 0.1M)
        {
            if (amount > amount2)
            {
                decimal diff = amount - amount2;
                //change is more than 10% and amount if > $6 

                if ((diff / amount) > precentageToCheck)
                {
                    return true;
                }
            }
            return false;
        }
        public static string ToPriceString(this decimal amount, string currencySymbol = "$")
        {
            return $"{currencySymbol}{amount.ToString("#,##")}";
        }

        public static string StripHTMLTags(this string input)
        {
            return Regex.Replace(input, "<.*?>", String.Empty);
        }
        public static bool IsAmountInRange(this decimal amount, decimal amount2, decimal percentageToCheck = 0.1M, decimal rangeToCheck = 6)
        {
            decimal maxAmount = Math.Max(amount, amount2);
            decimal diff = Math.Abs(amount - amount2);
            if (diff == 0 && amount2 == 0 && rangeToCheck == 0)
            {
                return true;
            }
            if ((diff == 0 || amount2 == 0) && Math.Abs(amount - amount2) > rangeToCheck)
            {
                return true;
            }
            // change is more than 10% and amount if > $6 
            if (maxAmount != 0 && (diff / maxAmount) > percentageToCheck && Math.Abs(amount - amount2) > rangeToCheck)
            {
                return false;
            }
            return true;
        }

        public static DateTime GetNextDateForDay(this DateTime dateTime, DayOfWeek dayOfWeek)
        {
            int daysUntil = ((int)dayOfWeek - (int)dateTime.DayOfWeek + 7) % 7;
            DateTime nextDay = dateTime.AddDays(daysUntil);
            return nextDay;
        }

        public static DateTime GetDateTimeInTimeZone(this DateTime datetime, string timeZoneName)
        {
            TimeZoneInfo tst = TimeZoneInfo.FindSystemTimeZoneById(timeZoneName);
            DateTime tstTime = TimeZoneInfo.ConvertTime(datetime, TimeZoneInfo.Local, tst);
            return tstTime;
        }

        public static T[] RemoveByValue<T>(this T[] array, T value)
        {
            int index = Array.IndexOf(array, value);
            while (index != -1)
            {
                T[] newArray = new T[array.Length - 1];
                Array.Copy(array, 0, newArray, 0, index);
                Array.Copy(array, index + 1, newArray, index, array.Length - index - 1);
                array = newArray;
                index = Array.IndexOf(array, value);
            }
            return array;
        }

        public static decimal ToDecimal(this string s, NumberStyles numberStyle = NumberStyles.None, string culture = "en-US")
        {
            if (string.IsNullOrEmpty(s))
            {
                return 0;
            }
            if (numberStyle != NumberStyles.None)
            {
                return decimal.Parse(s, numberStyle, CultureInfo.GetCultureInfo(culture));
            }
            return decimal.Parse(s, CultureInfo.GetCultureInfo(culture));
        }
        public static decimal ToDecimal(this double d, NumberStyles numberStyle = NumberStyles.None, string culture = "en-US")
        {
            if (numberStyle != NumberStyles.None)
            {
                return decimal.Parse(d.ToString(), numberStyle, CultureInfo.GetCultureInfo(culture));
            }
            return decimal.Parse(d.ToString(), CultureInfo.GetCultureInfo(culture));
        }

        public static double ToDouble(this string s, NumberStyles numberStyle = NumberStyles.None, string culture = "en-US")
        {
            if (string.IsNullOrEmpty(s))
            {
                return 0;
            }
            if (numberStyle != NumberStyles.None)
            {
                return double.Parse(s, numberStyle, CultureInfo.GetCultureInfo(culture));
            }
            return double.Parse(s, CultureInfo.GetCultureInfo(culture));
        }
        public static double ToDouble(this decimal d, NumberStyles numberStyle = NumberStyles.None, string culture = "en-US")
        {
            if (numberStyle != NumberStyles.None)
            {
                return double.Parse(d.ToString(), numberStyle, CultureInfo.GetCultureInfo(culture));
            }
            return double.Parse(d.ToString(), CultureInfo.GetCultureInfo(culture));
        }
        public static string Base64Encode(this string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }
        public static string Base64Decode(this string base64EncodedData)
        {
            var base64EncodedBytes = System.Convert.FromBase64String(AddBase64Padding(base64EncodedData));
            return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
        }
        private static string AddBase64Padding(string base64)
        {
            int padding = base64.Length % 4;
            if (padding > 0)
            {
                base64 = base64.PadRight(base64.Length + (4 - padding), '=');
            }
            return base64;
        }
        public static string TryBase64Decode(this string base64EncodedData)
        {
            base64EncodedData = AddBase64Padding(base64EncodedData);
            try
            {
                var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
                return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
            }
            catch (Exception ex)
            {
                throw ex;

            }

        }
        public static bool IsValidUrl(this string url)
        {
            string pattern = "^(https?://)?([\\w.-]+)\\.([a-z]{2,6})(/[\\w.-]*)*/?$";
            Regex regex = RegexHelper.GetInstance(pattern);
            return regex.IsMatch(url);
        }
        public static Dictionary<K, V> ToDictionary<K, V>(this Hashtable table)
        {
            return table
              .Cast<DictionaryEntry>()
              .ToDictionary(kvp => (K)kvp.Key, kvp => (V)kvp.Value);
        }

        public static decimal ToPrecantage(this decimal num, decimal num1)
        {
            decimal pres = 0;
            if (num > 0 && num1 > 0)
            {
                pres = Math.Round(num / num1, 2) * 100;
            }
            return pres;
        }

        public static DateTime? FromUnixDateTime(this long unixTimeStamp)
        {
            if (unixTimeStamp < 1)
            {
                return null;
            }
            // Unix timestamp is seconds past epoch
            System.DateTime dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc);

            unixTimeStamp = unixTimeStamp / 1000;
            dtDateTime = dtDateTime.AddSeconds(unixTimeStamp).ToLocalTime();

            return dtDateTime;
        }

        public static bool CompareAsDateTime(this string str, string dateAsString, int allowedDiffInSec = 60)
        {
            try
            {
                var allowedDifference = TimeSpan.FromSeconds(allowedDiffInSec);
                var date1 = Convert.ToDateTime(str);
                var date2 = Convert.ToDateTime(dateAsString);

                return Math.Abs((date1 - date2).TotalSeconds) <= allowedDifference.TotalSeconds;

            }
            catch
            {
                return false;

            }

        }

        public static string ToShopifyDateTimeFormat(this DateTime dateTime, string prefix = null)
        {
            if (!string.IsNullOrEmpty(prefix))
            {
                prefix = $"{prefix} ";
            }
            DateTime now = DateTime.Now;
            TimeSpan timeDiff = now - dateTime;

            if (timeDiff.TotalSeconds < 60)
            {
                return $"{prefix}just now";
            }
            else if (timeDiff.TotalMinutes < 60)
            {
                return $"{prefix}{(int)timeDiff.TotalMinutes} minutes ago";
            }
            else if (dateTime.Date == now.Date)
            {
                return $"{prefix}at " + dateTime.ToString("h:mm tt", CultureInfo.InvariantCulture);
            }
            else if (dateTime.Date == now.Date.AddDays(-1))
            {
                return $"{prefix}yesterday at {dateTime:h:mm tt}";
            }
            else if (timeDiff.TotalDays < 7)
            {

                return $"{prefix}at {dateTime:ddd} at {dateTime:h:mm tt}";
            }
            else if (timeDiff.TotalDays < 365)
            {

                return $"{prefix}at " + dateTime.ToString("ddd, MMM d, yyyy 'at' h:mm tt", CultureInfo.InvariantCulture);
            }
            else
            {

                return $"{prefix}at " + dateTime.ToString("MMM d, yyyy", CultureInfo.InvariantCulture);
            }
        }




        public static bool CompareDateTime(this DateTime date1, DateTime date2, int allowedDiffInMin = 1)
        {
            try
            {
                var allowedDifference = TimeSpan.FromMinutes(allowedDiffInMin);
                return Math.Abs((date1 - date2).TotalMinutes) <= allowedDifference.TotalMinutes;
            }
            catch
            {
                return false;

            }

        }
        public static bool CompareAsDecimal(this string str, string dateAsString, decimal rangeToCheck = 1)
        {
            try
            {
                var d1 = Convert.ToDecimal(str);
                var d2 = Convert.ToDecimal(dateAsString);
                return d1.IsValuehInRange(d2, rangeToCheck: rangeToCheck);

            }
            catch
            {
                return false;

            }

        }

        public static string FixStringForGraphQL(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return input
                .Replace("\\", "\\\\")  // Escape backslashes
                .Replace("\"", "\\\"")  // Escape double quotes
                .Replace("\n", "\\n")   // Escape newlines
                .Replace("\r", "\\r")   // Escape carriage returns
                .Replace("\t", "\\t");  // Escape tabs
        }

        public static string ConvertToAWAccountString(this string input)
        {
            string formattedNumber = input;
            // Ensure the number is padded to 10 digits
            if (long.TryParse(input, out var result))
            {
                string numberString = result.ToString("D10");
                // Format it using Substring
                formattedNumber = $"{numberString.Substring(0, 3)}-{numberString.Substring(3, 3)}-{numberString.Substring(6, 4)}";
            }
            return formattedNumber;
        }

        public static string SmartTrim(this string input, int maxLength, int minLength)
        {
            int hardCutLength = maxLength - 3;
            if (string.IsNullOrEmpty(input) || input.Length <= maxLength)
                return input;

            try
            {
                // Try to cut at the last "." before maxLength
                int dotIndex = input.LastIndexOf('.', Math.Min(maxLength, input.Length - 1));
                if (dotIndex >= 0)
                {
                    string trimmed = input.Substring(0, dotIndex + 1); // Include the dot
                    if (trimmed.Length >= minLength)
                        return trimmed;
                }

                // Fallback: cut to last full word before hardCutLength
                int safeCutLength = Math.Min(hardCutLength, input.Length);
                string fallback = input.Substring(0, safeCutLength);

                int spaceIndex = fallback.LastIndexOf(' ');
                if (spaceIndex > 0)
                {
                    fallback = fallback.Substring(0, spaceIndex);
                }

                return fallback.TrimEnd() + "...";
            }
            catch (ArgumentOutOfRangeException)
            {
                // Final fallback if something goes wrong
                return input.Length > 3 ? input.SubString2(0, Math.Min(input.Length, hardCutLength), true) : input;
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError($"SmartTrim: {input} - maxLength:{maxLength}, minLength:{minLength}", ex, 0);
            }
            return input;
        }

        public static TEnum? GetEnumByName<TEnum>(string name) where TEnum : struct, Enum
        {
            if (Enum.TryParse<TEnum>(name, ignoreCase: true, out var result))
            {
                return result;
            }
            return null;
        }
    }
}
