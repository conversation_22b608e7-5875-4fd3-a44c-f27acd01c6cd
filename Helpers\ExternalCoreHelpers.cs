﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public static class ExternalCoreHelpers
    {
        public static List<string> GetSiteNetworkRequests(string url)
        {
            string targetUrl = url;

            string exePath = ConfigHelper.GetValue("StoreYaBrowserHeadlessBrowser", @"C:\Applications\StoreYaBrowser\StoreYaBrowser.exe");

            string args = $"\"getlist:{targetUrl}\"";
            return ExecuteConsoleApp(exePath, args);
        }

        public static List<string> ExecuteConsoleAppByRelativePath(string exePath, string args, bool showInConsole = false)
        {
            string targetPath = exePath;
            if (!Path.IsPathRooted(exePath))
            {
                string appsRoot = ConfigHelper.GetValue("ApplicationsRoot", @"C:\Applications\");
                targetPath = Path.Combine(appsRoot, exePath);
            }
            return ExecuteConsoleApp(targetPath, args, showInConsole);
        }

        public static List<string> ExecuteConsoleApp(string exePath, string args, bool showInConsole = false)
        {
            string workingDirectory = Path.GetDirectoryName(exePath);
            List<string> requests = new List<string>();
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    WorkingDirectory = workingDirectory,
                    Arguments = args,
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            process.Start();
            while (!process.StandardOutput.EndOfStream)
            {
                string line = process.StandardOutput.ReadLine();
                if (showInConsole)
                {
                    Console.WriteLine(line);
                }
                requests.Add(line.ToLower());
            }
            process.WaitForExit();
            return requests;
        }
    }
}
