﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class FacebookB2BLeadsHelper
    {

        public static List<string> GetLastPeriodFbLeadsEmails(int daysAgo = 30)
        {
            var db = DataHelper.GetStoreYaEntities();
            DateTime fromDate = DateTime.Today.AddDays(-daysAgo);
            List<string> emails = db.FacebookB2BLeads.Where(x => x.InsertedAt > fromDate).Select(x => x.Email).ToList();
            return emails;
        }
    }
}
