﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text.RegularExpressions;
using Storeya.Core.Entities.Facebook;
using System.Net;
using System.IO;
using System.Runtime.Serialization.Json;
using System.Text;
using Storeya.Core.Models.TrafficBoosterModels;

namespace Storeya.Core.Helpers
{
    public class FbHelper
    {
        //private static string _fbVersion = ConfigHelper.GetValue("FacebookVersion");


        //public static string GetFbVersion()
        //{
        //    return _fbVersion;
        //}


        public static string ProfileImage(long profileID)
        {
            return "/common/images/missing_user_men_50X50.jpg";
            //if (profileID == -1 || profileID == 1)
            //{
            //    return "/common/images/missing_user_men_50X50.jpg";
            //}
            //return string.Format(@"https://graph.facebook.com/{0}/{1}/picture", 0, profileID);
        }

        //public static string ProfileImage(long profileID, string type)
        //{
        //    if (profileID == 0)
        //    {
        //        return "/common/images/missingface_m.gif";
        //    }
        //    return string.Format(@"https://graph.facebook.com/{0}/{1}/picture?type={2}", _fbVersion, profileID, type);
        //}

        public static string ProfileUrl(long profileID)
        {
            string pattern = @"https://www.facebook.com/profile.php?id={0}";
            return string.Format(pattern, profileID.ToString());
        }

        public static string PageUrl(long? pageID)
        {
            return ProfileUrl(pageID ?? 0);
        }

        public static string UserNameToDisplay(long profileID)
        {
            if (profileID == 0)
            {
                return "Anonimous";
            }
            else
            {
                return "Profile ID: " + profileID.ToString();
            }
        }

        public static string GetProfileIdOrUrl(string profileUrl)
        {
            if (!Uri.IsWellFormedUriString(profileUrl, UriKind.Absolute))
            {
                return "";
                //throw new Exception("wrong url");
            }
            Uri uri = new Uri(profileUrl, UriKind.Absolute);
            if (!uri.Host.Contains("facebook."))
            {
                return "";
                //throw new Exception("wrong url");
            }

            string urlWithoutDomain = uri.PathAndQuery + uri.Fragment;
            if (urlWithoutDomain.Contains("#"))
            {
                urlWithoutDomain = urlWithoutDomain.Substring(urlWithoutDomain.IndexOf("#"));
            }

            Regex reg = RegexHelper.GetInstance(@"profile\.php\?id=(?<profileID>[\d]+)");
            if (reg.IsMatch(urlWithoutDomain)) //get profile ID
            {
                return reg.Matches(urlWithoutDomain)[0].Groups["profileID"].Value;
            }
            else //try extract link
            {
                Regex reg2 = RegexHelper.GetInstance(@"[\w\.]+");
                if (reg2.IsMatch(urlWithoutDomain))
                {
                    return reg2.Matches(urlWithoutDomain)[0].Groups[0].Value;
                }
            }

            return "";
        }

        //public static List<FacebookPage> GetUsersPages(string token)
        //{
        //    List<FacebookPage> pages = new List<FacebookPage>();

        //    if (string.IsNullOrEmpty(token))
        //    {
        //        return pages;
        //    }

        //    var app = new FacebookClient(token);
        //    dynamic pagesResponse = app.Get("me/accounts");
        //    foreach (dynamic page in pagesResponse.data)
        //    {
        //        long id = Convert.ToInt64(page.id);
        //        string name = Convert.ToString(page.name);
        //        pages.Add(new FacebookPage() { ID = id, Name = name });
        //    }

        //    return pages.OrderBy(p => p.Name).ToList();
        //}


        //public static TabAppInstallationResponseCode InstallAppTab(long pageID, string appID, string token)
        //{
        //    //https://graph.facebook.com/PAGE_ID?fields=access_token&access_token=ACCESS_TOKEN
        //    var app = new FacebookClient(token);
        //    dynamic pagesResponse = app.Get(string.Format("{0}?fields=access_token&access_token={1}", pageID, token));
        //    string pageToken = pagesResponse.access_token;

        //    bool isAlreadyInstalled = IsAlreadyInstalled(pageID, appID, pageToken);
        //    if (isAlreadyInstalled)
        //        return TabAppInstallationResponseCode.AlreadyInstalled;

        //    //https://graph.facebook.com/PAGE_ID/tabs?app_id=APP_ID&method=POST&access_token=PAGE_ACCESS_TOKEN
        //    WebClient client = new WebClient();
        //    client.DownloadData(string.Format("https://graph.facebook.com/{0}/{1}/tabs?app_id={2}&method=POST&access_token={3}", _fbVersion, pageID, appID, pageToken));
        //    return TabAppInstallationResponseCode.InstalledSuccessfully;
        //}

        //private static bool IsAlreadyInstalled(long pageID, string appID, string pageToken)
        //{
        //    bool isAlreadyInstalled = false;
        //    Storeya.Core.Entities.Facebook.RootObject responseData = null;
        //    string getAllTabsRequestString = string.Format("https://graph.facebook.com/{0}/{1}/tabs?method=GET&access_token={2}", _fbVersion, pageID, pageToken);
        //    WebClient client2 = new WebClient();
        //    try
        //    {
        //        string content = client2.DownloadString(getAllTabsRequestString);
        //        using (MemoryStream ms = new MemoryStream(Encoding.Unicode.GetBytes(content)))
        //        {
        //            DataContractJsonSerializer serializer = new DataContractJsonSerializer(typeof(Storeya.Core.Entities.Facebook.RootObject));
        //            responseData = (Storeya.Core.Entities.Facebook.RootObject)serializer.ReadObject(ms);
        //            if (responseData != null && responseData.data != null && responseData.data.Count > 0)
        //            {
        //                foreach (Datum tab in responseData.data)
        //                {
        //                    if (tab.id == string.Format("{0}/tabs/app_{1}", pageID, appID))
        //                    {
        //                        isAlreadyInstalled = true;
        //                        break;
        //                    }
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception)
        //    {

        //    }

        //    return isAlreadyInstalled;
        //}

        //public static bool DeleteAppTab(long pageID, string appID, string token)
        //{
        //    var app = new FacebookClient(token);
        //    dynamic pagesResponse = app.Get(string.Format("{0}?fields=access_token&access_token={1}", pageID, token));
        //    string pageToken = pagesResponse.access_token;

        //    if (!string.IsNullOrEmpty(pageToken))
        //    {
        //        RestSharp.Http req = new RestSharp.Http();
        //        req.Url = new Uri(string.Format("https://graph.facebook.com/{0}/{1}/tabs/app_{2}?method=DELETE&access_token={3}", _fbVersion, pageID, appID, pageToken));
        //        req.Delete();
        //        return true;
        //    }
        //    else
        //    {
        //        //Log4NetLogger.Info("The tab appID: {0} can not be removed from the pageID: {1} because the the current token does not suite to the page - the access token is null");
        //        return false;
        //    }
        //}

        public static bool IsFbCrawler()
        {
            if (HttpContext.Current.Request == null || HttpContext.Current.Request.UserAgent == null)
            {
                return false;
            }
            return IsFbCrawler(HttpContext.Current.Request.UserAgent);
        }

        public static bool IsFbCrawler(string userAgent)
        {
            if (userAgent.ToLower().Contains("facebookexternalhit"))
            {
                return true;
            }
            return false;
        }

        public static void GetAllAdsUrlTags()
        {
            string token = Models.FbAds.FbAdsBusinesses.GetAdminToken(1);
            List<FBAdsResults> fbAds = new List<FBAdsResults>();
            var db = DataHelper.GetStoreYaEntities();
            var fbChannels = (from c in db.TbAccountTrafficChannels
                              where (c.Status == (int)TrafficChannelsStatuses.Active) && c.ChannelType == (int)TrafficChannelsTypes.Facebook
                              select new { c.AccountID, c.ShopID }).ToList();
            int total = fbChannels.Distinct().Count();
            Console.WriteLine($"Total FB accounts :{total}");
            foreach (var item in fbChannels.Distinct().ToList())
            {
                string accountID = item.AccountID;
                List<FBAdsResults> fbAdsa = GetAccountAdsUrlTags(accountID, item.ShopID, token);
                fbAds.AddRange(fbAdsa);
                Console.WriteLine($"{total--} left : added total Ads:{fbAdsa.Count} ");
            }
            fbAds.ToCSV(@"C:\temp\FacebookUrlTags.csv");
        }

        public static List<FBAdsResults> GetAccountAdsUrlTags(string accountID, int shopId = 0, string token = null)
        {
            List<FBAdsResults> fbAds = new List<FBAdsResults>();
            try
            {


                if (string.IsNullOrEmpty(token))
                {
                    token = Models.FbAds.FbAdsBusinesses.GetAdminToken(1);
                }
                Models.FbAds.FbApiClient _apiClient = new Models.FbAds.FbApiClient(token);
                var url = "/act_" + accountID + "/adcreatives?fields=id,actor_id,url_tags";
                var response = _apiClient.Get(url);
                dynamic j = response.Content.FromJson<dynamic>();

                if (j.error != null)
                {
                    fbAds.Add(new FBAdsResults
                    {
                        AccountId = accountID,
                        ShopId = shopId,
                        Error = j.error.message
                    });

                }
                else
                {
                    foreach (var u in j.data)
                    {
                        fbAds.Add(new FBAdsResults
                        {
                            AccountId = accountID,
                            ShopId = shopId,
                            PageId = u.actor_id,
                            AdsId = u.id,
                            urlTags = u.url_tags
                        });
                    }

                }
            }
            catch (Exception ex)
            {
                fbAds.Add(new FBAdsResults
                {
                    AccountId = accountID,
                    ShopId = shopId,
                    Error = ex.Message
                });
            }
            return fbAds;
        }
        public class FBAdsResults
        {
            public int ShopId { get; set; }
            public string AccountId { get; set; }
            public string PageId { get; set; }
            public string AdsId { get; set; }
            public string urlTags { get; set; }
            public string Error { get; set; }
        }

    }

    public enum TabAppInstallationResponseCode
    {
        FailedToInstall = -1,

        InstalledSuccessfully = 1,
        AlreadyInstalled = 2
    }

}