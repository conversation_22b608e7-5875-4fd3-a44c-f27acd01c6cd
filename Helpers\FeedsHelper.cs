﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Security.Policy;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Xml.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PostmarkDotNet.Model;
using Storeya.Core;
using Storeya.Core.Helpers;
using Storeya.Core.Models;
using Storeya.Core.Models.ChatGPT;
using Storeya.Core.Models.ShoppingFeed;
using Storeya.Core.Models.TrafficBoosterModels.TrafficChannels;
using static Storeya.Core.Models.ShoppingFeed.ShoppingFeedObject;

namespace Storeya.Core.Helpers
{
    public static class FeedsHelper
    {

        public enum CONTENT_TYPES
        {
            JSON,
            XML,
            TEXT,
            CSV
        }

        public static string HttpGet(string url, string username, string password, CONTENT_TYPES contentType, string cookiesAddition = null)
        {
            //ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            WebClient webClient = LoadWebClient(username, password, contentType);
            if (cookiesAddition != null)
            {
                //cookies example -  "disabledMemCache=1"
                webClient.Headers.Add(HttpRequestHeader.Cookie, cookiesAddition);
            }
            string results = webClient.DownloadString(url);
            return results;
        }

        private static WebClient LoadWebClient(CONTENT_TYPES contentType)
        {
            return LoadWebClient(null, null, contentType);
        }

        private static WebClient LoadWebClient(string userid, string key, CONTENT_TYPES contentType)
        {
            WebClient client = new WebClient();
            client.Encoding = Encoding.UTF8;

            switch (contentType)
            {
                case CONTENT_TYPES.JSON:
                    client.Headers["Content-Type"] = "application/json";
                    break;
                case CONTENT_TYPES.XML:
                    client.Headers["Content-Type"] = "application/xml";
                    break;
                case CONTENT_TYPES.TEXT:
                    client.Headers["Content-Type"] = "text/plain";
                    break;
                case CONTENT_TYPES.CSV:
                    client.Headers["Content-Type"] = "text/csv";
                    break;
                default:
                    client.Headers["Content-Type"] = "application/json";
                    break;
            }


            //client.Headers["user-agent"] = "Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.99 Safari/537.36";
            //client.Headers["Accept-Encoding"] = "gzip, deflate, br";
            //client.Headers["Accept-Language"] = "en-US,en;q=0.8,he;q=0.6";
            //client.Headers["Cache-Control"] = "no-cache";            
            if (!string.IsNullOrEmpty(key))
            {
                string basic = userid + ":" + key;
                string encoded = "Basic " + Convert.ToBase64String(Encoding.ASCII.GetBytes(basic));
                client.Headers["Authorization"] = encoded;
            }
            return client;
        }

        public static T DeserializeXMLToObject<T>(string xmlString, int shopId)
        {
            //T returnObject = default(T);
            if (string.IsNullOrEmpty(xmlString)) return default(T);

            try
            {
                using (StringReader stringReader = new StringReader(xmlString))
                {
                    XmlSerializer xmlSerializer = new XmlSerializer(typeof(T));
                    return (T)xmlSerializer.Deserialize(stringReader);
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed on creating Feed Manager instance.", ex, shopId);
                throw ex;
            }
            //return returnObject;
        }
        public static string SerializeXMLObject<T>(T xmlObject, int shopId)
        {
            try
            {
                XmlSerializer xmlSerializer = new XmlSerializer(xmlObject.GetType());

                using (StringWriter textWriter = new StringWriter())
                {
                    xmlSerializer.Serialize(textWriter, xmlObject);
                    return textWriter.ToString();
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed on creating Feed Manager instance.", ex, shopId);
                throw ex;
            }
        }

        public static List<T> DeserializeTSVToObject<T>(string tsvString, int shopId, string delimiter = "\t") where T : class
        {
            //T returnObject = default(T);
            if (string.IsNullOrEmpty(tsvString)) return new List<T>();

            try
            {
                using (StringReader stringReader = new StringReader(tsvString))
                {
                    var tsv = new CsvHelper.CsvReader(stringReader);
                    tsv.Configuration.HasHeaderRecord = true;
                    tsv.Configuration.Delimiter = delimiter;

                    var dataList = tsv.GetRecords<T>().ToList();
                    return dataList;
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed on creating Feed Manager instance.", ex, shopId);
                throw ex;
            }
            //return returnObject;
        }

        public static void LoadFeed(string feedContent, string filename, string fileExtention, string uploadFolder, int shopId = 1, bool upload = true, bool uploadAsZip = false)
        {
            if (!string.IsNullOrEmpty(feedContent))
            {
                byte[] feedData = new System.Text.UTF8Encoding().GetBytes(feedContent);
                string relativePath = SaveFile(shopId, feedData, fileExtention, filename, uploadFolder, null, upload, uploadAsZip);
                string file_url = String.Format("{0}{1}", ImagePathHelper.GetServerPath().TrimEnd('/') ?? HttpHelper.GetCurrentDomain().TrimEnd('/'), relativePath);
                ConsoleAppHelper.WriteLog(string.Format("Saved: {0}", file_url));
            }
        }

        public static string SaveFile(int shopID, byte[] feedData, string fileExtension, string fileName, string uploadFolder, string settingName = null, bool upload = true, bool uploadAsZip = false)
        {
            string rootFolderPath = ConfigurationManager.AppSettings["ProductImages.RootPath"];
            string productImagesPath = ConfigurationManager.AppSettings["ProductImages.Path"];

            string relativeFolder = string.Format(@"{0}/feeds/{1}/", productImagesPath, uploadFolder);
            uploadFolder = string.Format(@"{0}{1}", rootFolderPath, relativeFolder);
            if (!Directory.Exists(Path.GetDirectoryName(uploadFolder)))
            {
                Directory.CreateDirectory(uploadFolder);
            }

            string newFileName = string.Format("{0}.{1}", fileName, fileExtension);

            string new_path = uploadFolder + newFileName;
            System.IO.File.WriteAllBytes(new_path, feedData);
            if (upload)
            {
                if (uploadAsZip)
                {
                    CreateZipFile(new_path);
                    new_path = new_path + ".zip";
                    newFileName = newFileName + ".zip";
                }

                CloudManager.Upload(new_path, 0, settingName);
                System.IO.File.Delete(new_path);
            }
            FeedsManager.LoadLastUpdatedFile(shopID, rootFolderPath, productImagesPath);
            return string.Format("/{0}{1}", relativeFolder, newFileName);
        }

        public static bool CreateZipFile(string fullFileName)
        {
            var directory = Path.GetDirectoryName(fullFileName);
            var name = Path.GetFileName(fullFileName);

            using (FileStream fs = new FileStream(directory + @"\" + name + ".zip", FileMode.Create))
            using (ZipArchive arch = new ZipArchive(fs, ZipArchiveMode.Create))
            {
                arch.CreateEntryFromFile(fullFileName, name);
            }

            return true;
        }

        public static List<ShoppingFeedItem> CreateItemsFeed(List<ShoppingFeedItem> currentFeedItems, string fileName, string fileExtention, string uploadFolder, int shopId = 1, bool upload = true, string filterBy = null)
        {
            try
            {
                FeedsManager manager = new FeedsManager();
                manager.ShopID = shopId;
                ConsoleAppHelper.WriteLog("Extracting products...");
                if (currentFeedItems != null && currentFeedItems.Count > 0)
                {
                    if (!string.IsNullOrEmpty(filterBy))
                    {
                        currentFeedItems = manager.DynamicFilterFeedItems(currentFeedItems, filterBy);
                    }
                    string feedContent = manager.GetXml(currentFeedItems);
                    //create shopping feed
                    ConsoleAppHelper.WriteLog("Creating Shopping Feed for ..." + shopId);

                    FeedsHelper.LoadFeed(feedContent, fileName, fileExtention, uploadFolder, shopId, upload);
                }
                return currentFeedItems;
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed on creating Feed Manager instance.", ex, shopId);
                return null;
            }
        }

        public static void LoadDsaFeed(List<ShoppingFeedItem> currentFeedItems, string fileName, string fileExtention, string uploadFolder, string dsaCustomLabel, int shopId = 1, bool upload = true)
        {
            try
            {
                FeedsManager manager = new FeedsManager();
                ConsoleAppHelper.WriteLog("Extracting products...");
                if (currentFeedItems != null && currentFeedItems.Count > 0)
                {
                    string feedContent = manager.GetDSAFeedCsv(currentFeedItems, dsaCustomLabel);
                    //create shopping feed
                    ConsoleAppHelper.WriteLog("Creating DSA Feed for ..." + shopId);
                    FeedsHelper.LoadFeed(feedContent, fileName, fileExtention, uploadFolder, shopId, upload);
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed on creating DSA Feed Manager instance.", ex, shopId);
            }
        }

        public static void CreateFaceBookFeed(List<ShoppingFeedItem> shoppingFeedItems, string fileName, string fileExtention, string uploadFolder, int shopId = 1, bool upload = true, bool uploadAsZip = false)
        {
            FeedsManager feedsManager = new FeedsManager();

            List<ShoppingFeedItem> fbshoppingFeedItems = feedsManager.FormatItemsForFacebookFeed(shoppingFeedItems);
            string feedContent = feedsManager.GetXml(fbshoppingFeedItems);
            FeedsHelper.LoadFeed(feedContent, fileName, fileExtention, uploadFolder, shopId, upload, uploadAsZip);

        }
        public static string LoadFeedFile(string url)
        {
            string xml = FeedsHelper.HttpGet(url, null, null, FeedsHelper.CONTENT_TYPES.XML);
            return xml;
        }

        public static void CreateCDRMCsvFeed(List<ShoppingFeedItem> shoppingFeedItems, string fileName, string fileExtention, string uploadFolder, int shopId = 1, bool upload = true)
        {
            try
            {
                ConsoleAppHelper.WriteLog("Extracting products...");
                if (shoppingFeedItems != null && shoppingFeedItems.Count > 0)
                {
                    string feedContent = WriteToCDRMCsv(shoppingFeedItems);
                    //create shopping feed
                    ConsoleAppHelper.WriteLog("Creating CDRM Feed for ..." + shopId);
                    FeedsHelper.LoadFeed(feedContent, fileName, fileExtention, uploadFolder, shopId, upload);
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed on creating CDRM Feed Manager instance.", ex, shopId);
            }
        }

        private static string WriteToCDRMCsv(List<ShoppingFeedItem> listOfProducts)
        {
            StringBuilder s = new StringBuilder();

            s.Append(ShoppingFeedItem.GetCDRMFeedCsvHeader());
            s.Append(Environment.NewLine);

            if (listOfProducts != null)
            {
                foreach (var item in listOfProducts)
                {
                    string content = ToCDRMFeedCsv(item.id, null, item.title, item.image_link, item.link, item.price, (item.price != item.sale_price ? item.sale_price : null));
                    s.Append(content);
                    s.Append(Environment.NewLine);
                }
            }
            return s.ToString();
        }

        private static string ToCDRMFeedCsv(string item_group_id, string product_type, string title, string image_link, string link, string price, string sale_price)
        {
            var del = "\t";
            return item_group_id + del
                + product_type + del
                + title + del
                + image_link + del
                + link + del
                + price + del
                + sale_price;
        }

        public static Dictionary<FeedType, string> GetFeedsUrls(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            Dictionary<FeedType, string> feedToAdd = new Dictionary<FeedType, string>();
            var feeds = db.ProductsFeeds.Where(x => x.ShopID == shopId).ToList();
            foreach (var feed in feeds)
            {

                if (string.IsNullOrEmpty(feed.FeedCreationError) && feed.CreatedAt != null)
                {
                    feedToAdd.Add((FeedType)feed.FeedType, GetFeedUrl(feed));
                }
            }
            return feedToAdd;
        }

        public static string GetFeedUrl(ProductsFeed feed, bool ignoreStatus = false)
        {
            if (ignoreStatus)
            {
                return string.Format("https://s3.amazonaws.com/strys3/Uploads/feeds/{0}/{1}", feed.ShopID, GetFeedFileName(feed.FeedType));
            }
            else
            {
                if (string.IsNullOrEmpty(feed.FeedCreationError) && feed.CreatedAt != null)
                {
                    //https://www.storeya.com/go/feeds/347135/CDRM.csv 
                    //will redirect to                        
                    //https://s3.amazonaws.com/strys3/Uploads/feeds/347135/CDRM.csv                         
                    //return string.Format("https://www.storeya.com/out/feeds/{1}/{2}", HttpHelper.GetCurrentDomain(), feed.ShopID, GetFeedFileName(feed.FeedType));
                    return string.Format("https://s3.amazonaws.com/strys3/Uploads/feeds/{0}/{1}", feed.ShopID, GetFeedFileName(feed.FeedType));
                }
            }
            return null;
        }

        public static string GetFeedFileName(int? feedType)
        {
            string file_name = null;
            if (feedType == (int)FeedType.Google)
            {
                file_name = "Google.xml";
            }
            else if (feedType == (int)FeedType.Facebook)
            {
                file_name = "Facebook.xml";
            }
            else if (feedType == (int)FeedType.CDRM)
            {
                file_name = "CDRM.csv";
            }
            else if (feedType == (int)FeedType.DSA)
            {
                file_name = "DSA.csv";
            }

            return file_name;
        }
        public static string LeaveOnly2RowsInAFile(string fileText)
        {
            string[] arr = fileText.Split(Environment.NewLine.ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
            if (arr.Length > 2)
            {

                return string.Format("{0}{1}{2}", arr[0], Environment.NewLine, arr[1]);
            }
            return fileText;
        }

        public static void ActivateFeeds(int shopId, bool onlyFacebook = false)
        {
            try
            {


                bool hasAgreement = TbChannelManager.HasAgreementChannel(shopId);
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                IEnumerable<ProductsFeed> feeds = null;
                if (onlyFacebook)
                {
                    feeds = db.ProductsFeeds.Where(x => x.ShopID == shopId && x.FeedType == (int)FeedType.Facebook);
                }
                else
                {
                    if (hasAgreement)
                    {
                        feeds = db.ProductsFeeds.Where(x => x.ShopID == shopId && x.FeedType != (int)FeedType.Facebook);
                    }
                    else
                    {
                        feeds = db.ProductsFeeds.Where(x => x.ShopID == shopId);
                    }
                }
                if (feeds != null)
                {
                    foreach (var feed in feeds)
                    {
                        if (feed.Status != (int)ProductFeedStatusesEnum.Active)
                        {
                            feed.Status = (int)ProductFeedStatusesEnum.Active;
                            ConsoleAppHelper.WriteLog("Activating feed " + ((FeedType)feed.FeedType).ToString(), shopId);
                        }

                    }
                    db.SaveChanges();
                    ConsoleAppHelper.WriteLog("Activating feed is done.", shopId);
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Shop has upgrade but failed to activate the feeds.", ex, shopId);
                EmailHelper.SendEmail("<EMAIL>", "Shop has upgrade but failed to activate the feeds" + shopId, EmailHelper.AdminLinkHref(shopId) + "<br/>" + ex.ToString());
            }
        }


        public static void UpdateBulkID(int shopID, long? bulkID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var firstFeed = db.ProductsFeeds.Where(f=> f.ShopID == shopID).OrderBy(f => f.FeedType).First();

            firstFeed.BulkID = bulkID;

            db.SaveChanges();
        }

        public static ShoppingFeedItem GetShoppingFeedItemFromJToken(JToken product)
        {
            //var id = product["id"].ToString();
            //var title = product["name"].ToString();
            //var description = product["description"].ToString();
            //var link = product["productPageUrl"]["base"].ToString() + product["productPageUrl"]["path"].ToString();
            //var image_link = product["media"]["mainMedia"]["image"]["url"].ToString();
            //var price = product["convertedPriceData"]["price"].ToString() + " " + product["convertedPriceData"]["currency"].ToString();
            //var availability = product["stock"]["inventoryStatus"].ToString().Replace("_", " ").ToLower();
            //var sale_price = product["convertedPriceData"]["discountedPrice"].ToString() + " " + product["convertedPriceData"]["currency"].ToString();
            string brand = null;
            if (product["brand"] != null)
                brand = product["brand"].ToString();
            string mpn = null;
            if (product["numericId"] != null)
                mpn = product["numericId"].ToString();
            var description = HtmlToPlainText(product["description"].ToString()).Replace("\n", ""); //.Replace("<p>","").Replace("</p>", "")
            var price = product["convertedPriceData"]["price"].ToString() + " " + product["convertedPriceData"]["currency"].ToString();
            var sale_price = product["convertedPriceData"]["discountedPrice"].ToString() + " " + product["convertedPriceData"]["currency"].ToString();
            if (price == sale_price)
            {
                sale_price = null;
            }
            //var custom_label; -it's price range, it's calculates
            //var identifier_exists;
            //var pattern;
            //var custom_label_0;

            ShoppingFeedItem shoppingFeedItem = (new ShoppingFeedItem
            {
                id = product["id"].ToString(),
                title = product["name"].ToString(),
                description = description,
                link = product["productPageUrl"]["base"].ToString() + product["productPageUrl"]["path"].ToString(),
                image_link = product["media"]["mainMedia"]["image"]["url"].ToString(),
                price = price,
                availability = product["stock"]["inventoryStatus"].ToString().Replace("_", " ").ToLower(),
                sale_price = sale_price,
                brand = brand,
                mpn = mpn,
                condition = "new",

                //gtin = "", //product[key: "gtin"].ToString(),            //var gtin;
                //quantity = Int32.Parse(product[key: "inventory"].ToString()),
                //size = product[key: "size"].ToString(),            //var size;
                //product[key: "condition"].ToString(),              //var condition;
                //gender = product[key: "gender"].ToString(),            //var gender
                //color = product[key: "color"].ToString(),            //var color;
                //age_group = product[key: "age_group"].ToString(),            //var age_group;
                //item_group_id = product[key: "item_group_id"].ToString(),
                //product_type = product[key: "product_type"].ToString(),            //var product_type;
                //material = product[key: "material"].ToString(),            //var material;
            });


            return shoppingFeedItem;
        }
        private static string HtmlToPlainText(string html)
        {
            string buf;
            string block = "address|article|aside|blockquote|canvas|dd|div|dl|dt|" +
              "fieldset|figcaption|figure|footer|form|h\\d|header|hr|li|main|nav|" +
              "noscript|ol|output|p|pre|section|table|tfoot|ul|video";

            string patNestedBlock = $"(\\s*?</?({block})[^>]*?>)+\\s*";
            buf = Regex.Replace(html, patNestedBlock, "\n", RegexOptions.IgnoreCase);

            // Replace br tag to newline.
            buf = Regex.Replace(buf, @"<(br)[^>]*>", "\n", RegexOptions.IgnoreCase);

            // (Optional) remove styles and scripts.
            buf = Regex.Replace(buf, @"<(script|style)[^>]*?>.*?</\1>", "", RegexOptions.Singleline);

            // Remove all tags.
            buf = Regex.Replace(buf, @"<[^>]*(>|$)", "", RegexOptions.Multiline);

            // Replace HTML entities.
            buf = WebUtility.HtmlDecode(buf);
            return buf;
        }
        public static void ShoppingFeedTaxonomyImprovements(Dictionary<string, int> urlsAndShopIDs)
        {
            StringBuilder sb = new StringBuilder();
            foreach (var urlAndShopID in urlsAndShopIDs)
            {
                var url = urlAndShopID.Key;
                string xml = LoadFeedFile(url);
                XmlSerializer serializer = new XmlSerializer(typeof(FeedsFromXML));
                using (StringReader reader = new StringReader(xml))
                {
                    FeedsFromXML feedsFromXml = (FeedsFromXML)serializer.Deserialize(reader);
                    List<Feed> feeds = feedsFromXml.entries.Take(5).ToList();
                    string linkBO = EmailHelper.GetBoLinkHref(urlAndShopID.Value);
                    sb.AppendLine(linkBO);
                    sb.AppendLine("Title;Link;Response;Category;Comments");
                    List<string> listOfCategories = GetFirstTwoLevelsGoogleTaxonomy();
                    string categories = string.Join(Environment.NewLine, listOfCategories.ToArray());
                    foreach (var feed in feeds)
                    {
                        string question = $"Match product with title '{feed.Title}' to the exact category from this list: {Environment.NewLine} {categories} {Environment.NewLine} If you can't match, just return empty response";//$"match product with title \"{feed.Title}\" to the exact category from this list https://www.google.com/basepages/producttype/taxonomy.en-US.txt Give me just text of the category and make sure to be limited to the list. Look only for the root type (Animals & Pet Supplies, Arts & Entertainment, Baby & Toddler etc) + 1 type level. Don't combine types from different levels, don't create new types, just match line in the list"; //and first level (Live Animals, Clothing, Event Tickets etc).";//$"Match the exact category for the product with title \"{feed.Title}\" from this list: https://www.google.com/basepages/producttype/taxonomy.en-US.txt Give me just text of the category.";
                        string response = ChatGPTManager.GetGptString(question).Replace("\n", "").Replace(Environment.NewLine, "").Replace("\r", "").Replace(".", "");
                        string category = GetOneDepthOfTheSubcategory(response);
                        bool match = listOfCategories.Contains(category);
                        string comment = "";
                        if (match)
                        {
                            comment = "Match";
                        }
                        else if (response.ToLower().Contains("no match"))
                        {
                            comment = "no match";
                        }
                        else
                        {
                            comment = "GPT creative";
                        }
                        Console.WriteLine(feed.Title);
                        Console.WriteLine(category);
                        sb.AppendLine($"{feed.Title};{feed.Link};{response};{category};{comment}");
                    }
                }
            }


            using (System.IO.StreamWriter file = new System.IO.StreamWriter("C:/TEMP/FeedsWithCategory.txt"))
            {
                file.WriteLine(sb.ToString());
            }
        }

        private static string GetOneDepthOfTheSubcategory(string category)
        {
            var categoryText = "";
            var splittedText = category.Split(new string[] { " > " }, StringSplitOptions.None);
            if (splittedText.Length > 2)
            {
                categoryText = $"{splittedText[0]} > {splittedText[1]}";
            }
            else
            {
                categoryText = category;
            }
            return categoryText;
        }

        public static List<string> GetFirstTwoLevelsGoogleTaxonomy()
        {
            List<string> categories = new List<string>();
            WebClient client = new WebClient();
            Stream stream = client.OpenRead("https://www.google.com/basepages/producttype/taxonomy.en-US.txt");
            StreamReader sr = new StreamReader(stream);
            String line = sr.ReadLine();
            int counter = 0;
            while (line != null)
            {
                counter++;
                if (counter == 1)
                {
                    continue;
                }
                var categoryText = GetOneDepthOfTheSubcategory(line);
                if (!categories.Contains(categoryText))
                {
                    categories.Add(categoryText);
                }
                line = sr.ReadLine();
            }
            return categories;
        }
    }
    public class GoogleCategories
    {
        public List<string> Categories { get; set; }
        public GoogleCategories()
        {
            Categories = new List<string>()
            {
                    "Apparel & Accessories",
                    "Arts & Entertainment",
                    "Babies & Toddlers",
                    "Business & Industrial",
                    "Cameras & Optics",
                    "Electronics",
                    "Food, Beverages & Tobacco",
                    "Furniture",
                    "Health & Beauty",
                    "Home & Garden",
                    "Luggage & Bags",
                    "Media",
                    "Office Supplies",
                    "Software",
                    "Sporting Goods",
                    "Toys & Games",
                    "Vehicles & Parts"
            };
        }
        public string GetCategoryFromResponse(string response)
        {
            if (response.Length <= 30)
            {
                return response;
            }
            response = response.ToLower().Replace(" ", "");
            foreach (var cat in Categories)
            {
                if (response.Contains(cat.ToLower().Replace(" ", "")))
                {
                    return cat;
                }
            }
            return null;
        }

    }
    [XmlRoot("feed", Namespace = "http://www.w3.org/2005/Atom")]
    public class FeedsFromXML
    {
        [System.Xml.Serialization.XmlElementAttribute("entry")]
        public List<Feed> entries { get; set; }
    }
    [XmlRoot("entry", Namespace = "http://www.w3.org/2005/Atom")]
    public class Feed
    {
        [XmlElement("id", Namespace = "http://base.google.com/ns/1.0")]
        public string Id { get; set; }

        [XmlElement("item_group_id", Namespace = "http://base.google.com/ns/1.0")]
        public string ItemGroupId { get; set; }

        [XmlElement("title", Namespace = "http://base.google.com/ns/1.0")]
        public string Title { get; set; }

        [XmlElement("image_link", Namespace = "http://base.google.com/ns/1.0")]
        public string ImageLink { get; set; }

        //[XmlElement("additional_image_link", Namespace = "http://base.google.com/ns/1.0")]
        //public List<string> AdditionalImageLinks { get; set; }

        [XmlElement("brand", Namespace = "http://base.google.com/ns/1.0")]
        public string Brand { get; set; }

        //[XmlElement("description", Namespace = "http://base.google.com/ns/1.0")]
        //public string Description { get; set; }

        [XmlElement("link", Namespace = "http://base.google.com/ns/1.0")]
        public string Link { get; set; }

        //[XmlElement("condition", Namespace = "http://base.google.com/ns/1.0")]
        //public string Condition { get; set; }

        //[XmlElement("price", Namespace = "http://base.google.com/ns/1.0")]
        //public string Price { get; set; }

        // Add other properties as needed
    }
}

