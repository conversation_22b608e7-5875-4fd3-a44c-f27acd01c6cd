﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class FileDeleterHelper
    {

        public static List<string> DeleteFiles(int shopId, string folderPath, List<string> folderPrefixesToExclude = null, List<string> extensionsToExclude = null, DateTime? excludeIfAfter = null)
        {
            if (extensionsToExclude == null)
            {
                extensionsToExclude = new List<string>();
            }
            if (folderPrefixesToExclude == null)
            {
                folderPrefixesToExclude = new List<string>();
            }
            List<string> excludedFiles = DeleteFilesExceptExcludedInDirectory(shopId, folderPath, folderPrefixesToExclude, extensionsToExclude, excludeIfAfter);
            return excludedFiles;
        }

        private static List<string> DeleteFilesExceptExcludedInDirectory(int shopId, string path, List<string> folderPrefixesToExclude, List<string> extensionsToExclude, DateTime? excludeIfAfter = null)
        {
            List<string> excludedFiles = new List<string>();
            if (!Directory.Exists(path) || IsExcludedFolder(path, folderPrefixesToExclude))
            {
                return excludedFiles;
            }
            excludedFiles.AddRange(DeleteNonExcludedFiles(shopId, path, extensionsToExclude, excludeIfAfter));
            excludedFiles.AddRange(DeleteFilesInSubdirectories(shopId, path, folderPrefixesToExclude, extensionsToExclude, excludeIfAfter));

            if (IsDirectoryEmpty(path))
            {
                try
                {
                    Directory.Delete(path);
                    Console.WriteLine($"Directory: {path}, Was deleted");
                }
                catch (Exception ex)
                {
                    ConsoleAppHelper.WriteError($"Failed to delete {path} ", ex, shopId);
                }
            }
            return excludedFiles;
        }

        private static List<string> DeleteNonExcludedFiles(int shopId, string path, List<string> extensionsToExclude, DateTime? excludeIfAfter = null)
        {
            List<string> excludedFiles = new List<string>();
            var files = Directory.GetFiles(path);
            int total = 0;
            int totalExcluded = 0;
            foreach (var file in files)
            {
                if (IsExcludedFile(file, extensionsToExclude))
                {
                   // excludedFiles.Add(file);
                    continue;
                }

                try
                {
                    if (excludeIfAfter.HasValue && excludeIfAfter.Value > DateTime.MinValue)
                    {
                        FileInfo fileInfo = new FileInfo(file);
                        if (fileInfo.LastWriteTime.Date > excludeIfAfter.Value.Date)
                        {
                            excludedFiles.Add($"{fileInfo.LastWriteTime} - {file}");
                            totalExcluded++;
                            continue;
                        }
                    }
                    File.Delete(file);
                }
                catch (Exception ex)
                {
                    ConsoleAppHelper.WriteError($"Failed to delete file: {file} ", ex, shopId);
                }
                total++;
            }
            if (excludedFiles.Count > 0)
            {
                excludedFiles.Insert(0, $"Total files: {total}, Excluded files: {totalExcluded}");
            }
            return excludedFiles;
        }

        private static List<string> DeleteFilesInSubdirectories(int shopId, string path, List<string> folderPrefixesToExclude, List<string> extensionsToExclude, DateTime? excludeIfAfter = null)
        {
            List<string> excludedFiles = new List<string>();
            var dirs = Directory.GetDirectories(path);
            foreach (var dir in dirs)
            {
                excludedFiles.AddRange(DeleteFilesExceptExcludedInDirectory(shopId, dir, folderPrefixesToExclude, extensionsToExclude, excludeIfAfter));
            }
            return excludedFiles;
        }

        private static bool IsExcludedFile(string file, List<string> extensionsToExclude)
        {
            string extension = Path.GetExtension(file).ToLower();
            return extensionsToExclude.Contains(extension, StringComparer.InvariantCultureIgnoreCase);
        }

        private static bool IsExcludedFolder(string folderPath, List<string> folderPrefixesToExclude)
        {
            string folderName = Path.GetFileName(folderPath.TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar));
            return folderPrefixesToExclude.Any(prefix => folderName.ToLower().StartsWith(prefix.ToLower(), StringComparison.InvariantCultureIgnoreCase));
        }

        public static bool IsSubFolderExists(string path, string folderPrefixesToCheck)
        {
            var dirs = Directory.GetDirectories(path);
            foreach (var dir in dirs)
            {
                string folderName = Path.GetFileName(dir.TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar));
                if (folderName.ToLower().StartsWith(folderPrefixesToCheck))
                {
                    return true;
                }
            }
            return false;
        }
        private static bool IsDirectoryEmpty(string path)
        {
            return !Directory.EnumerateFileSystemEntries(path).Any();
        }
    }


}
