﻿using Storeya.Core.Models;
using Storeya.Core.Models.GA;
using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace Storeya.Core.Helpers
{
    public class GaStatsHelper
    {
        public enum GA_GROUP_TYPE
        {
            PRODUCT_UNIQUE_PURCHASES = 0,
        }
        public static List<GaReportData> GetGaReportData(int shopid, GA_GROUP_TYPE gaGroupType, DateTime? from = null, DateTime? to = null, int limit = 20)
        {
            string start_date = "30 days ago";
            string end_date = " - today";
            if (from.HasValue && to.HasValue)
            {
                start_date = from.Value.ToString("yyy-MM-dd");
                end_date = to.Value.ToString("yyy-MM-dd");
            }
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            GaProfileInfo gaProfileInfo = GaManager.GetActiveProfileInfo(shopid);
            if (gaProfileInfo != null)
            {
                IGaSimpleService service = GaSimpleService.GetInstanceWithRefreshToken(gaProfileInfo);
                GADataExtractor extractor = new GADataExtractor();
                Dictionary<string, string> metrics = new Dictionary<string, string>();
                Dictionary<string, string> groupBy = new Dictionary<string, string>();
                switch (gaGroupType)
                {
                    case GA_GROUP_TYPE.PRODUCT_UNIQUE_PURCHASES:
                        metrics.Add("UNIQUEPURCHASES", "ga:uniquePurchases");
                        //metrics.Add("ITEMREVENUE", "ga:itemRevenue");

                        //ga:productName                  
                        groupBy.Add("PRODUCTNAME", "ga:productName");
                        groupBy.Add("PRODUCTSKU", "ga:productSku");
                        break;
                    default:
                        break;
                }
                List<GaReportData> gaReportDatas = null;
                if (gaProfileInfo.V4)
                {
                    gaReportDatas = service.GetGaGroupedData(gaProfileInfo.GAv4ConnectedProperties.Property, metrics, groupBy, null, start_date, end_date, sortBy: "-ga:uniquePurchases");
                }
                else
                {
                    gaReportDatas = service.GetGaGroupedData(gaProfileInfo.GAv3ConnectedProfile.ProfileID, metrics, groupBy, null, start_date, end_date, sortBy: "-ga:uniquePurchases");
                }
                if (gaReportDatas != null)
                {
                    gaReportDatas = gaReportDatas.OrderByDescending(o => o.uniquePurchases).Take(limit).ToList();
                }
                return gaReportDatas;

            }
            return null;
        }
        public static void CollectGaInitialStats_ByShopID(int shopID, bool skipUpdate = false)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                GaProfileInfo gaProfile = GaManager.GetActiveProfileInfo(shopID);
                if (gaProfile != null)
                {
                    GAConnectedAccountsStat existingStats = db.GAConnectedAccountsStats.SingleOrDefault(s => s.ShopID == shopID);
                    if (existingStats != null && (skipUpdate))
                    {
                        RevenueRankManager.SetRank(shopID, existingStats.RevenuesInUSD ?? 0);
                        var message = "Skip as data exists already - shopID = " + shopID;
                        Log4NetLogger.Info(message, shopID);
                        return;
                    }

                    GAConnectedAccount gaConnectedAccount = db.GAConnectedAccounts.SingleOrDefault(g => g.ID == gaProfile.StoreYaProfileID);
                    if (gaConnectedAccount != null)
                    {
                        var result = GetStats(gaProfile);
                        if (result == null)
                        {
                            Console.WriteLine("Updated Stats Failed check log");
                            Log4NetLogger.Info("Updated Stats Failed check log", shopID);
                            return;
                        }
                        GAConnectedAccountsStat stats = result.StatsRow;
                        if (existingStats != null)
                        {
                            existingStats.FacebookRevenue = stats.FacebookRevenue;
                            existingStats.FacebookSessions = stats.FacebookSessions;
                            existingStats.FacebookTransactions = stats.FacebookTransactions;

                            existingStats.Sessions = stats.Sessions;
                            existingStats.Transactions = stats.Transactions;
                            existingStats.Revenue = stats.Revenue;
                            existingStats.RevenuesInUSD = stats.RevenuesInUSD;
                            existingStats.GooglePaidSessions = stats.GooglePaidSessions;
                            existingStats.GooglePaidTransactions = stats.GooglePaidTransactions;
                            existingStats.GooglePaidRevenue = stats.GooglePaidRevenue;

                            existingStats.GoogleOrganicSessions = stats.GoogleOrganicSessions;
                            existingStats.GoogleOrganicTransactions = stats.GoogleOrganicTransactions;
                            existingStats.GoogleOrganicRevenue = stats.GoogleOrganicRevenue;
                            existingStats.GoogleOrganicWrongSources = stats.GoogleOrganicWrongSources;

                            existingStats.WrongSources = stats.WrongSources;
                            existingStats.StryRevenue = stats.StryRevenue;
                            existingStats.StryTransactions = stats.StryTransactions;

                            existingStats.BingTransactions = stats.BingTransactions;
                            existingStats.BingRevenue = stats.BingRevenue;
                            existingStats.BingSessions = stats.BingSessions;

                            existingStats.UpdatedAt = DateTime.Now;
                            GAConnectedAccountsStatsHistory historyStat = existingStats.ToJson().FromJson<GAConnectedAccountsStatsHistory>();
                            db.GAConnectedAccountsStatsHistories.Add(historyStat);
                        }
                        else
                        {
                            stats.RevenueInitially = stats.Revenue;
                            db.GAConnectedAccountsStats.Add(stats);
                            GAConnectedAccountsStatsHistory historyStat = stats.ToJson().FromJson<GAConnectedAccountsStatsHistory>();
                            db.GAConnectedAccountsStatsHistories.Add(historyStat);
                        }
                        db.SaveChanges();
                        TbAppManager tbAppManager = new TbAppManager(shopID, onlyIfActiveAndPaid: true);
                        bool isActiveAndPaid = tbAppManager.IsAppActive;
                        if (GoodAccountHelper.IsGoodGaStats(stats) && isActiveAndPaid)
                        {
                            TbBigSpendersManager.Add(shopID, true);
                        }

                        RevenueRankManager.SetRank(shopID, stats.RevenuesInUSD ?? 0);

                        Log4NetLogger.Info("Ga stats updated successfully", shopID);
                    }
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Failed To Collect Ga Initial Stats For Shop:" + shopID, ex);
            }
        }
        //private static WrongSourceAnalysisResult GetStats(GAConnectedAccount gAConnectedAccount, GaProfileInfo gaProfile, ref StringBuilder sb, ref bool isBigSpender, int daysBefore = 30)
        public static WrongSourceAnalysisResult GetStats(GaProfileInfo gaProfile, int daysBefore = 30)
        {
            IGaSimpleService service = GaSimpleService.GetInstanceWithRefreshToken(gaProfile);

            GADataExtractor extractor = new GADataExtractor();
            Dictionary<string, string> metrics = new Dictionary<string, string>();
            metrics.Add("SESSIONS", "ga:sessions");
            metrics.Add("TRANSACTIONS", "ga:transactions");
            metrics.Add("TRANSACTIONREVENUE", "ga:transactionRevenue");

            List<GaReportData> total = service.GetGaGroupedData(daysBefore, gaProfile.ProfileID, metrics);

            //Sources / Media (top 10)   ga:source
            Dictionary<string, string> groupBySources = new Dictionary<string, string>();
            groupBySources.Add("SOURCE", "ga:sourceMedium");
            List<GaReportData> groupBySourcesReportData = service.GetGaGroupedData(daysBefore, gaProfile.ProfileID, metrics, groupBySources);
            string start_date = DateTime.Now.AddDays(-daysBefore).ToString("yyyy-MM-dd");
            List<GaReportData> groupByAllStrySegmentReportData = service.GetGaGroupedData(gaProfile.ProfileID, metrics, null, GaSimpleService.SEGMENT_ALL_STOREYA, start_date: start_date);

            WrongSourceAnalysisResult result = new WrongSourceAnalysisResult();
            result.StatsRow = GetGAConnectedAccountsStat(total, groupBySourcesReportData, groupByAllStrySegmentReportData);

            List<GaReportData> problematicDomains = GetWrongSources(groupBySourcesReportData);
            if (problematicDomains != null && problematicDomains.Count > 0)
            {
                result.StatsRow.WrongSources = GetWrongSourcesString(problematicDomains);

                //   Console.WriteLine("Setting email message about shopID " + gAConnectedAccount.ShopID.ToString());

                string details = GetEmailContent(problematicDomains.ToList(), gaProfile.ShopID, gaProfile.StoreyaGaEmail, gaProfile.WebPropertyID);
                string guide = "In Admin,<br/>Click Data Streams in the Property column.<br/> " +
                    "Click Web and then click a web data stream. In the web stream details,<br/> " +
                    "Click Configure tag settings (at the bottom). In the Settings section,<br/> " +
                    "Click Show all to see all available settings.<br/> " +
                    "Click List unwanted referrals.<br/> " +
                    "Under Include referrals that match ANY of the following conditions:<br/> " +
                    "Choose a match type. Under Domain, enter the identifier for the domain you want to match (e.g. example.com).<br/> " +
                    "Click Add condition to add another domain.<br/> " +
                    "Conditions are evaluated using OR logic.<br/> " +
                    "Click Save.";
                guide = Convert.ToBase64String(Encoding.UTF8.GetBytes(guide).ToArray());
                string script = $"<a href ='javascript:void(0);' onclick='ShowTemplateWithCustomHeader(\"{guide}\",\"Guide: \")' >guide</a>";

                result.WrongSourcesDetails = "<br/>Wrong sources alert: " + gaProfile.WebsiteUrl + Environment.NewLine + details + $"<br/> Use this {script}";
            }

            if (result.StatsRow.GooglePaidSessions > 2000) // paid more than $1K for ppc
            {
                result.PotentialReason = "<br/>GooglePaidSessions > 2000 alert: " + gaProfile.ShopID + " " + gaProfile.WebsiteUrl + Environment.NewLine + "</br>";
                result.IsBigSpender = true;
            }
            else if (result.StatsRow.Revenue > 10000)
            {
                result.PotentialReason = "<br/>Revenue > 10000 alert: " + gaProfile.ShopID + " " + gaProfile.WebsiteUrl + Environment.NewLine + "</br>";
                result.IsBigSpender = true;
            }
            else if (result.StatsRow.Transactions > 150)
            {
                result.PotentialReason = "<br/>Transactions > 150 alert: " + gaProfile.ShopID + " " + gaProfile.WebsiteUrl + Environment.NewLine + "</br>";
                result.IsBigSpender = true;
            }
            else if (result.StatsRow.FacebookRevenue > 15000)
            {
                result.PotentialReason = "<br/>FacebookRevenue > 15000 alert: " + gaProfile.ShopID + " " + gaProfile.WebsiteUrl + Environment.NewLine + "</br>";
                result.IsBigSpender = true;
            }
            else if (result.StatsRow.Sessions > 10000)
            {
                result.PotentialReason = "<br/>Sessions > 10000 alert: " + gaProfile.ShopID + " " + gaProfile.WebsiteUrl + Environment.NewLine + "</br>";
                //isBigSpender = true;
            }

            //else if (stats.GoogleOrganicSessions > 500) // nice organic traffic
            //{
            //    sb.AppendLine("<br/>GoogleOrganicSessions > 500 alert: " + gAConnectedAccount.ShopID + " " + gaProfile.WebsiteUrl + Environment.NewLine + "</br>");
            //}
            //if (stats.Transactions > 100 && ((double)stats.Transactions / (double)stats.Sessions) > 0.04) // paid more than $1K for ppc
            //{
            //    sb.AppendLine("</br>Conversion > %4 alert: " + gAConnectedAccount.ShopID + " " + gaProfile.WebsiteUrl);
            //}
            result.StatsRow.RevenuesInUSD = DBCurrencyHelper.GetRevenuesInUSD(gaProfile.Currency, result.StatsRow.Revenue);
            result.StatsRow.GAAccountID = gaProfile.StoreyaGaAccountId;
            result.StatsRow.ShopID = gaProfile.ShopID;
            result.StatsRow.Url = gaProfile.WebsiteUrl;
            result.StatsRow.InsertedAt = DateTime.Now;
            return result;
        }

        public static string GetEmailContent(List<GaReportData> wrongSourcesData, int shopID, string storeyaGaEmail, string webProperty)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(string.Format("ShopID: {0}, StoreyaGaEmail: {1}, WebProperty: {2}<br/>", shopID, storeyaGaEmail, webProperty));
            foreach (var item in wrongSourcesData)
            {
                sb.Append(string.Format("Source: {0}, Transactions: {1}, Sessions: {2}<br/>", item.Source, item.Transactions, item.Sessions));
            }
            return sb.ToString();
        }

        public static GAConnectedAccountsStat GetGAConnectedAccountsStat(List<GaReportData> total, List<GaReportData> groupBySourcesReportData, List<GaReportData> groupByAllStrySegmentReportData)
        {
            // extract from groupBySourcesReportData the  “google / cpc” data 
            // extract from groupBySourcesReportData the  “google / organic”  data
            GAConnectedAccountsStat stats = new GAConnectedAccountsStat();
            GaReportData rep = total.FirstOrDefault();
            if (rep != null)
            {
                stats.Sessions = rep.Sessions;
                stats.Transactions = rep.Transactions;
                stats.Revenue = rep.TransactionRevenue;

            }

            GaReportData g_cpc = groupBySourcesReportData.Where(c => !string.IsNullOrEmpty(c.Source) && c.Source.ToLower() == "google / cpc").FirstOrDefault();
            if (g_cpc != null)
            {
                stats.GooglePaidSessions = g_cpc.Sessions;
                stats.GooglePaidTransactions = g_cpc.Transactions;
                stats.GooglePaidRevenue = g_cpc.TransactionRevenue;
            }

            GaReportData g_org = groupBySourcesReportData.Where(c => !string.IsNullOrEmpty(c.Source) && c.Source.ToLower() == "google / organic").FirstOrDefault();
            if (g_org != null)
            {
                stats.GoogleOrganicSessions = g_org.Sessions;
                stats.GoogleOrganicTransactions = g_org.Transactions;
                stats.GoogleOrganicRevenue = g_org.TransactionRevenue;
            }


            List<GaReportData> g_fb = groupBySourcesReportData.Where(c => !string.IsNullOrEmpty(c.Source) && c.Source.ToLower().Contains("facebook")).ToList();
            if (g_fb != null)
            {
                stats.FacebookSessions = g_fb.Sum(x => x.Sessions);
                stats.FacebookTransactions = g_fb.Sum(x => x.Transactions);
                stats.FacebookRevenue = Math.Round(g_fb.Sum(x => x.TransactionRevenue), 2);
            }

            List<GaReportData> g_bing = groupBySourcesReportData.Where(c => !string.IsNullOrEmpty(c.Source) && c.Source.ToLower().Contains("bing / organic")).ToList();
            if (g_bing != null)
            {
                stats.BingSessions = g_bing.Sum(x => x.Sessions);
                stats.BingTransactions = g_bing.Sum(x => x.Transactions);
                stats.BingRevenue = Math.Round(g_bing.Sum(x => x.TransactionRevenue), 2);
            }

            if (groupByAllStrySegmentReportData != null)
            {
                stats.StryRevenue = groupByAllStrySegmentReportData.Sum(t => t.TransactionRevenue);
                stats.StryTransactions = groupByAllStrySegmentReportData.Sum(t => t.Transactions);
            }
            return stats;
        }

        public static string GetWrongSourcesString(List<GaReportData> wrongSourcesData)
        {
            return string.Join<string>(", ", wrongSourcesData.Select(a => a.Source));
        }



        public static List<GaReportData> GetWrongSources(List<GaReportData> data)
        {
            List<GaReportData> problematicDomains = new List<GaReportData>();

            if (data != null)
            {
                foreach (var item in data)
                {
                    var source = item.Source.ToLower();
                    if (SourcesValidation.IsWrongSource(source))
                    {
                        problematicDomains.Add(item);
                    }
                    else if (((double)item.Transactions / (double)item.Sessions) > 0.1
                             && (double)item.Transactions > 3
                             && !source.Contains("duckduckgo")
                             && !source.Contains("igshopping")
                             && !source.Contains("klaviyo")
                             && !source.Contains("bing / organic")
                             && !source.Contains("duckduckgo.com / referral")
                             && !source.Contains("lm.facebook.com / referral")
                             && !source.Contains("mail.google.com / referral")
                             && !source.Contains("mail.yahoo.com / referral")
                             && !source.Contains(" / email")
                             && !source.Contains("yahoo / organic")
                             && !source.Contains("(not set)"))

                    {
                        problematicDomains.Add(item);
                    }
                }
            }

            return problematicDomains;
        }

        //[ObsoleteAttribute("Use Eventer.ReportEvent but only if this is about few events.")]
        public static bool LogEvent(string eventCategory, string action, string eventLabel, int? eventValue = 1)
        {
            try
            {
                if (ConfigHelper.GetBoolValue("DebugGaEvents"))
                {
                    StoreYaEntities db = DataHelper.GetStoreYaEntities();
                    db.GaEvents.Add(new GaEvent() { Action = action, Category = eventCategory, Label = eventLabel, EventValue = eventValue, InsertedAt = DateTime.Now });
                    db.SaveChanges();
                }
                GoogleAnalyticsProvider.ReportEvent(eventCategory, action, eventLabel, eventValue.ToString());
            }
            catch
            {
                return false;
            }

            return true;
        }

        public static bool LogEventWithPost(string eventCategory, string action, string eventLabel, int? eventValue = 1)
        {
            try
            {
                GoogleAnalyticsProvider.ReportEvent(eventCategory, action, eventLabel, eventValue.ToString());
            }
            catch
            {
                return false;
            }

            return true;
        }
    }

    public enum GaEventCategories
    {
        CouponPopReport = 1,
        Warning = 2,
        ShopifyOrders = 3,
        ExeExecution = 4,
        WooCommerceOrders = 5,
        AdWordsData = 6
    }
}
