﻿using Storeya.Core.Models.ShopAttributes;
using Storeya.Core.Models.TbInternalTasks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class GoodAccountHelper
    {
        const int good_revenue_amount = 5000;
        public static bool IsGoodGaStats(GAConnectedAccountsStat stats)
        {
            if (stats == null)
            {
                return false;
            }
            return stats.Revenue >= good_revenue_amount;
        }

        public static bool IsGoodRevenue(decimal revenue)
        {
            return revenue >= good_revenue_amount;
        }
    }
}
