﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Storeya.Core.Helpers
{
    public static class GoogleBaseFeedHelper
    {
        public static bool IsAProductFeed(string input, int shopIDToLog)
        {
            string responseTxt = GetResponseContent(input, shopIDToLog);
            if (!string.IsNullOrEmpty(responseTxt))
            {
                return CheckFileFormat(responseTxt);
            }
            return false;
        }

         public static bool CheckFileFormat(string content)
        {
            Regex regxRss = new Regex(@"<rss.*?>", RegexOptions.Singleline);
            Regex regxAtom = new Regex(@"<feed.*?>", RegexOptions.Singleline);   //match: <feed xmlns="http://www.w3.org/2005/Atom" xmlns:g="http://base.google.com/ns/1.0">
            Regex regxAtom2 = new Regex(@"<atom:feed.*?>", RegexOptions.Singleline);

            Match firstLineMatch = Regex.Match(content, ".*$", RegexOptions.Multiline);
            Regex regxTextDelimited = new Regex(@"\t.*?\t", RegexOptions.Singleline); //tab \t delimited

            if (regxRss.Match(content).Success)
            {
                return true;
            }
            else if (regxAtom.Match(content).Success || regxAtom2.Match(content).Success)
            {
                return true;
            }
            else if (firstLineMatch.Success && regxTextDelimited.Match(firstLineMatch.Value).Success)
            {
                return true;
            }
            else
                return false;
        }

         public static string GetResponseContent(string urlToReadMeFile, int shopIDToLog)
         {
             string responseTxt = null;
             try
             {
                 //Log4NetLogger.Info(string.Format("Requesting to {0}", urlToReadMeFile), this.ShopID);
                 responseTxt = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(urlToReadMeFile, true, shopIDToLog);
             }
             catch (Exception ex)
             {
                 Log4NetLogger.Info(string.Format("Failed to ger response from {0}", urlToReadMeFile), ex, shopIDToLog);
             }
             return responseTxt;
         }
    }
}
