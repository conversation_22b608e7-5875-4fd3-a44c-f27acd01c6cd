﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Storeya.Core.Models.Payments;

namespace Storeya.Core.Helpers
{
    public class Helper
    {
        const int MaxProductsAllowed = 5000;

        public static int CalculateLimit(int existingAmount, int shopID)
        {
            string limitInConfig = ConfigHelper.GetValue("MaxProductsAmountPerStore");

            int importLimit = MaxProductsAllowed;
            if (!string.IsNullOrEmpty(limitInConfig))
            {
                importLimit = Convert.ToInt32(limitInConfig);
            }


            if (existingAmount >= importLimit)
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                Shop shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
                if (shop.IsPaid != null && shop.IsPaid.Value > 0)
                {
                    if (shop.IsPaid.Value == (int)PlanTypes.PrivateJetVeteran || shop.IsPaid.Value == (int)PlanTypes.PrivateJet)
                    {
                        importLimit = 10000;
                    }
                    else if (shop.IsPaid.Value == (int)PlanTypes.FirstClassVeteran || shop.IsPaid.Value == (int)PlanTypes.FirstClass || shop.IsPaid.Value == (int)PlanTypes.FirstClass_2)
                    {
                        importLimit = 5000;
                    }
                    else if (shop.IsPaid.Value == (int)PlanTypes.Business)
                    {
                        importLimit = 2000;
                    }

                    if (existingAmount >= importLimit)
                        Log4NetLogger.Info("Amount of products exeed allowed  - " + existingAmount + ". An Amount of Imported products for paid plan is limited to " + importLimit, shopID);
                    else
                        importLimit = existingAmount;
                }
                else if (shop.IsPaid != null && shop.IsPaid.Value == 0)
                {
                    Log4NetLogger.Info("Amount of products exeed allowed - " + existingAmount + ". Imported products will be limited to " + importLimit, shopID);
                }

                return importLimit;
            }

            return existingAmount;
        }

        public static int CalculateLimitForTabDelimited(int shopID)
        {
            int importLimit = MaxProductsAllowed;

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            if (shop.IsPaid != null && shop.IsPaid.Value == (int)PlanTypes.PrivateJetVeteran)
            {
                importLimit = 10000;
            }

            return importLimit;
        }

        public static int CalculateLimitToMaxProductsAllowed(int existingAmount, int shopID)
        {
            return CalculateLimit(existingAmount, shopID);

            //int importLimit = existingAmount;

            //StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //Shop shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            //if ((shop.IsPaid == null || shop.IsPaid.Value == (int)PlanTypes.None) && existingAmount > MaxProductsAllowed)
            //{
            //    importLimit = MaxProductsAllowed;
            //    Log4NetLogger.Info(string.Format("Not paid shop: amount of products will be limited from {0} to {1}", existingAmount, importLimit), shopID);                   

            //}

            //return importLimit;
        }


        public static double MaxErrorsCount(int totalProductsCount)
        {
            double maxErrorsCount = totalProductsCount * 0.05;
            if (maxErrorsCount < 10)
                maxErrorsCount = 10;
            return maxErrorsCount;
        }

        public static int GetTwentyPercentCount(int totalProductsCount)
        {
            double p = totalProductsCount * 0.2;
            if (p < 10)
                p = 10;
            int twentyPercentCount = (int)p;
            return twentyPercentCount;
        }

        public static int? GetIntOrNull(string value)
        {
            if (string.IsNullOrWhiteSpace(value) || string.IsNullOrEmpty(value))
            {
                return null;
            }
            else
            {
                int res = 0;
                if (int.TryParse(value, out res))
                {
                    return res;
                }
            }
            return null;
        }
        public static decimal? GetDecimalOrNull(string value)
        {
            if (string.IsNullOrWhiteSpace(value) || string.IsNullOrEmpty(value))
            {
                return null;
            }
            else
            {
                decimal res = 0;
                if (decimal.TryParse(value, out res))
                {
                    return res;
                }
            }
            return null;
        }

        public static int GetInt(string value)
        {
            int res = 0;
            if (int.TryParse(value, out res))
            {
                return res;
            }

            return 0;
        }



    }
}
