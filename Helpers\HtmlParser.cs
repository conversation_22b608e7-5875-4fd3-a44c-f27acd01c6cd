﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using HtmlAgilityPack;
using Storeya.Core.Models.DataProviders;

namespace Storeya.Core.Helpers
{
    public class HtmlParser
    {
        private string _htmlContent;

        public string Head;
        public string Body;
        public string Title;
        public string MetaDescription;
        public string MetaKeywords;
        public string MetaOgImage;
        public string MetaOgDescription;
        public string MetaOgTitle;
        public string MetaOgSiteName;
        public string CanonicalUrl;
        public List<string> Images;
        public List<string> LogoImages;
        public List<string> ImageTagsWithEmptyOrMissingAltAttribute;
        public List<string> DeprecatedHtmlTags;

        public string H1;
        public string H2;
        public string H3;
        public string H4;
        public string H5;
        public string H6;

        public string FaviconIcoUrl { get; internal set; }

        public HtmlParser(string htmlContent)
        {
            _htmlContent = htmlContent;
        }
        public static string GetTableByHeader(string html, string headerTag, string header)
        {
            HtmlDocument doc = new HtmlDocument();
            doc.LoadHtml(html);
            string selectionString = $"//{headerTag}[text()='{header}']";
            var h2Node = doc.DocumentNode.SelectSingleNode(selectionString);
            if (h2Node != null)
            {
                var tableNode = h2Node.NextSibling;
                while (tableNode != null && tableNode.Name != "table")
                {
                    tableNode = tableNode.NextSibling;
                }
                if (tableNode != null)
                {

                    return tableNode.OuterHtml;
                }
                else
                {
                    return null;
                }
            }
            return null;
        }
        public void ParseImagesOnly()
        {
            HtmlDocument doc = new HtmlDocument();
            doc.LoadHtml(this._htmlContent);
            HtmlNodeCollection images = doc.DocumentNode.SelectNodes("//img");
            if (images != null)
            {
                foreach (var imageItem in images)
                {
                    if (this.Images == null)
                        this.Images = new List<string>();

                    if (imageItem.Attributes["src"] != null && this.Images.All(a => a != imageItem.Attributes["src"].Value))
                    {
                        this.Images.Add(imageItem.Attributes["src"].Value);
                    }
                    else
                    {
                        if (imageItem.Attributes["data-src"] != null && this.Images.All(a => a != imageItem.Attributes["data-src"].Value))
                        {
                            this.Images.Add(imageItem.Attributes["data-src"].Value);
                        }
                    }
                }
            }
        }
        public List<string> GetAllValuesFromSubElements(string elementType, string attrName, string attrValue, string subElementType, string subAttrName, string subAttrValue, bool onlyFirstGroup = true)
        {
            List<string> res = new List<string>();
            string pattern = "<" + elementType + " " + attrName + "=\"" + attrValue + "\">(.*?)</" + elementType + ">";

            if (onlyFirstGroup)
            {
                string oc = RegexHelper.GetFirstGroupValue(this._htmlContent, pattern);
                if (string.IsNullOrEmpty(oc))
                {
                    return null;
                }
                res = GetAllValuesFromElements(subElementType, subAttrName, subAttrValue, oc);
            }
            else
            {
                List<string> ocs = RegexHelper.GetAllGroupValues(pattern, this._htmlContent);
                foreach (var item in ocs)
                {
                    try
                    {
                        var l = GetAllValuesFromElements(subElementType, subAttrName, subAttrValue, item);
                        if (l != null)
                        {
                            res.AddRange(l);
                        }
                    }
                    catch (System.Exception ex)
                    {
                        var d = ex;
                    }
                }
            }
            return res;
        }

        public Dictionary<string, string> GetAllValuesFromSubElementsComplex(string elementType, string attrName, string attrValue, string subElementType, string subAttrName, string subAttrValue, string subTitleAttrValue)
        {
            Dictionary<string, string> res = new Dictionary<string, string>();
            string pattern = "<" + elementType + " " + attrName + "=\"" + attrValue + "\">(.*?)</" + elementType + ">";


            List<string> ocs = RegexHelper.GetAllGroupValues(pattern, this._htmlContent);
            foreach (var item in ocs)
            {
                try
                {
                    string title = GetValueFromElement(subElementType, subAttrName, subTitleAttrValue, item);
                    var l = GetAllValuesFromElements(subElementType, subAttrName, subAttrValue, item);
                    if (l != null)
                    {
                        foreach (var k in l)
                        {
                            if (!res.ContainsKey(k))
                            {
                                res.Add(k, title);
                            }
                        }

                    }
                }
                catch (System.Exception ex)
                {
                    var d = ex;
                }
            }

            return res;
        }
        public string GetValueFromMetaElement(string elementType, string attrName, string attrValue, string getAttrValue, bool dontSplit = false)
        {
            List<string> res = new List<string>();
            string pattern = "<" + elementType + " " + attrName + "=" + attrValue + " " + getAttrValue + "=(.*?)>";


            string html = this._htmlContent;

            string oc = RegexHelper.GetFirstGroupValue(html, pattern);
            return oc;
        }
        public string GetValueFromElement(string elementType, string attrName, string attrValue, string html = null, bool dontSplit = false)
        {
            List<string> res = new List<string>();
            string pattern = "<" + elementType + " " + attrName + "=" + attrValue + ">(.*?)</" + elementType + ">";

            if (html == null)
            {
                html = this._htmlContent;
            }
            List<string> oc = RegexHelper.GetAllGroupValues(pattern, html);
            if (oc == null) return null;
            foreach (var item in oc)
            {
                if (dontSplit)
                {
                    string s = RegexHelper.StripHTML(item);
                    return s;
                }
                else
                {
                    string[] a = item.Split(',');
                    string s = item.Replace(a[1], "").Replace(",,", "");
                    s = RegexHelper.StripHTML(s);
                    return s;
                }
            }
            return null;
        }
        public string GetValueFromFirstElement(string elementType, string attrName, string attrValue)
        {
            string pattern = "<" + elementType + " " + attrName + "=" + attrValue + ">(.*?)</" + elementType + ">";
            Regex reg = RegexHelper.GetInstance(pattern);
            Match match = reg.Match(this._htmlContent);
            if (match.Success)
            {
                return match.Groups[1].Value;
            }
            return null;
        }

        public List<string> GetAllValuesFromElements(string elementType, string attrName, string attrValue, string html = null)
        {
            List<string> res = new List<string>();
            string pattern = "<" + elementType + " " + attrName + "=" + attrValue + ">(.*?)</" + elementType + ">";

            if (html == null)
            {
                html = this._htmlContent;
            }
            List<string> oc = RegexHelper.GetAllGroupValues(pattern, html);
            if (oc == null) return null;
            foreach (var item in oc)
            {
                string[] a = item.Split(',');
                res.Add(a[2]);
            }
            return res;
        }
        public void Parse()
        {
            HtmlDocument doc = new HtmlDocument();
            doc.LoadHtml(this._htmlContent);

            //the parameter is use xpath see: https://www.w3schools.com/xml/xml_xpath.asp 
            HtmlNodeCollection head = doc.DocumentNode.SelectNodes("//head");
            if (head != null)
            {
                this.Head = !string.IsNullOrEmpty(head[0].InnerHtml) ? head[0].InnerHtml.Trim() : null;
            }

            HtmlNodeCollection favicon = doc.DocumentNode.SelectNodes("//link[@rel='shortcut icon']");
            if (favicon != null)
            {
                this.FaviconIcoUrl = !string.IsNullOrEmpty(favicon[0].Attributes["href"].Value) ? HttpUtility.HtmlDecode(favicon[0].Attributes["href"].Value.Trim()) : null;
            }
            else
            {
                favicon = doc.DocumentNode.SelectNodes("//link[@rel='icon']");
                if (favicon != null)
                {
                    this.FaviconIcoUrl = !string.IsNullOrEmpty(favicon[0].Attributes["href"].Value) ? HttpUtility.HtmlDecode(favicon[0].Attributes["href"].Value.Trim()) : null;
                }
            }

            HtmlNodeCollection body = doc.DocumentNode.SelectNodes("//body");
            if (body != null)
                this.Body = !string.IsNullOrEmpty(body[0].InnerHtml) ? body[0].InnerHtml.Trim() : null;

            HtmlNodeCollection title = doc.DocumentNode.SelectNodes("//title");
            if (title != null)
                this.Title = !string.IsNullOrEmpty(title[0].InnerHtml) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(title[0].InnerHtml.Trim())) : null;

            HtmlNodeCollection h1 = doc.DocumentNode.SelectNodes("//h1");
            if (h1 != null)
                this.H1 = !string.IsNullOrEmpty(h1[0].InnerHtml) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(h1[0].InnerHtml.Trim())) : null;

            HtmlNodeCollection h2 = doc.DocumentNode.SelectNodes("//h2");
            if (h2 != null)
                this.H2 = !string.IsNullOrEmpty(h2[0].InnerHtml) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(h2[0].InnerHtml.Trim())) : null;

            HtmlNodeCollection h3 = doc.DocumentNode.SelectNodes("//h3");
            if (h3 != null)
                this.H3 = !string.IsNullOrEmpty(h3[0].InnerHtml) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(h3[0].InnerHtml.Trim())) : null;

            HtmlNodeCollection h4 = doc.DocumentNode.SelectNodes("//h4");
            if (h4 != null)
                this.H4 = !string.IsNullOrEmpty(h4[0].InnerHtml) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(h4[0].InnerHtml.Trim())) : null;

            HtmlNodeCollection h5 = doc.DocumentNode.SelectNodes("//h5");
            if (h5 != null)
                this.H5 = !string.IsNullOrEmpty(h5[0].InnerHtml) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(h5[0].InnerHtml.Trim())) : null;

            HtmlNodeCollection h6 = doc.DocumentNode.SelectNodes("//h6");
            if (h6 != null)
                this.H6 = !string.IsNullOrEmpty(h6[0].InnerHtml) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(h6[0].InnerHtml.Trim())) : null;

            HtmlNodeCollection metaDescription = doc.DocumentNode.SelectNodes("//meta[@name='description']");
            if (metaDescription != null)
                this.MetaDescription = !string.IsNullOrEmpty(metaDescription[0].Attributes["content"].Value) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(metaDescription[0].Attributes["content"].Value.Trim())) : null;

            HtmlNodeCollection metaKeywords = doc.DocumentNode.SelectNodes("//meta[@name='keywords']");
            if (metaKeywords != null)
                this.MetaKeywords = !string.IsNullOrEmpty(metaKeywords[0].Attributes["content"].Value) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(metaKeywords[0].Attributes["content"].Value.Trim())) : null;

            HtmlNodeCollection metaOgDescription = doc.DocumentNode.SelectNodes("//meta[@property='og:description']");
            if (metaOgDescription != null)
            {
                if (metaOgDescription.Count > 0 && metaOgDescription[0].Attributes["content"] != null)
                {
                    this.MetaOgDescription = !string.IsNullOrEmpty(metaOgDescription[0].Attributes["content"].Value) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(metaOgDescription[0].Attributes["content"].Value.Trim())) : null;
                }
            }
            HtmlNodeCollection metaOgImage = doc.DocumentNode.SelectNodes("//meta[@property='og:image']");
            if (metaOgImage != null)
            {
                if (metaOgImage.Count > 0 && metaOgImage[0].Attributes["content"] != null)
                {
                    this.MetaOgImage = !string.IsNullOrEmpty(metaOgImage[0].Attributes["content"].Value) ? HttpUtility.HtmlDecode(metaOgImage[0].Attributes["content"].Value.Trim()) : null;
                }
            }
            HtmlNodeCollection metaOgSiteName = doc.DocumentNode.SelectNodes("//meta[@property='og:site_name']");
            if (metaOgSiteName != null)
                this.MetaOgSiteName = !string.IsNullOrEmpty(metaOgSiteName[0].Attributes["content"].Value) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(metaOgSiteName[0].Attributes["content"].Value.Trim())) : null;

            HtmlNodeCollection metaOgTitle = doc.DocumentNode.SelectNodes("//meta[@property='og:title']");
            if (metaOgTitle != null)
                this.MetaOgTitle = !string.IsNullOrEmpty(metaOgTitle[0].Attributes["content"].Value) ? HttpUtility.HtmlDecode(SpecialCharactersHandler.ReplaceSpecialSymbols(metaOgTitle[0].Attributes["content"].Value.Trim())) : null;

            HtmlNodeCollection canonicalUrl = doc.DocumentNode.SelectNodes("//link[@rel='canonical']");
            if (canonicalUrl != null)
                this.CanonicalUrl = canonicalUrl[0].Attributes["href"].Value;

            HtmlNodeCollection images = doc.DocumentNode.SelectNodes("//img");
            if (images != null)
            {
                foreach (var imageItem in images)
                {
                    if (this.Images == null)
                        this.Images = new List<string>();

                    if (imageItem.Attributes["src"] != null && this.Images.All(a => a != imageItem.Attributes["src"].Value))
                    {
                        this.Images.Add(imageItem.Attributes["src"].Value);

                        if (imageItem.Attributes["alt"] == null || string.IsNullOrEmpty(imageItem.Attributes["alt"].Value))
                        {
                            if (this.ImageTagsWithEmptyOrMissingAltAttribute == null)
                                this.ImageTagsWithEmptyOrMissingAltAttribute = new List<string>();

                            this.ImageTagsWithEmptyOrMissingAltAttribute.Add(imageItem.Attributes["src"].Value);
                        }


                    }
                    else
                    {
                        if (imageItem.Attributes["data-src"] != null && this.Images.All(a => a != imageItem.Attributes["data-src"].Value))
                        {
                            this.Images.Add(imageItem.Attributes["data-src"].Value);

                            if (imageItem.Attributes["alt"] == null || string.IsNullOrEmpty(imageItem.Attributes["alt"].Value))
                            {
                                if (this.ImageTagsWithEmptyOrMissingAltAttribute == null)
                                    this.ImageTagsWithEmptyOrMissingAltAttribute = new List<string>();

                                this.ImageTagsWithEmptyOrMissingAltAttribute.Add(imageItem.Attributes["data-src"].Value);
                            }
                        }
                    }
                }
            }

            HtmlNodeCollection links = doc.DocumentNode.SelectNodes("//a/img");
            if (links != null)
            {
                var logolinks = links.Where(x => x.InnerHtml.ToLower().Contains("logo") || x.OuterHtml.ToLower().Contains("logo"));
                if (logolinks != null)
                {
                    foreach (var logolink in logolinks)
                    {
                        if (this.LogoImages == null)
                            this.LogoImages = new List<string>();

                        if (logolink.Attributes["src"] != null && this.LogoImages.All(a => a != logolink.Attributes["src"].Value))
                        {
                            this.LogoImages.Add(logolink.Attributes["src"].Value);
                        }
                        else
                        {
                            if (logolink.Attributes["data-src"] != null && this.LogoImages.All(a => a != logolink.Attributes["data-src"].Value))
                            {
                                this.LogoImages.Add(logolink.Attributes["data-src"].Value);
                            }
                        }
                    }
                }
            }


            var depricatedTags = FillDepricatedTags();
            foreach (var depricatedTag in depricatedTags)
            {
                if (this._htmlContent.Contains(depricatedTag))
                {
                    if (this.DeprecatedHtmlTags == null)
                        this.DeprecatedHtmlTags = new List<string>();

                    if (this.DeprecatedHtmlTags.All(a => a != depricatedTag))
                        this.DeprecatedHtmlTags.Add(depricatedTag);
                }
            }
        }

        private static List<string> FillDepricatedTags()
        {
            List<string> depricatedTags = new List<string>();
            depricatedTags.Add("<applet>");
            depricatedTags.Add("<basefont>");
            depricatedTags.Add("<center>");
            depricatedTags.Add("<dir>");
            depricatedTags.Add("<font>");
            depricatedTags.Add("<isindex>");
            depricatedTags.Add("<menu>");
            depricatedTags.Add("<s>");
            depricatedTags.Add("<strike>");
            depricatedTags.Add("<u>");
            return depricatedTags;
        }
        public static HtmlParser GetParsedShopifyContent(string shopUrl)
        {
            try
            {
                string homePageHtml = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(shopUrl, true, null, false, false);
                HtmlParser parser = new HtmlParser(homePageHtml);
                parser.Parse();
                return parser;
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "GetParsedShopifyContent failed", $"Url: {shopUrl} " + ex.ToString());
                return null;
            }
        }
    }
}
