﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using Storeya.Core.Helpers;

namespace Storeya.Core
{
    public class HttpHelper
    {
        public static string GetCurrectBaseUrl()
        {
            return GetCurrentDomain() + "/";
        }

        public static string GetCurrentDomain()
        {
            if (HttpContext.Current == null || HttpContext.Current.Request == null)
            {
                return "https://www.storeya.com";
            }
            var protocol = "https://";
            return protocol + HttpContext.Current.Request.Url.Authority
                + HttpContext.Current.Request.ApplicationPath.TrimEnd('/');
        }

        public static string GetCurrentDomainHttps()
        {
            var protocol = "https://";
            if (HttpContext.Current == null || HttpContext.Current.Request == null
                || (HttpContext.Current.Request.IsLocal && !HttpContext.Current.Request.Url.Authority.Contains("apps.fluxas.com")))
            {
                string currentDomain = "https://www.fluxas.com"; //for fluxas.com
                return currentDomain;
            }

            return protocol + HttpContext.Current.Request.Url.Authority
                + HttpContext.Current.Request.ApplicationPath.TrimEnd('/');
        }

        public static string GetCurrentDomainRelativeProtocol()
        {
            return "//" + HttpContext.Current.Request.Url.Authority
                + HttpContext.Current.Request.ApplicationPath.TrimEnd('/');
        }

        public static bool IsSecure()
        {
            return HttpContext.Current.Request.IsSecureConnection;
        }

        public static bool IsMobileRequest()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.UserAgent != null)
            {
                return MobileHelper.IsMobileUserAgent(HttpContext.Current.Request.UserAgent);
            }
            return false;
        }

        public static bool IsLocalIp(int ip)
        {
            string ip_string = GetIpString(ip);
            return IsLocalIp(ip_string);
        }

        public static bool IsLocalIp(string ip_string)
        {
            if (ip_string == "127.0.0.1")
                return true;

            return false;
        }


        public static int GetIP()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null)
            {
                //var clientIP = HttpContext.Current.Request.UserHostAddress;
                var clientIP = SslHelper.FetchUserIP();
                try
                {
                    var ips = clientIP.Split(',');
                    return BitConverter.ToInt32(IPAddress.Parse(ips[0]).GetAddressBytes(), 0);
                }
                catch
                {
                    return -1;
                }
            }
            return 0;
        }

        public static string GetIpString(int ip)
        {
            string ipAdress_string = new IPAddress(BitConverter.GetBytes(ip)).ToString();
            return ipAdress_string;          
        }

        public static string GetIpString()
        {
            return SslHelper.FetchUserIP();
        }

        public static string GetRefererDomain()
        {
            try
            {
                if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.UrlReferrer != null)
                {
                    return HttpContext.Current.Request.UrlReferrer.Host;
                }
                return "";
            }
            catch
            {
                return "";
            }
        }
        public static string GetFullUrl()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.Url != null)
            {
                return HttpContext.Current.Request.Url.ToString();
            }
            return null;
        }

        public static string GetPathAndQueryl()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.Url.PathAndQuery != null)
            {
                return HttpContext.Current.Request.Url.ToString();
            }
            return null;
        }
        public static string GetRefererPathAndQuery()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.UrlReferrer != null)
            {
                return HttpContext.Current.Request.UrlReferrer.PathAndQuery;
            }
            return null;
        }

        public static string GetRefererAbsoluteUri()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.UrlReferrer != null)
            {
                return HttpContext.Current.Request.UrlReferrer.AbsoluteUri;
            }
            return null;
        }


        public static bool IsBot()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.UserAgent != null)
            {
                string agent = HttpContext.Current.Request.UserAgent;
                if (!string.IsNullOrEmpty(agent))
                {
                    agent = agent.ToLower();
                    if (FbHelper.IsFbCrawler(agent) || agent.Contains("twitterbot") || agent.Contains("googlebot") || agent.Contains("yahoo! slurp"))
                    {
                        return true;
                    }
                }

            }
            return false;
        }

        public static bool IsExternalReferer(string referer)
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null && HttpContext.Current.Request.Url != null)
            {
                if (referer.Contains(HttpContext.Current.Request.Url.Host))
                {
                    return false;
                }
            }
            return true;
        }

    }
}