﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.IO.Compression;
using System.IO;
using Storeya.Core.Models.DataProviders;
using Storeya.Core.Models;

namespace Storeya.Core.Helpers
{


    public static class HttpRequestResponseHelper
    {
        public static string GetHttpWebResponseFromFullRequest(string url, bool allowAutoRedirect = true, int? shopID = null, bool convertToUtf8 = false, bool checkMetaCharSet = false, string metaCharSet = "UTF-8", bool useSecurityProtocol = true)
        {
            string content = null;
            Console.WriteLine("GetHttpWebResponseFromFullRequest start " + TextManipulationHelper.Truncate(url, 50));
            if (useSecurityProtocol)
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            }
            else
            {
                ServicePointManager.SecurityProtocol = new SecurityProtocolType();
            }

            HttpWebRequest request = null;
            request = WebRequest.CreateHttp(url);

            request.Method = "GET";
            request.AllowAutoRedirect = allowAutoRedirect;
            request.KeepAlive = false;
            //  request.Headers.Add("Cache-Control", "max-age=0");
            //request.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31";
            //request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.120 Safari/537.36";
            //request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36.";
            request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
            request.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
            request.Headers.Add("Accept-Encoding", "gzip,deflate,sdch");
            request.Headers.Add("Accept-Language", "en-US,en;q=0.8");
            request.Headers.Add("Accept-Charset", "ISO-8859-1,utf-8;q=0.7,*;q=0.3");
            request.CookieContainer = new CookieContainer();
            request.CookieContainer.Add(new Uri(url), new Cookie("id", "1234"));
            request.UseDefaultCredentials = true;
            //request.Timeout = 5000;
            //Console.WriteLine(string.Format("{0} requesting for {1}", DateTime.Now, url));           

            //WebClient WebClient = new WebClient();
            //return WebClient.DownloadString(url);
            HttpWebResponse webResponse = null;

            using (webResponse = (HttpWebResponse)request.GetResponse())
            {
                if (webResponse != null)
                {
                    Stream responseStream = responseStream = webResponse.GetResponseStream();
                    if (webResponse.ContentEncoding.ToLower().Contains("gzip"))
                    {
                        responseStream = new GZipStream(responseStream, CompressionMode.Decompress);
                    }
                    if (convertToUtf8)
                    {
                        string characterSet = null;
                        if (webResponse.CharacterSet == "")
                        {
                            characterSet = "utf-8";
                        }
                        else
                        {
                            characterSet = webResponse.CharacterSet;
                        }
                        content = ConvertToEncoding(responseStream, characterSet);
                        int code = 0;
                        switch (webResponse.CharacterSet.ToLower())
                        {
                            case "windows-1255":
                                code = 1255;
                                break;
                            case "windows-1252":
                                code = 1252;
                                break;
                            default:
                                break;
                        }
                        if (code > 0)
                        {
                            content = content.ConvertEncodingToUtf8(code);
                        }
                    }
                    else
                    {
                        content = ConvertToEncoding(responseStream, metaCharSet);
                        if (checkMetaCharSet)
                        {
                            if (content.Contains("<meta http-equiv=\"Content-Type\" content=\"text/html; charset="))
                            {
                                string pattern = @"<meta http-equiv=""Content-Type"" content=""text/html; charset=?(.*?)\"" />";
                                string charset = RegexHelper.GetFirstGroupValue(content, pattern);
                                if (!string.IsNullOrEmpty(charset) && charset.ToLower() != metaCharSet.ToLower())
                                {
                                    return GetHttpWebResponseFromFullRequest(url, allowAutoRedirect, shopID, convertToUtf8, false, charset);
                                }
                            }
                        }
                    }
                    webResponse.Close();
                }
                webResponse.Dispose();
            }
            return content;
        }

        private static string ConvertToEncoding(Stream responseStream, string characterSet)
        {
            string content;
            Encoding enc = System.Text.Encoding.GetEncoding(characterSet);
            using (StreamReader streamReader = new StreamReader(responseStream, enc))
            {
                content = streamReader.ReadToEnd();
            }

            return content;
        }

        public static string GetHttpWebResponseWithoutTryCatch(string path, bool allowAutoRedirect = true)
        {
            string content = null;

            HttpWebResponse webResponse = GetResponse(path, allowAutoRedirect);

            if (webResponse != null)
            {
                content = GetContent(webResponse);
            }
            return content;
        }

        public static string GetContent(HttpWebResponse webResponse)
        {
            string content = null;

            Stream responseStream = responseStream = webResponse.GetResponseStream();
            if (webResponse.ContentEncoding.ToLower().Contains("gzip"))
                responseStream = new GZipStream(responseStream, CompressionMode.Decompress);

            Encoding enc = System.Text.Encoding.GetEncoding("UTF-8");
            using (StreamReader streamReader = new StreamReader(responseStream, enc))
            {
                content = streamReader.ReadToEnd();
            }
            return content;
        }


        public static StoreYaWebResponse GetStoreYaHttpWebResponse(string url, bool allowAutoRedirect, int? shopID = null)
        {
            try
            {

                //if (!string.IsNullOrEmpty(url) && url.Contains("www.shop.com"))
                //{
                //    using (HttpWebResponse webResponse = ShopComProvider.GetResponse(url, allowAutoRedirect))
                //    {
                //        StoreYaWebResponse storeYaWebResponse = new StoreYaWebResponse(webResponse);
                //        webResponse.Close();
                //        return storeYaWebResponse;
                //    }
                //}
                //else
                //{
                return GetStoreYaResponse(url, allowAutoRedirect);
                //}
            }
            catch (WebException ex)
            {
                string errorMessage = null;
                HttpWebResponse webResponse = (HttpWebResponse)ex.Response;
                if (webResponse != null)
                {
                    errorMessage = GetContent(webResponse);
                }

                if (!string.IsNullOrEmpty(errorMessage) && errorMessage.ToLower().StartsWith("<!doctype html>"))
                    errorMessage = "The response contains an HTML content.";

                ConsoleAppHelper.WriteError(string.Format("Failed to get response from {0}, Error: {1}", url, errorMessage), ex, shopID ?? 0);
                return null;
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError(string.Format("Failed to get response from {0}", url), ex, shopID ?? 0);
                return null;
            }
        }

        public static HttpWebResponse GetHttpWebResponse(string url, bool allowAutoRedirect, int? shopID = null)
        {
            try
            {
                return GetResponse(url, allowAutoRedirect);
                //if (!string.IsNullOrEmpty(url) && url.Contains("www.shop.com"))
                //{
                //    return ShopComProvider.GetResponse(url, allowAutoRedirect);
                //}
                //else
                //{
                //    return GetResponse(url, allowAutoRedirect);
                //}

            }
            catch (WebException ex)
            {
                string errorMessage = null;
                HttpWebResponse webResponse = (HttpWebResponse)ex.Response;
                if (webResponse != null)
                {
                    errorMessage = GetContent(webResponse);
                }

                if (!string.IsNullOrEmpty(errorMessage) && errorMessage.ToLower().StartsWith("<!doctype html>"))
                    errorMessage = "The response contains an HTML content.";

                ConsoleAppHelper.WriteError(string.Format("Failed to get response from {0}, Error: {1}", url, errorMessage), ex, shopID ?? 0);
                return null;
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError(string.Format("Failed to get response from {0}", url), ex, shopID ?? 0);
                return null;
            }
        }
        public static HttpWebResponse GetRawResponse(string url, bool allowAutoRedirect)
        {
            return GetResponse(url, allowAutoRedirect);
        }
        private static HttpWebResponse GetResponse(string url, bool allowAutoRedirect)
        {
            // handling https requests           
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "GET";
            request.AllowAutoRedirect = allowAutoRedirect;
            request.KeepAlive = true;
            request.Headers.Add("Cache-Control", "max-age=0");
            //request.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31";
            //request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.120 Safari/537.36";
            request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
            string domain = UrlPathHelper.GetDomainName(url);
            if (domain == "www.bigmen.co.uk")
            {
                request.UserAgent = "PostmanRuntime/7.43.0";
                request.Referer = "https://www.google.com/";
                request.Headers.Add("Origin", $"https://{domain}");
                Console.WriteLine($"Origin: https://{domain}");
            }
            request.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
            request.Headers.Add("Accept-Encoding", "gzip,deflate,sdch");
            request.Headers.Add("Accept-Language", "en-US,en;q=0.8");
            request.Headers.Add("Accept-Charset", "ISO-8859-1,utf-8;q=0.7,*;q=0.3");
            request.CookieContainer = new CookieContainer();
            request.CookieContainer.Add(new Uri(url), new Cookie("id", "1234"));
            request.UseDefaultCredentials = true;

            //Console.WriteLine(string.Format("{0} requesting for {1}", DateTime.Now, url));

            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            return response;
        }

        private static StoreYaWebResponse GetStoreYaResponse(string url, bool allowAutoRedirect)
        {
            // handling https requests           
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "GET";
            request.AllowAutoRedirect = allowAutoRedirect;
            request.KeepAlive = true;
            request.Headers.Add("Cache-Control", "max-age=0");
            //request.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31";
            // request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.120 Safari/537.36";
            request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

            request.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
            request.Headers.Add("Accept-Encoding", "gzip,deflate,sdch");
            request.Headers.Add("Accept-Language", "en-US,en;q=0.8");
            request.Headers.Add("Accept-Charset", "ISO-8859-1,utf-8;q=0.7,*;q=0.3");
            request.CookieContainer = new CookieContainer();
            request.CookieContainer.Add(new Uri(url), new Cookie("id", "1234"));
            request.UseDefaultCredentials = true;

            //Console.WriteLine(string.Format("{0} requesting for {1}", DateTime.Now, url));

            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            {

                StoreYaWebResponse storeYaWebResponse = new StoreYaWebResponse(response);
                response.Close();
                return storeYaWebResponse;
            }
        }

        public static HttpWebRequest SetRequest(string url, string method, string userAgent = null, List<RequestHeader> headers = null, string accept = null, string contentType = null, string requestDetails = null)
        {
            // handling https requests
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            //bypass (ignore) SSL certificate
            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;

            HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(url);
            request.Method = method;
            // request.AllowAutoRedirect = true;
            request.KeepAlive = true;
            request.Headers.Add("Cache-Control", "max-age=0");
            request.UserAgent = userAgent;
            request.Accept = accept;

            if (headers != null && headers.Count > 0)
            {
                foreach (var header in headers)
                {
                    request.Headers.Add(header.Name, header.Value);
                }
            }

            if (method == "POST")
            {
                request.ContentType = contentType;
                byte[] arr = Encoding.ASCII.GetBytes(requestDetails);
                request.ContentLength = arr.Length;
                Stream dataStream = request.GetRequestStream();
                dataStream.Write(arr, 0, arr.Length);
                dataStream.Close();
            }

            return request;
        }

        public static string GetResponseContent(HttpWebRequest request)
        {
            string content = null;

            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            if (response != null)
            {
                content = GetContent(response);
            }
            return content;
        }

    }

    public class RequestHeader
    {
        public string Name { get; set; }
        public string Value { get; set; }
    }
}
