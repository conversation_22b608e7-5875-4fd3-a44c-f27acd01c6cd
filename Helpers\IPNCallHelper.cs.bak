﻿using Ez.Newsletter.MagentoApi;
using Storeya.Core.Entities;
using Storeya.Core.Models;
using Storeya.Core.Models.AppStore;
using Storeya.Core.Models.FastSpring;
using Storeya.Core.Models.Marketplaces;
using Storeya.Core.Models.Payments;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models.TrafficBoosterModels.TrafficChannels;
using System;
using System.Collections.Generic;
using System.Data.Entity.Validation;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using static Storeya.Core.Models.FastSpring.FastSpringHelper;

namespace Storeya.Core.Helpers
{
    public static class IPNCallHelper
    {
        public static void UpdateIPNSubType(int id, TransactionSubType subType)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var ipn = db.PlimusIpnCalls.SingleOrDefault(i => i.ID == id);
            if (ipn == null) return;
            TransactionSubType pre = ipn.TransactionSubType.HasValue ? (TransactionSubType)ipn.TransactionSubType : TransactionSubType.None;
            ipn.TransactionSubType = subType.GetHashCode();
            db.SaveChanges();
            ConsoleAppHelper.WriteLogWithDB($"IPN {id} - {ipn.subscriptionId}, Sub type was changed from:{pre} to: {subType} {ipn.TransactionType}", ipn.ShopID);
        }
        public static int SaveToDB(BlueSnapIpnCallEntity call, TransactionSubType? subType = null)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            try
            {

                int shopid = Helper.GetIntOrNull(call.shopID) ?? 0;

                PlimusIpnCall ipnCall = new PlimusIpnCall();
                ipnCall.accountId = call.accountId;

                ipnCall.AppID = Helper.GetIntOrNull(call.appID); // Helper.StringToNumber(call.appID);

                ipnCall.address1 = call.address1;
                ipnCall.address2 = call.address2;
                ipnCall.city = call.city;
                ipnCall.company = call.company;
                ipnCall.contractId = call.contractId;
                ipnCall.contractName = call.contractName;
                ipnCall.contractOwner = call.contractOwner;
                ipnCall.contractPrice = call.contractPrice;
                ipnCall.country = call.country;
                ipnCall.coupon = call.coupon;
                ipnCall.couponCode = call.couponCode;
                ipnCall.couponValue = call.couponValue;
                ipnCall.CreditCardType = call.creditCardType;
                ipnCall.currency = call.currency;
                ipnCall.email = string.IsNullOrEmpty(call.email) ? call.invoiceEmail : call.email;
                ipnCall.extension = call.extension;
                ipnCall.faxNumber = call.faxNumber;

                ipnCall.firstName = string.IsNullOrEmpty(call.invoiceFirstName) ? call.firstName : call.invoiceFirstName;
                ipnCall.lastName = string.IsNullOrEmpty(call.invoiceLastName) ? call.lastName : call.invoiceLastName;

                ipnCall.homePhone = call.homePhone;
                ipnCall.invoiceAmount = call.invoiceAmount;
                ipnCall.taxAmountUSD = call.taxAmountUSD;

                ipnCall.invoiceAmountInCurrency = call.invoiceAmountInCurrency;
                ipnCall.taxAmountInCurrency = call.taxAmountInCurrency;

                ipnCall.invoiceInfoURL = call.invoiceInfoURL;

                ipnCall.licenseKey = call.licenseKey;
                ipnCall.mobilePhone = call.mobilePhone;
                ipnCall.OriginalReferenceNumber = call.originalReferenceNumber;
                ipnCall.PaymentMethod = call.paymentMethod;
                ipnCall.productId = call.productId;
                ipnCall.productName = call.productName;
                ipnCall.quantity = Helper.GetIntOrNull(call.quantity);
                ipnCall.ReferenceNumber = call.referenceNumber;
                ipnCall.referrer = call.referrer;
                ipnCall.remoteAddress = call.remoteAddress;
                ipnCall.ShopID = shopid;
                ipnCall.state = call.state;
                ipnCall.TestMode = call.testMode;
                ipnCall.title = call.title;
                ipnCall.TransactionDate = ConvertToDateTime(call.transactionDate);
                ipnCall.TransactionType = call.transactionType;
                ipnCall.TransactionSubType = subType.HasValue ? subType.GetHashCode() : ConvertToSubType(call.transactionType).GetHashCode();
                ipnCall.UntilDate = ConvertToDateTime(call.untilDate);
                ipnCall.workPhone = call.workPhone;
                ipnCall.zipCode = call.zipCode;
                ipnCall.subscriptionId = call.subscriptionID;
                ipnCall.CancelReason = call.cancelReason + call.reversalReason;
                ipnCall.InsertedAt = DateTime.Now;
                ipnCall.TaxRateUSD = call.TaxRateUSD;
                //ipnCall.CardLast4Digt = call.CardLast4Digt;
                ipnCall.ChargeFrequency = call.ChargeFrequency;
                ipnCall.NextPaymenDate = ConvertToDateTime(call.NextPaymenDate);
                ipnCall.NextPaymentAmount = call.NextPaymentAmount;
                ipnCall.AgreeID = Helper.GetIntOrNull(call.agreeID);
                ipnCall.TransactionId = call.TransactionId;
                ipnCall.cbStatus = call.cbStatus;
                if (call.PaymentProvider.HasValue)
                {
                    ipnCall.PaymentAdapterType = call.PaymentProvider.Value.GetHashCode();
                }
                var ret = db.PlimusIpnCalls.Add(ipnCall);
                try
                {
                    db.SaveChanges();
                }
                catch (DbEntityValidationException exDb)
                {
                    var shopToLog = shopid;
                    if (shopToLog == 0)
                    {
                        shopToLog = (int)Log4NetLogger.SpecialShopIDs.CriticalError;
                    }
                    var newException = new FormattedDbEntityValidationException(exDb);
                    Log4NetLogger.Error("Failed to save IPN call details to DB.", newException, shopToLog);
                    try { db.PlimusIpnCalls.Remove(ipnCall); } catch { }
                }
                catch (Exception ex)
                {
                    Log4NetLogger.Error("Failed to save IPN call details to DB.", ex, (int)Log4NetLogger.SpecialShopIDs.CriticalError);
                    if (!string.IsNullOrEmpty(call.shopID))
                    {
                        Log4NetLogger.Error("Failed to save IPN call details to DB.", ex, shopid);
                    }
                    try { db.PlimusIpnCalls.Remove(ipnCall); } catch { }
                }

                if (call.PaymentProvider == PaymentAdapterTypes.FastSpring)
                {
                    PaymentsEventsHistoryHelper.AddEvent(ipnCall.ShopID, "System", PaymentsEventsHistoryHelper.EventHistoryCategory.WebHook, call.subscriptionID,
                        ipnCall.TransactionDate, PaymentsEventsHistoryHelper.EventHistoryStatus.Success, PaymentsEventsHistoryHelper.EventType.None,
                        PaymentAdapterTypes.FastSpring, call.transactionType, call.ToJson());
                }
                return ipnCall.ID;

            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Failed to save IPN call 2 details to DB. call.shopID = " + call.shopID, ex, (int)Log4NetLogger.SpecialShopIDs.CriticalError);
                Log4NetLogger.Error("Failed to save IPN call 2 details to DB.", ex, Convert.ToInt32(call.shopID ?? "0"));
            }
            return 0;
        }
        public static TransactionSubType ConvertToSubType(string transactionType)
        {
            switch (transactionType)
            {
                case "CANCELLATION":
                case "CANCELLATION_REFUND":
                case "CANCEL_ON_RENEWAL":
                    return TransactionSubType.Cancelled;
                case "AUTH_ONLY":
                case "UNDER_REVIEW":
                case "CONTRACT_CHANGE":
                    return TransactionSubType.None;
                case "CHARGE":
                    return TransactionSubType.Charge;
                case "DECLINE":
                    return TransactionSubType.Decline;
                case "REFUND":
                    return TransactionSubType.Refund;
                case "RECURRING":
                    return TransactionSubType.Recurring;
                default:
                    return TransactionSubType.None;

            }

        }
        public static DateTime? ConvertToDateTime(string datetime)
        {
            try
            {
                if (string.IsNullOrEmpty(datetime))
                {
                    return null;
                }
                return Convert.ToDateTime(datetime);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Info($"Failed to convert date from IPN call. {datetime}", ex);
                return null;
            }

        }
        public static void AddSystemEvent(int shopID, BlueSnapIpnCallEntity call)
        {
            int appID = (Helper.GetIntOrNull(call.appID) ?? 0);
            int planID = (Helper.GetIntOrNull(call.planID) ?? 0);
            decimal amount = (Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0);
            int? agreeID = Helper.GetIntOrNull(call.agreeID);
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            Shop shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            int userID = (shop != null ? (shop.UserID ?? 0) : 0);

            if (call.transactionType == "RECURRING")
            {
                ReportIpnAsSystemEvent(shopID, call, userID, SystemEventActions.Recurring);
            }
            else if (call.transactionType == "CANCELLATION" || call.transactionType == "CANCELLATION_REFUND" || call.transactionType == "CANCEL_ON_RENEWAL" || call.transactionType == "CHARGEBACK")
            {
                ReportIpnAsSystemEvent(shopID, call, userID, SystemEventActions.Cancellation);
            }
            else if (call.transactionType == "CONTRACT_CHANGE")
            {
                ReportIpnAsSystemEvent(shopID, call, userID, SystemEventActions.SubscriptionChange);
            }
            else if (call.transactionType == "ORDER_FAILURE")
            {
                ReportIpnAsSystemEvent(shopID, call, userID, SystemEventActions.CheckoutChargeFailed);
            }
            else if (call.transactionType == "SUBSCRIPTION_CHARGE_FAILURE")
            {
                ReportIpnAsSystemEvent(shopID, call, userID, SystemEventActions.ChargeFailed);
            }
            else if (call.transactionType == "REFUND")
            {
                ReportIpnAsSystemEvent(shopID, call, userID, SystemEventActions.Refund);
            }
            else if (IsPaymentAuthorization(call))
            {
                ReportIpnAsSystemEvent(shopID, call, userID, SystemEventActions.NewPaidAccount);
                if (appID == (int)AppTypes.TrafficBooster)
                {
                    RecurringTreatment(shopID, userID, appID, planID, amount, agreeID);
                }
            }
            else
            {
                ReportIpnAsSystemEvent(shopID, call, userID, SystemEventActions.Other);
            }
        }

        public static bool IsPaymentAuthorization(BlueSnapIpnCallEntity call)
        {
            return call.transactionType == "AUTH_ONLY" || call.transactionType == "CHARGE" || call.transactionType == "UNDER_REVIEW";
        }

        public static void ReportIpnAsFailedChargeEventTest(int shopID)
        {
            var db = DataHelper.GetStoreYaEntities();
            var userID = db.Shops.Where(x => x.ID == shopID).Single().UserID;
            BlueSnapIpnCallEntity call = new BlueSnapIpnCallEntity
            {
                appID = "200",
                planID = "21",
                agreeID = "123",
                email = "<EMAIL>",
            };
            ReportIpnAsSystemEvent(shopID, call, userID.Value, SystemEventActions.ChargeFailed);
        }
        private static void ReportIpnAsSystemEvent(int shopID, BlueSnapIpnCallEntity call, int userID, SystemEventActions eventType)
        {
            int appID = (Helper.GetIntOrNull(call.appID) ?? 0);
            int planID = (Helper.GetIntOrNull(call.planID) ?? 0);
            int? agreeID = Helper.GetIntOrNull(call.agreeID);
            decimal amount = (Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0);
            string labelSufix = string.Empty;
            if (eventType == SystemEventActions.Recurring || eventType == SystemEventActions.Other || eventType == SystemEventActions.Refund)
            {
                amount = (Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0) - (Helper.GetDecimalOrNull(call.taxAmountUSD) ?? 0);
                labelSufix = $" {call.subscriptionID} {call.currency} {call.invoiceAmountInCurrency}";
            }
            if (!string.IsNullOrEmpty(call.cancelReason))
            {
                labelSufix = $"{labelSufix}, Reason:{call.cancelReason}";
            }
            SystemEventHelper.Add(userID, shopID, appID, SystemEventTypes.IPNCalls, eventType, $"PlanID_{planID}_{call.email}{labelSufix}", amount, agreeID);
        }

        private static void RecurringTreatment(int shopID, int userID, int appID, int planID, decimal amount, int? agreeID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            TrafficBooster existingTbSettings = TrafficBoostersDbHelper.GetSettings(shopID);
            string eventName = "Upgrade/Re-purchase after the TB app was disabled";

            bool isReccurring = false;
            if (!string.IsNullOrEmpty(existingTbSettings.AdWordsAccount)) // upgrade/re-purchase after the app was disabled
            {
                isReccurring = true;
                DateTime monthBefore = DateTime.Now.AddMonths(-1);
                if (existingTbSettings.LastPaymentDate.HasValue && existingTbSettings.LastPaymentDate >= monthBefore) //If it was within a month - this is an upgrade.
                {
                    eventName = "Upgrade";
                }
                else if (existingTbSettings.LastPaymentDate.HasValue && existingTbSettings.LastPaymentDate < monthBefore) //if Last payment was more than one month before - this is probably repurchasing
                {
                    eventName = "Re-purchase after the TB app was disabled";
                }
            }

            if (isReccurring == false)
            {
                List<TrafficBooster> cancelledApps = TrafficBoostersDbHelper.GetRemovedAppsSettings(shopID);
                if (cancelledApps != null && cancelledApps.Count > 0 && cancelledApps.Where(x => !string.IsNullOrEmpty(x.AdWordsAccount)).Any())  // re-purchased after the app removal
                {
                    isReccurring = true;
                    eventName = "Re-purchased after the TB app was removed";
                }
            }


            if (isReccurring)
            {
                SystemEventHelper.Add(userID, shopID, appID, SystemEventTypes.Payments, SystemEventActions.Payments_TbUpdated, planID.ToString(), amount, agreeID);

                SystemEventHelper.Add(userID, shopID, appID, SystemEventTypes.IPNCalls, SystemEventActions.Recurring, string.Format("PlanID_{0}_Fake_Recurring", planID), amount, agreeID);

                //if the cancellation call of the old subscription succeeded to extract an app before it was marked with a new subscription id.
                ShopApp shopApp = db.ShopApps.FirstOrDefault(a => a.ShopID == shopID && a.AppTypeID == appID);
                if (shopApp != null && (shopApp.PaymentStatus == (int)AppPaymentStatus.Disabled ||
                    shopApp.PaymentStatus == (int)AppPaymentStatus.Disabled))
                {
                    EmailHelper.SendEmail(ConfigHelper.GetValue("paymentsEmail", "<EMAIL>"), eventName + " - shop " + shopID, string.Format("Client just re-purchased/upgraded his app with plan {0} and amount {1}, but an app is disabled. Please, set app as paid on StoreYa side and make sure, that all stuff will be enabled on the Adwords side. {2}/admin/logs/?shopid={3}", planID, amount, HttpHelper.GetCurrentDomain(), shopID), null, null, true, "SYSTEM");
                }

                Log4NetLogger.Info(string.Format("The {0} app was just re-purchased/upgraded. agreeID:{1}", appID, agreeID), shopID);
            }
        }
        public static void DissableAlertsWithUpgradeSubtype(BlueSnapIpnCallEntity call)
        {
            try
            {
                int shopID = Helper.GetIntOrNull(call.shopID) ?? 0;
                if (shopID != 0)
                {
                    if (call.transactionType == "RECURRING")
                    {
                        string result = DashboardAlertsHelper.ChangeActiveAlertsIfPaid(shopID);
                        //if (!string.IsNullOrEmpty(result))
                        //{
                        //    EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "Disabled alerts. Reason - RECURRING", $"Please check IPNcalls for shop: {call.shopID} Result: {result}");
                        //}
                    }
                    else if (call.transactionType == "CHARGE")
                    {
                        string result = DashboardAlertsHelper.ChangeActiveAlertsIfPaid(shopID);
                        string resultHoliday = DashboardAlertsHelper.DisableHolidayAlert(shopID);
                        //if (!string.IsNullOrEmpty(result) || !string.IsNullOrEmpty(resultHoliday))
                        //{
                        //    EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "Disabled alerts. Reason - CHARGE", $"Please check IPNcalls for shop: {call.shopID} Result: {result}{resultHoliday}");
                        //}
                    }
                }
            }
            catch (Exception ex)
            {
                string shopID = null;
                string callIsNull = "true";
                string callType = null;
                try
                {
                    if (call != null)
                    {
                        callIsNull = "false";
                        shopID = call.shopID;
                        callType = call.transactionType;
                    }
                }
                catch { }
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "DissableAlertsWithUpgradeSubtype failed", $"CallIsNull={callIsNull}, shopID={shopID}, type={callType}. <br/> {ex.ToString()}");
            }

        }
        public static bool IPNCall(BlueSnapIpnCallEntity call, string environment)
        {
            return IPNCall(call, environment, out bool setShopPermissions, out int userId);
        }

        public static bool IPNCall
            
            (BlueSnapIpnCallEntity call, string environment, out bool setShopPermissions, out int userId)
        {
            setShopPermissions = false;
            int shopID = 0;
            userId = 0;
            try
            {
                Log4NetLogger.Info("IPN call (" + environment + ")" + call.ToJson(), (int)Log4NetLogger.SpecialShopIDs.PlimusIPN);//Log4NetLogger.GetLogFor(call)
                if (call.paymentMethod == "ECP")
                {
                    //Bank Wire need to get ShopId Plan And Appid                    
                    EmailHelper.SendEmail("<EMAIL>", "A Bank wire BlueSnap IPN call was recived .shopId:" + call.shopID, EmailHelper.AdminLinkHref(Convert.ToInt32(call.shopID)) + "<br/>" + call.ToJson(true, Newtonsoft.Json.Formatting.Indented));
                }
                //update DB
                int ipnId = 0;
                shopID = Helper.GetIntOrNull(call.shopID) ?? 0;
                shopID = AddMissingDataForFSWithoutShopID(shopID, ref call);
                if (call.transactionType == "ORDER_FAILURE") //call.transactionType == "CC_CHARGE_FAILED" 
                {
                    PaymentsEventsHistoryHelper.AddEvent(shopID, "System", PaymentsEventsHistoryHelper.EventHistoryCategory.WebHook, call.subscriptionID,
                         IPNCallHelper.ConvertToDateTime(call.transactionDate), PaymentsEventsHistoryHelper.EventHistoryStatus.Failed,
                         PaymentsEventsHistoryHelper.EventType.Checkout_Failure,
                        PaymentAdapterTypes.FastSpring, call.transactionType, call.ToJson());

                }
                else
                {
                    ipnId = IPNCallHelper.SaveToDB(call);
                }
                DissableAlertsWithUpgradeSubtype(call);
                StoreYaEntities db = DataHelper.GetStoreYaEntities();


                int shopIDToLog = Helper.GetIntOrNull(call.shopID) ?? (int)Log4NetLogger.SpecialShopIDs.PlimusIPN;
                Log4NetLogger.Info(string.Format("IPN call ({4}) of {0} for app: {1}. BlueSnapSubscriptionID: {2}, ContractPrice: {3} Contract: {5}. {6}", call.transactionType, call.appID, call.subscriptionID, call.contractPrice, environment, call.contractId, $"Reason:{call.cancelReason}"), shopIDToLog);
                int appID = Helper.GetIntOrNull(call.appID) ?? 0;
                //care about transactionType

                if (call.transactionType == "SUBSCRIPTION_CHARGE_FAILURE") //call.transactionType == "CC_CHARGE_FAILED" 
                {
                    //removed from here. Will be done from RunOverIPNCalls
                    //NotifyAboutCloseCancellation(call);
                }
                else if (call.transactionType == "REFUND")
                {
                    if (call.reversalReason != null && call.reversalReason.ToLower().Contains("paypal"))
                    {
                        Log4NetLogger.Info(environment + " REFUND initiated by Paypal dispute - shop ID " + shopID + " (" + environment + ")", shopID);

                        string shopifyUninstallAlert = "Failed to uninstalled Shopify app ";
                        try
                        {
                            var api = ShopifyConnector.GetShopifyApiClient(shopID, (int)AppTypes.TrafficBooster);
                            if (api.UninstallStoryaApp())
                            {
                                shopifyUninstallAlert = "Shopify app was uninstalled.";
                            }
                        }
                        catch (Exception ex)
                        {
                            shopifyUninstallAlert = $"{shopifyUninstallAlert} - {ex}";
                        }
                        EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "REFUND initiated by Paypal dispute - shop ID " + shopID + " (" + environment + ")", $"Please check if we need to cancel account.<br/>{shopifyUninstallAlert}");
                        //You should be looking for reversalReason=Refunded+at+PayPal+%28IPN+received%29.
                    }
                }
                else if (call.transactionType == "CANCELLATION"
                    || call.transactionType == "CANCELLATION_REFUND"
                    || call.transactionType == "CANCEL_ON_RENEWAL"
                    || call.transactionType == "CHARGEBACK"
                    || call.transactionType == "DECLINE")
                {
                    int parsedShopID;
                    bool shopIsParsed = false;
                    Thread.Sleep(5000); //wait 5 sec for upgrade cases when subscription should be changed for app (it sometimes takes time)
                    string originSubscriptionID = call.subscriptionID;
                    ShopSubscription existingSubscription = db.ShopSubscriptions.Where(sub => sub.OriginalSubscriptionID == originSubscriptionID).FirstOrDefault();
                    if (existingSubscription != null)
                    {
                        parsedShopID = existingSubscription.ShopID;
                        shopIsParsed = true;
                        SubscriptionManager.UpdateShopSubscriptionStatus(shopID, originSubscriptionID, null, SUBSCRIPTION_STATUS.CANCEL, out DateTime? nextChargeDate, lastChargeStatus: LastChargeStatus.Cancelled);
                    }
                    else
                    {
                        shopIsParsed = int.TryParse(call.shopID, out parsedShopID);
                    }
                    int? agreeId = Helper.GetIntOrNull(call.agreeID);
                    //CancelAgreementChannel(shopID, agreeId, existingSubscription, environment);
                    if (shopIsParsed)
                    {
                        IPNCallHelper.ManageCancelBudgets(parsedShopID, appID, existingSubscription, agreeId, environment);
                        PaymentsTransactionHelper.CancelPaymentsTransaction(parsedShopID, originSubscriptionID);
                    }
                    else
                    {
                        string body = string.Format("subscriptionID: {0}, email: {1}", call.subscriptionID, call.email, call.email);
                        EmailHelper.SendEmail(ConfigHelper.GetValue("supportEmail"), "The BlueSnap CANCELLATION IPN call (" + environment + ") WITHOUT ShopID was received.", body);
                    }
                    Eventer.ReportEvent(shopID, EventCategory.Payments, "Cancelation", parsedShopID.ToString(), -1);
                }

                NotifyAboutInitialPaymentWithoutShopID(call);
                bool newSubscription = false;
                decimal? oldPlan = null;
                if (IPNCallHelper.IsPaymentAuthorization(call))
                {
                    string blueSnapSubscriptionID = call.subscriptionID;
                    if (!string.IsNullOrEmpty(blueSnapSubscriptionID))
                    {
                        int planID = (Helper.GetIntOrNull(call.planID) ?? 0);
                        int storeyaSubscriptionID = 0;

                        ShopSubscription existingSubscription = db.ShopSubscriptions.Where(sub => sub.OriginalSubscriptionID == blueSnapSubscriptionID).FirstOrDefault();
                        if (existingSubscription == null)
                        {
                            newSubscription = true;
                            int contractID = 0;
                            //new subscription created at BlueSnap
                            switch (call.PaymentProvider)
                            {
                                case PaymentAdapterTypes.FastSpring:
                                    decimal.TryParse(call.NextPaymentAmount, out decimal nextPayment);
                                    // DateTime.TryParse(call.NextPaymenDate, out DateTime nextPaymenDate);

                                    DateTime? nextPaymenDate = FastSpringHelper.GetNextChargeDate(call.ChargeFrequency);

                                    //SUBSCRIPTION_STATUS status = FastSpringHelper.GetSubscriptionStatus(call.cbStatus);
                                    contractID = FastSpringHelper.GetContacrtIDByValue(call.contractId) ?? 0;
                                    storeyaSubscriptionID = SubscriptionManager.SaveToDB(appID, shopID, planID, call.subscriptionID, call.contractId, call.accountId, call.currency, call.contractPrice, call.paymentMethod, call.agreeID, SUBSCRIPTION_STATUS.ACTIVE, nextPaymenDate, nextPayment, call.ChargeFrequency, "A", call.PaymentProvider.Value);
                                    FastSpringPaymentService.UpdateSubscriptionTags(shopID, call, nextPaymenDate.Value, PaymentStatus.OK, PaymentsEventsHistoryHelper.EventType.Charge);
                                    ShopManager.UpdateVatId(shopID, call.TaxExemptionData, true);
                                    UpdateIPNSubType(ipnId, TransactionSubType.Checkout);
                                    break;
                                default:
                                case PaymentAdapterTypes.BlueSnap:
                                    contractID = (Helper.GetIntOrNull(call.contractId) ?? 0);
                                    storeyaSubscriptionID = SubscriptionManager.SaveToDB(appID, shopID, planID, call.subscriptionID, call.contractId, call.accountId, call.currency, call.contractPrice, call.paymentMethod, call.agreeID);
                                    break;
                            }
                            int agreeID = Helper.GetIntOrNull(call.agreeID) ?? 0;
                            double? price = null;
                            bool convertNonUSDPrice = true;
                            if (appID == (int)AppTypes.TrafficBooster && planID == (int)PlanTypes.CustomPlan)
                            {
                                price = Convert.ToDouble(call.contractPrice);
                                if (call.currency != "USD")
                                {
                                    price = Convert.ToDouble(call.invoiceAmountInCurrency);
                                    decimal dPrice = CurrencyHelper.GetConvertedValueInUSD(shopID, call.currency, (decimal)price);
                                    Log4NetLogger.Info(string.Format("IPN Call - Payment was converted to USD from:{0} to {1} base currency: {2}", price, dPrice, call.currency), shopID);
                                    price = (double)dPrice;
                                    convertNonUSDPrice = false;
                                }
                            }
                            AppStoreManager.SetAppAsPaid(storeyaSubscriptionID, shopID, appID, planID, contractID, null, price, agreeID, convertNonUSDPrice: convertNonUSDPrice, paymentProvider: call.PaymentProvider);

                            Shop shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
                            if (planID != 0 && planID < (int)PlanTypes.ExternalPlan)
                            {
                                setShopPermissions = true;
                                userId = shop.UserID.Value;
                                //  PermissionManager.SetShopPermissionsByPlan(shopID, planID, shop.UserID.Value);
                            }
                            Eventer.ReportEvent(shopID, EventCategory.Payments, "NewAccount", AppStoreManager.GetAppByID(appID).AppName + "_" + call.contractId);
                            AppStoreManager.AddEventToIntercom(shopID, appID, "NewAccount");
                        }
                        else
                        {
                            oldPlan = existingSubscription.NextPaymentAmount;
                            if (call.PaymentProvider == PaymentAdapterTypes.FastSpring)
                            {
                                // storeyaSubscriptionID = ChargeSubscription(call, shopID, db, appID, planID, existingSubscription.ID);
                                SubscriptionManager.UpdateShopSubscriptionStatus(shopID, call.subscriptionID, Convert.ToDouble(call.contractPrice), SUBSCRIPTION_STATUS.ACTIVE, out DateTime? nextChargeDate, true, LastChargeStatus.Charged);
                                FastSpringPaymentService.UpdateSubscriptionTags(shopID, call, nextChargeDate.Value, PaymentStatus.OK, PaymentsEventsHistoryHelper.EventType.Charge);
                                Log4NetLogger.Info(string.Format("Existing Subscription record updated for {3} Subscription id: {0}. Current IPN call ({2}): {1}", call.subscriptionID, Log4NetLogger.GetLogFor(call), environment, call.PaymentProvider.HasValue ? call.PaymentProvider.Value.ToString() : "Bluesnap"), existingSubscription.ShopID);
                                UpdateIPNSubType(ipnId, TransactionSubType.Checkout);
                            }
                            else
                            {
                                storeyaSubscriptionID = existingSubscription.ID;
                                shopID = ((shopID == 0) ? existingSubscription.ShopID : shopID);
                                Log4NetLogger.Info(string.Format("There is already subscription record in DB for {3} Subscription id: {0}. Current IPN call ({2}): {1}", call.subscriptionID, Log4NetLogger.GetLogFor(call), environment, call.PaymentProvider.HasValue ? call.PaymentProvider.Value.ToString() : "Bluesnap"), existingSubscription.ShopID);
                            }

                        }
                    }
                }
                if (call.transactionType == "RECURRING")
                {
                    if (!HandelRecurring(shopID, call))
                    {
                        //no shop ID found in call
                        return false;
                    }
                }
                //ReportIpnToCRM(shopID, call);

                ReportIpnToCallToGA(shopID, call);

                AddSystemEvent(shopID, call);

                if (call.PaymentProvider == PaymentAdapterTypes.FastSpring)
                {
                    SendFSPaymentEmail(shopID, call, oldPlan, newSubscription);
                }

            }
            catch (Exception ex)
            {
                string message = "Error embeding IPN call (" + environment + ") for " + Log4NetLogger.GetLogFor(call) + "Error: " + ex.ToString();
                Log4NetLogger.Error(message, ex, (int)Log4NetLogger.SpecialShopIDs.PlimusIPN);
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Failed to care about IPN call", message);
                Eventer.ReportEvent(shopID, EventCategory.IPN, "IPN_call_failure", call.transactionType);
                return false;
            }
            return true;
        }

        private static int AddMissingDataForFSWithoutShopID(int shopID, ref BlueSnapIpnCallEntity call)
        {
            if (call.PaymentProvider == PaymentAdapterTypes.FastSpring)
            {
                if (shopID == 0)
                {
                    var shopSub = SubscriptionManager.GetShopSubscriptionById(call.subscriptionID);
                    if (shopSub == null)
                    {
                        EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"FastSpring {call.transactionType} IPN Call With no shopId!", $"{call.ToFormattedJson()}");
                        return 0;
                    }
                    call.shopID = shopSub.ShopID.ToString();
                    call.appID = shopSub.AppIDs;
                    call.planID = shopSub.PlanIDs;
                    call.agreeID = shopSub.AgreeID.HasValue ? shopSub.AgreeID.ToString() : "0";
                    call.ChargeFrequency = shopSub.ChargeFrequency;
                    if (string.IsNullOrEmpty(call.NextPaymenDate) && shopSub.NextPaymenDate.HasValue)
                    {
                        DateTime? np =FastSpringHelper.GetNextChargeDate(shopSub.ChargeFrequency, shopSub.NextPaymenDate, call);
                        call.NextPaymenDate = np.Value.ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"FastSpring {call.transactionType} IPN Call With no shopId was fixed {shopSub.ShopID}!", $"IPN Call Data</br>{call.ToFormattedJson()}");

                    return shopSub.ShopID;
                }
            }
            return shopID;
        }

        public static bool HandelRecurring(int shopID, BlueSnapIpnCallEntity call)
        {
            if (shopID == 0)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, "RECURRING IPN Call With no shopId!", $"{call.ToFormattedJson()}");
                return false;
            }
            if (call.PaymentProvider == PaymentAdapterTypes.FastSpring)
            {
                bool updateNextPaymenDate = true;
                SubscriptionManager.UpdateShopSubscriptionStatus(shopID, call.subscriptionID, Convert.ToDouble(call.contractPrice), SUBSCRIPTION_STATUS.ACTIVE, out DateTime? nextChargeDate, updateNextPaymenDate, LastChargeStatus.Charged);
                FastSpringPaymentService.UpdateSubscriptionTags(shopID, call, nextChargeDate.Value, PaymentStatus.OK, PaymentsEventsHistoryHelper.EventType.Recurring);
            }
            else
            {
                SubscriptionManager.UpdateShopSubscriptionStatus(call.subscriptionID, SUBSCRIPTION_STATUS.ACTIVE);
            }
            return true;
        }
        private static void NotifyAboutInitialPaymentWithoutShopID(BlueSnapIpnCallEntity call)
        {
            if (string.IsNullOrEmpty(call.shopID) && IPNCallHelper.IsPaymentAuthorization(call))
            {
                //if (!IsPlaceOrderCall(call.contractId))
                //{
                //notify about initial payment without SHOPID
                string body = $"subscriptionID: {call.subscriptionID}, email: {call.email},invoiceEmail:{call.invoiceEmail}<br/>{call.ToJson(false, Newtonsoft.Json.Formatting.Indented)}";
                EmailHelper.SendEmail(ConfigHelper.GetValue("supportEmail"), "The BlueSnap IPN call WITHOUT ShopID was received.", body);
                //}
            }
        }

        public static void ManageCancelBudgets(int shopID, int appID, ShopSubscription shopSubscription, int? agreeId = null, string environment = null)
        {
            bool activeInBudget = false;
            if (shopSubscription.ActiveInBudget == 1)
            {
                activeInBudget = true;
            }
            if (appID == (int)AppTypes.TrafficBooster)
            {
                DateTime? lastPaymentDate = null;
                TbAppManager tbApp = new TbAppManager(shopID, false, null, false, true);
                bool noneAgreementCall = (agreeId == null || agreeId == 0);
                if (noneAgreementCall)
                {
                    lastPaymentDate = tbApp.GetDefaultChannel() == null ? null : tbApp.GetDefaultChannel().LastPaymentDate;
                }
                else
                {
                    lastPaymentDate = tbApp.GetAgreementChannel() == null ? null : tbApp.GetAgreementChannel().LastPaymentDate;
                }
                switch (tbApp.ChannelsStruct)
                {
                    case TbAppManager.CHANNELS_STURCT.WITHOUT_AGREEMENT:
                        DowngradeAllSubscriptionApps(shopID, shopSubscription.OriginalSubscriptionID);
                        break;
                    case TbAppManager.CHANNELS_STURCT.WITH_AGREEMENT:
                        if (CancelAgreementChannel(shopID, agreeId, shopSubscription, environment))
                        {
                            AppStoreManager.DisableTrafficBoosterApp(shopID);
                        }
                        break;
                    default:
                    case TbAppManager.CHANNELS_STURCT.NONE:
                    case TbAppManager.CHANNELS_STURCT.BOTH:
                        bool isDefaultActive = tbApp.GetDefaultChannel() != null && tbApp.GetDefaultChannel().ActiveAndPaid;
                        bool isAgreementActive = tbApp.GetAgreementChannel() != null && tbApp.GetAgreementChannel().ActiveAndPaid;


                        if (!noneAgreementCall && isDefaultActive)
                        {
                            CancelAgreementChannel(shopID, agreeId, shopSubscription, environment);
                            break;
                        }
                        if (!noneAgreementCall && !isDefaultActive)
                        {
                            if (CancelAgreementChannel(shopID, agreeId, shopSubscription, environment))
                            {
                                AppStoreManager.DisableTrafficBoosterApp(shopID);
                            }
                            break;
                        }
                        if (noneAgreementCall && !isAgreementActive)
                        {
                            DowngradeAllSubscriptionApps(shopID, shopSubscription.OriginalSubscriptionID);
                            break;
                        }
                        if (noneAgreementCall && isAgreementActive)
                        {
                            //This case a defult subs was cancelled and agreement is active
                            //1.Check if Last
                            var lastSub = SubscriptionManager.GetTBLastShopSubscription(shopID, null);
                            if (lastSub.OriginalSubscriptionID == shopSubscription.OriginalSubscriptionID)
                            {
                                TrafficBoostersDbHelper.UpdateTbStatus(shopID, TB_AW_STATUS.CANCELED);
                                //Disable only none agreements channels
                                TbChannelManager.DisableChannelsInDb(shopID, true);
                            }
                            break;
                        }
                        break;
                }
                if (activeInBudget && lastPaymentDate.HasValue && lastPaymentDate.Value > DateTime.Now.AddMonths(-5))
                {
                    var user = ShopsHelper.GetUser(shopID);
                    int userID = (user != null ? user.ID : 0);
                    SystemEventHelper.Add(userID, shopID, (int)AppTypes.AdditionalService, SystemEventTypes.AdminActions, SystemEventActions.SubscriptionCancelEmail, string.Format("{0}_{1}", shopSubscription.OriginalSubscriptionID, shopSubscription.ID));
                    Log4NetLogger.Info(string.Format("A Cancellation subscription {0} Email systemEvenet was added.", shopSubscription.OriginalSubscriptionID), shopID);
                }
                //else
                //{
                //    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, $"Cancellation IPN for none ActiveInBudget subscription {shopSubscription.OriginalSubscriptionID}, {shopID}", $"{EmailHelper.AdminBoShopLinkHref(shopID) } , DB id:{shopSubscription.ID}, No Status Updates where done,This email is just to confirm we are OK");
                //    Log4NetLogger.Info(string.Format("A Cancellation IPN for not ActiveInBudget subscription {0} No Updates where done", shopSubscription.OriginalSubscriptionID), shopID);
                //}
            }
            else
            {
                DowngradeAllSubscriptionApps(shopID, shopSubscription.OriginalSubscriptionID);
            }
        }

        public static void DowngradeAllSubscriptionApps(int shopID, string blueSnapSubscriptionID)
        {
            var db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            if (!shop.IsDisabled.HasValue || shop.IsDisabled.Value == 0)
            {
                Log4NetLogger.Info(string.Format("A Cancellation IPN call ({1}) was received: all shop apps connected to blueSnapSubscriptionID: {0} will be downgraded.", blueSnapSubscriptionID, shopID), shopID);
                AppStoreManager.DowngradeAllSubscriptionApps(shopID, blueSnapSubscriptionID);
            }
        }
        public static bool CancelAgreementChannel(int shopID, int? agreeId, ShopSubscription existingSubscription, string environment)
        {
            if (agreeId.HasValue && agreeId > 0)
            {
                if (SubscriptionManager.IsShopLastAgreementSubscription(shopID, existingSubscription.OriginalSubscriptionID))
                {
                    Log4NetLogger.Info(string.Format("CancelAgreementChannel: subscription was cancel changing FB channel status to Cancel BlueSnapSubscriptionID: {0} environment:{1}", existingSubscription.BlueSnapSubscriptionID, environment), existingSubscription.ShopID);
                    TbChannelManager.UpdateChannelStatus(shopID, TrafficChannelsTypes.Facebook.GetHashCode(), TrafficChannelsStatuses.Cancelled.GetHashCode());
                    TbAgreementsManager.SetAgreementCancelledAt(agreeId.Value, DateTime.Now, "IPN");
                    return true;
                }
            }
            return false;
        }

        public static void ReportIpnToCallToGA(int shopID, BlueSnapIpnCallEntity call)
        {
            int amount = 0;
            if (!string.IsNullOrEmpty(call.invoiceAmount))
            {
                amount = Convert.ToInt32(Convert.ToDecimal(call.invoiceAmount));
            }

            string eventLabel = null;
            if (!string.IsNullOrEmpty(call.subscriptionID))
            {
                ShopSubscription existingSubscription = null;
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                switch (call.PaymentProvider)
                {
                    case PaymentAdapterTypes.FastSpring:
                        existingSubscription = db.ShopSubscriptions.Where(sub => sub.FsSubscriptionID == call.subscriptionID).FirstOrDefault();
                        break;
                    case PaymentAdapterTypes.BlueSnap:
                    default:
                        int blueSnapSubscriptionID = int.Parse(call.subscriptionID);
                        existingSubscription = db.ShopSubscriptions.Where(sub => sub.BlueSnapSubscriptionID == blueSnapSubscriptionID).FirstOrDefault();
                        break;
                }

                if (existingSubscription != null && existingSubscription.InsertedAt != null)
                {
                    eventLabel = (existingSubscription.InsertedAt.Value).ToString("yyyy-MM-dd"); //set subscription start date
                    if (call.transactionType == "RECURRING" && existingSubscription.InsertedAt.Value.AddDays(25) > DateTime.Now)
                    {
                        //less than 25 days pass after subscription started - this is now first payment after trial
                        eventLabel += "_FirstPayment";
                    }
                }
                else
                {
                    eventLabel += "_NoSubscriptionData";
                }
            }

            eventLabel += "_" + call.contractId + "_ShopID" + call.shopID;

            Eventer.ReportEvent(shopID, EventCategory.IPN, call.transactionType, eventLabel, amount);

        }

        public static void SendFSPaymentEmail(int shopID, BlueSnapIpnCallEntity call, decimal? oldPlan, bool ntp)
        {
            try
            {
                string title = GetEmailTitle(call, ntp, shopID);
                if (title == null)
                {
                    return;
                }
                string body = GetEmailBody(shopID, call, oldPlan);
                EmailHelper.SendEmail("<EMAIL>", title, body);
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "SendFSPaymentEmail failed", $"ShopID={shopID}, transactionType={call.transactionType}, {ex.ToString()}");
            }


        }

        private static string GetEmailTitle(BlueSnapIpnCallEntity call, bool ntp, int shopID)
        {
            if (ntp)
            {
                return $"FS Event - NTP - New Traffic Booster Payment - {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)} ShopID: {shopID}";
            }
            if (call.transactionType == "CHARGEBACK")
            {
                return $"FS Event - New Chargeback - {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)} ShopID: {shopID}";
            }
            if (call.transactionType == "RECURRING")
            {
                return $"FS Event - Recurring payment (FS) - {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)} ShopID: {shopID}";
            }
            if (call.transactionType == "CHARGE")
            {
                return $"FS Event - CHARGE payment (FS) - {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)} ShopID: {shopID}";
            }
            if (call.transactionType == "CANCELLATION")
            {
                return $"FS Event - Subscription Canceled - {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)} ShopID: {shopID}";
            }
            if (call.transactionType == "SUBSCRIPTION_CHARGE_FAILURE")
            {
                return $"FS Event - Failed Charges - {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)} ShopID: {shopID}";
            }
            if (call.transactionType == "ORDER_FAILURE")
            {
                return $"FS Event - Failed Checkout - ShopID: {shopID} - {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)}";
            }
            if (call.transactionType == "REFUND")
            {
                return $"FS Event - Refund - {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)} ShopID: {shopID}";
            }
            return null;
        }

        private static string GetEmailBody(int shopID, BlueSnapIpnCallEntity call, decimal? oldPlan)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            string domain = null;
            string manager = null;

            Shop shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            if (shop != null)
            {
                manager = StoreYaEmployeeHelper.GetmanagerNameIfExist(shopID);
                TrafficBooster tb = TrafficBoostersDbHelper.GetSettings(shopID);
                if (tb != null)
                {
                    domain = tb.Url1;
                }
                if (domain == null)
                {
                    domain = shop.BaseUrl;
                }
            }
            var body = new StringBuilder();
            body.AppendLine($"<h4 style='margin-bottom: 0;'>Custom Fields Data</h4>" +
                $"shopID: {EmailHelper.GetBoLinkHrefAndAMandRemarks(shopID)} <br/> " +
                $"Domain: {domain} <br/> " +
                $"Account Manager: {manager} <br/> "
               );
            if (!string.IsNullOrEmpty(call.subscriptionID))
            {
                body.AppendLine($"SubscriptionID: {call.subscriptionID} <br/> ");
            }
            if (!string.IsNullOrEmpty(call.cancelReason))
            {
                body.AppendLine($"Reason: {call.cancelReason} <br/> ");
            }
            if (call.transactionType == "RECURRING" || IPNCallHelper.IsPaymentAuthorization(call))
            {
                body.AppendLine($"planID: {(Helper.GetIntOrNull(call.planID) ?? 0)} <br/> " +
                $"appID: {(Helper.GetIntOrNull(call.appID) ?? 0)} <br/> " +
                $"agreeID: {Helper.GetIntOrNull(call.agreeID)} <br>"); ;
            }

            body.AppendLine($"<h4 style='margin-bottom: 0;'>Order Information</h4> " +
                $"Invoice ID: {call.referenceNumber} <br/> " +
                $"OriginalReferenceNumber: {call.originalReferenceNumber} <br/> " +
                $"Date: {call.transactionDate} <br/> ");
            if (call.transactionType == "CHARGEBACK")
            {
                body.AppendLine($"Chargeback: $ {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)}");
            }
            else
            {
                body.AppendLine($"Paid: $ {(Helper.GetDecimalOrNull(call.invoiceAmount) ?? 0)}‎ <br/> " +
                     $"Previous Payment: ${oldPlan} <br/> " +
                     $"Product: {call.productId} <br/> " +
                     $"Contract: {call.productName} <br/> " +
                     $"Quantity: {call.quantity} <br/> " +
                     $"Payment Type: {FixFormatingPaymentMethod(call.paymentMethod)} <br/>");
            }


            body.AppendLine($"<h4 style='margin-bottom: 0;'>Customer Information</h4> " +
                $"Full Name: {call.contractName} <br/> " +
                $"Email: {call.email} <br/> " +
                $"Account ID: ********* <br/> " +
                $"Zip Code: {call.zipCode} <br/> " +
                $"State: {call.state} <br/> " +
                $"Country: {call.country} <br/> " +
                $"IP Address: {call.remoteAddress} <br/> ");
            //$"IP Country: United States <br/>"); //????


            body.AppendLine($"<h4 style='margin-bottom: 0;'>Invoice Contact Information</h4> " +
                $"Last Name: {call.invoiceLastName} <br/> " +
                $"First Name: {call.invoiceFirstName} <br/> " +
                $"State: {call.shippingState} <br/> " +
                $"Zip Code: {call.shippingZipCode} <br/> " +
                $"Country: {call.shippingCountry} <br/> " +
                $"Email: {call.invoiceEmail}");
            return body.ToString();
        }

        private static string FixFormatingPaymentMethod(string paymentMethod)
        {
            if (paymentMethod == null)
            {
                return null;
            }
            string clearText = paymentMethod.Trim();
            switch (clearText)
            {
                case "googlepay":
                    return "Google Pay";
                case "creditcard":
                    return "Credit Card";
                case "applepay":
                    return "Apple Pay";
                default: return paymentMethod;
            }
        }
        public static PlimusIpnCall GetLastPaymentByTransactionID(string transactionID)
        {
            try
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                var payment = db.PlimusIpnCalls.Where(x => x.TransactionId == transactionID && (x.TransactionType == "RECURRING" || x.TransactionType == "CHARGE")).OrderByDescending(x => x.ID).FirstOrDefault();
                return payment;
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "GetLastPaymentByTransactionID faild.", $"TransactionID: {transactionID} <br/> {ex.ToString()}");
                return null;
            }
        }
    }
}
