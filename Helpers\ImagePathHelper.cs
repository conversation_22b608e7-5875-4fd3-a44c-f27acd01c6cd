﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Configuration;
using System.IO;
using System.Text.RegularExpressions;

namespace Storeya.Core.Helpers
{

    public class ImagePathHelper
    {
        const string DefaultImage = "/common/images/missing_product.png";

        public static string GetServerPath()
        {
            string path = null;
            string server = ConfigHelper.GetValue("AWSCdnDomain");
            string domain = ConfigHelper.GetValue("AWSUploadsBucket");
            if (!string.IsNullOrEmpty(domain))
            {
                path = server + domain;
            }
            return path;
        }

        public static string ExtractPathWithoutDomain(string fullFilePath)
        {
            if (!string.IsNullOrEmpty(fullFilePath))
            {
                string image = fullFilePath;
                string path = ImagePathHelper.GetServerPath();
                if (!string.IsNullOrEmpty(path))
                {
                    if (image.Contains(path))
                    {
                        image = image.Replace(path, "");
                    }
                }
                return image;
            }

            return null;
        }

        public static string GetProductImageUrl(string imagePath, string missingImagePath = null)
        {
            string missingProductImage = missingImagePath ?? DefaultImage;

            if (!string.IsNullOrWhiteSpace(imagePath))
            {
                //if (!string.IsNullOrWhiteSpace(ConfigHelper.GetValue("StaticImagesLocation")))
                //{
                //    return ConfigHelper.GetValue("StaticImagesLocation") + imagePath;
                //}

                imagePath = imagePath.Replace("\r", "").Replace("\n", "");
                if (!imagePath.Contains("/common/") && !imagePath.StartsWith("http"))
                {
                    return GetServerPath() + imagePath;
                }                    
                else
                {
                    return imagePath;
                }
            }
            return missingProductImage;
        }

        public static string GetProductImageUrl(Product product, string missingImagePath = null)
        {
            string missingProductImage = missingImagePath ?? DefaultImage;
            if (product == null)
                return missingProductImage;
            return GetProductImageUrl(product.LocalImage);
        }

        public static int GetUploadedNotSavedImagesCount()
        {
            // define images count of not saved images in temp folder C:\Dev\Storeya\trunk\storeya\Uploads\Temp\t

            string productImagesPath = ConfigurationManager.AppSettings["ProductImages.Path"];
            string uploadFolder = HttpContext.Current.Server.MapPath("/" + productImagesPath);
            string dir = uploadFolder + "\\Temp\\t";

            string[] files = Directory.GetFiles(dir);
            return files.Count();
        }

        public static string ShopifyGetImageUrlByLimit(string url, long limitSizeTo,bool square = false,bool removeLimit = false)
        {
            if (removeLimit)
            {
                url = ShopifyRemoveImageUrlLimit(url);
            }
            if (square)
            {
                return Regex.Replace(url, @"([^x])(\.jpg|\.png|\.jpeg)", "${1}_" + limitSizeTo + "x" + limitSizeTo + "${2}", RegexOptions.IgnoreCase);
            }
            return Regex.Replace(url, @"([^x])(\.jpg|\.png|\.jpeg)", "${1}_" + limitSizeTo + "x${2}", RegexOptions.IgnoreCase);
        }

        public static string ShopifyRemoveImageUrlLimit(string url)
        {
            string[] s = url.Split(new char[] { '.' }, StringSplitOptions.RemoveEmptyEntries);
            string extention = "." + s[s.Length - 1];           
            string[] x = url.Split(new char[] { '_' }, StringSplitOptions.RemoveEmptyEntries);
            if(x.Length > 1)
            {
                string limit = "_" + x[x.Length - 1].ToLower();
                if (limit.Contains("x"))
                {
                    return url.Replace(limit, "") + extention;
                }
            }
            
            return url;
        }
    }
}
