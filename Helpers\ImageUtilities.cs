using nQuant;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using ImageMagick;
using Storeya.Core.Models;
using SkiaSharp;

namespace Storeya.Core.Helpers
{
    public static class ImageUtilities
    {
        public enum IMAGE_TYPE
        {
            JPEG,
            PNG,
            WEBP
        }
        public struct MIME_TYPES
        {
            public const string PNG = "image/png";
            public const string JPEG = "image/jpeg";
            public const string WEBP = "image/webp";
            public const string TXT = "text/plain";
        }
        /// <summary>
        /// A quick lookup for getting image encoders
        /// </summary>
        private static Dictionary<string, ImageCodecInfo> encoders = null;

        /// <summary>
        /// A quick lookup for getting image encoders
        /// </summary>
        public static Dictionary<string, ImageCodecInfo> Encoders
        {
            //get accessor that creates the dictionary on demand
            get
            {
                //if the quick lookup isn't initialised, initialise it
                if (encoders == null)
                {
                    encoders = new Dictionary<string, ImageCodecInfo>();
                }

                //if there are no codecs, try loading them
                if (encoders.Count == 0)
                {
                    //get all the codecs
                    foreach (ImageCodecInfo codec in ImageCodecInfo.GetImageEncoders())
                    {
                        //add each codec to the quick lookup
                        encoders.Add(codec.MimeType.ToLower(), codec);
                    }
                }

                //return the lookup
                return encoders;
            }
        }

        /// <summary>
        /// Resize the image to the specified width and height.
        /// </summary>
        /// <param name="image">The image to resize.</param>
        /// <param name="width">The width to resize to.</param>
        /// <param name="height">The height to resize to.</param>
        /// <returns>The resized image.</returns>
        public static Bitmap ResizeImage(Image image, int width, int height)
        {
            //a holder for the result
            Bitmap result = new Bitmap(width, height);

            //use a graphics object to draw the resized image into the bitmap
            using (Graphics graphics = Graphics.FromImage(result))
            {
                //set the resize quality modes to high quality
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                //draw the image into the target bitmap
                graphics.DrawImage(image, 0, 0, result.Width, result.Height);
            }

            //return the resulting bitmap
            return result;
        }



        public static Bitmap ResizeImageSaveRatio_2(Image image, int width, int height)
        {
            System.Drawing.Image fullsizeImage = image;

            // Prevent using images internal thumbnail
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);

            //a holder for the result
            Bitmap result = new Bitmap(width, height);

            //use a graphics object to draw the resized image into the bitmap
            using (Graphics graphics = Graphics.FromImage(result))
            {
                //set the resize quality modes to high quality
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                //draw the image into the target bitmap
                graphics.DrawImage(image, 0, 0, result.Width, result.Height);
            }

            //return the resulting bitmap
            return result;
        }
        public static Bitmap ResizeImageSaveRatio(Image image, int width, int height)
        {
            System.Drawing.Image fullsizeImage = image;

            // Prevent using images internal thumbnail
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);

            if (fullsizeImage.Width <= width)
            {
                width = fullsizeImage.Width;
            }

            int newHeight = fullsizeImage.Height * width / fullsizeImage.Width;
            if (newHeight > height)
            {
                // Resize with height instead
                width = fullsizeImage.Width * height / fullsizeImage.Height;
                newHeight = height;
            }


            //a holder for the result
            Bitmap result = new Bitmap(width, newHeight);

            //use a graphics object to draw the resized image into the bitmap
            using (Graphics graphics = Graphics.FromImage(result))
            {
                //set the resize quality modes to high quality
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                //draw the image into the target bitmap
                graphics.DrawImage(image, 0, 0, result.Width, result.Height);
            }

            //return the resulting bitmap
            return result;
        }


        public static Bitmap ResizeImageByHeightSaveRatio(Image image, int height)
        {
            System.Drawing.Image fullsizeImage = image;

            // Prevent using images internal thumbnail
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);

            int newWidth = 0;
            //calc new width
            if (image.Height >= height)
            {
                double percent = (height * 100) / image.Height;
                newWidth = (int)(image.Width * percent) / 100;
            }
            else
            {
                newWidth = image.Width;
                height = image.Height;
            }

            //a holder for the result
            Bitmap result = new Bitmap(newWidth, height);

            //use a graphics object to draw the resized image into the bitmap
            using (Graphics graphics = Graphics.FromImage(result))
            {
                //set the resize quality modes to high quality
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                //draw the image into the target bitmap
                graphics.DrawImage(image, 0, 0, result.Width, result.Height);
            }

            //return the resulting bitmap
            return result;
        }
        public static string OptimizeFolder(string path, int quality, out List<string> errors, int? resizeInPercentage = null)
        {
            string uploadPath = $"{path}\\Upload\\";
            if (!Directory.Exists(uploadPath))
            {
                Directory.CreateDirectory(uploadPath);
            }
            errors = new List<string>();
            foreach (var imageFile in Directory.GetFiles(path))
            {
                if (Path.GetExtension(imageFile).ToLower() == ".json")
                {
                    continue;
                }
                string targetFile = $"{uploadPath}{Path.GetFileName(imageFile)}";
                int calculateQuality = CalculateQuality(imageFile, quality);


                try
                {
                    if (Path.GetExtension(imageFile).ToLower() == ".png")
                    {
                        SavePNG(imageFile, targetFile, calculateQuality, resizeInPercentage);
                    }
                    if (Path.GetExtension(imageFile).ToLower() == ".webp")
                    {
                        SaveWebP(imageFile, targetFile, calculateQuality, resizeInPercentage: resizeInPercentage);
                    }
                    else
                    {
                        SaveJpeg(imageFile, targetFile, calculateQuality, resizeInPercentage);
                    }
                    //double compRes = CompareImageQuality(imageFile, targetFile, out bool hasTransparentBackground);
                    //Console.WriteLine($"OptimizeFolder {imageFile}, calculateQuality {calculateQuality},CompareImageQuality:{compRes}, HasTransparentBackground: {hasTransparentBackground}");


                }
                catch (Exception ex)
                {
                    errors.Add(imageFile);
                    ConsoleAppHelper.WriteError($"OptimizeFolder failed optimize Image:{imageFile}", ex);
                }
            }
            return uploadPath;
        }

        public static void SaveWebP(string sourceFile, string targetFile, int quality, int? resizeInPercentage = null, int? resizeWidth = null, int? resizeHeight = null)
        {
            try
            {
                using (var input = SKBitmap.Decode(sourceFile))
                {
                    SKBitmap bitmap = input;
                    if (resizeHeight.HasValue || resizeInPercentage.HasValue)
                    {
                        SKSamplingOptions sampling = new SKSamplingOptions(SKFilterMode.Linear, SKMipmapMode.Linear);
                        if (resizeHeight.HasValue && resizeWidth.HasValue)
                        {
                            bitmap = bitmap.Resize(new SKImageInfo(resizeWidth.Value, resizeHeight.Value), sampling);
                        }
                        else if (resizeInPercentage.HasValue)
                        {
                            decimal percentage = (decimal)resizeInPercentage.Value / 100m;
                            int newWidth = (int)(input.Width * percentage);
                            int newHeight = (int)(input.Height * percentage);

                            bitmap = bitmap.Resize(new SKImageInfo(newWidth, newHeight), sampling);
                        }
                    }


                    using (var image = SKImage.FromBitmap(bitmap))
                    using (var data = image.Encode(SKEncodedImageFormat.Webp, quality))
                    using (var stream = new FileStream(targetFile, FileMode.Create, FileAccess.Write, FileShare.None))
                    {
                        data.SaveTo(stream);
                    }

                    if (bitmap != input)
                        bitmap.Dispose();
                }

            }
            catch (Exception ex)
            {
                throw new Exception($"Error during fast WebP processing {sourceFile}: {ex.Message}", ex);
            }
        }

        public static void CompareImageFolders(string path1, string path2)
        {
            Console.WriteLine($"Compare images between:{path1}  -  {path2}");
            foreach (var imageFile in Directory.GetFiles(path1))
            {
                if (Path.GetExtension(imageFile).ToLower() == ".json")
                {
                    continue;
                }
                string filename = Path.GetFileName(imageFile);
                string targetFile = $"{path2}{filename}";
                if (File.Exists(targetFile))
                {
                    bool pass = PassOptimizationQA(imageFile, targetFile);
                    Console.WriteLine($"{filename}, {pass}");
                }
                else
                {
                    Console.WriteLine($"{filename}, not exists");
                }
            }

        }


        // SSIM calculation method
        public static double CalculateSSIM(Bitmap image1, Bitmap image2)
        {
            int width = image1.Width;
            int height = image1.Height;
            int numPixels = width * height;

            double sumSsim = 0;

            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    Color pixel1 = image1.GetPixel(x, y);
                    Color pixel2 = image2.GetPixel(x, y);

                    double luminance1 = 0.299 * pixel1.R + 0.587 * pixel1.G + 0.114 * pixel1.B;
                    double luminance2 = 0.299 * pixel2.R + 0.587 * pixel2.G + 0.114 * pixel2.B;

                    double luminanceMean = (luminance1 + luminance2) / 2;
                    double luminanceVariance1 = luminance1 * luminance1;
                    double luminanceVariance2 = luminance2 * luminance2;
                    double luminanceCovariance = luminance1 * luminance2;

                    double c1 = Math.Pow(0.01 * 255, 2);
                    double c2 = Math.Pow(0.03 * 255, 2);

                    double luminanceVariance = (luminanceVariance1 + luminanceVariance2) / 2 - luminanceMean * luminanceMean;
                    double luminanceCovarianceVariance = (luminanceCovariance) - luminanceMean * luminanceMean;

                    double ssim = (2 * luminanceMean * luminanceCovarianceVariance + c1) * (2 * luminanceCovariance + c2) /
                                  ((luminanceMean * luminanceMean + luminanceVariance1 + luminanceVariance2 + c1) *
                                   (luminanceVariance + c2));

                    sumSsim += ssim;

                }
            }
            double averageSsim = sumSsim / numPixels;
            averageSsim = Math.Max(Math.Min(averageSsim, 1.0), -1.0);

            return averageSsim;
        }

        // Method to calculate image quality change grade from 0 to 100
        public static double CalculateQualityChangeGrade(string imagePath1, string imagePath2)
        {
            using (Bitmap originalImage = new Bitmap(imagePath1))
            using (Bitmap optimizedImage = new Bitmap(imagePath2))
            {
                double ssim = CalculateSSIM(originalImage, optimizedImage);
                // Map the SSIM value to the range [0, 100]
                //  int grade = (int)Math.Round((ssim + 1) * 50);
                return ssim;
            }
        }

        // Method to calculate Mean Squared Error (MSE)
        public static double CalculateMSE(Bitmap image1, Bitmap image2, int pixelDiffThreshhold = 10, int diffThreshhold = 100)
        {
            if (image1.Size != image2.Size)
                throw new ArgumentException("Images must have the same dimensions.");

            int width = image1.Width;
            int height = image1.Height;
            int numPixels = width * height;

            double sumSquaredError = 0;
            double notSamePixelCount = 0;
            double diffPrec = 0;
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    Color pixel1 = image1.GetPixel(x, y);
                    Color pixel2 = image2.GetPixel(x, y);

                    int redDiff = pixel1.R - pixel2.R;
                    int greenDiff = pixel1.G - pixel2.G;
                    int blueDiff = pixel1.B - pixel2.B;

                    double squaredError = redDiff * redDiff + greenDiff * greenDiff + blueDiff * blueDiff;
                    sumSquaredError += squaredError;
                    if (squaredError > pixelDiffThreshhold)
                    {
                        notSamePixelCount++;
                        diffPrec = notSamePixelCount / numPixels * 100;
                        if (diffPrec > diffThreshhold)
                        {
                            return diffPrec;
                        }
                    }
                }
            }
            return diffPrec;
        }
        public static bool PassOptimizationQA(string imagePath1, string imagePath2, int pixelDiffThreshhold = 10, int diffThreshhold = 55)
        {
            double compRes = CalculatePercentageDifference(imagePath1, imagePath2, pixelDiffThreshhold, diffThreshhold);
            Console.WriteLine($"{imagePath1}, {compRes}");
            if (diffThreshhold >= compRes)
            {
                return true;
            }
            return false;
        }

        // Method to calculate the percentage difference between two images
        public static double CalculatePercentageDifference(string imagePath1, string imagePath2, int pixelDiffThreshhold = 10, int diffThreshhold = 55)
        {
            using (Bitmap originalImage = new Bitmap(imagePath1))
            using (Bitmap optimizedImage = new Bitmap(imagePath2))
            {
                double psnr = CalculateMSE(originalImage, optimizedImage, pixelDiffThreshhold, diffThreshhold);

                return psnr;
            }
        }
        private static int CalculateQuality(string imageFile, int baseQuality)
        {
            FileInfo fi = new FileInfo(imageFile);
            if (fi.Length > 1000000)
            {
                baseQuality = baseQuality - (int)(baseQuality * 0.22M);
                if (baseQuality < 55)
                {
                    baseQuality = 55;
                }
            }
            if (fi.Length > 400000 && fi.Length < 1000000)
            {
                baseQuality = baseQuality - (int)(baseQuality * 0.15M);
                if (baseQuality < 55)
                {
                    baseQuality = 55;
                }
            }
            return baseQuality;
        }

        public static bool HasTransparentBackground(string imagePath)
        {
            // Load the image into a Bitmap object
            using (Bitmap bitmap = new Bitmap(imagePath))
            {
                // Check each pixel of the image
                for (int x = 0; x < bitmap.Width; x++)
                {
                    for (int y = 0; y < bitmap.Height; y++)
                    {
                        // Get the color of the current pixel
                        Color pixelColor = bitmap.GetPixel(x, y);

                        // Check if the alpha channel is less than 255 (fully opaque)
                        if (pixelColor.A < 255)
                        {
                            // The image has a transparent background
                            return true;
                        }
                    }
                }
            }

            // No transparent pixels found, image has no transparent background
            return false;
        }





        public static double CompareImageQuality(string imagePath1, string imagePath2, out bool hasTransparentBackground, int threshold = 0)
        {
            hasTransparentBackground = false;
            using (Bitmap image1 = new Bitmap(imagePath1))
            using (Bitmap image2 = new Bitmap(imagePath2))
            {
                if (image1.Width != image2.Width || image1.Height != image2.Height)
                {
                    throw new ArgumentException("Images must have the same dimensions.");
                }

                int totalPixels = image1.Width * image1.Height;
                int differentPixels = 0;

                for (int y = 0; y < image1.Height; y++)
                {
                    for (int x = 0; x < image1.Width; x++)
                    {
                        Color pixel1 = image1.GetPixel(x, y);
                        Color pixel2 = image2.GetPixel(x, y);
                        if (pixel1.A < 255)
                        {
                            // The image has a transparent background
                            hasTransparentBackground = true;
                        }
                        int rDiff = Math.Abs(pixel1.R - pixel2.R);
                        int gDiff = Math.Abs(pixel1.G - pixel2.G);
                        int bDiff = Math.Abs(pixel1.B - pixel2.B);

                        if (rDiff > threshold || gDiff > threshold || bDiff > threshold)
                        {
                            differentPixels++;
                        }
                    }
                }

                double score = (double)differentPixels / totalPixels * 100;
                //score = Math.Max(1 - score, 0); // Set score to 0 if difference exceeds threshold

                return score;
            }
        }


        public static string Optimize(string imageFullPath, int quality, int? resizeInPercentage = null, IMAGE_TYPE imageType = IMAGE_TYPE.JPEG, bool overwriteBackup = true)
        {

            string backupPath = Path.GetDirectoryName(imageFullPath) + @"\bak\";
            if (!Directory.Exists(backupPath))
            {
                Directory.CreateDirectory(backupPath);
            }
            string bakFileName = backupPath + Path.GetFileName(imageFullPath);
            if (overwriteBackup)
            {
                File.Copy(imageFullPath, bakFileName, true);
            }
            else
            {
                if (!File.Exists(bakFileName))
                {
                    File.Copy(imageFullPath, bakFileName, true);
                }
            }

            File.Delete(imageFullPath);

            if (imageType == IMAGE_TYPE.PNG)
            {
                if (!resizeInPercentage.HasValue)
                {
                    resizeInPercentage = 100;
                }
            }
            switch (imageType)
            {
                case IMAGE_TYPE.JPEG:

                    SaveJpeg(bakFileName, imageFullPath, quality, resizeInPercentage);
                    break;
                case IMAGE_TYPE.PNG:
                    SavePNG(bakFileName, imageFullPath, quality, resizeInPercentage);
                    break;
                case IMAGE_TYPE.WEBP:
                    SaveWebP(bakFileName, imageFullPath, quality, resizeInPercentage: resizeInPercentage);
                    break;
            }
            return imageFullPath;
        }

        /// <summary> 
        /// Saves an image as a jpeg image, with the given quality 
        /// </summary> 
        /// <param name="path">Path to which the image would be saved.</param> 
        /// <param name="quality">An integer from 0 to 100, with 100 being the 
        /// highest quality</param> 
        /// <exception cref="ArgumentOutOfRangeException">
        /// An invalid value was entered for image quality.
        /// </exception>

        public static void SaveJpeg(string path, Image image, int quality)
        {
            try
            {

                //ensure the quality is within the correct range
                if ((quality < 0) || (quality > 100))
                {
                    //create the error message
                    string error = string.Format("Jpeg image quality must be between 0 and 100, with 100 being the highest quality.  A value of {0} was specified.", quality);
                    //throw a helpful exception
                    throw new ArgumentOutOfRangeException(error);
                }

                //create an encoder parameter for the image quality
                EncoderParameter qualityParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, quality);

                //get the jpeg codec
                ImageCodecInfo jpegCodec = GetEncoderInfo(MIME_TYPES.JPEG);

                //create a collection of all parameters that we will pass to the encoder
                EncoderParameters encoderParams = new EncoderParameters(1);
                //set the quality parameter for the codec
                encoderParams.Param[0] = qualityParam;

                //save the image using the codec and the parameters
                image.Save(path, jpegCodec, encoderParams);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                image.Dispose();
            }
        }

        public static void SaveJpeg(string sourceFile, string targetFile, int quality, int? resizeInPercentage = null)
        {

            try
            {
                //ensure the quality is within the correct range
                if ((quality < 0) || (quality > 100))
                {
                    //create the error message
                    string error = string.Format("Jpeg image quality must be between 0 and 100, with 100 being the highest quality.  A value of {0} was specified.", quality);
                    //throw a helpful exception
                    throw new ArgumentOutOfRangeException(error);
                }
                //create an encoder parameter for the image quality
                EncoderParameter qualityParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, quality);

                //get the jpeg codec
                ImageCodecInfo jpegCodec = GetEncoderInfo(MIME_TYPES.JPEG);

                //create a collection of all parameters that we will pass to the encoder
                EncoderParameters encoderParams = new EncoderParameters(1);
                //set the quality parameter for the codec
                encoderParams.Param[0] = qualityParam;
                //using (var ms = new FileStream(sourceFile, FileMode.OpenOrCreate))
                //{
                //    Image image = Image.FromStream(ms);
                //    image.Dispose();
                //}
                using (Image img = Image.FromFile(sourceFile))//ok
                {
                    if (resizeInPercentage.HasValue)
                    {
                        decimal percentage = ((decimal)resizeInPercentage.Value / 100);
                        int newHeight = (int)((decimal)img.Height * percentage);
                        int newWidth = (int)((decimal)img.Width * percentage);
                        Image resImage = ResizeImageSaveRatio_2(img, newWidth, newHeight);
                        resImage.Save(targetFile, jpegCodec, encoderParams);
                    }
                    else
                    {
                        img.Save(targetFile, jpegCodec, encoderParams);
                    }
                }
                //save the image using the codec and the parameters

            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                // image.Dispose();
            }
        }
        public static void SavePNG(string sourceFile, string targetFile, int quality, int? resizeInPercentage = null)
        {

            var quantizer = new WuQuantizer();
            using (var memStream = Image.FromFile(sourceFile, true))//ok
            using (var bitmap = new Bitmap(memStream))
            {
                if (resizeInPercentage.HasValue)
                {
                    decimal percentage = ((decimal)resizeInPercentage.Value / 100);
                    int newHeight = (int)((decimal)bitmap.Height * percentage);
                    int newWidth = (int)((decimal)bitmap.Width * percentage);
                    ResizeImageSaveRatio_2(bitmap, newWidth, newHeight);
                }
                try
                {
                    //   quality = quality;
                    //ensure the quality is within the correct range
                    if ((quality < 0))
                    {
                        quality = 0;
                    }
                    else if ((quality > 100))
                    {
                        quality = 100;
                    }
                    int alphaTransparency = 10;
                    int alphaFader = 70;
                    using (var quantized = quantizer.QuantizeImage(bitmap, alphaTransparency, alphaFader))
                    {
                        EncoderParameter qualityParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Compression, quality);
                        ImageCodecInfo jpegCodec = GetEncoderInfo(MIME_TYPES.PNG);
                        EncoderParameters encoderParams = new EncoderParameters(1);
                        encoderParams.Param[0] = qualityParam;
                        quantized.Save(targetFile, jpegCodec, encoderParams);
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                finally
                {
                    quantizer = null;
                    //bitmap.Dispose();
                }
            }
        }
        /// <summary> 
        /// Returns the image codec with the given mime type 
        /// </summary> 
        public static ImageCodecInfo GetEncoderInfo(string mimeType)
        {
            //do a case insensitive search for the mime type
            string lookupKey = mimeType.ToLower();

            //the codec to return, default to null
            ImageCodecInfo foundCodec = null;

            //if we have the encoder, get it to return
            if (Encoders.ContainsKey(lookupKey))
            {
                //pull the codec from the lookup
                foundCodec = Encoders[lookupKey];
            }

            return foundCodec;
        }

        public static Image FixedSize(Image imgPhoto, int Width, int Height)
        {
            int sourceWidth = imgPhoto.Width;
            int sourceHeight = imgPhoto.Height;
            int sourceX = 0;
            int sourceY = 0;
            int destX = 0;
            int destY = 0;

            float nPercent = 0;
            float nPercentW = 0;
            float nPercentH = 0;

            nPercentW = ((float)Width / (float)sourceWidth);
            nPercentH = ((float)Height / (float)sourceHeight);
            if (nPercentH < nPercentW)
            {
                nPercent = nPercentH;
                destX = System.Convert.ToInt16((Width -
                              (sourceWidth * nPercent)) / 2);
            }
            else
            {
                nPercent = nPercentW;
                destY = System.Convert.ToInt16((Height -
                              (sourceHeight * nPercent)) / 2);
            }

            int destWidth = (int)(sourceWidth * nPercent);
            int destHeight = (int)(sourceHeight * nPercent);

            Bitmap bmPhoto = new Bitmap(Width, Height,
                              PixelFormat.Format24bppRgb);
            bmPhoto.SetResolution(imgPhoto.HorizontalResolution,
                             imgPhoto.VerticalResolution);

            Graphics grPhoto = Graphics.FromImage(bmPhoto);
            grPhoto.Clear(Color.White);
            grPhoto.InterpolationMode =
                    InterpolationMode.HighQualityBicubic;

            grPhoto.DrawImage(imgPhoto,
                new Rectangle(destX, destY, destWidth, destHeight),
                new Rectangle(sourceX, sourceY, sourceWidth, sourceHeight),
                GraphicsUnit.Pixel);

            grPhoto.Dispose();
            return bmPhoto;
        }


        public static void MergeImagesOLD(Image front, Image background, string saveAs, bool isBanner1Landscape = false)
        {
            int h = Math.Max(front.Height, background.Height);
            int w = Math.Max(front.Width, background.Width);

            using (var bitmap = new Bitmap(w, h))
            {
                using (var canvas = Graphics.FromImage(bitmap))
                {
                    canvas.InterpolationMode = InterpolationMode.HighQualityBilinear;

                    int y = isBanner1Landscape ? 40 : 0;

                    canvas.DrawImage(background, new Rectangle(0, y, w, h), new Rectangle(0, 0, w, h), GraphicsUnit.Pixel);
                    canvas.DrawImage(front, 0, 0);
                    canvas.Save();
                }

                bitmap.Save(saveAs, ImageFormat.Png);
            }

        }

        public static void MergeImages(Image front, Image background, string saveAs, Point backgroundPoint)
        {
            int h = front.Height;
            int w = front.Width;

            using (var bitmap = new Bitmap(w, h))
            {
                using (var canvas = Graphics.FromImage(bitmap))
                {
                    canvas.InterpolationMode = InterpolationMode.HighQualityBilinear;

                    canvas.DrawImage(background, backgroundPoint);
                    canvas.DrawImage(front, 0, 0, w, h);
                    canvas.Save();
                }

                bitmap.Save(saveAs, ImageFormat.Png);
            }
        }


        public static Image AddCenteredText(Image target, string text, Font font, float y)
        {
            using (var canvas = Graphics.FromImage(target))
            {
                SizeF stringsize = canvas.MeasureString(text, font);
                var x = (target.Width - stringsize.Width) / 2;

                canvas.DrawString(text, font, Brushes.White, x, y);

                canvas.Save();
            }

            return target;
        }


        public static Image AddCenteredText(Image target, string text, Font font, Rectangle rectangle)
        {
            using (var canvas = Graphics.FromImage(target))
            {
                SizeF stringsize = canvas.MeasureString(text, font);
                var x = rectangle.Left + (rectangle.Width - stringsize.Width) / 2;
                canvas.DrawString(text, font, Brushes.White, x, rectangle.Top);

                canvas.Save();
            }

            return target;
        }

        public static Image AddCenteredText2(Image target, string text, Font font, Rectangle rectangle)
        {
            using (var canvas = Graphics.FromImage(target))
            {
                //SizeF stringsize = canvas.MeasureString(text, font);
                //var x = rectangle.Left + (rectangle.Width - stringsize.Width) / 2;
                //canvas.DrawString(text, font, Brushes.White, x, rectangle.Top);

                StringFormat sf = new StringFormat();
                sf.LineAlignment = StringAlignment.Center;
                sf.Alignment = StringAlignment.Center;

                canvas.DrawString(text, font, Brushes.White, rectangle, sf);

                canvas.Save();
            }

            return target;
        }

        public static Image CropImage(Image img, Rectangle cropArea)
        {
            Bitmap bmpImage = new Bitmap(img);
            return bmpImage.Clone(cropArea, bmpImage.PixelFormat);
        }

        public static string ToBase64(string imagePath)
        {
            using (Image image = Image.FromFile(imagePath))
            {
                using (MemoryStream m = new MemoryStream())
                {
                    image.Save(m, image.RawFormat);
                    byte[] imageBytes = m.ToArray();

                    // Convert byte[] to Base64 String
                    string base64String = Convert.ToBase64String(imageBytes);
                    return base64String;
                }
            }
        }

        public static void FromBase64(string base64String, string filePath)
        {
            // Remove possible data URI prefix
            if (base64String.StartsWith("data:image"))
            {
                var base64Parts = base64String.Split(',');
                base64String = base64Parts[1];
            }

            // Convert base64 to bytes
            byte[] imageBytes = Convert.FromBase64String(base64String);

            // Save bytes to file
            File.WriteAllBytes(filePath, imageBytes);

            Console.WriteLine($"Image saved to {filePath}");
        }
        public static double CheckTransparencyPercentage(string imagePath)
        {

            using (Bitmap bitmap = new Bitmap(imagePath))
            {
                double p = CheckTransparencyPercentage(bitmap);
                return p;
            }
        }
        public static double CheckTransparencyPercentage(Bitmap bitmap)
        {
            int transparentPixels = 0;
            int totalPixels = bitmap.Width * bitmap.Height;

            for (int x = 0; x < bitmap.Width; x++)
            {
                for (int y = 0; y < bitmap.Height; y++)
                {
                    Color pixelColor = bitmap.GetPixel(x, y);
                    if (pixelColor.A < 255)
                    {
                        transparentPixels++;
                    }
                }
            }
            bitmap.Dispose();
            if (transparentPixels > 0)
            {
                double transparencyPercentage = (double)transparentPixels / (double)totalPixels * 100;
                //Console.WriteLine("Image has transparency. Transparency percentage: " + transparencyPercentage + "%");
                return transparencyPercentage;
            }
            else
            {
                //Console.WriteLine("Image does not have transparency.");
                return 0;
            }
        }
        //private static string SaveImageInS3AndGetALink(string imagePath, string desiredFolder)
        //{
        //    Bitmap bitmap = new Bitmap(imagePath);

        //    string productImagesPath = ConfigurationManager.AppSettings["ProductImages.Path"];
        //    string uploadFolder = HttpContext.Current.Server.MapPath("/" + productImagesPath);
        //    string dir = uploadFolder + "\\" + desiredFolder;
        //    if (!Directory.Exists(dir)) Directory.CreateDirectory(dir);
        //    string fileName = Guid.NewGuid().ToString().Substring(0, 8);
        //    string imageFullPath = string.Format("{0}\\{1}.{2}", dir, fileName, ImagesDownloader.GetImageExtentionByPath(imagePath));
        //    bitmap.Save(imageFullPath);
        //    //file.SaveAs(imageFullPath);
        //    CloudManager.Upload(imageFullPath);
        //    System.IO.File.Delete(imageFullPath);

        //    return string.Format("/{0}/{1}/{2}.{3}", productImagesPath, desiredFolder, fileName, ImagesDownloader.GetImageExtentionByPath(imageFullPath);
        //}
        //For Testing
        public static void OptimizeFolderCompareBlack(string path)
        {
            string uploadPath = $"{path}\\Upload\\";

            foreach (var imageFile in Directory.GetFiles(path))
            {
                try
                {
                    double a = GetBlackPixelPercentage(imageFile);
                    string f = Path.GetFileName(imageFile);
                    string fn = $"{uploadPath}\\{f}";
                    double b = GetBlackPixelPercentage(fn);
                    Console.WriteLine($"{f} {a}% -{b}%");
                }
                catch (Exception ex)
                {

                    Console.WriteLine(ex.Message);
                }

            }

        }

        public static double GetBlackPixelPercentage(string imagePath)
        {
            Bitmap image = new Bitmap(imagePath);
            return GetBlackPixelPercentage(image);
        }
        public static double GetBlackPixelPercentage(Bitmap image)
        {
            try
            {
                int totalPixels = image.Width * image.Height;
                int blackPixels = 0;

                for (int x = 0; x < image.Width; x++)
                {
                    for (int y = 0; y < image.Height; y++)
                    {
                        Color pixelColor = image.GetPixel(x, y);

                        // Check if the pixel is black (you may need to adjust the threshold based on your needs)
                        if (pixelColor.R == 0 && pixelColor.G == 0 && pixelColor.B == 0)
                        {
                            blackPixels++;
                        }
                    }
                }
                double percentageBlack = Math.Round((double)blackPixels / totalPixels * 100, 1);
                //Console.WriteLine($"Percentage of black pixels: {percentageBlack}%");
                return percentageBlack;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to get percentage of black pixels: {ex}");
            }
            return -1;
        }

        public static string ConvertPngToJpeg(string pngPath, long quality = 85L)
        {
            try
            {
                if (!File.Exists(pngPath))
                    throw new FileNotFoundException("PNG file not found", pngPath);
                string jpegPath = Path.ChangeExtension(pngPath, ".jpg");
                using (Image image = Image.FromFile(pngPath))//ok
                {
                    // Get JPEG encoder
                    ImageCodecInfo jpegEncoder = GetEncoder(ImageFormat.Jpeg);
                    // Set compression quality
                    EncoderParameters encoderParams = new EncoderParameters(1);
                    encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, quality);
                    image.Save(jpegPath, jpegEncoder, encoderParams);
                    try
                    {
                        File.Delete(pngPath);
                    }
                    catch { }
                }
                return jpegPath;
            }
            catch
            {
                return pngPath;
            }
        }

        private static ImageCodecInfo GetEncoder(ImageFormat format)
        {
            return Array.Find(ImageCodecInfo.GetImageDecoders(), c => c.FormatID == format.Guid);
        }

        public static void GetImageParametrsBySkiImage(string imagePath, out int width, out int height, out float horizontalResolution, out float verticalResolution)
        {
            using (var image = SKImage.FromEncodedData(imagePath))
            {
                width = image.Width;
                height = image.Height;
            }
            horizontalResolution = 96f;
            verticalResolution = 96f;
        }
    }
}
