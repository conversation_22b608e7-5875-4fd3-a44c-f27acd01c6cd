﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Configuration;
using System.Drawing;
using System.IO;
using System.Text.RegularExpressions;

using Storeya.Core.Entities;
using Storeya.Core.Models.CatalogSync;
using Storeya.Core.Models.DataProviders;
using ImageMagick;

namespace Storeya.Core.Helpers
{
    public class ImagesDownloader
    {
        public ImagesDownloader(Shop shop)
        {
            this.Shop = shop;
        }

        private HttpContext _context;
        public Shop Shop { get; set; }
        public HttpContext Context
        {
            get
            {
                return _context ?? HttpContext.Current;
            }
            set
            {
                _context = value;
            }
        }

        public void DownloadImagesFirstTime(bool uploadAdditionalImages = false)
        {
            using (StoreYaEntities db = new StoreYaEntities())
            {

                var productsEnum = from p in db.Products
                                       //where p.ShopID == this.Shop.ID && p.LocalImage == null && p.ImageUrl != null
                                   where p.ShopID == this.Shop.ID && p.LocalImage == null && !string.IsNullOrEmpty(p.ImageUrl) && !p.ImageUrl.Contains("no_selection")
                                   select p;
                IList<Product> products = productsEnum.ToList();

                Log4NetLogger.Info(string.Format("Running missing images update for {0} images.", products.Count()), this.Shop.ID);

                DownloadImagesForProductList(db, products, false);

                Log4NetLogger.Info("Running missing images has finished.", this.Shop.ID);
            }
        }
        public void DownloadImagesUpgradeAll()
        {
            using (StoreYaEntities db = new StoreYaEntities())
            {

                var productsEnum = from p in db.Products
                                       //where p.ShopID == this.Shop.ID && p.LocalImage == null && p.ImageUrl != null
                                   where p.ShopID == this.Shop.ID && !string.IsNullOrEmpty(p.ImageUrl)
                                   select p;
                IList<Product> products = productsEnum.ToList();

                Log4NetLogger.Info(string.Format("Running ALL images (including an additional images) update for {0} products.", products.Count()), this.Shop.ID);

                DownloadImagesForProductList(db, products, true);

                Log4NetLogger.Info("Running ALL images update has finished.", this.Shop.ID);
            }
        }

        private void DownloadImagesForProductList(StoreYaEntities db, IList<Product> products, bool uploadAdditionalImages)
        {
            int errorCount = 0;
            string productLocalImageUrl = string.Empty;
            int errorsLimit = 20;
            if (products.Count() > 0)
            {
                //stop if more than 10% images are broken
                errorsLimit = Math.Max(products.Count() / 10, 20);
            }

            try
            {
                foreach (Product product in products)
                {

                    if (errorCount > errorsLimit)
                    {
                        throw new Exception("Images downloading stopped. Max amount of errors (20) exceeded. DownloadImagesFirstTime process will be stopped.");
                    }
                    try
                    {
                        productLocalImageUrl = GenerateImagesByProduct(product);
                        product.LocalImage = productLocalImageUrl;
                        db.SaveChanges();

                        if (uploadAdditionalImages)
                        {
                            UploadAdditionalImages(product);
                        }

                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        Log4NetLogger.Info(string.Format("Unable to download and resize product image. ProductID: {0} to Local directory: {1}. Internal error: {2}", product.ID, productLocalImageUrl, ex.Message, this.Shop.ID), ex, this.Shop.ID);
                    }
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Images downloading stopped. Max amount of errors (20) exceeded.", ex, this.Shop.ID);
            }

            // db.SaveChanges();
        }


        public static void UploadAdditionalImages(Product product)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            List<ProductImage> additionalImages = db.ProductImages.Where(i => i.ProductID == product.ID).ToList();
            if (additionalImages != null && additionalImages.Count > 0)
            {
                int index = 0;
                foreach (ProductImage additionalimage in additionalImages)
                {
                    index++;
                    try
                    {
                        string fileName = ProductImageHelper.CreateImagePath(product.Name, product.OriginalProductID, additionalimage.ImageUrl, index);
                        string phicalPath = ProductImageHelper.GetFullLocalFilePath(fileName, product.ShopID);
                        string relativePath = ProductImageHelper.GetRelativeImagePath(fileName, product.ShopID);

                        DownloadAndRescaleImage(additionalimage.ImageUrl, phicalPath, product.ShopID);

                        additionalimage.LocalImage = relativePath;
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Log4NetLogger.Error(string.Format("Failed to generate an additional image from {0}. Productid: {1}, OriginalProductID: {2}", additionalimage.ImageUrl, product.ID, product.OriginalProductID), ex, product.ShopID);

                    }
                }
            }
        }


        public static string GenerateImagesByProduct(Product product)
        {
            if (string.IsNullOrEmpty(product.ImageUrl))
            {
                //no image source specified
                return null;
            }

            string productImagesPath = ConfigurationManager.AppSettings["ProductImages.Path"];
            string rootFolderPath = ConfigurationManager.AppSettings["ProductImages.RootPath"];

            int shopID = product.ShopID;

            if (shopID == 0 && product.Shop != null)
                shopID = product.Shop.ID;

            if (shopID == 0)
                throw new ArgumentNullException("Missing shop ID value to create image path for download.");


            //string fileName;
            ////string imageSmallFullPath;
            //string imageFullPath;
            //string imageTempFullPath;
            //GetPathNames(product, productImagesPath, out fileName, out imageFullPath, out imageTempFullPath, rootFolderPath);

            string imageName = ProductImageHelper.CreateImagePath(product, product.ImageUrl);
            string physicalPath = ProductImageHelper.GetFullLocalFilePath(imageName, shopID);
            string relativePath = ProductImageHelper.GetRelativeImagePath(imageName, shopID);

            if (ConfigHelper.IsResizeImages(shopID))
            {
                Download_WithoutRescaleImage(product.ImageUrl, physicalPath, product.ShopID);
            }
            else
            {
                DownloadAndRescaleImage(product.ImageUrl, physicalPath, product.ShopID);
            }


            return relativePath;//string.Format("/{0}/{1}{2}", productImagesPath, GetImagesFolderName(shopID), CreateImagePath(product));
        }

        public static string GetImageExtentionByPath(string path)
        {
            if (path.ToLower().Contains(".png")) return "png";
            if (path.ToLower().Contains(".jpg")) return "jpg";
            if (path.ToLower().Contains(".jpeg")) return "jpg";
            if (path.ToLower().Contains(".bmp")) return "bmp";
            if (path.ToLower().Contains(".gif")) return "gif";
            if (path.ToLower().Contains(".heic")) return "heic";
            if (path.ToLower().Contains(".webp")) return "webp";
            return "jpg";
        }

        public static List<RawProductImage> GenerateAdditionalImagesByProduct(ProductRawData product, int shopID)
        {
            if (product.Images.Count == 0)
            {
                //no additional images specified
                return null;
            }

            List<RawProductImage> localImages = new List<RawProductImage>();

            if (shopID == 0)
                throw new ArgumentNullException("Missing shop ID value to create image path for download.");

            int index = 0;
            foreach (var image in product.Images)
            {
                index++;
                string fileName = ProductImageHelper.CreateImagePath(product.Name, product.OriginalID, image.ExternalUrl, index);
                string phicalPath = ProductImageHelper.GetFullLocalFilePath(fileName, shopID);
                string relativePath = ProductImageHelper.GetRelativeImagePath(fileName, shopID);

                try
                {
                    DownloadAndRescaleImage(image.ExternalUrl, phicalPath, shopID);
                    localImages.Add(new RawProductImage() { ExternalUrl = image.ExternalUrl, RelativeUrl = relativePath });
                }
                catch (Exception ex)
                {
                    Log4NetLogger.Error("Failed to generate an additional image from " + image.ExternalUrl, ex, shopID);
                }

            }
            return localImages;
        }

        public static void DownloadAndRescaleImage(string sourceUrl, string localPath, int shopID, int? requiredHeight = null, int? requiredWidth = null)
        {
            // handling https requests
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            //bypass (ignore) SSL certificate
            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;

            WebClient client = null;
            if (ConfigHelper.IsValueInList("DownloadImageWithReferer", shopID.ToString()))
            {
                string referer = "http://" + UrlPathHelper.GetDomainName(sourceUrl);
                client = SetWebClient(referer);

                //if (!string.IsNullOrEmpty(sourceUrl) && sourceUrl.Contains("edge.shop.com/ccimg.shop.com"))
                //{
                //    client = ShopComProvider.SetWebClient(referer);
                //}
                //else
                //{
                //    client = SetWebClient(referer);
                //}
            }
            else
            {
                client = SetWebClient();
                //if (!string.IsNullOrEmpty(sourceUrl) && sourceUrl.Contains("edge.shop.com/ccimg.shop.com"))
                //{
                //    client = ShopComProvider.SetWebClient();
                //}
                //else
                //{
                //    client = SetWebClient();
                //}
            }

            try
            {
                string tempFileName = ProductImageHelper.GetTempFileName(localPath);

                if (!Directory.Exists(Path.GetDirectoryName(localPath)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(localPath));
                }
                if (!Directory.Exists(Path.GetDirectoryName(tempFileName)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(tempFileName));
                }
                if (sourceUrl.ToLower().Contains(".webp"))
                {
                    client = Storeya.Core.Models.ImageOptimizer.ImageOptimizer.AddHeadersForWebP(client);
                }
                try
                {
                    client.DownloadFile(sourceUrl, tempFileName);
                }
                catch (Exception ex)
                {
                    if (sourceUrl.ToLower().StartsWith("https://"))
                    {
                        sourceUrl = sourceUrl.Replace("https://", "http://");
                        client.DownloadFile(sourceUrl, tempFileName);
                    }
                    else
                    {
                        throw ex;
                    }
                }

                RescaleImage(tempFileName, localPath, null, requiredHeight, requiredWidth);

                MoveToImagesStorage(localPath);

                System.IO.File.Delete(localPath);

                //if (!string.IsNullOrEmpty(ConfigHelper.GetValue("StaticImagesLocation")))
                //{
                //MoveToImagesStorage(localPath);
                //}
            }
            catch (Exception ex)
            {
                //Log4NetLogger.Error(Log4NetLogger.BuildMessage("Failed to download image " + product.ImageUrl, product.Shop.ID));
                throw new Exception("Failed to create image from " + sourceUrl, ex);
            }
            finally
            {
                client.Dispose();
            }
        }

        public static WebClient SetWebClient(string referer = null)
        {
            WebClient client = new WebClient();
            client.Headers.Add("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.120 Safari/537.36");
            //client.Headers.Add("Accept-Encoding", "gzip,deflate");
            client.Headers.Add("Accept-Language", "en-US,en;q=0.8,ru;q=0.6");

            //client.Headers.Add("Content-Type", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            client.Headers.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");

            client.Headers.Add(HttpRequestHeader.Cookie, "storeya");

            if (!string.IsNullOrEmpty(referer))
            {
                client.Headers.Add("Referer", referer);
            }

            return client;
        }


        public static void Download_WithoutRescaleImage(string sourceUrl, string localPath, int shopID)
        {
            // handling https requests
            //ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            //bypass (ignore) SSL certificate
            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;

            WebClient client = null;

            if (ConfigHelper.IsValueInList("DownloadImageWithReferer", shopID.ToString()))
            {
                string referer = "http://" + UrlPathHelper.GetDomainName(sourceUrl);
                client = SetWebClient(referer);
                //if (!string.IsNullOrEmpty(sourceUrl) && sourceUrl.Contains("edge.shop.com/ccimg.shop.com"))
                //{
                //    client = ShopComProvider.SetWebClient(referer);
                //}
                //else
                //{
                //    client = SetWebClient(referer);
                //}
            }
            else
            {
                client = SetWebClient();
                //if (!string.IsNullOrEmpty(sourceUrl) && sourceUrl.Contains("edge.shop.com/ccimg.shop.com"))
                //{
                //    client = ShopComProvider.SetWebClient();
                //}
                //else
                //{
                //    client = SetWebClient();
                //}
            }

            try
            {
                string tempFileName = ProductImageHelper.GetTempFileName(localPath);

                if (!Directory.Exists(Path.GetDirectoryName(localPath)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(localPath));
                }
                if (!Directory.Exists(Path.GetDirectoryName(tempFileName)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(tempFileName));
                }

                try
                {
                    client.DownloadFile(sourceUrl, tempFileName);
                }
                catch (Exception)
                {
                    if (sourceUrl.ToLower().StartsWith("https://"))
                    {
                        sourceUrl = sourceUrl.Replace("https://", "http://");
                        client.DownloadFile(sourceUrl, tempFileName);
                    }
                    else
                    {
                        throw;
                    }
                }


                File.Copy(tempFileName, localPath, true);
                MoveToImagesStorage(localPath);

                DeleteTempFile(tempFileName);
                System.IO.File.Delete(localPath);
            }
            catch (Exception ex)
            {
                //Log4NetLogger.Error(Log4NetLogger.BuildMessage("Failed to download image " + product.ImageUrl, product.Shop.ID));
                throw new Exception("Failed to create image from " + sourceUrl, ex);
            }
            finally
            {
                client.Dispose();
            }
        }
        private static void MoveToImagesStorage(string localPath)
        {
            //AzureManager.UploadFileToAzure(localPath);
            CloudManager.Upload(localPath);
        }


        #region Images manipulations
        public static void RescaleImageNew(string sourceFullPath, string imageFullPath, Color? color = null, float? requiredHeight = null, float? requiredWidth = null)
        {
            float height = (requiredHeight == null) ? float.Parse(ConfigurationManager.AppSettings["ProductImages.Height"]) : requiredHeight.Value;
            float width = (requiredWidth == null) ? float.Parse(ConfigurationManager.AppSettings["ProductImages.Width"]) : requiredWidth.Value;

            int dimX, dimY;
            try
            {
                try
                {

                    //try first method
                    RescaleImageNew(
                                    sourceFullPath,
                                    width, height, true,
                                    imageFullPath
                                    , out dimX
                                    , out dimY, color);
                }
                catch (Exception ex)
                {
                    //Log4NetLogger.Info("Using old resizing method failed for file " + sourceFullPath + ". Trying the new method.", ex);

                    //try second method
                    try
                    {
                        RescaleImage2New(sourceFullPath, width, height, imageFullPath);
                    }
                    catch
                    {
                        //both methods failed - just copy original image
                        try
                        {
                            File.Copy(sourceFullPath, imageFullPath, false);
                            Log4NetLogger.Error("Failed to convert Image - original image copied to " + imageFullPath, ex);
                        }
                        catch (Exception ex2)
                        {
                            Log4NetLogger.Error("Failed to copy original file " + imageFullPath, ex2);
                        }
                    }

                }

                //Delete temp file after the thumbnail was created
                DeleteTempFile(sourceFullPath);

            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("Error saving thumbnail {0}, Adding to error list. Message: {1}. Exception: {2}.", sourceFullPath, ex.Message, ex.ToString()));
            }
        }



        public static void RescaleImage(string sourceFullPath, string imageFullPath, Color? color = null, int? requiredHeight = null, int? requiredWidth = null)
        {
            int height = (requiredHeight == null) ? int.Parse(ConfigurationManager.AppSettings["ProductImages.Height"]) : requiredHeight.Value;
            int width = (requiredWidth == null) ? int.Parse(ConfigurationManager.AppSettings["ProductImages.Width"]) : requiredWidth.Value;

            int dimX, dimY;
            try
            {
                try
                {

                    //try first method
                    RescaleImage(
                                  Image.FromFile(sourceFullPath),
                                    width, height, true,
                                    imageFullPath
                                    , out dimX
                                    , out dimY, color);
                }
                catch (Exception ex)
                {
                    //Log4NetLogger.Info("Using old resizing method failed for file " + sourceFullPath + ". Trying the new method.", ex);

                    //try second method
                    try
                    {
                        RescaleImage2(sourceFullPath, width, height, imageFullPath);
                    }
                    catch
                    {
                        //both methods failed - just copy original image
                        try
                        {
                            File.Copy(sourceFullPath, imageFullPath, false);
                            Log4NetLogger.Error("Failed to convert Image - original image copied to " + imageFullPath, ex);
                        }
                        catch (Exception ex2)
                        {
                            Log4NetLogger.Error("Failed to copy original file " + imageFullPath, ex2);
                        }
                    }

                }

                //Delete temp file after the thumbnail was created
                DeleteTempFile(sourceFullPath);

            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("Error saving thumbnail {0}, Adding to error list. Message: {1}. Exception: {2}.", sourceFullPath, ex.Message, ex.ToString()));
            }
        }
        public static void RescaleImage(string sourceFullPath, string imageFullPath, int width, int height, Color? color = null)
        {
            int dimX, dimY;
            try
            {
                ImagesDownloader.RescaleImage(
                                System.Drawing.Image.FromFile(sourceFullPath),
                                width, height, true,
                                imageFullPath
                                , out dimX
                                , out dimY, color);
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("Error saving thumbnail {0}, Adding to error list. Exception: {1}. Stack: {2}", sourceFullPath, ex.Message, ex.StackTrace));
            }
        }

        public static void RescaleImage2New(string sourceImage, float width, float height, string saveAs)
        {
            Image resizedImage = ImagesDownloader.ResizeImageNew(sourceImage, saveAs, width, height, true);
            Image img2 = Storeya.Core.Helpers.ImageUtilities.FixedSize(resizedImage, (int)width, (int)height);
            Storeya.Core.Helpers.ImageUtilities.SaveJpeg(saveAs, img2, 100);
        }


        public static void RescaleImage2(string sourceImage, int width, int height, string saveAs)
        {
            Image resizedImage = ImagesDownloader.ResizeImage(sourceImage, saveAs, width, height, true);
            Image img2 = Storeya.Core.Helpers.ImageUtilities.FixedSize(resizedImage, width, height);
            Storeya.Core.Helpers.ImageUtilities.SaveJpeg(saveAs, img2, 100);
        }
        public static void RescaleImage(Image sourceImage, int width, int height, bool isFixed, string saveAs, out int sourceWidth, out int sourceHeight, Color? color = null)
        {
            sourceWidth = 0;
            sourceHeight = 0;
            try
            {
                int iow = sourceImage.Width;
                int ioh = sourceImage.Height;
                sourceWidth = iow;
                sourceHeight = ioh;
                bool bresize = true;

                //If the size is ok, do nothing
                if (iow == width && ioh == height)
                {
                    bresize = false;
                }

                //If we aren't strict and the image size is smaller than allowed do nothing
                if (!isFixed && iow <= width && ioh <= height)
                {
                    bresize = false;
                }

                float fwratio = (float)((float)iow / (float)width);
                float fhratio = (float)((float)ioh / (float)height);
                float fmratio = Math.Max(Math.Max(fwratio, fhratio), 1);

                int inw = (int)Math.Round(((float)iow / fmratio), 0);
                int inh = (int)Math.Round((float)ioh / fmratio, 0);

                if (!bresize)
                {
                    inw = iow;
                    inh = ioh;
                }

                System.Drawing.Imaging.PixelFormat pxf = sourceImage.PixelFormat;

                if ((int)sourceImage.PixelFormat == 8207)
                {
                    pxf = System.Drawing.Imaging.PixelFormat.Format32bppArgb;

                }
                else
                {
                    switch (sourceImage.PixelFormat)
                    {
                        case System.Drawing.Imaging.PixelFormat.Format1bppIndexed:
                        case System.Drawing.Imaging.PixelFormat.Format4bppIndexed:
                        case System.Drawing.Imaging.PixelFormat.Format8bppIndexed:
                        case System.Drawing.Imaging.PixelFormat.Undefined:
                        case System.Drawing.Imaging.PixelFormat.Format16bppArgb1555:
                        case System.Drawing.Imaging.PixelFormat.Format16bppGrayScale:
                            pxf = System.Drawing.Imaging.PixelFormat.Format24bppRgb;
                            break;

                        default:
                            pxf = sourceImage.PixelFormat;
                            break;
                    }
                }


                Bitmap bmpBitmap = new Bitmap(width, height, pxf);
                Image imNewThumb = new Bitmap(inw, inh, pxf);

                System.Drawing.Imaging.ImageFormat imf = sourceImage.RawFormat;

                System.Drawing.Imaging.EncoderParameters encoderParameters = new System.Drawing.Imaging.EncoderParameters(1);
                encoderParameters.Param[0] = new System.Drawing.Imaging.EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 85L);

                Graphics graphics = Graphics.FromImage(imNewThumb);
                graphics.FillRectangle(new SolidBrush(color ?? Color.White), 0, 0, width, height);
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                graphics.DrawImage(sourceImage, 0, 0, inw, inh);

                Graphics bgraphics = null;
                if (isFixed)
                {
                    bgraphics = Graphics.FromImage(bmpBitmap);
                    bgraphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                    bgraphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    bgraphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                    bgraphics.FillRectangle(new SolidBrush(color ?? Color.White), 0, 0, width, height);
                    bgraphics.DrawImage(imNewThumb, (int)((width - inw) / 2), (int)((height - inh) / 2), inw, inh);
                }

                sourceImage.Dispose();
                sourceImage = null;

                if (imf.Guid.ToString().ToLower() == "b96b3cae-0728-11d3-9d7b-0000f81ef32e")
                    bmpBitmap.Save(saveAs, System.Drawing.Imaging.ImageCodecInfo.GetImageEncoders()[1], encoderParameters);
                else
                    bmpBitmap.Save(saveAs, imf);

                bgraphics.Dispose();

                graphics.Dispose();
                bmpBitmap.Dispose();
                imNewThumb.Dispose();
                graphics = null;
                bgraphics = null;
                imNewThumb = null;
                bmpBitmap = null;
            }
            catch (Exception ex)
            {
                throw new Exception("Unable to convert image", ex);
                //Log4NetLogger.Error("Unable to convert image", ex);
            }
        }


        public static void RescaleImageNew(string sourceFullPath, float width, float height, bool isFixed, string saveAs, out int sourceWidth, out int sourceHeight, Color? color = null)
        {
            Image sourceImage = Image.FromFile(sourceFullPath);

            sourceWidth = 0;
            sourceHeight = 0;
            try
            {
                int iow = sourceImage.Width;
                int ioh = sourceImage.Height;
                sourceWidth = iow;
                sourceHeight = ioh;
                bool bresize = true;

                //If the size is ok, do nothing
                if (iow == width && ioh == height)
                {
                    bresize = false;
                }

                //If we aren't strict and the image size is smaller than allowed do nothing
                if (!isFixed && iow <= width && ioh <= height)
                {
                    bresize = false;
                }

                float fwratio = (float)((float)iow / width);
                float fhratio = (float)((float)ioh / height);
                float fmratio = Math.Max(Math.Max(fwratio, fhratio), 1);

                int inw = (int)Math.Round(((float)iow / fmratio), 0);
                int inh = (int)Math.Round((float)ioh / fmratio, 0);

                if (!bresize)
                {
                    inw = iow;
                    inh = ioh;
                }

                System.Drawing.Imaging.PixelFormat pxf = sourceImage.PixelFormat;

                if ((int)sourceImage.PixelFormat == 8207)
                {
                    pxf = System.Drawing.Imaging.PixelFormat.Format32bppArgb;

                }
                else
                {
                    switch (sourceImage.PixelFormat)
                    {
                        case System.Drawing.Imaging.PixelFormat.Format1bppIndexed:
                        case System.Drawing.Imaging.PixelFormat.Format4bppIndexed:
                        case System.Drawing.Imaging.PixelFormat.Format8bppIndexed:
                        case System.Drawing.Imaging.PixelFormat.Undefined:
                        case System.Drawing.Imaging.PixelFormat.Format16bppArgb1555:
                        case System.Drawing.Imaging.PixelFormat.Format16bppGrayScale:
                            pxf = System.Drawing.Imaging.PixelFormat.Format24bppRgb;
                            break;

                        default:
                            pxf = sourceImage.PixelFormat;
                            break;
                    }
                }


                Bitmap bmpBitmap = new Bitmap((int)width, (int)height, pxf);
                Image imNewThumb = new Bitmap(inw, inh, pxf);

                System.Drawing.Imaging.ImageFormat imf = sourceImage.RawFormat;

                System.Drawing.Imaging.EncoderParameters encoderParameters = new System.Drawing.Imaging.EncoderParameters(1);
                encoderParameters.Param[0] = new System.Drawing.Imaging.EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 85L);

                Graphics graphics = Graphics.FromImage(imNewThumb);
                graphics.FillRectangle(new SolidBrush(color ?? Color.White), 0, 0, width, height);
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                graphics.DrawImage(sourceImage, 0, 0, inw, inh);

                Graphics bgraphics = null;
                if (isFixed)
                {
                    bgraphics = Graphics.FromImage(bmpBitmap);
                    bgraphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                    bgraphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    bgraphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                    bgraphics.FillRectangle(new SolidBrush(color ?? Color.White), 0, 0, width, height);
                    bgraphics.DrawImage(imNewThumb, (int)((width - inw) / 2), (int)((height - inh) / 2), inw, inh);
                }

                sourceImage.Dispose();
                sourceImage = null;

                if (imf.Guid.ToString().ToLower() == "b96b3cae-0728-11d3-9d7b-0000f81ef32e")
                    bmpBitmap.Save(saveAs, System.Drawing.Imaging.ImageCodecInfo.GetImageEncoders()[1], encoderParameters);
                else
                    bmpBitmap.Save(saveAs, imf);

                bgraphics.Dispose();

                graphics.Dispose();
                bmpBitmap.Dispose();
                imNewThumb.Dispose();
                graphics = null;
                bgraphics = null;
                imNewThumb = null;
                bmpBitmap = null;
            }
            catch (Exception ex)
            {
                throw new Exception("Unable to convert image", ex);
                //Log4NetLogger.Error("Unable to convert image", ex);
            }
        }

        public static Image ResizeImageNew(string originalFile, string newFile, float newWidth, float maxHeight, bool onlyResizeIfWider)
        {
            System.Drawing.Image fullsizeImage = System.Drawing.Image.FromFile(originalFile);

            // Prevent using images internal thumbnail
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);

            if (onlyResizeIfWider)
            {
                if (fullsizeImage.Width <= newWidth)
                {
                    newWidth = fullsizeImage.Width;
                }
            }

            float newHeight = fullsizeImage.Height * newWidth / fullsizeImage.Width;
            if (newHeight > maxHeight)
            {
                // Resize with height instead
                newWidth = fullsizeImage.Width * maxHeight / fullsizeImage.Height;
                newHeight = maxHeight;
            }

            System.Drawing.Image newImage = fullsizeImage.GetThumbnailImage((int)newWidth, (int)newHeight, null, IntPtr.Zero);

            // Clear handle to original file so that we can overwrite it if necessary
            fullsizeImage.Dispose();

            return newImage;

        }


        public static Image ResizeImage(string originalFile, string newFile, int newWidth, int maxHeight, bool onlyResizeIfWider)
        {
            System.Drawing.Image fullsizeImage = System.Drawing.Image.FromFile(originalFile);

            // Prevent using images internal thumbnail
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);
            fullsizeImage.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);

            if (onlyResizeIfWider)
            {
                if (fullsizeImage.Width <= newWidth)
                {
                    newWidth = fullsizeImage.Width;
                }
            }

            int newHeight = fullsizeImage.Height * newWidth / fullsizeImage.Width;
            if (newHeight > maxHeight)
            {
                // Resize with height instead
                newWidth = fullsizeImage.Width * maxHeight / fullsizeImage.Height;
                newHeight = maxHeight;
            }

            System.Drawing.Image newImage = fullsizeImage.GetThumbnailImage(newWidth, newHeight, null, IntPtr.Zero);

            // Clear handle to original file so that we can overwrite it if necessary
            fullsizeImage.Dispose();

            return newImage;

        }
        #endregion

        private static void DeleteTempFile(string sourceFullPath)
        {
            try
            {
                File.Delete(sourceFullPath);
            }
            catch { }
        }

        //public static void GetPathNames(int shopID, string uploadFolder, string imageFolderName, out string fileName, out string imageFullPath, out string imageTempFullPath, object basePath = null)
        //{            
        //    string uploadFolderPath = string.Empty;
        //    if (basePath == null)
        //        uploadFolderPath = HttpContext.Current.Server.MapPath("/" + uploadFolder);
        //    else
        //        uploadFolderPath = (string)basePath + uploadFolder;

        //    string shopFolderPath = uploadFolderPath + "\\" + ImagesDownloader.GetImagesFolderName(shopID);

        //    fileName = Guid.NewGuid().ToString().Substring(0, 8);         

        //    string dir = string.Format("{0}\\{1}", shopFolderPath, imageFolderName);          
        //    string dirTemp = string.Format("{0}\\Temp", dir);

        //    if (!Directory.Exists(dir))
        //    {
        //        Directory.CreateDirectory(dir);
        //    }
        //    if (!Directory.Exists(dirTemp))
        //    {
        //        Directory.CreateDirectory(dirTemp);
        //    }

        //    imageFullPath = string.Format("{0}\\{1}.jpg", dir, fileName);          
        //    imageTempFullPath = string.Format("{0}\\{1}.jpg", dirTemp, fileName);
        //}
        //private static string GetProductImageFullPath(Product product)
        //{
        //    string uploadFolder = GetUploadFolder();

        //    var fileName = CreateImagePath(product);
        //    int shopID = product.ShopID;
        //    if (shopID == 0 && product.Shop != null)
        //    {
        //        shopID = product.Shop.ID;
        //    }
        //    if (shopID == 0)
        //    {
        //        throw new ArgumentNullException("Missing shop ID value to create image path for download.");
        //    }
        //    string dir = string.Format("{0}\\{1}", uploadFolder, GetImagesFolderName(shopID));

        //    return string.Format("{0}\\{1}", dir, fileName);

        //}
        //private static void GetPathNames(Product product, string productImagesPath, out string fileName, out string imageFullPath, out string imageTempFullPath, string serverPath)
        //{
        //    string uploadFolder = string.Empty;
        //    uploadFolder = (serverPath + productImagesPath).ToLower();

        //    fileName = CreateImagePath(product);
        //    int shopID = product.ShopID;
        //    if (shopID == 0 && product.Shop != null)
        //    {
        //        shopID = product.Shop.ID;
        //    }
        //    if (shopID == 0)
        //    {
        //        throw new ArgumentNullException("Missing shop ID value to create image path for download.");
        //    }
        //    string dirTemp = string.Format("{0}\\Temp\\{1}", uploadFolder, GetImagesFolderName(shopID));
        //    string dir = string.Format("{0}\\{1}", uploadFolder, GetImagesFolderName(shopID));
        //    //string dirSmall = dir + @"\small";


        //    imageTempFullPath = string.Format("{0}\\{1}", dirTemp, fileName);
        //    imageFullPath = string.Format("{0}\\{1}", dir, fileName);
        //    //imageSmallFullPath = string.Format("{0}\\{1}", dirSmall, fileName);

        //    //if (!Directory.Exists(Path.GetDirectoryName(imageFullPath)))
        //    //{
        //    //    Directory.CreateDirectory(Path.GetDirectoryName(imageFullPath));
        //    //}
        //    //if (!Directory.Exists(Path.GetDirectoryName(imageSmallFullPath)))
        //    //{
        //    //    Directory.CreateDirectory(Path.GetDirectoryName(imageSmallFullPath));
        //    //}
        //    //if (!Directory.Exists(Path.GetDirectoryName(imageTempFullPath)))
        //    //{
        //    //    Directory.CreateDirectory(Path.GetDirectoryName(imageTempFullPath));
        //    //}


        //}

        // prod/0/001/1312312 or 1312312 for old shops

        private static string GetShopImagesFolder(int shopID)
        {
            return ProductImageHelper.GetImagesFolderName(shopID);
        }

        public static string GetValidFileName(string name)
        {
            return ProductImageHelper.GetValidFileName(name);
        }

        public static string GetShopImagesDirectory(int shopID)
        {
            string urlToShop = ProductImageHelper.GetImagesFolderName(shopID);

            urlToShop = urlToShop.Replace("/", "\\");

            string rootPath = ConfigHelper.GetValue("ProductImages.RootPath");
            string uploadsFolderPath = ConfigHelper.GetValue("ProductImages.Path");

            return Path.Combine(Path.Combine(rootPath, uploadsFolderPath), urlToShop);
        }
        public static string GetShopImagesUrl(int shopID)
        {
            string uploadsFolderPath = ConfigHelper.GetValue("ProductImages.Path");
            return uploadsFolderPath + "/" + ProductImageHelper.GetImagesFolderName(shopID);
        }


        public static void DownloadForBH(string sourceUrl, string localPath, int shopID)
        {
            // handling https requests
            //ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            WebClient client = ShopComProvider.SetWebClient();

            try
            {
                string tempFileName = ProductImageHelper.GetTempFileName(localPath);

                if (!Directory.Exists(Path.GetDirectoryName(localPath)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(localPath));
                }

                client.DownloadFile(sourceUrl, localPath);
            }
            catch (Exception ex)
            {
                //Log4NetLogger.Error(Log4NetLogger.BuildMessage("Failed to download image " + product.ImageUrl, product.Shop.ID));
                throw new Exception("Failed to create image from " + sourceUrl, ex);
            }
            finally
            {
                client.Dispose();
            }
        }

    }
}