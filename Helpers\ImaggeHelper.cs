﻿using Amazon.S3.Model;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Drawing.Imaging;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using System.Windows.Forms.VisualStyles;
using System.Drawing.Drawing2D;
using Storeya.Core.Models.CRM;

namespace Storeya.Core.Helpers
{

    public enum PmaxImageTypeMinSize
    {
        portraitimage = 288000,
        squareimage = 90000,
        landscapeimage = 188400,
        logo = 16400
    }
    public static class PmaxImageMaxSize
    {
        public const string PortraitImage = "960x1200";
        public const string SquareImage = "1200x1200";
        public const string LandscapeImage = "1200x628";
    }
    public class ImaggeHelper
    {
        public const string _apiKey = "acc_583b6379a109e44";
        public const string _apiSecret = "c2c79c086317f4beafc22766f9fdc04b";
        public static List<string> CropImage(string filePath, int counter, string folderName, out bool hasIssue, bool logoImage = false)
        {
            //string apiKey = "acc_583b6379a109e44";
            //string apiSecret = "c2c79c086317f4beafc22766f9fdc04b";
            string image = filePath;

            string basicAuthValue = Convert.ToBase64String(Encoding.UTF8.GetBytes(String.Format("{0}:{1}", _apiKey, _apiSecret)));

            var client = new RestClient("https://api.imagga.com/v2/croppings");
            client.Timeout = -1;

            var request = new RestRequest(Method.POST);
            request.AddHeader("Authorization", String.Format("Basic {0}", basicAuthValue));
            request.AddFile("image", image);
            if (!logoImage)
            {
                request.AddParameter("resolution", "4x5,1x1,600x314"); //Portrait image,  Square image,  Landscape image
            }
            else
            {
                request.AddParameter("resolution", "1200x1200");
            }
            //request.AddParameter("image_result", "1");

            IRestResponse response = client.Execute(request);
            Console.WriteLine(response.Content);

            return CropImageByInstructions(filePath, response.Content, folderName, counter, out hasIssue);

            //string fileName = Path.GetFileName(filePath);
            //string ext = Path.GetExtension(filePath);
            //string newFileName = filePath.Replace(fileName,"apiresponse.txt");
        }

        public static List<string> CropImageByInstructions(string inputImagePath, string cropInstructions, string folderName, int counter, out bool hasIssue)
        {
            hasIssue = false;
            List<string> croppedImages = new List<string>();
            var number = counter + 1;
            JObject json = JObject.Parse(cropInstructions);
            try
            {
                dynamic instructions = json["result"]["croppings"];
                foreach (var inst in instructions)
                {
                    int target_height = inst.target_height;
                    int target_width = inst.target_width;
                    string image_name = $"{number}_";
                    string maxSize = null;
                    if (target_height * target_width == 4 * 5)
                    {
                        image_name += PmaxImageTypeMinSize.portraitimage.ToString();
                        maxSize = PmaxImageMaxSize.PortraitImage;
                    }
                    else if (target_height * target_width == 1)
                    {
                        image_name += PmaxImageTypeMinSize.squareimage.ToString();
                        maxSize = PmaxImageMaxSize.SquareImage;
                    }
                    else if (target_height * target_width == 600 * 314)
                    {
                        image_name += PmaxImageTypeMinSize.landscapeimage.ToString();
                        maxSize = PmaxImageMaxSize.LandscapeImage;
                    }
                    else if (target_height == 1200 && target_width == 1200)
                    {
                        image_name += "logo_image";
                        maxSize = PmaxImageMaxSize.SquareImage;
                    }
                    string ext = Path.GetExtension(inputImagePath);
                    image_name = image_name + ext;
                    croppedImages.Add(image_name);
                    if (!folderName.EndsWith("/"))
                    {
                        folderName += "/";
                    }
                    string fullImagePath = folderName + image_name;
                    CropSingleImageImage(inputImagePath, inst.ToString(), fullImagePath);
                    if (ImageIsTooBig(fullImagePath, maxSize))
                    {
                        ResizeImage(fullImagePath, maxSize);
                    }
                }
            }
            catch (Exception ex)
            {
                hasIssue = true;
                try
                {
                    if (json["status"]["type"].ToString() == "error")
                    {
                        EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "Please check next error for api.imagga.com (Cropping images for Pmax)", json["status"]["text"].ToString());
                        return null;
                    }
                }
                catch
                {
                    throw new Exception(cropInstructions + "; " + ex.ToString());
                }
            }
            return croppedImages;
        }
        public static void ResizeImage(string fullImagePath, string maxSize)
        {
            string[] maxSizeNumbers = maxSize.Split('x');
            int height = Int16.Parse(maxSizeNumbers[1]);
            int width = Int16.Parse(maxSizeNumbers[0]);
            var destRect = new Rectangle(0, 0, width, height);
            if (fullImagePath.Contains(".webp"))
            {
                ImageUtilities.SaveWebP(fullImagePath, fullImagePath, quality: 75, resizeHeight: height, resizeWidth: width);
            }
            else
            {
                using (Image image = Image.FromFile(fullImagePath)) //ok
                {
                    var destImage = new Bitmap(width, height);
                    destImage.SetResolution(image.HorizontalResolution, image.VerticalResolution);

                    using (var graphics = Graphics.FromImage(destImage))
                    {
                        graphics.CompositingMode = CompositingMode.SourceCopy;
                        graphics.CompositingQuality = CompositingQuality.HighQuality;
                        graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;

                        using (var wrapMode = new ImageAttributes())
                        {
                            wrapMode.SetWrapMode(WrapMode.TileFlipXY);
                            graphics.DrawImage(image, destRect, 0, 0, image.Width, image.Height, GraphicsUnit.Pixel, wrapMode);
                        }
                    }
                    image.Dispose();
                    destImage.Save(fullImagePath);
                }
            }
        }
        private static bool ImageIsTooBig(string fullImagePath, string maxSize)
        {
            string[] maxSizeNumbers = maxSize.Split('x');
            int heigth = Int16.Parse(maxSizeNumbers[1]);
            int width = Int16.Parse(maxSizeNumbers[0]);
            int imageHeight, imageWidth;
            if (fullImagePath.Contains(".webp"))
            {
                ImageUtilities.GetImageParametrsBySkiImage(fullImagePath, out imageWidth, out imageHeight, out float hResolution, out float vResolution);
            }
            else
            {
                using (var image = Image.FromFile(fullImagePath)) //ok
                {
                    imageHeight = image.Height;
                    imageWidth = image.Width;
                }
            }
            if ((imageHeight * imageWidth) / 1.25 > (heigth * width))
            {
                return true;
            }
            return false;
        }

        public static void CropSingleImageImage(string inputImagePath, string cropInstructions, string imagePath)
        {
            dynamic instr1 = JObject.Parse(cropInstructions);
            int x1 = (int)instr1["x1"];
            int y1 = (int)instr1["y1"];
            int x2 = (int)instr1["x2"];
            int y2 = (int)instr1["y2"];

            using (Image originalImage = Image.FromFile(inputImagePath))
            {
                // Create a rectangle representing the cropping area
                Rectangle cropArea = new Rectangle(x1, y1, x2 - x1, y2 - y1);

                // Crop the image
                using (Bitmap croppedBitmap = new Bitmap(cropArea.Width, cropArea.Height))
                {
                    using (Graphics g = Graphics.FromImage(croppedBitmap))
                    {
                        g.DrawImage(originalImage, new Rectangle(0, 0, croppedBitmap.Width, croppedBitmap.Height), cropArea, GraphicsUnit.Pixel);


                    }
                    // Save the cropped image
                    croppedBitmap.Save(imagePath);
                }
            }
        }

        /// <summary>
        /// Removes background from an image using Imagga API
        /// </summary>
        /// <param name="inputImagePath">Path to the input image file</param>
        /// <param name="outputImagePath">Path where the background-removed image will be saved</param>
        /// <param name="hasIssue">Output parameter indicating if there was an issue with the API call</param>
        /// <returns>True if background removal was successful, false otherwise</returns>
        public static bool RemoveBackground(string inputImagePath, string outputImagePath, out bool hasIssue)
        {
            hasIssue = false;
            //string apiKey = "acc_583b6379a109e44";
            //string apiSecret = "c2c79c086317f4beafc22766f9fdc04b";

            string basicAuthValue = Convert.ToBase64String(Encoding.UTF8.GetBytes(String.Format("{0}:{1}", _apiKey, _apiSecret)));

            var client = new RestClient("https://api.imagga.com/v2/remove-background");
            client.Timeout = -1;

            var request = new RestRequest(Method.POST);
            request.AddHeader("Authorization", String.Format("Basic {0}", basicAuthValue));
            request.AddFile("image", inputImagePath);

            try
            {
                IRestResponse response = client.Execute(request);

                if (response.IsSuccessful)
                {
                    // The API returns a binary PNG file directly
                    byte[] imageBytes = response.RawBytes;

                    if (imageBytes != null && imageBytes.Length > 0)
                    {
                        // Ensure the output directory exists
                        string outputDirectory = Path.GetDirectoryName(outputImagePath);
                        if (!Directory.Exists(outputDirectory))
                        {
                            Directory.CreateDirectory(outputDirectory);
                        }

                        File.WriteAllBytes(outputImagePath, imageBytes);
                        return true;
                    }
                    else
                    {
                        hasIssue = true;
                        Console.WriteLine("No image data found in API response");
                        return false;
                    }
                }
                else
                {
                    hasIssue = true;
                    Console.WriteLine($"HTTP Error: {response.StatusCode} - {response.ErrorMessage}");
                    if (!string.IsNullOrEmpty(response.Content))
                    {
                        Console.WriteLine($"Response content: {response.Content}");
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                hasIssue = true;
                Console.WriteLine($"Exception during background removal: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Removes background from an image using Imagga API with additional parameters
        /// </summary>
        /// <param name="inputImagePath">Path to the input image file</param>
        /// <param name="outputImagePath">Path where the background-removed image will be saved</param>
        /// <param name="hasIssue">Output parameter indicating if there was an issue with the API call</param>
        /// <param name="addShadow">Whether to add shadow to the result (default: false)</param>
        /// <param name="semiTransparency">Whether to use semi-transparency (default: false)</param>
        /// <param name="bgColor">Background color in hex format (e.g., "ffffff" for white)</param>
        /// <returns>True if background removal was successful, false otherwise</returns>
        public static bool RemoveBackground(string inputImagePath, string outputImagePath, out bool hasIssue,
            bool addShadow = false, bool semiTransparency = false, string bgColor = null)
        {
            hasIssue = false;
            string apiKey = "acc_583b6379a109e44";
            string apiSecret = "c2c79c086317f4beafc22766f9fdc04b";

            string basicAuthValue = Convert.ToBase64String(Encoding.UTF8.GetBytes(String.Format("{0}:{1}", apiKey, apiSecret)));

            var client = new RestClient("https://api.imagga.com/v2/remove-background");
            client.Timeout = -1;

            var request = new RestRequest(Method.POST);
            request.AddHeader("Authorization", String.Format("Basic {0}", basicAuthValue));
            request.AddFile("image", inputImagePath);

            // Add optional parameters
            if (addShadow)
            {
                request.AddParameter("add_shadow", "1");
            }

            if (semiTransparency)
            {
                request.AddParameter("semi_transparency", "1");
            }

            if (!string.IsNullOrEmpty(bgColor))
            {
                request.AddParameter("bg_color", bgColor);
            }

            try
            {
                IRestResponse response = client.Execute(request);

                if (response.IsSuccessful)
                {
                    // The API returns a binary PNG file directly
                    byte[] imageBytes = response.RawBytes;

                    if (imageBytes != null && imageBytes.Length > 0)
                    {
                        // Ensure the output directory exists
                        string outputDirectory = Path.GetDirectoryName(outputImagePath);
                        if (!Directory.Exists(outputDirectory))
                        {
                            Directory.CreateDirectory(outputDirectory);
                        }

                        File.WriteAllBytes(outputImagePath, imageBytes);
                        return true;
                    }
                    else
                    {
                        hasIssue = true;
                        Console.WriteLine("No image data found in API response");
                        return false;
                    }
                }
                else
                {
                    hasIssue = true;
                    Console.WriteLine($"HTTP Error: {response.StatusCode} - {response.ErrorMessage}");
                    if (!string.IsNullOrEmpty(response.Content))
                    {
                        Console.WriteLine($"Response content: {response.Content}");
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                hasIssue = true;
                Console.WriteLine($"Exception during background removal: {ex.Message}");
                return false;
            }
        }
    }
}
