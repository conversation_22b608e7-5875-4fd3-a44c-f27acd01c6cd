﻿using Storeya.Core.Entities;
using Storeya.Core.Models;
using Storeya.Core.Models.CRM;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Storeya.Core.Models.Launcher;

namespace Storeya.Core.Helpers
{
    public class LauncherHelper : IDisposable
    {

        private StoreYaEntities db { get; set; }

        public LauncherHelper()
        {
            db = new StoreYaEntities();
        }
        public List<LauncherCommand> GetCommands()
        {
            return db.LauncherCommands.ToList();
        }

        public LauncherCommand GetCommand(int ID)
        {
            return db.LauncherCommands.SingleOrDefault(c => c.ID == ID);
        }

        public List<LauncherCommandType> GetCommandTypes()
        {
            return db.LauncherCommandTypes.ToList();
        }
        public LauncherCommandType GetCommandType(int ID)
        {
            return db.LauncherCommandTypes.SingleOrDefault(c => c.ID == ID);
        }
        public LauncherCommandType GetCommandType(string name)
        {
            if (int.TryParse(name, out int uid))
            {
                return db.LauncherCommandTypes.SingleOrDefault(c => c.UID == uid);
            }
            return db.LauncherCommandTypes.SingleOrDefault(c => c.Name.ToUpper() == name.ToUpper());
        }

        public string GetCommandThreadName(int commandId)
        {
            var com = GetCommand(commandId);
            var t = db.LauncherCommandTypes.SingleOrDefault(c => c.UID == com.UID);
            if (t == null)
            {
                return null;
            }
            return t.Thread;
        }

        public Dictionary<string, string> GetCommandTypesList()
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            var types = db.LauncherCommandTypes;
            if (types != null)
            {
                foreach (var item in types)
                {
                    dic.Add(item.UID.ToString(), item.Name);
                }
            }
            return dic;
        }
        public List<LauncherTriggeredCommand> GetTriggeredCommands()
        {
            return db.LauncherTriggeredCommands.ToList();
        }
        public List<LauncherTriggeredCommand> GetTriggeredCommands(Launcher.COMMAND_TYPE commandType = Launcher.COMMAND_TYPE.TRIGGERD)
        {
            var data = (from t in db.LauncherTriggeredCommands
                        join c in db.LauncherCommands on t.CommandID equals c.ID
                        where c.CommandType == (int)commandType
                        select t
                           ).ToList();
            return data;
        }
        public List<LauncherTriggeredCommand> GetTriggeredCommands(int typeUid)
        {
            var data = (from t in db.LauncherTriggeredCommands
                        join c in db.LauncherCommands on t.CommandID equals c.ID
                        where c.UID == typeUid && c.Status == (int)Launcher.COMMAND_STATUS.ENABLE
                        select t
                           ).ToList();
            return data;
        }
        public List<LauncherTriggeredCommand> GetTriggeredCommands(List<Launcher.TRIGGERED_COMMAND_STATUS> status)
        {
            //pasha - added take last 100
            return db.LauncherTriggeredCommands.Where(t => status.Contains((Launcher.TRIGGERED_COMMAND_STATUS)t.Status)).OrderByDescending(c => c.ID).Take(100).ToList();
        }

        public IOrderedEnumerable<LauncherTriggeredCommand> GetScheduledTriggeredCommandsToRun()
        {
            DateTime now = DateTime.Now;
            List<LauncherTriggeredCommand> tCommands = new List<LauncherTriggeredCommand>();
            IEnumerable<LauncherCommand> commands = db.LauncherCommands.Where(c => c.CommandType == (int)Launcher.COMMAND_TYPE.SCHEDULER && c.Status == (int)Launcher.COMMAND_STATUS.ENABLE);
            foreach (var command in commands)
            {
                Launcher.Scheduler schedulerObject = command.SchedulerJson.FromJson<Launcher.Scheduler>();
                Launcher.SCHEDULER_INTERVAL interval = (Launcher.SCHEDULER_INTERVAL)Enum.Parse(typeof(Launcher.SCHEDULER_INTERVAL), schedulerObject.Interval.ToUpper());
                if (!string.IsNullOrEmpty(schedulerObject.TimeZoneName))
                {
                    now = DateTime.Now;
                    string log = $"Now:{now}";
                    now = now.GetDateTimeInTimeZone(schedulerObject.TimeZoneName);
                    Console.WriteLine($"{log}, Timezone:{schedulerObject.TimeZoneName}:{now}");
                }
                switch (interval)
                {
                    case Launcher.SCHEDULER_INTERVAL.DAILY:
                        if (schedulerObject.TimeSpan.Any(t => t.Hours == now.Hour && t.Minutes == now.Minute))
                        {
                            tCommands.AddRange(ExecuteSchedulerLauncherTriggeredCommands(command));
                        }
                        break;
                    case Launcher.SCHEDULER_INTERVAL.WEEKLY:
                        if (schedulerObject.DayToRun.Value == now.DayOfWeek.GetHashCode() && schedulerObject.HourToRun.Value.Hours == now.Hour && schedulerObject.HourToRun.Value.Minutes == now.Minute)
                        {
                            tCommands.AddRange(ExecuteSchedulerLauncherTriggeredCommands(command));
                        }
                        break;
                    case Launcher.SCHEDULER_INTERVAL.MONTHLY:

                        if (schedulerObject.DayToRun.Value == now.Day && schedulerObject.HourToRun.Value.Hours == now.Hour && schedulerObject.HourToRun.Value.Minutes == now.Minute)
                        {
                            tCommands.AddRange(ExecuteSchedulerLauncherTriggeredCommands(command));
                        }
                        break;
                    default:
                        break;
                }
            }
            db.SaveChanges();
            return tCommands.OrderBy(c => c.Inserted);
        }

        private List<LauncherTriggeredCommand> ExecuteSchedulerLauncherTriggeredCommands(LauncherCommand command)
        {
            List<LauncherTriggeredCommand> tCommands = new List<LauncherTriggeredCommand>();
            foreach (var t in db.LauncherTriggeredCommands.Where(t => t.CommandID == command.ID))
            {
                if (t.Status != (int)Launcher.TRIGGERED_COMMAND_STATUS.IN_QUEUE || t.Status != (int)Launcher.TRIGGERED_COMMAND_STATUS.IN_PROGRESS)
                {
                    if (t.ExecuteStarted.HasValue)
                    {
                        if ((DateTime.Now - t.ExecuteStarted.Value).TotalMinutes > 1)
                        {
                            UpdateSchedulerTriggerCommand(t, GetNextRunAt(command.SchedulerJson));
                            tCommands.Add(t);
                        }
                    }
                    else
                    {
                        UpdateSchedulerTriggerCommand(t, GetNextRunAt(command.SchedulerJson));
                        tCommands.Add(t);
                    }
                }
            }
            return tCommands;
        }

        private void UpdateSchedulerTriggerCommand(LauncherTriggeredCommand launcherTriggeredCommand, DateTime nextRunAt)
        {
            var command = db.LauncherTriggeredCommands.SingleOrDefault(testc => testc.ID == launcherTriggeredCommand.ID);
            command.Updated = DateTime.Now;
            command.Status = Launcher.TRIGGERED_COMMAND_STATUS.IN_QUEUE.GetHashCode();
            command.NextRunAt = nextRunAt;

        }
        public IOrderedEnumerable<LauncherTriggeredCommand> GetTriggeredCommandsToRun()
        {
            IEnumerable<LauncherTriggeredCommand> commands = db.LauncherTriggeredCommands.Where(t => t.Status == (int)Launcher.TRIGGERED_COMMAND_STATUS.INSERTED || t.Status == (int)Launcher.TRIGGERED_COMMAND_STATUS.IN_QUEUE);
            foreach (var command in commands)
            {
                command.Updated = DateTime.Now;
                command.Status = Launcher.TRIGGERED_COMMAND_STATUS.IN_QUEUE.GetHashCode();
            }
            db.SaveChanges();
            return commands.OrderBy(c => c.Inserted);
        }

        public LauncherTriggeredCommand GetTriggeredCommand(int ID)
        {
            return db.LauncherTriggeredCommands.SingleOrDefault(c => c.ID == ID);
        }
        public LauncherTriggeredCommand CreateSchedulerTriggeredCommand(LauncherCommand launcherCommands)
        {
            if (launcherCommands == null)
            {
                return null;
            }
            string userName = "Launcher";
            string userEmail = "<EMAIL>";
            LauncherTriggeredCommand launcherTriggeredCommand = new LauncherTriggeredCommand()
            {
                CommandID = launcherCommands.ID,
                CommandName = launcherCommands.Name,
                Args = launcherCommands.ArgumentFormat,
                EXEPath = launcherCommands.EXEPath,
                Inserted = DateTime.Now,
                Updated = DateTime.Now,
                Status = Launcher.TRIGGERED_COMMAND_STATUS.SCHEDULED.GetHashCode(),
                InitiatedBy = userName,
                InitiatedByEmail = userEmail,
                NextRunAt = GetNextRunAt(launcherCommands.SchedulerJson)
            };
            db.LauncherTriggeredCommands.Add(launcherTriggeredCommand);
            db.SaveChanges();

            return launcherTriggeredCommand;
        }
        public DateTime GetNextRunAt(string schedulerJson)
        {
            Launcher.Scheduler scheduler = schedulerJson.FromJson<Launcher.Scheduler>();
            TimeSpan now = new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, 0);
            DateTime dNow = DateTime.Now;
            if (!string.IsNullOrEmpty(scheduler.TimeZoneName))
            {
                dNow = dNow.GetDateTimeInTimeZone(scheduler.TimeZoneName);
            }
            Launcher.SCHEDULER_INTERVAL interval = (Launcher.SCHEDULER_INTERVAL)Enum.Parse(typeof(Launcher.SCHEDULER_INTERVAL), scheduler.Interval.ToUpper());
            switch (interval)
            {
                case Launcher.SCHEDULER_INTERVAL.DAILY:
                    scheduler.TimeSpan.Sort();
                    var spans = scheduler.TimeSpan;
                    TimeSpan tt = spans[0];
                    for (int i = 0; i < spans.Count; i++)
                    {
                        if (i + 1 == spans.Count)
                        {
                            tt = spans[0];
                            break;
                        }
                        //  Console.WriteLine(now + " # " + spans[i] + " @ " + spans[i + 1]);
                        if (IsInRange(now, spans[i], spans[i + 1]))
                        {
                            tt = spans[i + 1];
                            break;
                        }

                    }
                    int day = 0;

                    if (now.Hours - tt.Hours > 12)
                    {
                        day = 1;
                    }
                    DateTime next = new DateTime(dNow.Year, dNow.Month, dNow.Day, tt.Hours, tt.Minutes, 0);
                    //Console.WriteLine(next.AddDays(day));
                    return next.AddDays(day);
                case Launcher.SCHEDULER_INTERVAL.WEEKLY:
                    DateTime nextWeek = new DateTime(dNow.Year, dNow.Month, dNow.Day, scheduler.HourToRun.Value.Hours, scheduler.HourToRun.Value.Minutes, 0);
                    if (nextWeek > dNow)
                    {
                        return nextWeek;
                    }
                    return nextWeek.AddDays(7);
                case Launcher.SCHEDULER_INTERVAL.MONTHLY:
                    DateTime nextMounth = new DateTime(dNow.Year, dNow.Month, scheduler.DayToRun.Value, scheduler.HourToRun.Value.Hours, scheduler.HourToRun.Value.Minutes, 0);
                    if (nextMounth > dNow)
                    {
                        return nextMounth;
                    }
                    return nextMounth.AddMonths(1);

            }
            return dNow;
            // new LauncherHelper().GetNextRunAt("{'Interval': 'DAILY','TimeSpan': ['11:00:00','17:01:00','17:03:00',  '23:30:00', '10:00:00'   ]}");              
        }

        private bool IsInRange(TimeSpan num, TimeSpan min, TimeSpan max)
        {

            if (num >= min && num < max)
            {
                //  Console.WriteLine(string.Format("mun:{0} min:{1} max:{2} - True", num, min, max));
                return true;
            }
            return false;
        }
        public LauncherTriggeredCommand CreateTriggeredCommand(User user, string commandType, bool distinct = false, params string[] args)
        {
            var cType = GetCommandType(commandType);
            if (cType == null)
            {
                throw new Exception("Cannot find command Type with name: " + commandType);
            }
            LauncherCommand launcherCommands = db.LauncherCommands.SingleOrDefault(c => c.UID == cType.UID && c.CommandType != (int)Launcher.COMMAND_TYPE.SCHEDULER);
            if (launcherCommands == null)
            {
                throw new Exception("Cannot find command with Type: " + commandType);
            }
            string userName = "Launcher";
            string userEmail = "<EMAIL>";
            if (user != null)
            {
                if (launcherCommands.Permissions.HasValue && launcherCommands.Permissions.Value == Launcher.COMMAND_PERMISSIONS.DEVONLY.GetHashCode())
                {
                    bool devUser = StoreYaEmployeeHelper.IsDeveloper(user);
                    if (!devUser)
                    {
                        throw new Exception("You are Unauthorized to exeute this action " + launcherCommands.Name);
                    }
                }
                userName = user.Name ?? user.ID.ToString();
                userEmail = user.Email;
            }

            string argsToRun = launcherCommands.ArgumentFormat;
            if (args != null)
            {
                argsToRun = string.Format(launcherCommands.ArgumentFormat, args);
            }
            if (distinct)
            {
                var triggers = db.LauncherTriggeredCommands.Where(t => t.CommandID == launcherCommands.ID && t.Args == argsToRun && (t.Status == (int)Launcher.TRIGGERED_COMMAND_STATUS.INSERTED || t.Status == (int)Launcher.TRIGGERED_COMMAND_STATUS.IN_QUEUE || t.Status == (int)Launcher.TRIGGERED_COMMAND_STATUS.IN_PROGRESS)).OrderByDescending(c => c.Inserted).ToList();
                if (triggers != null && triggers.Count() > 0)
                {
                    return triggers.Last();
                }
            }

            LauncherTriggeredCommand launcherTriggeredCommand = new LauncherTriggeredCommand()
            {
                CommandID = launcherCommands.ID,
                CommandName = launcherCommands.Name,
                Args = argsToRun,
                EXEPath = launcherCommands.EXEPath,
                Inserted = DateTime.Now,
                Updated = DateTime.Now,
                Status = Launcher.TRIGGERED_COMMAND_STATUS.INSERTED.GetHashCode(),
                InitiatedBy = userName,
                InitiatedByEmail = userEmail,
            };
            db.LauncherTriggeredCommands.Add(launcherTriggeredCommand);
            db.SaveChanges();

            return launcherTriggeredCommand;
        }

        public static void EndFailedLogTriggeredCommand(LauncherTriggeredCommand launcherTriggeredCommand, Exception ex)
        {
            EndLogTriggeredCommand(launcherTriggeredCommand, Launcher.TRIGGERED_COMMAND_STATUS.FAILED, ex.ToString());
        }
        public static void EndAjaxTriggeredCommand(int triggerId, string results, Launcher.TRIGGERED_COMMAND_STATUS status = Launcher.TRIGGERED_COMMAND_STATUS.DONE)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                var trig = db.LauncherTriggeredCommands.SingleOrDefault(t => t.ID == triggerId);
                trig.Results = results;
                trig.Status = status.GetHashCode();
                trig.ExecuteEnded = DateTime.Now;
                trig.Updated = DateTime.Now;
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed to End Ajax Trigger with Id:" + triggerId, ex);
            }
        }
        public static void EndLogTriggeredCommand(LauncherTriggeredCommand launcherTriggeredCommand, Launcher.TRIGGERED_COMMAND_STATUS status = Launcher.TRIGGERED_COMMAND_STATUS.DONE, string results = "")
        {
            try
            {
                if (launcherTriggeredCommand == null)
                {
                    //Console.WriteLine("Failed to End Log Trigger launcherTriggeredCommand is null");
                    return;
                }
                var db = DataHelper.GetStoreYaEntities();

                var trig = db.LauncherTriggeredCommands.SingleOrDefault(t => t.ID == launcherTriggeredCommand.ID);
                if (trig.Status != Launcher.TRIGGERED_COMMAND_STATUS.FAILED.GetHashCode())
                {
                    trig.Results = results;
                    trig.Status = status.GetHashCode();
                    trig.ExecuteEnded = DateTime.Now;
                    trig.Updated = DateTime.Now;
                    db.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed to End Log Trigger with Id:" + launcherTriggeredCommand.ID, ex);
            }
        }

        public static LauncherTriggeredCommand StartLogTriggeredCommand(string name, string[] args)
        {
            try
            {
                var db = new StoreYaEntities();
                var cType = db.LauncherCommandTypes.SingleOrDefault(c => c.UID == 1000);
                if (cType == null)
                {
                    Console.WriteLine("Cannot find command Type with UID 1000.");
                    return null;
                }
                LauncherCommand launcherCommands = db.LauncherCommands.SingleOrDefault(c => c.Name.ToUpper() == name.ToUpper() && c.CommandType == (int)Launcher.COMMAND_TYPE.LOG && c.Status == (int)Launcher.COMMAND_STATUS.ENABLE);
                if (launcherCommands == null)
                {
                    Console.WriteLine("Cannot find command with Name: " + name);
                    return null;
                }
                string argmants = string.Empty;
                if (args != null && args.Length > 0)
                {
                    argmants = string.Join(",", args);
                    if (argmants.ToLower().Contains("initbylauncher"))
                    {
                        return null;
                    }
                }
                string userName = "Logger";
                try
                {
                    userName = System.Security.Principal.WindowsIdentity.GetCurrent().Name;
                    if (!userName.ToLower().Contains("applicationuser"))
                    {
                        return null;
                    }
                }
                catch (Exception)
                {

                }

                if (launcherCommands.ArgumentFormat == "overwrite")
                {
                    LauncherTriggeredCommand launcherExistsTriggeredCommand = db.LauncherTriggeredCommands.Where(t => t.CommandID == launcherCommands.ID).OrderByDescending(c => c.ID).FirstOrDefault();
                    if (launcherExistsTriggeredCommand != null)
                    {
                        launcherExistsTriggeredCommand.Status = Launcher.TRIGGERED_COMMAND_STATUS.IN_PROGRESS.GetHashCode();
                        launcherExistsTriggeredCommand.Args = argmants;
                        launcherExistsTriggeredCommand.ExecuteStarted = DateTime.Now;
                        launcherExistsTriggeredCommand.Updated = DateTime.Now;
                        launcherExistsTriggeredCommand.InitiatedBy = userName;
                        db.SaveChanges();
                        return launcherExistsTriggeredCommand;
                    }
                }
                string userEmail = "<EMAIL>";
                LauncherTriggeredCommand launcherTriggeredCommand = new LauncherTriggeredCommand()
                {
                    CommandID = launcherCommands.ID,
                    CommandName = launcherCommands.Name,
                    Args = argmants,
                    EXEPath = launcherCommands.EXEPath,
                    Inserted = DateTime.Now,
                    Updated = DateTime.Now,
                    Status = Launcher.TRIGGERED_COMMAND_STATUS.IN_PROGRESS.GetHashCode(),
                    InitiatedBy = userName,
                    InitiatedByEmail = userEmail,
                    ExecuteStarted = DateTime.Now
                };
                db.LauncherTriggeredCommands.Add(launcherTriggeredCommand);
                db.SaveChanges();
                return launcherTriggeredCommand;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed to Created Log Trigger with name:" + name + " Exception:" + ex.ToString());
            }
            return null;
        }

        public bool SetTriggeredCommandStatus(int id, Launcher.TRIGGERED_COMMAND_STATUS status)
        {

            LauncherTriggeredCommand triggeredCommand = db.LauncherTriggeredCommands.SingleOrDefault(c => c.ID == id);
            if (triggeredCommand == null)
            {
                return false;
            }
            triggeredCommand.Updated = DateTime.Now;
            triggeredCommand.Status = status.GetHashCode();

            db.SaveChanges();

            return true;
        }

        public LauncherTriggeredCommand StartTriggeredCommand(int id)
        {

            LauncherTriggeredCommand triggeredCommand = db.LauncherTriggeredCommands.SingleOrDefault(c => c.ID == id);
            if (triggeredCommand == null)
            {
                return null;
            }
            triggeredCommand.Updated = DateTime.Now;
            triggeredCommand.ExecuteStarted = DateTime.Now;
            triggeredCommand.Status = Launcher.TRIGGERED_COMMAND_STATUS.IN_PROGRESS.GetHashCode();
            db.SaveChanges();

            return triggeredCommand;
        }

        public LauncherTriggeredCommand EndTriggeredCommand(int id, string results, bool failed = false)
        {
            LauncherTriggeredCommand triggeredCommand = db.LauncherTriggeredCommands.SingleOrDefault(c => c.ID == id);
            LauncherCommand launcherCommand = GetCommand(triggeredCommand.CommandID);
            if (triggeredCommand == null)
            {
                return null;
            }
            triggeredCommand.Updated = DateTime.Now;
            triggeredCommand.ExecuteEnded = DateTime.Now;
            if (failed)
            {
                triggeredCommand.Status = Launcher.TRIGGERED_COMMAND_STATUS.FAILED.GetHashCode();
            }
            else
            {
                if (triggeredCommand.InitiatedBy == "Launcher" || triggeredCommand.InitiatedBy == "Dev")
                {
                    triggeredCommand.Status = Launcher.TRIGGERED_COMMAND_STATUS.SCHEDULED.GetHashCode();
                }
                else
                {
                    triggeredCommand.Status = Launcher.TRIGGERED_COMMAND_STATUS.DONE.GetHashCode();
                }
            }
            if (launcherCommand.CommandType != Launcher.COMMAND_TYPE.AJAX.GetHashCode())
            {
                triggeredCommand.Results = results;
            }
            db.SaveChanges();
            if (launcherCommand.ResultType == Launcher.COMMAND_RESULTS_TYPE.ASYNC.GetHashCode())
            {
                if (StoreYaEmployeeHelper.IsStroreYaUser(triggeredCommand.InitiatedByEmail) || triggeredCommand.InitiatedByEmail == EmailHelper.DEV_FB_EMAIL)
                {
                    try
                    {
                        string body = string.Format("<b>Launcher has completed to execute</b><br/>{0}", triggeredCommand.ToHtmlTable());
                        EmailHelper.SendEmail(triggeredCommand.InitiatedByEmail, string.Format("Launcher has completed to execute :{0}-{1}", triggeredCommand.CommandName, triggeredCommand.CommandID), body);
                    }
                    catch (Exception ex)
                    {
                        ConsoleAppHelper.WriteError($"Failed To email to:{triggeredCommand.InitiatedByEmail} on EndTriggeredCommand", ex, 0);
                    }
                }
            }
            if (triggeredCommand.Status == (int)Launcher.TRIGGERED_COMMAND_STATUS.FAILED)
            {
                string body = string.Format("<b>Launcher has failed to execute</b><br/>{0}", triggeredCommand.ToHtmlTable());
                EmailHelper.SendEmail(triggeredCommand.InitiatedByEmail, string.Format("Launcher has failed to execute :{0}-{1}", triggeredCommand.CommandName, triggeredCommand.CommandID), body);

            }
            return triggeredCommand;
        }
        public LauncherCommandType ManageCommandType(LauncherCommandType launcherCommandType)
        {
            LauncherCommandType dbLauncherCommandType = null;
            if (launcherCommandType.ID == 0)
            {
                dbLauncherCommandType = db.LauncherCommandTypes.SingleOrDefault(c => c.Name.ToUpper().Equals(launcherCommandType.Name.ToUpper()) || c.UID == launcherCommandType.UID);
            }
            else
            {
                dbLauncherCommandType = db.LauncherCommandTypes.SingleOrDefault(c => c.ID == launcherCommandType.ID);
            }
            if (dbLauncherCommandType == null)
            {
                LauncherCommandType _launcherCommandType = new LauncherCommandType()
                {
                    Inserted = DateTime.Now,
                    UID = launcherCommandType.UID,
                    Name = launcherCommandType.Name,
                    Thread = launcherCommandType.Thread,

                };
                db.LauncherCommandTypes.Add(_launcherCommandType);
                db.SaveChanges();
                return _launcherCommandType;
            }
            else
            {
                dbLauncherCommandType.Name = launcherCommandType.Name;
                dbLauncherCommandType.UID = launcherCommandType.UID;
                dbLauncherCommandType.Thread = launcherCommandType.Thread;
                db.SaveChanges();
                return dbLauncherCommandType;
            }
        }
        public LauncherCommand ManageCommand(LauncherCommand launcherCommand)
        {
            LauncherCommand dbLauncherCommand = null;
            if (launcherCommand.CommandType.HasValue)
            {
                switch ((Launcher.COMMAND_TYPE)launcherCommand.CommandType)
                {
                    case Launcher.COMMAND_TYPE.AJAX:
                    case Launcher.COMMAND_TYPE.TRIGGERD:
                    case Launcher.COMMAND_TYPE.LOG:
                        dbLauncherCommand = db.LauncherCommands.SingleOrDefault(c => c.Name.ToUpper().Equals(launcherCommand.Name.ToUpper()) && c.UID == launcherCommand.UID);
                        break;
                    case Launcher.COMMAND_TYPE.SCHEDULER:
                        dbLauncherCommand = db.LauncherCommands.SingleOrDefault(c => c.Name.ToUpper().Equals(launcherCommand.Name.ToUpper()));
                        break;
                }
            }
            else
            {
                dbLauncherCommand = db.LauncherCommands.SingleOrDefault(c => c.Name.ToUpper().Equals(launcherCommand.Name.ToUpper()) && c.UID == launcherCommand.UID);
            }

            if (dbLauncherCommand == null)
            {
                LauncherCommand _launcherCommand = new LauncherCommand()
                {
                    SchedulerJson = launcherCommand.SchedulerJson,
                    CommandType = launcherCommand.CommandType,
                    EXEPath = launcherCommand.EXEPath,
                    Permissions = launcherCommand.Permissions,
                    Name = launcherCommand.Name,
                    ArgumentFormat = launcherCommand.ArgumentFormat,
                    Inserted = DateTime.Now,
                    ResultType = launcherCommand.ResultType ?? Launcher.COMMAND_RESULTS_TYPE.ASYNC.GetHashCode(),
                    Status = Launcher.COMMAND_STATUS.ENABLE.GetHashCode(),
                    RunningLimitAlertInMinutes = launcherCommand.RunningLimitAlertInMinutes,
                    Updated = DateTime.Now,
                    UID = launcherCommand.UID
                };
                db.LauncherCommands.Add(_launcherCommand);
                db.SaveChanges();
                if (launcherCommand.CommandType == Launcher.COMMAND_TYPE.SCHEDULER.GetHashCode())
                {
                    InitScheduledCommand(_launcherCommand);
                }
                return _launcherCommand;
            }
            else
            {
                dbLauncherCommand.Updated = DateTime.Now;
                dbLauncherCommand.Status = launcherCommand.Status;
                dbLauncherCommand.ArgumentFormat = launcherCommand.ArgumentFormat;
                dbLauncherCommand.EXEPath = launcherCommand.EXEPath;
                dbLauncherCommand.Name = launcherCommand.Name;
                dbLauncherCommand.Permissions = launcherCommand.Permissions;
                dbLauncherCommand.ResultType = launcherCommand.ResultType;
                dbLauncherCommand.UID = launcherCommand.UID;
                dbLauncherCommand.CommandType = launcherCommand.CommandType;
                dbLauncherCommand.SchedulerJson = launcherCommand.SchedulerJson;
                dbLauncherCommand.RunningLimitAlertInMinutes = launcherCommand.RunningLimitAlertInMinutes;

                db.SaveChanges();
                if (launcherCommand.CommandType == Launcher.COMMAND_TYPE.SCHEDULER.GetHashCode())
                {
                    InitScheduledCommand(dbLauncherCommand);
                }
                return dbLauncherCommand;
            }
        }
        public void InitScheduledCommand(LauncherCommand launcherCommands)
        {
            var tC = db.LauncherTriggeredCommands.SingleOrDefault(c => c.CommandID == (int)launcherCommands.ID && c.CommandName.ToUpper() == launcherCommands.Name.ToUpper());
            if (tC == null)
            {
                new LauncherHelper().CreateSchedulerTriggeredCommand(launcherCommands);
            }
        }

        public dynamic GetNoneFinishedCommands(int minutesFromStart = 60)
        {


            DateTime now = DateTime.Now;
            var data = (from t in db.LauncherTriggeredCommands
                        join c in db.LauncherCommands on t.CommandID equals c.ID
                        where t.Status != (int)Launcher.TRIGGERED_COMMAND_STATUS.DONE
                        && t.ExecuteEnded == null && t.ExecuteStarted.HasValue
                        && (c.RunningLimitAlertInMinutes.HasValue ? DbFunctions.AddMinutes(t.ExecuteStarted, c.RunningLimitAlertInMinutes.Value) < now : DbFunctions.AddMinutes(t.ExecuteStarted, minutesFromStart) < now)

                        select new { ID = t.ID, t.CommandName, t.ExecuteStarted, t.Args, Limit = c.RunningLimitAlertInMinutes.HasValue ? c.RunningLimitAlertInMinutes.Value : minutesFromStart, Expected = (c.RunningLimitAlertInMinutes.HasValue ? DbFunctions.AddMinutes(t.ExecuteStarted, c.RunningLimitAlertInMinutes.Value) : DbFunctions.AddMinutes(t.ExecuteStarted, minutesFromStart)) }
                           ).ToList();
            Console.WriteLine($"Total None Finished Commands :{data.Count}");
            return data;
        }
        public static void Report(int minutesFromStart = 20)
        {
            try
            {
                LauncherHelper launcherHelper = new LauncherHelper();
                var list = launcherHelper.GetNoneFinishedCommands(minutesFromStart);
                StringBuilder sb = new StringBuilder();
                foreach (var item in list)
                {
                    sb.AppendFormat("StartTime:{2} <a href='https://bo.storeya.com/Launcher/Results?id={0}'>{1}</a> Args:{3} Expected to End At:{4} - Minutes To Alert:{5}<br/>", item.ID, item.CommandName, item.ExecuteStarted, item.Args, item.Expected, item.Limit);
                }
                if (!string.IsNullOrEmpty(sb.ToString()))
                {
                    sb.Insert(0, $"Total open tasks :{list.Count} checked at:{DateTime.Now} <br/>");
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Launcher monitor - {list.Count} Tasks weren't finished.", sb.ToString());
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, "Launcher monitor Faild- Tasks that weren't finished.", ex.ToString());
            }
        }
        public static void InitDistinctCommand(int? shopId, string userName, string email, string commandType, params string[] args)
        {
            List<string> newArgs = new List<string>();
            if (shopId.HasValue)
            {
                newArgs.Add(shopId.ToString());
            }
            if (args != null)
            {
                newArgs.AddRange(args.ToList());
            }

            new LauncherHelper().CreateTriggeredCommand(new User() { Name = userName, Email = email }, commandType, true, newArgs.ToArray());
        }
        public static LauncherTriggeredCommand InitCommandAsDev(int? shopId, string commandType, params string[] args)
        {
            return InitCommand(shopId, new User() { Name = "System", Email = EmailHelper.DEV_FB_EMAIL }, commandType, args);
        }
        public static LauncherTriggeredCommand InitCommandWaitForStart(int? shopId, int userId, string commandType, params string[] args)
        {
            var db = DataHelper.GetStoreYaEntities();
            User user = db.Users.SingleOrDefault(s => s.ID == userId);
            LauncherTriggeredCommand tc = InitCommand(shopId, user, commandType, args);
            if (tc != null)
            {
                int count = 0;
                bool wait = true;
                while (wait)
                {
                    if (tc.Status == (int)Launcher.TRIGGERED_COMMAND_STATUS.IN_PROGRESS)
                    {
                        wait = false;
                        return tc;
                    }
                    tc = db.LauncherTriggeredCommands.Single(s => s.ID == tc.ID);
                    System.Threading.Thread.Sleep(1000);
                    count++;
                    if (count > 30)
                    {
                        wait = false;
                        return tc;
                    }
                }
            }
            return tc;
        }
        public static LauncherTriggeredCommand InitCommand(int? shopId, int userId, string commandType, params string[] args)
        {
            var db = DataHelper.GetStoreYaEntities();
            User user = db.Users.SingleOrDefault(s => s.ID == userId);

            return InitCommand(shopId, user, commandType, args);
        }
        public static LauncherTriggeredCommand InitCommand(int? shopId, User user, string commandType, params string[] args)
        {
            List<string> newArgs = new List<string>();
            if (shopId.HasValue)
            {
                newArgs.Add(shopId.ToString());
            }
            if (args != null)
            {
                newArgs.AddRange(args.ToList());
            }
            LauncherHelper launcherHelper = new LauncherHelper();
            LauncherTriggeredCommand launcherTriggeredCommand = launcherHelper.CreateTriggeredCommand(user, commandType, false, newArgs.ToArray());
            return launcherTriggeredCommand;
        }

        public bool IsAnyTriggeredCommandsFromSamePath(string exePath, Launcher.TRIGGERED_COMMAND_STATUS status = Launcher.TRIGGERED_COMMAND_STATUS.IN_PROGRESS)
        {
            var data = db.LauncherTriggeredCommands.Any(c => c.Status == (int)status && c.EXEPath.ToLower() == exePath.ToLower());
            return data;
        }

        public Dictionary<string, string> GetAllExePaths()
        {
            Dictionary<string, string> pairs = new Dictionary<string, string>();
            List<string> data = db.LauncherCommands.Where(c => c.Status == (int)COMMAND_STATUS.ENABLE).Select(c => c.EXEPath).Distinct().ToList();
            foreach (var item in data)
            {
                string directoryName = Path.GetDirectoryName(item);
                string fileName = Path.GetFileName(item);
                if (!pairs.ContainsKey(fileName))
                {
                    pairs.Add(fileName, directoryName);
                }
            }
            return pairs;
        }

        public void Dispose()
        {
            ((IDisposable)db).Dispose();
        }

        public void ExecuteAjaxSyncFromExe()
        {

        }
    }
}
