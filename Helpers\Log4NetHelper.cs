﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using log4net;
using log4net.Core;
using System.Text;
using System.Diagnostics;
using System.Text.RegularExpressions;
using System.IO;
using static log4net.Appender.RollingFileAppender;

namespace Storeya.Core.Helpers
{
    public class Log4NetLogger
    {

        private static ILog _logger;

        public static string BuildMessage(string message, int shopID)
        {
            if (shopID != 0)
            {
                return string.Format("S::{0};{1}", shopID.ToString(), message);
            }
            return message;
        }

        static Log4NetLogger()
        {
            //_logger = LogManager.GetLogger(this.GetType());
            _logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        }

        public static void Info(string message)
        {
            Info(message, null);
            //string cleanMessage = SetMeta(message);
            //_logger.Info(cleanMessage);
            //CleanMeta();
        }

        //main
        public static void Info(string message, Exception x)
        {
            string cleanMessage = SetMeta(message);
            _logger.Info(cleanMessage, x);
            CleanMeta();
        }

        public static void Info(string message, int shopID)
        {
            string messageWithMeta = Log4NetLogger.BuildMessage(message, shopID);
            Info(messageWithMeta, null);
        }

        public static void Info(string message, Exception x, int shopID)
        {
            string messageWithMeta = Log4NetLogger.BuildMessage(message, shopID);
            Info(messageWithMeta, x);
        }

        public static void InfoWithDB(string message, Exception x, int? shopID = null)
        {
            if (shopID.HasValue)
            {
                message = Log4NetLogger.BuildMessage(message, shopID.Value);
            }
            string cleanMessage = SetMeta(message);
            _logger.Info(cleanMessage, x);
            CleanMeta();
            WriteLogToDB(shopID, "0", "INFO", "Storeya.Core.Helpers.Log4NetLogger", cleanMessage, x);
        }
        public static void DebugWithDB(string message, Exception x, int? shopID = null)
        {
            if (shopID.HasValue)
            {
                message = Log4NetLogger.BuildMessage(message, shopID.Value);
            }
            string cleanMessage = SetMeta(message);
            _logger.Debug(cleanMessage, x);
            CleanMeta();
            WriteLogToDB(shopID, "0", "DEBUG", "Storeya.Core.Helpers.Log4NetLogger", cleanMessage, x);
        }
        public static void Debug(string message)
        {
            string cleanMessage = SetMeta(message);
            _logger.Debug(cleanMessage);
            CleanMeta();
        }
        public static void Debug(string message, int shopID)
        {
            string messageWithMeta = Log4NetLogger.BuildMessage(message, shopID);
            Debug(messageWithMeta);
        }
        public static void Error(string message)
        {
            Error(message, null);
        }

        public static void Error(string message, int shopID)
        {
            Error(message, null, shopID);
        }

        public static void Error(string message, Exception x, int shopID)
        {
            string messageWithMeta = Log4NetLogger.BuildMessage(message, shopID);
            Error(messageWithMeta, x);
        }

        public static void Error(string message, Exception x)
        {
            string cleanMessage = SetMeta(message);
            _logger.Error(cleanMessage, x);
            CleanMeta();
        }
        public static void ErrorWithDB(string message, Exception x, int? shopID = null)
        {
            if (shopID.HasValue)
            {
                message = Log4NetLogger.BuildMessage(message, shopID.Value);
            }
            string cleanMessage = SetMeta(message);
            _logger.Error(cleanMessage, x);
            CleanMeta();
            WriteLogToDB(shopID, "0", "ERROR", "Storeya.Core.Helpers.Log4NetLogger", cleanMessage, x);
        }
        //public static void Fatal(string message, Exception x)
        //{
        //    _logger.Fatal(message, x);
        //}

        private static string SetMeta(string message)
        {
            int shopID = 0;
            //bool metaIncluded = false;
            if (message.StartsWith("S::"))
            {
                int index = message.IndexOf(';');
                shopID = Convert.ToInt32(message.Substring(3, index - 3));
                message = message.Substring(index + 1).Trim();
                //metaIncluded = true;


            }

            log4net.GlobalContext.Properties["shopID"] = shopID;

            return message;
        }
        private static void CleanMeta()
        {
            log4net.GlobalContext.Properties["shopID"] = 0;
        }

        /// <summary>
        /// Creates a string of all property value pair in the provided object instance
        /// </summary>
        /// <param name="objectToGetStateOf"></param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public static string GetLogFor(object objectToGetStateOf)
        {
            if (objectToGetStateOf == null)
            {
                const string PARAMETER_NAME = "objectToGetStateOf";
                throw new ArgumentException(string.Format("Parameter {0} cannot be null", PARAMETER_NAME), PARAMETER_NAME);
            }
            var builder = new StringBuilder();

            foreach (var property in objectToGetStateOf.GetType().GetProperties())
            {
                object value = property.GetValue(objectToGetStateOf, null);

                builder.Append(property.Name)
                .Append(" = ")
                .Append((value ?? "null"))
                .AppendLine();
            }
            return builder.ToString();
        }


        public static string GetTabDelimited_OnlyHeader(object objectToGetStateOf)
        {
            if (objectToGetStateOf == null)
            {
                const string PARAMETER_NAME = "objectToGetStateOf";
                throw new ArgumentException(string.Format("Parameter {0} cannot be null", PARAMETER_NAME), PARAMETER_NAME);
            }
            var builder = new StringBuilder();

            foreach (var property in objectToGetStateOf.GetType().GetProperties())
            {
                object value = property.GetValue(objectToGetStateOf, null);

                builder.Append(property.Name).Append("\t");
            }
            return builder.ToString();
        }

        public static string GetTabDelimited(object objectToGetStateOf)
        {
            if (objectToGetStateOf == null)
            {
                const string PARAMETER_NAME = "objectToGetStateOf";
                throw new ArgumentException(string.Format("Parameter {0} cannot be null", PARAMETER_NAME), PARAMETER_NAME);
            }
            var builder = new StringBuilder();

            foreach (var property in objectToGetStateOf.GetType().GetProperties())
            {
                object value = property.GetValue(objectToGetStateOf, null);

                builder//.Append(property.Name)
                //.Append(" = ")
                .Append((value ?? "null"))
                .Append("\t");
            }
            return builder.ToString();
        }

        public enum SpecialShopIDs
        {
            ResetPassword = -10,
            LoginAndSignUp = -50,
            ShopifyWebHooks = -98,
            ShopifyPayments = -100,

            AppStorePayments = -102,
            PlimusTest = -103,
            AvangateIPN = -201,
            Updown = -300,

            Prestashop = -400,
            StoreYaApi = -501,
            APIToken = -500,
            TrafficBoosterImpressions = -600,
            GoogleAnalytics = -700,
            GoogleMCConnector = -701,
            Wix = -801,
            Ecwid = -900,
            ePages = -910,
            Zapier = -920,
            Lexity = -930,
            Yext = -940,
            BigCommerce = -950,
            Magento2 = -960,
            CriticalError = -1000,
            PaymentCriticalError = -1001,
            IPNSimulator = -1002,
            PlimusIPN = -1011,//-1010,
            SquareoAuth = -1111,
            RemoteLogInAs = -1112,
            NetooAuth = -1113,
            WixoAuth = -1146,
            FBLoginRemove = -1115,
            DataReportLog = -1116,
            PublicWebHook = -1117,
            PublicFBWebHook = -1118,
            PublicFBWebHookLeadGen = -1119,

            FacebookLeads = -1130,
            ProductDescriber = -1140,
            ZendescNoShopID = -1141,
            BudgetsWithAgreements = -1142,
            ImageLog = -1143,
            ChatGPTPPCPlugIn = -1144,
            EmailsSeries = -1145,
            WixoAuthOld = -1114,
            AISeoProductOptimizer = -1147,
            UsedOldAuthMethod = -1148,
            LogInIssue = -1149,
            EmbeddedApp = -1150,
            FSIPNCalls = -2000,
            TestShopDeleted = -2100,
            ShopifyRestApiCalls = -2101,
            FailedToGetAIShopInfo = -2102
        }
        public static void WriteLogToDB(int? shopID, string thread, string level, string logger, string message, Exception exception)
        {
            try
            {
                string pName = null;
                string ex = null;
                if (exception != null)
                {
                    ex = exception.ToString();
                }
                try
                {
                    pName = Process.GetCurrentProcess().ProcessName;
                    if (pName == "w3wp")
                    {
                        return;
                    }
                }
                catch { }

                message = string.Format("{0}: {1}", pName, message);
                var db = DataHelper.GetStoreYaEntities();
                db.proc_Insert_Log4Net(DateTime.Now, thread, level, logger, message, ex, shopID);
            }
            catch (Exception ex)
            {
                Error(message, ex);
            }

        }
        public class LogEntry
        {
            public DateTime Timestamp { get; set; }
            public string Message { get; set; }
            public string ShopID { get; set; }

            public double TimeTakenSecond { get; set; }

        }
        public static List<LogEntry> ParseLogFile(string filePath)
        {
            return ParseLog(File.ReadAllText(filePath));
        }
        public static List<LogEntry> ParseLog(string logText)
        {
            var logEntries = new List<LogEntry>();

            var lines = logText.Split('\n');

            foreach (var line in lines)
            {
                var match = Regex.Match(line, @"^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) (INFO.*)$");

                if (match.Success)
                {
                    var timestampStr = match.Groups[1].Value;
                    var message = match.Groups[2].Value;
                    if (DateTime.TryParseExact(timestampStr, "yyyy-MM-dd HH:mm:ss,fff", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out DateTime timestamp))
                    {

                        var logEntry = new LogEntry
                        {
                            Timestamp = timestamp,
                            Message = message.TrimEnd(new char[] { '\r', '\n' })
                        };

                        var shopIdMatch = Regex.Match(message, @"ShopID:(\d+)");
                        if (shopIdMatch.Success)
                        {
                            logEntry.ShopID = shopIdMatch.Groups[1].Value;
                        }

                        logEntries.Add(logEntry);
                    }
                }
            }
            logEntries = CalculateTimeTaken(logEntries);
            return logEntries;
        }


        public static List<LogEntry> CalculateTimeTaken(List<LogEntry> logEntries)
        {
            for (int i = 0; i < logEntries.Count - 1; i++)
            {
                TimeSpan timeTaken = logEntries[i + 1].Timestamp - logEntries[i].Timestamp;
                if (!string.IsNullOrEmpty(logEntries[i].ShopID))
                {
                    Console.WriteLine($"ShopID: {logEntries[i].ShopID}, Time taken for line, {i + 1}, {timeTaken.TotalSeconds} ,seconds");
                }
                else
                {
                    Console.WriteLine($"ShopID:0, Time taken for line, {i + 1}, {timeTaken.TotalSeconds}, seconds");
                }
                logEntries[i].TimeTakenSecond = timeTaken.TotalSeconds;
            }
            return logEntries;
        }
    }


}
