﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;

namespace Storeya.Core.Helpers
{
    public static class LogHelper1
    {
        public static void Error(string message, Exception ex)
        {
            //LogHelper.Error(message, ex, HttpContext.Current);
        }
        //public static void Error(string Message, Exception ex, HttpContext context)
        //{
        //    //try
        //    //{
        //    //    if (context == null)
        //    //    {
        //    //        ErrorLog.GetDefault(null).Log(new Error(new Exception("Non Http exception. Message: " + Message + " Exception: " + ex.ToString())));   
        //    //    }
        //    //    else
        //    //    {
        //    //        ErrorSignal.FromContext(context).Raise(new Elmah.ApplicationException(Message, ex));    
        //    //    }
                
        //    //}
        //    //catch (Exception)
        //    //{
        //    //    if (ex !=null)
        //    //    {
        //    //        ErrorLog.GetDefault(null).Log(new Error(ex));
        //    //    }
        //    //    else
        //    //    {
        //    //        ErrorLog.GetDefault(null).Log(new Error(new Exception("Exception during writing exception :-). Message: " + Message)));   
        //    //    }
        //    //    //ErrorLog.GetDefault(null).Log(new Error(logex));
        //    //}
        //}

        public static void Error(string message)
        {
            //LogHelper.Error(message, null);
        }

        public static void Error(Exception ex)
        {
            //LogHelper.Error(null, ex);
        }

        public static void Warning(string message)
        {
            //message = "Warning:" + message;

            //try
            //{
            //    ErrorSignal.FromCurrentContext().Raise(new Elmah.ApplicationException(message));
            //}
            //catch
            //{ }
        }

        /// <summary>
        /// Creates a string of all property value pair in the provided object instance
        /// </summary>
        /// <param name="objectToGetStateOf"></param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public static string GetLogFor(object objectToGetStateOf)
        {
            if (objectToGetStateOf == null)
            {
                const string PARAMETER_NAME = "objectToGetStateOf";
                throw new ArgumentException(string.Format("Parameter {0} cannot be null", PARAMETER_NAME), PARAMETER_NAME);
            }
            var builder = new StringBuilder();

            foreach (var property in objectToGetStateOf.GetType().GetProperties())
            {
                object value = property.GetValue(objectToGetStateOf, null);

                builder.Append(property.Name)
                .Append(" = ")
                .Append((value ?? "null"))
                .AppendLine();
            }
            return builder.ToString();
        }
    }
}