﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace Storeya.Core.Helpers
{
    public class LogInHelper
    {
        public static UserTokenData GetUserDataFromToken(string token)
        {
            UserTokenData userTokenData = new UserTokenData();
            userTokenData.ExpiredAt = DateTime.Now.AddDays(1);
            string tokenDecoded = token.Base64Decode();
            string e = EncryptionHelper.DecryptAES(tokenDecoded);
            userTokenData.Token = e;

            if (string.IsNullOrEmpty(e))
            {
                return null;
            }

            var a = e.Split('&');
            foreach (var item in a)
            {
                var i = item.Split('=');
                switch (i[0].ToLower())
                {
                    case "uid":
                        userTokenData.UserID = i[1];
                        //Account.Login(uid);
                        break;
                    case "shopid":
                        userTokenData.ShopID = Helper.GetIntOrNull(i[1]);
                        break;
                    case "returnurl":
                        userTokenData.ReturnUrl = item.Replace("returnurl=", "").Base64Decode();
                        break;
                    case "exp":
                        userTokenData.ExpiredAt = DateTime.FromBinary(long.Parse(i[1]));
                        break;
                    case "date":
                        userTokenData.DateFromToken = DateTime.FromBinary(long.Parse(i[1]));
                        break;
                    case "adminuserid":
                        userTokenData.AdminUserID = i[1];
                        break;

                }
            }
            return userTokenData;
        }
        public static string GetLogInOnStoreyaLink(bool iscom, int userID, int? shopID = null, string redirectURL = null, string adminUserID = null)
        {
            string token = CreateAuthToken(userID, shopID, redirectURL, adminUserID);
            string domain = Storeya.Core.Helpers.ConfigHelper.GetValue("Storeya.SiteUrl");
            if (iscom)
            {
                domain = Storeya.Core.Helpers.ConfigHelper.GetValue("Storeya.Com", "https://www.storeya.com/");
            }
            string returnUrl = domain + $"/Auth/RemoteLogMeAs?token={token}";
            return returnUrl;
        }

        public static string CreateAuthToken(int userID, int? shopID = null, string redirectURL = null, string adminUserID = null)
        {
            string redirect = null;
            var exp = DateTime.Now.AddDays(2).ToBinary();
            var date = DateTime.Now.ToBinary();
            if (redirectURL != null)
            {
                redirect = $"&returnurl={redirectURL.Base64Encode()}";
            }
            string shopIDText = null;
            if (shopID != null)
            {
                shopIDText = $"&shopid={shopID}";
            }
            string adminuID = null;
            if (adminUserID != null)
            {
                adminuID = $"&adminUserID={adminUserID}";
            }
            string token = (EncryptionHelper.EncryptAES($"uid={userID}&exp={exp}&date={date}{shopIDText}{redirect}{adminuID}")).Base64Encode();
            return token;
        }
    }
    public class UserTokenData
    {
        public string UserID { get; set; }
        public int? ShopID { get; set; }
        public string ReturnUrl { get; set; }
        public DateTime ExpiredAt { get; set; }
        //public string AdminFbID { get; set; }
        public string AdminUserID { get; set; }
        public DateTime DateFromToken { get; set; }
        public string Token { get; set; }
    }
}
