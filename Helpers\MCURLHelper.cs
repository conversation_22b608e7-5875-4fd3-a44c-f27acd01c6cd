﻿using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices.ComTypes;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class MCURLHelper
    {
        public static string AccountChooser(string sharedWithStoreYa, Boolean? makeChooserForEmma = false)
        {
            string accountChooser = null;
            if (sharedWithStoreYa != null)
            {
                if ((!sharedWithStoreYa.ToLower().Contains("storeya mca") && !sharedWithStoreYa.ToLower().Contains("emma")) || makeChooserForEmma.Value)
                {
                    accountChooser = "https://accounts.google.com/AccountChooser?Email=" + sharedWithStoreYa + "&continue=";
                }
            }

            return accountChooser;
        }
        public static string GetMCurl(ulong accountId, string sharedWith, string page = "overview")
        {
            var directory = "https://merchants.google.com/mc/overview?a=";
            if (page == "settings")
            {
                directory = "https://merchants.google.com/mc/settings/website?a=";
            }
            return sharedWith + directory + accountId;
        }

    }
    public class CandidatesToExludeUserFromMC
    {
        public List<int> ShopIDs { get; set; }
        public ulong MerchantID { get; set; }
        public string URL { get; set; }
        public string URLFromTb { get; set; }
        public string Name { get; set; }
        public string AccountName { get; set; }
        public List<TrafficBooster> TbSettings { get; set; }
        public int? AmountOfAccounts { get; set; }
        public int? AmountOfCanceledAccounts { get; set; }
        public bool FoundActiveAppByDomain { get; set; }
        public bool CanceledLongAgo { get; set; }
        public bool HasFewMCAccounts { get; set; }
        public string AccessStatus { get; set; }
        public bool GoodCandidateToExlude { get; set; }
        public bool CanceledNotInrestingShop { get; set; }

        public CandidatesToExludeUserFromMC(KeyValuePair<string, ulong> merchant)
        {
            if (!merchant.Key.ToLower().Contains("error"))
            {
                this.URL = merchant.Key.Split('_')[0];
                this.Name = merchant.Key.Split('_')[2];
                this.AccessStatus = "Success";
            }
            else
            {
                this.URL = null;
                this.AccessStatus = "Error";
            }
            this.MerchantID = merchant.Value;
            this.AccountName = merchant.Key.Split('_')[1];
            this.FoundActiveAppByDomain = false;
            this.CanceledLongAgo = false;
            this.GoodCandidateToExlude = false;
            this.HasFewMCAccounts = false;
            this.CanceledNotInrestingShop = false;
        }
        public void CheckIfHasActiveShop(List<TrafficBooster> activeAccounts)
        {
            string domain = UrlPathHelper.GetHostName(this.URL).ToLower();
            foreach (TrafficBooster tb in activeAccounts)
            {
                var url = tb.Url1;
                if (url != null)
                {
                    if (url.ToLower().Contains(domain))
                    {
                        if (this.TbSettings == null)
                        {
                            this.TbSettings = new List<TrafficBooster> { tb };
                        }
                        else
                        {
                            this.TbSettings.Add(tb);
                        }
                        this.FoundActiveAppByDomain = true;
                        break;
                    }
                }
            }
            if (this.TbSettings != null)
            {
                this.ShopIDs = this.TbSettings.Select(x => x.ShopID.Value).ToList();
            }
            CheckIfGoodCandidateToExlude();
        }

        public void CheckIfCanceledLongAgo()
        {
            int yarsAgo = -1;
            if (this.AccountName.ToLower() == "storeya")
            {
                yarsAgo = -2;
            }
            DateTime yearsHavePassed = DateTime.Now.AddYears(yarsAgo);
            if (this.TbSettings != null && this.TbSettings.Count > 0)
            {
                this.AmountOfCanceledAccounts = this.TbSettings.Where(x => (x.CancelledAt != null && x.CancelledAt <= yearsHavePassed)
                || (x.AppStatus == TB_APP_STATUS.CANCELED.GetHashCode() && (x.LastPaymentDate == null || x.LastPaymentDate < yearsHavePassed))).Count();
                if (this.AmountOfCanceledAccounts == this.AmountOfAccounts) //all accounts canceled long ago
                {
                    this.CanceledLongAgo = true;
                }
                this.URLFromTb = this.TbSettings.Select(x => x.Url1).FirstOrDefault();
                this.ShopIDs = this.TbSettings.Select(x => x.ShopID.Value).Distinct().ToList();
            }
            CheckIfGoodCandidateToExlude();
        }
        public void CheckIfGoodCandidateToExlude()
        {
            if (this.FoundActiveAppByDomain == true || this.HasFewMCAccounts)
            {
                this.GoodCandidateToExlude = false;
            }
            else if (this.CanceledLongAgo)
            {
                this.GoodCandidateToExlude = true;
            }
            else if (this.CanceledNotInrestingShop)
            {
                this.GoodCandidateToExlude = true;
            }
            else
            {
                this.GoodCandidateToExlude = false;
            }
        }

        public void FillInTbSettings()
        {
            var db = DataHelper.GetStoreYaEntities();
            this.TbSettings = db.TrafficBoosters.Where(x => x.MerchantCenterAccountID == (long)this.MerchantID).ToList();
            this.AmountOfAccounts = this.TbSettings.Count;
        }

        public void CheckIfCanceledNotInterestShop()
        {
            if (this.AmountOfAccounts == 1)
            {
                var tbs = this.TbSettings[0];
                var fromDate = DateTime.Today.AddDays(-30);
                if (tbs.CancelledAt != null && tbs.CancelledAt < fromDate)
                {
                    if (tbs.PurchasedAt == null)
                    {
                        EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, $"Tb with shopID {tbs.ShopID} is missing purchased at", "This tb is disabled. Please check. And if it's not interest shop, remove it's MCid from Merchant Center");
                        return;
                    }
                    TimeSpan difference = tbs.CancelledAt.Value - tbs.PurchasedAt.Value;
                    if (difference.Days <= 60)
                    {
                        this.CanceledNotInrestingShop = true;
                        CheckIfGoodCandidateToExlude();
                    }
                }
            }
        }
    }
}
