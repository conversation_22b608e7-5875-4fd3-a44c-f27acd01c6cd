﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Helpers
{
    public class MathHelper
    {
        public static decimal CacluclatePercentageChange(decimal currentValue, decimal previousValue)
        {
            if (currentValue > 0 && previousValue > 0)
            {
                var change = ((currentValue - previousValue) / Math.Abs(previousValue)) * 100;
                return Math.Round(change, 2);
            }
            return 0;
        }

        public static double GetCents(double price)
        {
            return Convert.ToDouble("0." + (Math.Round(price, 0).ToString()));
        }

        public static decimal GetCents(decimal price)
        {
            return Convert.ToDecimal("0." + (Math.Round(price, 0).ToString()));
        }

        public static bool IsOdd(int value)
        {
            return value % 2 != 0;
        }

        public static string GetShortNumber(int number)
        {
            string result = "0";
            if (number >= 1000000)
                result = Math.Round((decimal)number / 1000000, 1).ToString() + "M";
            else if (number >= 10000)
                result = Math.Round((decimal)number / 1000, 0).ToString() + "K";
            else if (number >= 1000)
                result = Math.Round((decimal)number / 1000, 1).ToString() + "K";
            else
                result = number.ToString();
            return result;
        }

        public static string GetShortNumber(long number)
        {
            string result = "0";
            if (number >= 1000000)
                result = Math.Round((decimal)number / 1000000, 1).ToString() + "M";
            else if (number >= 10000)
                result = Math.Round((decimal)number / 1000, 0).ToString() + "K";
            else if (number >= 1000)
                result = Math.Round((decimal)number / 1000, 1).ToString() + "K";
            else
                result = number.ToString();
            return result;
        }


    }
}
