﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Storeya.Core.Helpers
{
    public class MobileHelper
    {
        public static bool IsMobileUserAgent(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent))
            {
                return false;
            }
            string pattern = "iPhone|iPod|BlackBerry|Android.*Mobile|BB10.*Mobile|webOS|Windows CE|IEMobile|Opera Mini|Opera Mobi|HTC|LG-|LGE|SAMSUNG|Samsung|SEC-SGH|Symbian|Nokia|PlayStation|PLAYSTATION|Nintendo DSi";
            Regex reg = RegexHelper.GetInstance(pattern);
            return reg.IsMatch(userAgent);
        }

        public static bool IsTablet(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent))
            {
                return false;
            }
            Regex r = new Regex("Tablet|iPad|PlayBook|BB10|Z30|Nexus 10|Nexus 7|GT-P|SCH-I800|Xoom|Kindle|Silk|KFAPWI", RegexOptions.IgnoreCase);
            return r.Is<PERSON>atch(userAgent);
        }
    }
}
