﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class NumbersFormater
    {
        public static string FormatBigNumber(int? amount)
        {
            if (amount == null)
            {
                return null;
            }
            else
            {
                string totalAmountToShowString = (amount % 1) == 0
                 ? String.Format("{0:n0}", amount)
                 : String.Format("{0:n}", amount);

                return totalAmountToShowString;
            }

        }
        public static string FormatBigNumber(decimal? amount, bool toInt = true)
        {
            if(amount == null)
            {
                return null;
            }
            else
            {
                if (toInt)
                {
                    amount = Convert.ToInt32(amount);
                }
                string totalAmountToShowString = (amount % 1) == 0
                    ? String.Format("{0:n0}", amount)
                    : String.Format("{0:n}", amount);

                return totalAmountToShowString;
            }
        }

        public static string GetPercentage(decimal wholeAmount, decimal part)
        {
            if (wholeAmount > 0)
            {
                var perc = part / wholeAmount * 100;
                int percInt = 0;
                if (perc > 0)
                {
                    percInt = Convert.ToInt32(perc);
                }
                return $"{percInt}%";
            }
            return "";
        }

    }
}
