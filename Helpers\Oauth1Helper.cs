﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace Storeya.Core.Helpers
{
    public class Oauth1Helper
    {
        private static readonly DateTime _epoch = new DateTime(1970, 1, 1, 0, 0, 0, 0);
       
        private static string unreservedChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_.~";

        public static string GenerateTimeStamp()
        {
            TimeSpan ts = DateTime.UtcNow - _epoch;
            return Convert.ToInt64(ts.TotalSeconds).ToString();
        }

        public static string GenerateNonce()
        {
            Random _random = new Random();
            var sb = new System.Text.StringBuilder();
            for (int i = 0; i < 8; i++)
            {
                int g = _random.Next(3);
                switch (g)
                {
                    case 0:
                        // lowercase alpha
                        sb.Append((char)(_random.Next(26) + 97), 1);
                        break;
                    default:
                        // numeric digits
                        sb.Append((char)(_random.Next(10) + 48), 1);
                        break;
                }
            }
            return sb.ToString();
        }

        public static string GenerateOauthSignature(string uri, string method, string consumer_secret, string token_secret, Dictionary<String, String> parameters)
        {
            var signatureBase = GetSignatureBase(uri, method, parameters);
            var hash = GetHash(consumer_secret, token_secret);

            byte[] dataBuffer = Encoding.ASCII.GetBytes(signatureBase);
            byte[] hashBytes = hash.ComputeHash(dataBuffer);
            var sig = Convert.ToBase64String(hashBytes);
            return sig;
        }

        private static HashAlgorithm GetHash(string consumer_secret, string token_secret)
        {
            var keystring = string.Format("{0}&{1}", UrlEncode(consumer_secret), UrlEncode(token_secret));

            return new HMACSHA1
            {
                Key = Encoding.ASCII.GetBytes(keystring)
            };
        }

        public static string UrlEncode(string value)
        {
            var result = new StringBuilder();
            foreach (char symbol in value)
            {
                if (unreservedChars.IndexOf(symbol) != -1)
                    result.Append(symbol);
                else
                {
                    foreach (byte b in Encoding.UTF8.GetBytes(symbol.ToString()))
                    {
                        result.Append('%' + String.Format("{0:X2}", b));
                    }
                }
            }
            return result.ToString();
        }

        private static string GetSignatureBase(string url, string method, Dictionary<String, String> parameters)
         {
             // normalize the URI
             var uri = new Uri(url);
             var normUrl = string.Format("{0}://{1}", uri.Scheme, uri.Host);
             if (!((uri.Scheme == "http" && uri.Port == 80) ||
                   (uri.Scheme == "https" && uri.Port == 443)))
                 normUrl += ":" + uri.Port;

             normUrl += uri.AbsolutePath;

             // the sigbase starts with the method and the encoded URI
             StringBuilder sb = new StringBuilder();
             sb.Append(method)
                 .Append('&')
                 .Append(UrlEncode(normUrl))
                 .Append('&');

             var sb1 = new System.Text.StringBuilder();
             foreach (KeyValuePair<String, String> item in parameters.OrderBy(x => x.Key))
             {
                 // even "empty" params need to be encoded this way.
                 sb1.AppendFormat("{0}={1}&", item.Key, item.Value);
             }

             sb.Append(UrlEncode(sb1.ToString().TrimEnd('&')));
             var result = sb.ToString();
             
             return result;
         }

        public static string EncodeRequestParameters(ICollection<KeyValuePair<String, String>> p)
        {
            var sb = new System.Text.StringBuilder();
            foreach (KeyValuePair<String, String> item in p.OrderBy(x => x.Key))
            {
                if (!String.IsNullOrEmpty(item.Value)
                   // && !item.Key.EndsWith("secret")
                    )
                    sb.AppendFormat("{0}=\"{1}\", ",
                                    item.Key,
                                    UrlEncode(item.Value));
            }

            return sb.ToString().TrimEnd(' ').TrimEnd(',');
        }

        public static string GetResponse(string url, string method, Dictionary<String, String> parameters)
        {
            string content = "";

            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = method;
            request.PreAuthenticate = true;
            var authzHeader = Oauth1Helper.EncodeRequestParameters(parameters);
            request.Headers.Add("Authorization", authzHeader);

            using (var response = (HttpWebResponse)request.GetResponse())
            {

                Stream s = response.GetResponseStream();
                content = new StreamReader(s).ReadToEnd();
            }

            return content;
        }
    }
}
