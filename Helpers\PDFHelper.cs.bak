﻿using System.IO;
using System;
using Storeya.Core.Models;
using PostmarkDotNet;
using System.Data.Entity.Core.Metadata.Edm;
using System.Collections.Generic;
using LinqToTwitter;
using System.Linq;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models.AppStore;
using Storeya.Core.Models.TrafficBoosterModels.TrafficChannels;

namespace Storeya.Core.Helpers
{
    public class ReferrerReportTemplate
    {
        public string ReportName { get; set; }
        public string ReportDate { get; set; }
        public string HtmlRows { get; set; }
        public string DateTimeNow { get; set; }
        public decimal TotalCharged { get; set; }
        public decimal TotalBudget { get; set; }
        public decimal TotalFee { get; set; }



    }
    public class ReferrerReportRow
    {
        public DateTime? TransactionDate { get; set; }
        public string TransactionType { get; set; }
        public int ShopID { get; set; }
        public int? AgreeID { get; set; }
        public string InvoiceAmount { get; set; }
        public string Tax { get; set; }
        public decimal? FeePercentage { get; set; }
        public decimal? FeeOnlyFeePercentage { get; set; }
    }

    public class ReferrerReportHtmlRow
    {
        public string TransactionDate { get; set; }
        public string TransactionType { get; set; }
        public string ShopID { get; set; }
        public decimal InvoiceAmount { get; set; }
        //public decimal? FeePercentage { get; set; }
        //public decimal? FeeOnlyFeePercentage { get; set; }       
        public decimal Advertisingbudget { get; set; }
        public decimal PartnerFee { get; set; }
    }
    public class CancelSubscriptionTemplate
    {
        public string CustomerEmail { get; set; }
        public string ShipToEmail { get; set; }
        public string InvoiceNumber { get; set; }
        public string DateTimeNow { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string ContractName { get; set; }
        public string SubscriptionID { get; set; }
        public string AccountId { get; set; }
    }
    public class InvoiceTemplate
    {
        public string InvoiceNumber { get; set; }
        public string DateTimeNow { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string CardType { get; set; }
        public string Last4Digt { get; set; }
        public string PaymentDetails { get; set; }
        public string ShipToFirstName { get; set; }
        public string ShipToLastName { get; set; }
        public string ShipToEmail { get; set; }
        public string ContractId { get; set; }
        public string ContractName { get; set; }
        public string Quantity { get; set; }
        public string Total { get; set; }
        public string Currency { get; set; }
        public string SubTotal { get; set; }
        public string PaymentDate { get; set; }
        public string PaymentType { get; set; }
        public string Balance { get; set; }
        public string PaymentReceived { get; set; }
        public string Tax { get; set; }
        public string Tax2 { get; set; }
        public string TaxMessage { get; set; }
        public string NextPaymenDate { get; set; }
        public string NextPayment { get; set; }
        public string ChargeFrequency { get; set; }

        public string CustomerEmail { get; set; }
        public string AdditionalInfo { get; set; }
        public string PaymentPlatformTable { get; set; }
        public string PaymentPlatformTableEmail { get; set; }
    }
    public class PDFHelper
    {



        public static int EmailReferersReport(int monthsAgo, int? refid = null, int? startFromMonthsAgo = null)
        {
            try
            {
                decimal defaultFeeValue = 0.06M;
                var db = DataHelper.GetStoreYaEntities();
                List<Referer> referers = null;
                if (db.Referers.Any())
                {
                    if (refid.HasValue)
                    {
                        referers = db.Referers.Where(x => x.ID == refid).ToList();
                    }
                    else
                    {
                        referers = db.Referers.Where(r => r.IsActive == 1).ToList();
                    }


                }

                DateTime dateXMonthsBack = DateTime.Now.AddMonths(-monthsAgo);


                DateTime fromDate = new DateTime(dateXMonthsBack.Year, dateXMonthsBack.Month, 1);
                DateTime toDateForQuery = fromDate.AddMonths(1);
                DateTime toDate = fromDate.AddMonths(1).AddDays(-1);

                if (startFromMonthsAgo != null)
                {
                    var d = DateTime.Now.AddMonths(-startFromMonthsAgo.Value);
                    fromDate = new DateTime(d.Year, d.Month, 1);
                    toDateForQuery = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    toDate = toDateForQuery.AddDays(-1);
                }
                if (referers == null)
                {
                    return 0;
                }
                System.Net.Mail.MailAddress fromAdress = new System.Net.Mail.MailAddress("<EMAIL>", "StoreYa Support");
                List<System.Net.Mail.Attachment> attachments = new List<System.Net.Mail.Attachment>();
                foreach (Referer referer in referers)
                {
                    refid = referer.ID;
                    List<ReferrerReportRow> query = (from p in db.PlimusIpnCalls
                                                     join apps in db.ShopApps.Where(x => x.RefererID == refid && x.AppTypeID == (int)AppTypes.TrafficBooster) on p.ShopID equals apps.ShopID
                                                     where p.TransactionDate >= fromDate && p.TransactionDate < toDateForQuery
                                                                                         && p.AppID == 204
                                                                                         && p.TransactionType != "cancellation"
                                                                                         && p.TransactionType != "SUBSCRIPTION_CHARGE_FAILURE"
                                                                                         && p.TransactionType != "CONTRACT_CHANGE"
                                                                                         && p.TransactionType != "CC_CHARGE_FAILED"
                                                                                         && p.TransactionType != "CANCEL_ON_RENEWAL"
                                                     orderby p.ID
                                                     select new ReferrerReportRow()
                                                     {

                                                         TransactionDate = p.TransactionDate,
                                                         TransactionType = p.TransactionType,
                                                         ShopID = p.ShopID,
                                                         AgreeID = p.AgreeID,
                                                         InvoiceAmount = p.invoiceAmount,
                                                         Tax = p.taxAmountUSD,
                                                         FeePercentage = referer.FeePercentage,
                                                         FeeOnlyFeePercentage = referer.FeeOnlyFeePercentage
                                                     }).ToList();

                    if (query != null && query.Count() > 0)
                    {
                        string fileName = string.Format("Storeya_Report_R{0}_{1}", refid, fromDate.ToString("MMM_yyyy"));
                        //string dateString = string.Format("{0} - {1}", fromDate.ToString("dd"), toDate.ToString("dd MMM, yyyy")); //May 1st – 31st 2017
                        string dateString = $"{fromDate.ToString("dd MMM")} - {toDate.ToString("dd MMM, yyyy")}";

                        ReferrerReportTemplate template = new ReferrerReportTemplate();
                        template.ReportName = referer.ReportName;
                        template.ReportDate = dateString;
                        string rows = string.Empty;
                        foreach (var item in query)
                        {

                            decimal invoiceAmount = Math.Round(decimal.Parse((string.IsNullOrEmpty(item.Tax) ? item.InvoiceAmount : (decimal.Parse(item.InvoiceAmount) - decimal.Parse(item.Tax)).ToString())), 2);
                            decimal advertisingbudget = (invoiceAmount * 80) / 100;
                            decimal partnerFee = Math.Round(invoiceAmount * (item.FeePercentage.HasValue ? item.FeePercentage.Value : defaultFeeValue), 2);
                            string agree = string.Empty;
                            if (item.AgreeID.HasValue && item.AgreeID > 0)
                            {
                                var fbChannel = Models.TrafficBoosterModels.TrafficChannels.TbChannelManager.GetShopChannelData(item.ShopID, Models.TrafficBoosterModels.TrafficChannelsTypes.Facebook);
                                agree = "FB";
                                decimal fee = (fbChannel.OurFee.HasValue ? fbChannel.OurFee.Value : TbFeeHelper.DefaultFbFeeValue);
                                advertisingbudget = Math.Round(invoiceAmount / fee, 2);
                                partnerFee = Math.Round(invoiceAmount * (item.FeeOnlyFeePercentage.HasValue ? item.FeeOnlyFeePercentage.Value : defaultFeeValue), 2);
                            }
                            ReferrerReportHtmlRow row = new ReferrerReportHtmlRow()
                            {
                                TransactionDate = item.TransactionDate.Value.ToString("dd MMM yyyy"),
                                TransactionType = item.TransactionType,
                                ShopID = string.Format("{0} {1}", item.ShopID.ToString(), agree),
                                InvoiceAmount = invoiceAmount,
                                Advertisingbudget = advertisingbudget,
                                PartnerFee = partnerFee
                            };
                            template.TotalCharged += row.InvoiceAmount;
                            template.TotalFee += row.PartnerFee;
                            template.TotalBudget += row.Advertisingbudget;
                            rows = string.Format("{0}{1}", rows, row.ToHtmlTableRow(false, "style='padding:10px;'"));
                        }

                        template.HtmlRows = rows;
                        template.DateTimeNow = DateTime.Now.ToString("dd MMM yyyy");
                        string htmlTemplate = Storeya.Core.Properties.Resources.ReferersReport;
                        string html = htmlTemplate.FormatWith(template);

                        var htmlToPdf = new NReco.PdfGenerator.HtmlToPdfConverter();

                        Stream pfdStream = new MemoryStream(htmlToPdf.GeneratePdf(html));


                        System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(pfdStream, fileName + ".pdf");
                        attachments.Add(attachment);

                    }
                }
                EmailHelper.SendEmail("<EMAIL>", "Referers Report " + DateTime.Now.ToString("MMM - yyyy"), "", fromAdress, attachments);
                return attachments.Count();
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError("Failed to create referers report! error:" + ex.ToString(), ex);
            }
            return 0;
        }

        public static byte[] DownloadInvoice(string invoiceId)
        {
            InvoiceTemplate invoiceTemplate = GenerateInvoiceTemplate(invoiceId);
            string htmlTemplate = Storeya.Core.Properties.Resources.InvoicePdf;
            //string htmlTemplate = File.ReadAllText(@"C:\Dev\StoreYa\trunk\storeya\EmailTemplates\InvoicePdf.html");
            string html = htmlTemplate.FormatWith(invoiceTemplate);

            var htmlToPdf = new NReco.PdfGenerator.HtmlToPdfConverter();
            ;// @"c:\temp\document1.pdf");

            return htmlToPdf.GeneratePdf(html);
            //Stream pfdStream = new MemoryStream();
        }

        public static void EmailCustomerRefundInvoice(string invoiceId, string overrideEmail = null, int? shopId = 0)
        {
            InvoiceTemplate invoiceTemplate = GenerateInvoiceTemplate(invoiceId);
            string htmlBodyTemplate = Storeya.Core.Properties.Resources.InvoiceRefundBody;
            //string htmlTemplate = File.ReadAllText(@"C:\Dev\StoreYa\trunk\storeya\EmailTemplates\InvoicePdf.html");            
            string subject = string.Format("StoreYa.com - Refund Invoice #{0}", invoiceId);
            if (string.IsNullOrEmpty(overrideEmail))
            {
                overrideEmail = invoiceTemplate.ShipToEmail;
            }
            else
            {
                invoiceTemplate.CustomerEmail = "Customer Email: " + invoiceTemplate.ShipToEmail;
                subject = subject + " For ShopID:" + shopId;
            }
            string htmlBody = htmlBodyTemplate.FormatWith(invoiceTemplate);
            System.Net.Mail.MailAddress fromAdress = new System.Net.Mail.MailAddress("<EMAIL>", "StoreYa Support");
            EmailHelper.SendEmail(overrideEmail, subject, htmlBody, fromAdress);
        }
        public static void EmailCustomerCancelSubscription(string shopSubscriptionId, string bsSubscriptionId, User user, string overrideEmail = null, int? shopId = 0)
        {
            var db = DataHelper.GetStoreYaEntities();
            int id = int.Parse(shopSubscriptionId);
            var shopSubscription = db.ShopSubscriptions.SingleOrDefault(s => s.ID == id);
            PlimusIpnCall ipnCall = null;
            List<string> types = new List<string>() {
               "CANCELLATION",
               "CANCELLATION_REFUND",
               "CANCEL_ON_RENEWAL",
                    "CHARGEBACK",
                    "DECLINE",
            };
            var ipnCalls = db.PlimusIpnCalls.Where(i => i.subscriptionId.Trim().Equals(bsSubscriptionId) && types.Contains(i.TransactionType));
            if (ipnCalls != null)
            {
                ipnCall = ipnCalls.OrderByDescending(d => d.InsertedAt).FirstOrDefault();
            }
            string contractName = shopSubscription.ContractID.ToString();
            string invoiceNumber = "";
            string accountId = shopSubscription.ShopID.ToString();
            if (ipnCall != null)
            {
                contractName = ipnCall.contractName;
                invoiceNumber = ipnCall.ReferenceNumber;
                accountId = ipnCall.accountId;
            }
            CancelSubscriptionTemplate template = new CancelSubscriptionTemplate()
            {
                ContractName = contractName,
                CustomerEmail = user.Email,
                DateTimeNow = DateTime.Now.ToString("dd MMM yyyy"),
                FirstName = user.Name,
                LastName = null,
                InvoiceNumber = invoiceNumber,
                ShipToEmail = user.Email,
                SubscriptionID = shopSubscription.BlueSnapSubscriptionID.ToString(),
                AccountId = accountId

            };
            string htmlTemplate = Storeya.Core.Properties.Resources.CancelSuscriptionEmail;
            string htmlBody = htmlTemplate.FormatWith(template);
            string subject = "StoreYa.com - Subscription Cancellation";
            if (string.IsNullOrEmpty(overrideEmail))
            {
                overrideEmail = template.ShipToEmail;
            }
            else
            {
                template.CustomerEmail = "Customer Email: " + template.ShipToEmail;
                subject = subject + " For ShopID:" + shopId;
            }
            System.Net.Mail.MailAddress fromAdress = new System.Net.Mail.MailAddress("<EMAIL>", "StoreYa Support");
            EmailHelper.SendEmail(overrideEmail, subject, htmlBody, fromAdress);
        }
        public static void EmailCustomerInvoice(string invoiceId, string overrideEmail = null, int? shopId = 0)
        {
            InvoiceTemplate invoiceTemplate = GenerateInvoiceTemplate(invoiceId);
            string htmlTemplate = Storeya.Core.Properties.Resources.InvoicePdf;
            string htmlBodyTemplate = Storeya.Core.Properties.Resources.InvoiceBody;
            //string htmlTemplate = File.ReadAllText(@"C:\Dev\StoreYa\trunk\storeya\EmailTemplates\InvoicePdf.html");
            string html = htmlTemplate.FormatWith(invoiceTemplate);
            string subject = string.Format("StoreYa.com - Invoice #{0}", invoiceId);
            if (string.IsNullOrEmpty(overrideEmail))
            {
                overrideEmail = invoiceTemplate.ShipToEmail;
            }
            else
            {
                invoiceTemplate.CustomerEmail = "Customer Email: " + invoiceTemplate.ShipToEmail;
                subject = subject + " For ShopID:" + shopId;
            }
            string htmlBody = htmlBodyTemplate.FormatWith(invoiceTemplate);
            var htmlToPdf = new NReco.PdfGenerator.HtmlToPdfConverter();
            ;// @"c:\temp\document1.pdf");
            Stream pfdStream = new MemoryStream(htmlToPdf.GeneratePdf(html));
            System.Net.Mail.MailAddress fromAdress = new System.Net.Mail.MailAddress("<EMAIL>", "StoreYa Support");

            string body = string.Format("StoreYa.com - Invoice #{0}", invoiceId);
            string filename = string.Format("StoreYa Invoice {0}.pdf", invoiceId);
            System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(pfdStream, filename);
            List<System.Net.Mail.Attachment> attachments = new List<System.Net.Mail.Attachment>() { attachment };
            EmailHelper.SendEmail(overrideEmail, subject, htmlBody, fromAdress, attachments);
        }

        public static InvoiceTemplate GenerateInvoiceTemplateFromBS(string invoiceId, string subscriptionId = null)
        {
            BlueSnapApi blueSnapApi = new BlueSnapApi();
            Storeya.Core.Models.Plimus.order order = blueSnapApi.RetrieveOrderByInvoice(invoiceId);


            InvoiceTemplate invoiceTemplate = new InvoiceTemplate()
            {
                ContractName = order.cart.cartitem.sku.skuname,
                ContractId = order.cart.cartitem.sku.skuid,
                Quantity = order.cart.cartitem.quantity.ToString(),
                Currency = order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.currency,
                DateTimeNow = DateTime.Now.ToString("dd MMM yyyy"),
                InvoiceNumber = order.postsaleinfo.invoices.invoice.invoiceid,
                PaymentDate = order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.datecreated,
                PaymentType = order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.paymentmethod,
                PaymentDetails = string.Empty,
                FirstName = order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.invoicecontactinfo.firstname,
                LastName = order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.invoicecontactinfo.lastname,
                ShipToEmail = order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.invoicecontactinfo.email,
                ShipToFirstName = order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.invoicecontactinfo.firstname,
                ShipToLastName = order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.invoicecontactinfo.lastname,
                SubTotal = order.cart.cartitem.itemsubtotal.ToString(),
                Total = order.cart.totalcartcost.ToString(),
                PaymentReceived = "-" + order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.amount.ToString(),
                Balance = "0.00",
                TaxMessage = string.Empty,
                Tax = string.Empty
            };

            if (!string.IsNullOrEmpty(subscriptionId))
            {
                var recurringSubscription = blueSnapApi.RetrieveSubscriptionObject(subscriptionId);
                invoiceTemplate.NextPaymenDate = recurringSubscription.nextchargedate.ToString();
                if (recurringSubscription.overriderecurringcharge != null)
                {
                    invoiceTemplate.NextPayment = recurringSubscription.overriderecurringcharge.amount.ToString();
                }
                invoiceTemplate.ChargeFrequency = recurringSubscription.chargefrequency.ToLower();
                invoiceTemplate.TaxMessage = "";
            }

            if (invoiceTemplate.PaymentType.ToLower().Equals("credit card") || invoiceTemplate.PaymentType.ToLower().Equals("cc"))
            {
                invoiceTemplate.CardType = order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.creditcard.cardtype;
                invoiceTemplate.Last4Digt = string.Empty;// ": XXXX-XXXX-XXXX-" + order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.creditcard.cardlastfourdigits.ToString();
            }
            else if (invoiceTemplate.PaymentType.ToLower().Contains("paypal"))
            {
                invoiceTemplate.CardType = "PayPal";
                invoiceTemplate.Last4Digt = string.Empty;
            }
            else
            {
                invoiceTemplate.CardType = invoiceTemplate.PaymentType;
                invoiceTemplate.Last4Digt = string.Empty;
            }
            if (order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.tax > 0)
            {
                invoiceTemplate.Tax = string.Format("<tr><td></td><td><font style='margin:5px;'></font></td><td><font style='margin:5px;'>VAT/GST ({0}%)</font></td><td><font style='margin:5px;'>1</font></td><td align='right'>{1} {2}</td></tr>",
                    order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.taxrate, order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.tax, invoiceTemplate.Currency);
                invoiceTemplate.Tax2 = string.Format("<tr><td colspan='2' align='right'><font style='margin-right:5px;'>VAT/GST ({0}%)</font></td><td align='right'>{1} {2}</td></tr>",
                    order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.taxrate, order.postsaleinfo.invoices.invoice.financialtransactions.financialtransaction.tax, invoiceTemplate.Currency);

            }

            return invoiceTemplate;
        }

        public static InvoiceTemplate GenerateInvoiceTemplate(string invoiceId)
        {
            var db = DataHelper.GetStoreYaEntities();

            PlimusIpnCall ipnCall = null;
            var ipnCalls = db.PlimusIpnCalls.Where(i => i.ReferenceNumber.Trim().Equals(invoiceId));
            bool isFastSpring = false;
            if (ipnCalls != null)
            {
                ipnCall = ipnCalls.OrderByDescending(d => d.InsertedAt).FirstOrDefault();
            }
            if (ipnCall != null && ipnCall.subscriptionId != null)
            {
                isFastSpring = !long.TryParse(ipnCall.subscriptionId, out long result);
            }

            if (ipnCall == null)
            {
                //log
            }
            else
            {
                string invoiceAdditionalInfo = string.Empty;
                string chargeFrequency = ipnCall.ChargeFrequency;
                string nextPaymentAmount = ipnCall.NextPaymentAmount;
                string nextPaymenDate = string.Empty;
                if (ipnCall.NextPaymenDate.HasValue)
                {
                    nextPaymenDate = ipnCall.NextPaymenDate.Value.ToString("dd MMM yyyy");
                }
                if (ipnCall.ShopID > 0)
                {
                    var tb = TrafficBoostersDbHelper.GetSettings(ipnCall.ShopID, true);
                    if (tb != null)
                    {
                        invoiceAdditionalInfo = tb.InvoiceAdditionalInfo.SubString2(0, 150);
                        decimal? dNextPaymentAmount = null;
                        try
                        {
                            ShopSubscription shopSubscription = SubscriptionManager.UpdateNextPayment(ipnCall.ShopID, ipnCall.subscriptionId);
                            dNextPaymentAmount = shopSubscription.NextPaymentAmount;
                            nextPaymenDate = shopSubscription.NextPaymenDate.HasValue ? $" {shopSubscription.NextPaymenDate.Value.ToString("dd MMM yyyy")}" : "";
                            chargeFrequency = TbAppManager.GetChargeFrequency(ipnCall.ShopID, shopSubscription.ChargeFrequency).ToString().ToTitleCase();
                        }
                        catch //(Exception ex)
                        {
                            TbAppManager tbApp = new TbAppManager(ipnCall.ShopID, false, ipnCall.AgreeID, loadSubscriptionInfo: true);
                            Models.TrafficBoosterModels.TrafficChannels.AccountTbBudget budget = null;
                            if (ipnCall.AgreeID.HasValue && ipnCall.AgreeID.Value > 0)
                            {
                                budget = tbApp.GetAgreementChannel();
                            }
                            else
                            {
                                budget = tbApp.GetDefaultChannel();
                            }
                            if (budget != null)
                            {
                                if (budget.NextPaymenDate.HasValue && budget.NextPaymenDate.Value > DateTime.Now.AddHours(8))
                                {
                                    nextPaymenDate = budget.NextPaymenDate.HasValue ? $" {budget.NextPaymenDate.Value.ToString("dd MMM yyyy")}" : "";
                                    if (budget.ChargeFrequency != TbAppManager.CHARGE_FREQUENCY.ON_1_AND_15)
                                    {
                                        chargeFrequency = budget.ChargeFrequency.ToString().ToTitleCase();
                                    }
                                    dNextPaymentAmount = tbApp.GetExpectedNextPaymentAmount(budget);
                                    nextPaymentAmount = dNextPaymentAmount.HasValue ? $" {NumbersFormater.FormatBigNumber(dNextPaymentAmount.Value)}" : " 0";

                                }
                            }
                        }
                    }

                }
                InvoiceTemplate invoiceTemplate = new InvoiceTemplate()
                {
                    ContractName = ipnCall.productName,
                    ContractId = ipnCall.productId, //refund - null
                    Quantity = ipnCall.quantity.ToString(), //refund - null
                    Currency = ipnCall.currency,
                    DateTimeNow = DateTime.Now.ToString("dd MMM yyyy"),
                    InvoiceNumber = ipnCall.ReferenceNumber, //refund - null
                    PaymentDate = ipnCall.TransactionDate.Value.ToString("dd MMM yyyy"),
                    PaymentType = ipnCall.PaymentMethod,
                    PaymentDetails = string.Empty,
                    FirstName = ipnCall.firstName, //refund - null
                    LastName = ipnCall.lastName, //refund - null
                    ShipToEmail = ipnCall.email, //refund - null
                    //ShipToFirstName = ipnCall.shippingFirstName,
                    //ShipToLastName = ipnCall.shippingLastName,
                    SubTotal = string.IsNullOrEmpty(ipnCall.invoiceAmountInCurrency) ? ipnCall.invoiceAmount : ipnCall.invoiceAmountInCurrency,
                    Total = string.IsNullOrEmpty(ipnCall.invoiceAmountInCurrency) ? ipnCall.invoiceAmount : ipnCall.invoiceAmountInCurrency,
                    PaymentReceived = string.IsNullOrEmpty(ipnCall.invoiceAmountInCurrency) ? "-" + ipnCall.invoiceAmount : "-" + ipnCall.invoiceAmountInCurrency,
                    Balance = "0.00",
                    Tax = string.Empty,
                    NextPayment = nextPaymentAmount, //refund - null (but we can get it from subscription)
                    ChargeFrequency = chargeFrequency,
                    TaxMessage = "",
                    AdditionalInfo = invoiceAdditionalInfo
                };
                if (isFastSpring)
                {
                    invoiceTemplate.PaymentPlatformTable = Storeya.Core.Properties.Resources.FastSpringContactInfo;
                    invoiceTemplate.PaymentPlatformTableEmail = Storeya.Core.Properties.Resources.FastSpringContactInfoEmail;
                }
                else
                {
                    invoiceTemplate.PaymentPlatformTable = Storeya.Core.Properties.Resources.BlueSnapContactInfo;
                    invoiceTemplate.PaymentPlatformTableEmail = Storeya.Core.Properties.Resources.BlueSnapContactInfoEmail;
                }
                if (!string.IsNullOrEmpty(nextPaymenDate))
                {
                    invoiceTemplate.NextPaymenDate = nextPaymenDate;
                }

                if (invoiceTemplate.PaymentType.ToLower().Equals("credit card") || invoiceTemplate.PaymentType.ToLower().Equals("cc"))
                {
                    invoiceTemplate.CardType = ipnCall.CreditCardType;
                    invoiceTemplate.Last4Digt = string.Empty;// ": XXXX-XXXX-XXXX-" + ipnCall.CardLast4Digt;
                }
                else if (invoiceTemplate.PaymentType.ToLower().Contains("paypal"))
                {
                    invoiceTemplate.CardType = "PayPal";
                    invoiceTemplate.Last4Digt = string.Empty;
                }
                else
                {
                    invoiceTemplate.CardType = invoiceTemplate.PaymentType;
                    invoiceTemplate.Last4Digt = string.Empty;
                }
                decimal? tax = Helper.GetDecimalOrNull(ipnCall.taxAmountUSD);

                if (tax.HasValue && tax > 0)
                {
                    decimal? total = Helper.GetDecimalOrNull(string.IsNullOrEmpty(ipnCall.invoiceAmountInCurrency) ? ipnCall.invoiceAmount : ipnCall.invoiceAmountInCurrency);
                    invoiceTemplate.SubTotal = (total - tax).ToString();

                    invoiceTemplate.Tax = string.Format("<tr><td></td><td><font style='margin:5px;'></font></td><td><font style='margin:5px;'>VAT/GST ({0}%)</font></td><td><font style='margin:5px;'>1</font></td><td align='right'>{1} {2}</td></tr>",
                        ipnCall.TaxRateUSD, ipnCall.taxAmountInCurrency, invoiceTemplate.Currency);
                    invoiceTemplate.Tax2 = string.Format("<tr><td colspan='2' align='right'><font style='margin-right:5px;'>VAT/GST ({0}%)</font></td><td align='right'>{1} {2}</td></tr>",
                        ipnCall.TaxRateUSD, ipnCall.taxAmountInCurrency, invoiceTemplate.Currency);

                }
                return invoiceTemplate;
            }
            return null;
        }
    }
}
