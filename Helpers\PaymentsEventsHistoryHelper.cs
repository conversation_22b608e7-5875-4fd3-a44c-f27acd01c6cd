﻿using Storeya.Core.Models.Payments;
using System;
using System.Collections.Generic;
using System.Data.Entity.Validation;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace Storeya.Core.Helpers
{
    public class PaymentsEventsHistoryHelper
    {
        public enum EventHistoryCategory
        {
            Subscription = 1,
            Order = 2,
            Account = 3,
            WebHook = 4,
        }

        public enum EventType
        {

            Cancelled = -1,
            None = 0,
            Charge = 1,
            Upgrade = 2,
            Prorated = 3,
            Recurring = 4,
            Bank_Transfer = 5,
            Postponed = 6,
            Update = 7,
            Checkout_Failure = 8,

        }
        public enum EventHistoryStatus
        {
            Success = 1,
            Failed = -1,

        }
        public static void AddEvent(int shopId, string createdBy, EventHistoryCategory category,
            string refernceId,
            DateTime? chargeDate,
            EventHistoryStatus status,
            EventType eventType,
            PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.FastSpring,
            string comments = null, string data = null, int? agreeId = 0)
        {
            if (string.IsNullOrEmpty(refernceId))
            {
                refernceId = "none";
            }
            PaymentsEventsHistory paymentsEventsHistory = null;
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                paymentsEventsHistory = new PaymentsEventsHistory()
                {
                    InsertedAt = DateTime.Now,
                    ShopId = shopId,
                    AgreeId = agreeId,
                    Amount = 0,
                    Category = category.GetHashCode(),
                    RefernceId = refernceId,
                    ChargeDate = chargeDate,
                    Comments = comments,
                    CreatedBy = createdBy,
                    PaymentProvider = paymentProvider.GetHashCode(),
                    Status = status.GetHashCode(),
                    EventType = eventType.GetHashCode(),
                    Data = data,
                };
                db.PaymentsEventsHistory.Add(paymentsEventsHistory);
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                var exString = ex.ToString();
                if (ex is DbEntityValidationException)
                {
                    var newException = new FormattedDbEntityValidationException((DbEntityValidationException)ex);
                    exString = newException.ToString();
                    ex = newException;
                }
                string o = paymentsEventsHistory == null ? "" : paymentsEventsHistory.ToJson();
                ConsoleAppHelper.WriteErrorWithDB($"Failed to Add Event - {o}", ex, shopId);
            }
        }
        public static List<PaymentsEventsHistory> Get(int? shopid = null, string refernceId = null, DateTime? from = null, DateTime? to = null,
           int? categoryId = null,
           int? eventTypeId = null,
           int? statusId = null,
           int paymentProviderId = 3)
        {
            EventHistoryCategory? category = null;
            if (categoryId.HasValue)
            {
                category = (EventHistoryCategory)categoryId.Value;
            }
            EventType? eventType = null;
            if (eventTypeId.HasValue)
            {
                eventType = (EventType)eventTypeId.Value;
            }
            EventHistoryStatus? status = null;
            if (statusId.HasValue)
            {
                status = (EventHistoryStatus)statusId.Value;
            }
            PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.FastSpring;
            if (paymentProviderId != (int)PaymentAdapterTypes.FastSpring)
            {
                paymentProvider = (PaymentAdapterTypes)paymentProviderId;
            }
            return Get(shopid, refernceId, from, to, category, eventType, status, paymentProvider);

        }
        public static List<PaymentsEventsHistory> Get(int? shopid = null, string refernceId = null, DateTime? from = null, DateTime? to = null,
        EventHistoryCategory? category = null,
        EventType? eventType = null,
        EventHistoryStatus? status = null,
        PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.FastSpring)
        {
            try
            {
                DateTime now = DateTime.Now;
                var db = DataHelper.GetStoreYaEntities();
                var paymentsEventsHistories = db.PaymentsEventsHistory.Where(c => c.PaymentProvider == (int)paymentProvider);
                if (shopid != null)
                {
                    paymentsEventsHistories = paymentsEventsHistories.Where(c => c.ShopId == shopid);
                }
                if (from != null)
                {
                    paymentsEventsHistories = paymentsEventsHistories.Where(c => c.InsertedAt >= from);
                }
                if (to != null)
                {
                    paymentsEventsHistories = paymentsEventsHistories.Where(c => c.InsertedAt <= to);
                }
                if (category != null)
                {
                    paymentsEventsHistories = paymentsEventsHistories.Where(c => c.Category == (int)category);
                }
                if (status != null)
                {
                    paymentsEventsHistories = paymentsEventsHistories.Where(c => c.Status == (int)status);
                }
                if (eventType != null)
                {
                    paymentsEventsHistories = paymentsEventsHistories.Where(c => c.EventType == (int)eventType);
                }
                if (category != null)
                {
                    paymentsEventsHistories = paymentsEventsHistories.Where(c => c.Category == (int)category);
                }
                if (refernceId != null)
                {
                    paymentsEventsHistories = paymentsEventsHistories.Where(c => c.RefernceId == refernceId);
                }


                return paymentsEventsHistories == null ? new List<PaymentsEventsHistory>() : paymentsEventsHistories.ToList();
            }
            catch (Exception ex)
            {

                throw ex;
            }


        }
    }
}
