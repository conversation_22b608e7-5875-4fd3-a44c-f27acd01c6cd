﻿using eBayAdapter.eBayFindingService;
using Ez.Newsletter.MagentoApi;
using Storeya.Core.Entities;
using Storeya.Core.Models;
using Storeya.Core.Models.Account;
using Storeya.Core.Models.AppStore;
using Storeya.Core.Models.CRM;
using Storeya.Core.Models.FastSpring;
using Storeya.Core.Models.Payments;
using Storeya.Core.Models.ShopAttributes;
using Storeya.Core.Models.TbInternalTasks;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models.TrafficBoosterModels.TrafficChannels;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class PaymentsTransactionHelper
    {

        public enum ACTIONTYPE
        {
            NA = -1,
            ONCE = 1,
            WEEKLY = 2,
            MONTH_1_AND_15 = 3,
            PAYPALDISCOUNT = 4,
            ON_IPN_SUCCESS_CALL = 5,
            ON_IPN_FAILED_CALL = 6,
            ON_IPN_ALL_CALL = 7,

        }
        public enum TRANSACTIONTYPE
        {
            UPGRADE = 1,
            PRORATED = 2,
            RECURRING = 3,
            UPGRADE_PAYPAL = 4,
            BANK_TRANSFER = 5,
            POSTPONED = 6,
            PAYMENT = 10,
            CHARGE = 11

        }
        public enum IPN_TRANSACTION_TYPE
        {
            NULL,
            AUTH_ONLY,
            CANCEL,
            CANCEL_ON_RENEWAL,
            CANCELLATION,
            CANCELLATION_REFUND,
            CC_CHARGE_FAILED,
            CHARGE,
            CHARGEBACK,
            CONTRACT_CHANGE,
            DECLINE,
            RECURRING,
            REFUND,
            SUBSCRIPTION_CHARGE_FAILURE,
            UNDER_REVIEW,
        }
        public enum STATUS
        {
            FAILED = -1,
            CREATED = 0,
            DONE = 1,
            OVERWRITE = 2,
            WAITING_IPN = 3,
            CANCELLED = 4
        }

        public static bool CreatePaymentNotification(int shopId, string updatedBy, string subscriptionId, decimal? previousAmount, decimal amount, DateTime chargeDate, DateTime? previousChargeDate = null, string comments = null, int? agreeid = null, ACTIONTYPE actionType = ACTIONTYPE.NA)
        {
            PaymentsTransaction paymentsTransaction = new PaymentsTransaction()
            {
                ShopId = shopId,
                InsertedAt = DateTime.Now,
                UpdatedBy = updatedBy,
                Status = STATUS.DONE.GetHashCode(),
                TransactionType = TRANSACTIONTYPE.PAYMENT.GetHashCode(),
                SubscriptionId = subscriptionId,
                AgreeId = agreeid,
                ActionType = actionType.GetHashCode(),
                Amount = amount,
                PreviousAmount = previousAmount,
                ChargeDate = chargeDate,
                previousChargeDate = previousChargeDate,
                Comments = comments
            };
            return CreatePaymentsTransaction(paymentsTransaction);
        }

        public static bool CreatePaymentsTransaction(PaymentsTransaction paymentsTransaction)
        {
            var db = DataHelper.GetStoreYaEntities();

            db.PaymentsTransactions.Add(paymentsTransaction);
            db.SaveChanges();
            return true;

        }
        private class ReportResult
        {
            public int shopId { get; set; }
            public decimal TransactionPayment { get; set; }
            public decimal SubscriptionPayment { get; set; }
            public string ChargeDate { get; set; }
            public string SubscriptionUpdate { get; set; }
            public string Comments { get; set; }
        }
        public static void SendTransactionsReport(string emailTo = "<EMAIL>")
        {
            try
            {
                ExtentionsHelper.TableMetaData metaDate = new ExtentionsHelper.TableMetaData();
                metaDate.EnumsMetaDate.Add("status", PaymentsTransactionHelper.STATUS.CREATED);
                metaDate.EnumsMetaDate.Add("transactiontype", PaymentsTransactionHelper.TRANSACTIONTYPE.BANK_TRANSFER);
                metaDate.EnumsMetaDate.Add("actiontype", PaymentsTransactionHelper.ACTIONTYPE.NA);

                bool email = false;
                var db = DataHelper.GetStoreYaEntities();
                DateTime d = DateTime.Now.AddDays(-1);
                var paymentsTransactions = db.PaymentsTransactions.Where(t => t.Status == (int)STATUS.WAITING_IPN && t.TransactionType != (int)TRANSACTIONTYPE.BANK_TRANSFER && t.ChargeDate < d && (t.ActionType.HasValue || t.ActionType > 0));
                var paymentsTransactionsDone = db.PaymentsTransactions.Where(t => t.Status == (int)STATUS.DONE && t.UpdatedAt > d && (t.ActionType.HasValue || t.ActionType > 0));
                var paymentsTransactionsFailed = db.PaymentsTransactions.Where(t => t.Status == (int)STATUS.FAILED && t.UpdatedAt > d && (t.ActionType.HasValue || t.ActionType > 0));
                var paymentsTransactionsCancelled = db.PaymentsTransactions.Where(t => t.Status == (int)STATUS.CANCELLED && t.UpdatedAt > d && (t.ActionType.HasValue || t.ActionType > 0));
                DateTime dd = DateTime.Now.AddDays(-2);
                var paymentsTransactionsWaitingBankTransfer = db.PaymentsTransactions.Where(t => t.Status == (int)STATUS.WAITING_IPN && t.TransactionType == (int)TRANSACTIONTYPE.BANK_TRANSFER && t.ChargeDate < dd);

                StringBuilder sb = new StringBuilder();
                if (paymentsTransactions != null && paymentsTransactions.Count() > 0)
                {
                    List<ReportResult> reportResults = new List<ReportResult>();
                    foreach (var paymentsTransaction in paymentsTransactions)
                    {
                        string msg = string.Empty;
                        try
                        {
                            if (int.TryParse(paymentsTransaction.SubscriptionId, out int bsSubId))
                            {
                                var bsSub = SubscriptionManager.GetBlueSnapSubscriptionData(paymentsTransaction.ShopId, paymentsTransaction.AgreeId, paymentsTransaction.SubscriptionId);
                                ReportResult reportResult = new ReportResult();
                                reportResult.shopId = paymentsTransaction.ShopId;
                                bool add = false;
                                decimal bsSubPrice = 0;
                                if (!string.IsNullOrEmpty(bsSub.contractPrice))
                                {
                                    bsSubPrice = decimal.Parse(bsSub.contractPrice);
                                }
                                if (bsSub.TransactionType == "SUBSCRIPTION_CHARGE_FAILURE")
                                {
                                    reportResult.SubscriptionPayment = bsSubPrice;
                                    reportResult.TransactionPayment = paymentsTransaction.Amount.Value;
                                    reportResult.ChargeDate = paymentsTransaction.ChargeDate.Value.ToShortDateString();
                                    reportResult.SubscriptionUpdate = bsSub.TransactionDate.Value.ToShortDateString();
                                    add = true;
                                    reportResult.Comments = string.Format("Subscription failed to charge {0}", reportResult.Comments);
                                }
                                else
                                {
                                    if (paymentsTransaction.Amount.Value != bsSubPrice)
                                    {
                                        reportResult.SubscriptionPayment = bsSubPrice;
                                        reportResult.TransactionPayment = paymentsTransaction.Amount.Value;
                                        add = true;
                                        reportResult.Comments = string.Format("Payment is not equal {0}", reportResult.Comments);
                                    }
                                    if (bsSub.TransactionDate.HasValue && bsSub.TransactionDate.Value.Date != paymentsTransaction.ChargeDate.Value.Date)
                                    {
                                        reportResult.ChargeDate = paymentsTransaction.ChargeDate.Value.ToShortDateString();
                                        reportResult.SubscriptionUpdate = bsSub.TransactionDate.Value.ToShortDateString();
                                        reportResult.Comments = string.Format("Dates are not equal, {0}", reportResult.Comments);
                                        add = true;
                                    }
                                    if (!bsSub.TransactionDate.HasValue)
                                    {
                                        reportResult.ChargeDate = paymentsTransaction.ChargeDate.Value.ToShortDateString();
                                        reportResult.Comments = string.Format("Subscription was not update yet,{0}", reportResult.Comments);
                                        add = true;
                                    }

                                }
                                if (add)
                                {
                                    reportResults.Add(reportResult);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            sb.Append(string.Format("ShopId:{0} Error while checking payments,{1}<br/>Exception:{3}<br/>{2}", EmailHelper.AdminLinkHref(paymentsTransaction.ShopId), msg, Environment.NewLine, ex.ToString()));
                        }
                        email = true;
                    }
                    if (reportResults.Count() > 0)
                    {
                        sb.Append(string.Format("<br/><b>Needs to review waiting for IPN Transactions that was not updated until the :{0} </b><br/><hr/><br/>{1}", d, Environment.NewLine));
                        sb.Append(reportResults.ToList().ToHtmlTable(metaDate));
                        email = true;
                    }
                }
                if (paymentsTransactions != null && paymentsTransactions.Count() > 0)
                {
                    sb.Append(string.Format("<br/><b>Waiting For IPN Transactions until the :{0} </b><br/><hr/><br/>{1}", d, Environment.NewLine));
                    sb.Append(paymentsTransactions.ToList().ToHtmlTable(metaDate, 100));
                    email = true;
                }
                if (paymentsTransactionsDone != null && paymentsTransactionsDone.Count() > 0)
                {
                    sb.Append(string.Format("<br/><b>Done For IPN Transactions from the :{0} </b><br/><hr/><br/>{1}", d, Environment.NewLine));
                    sb.Append(paymentsTransactionsDone.ToList().ToHtmlTable(metaDate, 100));
                    email = true;
                }
                if (paymentsTransactionsWaitingBankTransfer != null && paymentsTransactionsWaitingBankTransfer.Count() > 0)
                {
                    sb.Append(string.Format("<br/><b>Bank transfer waiting for IPN Transactions until the :{0} </b><br/><hr/><br/>{1}", dd, Environment.NewLine));
                    sb.Append(paymentsTransactionsWaitingBankTransfer.ToList().ToHtmlTable(metaDate, 100));
                    email = true;
                }
                if (paymentsTransactionsCancelled != null && paymentsTransactionsCancelled.Count() > 0)
                {
                    sb.Append(string.Format("<br/><b>Cancelled IPN Transactions from :{0} </b><br/><hr/><br/>{1}", dd, Environment.NewLine));
                    sb.Append(paymentsTransactionsCancelled.ToList().ToHtmlTable(metaDate, 100));
                    email = true;
                }
                if (paymentsTransactionsFailed != null && paymentsTransactionsFailed.Count() > 0)
                {
                    sb.Append(string.Format("<br/><b>Failed IPN Transactions from the :{0} </b><br/><hr/><br/>{1}", d, Environment.NewLine));
                    sb.Append(paymentsTransactionsFailed.ToList().ToHtmlTable(metaDate, 100));
                    email = true;
                }

                sb.Append(string.Format("<br/><b>All IPN Transactions Report:Click {0}</b><br/><hr/><br/>{1}", "<a href=\"https://bo.storeya.com/Home/PaymentsTransactions\">Here</a>", Environment.NewLine));
                if (email)
                {
                    EmailHelper.SendEmail(emailTo, "Validate Actions & ECP Payments Transactions.", sb.ToString());
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(emailTo, "Failed To Validate Waiting For IPN Transactions!", ex.ToString());
            }
        }
        public static bool Is1And15ChargeFrequencyIPN(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            return db.PaymentsTransactions.Any(t => t.ShopId == shopId && t.ActionType == (int)ACTIONTYPE.MONTH_1_AND_15 && (t.Status == (int)STATUS.CREATED));
        }
        public static bool IsBankTransferWaitingIPN(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            return db.PaymentsTransactions.Any(t => t.ShopId == shopId && t.TransactionType == (int)TRANSACTIONTYPE.BANK_TRANSFER && (t.Status == (int)STATUS.WAITING_IPN));
        }
        public static bool IsBankTransferIPNDone(int shopId, int daysTocheck = 2)
        {
            var db = DataHelper.GetStoreYaEntities();
            var d = DateTime.Now.AddDays(-daysTocheck);
            return db.PaymentsTransactions.Any(t => t.ShopId == shopId && t.TransactionType == (int)TRANSACTIONTYPE.BANK_TRANSFER && (t.Status == (int)STATUS.DONE) && t.UpdatedAt > d);
        }
        public static PaymentsTransaction GetLatestProratedPayment(int shopId, int daysTocheck = 7, int? agreeId = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var d = DateTime.Now.AddDays(-daysTocheck);


            if (agreeId.HasValue && agreeId.Value > 0)
            {
                var trans = db.PaymentsTransactions.Where(t => t.ShopId == shopId && t.AgreeId.HasValue && t.AgreeId == agreeId && t.TransactionType == (int)TRANSACTIONTYPE.PRORATED && (t.Status == (int)STATUS.DONE || t.Status == (int)STATUS.WAITING_IPN) && t.InsertedAt > d).OrderByDescending(c => c.InsertedAt);
                if (trans == null) return null;
                return trans.FirstOrDefault();
            }
            else
            {
                var trans = db.PaymentsTransactions.Where(t => t.ShopId == shopId && (t.AgreeId == null || t.AgreeId == 0) && t.TransactionType == (int)TRANSACTIONTYPE.PRORATED && (t.Status == (int)STATUS.DONE || t.Status == (int)STATUS.WAITING_IPN) && t.InsertedAt > d).OrderByDescending(c => c.InsertedAt);
                if (trans == null) return null;
                return trans.FirstOrDefault();
            }
        }
        public static bool IsProrated(int shopId, DateTime chargeDate, string subscriptionId = null, decimal? amount = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            chargeDate = chargeDate.Date;
            if (string.IsNullOrEmpty(subscriptionId))
            {
                return db.PaymentsTransactions.Any(t => t.ShopId == shopId && t.Amount == amount && t.TransactionType == (int)TRANSACTIONTYPE.PRORATED && DbFunctions.TruncateTime(t.ChargeDate) == chargeDate);
            }
            return db.PaymentsTransactions.Any(t => t.ShopId == shopId && t.Amount == amount && t.SubscriptionId == subscriptionId && t.TransactionType == (int)TRANSACTIONTYPE.PRORATED && DbFunctions.TruncateTime(t.ChargeDate) == chargeDate);
        }

        public static PaymentsTransaction GetBankTransferTransaction(int shopId, int? agreeId = null, int daysTocheck = 7)
        {
            var db = DataHelper.GetStoreYaEntities();
            var d = DateTime.Now.AddDays(-daysTocheck);
            if (agreeId.HasValue && agreeId.Value > 0)
            {
                var trans = db.PaymentsTransactions.Where(t => t.ShopId == shopId && t.AgreeId == agreeId && t.TransactionType == (int)TRANSACTIONTYPE.BANK_TRANSFER && (t.Status == (int)STATUS.DONE || t.Status == (int)STATUS.WAITING_IPN) && t.InsertedAt > d).OrderByDescending(c => c.InsertedAt);
                if (trans == null) return null;
                return trans.FirstOrDefault();
            }
            else
            {
                var trans = db.PaymentsTransactions.Where(t => t.ShopId == shopId && t.TransactionType == (int)TRANSACTIONTYPE.BANK_TRANSFER && (t.Status == (int)STATUS.DONE || t.Status == (int)STATUS.WAITING_IPN) && t.InsertedAt > d).OrderByDescending(c => c.InsertedAt);
                if (trans == null) return null;
                return trans.FirstOrDefault();
            }

        }
        public static bool GetBankTransferDetails(string referenceNumber, out int shopId, out string appId, out string planId, out string agreeId)
        {
            var pt = GetLastPaymentsTransactions(referenceNumber, TRANSACTIONTYPE.BANK_TRANSFER);
            shopId = 0;
            appId = string.Empty;
            planId = string.Empty;
            agreeId = string.Empty;
            if (pt == null)
            {
                return false;
            }
            shopId = pt.ShopId;
            if (pt.AgreeId.HasValue)
            {
                agreeId = pt.AgreeId.Value.ToString();
            }
            string[] res = pt.Comments.Split(',', ':');
            planId = res[1];
            appId = res[3];

            return true;
        }
        public static PaymentsTransaction GetLastPaymentsTransactions(string subscriptionId, TRANSACTIONTYPE? type = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var trans = db.PaymentsTransactions.Where(t => t.SubscriptionId == subscriptionId && type.HasValue ? t.TransactionType == (int)type : 1 == 1).OrderByDescending(c => c.InsertedAt);
            if (trans == null) return null;
            return trans.FirstOrDefault();
        }
        public static int CreatePaymentsTransaction(int shopId, string updatedBy, TRANSACTIONTYPE type, string subscriptionId, decimal previousAmount, decimal amount, DateTime chargeDate, DateTime previousChargeDate, string comments, int? agreeid = null, STATUS status = STATUS.CREATED, DateTime? scheduledAt = null, ACTIONTYPE actionType = ACTIONTYPE.NA, int? parentId = null, bool overwritePreviousActions = false, PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.BlueSnap)
        {
            var db = DataHelper.GetStoreYaEntities();
            List<int> overWriteIfExists = new List<int>();
            overWriteIfExists.Add(TRANSACTIONTYPE.RECURRING.GetHashCode());
            overWriteIfExists.Add(TRANSACTIONTYPE.UPGRADE_PAYPAL.GetHashCode());
            overWriteIfExists.Add(TRANSACTIONTYPE.UPGRADE.GetHashCode());
            overWriteIfExists.Add(TRANSACTIONTYPE.POSTPONED.GetHashCode());
            overWriteIfExists.Add(TRANSACTIONTYPE.PRORATED.GetHashCode());
            overWriteIfExists.Add(TRANSACTIONTYPE.UPGRADE.GetHashCode());



            if (overWriteIfExists.Contains(type.GetHashCode()))
            {
                var paymentsTransactions = db.PaymentsTransactions.Where(t => t.ShopId == shopId && overWriteIfExists.Contains(t.TransactionType) && t.SubscriptionId == subscriptionId && (t.Status == (int)STATUS.CREATED || t.Status == (int)STATUS.WAITING_IPN));
                if (paymentsTransactions != null && paymentsTransactions.Count() > 0)
                {
                    List<int> onIpnActions = new List<int>() {
                        ACTIONTYPE.ON_IPN_ALL_CALL.GetHashCode(),
                        ACTIONTYPE.ON_IPN_FAILED_CALL.GetHashCode(),
                        ACTIONTYPE.ON_IPN_SUCCESS_CALL.GetHashCode(),
                        ACTIONTYPE.MONTH_1_AND_15.GetHashCode(),
                    };
                    foreach (var item in paymentsTransactions)
                    {
                        if (item.ActionType.HasValue && item.ActionType.Value != ACTIONTYPE.NA.GetHashCode())
                        {
                            if (actionType != ACTIONTYPE.NA && scheduledAt == null && parentId == null && onIpnActions.Contains(item.ActionType.Value) || overwritePreviousActions)
                            {
                                item.UpdatedAt = DateTime.Now;
                                item.Comments = $"{item.Comments} ,Status changed: {Enum.GetName(typeof(STATUS), item.Status)}";
                                item.Status = STATUS.OVERWRITE.GetHashCode();
                            }
                            if (actionType == ACTIONTYPE.NA && item.ActionType == ACTIONTYPE.PAYPALDISCOUNT.GetHashCode() || overwritePreviousActions)
                            {
                                item.UpdatedAt = DateTime.Now;
                                item.Comments = $"{item.Comments} ,Status changed: {Enum.GetName(typeof(STATUS), item.Status)}";
                                item.Status = STATUS.OVERWRITE.GetHashCode();
                            }
                        }
                        else
                        {
                            item.UpdatedAt = DateTime.Now;
                            item.Comments = $"{item.Comments} ,Status changed: {Enum.GetName(typeof(STATUS), item.Status)}";
                            item.Status = STATUS.OVERWRITE.GetHashCode();
                        }

                    }
                    db.SaveChanges();
                }
            }
            //var paymentsTransaction = db.PaymentsTransactions.SingleOrDefault(t => t.ShopId == shopId && t.TransactionType == (int)type && t.SubscriptionId == subscriptionId);
            //if (paymentsTransaction == null)
            //{
            PaymentsTransaction pt = new PaymentsTransaction()
            {
                InsertedAt = DateTime.Now,
                AgreeId = agreeid,
                Amount = amount,
                ChargeDate = chargeDate,
                Comments = comments,
                PreviousAmount = previousAmount,
                ShopId = shopId,
                Status = status.GetHashCode(),
                SubscriptionId = subscriptionId,
                UpdatedBy = updatedBy,
                TransactionType = type.GetHashCode(),
                previousChargeDate = previousChargeDate,
                ScheduledAt = scheduledAt,
                PaymentProvider = (int)paymentProvider
            };
            if (actionType != ACTIONTYPE.NA)
            {
                pt.ActionType = actionType.GetHashCode();
            }
            if (parentId.HasValue)
            {
                pt.ParentId = parentId;
            }
            db.PaymentsTransactions.Add(pt);
            db.SaveChanges();
            return pt.ID;
            //}
            //return false;
        }
        public static bool HandleRecurringPaymentTransaction(int shopId, int? agreeId, decimal purchasedAmount, out bool isProrated)
        {
            var db = DataHelper.GetStoreYaEntities();
            isProrated = false;
            var paymentsTransaction = GetWaitingIPN(shopId, agreeId);
            if (paymentsTransaction == null)
            {
                return false;
            }
            if (paymentsTransaction.TransactionType == TRANSACTIONTYPE.PRORATED.GetHashCode())
            {
                isProrated = true;
            }
            if (purchasedAmount == paymentsTransaction.Amount)
            {
                TbAppManager tbAppManager = new TbAppManager(shopId, true, onlyIfActiveAndPaid: false);
                if (agreeId.HasValue && agreeId > 0)
                {
                    tbAppManager.GetAgreementChannel().UpdatePurchasedBudget(purchasedAmount, "System");
                }
                else
                {
                    tbAppManager.GetDefaultChannel().UpdatePurchasedBudget(purchasedAmount, "System");
                }
                paymentsTransaction.Status = STATUS.DONE.GetHashCode();
                paymentsTransaction.UpdatedAt = DateTime.Now;
                paymentsTransaction.Comments = $"Budget Updated,{paymentsTransaction.Comments}";
                db.SaveChanges();
                return true;
            }
            return false;
        }
        public static PaymentsTransaction GetWaitingIPN(int shopId, int? agreeId)
        {
            if (agreeId == null)
            {
                agreeId = 0;
            }
            var db = DataHelper.GetStoreYaEntities();
            var paymentsTransaction = db.PaymentsTransactions.SingleOrDefault(t => t.ShopId == shopId && t.Status == (int)STATUS.WAITING_IPN && (agreeId == 0 ? (t.AgreeId == 0 || t.AgreeId == null) : t.AgreeId == agreeId) && (t.ActionType == null || t.ActionType != (int)ACTIONTYPE.PAYPALDISCOUNT));
            return paymentsTransaction;
        }
        public static PaymentsTransaction GetNextCreatedTransaction(int shopId, TRANSACTIONTYPE type, string subscriptionId)
        {
            var db = DataHelper.GetStoreYaEntities();
            var paymentsTransactions = db.PaymentsTransactions.Where(t => t.ShopId == shopId && t.TransactionType == (int)type && t.SubscriptionId == subscriptionId && t.Status == (int)STATUS.CREATED);
            PaymentsTransaction paymentsTransaction = null;

            if (paymentsTransaction == null)
            {
                //throw new Exception(string.Format("Cannot find Payment Transaction for ShopId:{0}  - SubscriptionId:{1} - Type:{2}", shopId, subscriptionId, type.ToString()));
                return null;
            }
            if (paymentsTransactions.Count() == 1)
            {
                paymentsTransaction = paymentsTransactions.First();
            }
            else
            {
                if (paymentsTransactions.Any(t => t.ScheduledAt.HasValue))
                {
                    paymentsTransaction = paymentsTransactions.SingleOrDefault(t => t.ScheduledAt.Value.Date == DateTime.Now.Date);
                }
                else if (paymentsTransactions.Any(t => t.ParentId.HasValue))
                {
                    paymentsTransaction = paymentsTransactions.OrderBy(t => t.ParentId).First();
                }
                else
                {
                    paymentsTransaction = paymentsTransactions.OrderBy(t => t.InsertedAt).Last();
                }
            }
            if (paymentsTransaction == null)
            {
                //throw new Exception(string.Format("Cannot find Payment Transaction for ShopId:{0}  - SubscriptionId:{1} - Type:{2}", shopId, subscriptionId, type.ToString()));
                return null;
            }
            return paymentsTransaction;
        }

        public static PaymentsTransaction CompleteRecurringPaymentsTransaction(int shopId, TRANSACTIONTYPE type, string subscriptionId, string error = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var paymentsTransactions = db.PaymentsTransactions.Where(t => t.ShopId == shopId && t.TransactionType == (int)type && t.SubscriptionId == subscriptionId && t.Status == (int)STATUS.CREATED);
            PaymentsTransaction paymentsTransaction = null;

            if (paymentsTransaction == null)
            {
                throw new Exception(string.Format("Cannot find Payment Transaction for ShopId:{0}  - SubscriptionId:{1} - Type:{2}", shopId, subscriptionId, type.ToString()));
            }
            if (paymentsTransactions.Count() == 1)
            {
                paymentsTransaction = paymentsTransactions.First();
            }
            else
            {
                if (paymentsTransactions.Any(t => t.ScheduledAt.HasValue))
                {
                    paymentsTransaction = paymentsTransactions.SingleOrDefault(t => t.ScheduledAt.Value.Date == DateTime.Now.Date);
                }
                else if (paymentsTransactions.Any(t => t.ParentId.HasValue))
                {
                    paymentsTransaction = paymentsTransactions.OrderBy(t => t.ParentId).First();
                }
                else
                {
                    paymentsTransaction = paymentsTransactions.OrderBy(t => t.InsertedAt).Last();
                }
            }
            if (paymentsTransaction == null)
            {
                throw new Exception(string.Format("Cannot find Payment Transaction for ShopId:{0}  - SubscriptionId:{1} - Type:{2}", shopId, subscriptionId, type.ToString()));
            }
            paymentsTransaction.UpdatedAt = DateTime.Now;
            if (string.IsNullOrEmpty(error))
            {
                paymentsTransaction.Status = STATUS.DONE.GetHashCode();
            }
            else
            {
                paymentsTransaction.Status = STATUS.FAILED.GetHashCode();
                paymentsTransaction.Comments = paymentsTransaction.Comments + " error:" + error;
            }
            db.SaveChanges();
            return paymentsTransaction;
        }

        public static PaymentsTransaction CancelPaymentsTransaction(int shopId, string subscriptionId, STATUS statusToCancel = STATUS.WAITING_IPN)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                var paymentsTransaction = db.PaymentsTransactions.SingleOrDefault(t => t.ShopId == shopId && t.SubscriptionId == subscriptionId && t.Status == (int)statusToCancel);
                if (paymentsTransaction != null)
                {
                    paymentsTransaction.UpdatedAt = DateTime.Now;
                    paymentsTransaction.Status = STATUS.CANCELLED.GetHashCode();
                    paymentsTransaction.Comments = paymentsTransaction.Comments + " - Cancelled IPN  was recived!";
                    db.SaveChanges();
                    if (paymentsTransaction.ActionType == ACTIONTYPE.PAYPALDISCOUNT.GetHashCode())
                    {
                        CancelPaymentsTransaction(shopId, subscriptionId, STATUS.CREATED);
                    }
                }
                return paymentsTransaction;
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Falied to set cancelled status to PaymentsTransaction", ex, shopId);
            }
            return null;
        }
        public static PaymentsTransaction CompleteWaitingIpnProratedPaymentsTransaction(int shopId, int? agreeId, decimal? amount = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var paymentsTransaction = GetLatestProratedPayment(shopId, 7, agreeId);
            if (paymentsTransaction != null)
            {
                if (paymentsTransaction.Status == STATUS.WAITING_IPN.GetHashCode() && paymentsTransaction.Amount == amount)
                {
                    paymentsTransaction.Status = STATUS.DONE.GetHashCode();
                    paymentsTransaction.UpdatedAt = DateTime.Now;
                    db.SaveChanges();
                }
            }
            return paymentsTransaction;
        }
        public static void ClearOldWaitingIpn(int shopId, int? agreeId)
        {
            bool wasUpdated = false;
            var db = DataHelper.GetStoreYaEntities();
            List<PaymentsTransaction> paymentsTransactions = null;
            if (agreeId.HasValue && agreeId.Value > 0)
            {
                paymentsTransactions = db.PaymentsTransactions.Where(t => t.ShopId == shopId && t.AgreeId == agreeId && t.Status == (int)STATUS.WAITING_IPN).OrderBy(c => c.ID).ToList();
            }
            else
            {
                paymentsTransactions = db.PaymentsTransactions.Where(t => t.ShopId == shopId && (t.AgreeId == null || t.AgreeId == 0) && t.Status == (int)STATUS.WAITING_IPN).OrderBy(c => c.ID).ToList();
            }
            if (paymentsTransactions != null)
            {


                paymentsTransactions = paymentsTransactions.Take(paymentsTransactions.Count() - 1).ToList();
                foreach (var item in paymentsTransactions)
                {
                    wasUpdated = true;
                    item.Status = (int)STATUS.OVERWRITE;
                    item.UpdatedAt = DateTime.Now;
                    item.Comments = $"{item.Comments},Reset Waiting Ipn";
                }
                if (wasUpdated)
                {
                    db.SaveChanges();
                }
            }
        }

        public static PaymentsTransaction CompleteWaitingIpnPaymentsTransaction(int shopId, int? agreeId, string error = null, PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.BlueSnap)
        {
            try
            {
                ClearOldWaitingIpn(shopId, agreeId);
                var db = DataHelper.GetStoreYaEntities();
                PaymentsTransaction paymentsTransaction = null;
                if (agreeId.HasValue && agreeId.Value > 0)
                {
                    paymentsTransaction = db.PaymentsTransactions.SingleOrDefault(t => t.ShopId == shopId && t.AgreeId == agreeId && t.Status == (int)STATUS.WAITING_IPN);
                }
                else
                {
                    paymentsTransaction = db.PaymentsTransactions.SingleOrDefault(t => t.ShopId == shopId && (t.AgreeId == null || t.AgreeId == 0) && t.Status == (int)STATUS.WAITING_IPN);
                }

                if (paymentsTransaction == null)
                {
                    return null;
                }

                paymentsTransaction.UpdatedAt = DateTime.Now;

                if (string.IsNullOrEmpty(error))
                {
                    paymentsTransaction.Status = STATUS.DONE.GetHashCode();
                    if (paymentsTransaction.TransactionType == TRANSACTIONTYPE.BANK_TRANSFER.GetHashCode())
                    {
                        string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
                        EmailHelper.SendEmail(email, string.Format("Bank wire of shopID {0} was cleared", shopId), string.Format("{0}, Wired us a sum of ${1}.The payment was cleared by the bank.", EmailHelper.AdminLinkHref(shopId), paymentsTransaction.Amount));
                    }
                }
                else
                {
                    paymentsTransaction.Status = STATUS.FAILED.GetHashCode();
                    paymentsTransaction.Comments = paymentsTransaction.Comments + " error:" + error;
                    if (paymentsTransaction.TransactionType == TRANSACTIONTYPE.BANK_TRANSFER.GetHashCode())
                    {
                        string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
                        EmailHelper.SendEmail(email, string.Format("Bank wire of shopID {0} was failed to cleared", shopId), string.Format("{0},Failed to Wired us a sum of ${1}.The payment was cleared by the bank.<br/>Error:{2}", EmailHelper.AdminLinkHref(shopId), paymentsTransaction.Amount, error));
                    }
                }
                db.SaveChanges();
                HandelActionedTransaction(paymentsTransaction, paymentProvider);
                Log4NetLogger.InfoWithDB(string.Format("Payment Transaction for subscription:{0} was completed with status:{1} , Id:{2}", paymentsTransaction.SubscriptionId, ((STATUS)paymentsTransaction.Status).ToString(), paymentsTransaction.ID), null, shopId);
                return paymentsTransaction;
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail("<EMAIL>", string.Format("Payment Transaction failed to cleared shopID:{0} ", shopId), string.Format("{0},Failed to Update the payment transaction, AgreeId:{1},<br/>Error:{2}", EmailHelper.AdminLinkHref(shopId), agreeId, ex.ToString()));
            }
            return null;
        }

        public static PaymentsTransaction HandelActionedTransaction(PaymentsTransaction paymentsTransaction, PaymentAdapterTypes paymentProvider)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                BluesnapHelper bluesnapHelper = new BluesnapHelper();
                PaymentsTransaction actionPaymentsTransaction = null;
                if (paymentsTransaction.ScheduledAt.HasValue && paymentsTransaction.ActionType.HasValue
                    && paymentsTransaction.ActionType != ACTIONTYPE.NA.GetHashCode())
                {
                    actionPaymentsTransaction = paymentsTransaction;
                }
                else
                {
                    actionPaymentsTransaction = db.PaymentsTransactions.Where(t => t.ShopId == paymentsTransaction.ShopId
                        && t.Status == (int)STATUS.CREATED
                        && (t.ActionType.HasValue && t.ActionType.Value != (int)ACTIONTYPE.NA)
                        && t.SubscriptionId == paymentsTransaction.SubscriptionId).OrderByDescending(t => t.InsertedAt).FirstOrDefault();
                }
                if (actionPaymentsTransaction == null)
                {
                    return null;
                }
                string chargeDate = string.Empty;
                DateTime dChargeDate = DateTime.Now;
                switch ((ACTIONTYPE)actionPaymentsTransaction.ActionType.Value)
                {
                    case ACTIONTYPE.ONCE:
                        break;
                    case ACTIONTYPE.WEEKLY:
                        break;
                    case ACTIONTYPE.MONTH_1_AND_15:
                        //int daysoffset = 0;
                        //if (actionPaymentsTransaction.TransactionType == TRANSACTIONTYPE.BANK_TRANSFER.GetHashCode())
                        //{
                        //    //Initiate Bank wire 4 days before actual charge date                            
                        //    daysoffset = -4;
                        //}
                        //dChargeDate = CalculateNextPayment1and15(out chargeDate, daysoffset);
                        //if (bluesnapHelper.UpdateBlueSnapSubscription(actionPaymentsTransaction.SubscriptionId, actionPaymentsTransaction.Amount.Value, dChargeDate, actionPaymentsTransaction.ShopId))
                        //{
                        //    string comments = string.IsNullOrEmpty(actionPaymentsTransaction.Comments) ? "MONTH_1_AND_15" : actionPaymentsTransaction.Comments + ",MONTH_1_AND_15";
                        //    CreatePaymentsTransaction(actionPaymentsTransaction.ShopId, actionPaymentsTransaction.UpdatedBy, TRANSACTIONTYPE.UPGRADE, actionPaymentsTransaction.SubscriptionId, paymentsTransaction.Amount.Value, (decimal)actionPaymentsTransaction.Amount, dChargeDate, paymentsTransaction.ChargeDate.Value, comments, actionPaymentsTransaction.AgreeId, PaymentsTransactionHelper.STATUS.WAITING_IPN);
                        //    Log4NetLogger.InfoWithDB(string.Format("UpdateSubscriptionData:1_AND_15 Successfully updated Subscription:{0},amount:{1},nextChargeDate:{2},paymentType:{4} by:{3}", actionPaymentsTransaction.SubscriptionId, actionPaymentsTransaction.Amount, chargeDate, actionPaymentsTransaction.UpdatedBy, TRANSACTIONTYPE.UPGRADE.ToString()), null, actionPaymentsTransaction.ShopId);
                        //}
                        break;
                    case ACTIONTYPE.NA:
                        break;
                    case ACTIONTYPE.PAYPALDISCOUNT:
                        if (actionPaymentsTransaction != null && db.PaymentsTransactions.Any(t => t.ID == actionPaymentsTransaction.ParentId && t.Status == (int)STATUS.DONE))
                        {
                            if (paymentProvider == PaymentAdapterTypes.FastSpring)
                            {
                                new FastSpringPaymentService().SetSubscriptionNextCharge(actionPaymentsTransaction.ShopId, actionPaymentsTransaction.SubscriptionId, actionPaymentsTransaction.ChargeDate.Value, (double)actionPaymentsTransaction.Amount.Value);
                            }
                            else
                            {
                                bluesnapHelper.UpdateBlueSnapSubscription(actionPaymentsTransaction.SubscriptionId, actionPaymentsTransaction.Amount.Value, actionPaymentsTransaction.ChargeDate.Value, actionPaymentsTransaction.ShopId);
                            }
                            actionPaymentsTransaction.Status = STATUS.WAITING_IPN.GetHashCode();
                            actionPaymentsTransaction.UpdatedAt = DateTime.Now;
                            db.SaveChanges();
                            Log4NetLogger.InfoWithDB(string.Format("UpdateSubscriptionData:Successfully updated PAYPALDISCOUNT Subscription:{0},amount:{1},nextChargeDate:{2},paymentType:{4} by:{3} - Provider:{4}", actionPaymentsTransaction.SubscriptionId, actionPaymentsTransaction.Amount, actionPaymentsTransaction.ChargeDate.Value, actionPaymentsTransaction.UpdatedBy, PaymentsTransactionHelper.TRANSACTIONTYPE.UPGRADE.ToString(), paymentProvider), null, actionPaymentsTransaction.ShopId);
                        }
                        break;
                    default:
                        break;
                }
                return paymentsTransaction;
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail("<EMAIL>", string.Format("Payment Transaction failed to run action shopID:{0} ", paymentsTransaction.ShopId), string.Format("{0},Failed to Update the payment transaction, AgreeId:{1},<br/>Error:{2}", EmailHelper.GetBoLinkHref(paymentsTransaction.ShopId, "PaymentsHistory"), paymentsTransaction.AgreeId, ex.ToString()));
            }
            return paymentsTransaction;
        }

        public static IEnumerable<PaymentsTransaction> GetShopPaymentsTransactions(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            var paymentsTransactions = db.PaymentsTransactions.Where(t => t.ShopId == shopId).ToList();
            return paymentsTransactions.OrderByDescending(o => o.InsertedAt);
        }

        public static IEnumerable<PaymentsTransaction> GetPaymentsTransactions(int? status = null, int? transactionType = null, int? actionType = null, DateTime? fromDate = null, DateTime? toDate = null, int? shopId = null, int? agreeId = null, PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.BlueSnap)
        {
            var db = DataHelper.GetStoreYaEntities();

            if (toDate.HasValue)
            {
                toDate = toDate.Value.AddDays(1);
            }
            else
            {
                toDate = DateTime.Now.AddDays(1);
            }
            var p = from c in db.PaymentsTransactions
                    where (status == null ? 1 == 1 : c.Status == status)
                    && (transactionType == null ? 1 == 1 : c.TransactionType == transactionType)
                    && (actionType == null ? 1 == 1 : c.ActionType == actionType)
                    && (fromDate == null ? 1 == 1 : c.ChargeDate > fromDate && c.ChargeDate < toDate)
                    && (shopId == null ? 1 == 1 : c.ShopId == shopId)
                    && ((agreeId == null || agreeId == 0) ? 1 == 1 : c.AgreeId == agreeId)
                    orderby c.ID descending
                    select c;
            return p;
        }

        public static bool HandelPaypalAccountDiscount(int shopId, int userId, string subscriptionId, decimal amount, int? agreeId, PaymentAdapterTypes paymentProvider)
        {
            var db = DataHelper.GetStoreYaEntities();
            var user = db.Users.SingleOrDefault(u => u.ID == userId);
            if (user.UserType == UserTypes.PayPalUS.GetHashCode())
            {
                if (db.PaymentsTransactions.Any(t => t.ShopId == shopId && t.ActionType == (int)ACTIONTYPE.PAYPALDISCOUNT))
                {
                    return false;
                }
                return HandelPaypalAccountDiscountAdmin(shopId, user.Email, subscriptionId, amount, agreeId, paymentProvider);
            }
            return false;
        }

        public static bool HandelPaypalAccountDiscountAdmin(int shopId, string updatedBy, string subscriptionId, decimal amount, int? agreeId, PaymentAdapterTypes paymentProvider)
        {
            string comments = "Paypal Account Discount";
            DateTime dChargeDate = DateTime.Now;
            int parentId = CreatePaymentsTransaction(shopId, updatedBy, PaymentsTransactionHelper.TRANSACTIONTYPE.UPGRADE, subscriptionId, 0, amount, dChargeDate, DateTime.Now, comments, agreeId, PaymentsTransactionHelper.STATUS.DONE, null, PaymentsTransactionHelper.ACTIONTYPE.PAYPALDISCOUNT);
            DateTime dPreviousChargeDate = dChargeDate;
            dChargeDate = DateTime.Now.AddMonths(1);
            if (paymentProvider == PaymentAdapterTypes.BlueSnap)
            {
                new BluesnapHelper().UpdateBlueSnapSubscription(subscriptionId, amount, dChargeDate, shopId);
            }
            else if (paymentProvider == PaymentAdapterTypes.FastSpring)
            {
                new FastSpringPaymentService().SetSubscriptionNextCharge(shopId, subscriptionId, dChargeDate, (double)amount);
            }
            parentId = CreatePaymentsTransaction(shopId, updatedBy, PaymentsTransactionHelper.TRANSACTIONTYPE.UPGRADE, subscriptionId, amount, amount, dChargeDate, dPreviousChargeDate, comments, agreeId, PaymentsTransactionHelper.STATUS.WAITING_IPN, null, PaymentsTransactionHelper.ACTIONTYPE.PAYPALDISCOUNT, parentId);
            dPreviousChargeDate = dChargeDate;
            dChargeDate = DateTime.Now.AddMonths(2);
            decimal previousAmount = amount;
            amount = ConractSettingsHelper.GetTheAmountWithoutDiscount(amount);
            CreatePaymentsTransaction(shopId, updatedBy, PaymentsTransactionHelper.TRANSACTIONTYPE.UPGRADE, subscriptionId, previousAmount, amount, dChargeDate, dPreviousChargeDate, comments, agreeId, PaymentsTransactionHelper.STATUS.CREATED, null, PaymentsTransactionHelper.ACTIONTYPE.PAYPALDISCOUNT, parentId);
            Log4NetLogger.InfoWithDB(string.Format("HandelPaypalAccountDiscount:Successfully updated Subscription:{0},amount:{1},nextChargeDate:{2},paymentType:{4} by:{3}", subscriptionId, amount, dChargeDate, updatedBy, TRANSACTIONTYPE.UPGRADE.ToString()), null, shopId);
            return true;
        }

        public static List<PaymentsTransaction> GetSchdulaedTransactions(DateTime? scheduledAt = null)
        {
            if (!scheduledAt.HasValue)
            {
                scheduledAt = DateTime.Now;
            }
            var db = DataHelper.GetStoreYaEntities();
            List<PaymentsTransaction> paymentsTransactions = db.PaymentsTransactions.Where(t => t.ScheduledAt.HasValue && t.ScheduledAt.Value.Date == scheduledAt.Value.Date).ToList();
            return paymentsTransactions;
        }


        public static void RunOverIPNCalls(int? shopIDforTest = null)
        {
            ConsoleAppHelper.WriteLog("Run Over Payments Transactions and Do required Actions.");

            int lastEventWeCarredAbout = SystemVarHelper.GetValue(VariableTypes.PaymentTransactions_Related_Action);
            var db = DataHelper.GetStoreYaEntities();
            List<PlimusIpnCall> IPNCalls = db.PlimusIpnCalls.Where(i => i.ID > lastEventWeCarredAbout).ToList();
            if (shopIDforTest != null)
            {
                IPNCalls = db.PlimusIpnCalls.Where(i => i.ShopID == shopIDforTest).ToList();// && i.TransactionType == "SUBSCRIPTION_CHARGE_FAILURE").ToList();
            }
            List<int> onIpnActions = new List<int>() {
                ACTIONTYPE.ON_IPN_ALL_CALL.GetHashCode(),
                ACTIONTYPE.ON_IPN_FAILED_CALL.GetHashCode(),
                ACTIONTYPE.ON_IPN_SUCCESS_CALL.GetHashCode(),
            };
            var ipnEventTransactons = db.PaymentsTransactions.Where(t => t.ActionType.HasValue && onIpnActions.Contains(t.ActionType.Value) && t.Status == (int)STATUS.CREATED).ToList();
            StringBuilder sb = new StringBuilder();
            StringBuilder errosSb = new StringBuilder();
            BluesnapHelper bluesnapHelper = new BluesnapHelper();
            FastSpringPaymentService fastSpringPaymentService = new FastSpringPaymentService();
            Dictionary<int, int> uniqueShopIDs = new Dictionary<int, int>();
            try
            {
                ConsoleAppHelper.WriteLog($"Total IPNs Calls:{IPNCalls.Count()}");
                int errorCount = 0;
                foreach (var ipnCall in IPNCalls)
                {
                    PaymentAdapterTypes paymentProvider = FastSpringHelper.ConvertToPaymentProvider(ipnCall.PaymentAdapterType);
                    try
                    {
                        var tb = TrafficBoostersDbHelper.GetSettings(ipnCall.ShopID);
                        if (!uniqueShopIDs.Keys.Contains(ipnCall.ShopID))
                        {
                            uniqueShopIDs.Add(ipnCall.ShopID, ipnCall.ShopID);
                        }
                        var ipnEventTransacton = ipnEventTransactons.SingleOrDefault(t => t.SubscriptionId == ipnCall.subscriptionId && t.ShopId == ipnCall.ShopID);
                        var shop = db.Shops.SingleOrDefault(s => s.ID == ipnCall.ShopID);
                        bool missingShopSubscriptionAlert = false;
                        try
                        {
                            ShopSubscription shopSubscription = null;
                            try
                            {
                                if (string.IsNullOrEmpty(ipnCall.subscriptionId) && paymentProvider == PaymentAdapterTypes.FastSpring)
                                {
                                    errosSb.Append($"{EmailHelper.GetBoLinkHref(ipnCall.ShopID, "subscriptions")}, SubscriptionId:{ipnCall.subscriptionId} - is null - IPN Call: {ipnCall.ToPretyPrint()}<br/>");
                                    continue;
                                }
                                else
                                {
                                    if (paymentProvider == PaymentAdapterTypes.BlueSnap)
                                    {
                                        if (!string.IsNullOrEmpty(ipnCall.subscriptionId))
                                        {
                                            if (int.TryParse(ipnCall.subscriptionId, out int subscriptionId))
                                            {
                                                shopSubscription = db.ShopSubscriptions.Where(s => s.BlueSnapSubscriptionID == subscriptionId).SingleOrDefault();
                                            }
                                            else
                                            {
                                                errosSb.Append($"{EmailHelper.GetBoLinkHref(ipnCall.ShopID, "subscriptions")}, SubscriptionId:{ipnCall.subscriptionId} - is BlueSnap but not a number!!! - IPN Call: {ipnCall.ToPretyPrint()}<br/>");
                                                continue;
                                            }
                                        }
                                    }
                                    else if (paymentProvider == PaymentAdapterTypes.FastSpring)
                                    {
                                        shopSubscription = db.ShopSubscriptions.Where(s => s.FsSubscriptionID == ipnCall.subscriptionId).SingleOrDefault();
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                errosSb.Append($"{EmailHelper.GetBoLinkHref(ipnCall.ShopID, "subscriptions")}, SubscriptionId:{ipnCall.subscriptionId} - There is no ShopSubscription related to this IPN Call: {ipnCall.ToPretyPrint()}<br/> {ex}");
                                continue;
                            }

                            if (ipnCall.AppID.HasValue)
                            {
                                if (shopSubscription == null)
                                {
                                    missingShopSubscriptionAlert = true;
                                }
                                else
                                {
                                    if (ipnCall.AgreeID.HasValue && ipnCall.AgreeID > 0)
                                    {
                                        if (shopSubscription.AgreeID != ipnCall.AgreeID)
                                        {
                                            errosSb.Append($"{EmailHelper.GetBoLinkHref(ipnCall.ShopID, "subscriptions")}, SubscriptionId:{ipnCall.subscriptionId} - ShopSubscription agreeid:{shopSubscription.AgreeID} is not the same as IPN Call AgreeId :{ipnCall.AgreeID}");
                                        }
                                    }
                                    if (shopSubscription.CustomContractType.HasValue
                                        && shopSubscription.CustomContractType == CUSTOM_CONTRACT_TYPE.PAYAT_1AND15.GetHashCode())
                                    {
                                        if (shopSubscription.NextPaymenDate.Value.Subtract(ipnCall.TransactionDate.Value).Days > 16
                                            && !ipnCall.TransactionType.Contains("CANCEL"))
                                        {
                                            errosSb.Append($"{EmailHelper.GetBoLinkHref(ipnCall.ShopID, "subscriptions")}, " +
                                                $"SubscriptionId:{ipnCall.subscriptionId} - ShopSubscription Type:{ipnCall.PaymentMethod} " +
                                                $"is 1st and 15th but next payment is larger then 15 days NextPaymenDate:{shopSubscription.NextPaymenDate} " +
                                                $"IPN Date :{ipnCall.InsertedAt}<br/>");
                                        }
                                    }
                                }
                            }
                            //}
                        }
                        catch (Exception ex)
                        {
                            errosSb.Append($"{EmailHelper.GetBoLinkHref(ipnCall.ShopID, "subscriptions")}, SubscriptionId:{ipnCall.subscriptionId} Faild to validate shopsubscription ex:<pre>{ex}</pre>");
                        }
                        decimal price = 0;
                        decimal.TryParse(ipnCall.contractPrice, out price);
                        if (paymentProvider == PaymentAdapterTypes.FastSpring)
                        {
                            decimal.TryParse(ipnCall.invoiceAmount, out price);
                        }
                        switch (ipnCall.TransactionType)
                        {
                            case "CHARGE":
                                missingShopSubscriptionAlert = false;
                                //case "AUTH_ONLY":
                                //case "UNDER_REVIEW":
                                if (!ipnCall.AgreeID.HasValue || ipnCall.AgreeID == 0)
                                {
                                    if (HandelPaypalAccountDiscount(ipnCall.ShopID, shop.UserID.Value, ipnCall.subscriptionId, price, ipnCall.AgreeID, paymentProvider))
                                    {
                                        //  sb.AppendFormat("Paypal User ({4}) payment to update Transaction for Shopid:{0} subscription id:{1} Transaction Type:{2}, Date:{3}<br/>", EmailHelper.AdminBoShopLinkHref(IPNCall.ShopID, "PaymentsHistory"), IPNCall.subscriptionId, IPNCall.TransactionType, IPNCall.TransactionDate, shop.UserID.Value);
                                    }
                                }
                                var trans = CompleteWaitingIpnProratedPaymentsTransaction(ipnCall.ShopID, ipnCall.AgreeID, price);
                                if (trans != null)
                                {
                                    //sb.AppendFormat("INFO: Prorated charged completed user ({4}) payment to update Transaction for Shopid:{0} subscription id:{1} Transaction Type:{2}, Date:{3}<br/>", EmailHelper.GetBoLinkHref(ipnCall.ShopID, "PaymentsHistory"), ipnCall.subscriptionId, ipnCall.TransactionType, ipnCall.InsertedAt, shop.UserID.Value);
                                }
                                if (ipnCall.PaymentMethod == "ECP")
                                {
                                    //Bank Wire need to get ShopId Plan And Appid                                                
                                    trans = CompleteWaitingIpnPaymentsTransaction(ipnCall.ShopID, ipnCall.AgreeID, null, FastSpringHelper.ConvertToPaymentProvider(ipnCall.PaymentAdapterType));
                                    if (trans != null)
                                    {
                                        sb.AppendFormat("BankWire charge user ({4}) payment to update Transaction for Shopid:{0} subscription id:{1} Transaction Type:{2}, Date:{3}<br/>", EmailHelper.GetBoLinkHref(ipnCall.ShopID, "PaymentsHistory"), ipnCall.subscriptionId, ipnCall.TransactionType, ipnCall.InsertedAt, shop.UserID.Value);
                                    }
                                    TbInternalTasksManager.AddIfNew(ipnCall.ShopID, TbInternalTasksCategories.Bankwire_Payment, "Bank wire received", $"${ipnCall.invoiceAmount} received through a bank wire");
                                }
                                if (ipnEventTransacton != null && ipnEventTransacton.ActionType == ACTIONTYPE.ON_IPN_SUCCESS_CALL.GetHashCode())
                                {
                                    if (paymentProvider == PaymentAdapterTypes.BlueSnap)
                                    {
                                        if (bluesnapHelper.UpdateBlueSnapSubscription(ipnEventTransacton.SubscriptionId, ipnEventTransacton.Amount.Value, ipnEventTransacton.ChargeDate.Value, ipnEventTransacton.ShopId))
                                        {
                                            ipnEventTransacton.Status = STATUS.WAITING_IPN.GetHashCode();
                                            ipnEventTransacton.UpdatedAt = DateTime.Now;
                                        }
                                        else
                                        {
                                            ipnEventTransacton.Status = STATUS.FAILED.GetHashCode();
                                            ipnEventTransacton.UpdatedAt = DateTime.Now;
                                        }
                                        db.SaveChanges();
                                    }
                                    if (paymentProvider == PaymentAdapterTypes.FastSpring)
                                    {
                                        //TODO:FASTSPRING check what needs to do (if needed)
                                    }
                                }
                                if (ipnCall.TransactionDate.HasValue)
                                {
                                    if (IsProrated(ipnCall.ShopID, ipnCall.InsertedAt.Value, ipnCall.subscriptionId, Convert.ToDecimal(ipnCall.invoiceAmount)))
                                    {
                                        var ipnCallToUpdate = db.PlimusIpnCalls.SingleOrDefault(i => i.ID == ipnCall.ID);
                                        ipnCallToUpdate.InternalTransactionType = TRANSACTIONTYPE.PRORATED.ToString();
                                        db.SaveChanges();
                                    }
                                    else
                                    {
                                        if (ipnCall.AppID == 204)//Only TB
                                        {
                                            if (tb != null)
                                            {
                                                //Check if was charge and Last Payment date was not updated 
                                                if (!tb.LastPaymentDate.HasValue || (ipnCall.InsertedAt.HasValue && ipnCall.InsertedAt.Value.Date != tb.LastPaymentDate.Value.Date))
                                                {
                                                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"IPN Charge but TB LastPaymentDate is not the same! {ipnCall.ShopID}", $"{EmailHelper.GetBoLinkHrefAndAM(ipnCall.ShopID)}<BR/>TB LastPaymentDate:{tb.LastPaymentDate}<BR/>PurchasedAt:{tb.PurchasedAt}<BR/>IpnCall:<BR/>{ipnCall.ToJson(true, format: Newtonsoft.Json.Formatting.Indented)}");
                                                }
                                            }
                                        }
                                    }
                                }
                                try
                                {
                                    DateTime startOfTransacationDay = ipnCall.InsertedAt.Value.StartOfDay();
                                    DateTime endOfTransacationDay = ipnCall.InsertedAt.Value.EndOfDay();
                                    var p = db.PaymentsTransactions.Where(s => s.SubscriptionId == ipnCall.subscriptionId
                                    && s.ChargeDate.Value >= startOfTransacationDay
                                    && s.ChargeDate.Value <= endOfTransacationDay
                                    && s.Comments.Contains("Auto-Charge")).OrderByDescending(s => s.ChargeDate).FirstOrDefault();
                                    if (p != null)
                                    {
                                        var ipnCallToUpdate = db.PlimusIpnCalls.SingleOrDefault(i => i.ID == ipnCall.ID);
                                        ipnCallToUpdate.InternalTransactionType = "Auto-Charge";
                                        db.SaveChanges();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    ConsoleAppHelper.WriteErrorWithDB($"Failed to mark PlimusIpnCalls as Auto-Charge", ex, ipnCall.ShopID);
                                }
                                //DashboardAlertsHelper.ChangeActiveAlertsIfPaid(ipnCall.ShopID);
                                CheckMonthlyNextPaymentDate(ipnCall.ShopID, ipnCall.subscriptionId, "CHARGE");
                                AlertIfChargeFrequencyWasChanged(ipnCall.ShopID, ipnCall.subscriptionId, ipnCall.AppID);
                                break;
                            case "RECURRING":
                                if (ipnEventTransacton != null && ipnEventTransacton.ActionType == ACTIONTYPE.ON_IPN_SUCCESS_CALL.GetHashCode())
                                {
                                    if (paymentProvider == PaymentAdapterTypes.BlueSnap)
                                    {
                                        if (bluesnapHelper.UpdateBlueSnapSubscription(ipnEventTransacton.SubscriptionId, ipnEventTransacton.Amount.Value, ipnEventTransacton.ChargeDate.Value, ipnEventTransacton.ShopId))
                                        {
                                            ipnEventTransacton.Status = STATUS.WAITING_IPN.GetHashCode();
                                            ipnEventTransacton.UpdatedAt = DateTime.Now;
                                        }
                                        else
                                        {
                                            ipnEventTransacton.Status = STATUS.FAILED.GetHashCode();
                                            ipnEventTransacton.UpdatedAt = DateTime.Now;
                                        }
                                        db.SaveChanges();
                                    }
                                    if (paymentProvider == PaymentAdapterTypes.FastSpring)
                                    {
                                        //TODO:FASTSPRING check what needs to do (if needed)
                                    }
                                }
                                //DashboardAlertsHelper.ChangeActiveAlertsIfPaid(ipnCall.ShopID);
                                //Handeled on ShopAppsUpdater.ActivateAndSetTbSettings
                                //if charge type is mountly and next payment is less then 14 days alert
                                CheckMonthlyNextPaymentDate(ipnCall.ShopID, ipnCall.subscriptionId, "RECURRING");
                                break;
                            case "CANCELLATION":
                            case "CANCELLATION_REFUND":
                            case "CANCEL_ON_RENEWAL":
                            case "CHARGEBACK":
                            case "DECLINE":
                                if (CancelPaymentsTransaction(ipnCall.ShopID, ipnCall.subscriptionId) != null)
                                {
                                    //  sb.AppendFormat("IPN cancellation, user ({4}) payment to update Transaction for Shopid:{0} subscription id:{1} Transaction Type:{2}, Date:{3}<br/>", EmailHelper.AdminBoShopLinkHref(IPNCall.ShopID, "PaymentsHistory"), IPNCall.subscriptionId, IPNCall.TransactionType, IPNCall.TransactionDate, shop.UserID.Value);
                                }

                                if (ipnEventTransacton != null && ipnEventTransacton.ActionType == ACTIONTYPE.ON_IPN_FAILED_CALL.GetHashCode())
                                {
                                    if (paymentProvider == PaymentAdapterTypes.BlueSnap)
                                    {
                                        bluesnapHelper.UpdateBlueSnapSubscription(ipnEventTransacton.SubscriptionId, ipnEventTransacton.Amount.Value, ipnEventTransacton.ChargeDate.Value, ipnEventTransacton.ShopId);
                                    }
                                    if (paymentProvider == PaymentAdapterTypes.FastSpring)
                                    {
                                        //TODO:FASTSPRING check what needs to do (if needed)
                                    }
                                }
                                if (ipnCall.TransactionType == "CHARGEBACK")
                                {
                                    //add shop data to black list
                                    BlackListManager.AddChargbackedShopToList(ipnCall.ShopID);


                                    if (tb != null && TbSettingsHelper.IsAppActive(tb))
                                    {
                                        string shopBOUrl = EmailHelper.GetBoLinkHrefAndAM(ipnCall.ShopID, out string am, "PaymentsHistory");
                                        EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Chargeback done from the account that has an active TB", $"{shopBOUrl}");
                                    }

                                }
                                if (ipnCall.AgreeID.HasValue && ipnCall.AgreeID.Value > 0)
                                {
                                    ShopAttributesManager shopAttributesManager = new ShopAttributesManager(ipnCall.ShopID);
                                    shopAttributesManager.Update(Attributes.Accounts.Facebook.IgnoreExternalFacebookSpend, "SYSTEM", "Account Cancelled", false);
                                }
                                break;
                            case "SUBSCRIPTION_CHARGE_FAILURE":
                                try
                                {
                                    var failedTransaction = db.PaymentsTransactions.Where(t => t.ShopId == ipnCall.ShopID && t.SubscriptionId == ipnCall.subscriptionId && t.Status == (int)STATUS.WAITING_IPN).SingleOrDefault();
                                    if (failedTransaction != null)
                                    {
                                        // failedTransaction.Status = STATUS.FAILED.GetHashCode();
                                        failedTransaction.Comments = failedTransaction.Comments + " SUBSCRIPTION_CHARGE_FAILURE";
                                        failedTransaction.UpdatedAt = DateTime.Now;
                                        db.SaveChanges();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    errosSb.AppendFormat("IPN SUBSCRIPTION_CHARGE_FAILURE ,Failed to update Transaction for Shopid:{0} subscription id:{1} Transaction Type:{2}, Date:{3}. Exception:{4}<br/>", EmailHelper.GetBoLinkHref(ipnCall.ShopID, "PaymentsHistory"), ipnCall.subscriptionId, ipnCall.TransactionType, ipnCall.InsertedAt, ex.ToString());
                                }

                                NotifyMerchantAndAM(ipnCall);

                                break;

                            case "REFUND":
                                missingShopSubscriptionAlert = false;
                                if (tb != null && TbSettingsHelper.IsAppActive(tb))
                                {
                                    TbInternalTasksManager.AddIfNew(ipnCall.ShopID, TbInternalTasksCategories.RefundIpn, "Refund was wired", $"A ${ipnCall.invoiceAmount} refund was wired, consider overriding the budget if needed.");
                                    sb.AppendFormat("Refund was wired, check user ({4}) payments to update Transaction for Shopid:{0} subscription id:{1} Transaction Type:{2}, Date:{3}<br/>", EmailHelper.GetBoLinkHref(ipnCall.ShopID, "PaymentsHistory"), ipnCall.subscriptionId, ipnCall.TransactionType, ipnCall.InsertedAt, shop.UserID.Value);
                                }
                                //var waitingTransaction = db.PaymentsTransactions.Where(t => t.ShopId == IPNCall.ShopID && t.SubscriptionId == IPNCall.subscriptionId && t.Status == (int)STATUS.WAITING_IPN).SingleOrDefault();
                                //{
                                //    // failedTransaction.Status = STATUS.FAILED.GetHashCode();
                                //    waitingTransaction.Comments = waitingTransaction.Comments + ", Was Refunded";
                                //    waitingTransaction.UpdatedAt = DateTime.Now;
                                //    waitingTransaction.Status = STATUS.OVERWRITE.GetHashCode();
                                //    db.SaveChanges();
                                //}
                                break;
                            default:
                                break;
                        }
                        if (missingShopSubscriptionAlert)
                        {
                            errosSb.Append($"{EmailHelper.GetBoLinkHref(ipnCall.ShopID, "subscriptions")}, SubscriptionId:{ipnCall.subscriptionId} - There is no ShopSubscription related to this IPN Call<br/>");
                        }
                        if (ipnEventTransacton != null && ipnEventTransacton.ActionType == ACTIONTYPE.ON_IPN_ALL_CALL.GetHashCode())
                        {
                            if (paymentProvider == PaymentAdapterTypes.BlueSnap)
                            {
                                if (bluesnapHelper.UpdateBlueSnapSubscription(ipnEventTransacton.SubscriptionId, ipnEventTransacton.Amount.Value, ipnEventTransacton.ChargeDate.Value, ipnEventTransacton.ShopId))
                                {
                                    ipnEventTransacton.Status = STATUS.WAITING_IPN.GetHashCode();
                                    ipnEventTransacton.UpdatedAt = DateTime.Now;
                                }
                                else
                                {
                                    ipnEventTransacton.Status = STATUS.FAILED.GetHashCode();
                                    ipnEventTransacton.UpdatedAt = DateTime.Now;
                                }
                                db.SaveChanges();
                            }
                            if (paymentProvider == PaymentAdapterTypes.FastSpring)
                            {
                                //TODO:FASTSPRING check what needs to do (if needed)
                            }
                        }
                        SystemVarHelper.SetValue(VariableTypes.PaymentTransactions_Related_Action, ipnCall.ID);
                    }
                    catch (Exception ex)
                    {
                        errosSb.AppendFormat("Failed to update Transaction for Shopid:{0} subscription id:{1} Transaction Type:{2}, Date:{3}. Exception:{4}<br/>", EmailHelper.GetBoLinkHref(ipnCall.ShopID, "PaymentsHistory"), ipnCall.subscriptionId, ipnCall.TransactionType, ipnCall.InsertedAt, ex.ToString());
                        Log4NetLogger.Error(string.Format("Failed to update Transaction for Shopid:{0} subscription id:{1} Transaction Type:{2}, Date:{3}. Exception:{4}", ipnCall.ShopID, ipnCall.subscriptionId, ipnCall.TransactionType, ipnCall.InsertedAt, ex.ToString()), ex, ipnCall.ShopID);
                        errorCount++;
                        if (errorCount > 9)
                        {
                            throw new Exception("There were more then 10 errors. Please handel the exceptions.");
                        }
                    }
                }

                //update DB data for all shops that had changes
                foreach (var sid in uniqueShopIDs)
                {
                    try
                    {
                        SubscriptionManager.UpdateNextPaymentInAllSubscriptions(sid.Key);
                    }
                    catch (Exception ex)
                    {
                        if (!ex.ToString().Contains("The remote server returned an error: (403)"))
                        {
                            errosSb.AppendFormat("Failed to update UpdateNextPaymentInAllSubscriptions for shopid:{0} general error :{1}", sid, ex.ToString());
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                errosSb.AppendFormat("Failed to update Transactions general error :{0}", ex.ToString());
            }
            finally
            {
                if (!string.IsNullOrEmpty(errosSb.ToString()) || !string.IsNullOrEmpty(sb.ToString()))
                {
                    string hasErrors = "";
                    string errors = "";
                    if (!string.IsNullOrEmpty(errosSb.ToString()))
                    {
                        errors = "<br/><b>Errors:</b><br/>" + errosSb.ToString();
                        hasErrors = " - Check errors.";
                    }
                    string body = sb.ToString() + errors;
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, "Payment Transaction IPN calls handler " + hasErrors, body);
                }
                ConsoleAppHelper.WriteLog($"IPNs Calls Handler Done");
            }
        }
        public static void AlertIfChargeFrequencyWasChanged(int shopId, string bsSubscriptionId, int? appId = null)
        {

            string appIDs = appId.HasValue ? appId.Value.ToString() : "";
            var db = DataHelper.GetStoreYaEntities();
            var shopSubs = db.ShopSubscriptions.Where(i => i.ShopID == shopId && i.AppIDs == appIDs).OrderByDescending(s => s.InsertedAt).Take(2).Select(s => new { s.ShopID, s.InsertedAt, s.OriginalSubscriptionID, s.BsStatus, s.ChargeFrequency, s.ContractID, s.NextPaymenDate }).ToList();
            if (shopSubs.Count() > 1 && shopSubs.Last().ChargeFrequency.ToLower() != shopSubs.First().ChargeFrequency.ToLower())
            {
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Subscription charge frequency was changed  {shopId}", $"{EmailHelper.GetBoLinkHrefAndAM(shopId)}<br/>Need to check subscrptions: {shopSubs.ListToHtmlTable()}");
            }

        }
        public static bool CheckMonthlyNextPaymentDate(int shopId, string bsSubscriptionId, string ipnType)
        {
            try
            {
                SubscriptionManager.UpdateNextPaymentInAllSubscriptions(shopId);
                var db = DataHelper.GetStoreYaEntities();
                DateTime days = DateTime.Now.AddDays(14);
                var shopSub = FastSpringHelper.GetShopSubscription(shopId, bsSubscriptionId);
                if (shopSub != null)
                {
                    if (shopSub.CustomContractType.HasValue && shopSub.CustomContractType == CUSTOM_CONTRACT_TYPE.PAYAT_1AND15.GetHashCode())
                    {
                        //   DateTime subContractDate = CalculateNextPayment1and15(out string chargeDate);
                        return false;
                    }
                    if (shopSub.ChargeFrequency != null && shopSub.ChargeFrequency.ToUpper() == "MONTHLY"
                        && shopSub.NextPaymenDate.HasValue && shopSub.NextPaymenDate.Value < days && shopSub.NextPaymenDate.Value.Date != DateTime.Now.Date)
                    {

                        EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Monthly subscriptions check next payment date: {shopId} , sub:{bsSubscriptionId}", $"{EmailHelper.GetBoLinkHrefAndAM(shopId)},<br/>{ipnType} Next Payment date:{shopSub.NextPaymenDate} is less then 14 days need to check subscription id:{bsSubscriptionId} ");
                        return true;
                    }
                }

            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Monthly subscriptions check next payment date failed: {shopId} , sub:{bsSubscriptionId}", $"{EmailHelper.GetBoLinkHrefAndAM(shopId)},<br/>{ex}");
            }
            return false;
        }
        public static void NotifyFailedChargeTest(double dayspassed)
        {
            int testShopID = 238;
            DateTime nextPaymenDate = DateTime.Now.AddDays(-dayspassed);
            PlimusIpnCall ipnCall = new PlimusIpnCall() { email = "<EMAIL>", NextPaymenDate = nextPaymenDate, ShopID = testShopID, AppID = 200, TransactionDate = DateTime.Now };
            IPNCallHelper.ReportIpnAsFailedChargeEventTest(testShopID);
            NotifyMerchantAndAM(ipnCall, testRun: true);
        }
        public static void NotifyFailedChargeTest(PlimusIpnCall ipnCall)
        {
            NotifyMerchantAndAM(ipnCall);
        }
        private static void NotifyMerchantAndAM(PlimusIpnCall ipnCall, bool testRun = false)
        {
            if (ipnCall.ShopID == 0)
            {
                //old IPNs without shopID
                return;
            }
            try
            {
                ConsoleAppHelper.WriteErrorWithDB($"NotifyMerchantAndAM - failed charge", null, ipnCall.ShopID);
                string subject = "[Action needed] Recurring Payment Failed";
                string body = null;
                int? numberOfTheLetter = null;
                int planID = 0;
                if (!testRun)
                {
                    planID = GetPlanIDFromSubascription(ipnCall) ?? 0;
                }
                numberOfTheLetter = DetermineTheFailedChargeLetterNumberByIpnCall(ipnCall, out double daysHavePassedSinceTheDateOfPayment);
                bool emailAlreadySent = CheckIfEmailAlreadySent(numberOfTheLetter, ipnCall.ShopID, ipnCall.AppID.Value, out int? previouseNumber);
                if (emailAlreadySent)
                {
                    return;
                }
                if (ipnCall.AppID == (int)AppTypes.TrafficBooster)
                {
                    planID = 20;
                }
                string paymentLink = $"<a href='https://www.storeya.com/public/Checkout?shopId={ipnCall.ShopID}&planID={planID}&appID={ipnCall.AppID}&methodID=0&item={ipnCall.NextPaymentAmount}'>link.<a>";
                switch (numberOfTheLetter)
                {
                    case 1:
                        body = Storeya.Core.Properties.Resources.FailedChargedEmail1.Replace("[link]", paymentLink);
                        break;
                    case 2:
                        subject = "[Action needed] Your StoreYa Account was Paused";
                        body = Storeya.Core.Properties.Resources.FailedChargedEmail2.Replace("[link]", paymentLink);
                        break;
                    case 3:
                        body = Storeya.Core.Properties.Resources.FailedChargedEmail3.Replace("[link]", paymentLink);
                        break;
                    case 4:
                        body = Storeya.Core.Properties.Resources.FailedChargedEmail4.Replace("[link]", paymentLink);
                        subject = "[Your Action is Needed] Recurring Payment Failed";
                        break;
                    case 5:
                        body = Storeya.Core.Properties.Resources.FailedChargedEmail5.Replace("[link]", paymentLink);
                        subject = "[Action Required] Google Ads Account is Going to be Cancelled";
                        break;
                    default:
                        if (daysHavePassedSinceTheDateOfPayment > 30)
                        {

                            EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Failed to determine mail number failedCharge", $"{daysHavePassedSinceTheDateOfPayment} days have passed since ipnCall.NextPaymenDate. <a href='https://bo.storeya.com/Shop/Details/{ipnCall.ShopID}'>Shop link</a>");
                        }
                        else if (daysHavePassedSinceTheDateOfPayment <= 0)
                        {
                            EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Failed to determine mail number failedCharge", $"{daysHavePassedSinceTheDateOfPayment} days have passed since ipnCall.NextPaymenDate - logic error occurred:" + ipnCall.ToJson());
                        }
                        return;
                }



                ConsoleAppHelper.WriteLogWithDB($"Sending failed charge notification email{numberOfTheLetter}. AppID = {ipnCall.AppID}", ipnCall.ShopID);
                var db = DataHelper.GetStoreYaEntities();
                //TODO: change to use resource
                var email = ShopsHelper.GetUserEmail(ipnCall.ShopID);
                if (ConfigHelper.GetBoolValue("SendToDevFailedChargeEmails"))
                {
                    body += $"<br/><br/> <a href=\"https://bo.storeya.com/Shop/Details/{ipnCall.ShopID}\">ShopID={ipnCall.ShopID}.<a>. Email: {ipnCall.email}";
                    email = EmailHelper.DEV_EMAIL;
                }
                var user = ShopsHelper.GetUser(ipnCall.ShopID);
                SystemEventHelper.Add(user.ID, ipnCall.ShopID, ipnCall.AppID, SystemEventTypes.IPNCalls, SystemEventActions.FailedChargeEmailSent, label: numberOfTheLetter.ToString());
                EmailHelper.SendEmail(email, subject, body);
                //SystemEventHelper.Add(ipnCall.ShopID, (int)AppTypes.None, SystemEventTypes.IPNCalls, SystemEventActions.ChargeFailed, label: null);
                if (ipnCall != null && ipnCall.AppID == (int)AppTypes.TrafficBooster)
                {
                    CreateFailedChargeDashboardAlert(ipnCall);
                }
                if (numberOfTheLetter == 1)
                {
                    if (user != null && GoodAccountHelper.IsGoodRevenue(user.RevenueRank ?? 0))
                    {
                        string budget = "Google budget";
                        if (ipnCall.AgreeID > 0)
                        {
                            budget = "Facebook budget";
                        }

                        TbInternalTasksManager.AddIfNew(ipnCall.ShopID, TbInternalTasksCategories.Subscription_Charge_Failed,
                            "Your account has failed to charge on the -" + ipnCall.InsertedAt.ToString(), $"Failed to charge {budget} Subscription {ipnCall.subscriptionId}", null);
                    }
                }
            }
            catch (Exception ex)
            {
                var shopLinck = EmailHelper.GetBoLinkHref(ipnCall.ShopID);
                var insertedAtAtIpnCall = ipnCall.InsertedAt;
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "An error while NotifyMerchantAndAM run", $"See IPNCall for {shopLinck}, inserted at: {insertedAtAtIpnCall}<br/>" + ex.ToString());
            }

        }

        private static int? GetPlanIDFromSubscription(PlimusIpnCall ipnCall)
        {
            try
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                var sub = db.ShopSubscriptions.Where(x => x.ShopID == ipnCall.ShopID && x.OriginalSubscriptionID == ipnCall.subscriptionId && x.AppIDs == ipnCall.AppID.ToString() && x.Status == (int)SUBSCRIPTION_STATUS.ACTIVE).SingleOrDefault();
                if (sub != null)
                {
                    return int.Parse(sub.PlanIDs);
                }
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "GetPlanIDFromSubscription issue", $"ShopID:{ipnCall.ShopID}, subscriptionId:{ipnCall.subscriptionId}, ipnCallID:{ipnCall.ID}. Can't find ShopSubscription for this shop. Link was created with planID = 0.");
                return 0;
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "GetPlanIDFromSubscription failed", $"ShopID:{ipnCall.ShopID}, subscriptionId:{ipnCall.subscriptionId}, ipnCallID:{ipnCall.ID}. Link was created with planID=0. {ex.ToString()}");
            }
            return null;
        }

        private static bool CheckIfEmailAlreadySent(int? numberOfTheLetter, int shopID, int appID, out int? previouseNumber)
        {
            previouseNumber = GetNumberOfThePreviouseEmailFromEvent(shopID, appID);
            if (previouseNumber == null || numberOfTheLetter == null)
            {
                return false;
            }
            else if (previouseNumber < numberOfTheLetter)
            {
                return false;
            }
            else
            {
                return true;
            }
        }
        private static int? GetNumberOfThePreviouseEmailFromEvent(int shopID, int appID)
        {
            try
            {
                SystemEvent sysEvent = SystemEventHelper.GetLatest(shopID, appID, SystemEventTypes.IPNCalls, SystemEventActions.FailedChargeEmailSent, daysToSkipIfExists: 21);
                if (sysEvent != null)
                {
                    return int.Parse(sysEvent.Label);
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "GetNumberOfThePreviouseEmailFromEvent failed", $"ShopID:{shopID}, appID:{appID}. {ex.ToString()}");
            }
            return null;
        }

        public static int? DetermineTheFailedChargeLetterNumberByIpnCall(PlimusIpnCall ipnCall, out double daysHavePassedSinceTheDateOfPayment)
        {
            daysHavePassedSinceTheDateOfPayment = 0;
            DateTime transactionDate = ipnCall.TransactionDate == null ? ipnCall.InsertedAt.Value : ipnCall.TransactionDate.Value;
            DateTime nextPaymentDate;

            if (ipnCall.NextPaymenDate == null)
            {
                return null;
            }
            nextPaymentDate = ipnCall.NextPaymenDate.Value;
            daysHavePassedSinceTheDateOfPayment = (transactionDate - nextPaymentDate).TotalDays;
            if (daysHavePassedSinceTheDateOfPayment < 0)
            {
                return null; //it's not expired
            }
            else if (daysHavePassedSinceTheDateOfPayment <= 2)
            {
                return 1;
            }
            else if (daysHavePassedSinceTheDateOfPayment > 2 && daysHavePassedSinceTheDateOfPayment < 4)
            {
                return 2;
            }
            else if (daysHavePassedSinceTheDateOfPayment >= 4 && daysHavePassedSinceTheDateOfPayment < 10)
            {
                return 3;
            }
            else if (daysHavePassedSinceTheDateOfPayment >= 10 && daysHavePassedSinceTheDateOfPayment < 15)
            {
                return 4;
            }
            else if (daysHavePassedSinceTheDateOfPayment >= 15 && daysHavePassedSinceTheDateOfPayment < 20)
            {
                return 5;
            }
            return null; //too late :)
        }

        public static void CreateFailedChargeDashboardAlert(PlimusIpnCall ipnCall)
        {
            string title = "Your Payment Failed";
            string paymentLink = $"<a href='https://www.storeya.com/public/Checkout?shopId={ipnCall.ShopID}&planID=20&appID=204&methodID=0&item={ipnCall.NextPaymentAmount}'>link.<a>";
            string subject = Storeya.Core.Properties.Resources.FailedChargeAlert.Replace("[link]", paymentLink);

            DashboardAlertsHelper.CreateDashboardAlertIfItDoesNotExist(ipnCall.ShopID, title, subject, (int)Storeya.Core.Entities.DashboardNotificationAlertType.FailedCharge, active: true, validUntilInDays: 45);
        }
        public static void SendFailedChargeEmails2(bool test = false) //old function, using only for BS
        {
            PlimusIpnCall currentIpn = null;
            try
            {

                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                var firstEvent = SystemVarHelper.GetValue(VariableTypes.PaymentFailed);
                var events = SystemEventHelper.Get(firstEvent, SystemEventTypes.IPNCalls, SystemEventActions.ChargeFailed);
                if (events != null && !test)
                {
                    events = events.Where(x => x.InsertedAt <= DateTime.Now.AddDays(-2)).OrderBy(x => x.ID).ToList();
                }
                if (events != null && events.Count() > 0)
                {
                    foreach (var systemEvent in events)
                    {

                        TbAppManager tb = new TbAppManager(systemEvent.ShopID.Value);
                        if (tb.IsAppActive)
                        {

                            PlimusIpnCall latestFailedIpnCall = IPNDataHelper.GetLastIpnCallByShopIDAndTransactionType(systemEvent.ShopID.Value, "SUBSCRIPTION_CHARGE_FAILURE", systemEvent.AppID);
                            currentIpn = latestFailedIpnCall;
                            if (FastSpringHelper.ConvertToPaymentProvider(currentIpn.PaymentAdapterType) == PaymentAdapterTypes.BlueSnap)
                            {
                                int shopID = systemEvent.ShopID.Value;
                                bool emailAlreadySent = CheckIfEmailAlreadySent(numberOfTheLetter: 2, shopID, systemEvent.AppID.Value, out int? previouseNumber);
                                if (emailAlreadySent && previouseNumber != null)
                                {
                                    continue;
                                }
                                DateTime? lastPaymentDate = null;
                                var tbSettings = TrafficBoostersDbHelper.GetSettings(systemEvent.ShopID.Value);
                                if (tbSettings != null)
                                {
                                    lastPaymentDate = tbSettings.LastPaymentDate;
                                }
                                if (lastPaymentDate.HasValue && lastPaymentDate < latestFailedIpnCall.NextPaymenDate || test)
                                {
                                    string subject = "[Action needed] Your StoreYa Account was Paused";
                                    int planId = GetPlanIDFromLable(systemEvent);
                                    string paymentLink = $"<a href='https://www.storeya.com/public/Checkout?shopId={latestFailedIpnCall.ShopID}&planID={planId}&appID={systemEvent.AppID}&methodID=0&item={latestFailedIpnCall.NextPaymentAmount}'>link.<a>";
                                    string body = Storeya.Core.Properties.Resources.FailedChargedEmail2.Replace("[link]", paymentLink);
                                    var email = ShopsHelper.GetUserEmail(shopID);
                                    if (ConfigHelper.GetBoolValue("SendToDevFailedChargeEmails"))
                                    {
                                        email = EmailHelper.DEV_EMAIL;
                                    }

                                    EmailHelper.SendEmail(email, subject, body);
                                    SystemEventHelper.Add(systemEvent.UserID.Value, systemEvent.ShopID.Value, systemEvent.AppID, SystemEventTypes.IPNCalls, SystemEventActions.FailedChargeEmailSent, label: "2");
                                    ConsoleAppHelper.WriteLog($"Sending failed charge notification email2.", shopID);
                                    //The systemVar will be rewritten in loop just in case an error occurs in the next step 
                                    SystemVarHelper.SetValue(VariableTypes.PaymentFailed, systemEvent.ID);
                                }
                            }
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                if (currentIpn != null)
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, "SendFailedChargeEmails2 method error", $"{EmailHelper.GetBoLinkHrefAndAM(currentIpn.ShopID)}<br/> {currentIpn.ToJson(format: Newtonsoft.Json.Formatting.Indented)}<br/> " + ex.ToString());
                }
                else
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, "SendFailedChargeEmails2 method error", ex.ToString());
                }

            }
        }

        private static int GetPlanIDFromLable(SystemEvent systemEvent)
        {
            try
            {
                string[] args = systemEvent.Label.Split('_');
                int planID = int.Parse(args[1]);
                return planID;
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "GetPlanIDFromLable failed. Second failed charge email was sent with planID=0", $"ShopID: {systemEvent.ShopID}, label: {systemEvent.Label}, systemEvent id: {systemEvent.ID}. {ex.ToString()}");
            }
            return 0;
        }

        public static DateTime CalculateNextPayment1and15(out string chargeDate, int daysOffset = 0, DateTime? checkedAt = null)
        {
            DateTime today = DateTime.Now.Date;
            if (checkedAt != null)
            {
                today = checkedAt.Value;
            }
            int thisDay = today.Day;
            int rangeEnd = 15;
            if (daysOffset != 0)
            {
                int rangeStart = DateTime.DaysInMonth(today.Year, today.Month) + daysOffset;
                rangeEnd = rangeEnd + daysOffset;
                if (thisDay > 0 && thisDay < 15 && rangeEnd > thisDay)
                {
                    chargeDate = string.Format("{0}-{1}", rangeEnd.ToString("D2"), today.ToString("MMM-yy"));
                    return DateTime.Parse(chargeDate);
                }
                else
                {
                    if (thisDay > rangeStart)
                    {
                        chargeDate = string.Format("{0}-{1}", rangeEnd.ToString("D2"), today.AddMonths(1).ToString("MMM-yy"));
                        return DateTime.Parse(chargeDate);
                    }
                    else
                    {
                        chargeDate = string.Format("{0}-{1}", rangeStart.ToString("D2"), today.ToString("MMM-yy"));
                        return DateTime.Parse(chargeDate);
                    }
                }
            }
            else
            {
                if (thisDay >= 1 && thisDay < 15)
                {
                    chargeDate = string.Format("15-{0}", DateTime.Now.ToString("MMM-yy"));
                    return DateTime.Parse(chargeDate);
                }
                else
                {
                    chargeDate = string.Format("01-{0}", DateTime.Now.AddMonths(1).ToString("MMM-yy"));
                    return DateTime.Parse(chargeDate);
                }

            }
        }

        public static void ReportOnUpcomingDowngrades(int? shopId = null)
        {
            DateTime nextThursday = DateTime.Today.GetNextDateForDay(DayOfWeek.Thursday).AddHours(15);
            DateTime nextSunday = nextThursday.Date.AddDays(3).AddHours(9);
            var db = DataHelper.GetStoreYaEntities();
            var transactions = db.PaymentsTransactions.Where(t => t.Status == (int)STATUS.WAITING_IPN
                                && t.TransactionType == (int)TRANSACTIONTYPE.UPGRADE
                                && t.Amount < t.PreviousAmount
                                && t.ChargeDate > nextThursday && t.ChargeDate <= nextSunday

                                && (shopId.HasValue ? t.ShopId == shopId : 1 == 1)).ToList();
            foreach (var item in transactions)
            {
                string time = "";
                if (item.ChargeDate.Value.DayOfWeek == DayOfWeek.Thursday || item.ChargeDate.Value.DayOfWeek == DayOfWeek.Sunday)
                {
                    time = $"at {item.ChargeDate.Value.Hour}:{item.ChargeDate.Value.Minute}";
                }
                TbInternalTasksManager.AddIfNew(item.ShopId, TbInternalTasksCategories.BudgetDowngrade, "Downgrade is scheduled for the upcoming weekend", $"Please adjust the budget<br/>Charge:{item.Amount}$, Previous:{item.PreviousAmount}$, Charge Date:{item.ChargeDate.Value.ToString("dd/MM/yyyy")} {time}");
            }


        }
    }
}
