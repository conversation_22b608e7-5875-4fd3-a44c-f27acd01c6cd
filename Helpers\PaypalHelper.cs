﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Entities;
using System.Collections.Specialized;
using System.Web;

namespace Storeya.Core.Helpers
{
    public class PaypalHelper
    {
        public static void SetPaypalAgencyID(int userID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var user = db.Users.Where(u => u.ID == userID).SingleOrDefault();
            if (user != null)
            {
                user.AgencyID = 195;
                db.SaveChanges();
            }
        }
        public static void FixPayPalUserEmail(string payerID, string email, int userId)
        {
            if (!string.IsNullOrEmpty(payerID))
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                var papaluser = db.PaypalConnectedUsers.FirstOrDefault(u => u.PayerID == payerID);
                if (papaluser != null)
                {
                    if (string.IsNullOrEmpty(papaluser.Email))
                    {
                        papaluser.Email = email;
                        db.SaveChanges();
                        EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Paypal User Email was fixed Userid:{userId}", $"PayPal PayerID:{payerID} ,Email:{email},papaluserId:{papaluser.ID}");
                    }
                }
            }
        }
    }
}
