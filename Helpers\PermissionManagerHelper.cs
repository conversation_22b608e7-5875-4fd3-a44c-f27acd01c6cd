﻿using Storeya.Core.Entities;
using Storeya.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class PermissionManagerHelper
    {

        public static void SetUserPermissions(int userID, int shopID, Permissions permissionsPlan)
        {
            ShopManager.SetUserPermissions(userID, shopID, permissionsPlan);
            //StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //if (db.UserPermissions.Any(p => p.UserID == userID && p.ShopID == shopID))
            //{
            //    var perm = db.UserPermissions.Single(p => p.UserID == userID && p.ShopID == shopID);
            //    perm.Permissions = (int)permissionsPlan;
            //}
            //else
            //{
            //    db.AddToUserPermissions(new UserPermission() { Permissions = (int)permissionsPlan, ShopID = shopID, UserID = userID });
            //}

            //db.SaveChanges();
        }
        public static Permissions GetUserPermissions(int userID, int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            if (db.UserPermissions.Any(p => p.UserID == userID && p.ShopID == shopID))
            {
                var perm = db.UserPermissions.Single(p => p.UserID == userID && p.ShopID == shopID);
                return (Permissions)perm.Permissions;
            }
            return (Permissions)0;
        }
        public static void SetShopPermissionsByPlanNoSessions(int shopID, int planID, int ownerUserID = 0)
        {
            Permissions newPermission = PermissionsHelper.PlanToPermission(planID);

            //get owner permissions - if owner agency - leave all permissions
            Permissions existingOwnerPermission = GetUserPermissions(ownerUserID, shopID);

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            User user = db.Users.Where(u => u.ID == ownerUserID).SingleOrDefault();

            SetUserPermissions(ownerUserID, shopID, newPermission);
           // SessionHelper.ReloadUserInfo();

            if (user != null && user.AgencyID.HasValue && user.AgencyID > 0)
            {
                //get other shop admins
                List<ShopAdmin> admins = ShopManager.GetShopAdmins(shopID);

                foreach (var admin in admins)
                {
                    SetUserPermissions(admin.UserID, shopID, newPermission);
                }
            }
        }
    }
}
