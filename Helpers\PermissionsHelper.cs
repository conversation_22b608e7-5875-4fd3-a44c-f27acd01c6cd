﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Entities;
using Storeya.Core.Models.Payments;

namespace Storeya.Core.Helpers
{
    public class PermissionsHelper
    {
        public static Permissions PlanToPermission(int planID)
        {
            Permissions userPermission = Permissions.PlanFirstClass;
            PlanTypes planType = (PlanTypes)planID;

            switch (planType)
            {
                case PlanTypes.FreePlan:
                    userPermission = Permissions.PlanFreemium;
                    break;

                case PlanTypes.EconomyVeteran:
                case PlanTypes.Economy:
                    userPermission = Permissions.PlanEconomy;
                    break;
                case PlanTypes.BusinessVeteran:
                case PlanTypes.Business:
                    userPermission = Permissions.PlanBusiness;
                    break;
                case PlanTypes.FirstClassVeteran:
                case PlanTypes.FirstClass:
                case PlanTypes.FirstClass_2:
                    userPermission = Permissions.PlanFirstClass;
                    break;
                case PlanTypes.PrivateJetVeteran:
                case PlanTypes.PrivateJet:
                    userPermission = Permissions.PlanPrivateJet;
                    break;
                //case PlanTypes.Agency:
                //    userPermission = Permissions.PlanAgency;
                //    break;
                case PlanTypes.EtsyPro:
                    userPermission = Permissions.PlanEtsyPro;
                    break;
                case PlanTypes.Tuxedo:
                    userPermission = Permissions.PlanPrivateJet;
                    break;
                default:
                    userPermission = Permissions.PlanEconomy;
                    break;
            }

            return userPermission;
        }
    }
}
