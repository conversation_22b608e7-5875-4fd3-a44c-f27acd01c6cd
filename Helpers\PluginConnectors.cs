﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Helpers.PluginHelper
{
    public class OAuthUtil : Storeya.Core.Helpers.OAuthBase
    {
        public string Url;
        public string Params;
        public string Base;
        public string Signature;
        public string RequestString;

        public OAuthUtil(string url, string method, string consumerKey = "", string token = "", string tokenSecret = "", string customParams = "", string timestamp = "", string nonce = "", string signatureType = "")
        {
            if (String.IsNullOrEmpty(nonce)) nonce = base.GenerateNonce();
            if (String.IsNullOrEmpty(timestamp)) timestamp = base.GenerateTimeStamp();

            Base = base.GenerateSignatureBase(new Uri(url), consumerKey, token, tokenSecret, method, customParams, timestamp, nonce, signatureType, out Url, out Params);

            Signature = base.GenerateSignatureFromBase(Base, tokenSecret, "");

            RequestString = String.Format("{0}?{1}&oauth_signature={2}", Url, Params, UrlEncode(Signature));
        }
    }

    public static class PluginConnectors
    {
        public static PluginConnectorModel OpenCart(string url, string secret, string appkey, string action = "GetOrders")
        {
            url = url.Replace("index.php", "");

            if (url.LastIndexOf("/") != url.Length - 1) url += "/";

            OAuthUtil auth = new OAuthUtil(url, "GET", appkey, "", secret, "route=module/storeyajson/" + action, "", "", "HMAC-SHA1");

            return new PluginConnectorModel()
            {
                QueryString = auth.RequestString
            };
        }

        public static PluginConnectorModel WooCommerce(string url, string secret, string appkey, string action = "products")
        {
            if (url.LastIndexOf("/") != url.Length - 1) url += "/";
            OAuthUtil auth = new OAuthUtil(url, "GET", appkey, "", secret, "storeya-api-route=/" + action, "", "", "HMAC-SHA1");

            return new PluginConnectorModel()
            {
                QueryString = auth.RequestString
            };
        }

        public static PluginConnectorModel PrestaShop()
        {
            return new PluginConnectorModel()
            {
                QueryString = ""
            };
        }

        public static PluginConnectorModel Default(string url)
        {
            return new PluginConnectorModel()
            {
                QueryString = url
            };
        }

    }

    public class PluginConnectorModel
    {
        public string QueryString;
    }
}
