﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Storeya.Core.Helpers
{
    public static class PriceParser
    {
        public static double GetDouble(string price, string decimalSeparator = null)
        {
            double result = 0;

            if (string.IsNullOrWhiteSpace(price))
            {
                return 0;
            }

            if (price.Contains("$"))
                price = price.Replace("$", "");
            else if (price.Contains("£"))
                price = price.Replace("£", "");

            price = price.Trim();

            if (decimalSeparator == null && price.Contains(".") && !price.Contains(","))
            {
                //16.800 - format when 3.000 is 3K and not 3
                string[] part = price.Split('.');
                if (part.Length == 2 && part[1].Length == 3)
                {
                    price = price.Replace(".", "");
                }
            }

            if (price.Contains(' ')) //14.95 USD       //$15.95 ??
            {
                string[] splitted = price.Split(' ');
                price = splitted[0];
            }

            result = double.Parse(price);

            result = Math.Round(result, 2, MidpointRounding.AwayFromZero);

            return result;
        }

        public static PriceAndCurrency GetPriceAndCurrency(string price)
        {
            PriceAndCurrency priceAndCurrencyData = new PriceAndCurrency();

            if (price.Contains(' ')) //14.95 USD      
            {
                string[] splitted = price.Split(' ');

                double extractedPrice = ExtractPriceWithRegex(splitted[0]);
                if (extractedPrice != 0)
                {
                    priceAndCurrencyData.Price = extractedPrice;
                    priceAndCurrencyData.Currency = splitted[1];
                }
                else
                {
                    priceAndCurrencyData.Currency = splitted[0];
                    priceAndCurrencyData.Price = ExtractPriceWithRegex(splitted[1]);
                }
            }
            else
            {
                priceAndCurrencyData= ExtractPriceAndCurrecnyNoSpace(price);
            }

            return priceAndCurrencyData;
        }

        public static double ExtractPriceWithRegex(string priceString)
        {
            double extractedPrice = 0;
            Regex regNumber = new Regex(@"\b\d[\d,.]*\b", RegexOptions.IgnoreCase);
            if (regNumber.Match(priceString).Success)
            {
                string number = regNumber.Match(priceString).ToString();
                double price;
                bool isConverted = double.TryParse(number, out price);
                if (isConverted)
                    price = Math.Round(price, 2, MidpointRounding.AwayFromZero);
                extractedPrice = price;
            }
            return extractedPrice;
        }

        public static PriceAndCurrency ExtractPriceAndCurrecnyNoSpace(string priceString)
        {
            PriceAndCurrency priceAndCurrency = new PriceAndCurrency();
            string alphabets = string.Empty;
            string numbers = string.Empty;

            foreach (char item in priceString)
            {
                if (Char.IsLetter(item))
                    alphabets += item;
                if (item == '.' || Char.IsNumber(item))
                    numbers += item;
            }

            string number = numbers;
            double price;
            bool isConverted = double.TryParse(number, out price);
            if (isConverted)
                price = Math.Round(price, 2, MidpointRounding.AwayFromZero);
            priceAndCurrency.Price = price;
            priceAndCurrency.Currency = alphabets;

            return priceAndCurrency;
        }

    }

    public class PriceAndCurrency
    {
        public double Price { get; set; }
        public string Currency { get; set; }
    }
}

