﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
namespace Storeya.Core.Helpers
{ 

    public class ProcessHelper
    {
        public static bool EnsureExeIsRunningAsAdmin(string exeName, string exeFullPath)
        {
            try
            {
                string processName = Path.GetFileNameWithoutExtension(exeName);

                // Check if process is already running
                bool isRunning = Process.GetProcessesByName(processName).Any();

                if (isRunning)
                {
                    Console.WriteLine($"{exeName} is already running.");
                    return true;
                }

                if (!File.Exists(exeFullPath))
                {
                    Console.WriteLine($"File not found: {exeFullPath}");
                    return true;
                }

                // Start the EXE as administrator
                var startInfo = new ProcessStartInfo(exeFullPath)
                {
                    UseShellExecute = true,
                    Verb = "runas" // UAC prompt
                };

                Process.Start(startInfo);
                Console.WriteLine($"{exeName} was not running and has been started as administrator.");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error while starting {exeName}: {ex.Message}");
                throw ex;
            }
        }
    }

}
