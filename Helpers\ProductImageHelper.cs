﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Configuration;


namespace Storeya.Core.Helpers
{
    public class ProductImageHelper
    {
        public static string CreateImagePath(string productName, string productOriginalID, string imageUrl, int index = 0)
        {
            string fileName = string.Format("{0}_{1}", GetValidFileName(productName.Trim()), GetValidFileName(productOriginalID.Trim()));
            if (fileName.Length >= 150) //limit is 240 but since directory name is added - getting spair space
            {
                fileName = fileName.Substring(0, 150);
            }

            if (index != 0)
            {
                fileName = fileName + "--" + index.ToString();
            }
            return string.Format("/{0}/{1}.{2}", fileName[0], fileName, ImagesDownloader.GetImageExtentionByPath(imageUrl)).ToLower();
        }
        public static string GetValidFileName(string originalName)
        {
            string output = Regex.Replace(originalName, @"[^0-9a-zA-Z]", "-");
            output = TextManipulationHelper.RemoveDuplicateCharacters(output, '-');
            output = output.Trim('-');
            return output;
        }
        public static string CreateImagePath(Product product, string imageUrl)
        {
            return CreateImagePath(product.Name, product.OriginalProductID, imageUrl);
        }

        public static string GetFullLocalFilePath(string fileName, int shopID)
        {
            return GetUploadFolder() + "\\" + GetImagesFolderName(shopID) + fileName;
            //return GetImagesFolderName(shopID) + fileName;
        }
        public static string GetRelativeImagePath(string fileName, int shopID)
        {
            string productImagesPath = ConfigurationManager.AppSettings["ProductImages.Path"];
            return "/" + productImagesPath + "/" + GetImagesFolderName(shopID) + fileName;
        }

        private static string GetUploadFolder()
        {
            string productImagesPath = ConfigurationManager.AppSettings["ProductImages.Path"];
            string rootFolderPath = ConfigurationManager.AppSettings["ProductImages.RootPath"];

            return (rootFolderPath + productImagesPath);
        }

        public static string GetImagesFolderName(int shopID, bool addProdInPath = true)
        {
            int startNewNamesFrom = 35000;
            if (!string.IsNullOrEmpty(ConfigHelper.GetValue("StartNewImageFoldersFrom")))
            {
                startNewNamesFrom = Convert.ToInt32(ConfigHelper.GetValue("StartNewImageFoldersFrom"));
            }

            if (shopID <= startNewNamesFrom)
            {
                return shopID.ToString();
            }
            string prod = "prod/";
            if (!addProdInPath)
            {
                prod = null;
            }
            //generate subfolders
            return prod + NumberToFolders(shopID);
        }
        private static string NumberToFolders(int number)
        {
            string words = "";
            int origNumber = number;

            if ((number / 1000000) > 0)
            {
                words += (number / 1000000).ToString().PadLeft(3, '0') + "/";
                number %= 1000000;
            }
            else
            {
                words = "000/";
            }

            if ((number / 1000) > 0)
            {
                words += (number / 1000).ToString().PadLeft(3, '0') + "/";
                number %= 1000;
            }

            words += origNumber;
            return words;
        }
        public static string GetTempFileName(string localPath)
        {
            string uploadFolder = GetUploadFolder();
            return localPath.Replace(uploadFolder, uploadFolder + "\\Temp");
        }


        public static string GetTempShopFileFolder(int shopID)
        {
            string uploadFolder = GetUploadFolder();
            string tempShopFileFolder = uploadFolder + "\\Temp\\" + GetImagesFolderName(shopID);
            return tempShopFileFolder;
        }



    }

}
