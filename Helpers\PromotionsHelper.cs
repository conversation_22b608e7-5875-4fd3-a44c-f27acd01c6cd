﻿using Storeya.Core.Entities;
using Storeya.Core.Models.Galleries;
using Storeya.Core.Models.ShopifyDiscounts;
using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Policy;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using static Storeya.Core.Models.ShopAttributes.Attributes.Accounts;
using static System.Net.Mime.MediaTypeNames;

namespace Storeya.Core.Helpers
{
    public class PromotionsHelper
    {
        public class SearchResults
        {
            public SearchResults()
            {
                Results = new List<SearchResult>();
            }
            public List<SearchResult> Results { get; set; }
            public class SearchResult
            {
                public int LineNumber { get; set; }
                public string Key { get; set; }
                public string Promotion { get; set; }

            }
            public void Add(int lineNumber, string key, string promotion)
            {
                Results.Add(new SearchResult() { Key = key, LineNumber = lineNumber, Promotion = promotion });
            }
            public bool ContainsKey(string key)
            {
                return Results.Any(k => k.Key == key);
            }
            public bool ContainsValue(string value)
            {
                return Results.Any(k => k.Promotion.Trim().ToLower() == value.Trim().ToLower());
            }

        }

        public static List<string> LoadPromotionKeys(out List<string> excludes, string localPath = null)
        {
            excludes = new List<string>();
            try
            {
                List<string> list = new List<string>();
                string s = null;
                if (!string.IsNullOrEmpty(localPath))
                {
                    s = File.ReadAllText($"{localPath}\\OnlinePromotionKeys.json");
                }
                else
                {
                    s = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest($"https://strys3.s3.amazonaws.com/Configurations/OnlinePromotionKeys.json");
                }

                dynamic d = s.FromJson<dynamic>();
                foreach (var item in d.keys)
                {
                    list.Add(item.ToString());
                }
                foreach (var item in d.excludes)
                {
                    excludes.Add(item.ToString());
                }

                return list;
            }
            catch //(Exception ex)
            {

            }
            return null;
        }

        public static SearchResults FindAndGetPromotionsFromHtml(string url)
        {
            List<string> promotionDictionary = LoadPromotionKeys(out List<string> excludes);
            Console.WriteLine($"Search for promotions in {url}");
            string html = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
            SearchResults searchResults = Search(promotionDictionary, excludes, html);
            if (searchResults.Results.Count == 0)
            {
                Console.WriteLine("No Promotions were found");
            }
            return searchResults;
        }
        public static void RunLookupForDeliveryPhrases(bool test = false, List<int> shopIDs = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            DateTime thirtyDaysAgo = DateTime.Now.Date.AddDays(-30);
            List<TrafficBooster> tbs = null;
            if (shopIDs != null)
            {
                tbs = new List<TrafficBooster>();
                foreach (var shopID in shopIDs)
                {
                    var tb = db.TrafficBoosters.Where(x => x.ShopID == shopID).SingleOrDefault();
                    tbs.Add(tb);
                }

            }
            else
            {
                if (test)
                {
                    tbs = db.TrafficBoosters.Where(x => x.Status == (int)TB_APP_STATUS.ACTIVE).Take(5).ToList();
                }
                else
                {
                    tbs = (from tb in db.TrafficBoosters
                           join s in db.Shops on tb.ShopID equals s.ID
                           join g in db.GAConnectedAccountsStats on tb.ShopID equals g.ShopID
                           join sf in db.ShopifyConnectedShops on tb.ShopID equals sf.ShopID
                           where sf.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster && tb.AppStatus.HasValue
                           && tb.AppStatus == (int)TB_APP_STATUS.ACTIVE && tb.LastPaymentDate > thirtyDaysAgo
                           && g.Revenue > 1000 && sf.PermissionsScope.HasValue && sf.PermissionsScope > -1
                           select tb).ToList();
                }
            }

            int total = tbs.Count;
            int progress = 0;
            List<dynamic> result = new List<dynamic>();
            foreach (var t in tbs)
            {
                progress++;
                Console.WriteLine($"{progress}/{total} {t.Url1}");
                string freeShippingExist = "No";
                string fullFreeShippingText = "";
                string freeDeliveryExist = "No";
                string fullDeliveryText = "";
                string html = null;
                try
                {
                    html = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(t.Url1).ToLower();
                }
                catch
                {
                    html = null;
                }


                if (html != null)
                {
                    html = TextManipulationHelper.RemoveImagesAndIframesAndScripts(html, true, true);
                    html = Regex.Replace(html, @"<(?!\/?div)[^>]*>", "");

                    string pattern = $@">(.*?)<";
                    MatchCollection matches = Regex.Matches(html, pattern, RegexOptions.IgnoreCase);
                    foreach (Match match in matches)
                    {
                        string textInDiv = match.Groups[1].Value;
                        if (textInDiv.Contains("free shipping"))
                        {
                            fullFreeShippingText += $"'{textInDiv}';";
                            freeShippingExist = "YES";
                        }
                        else if (textInDiv.Contains("free delivery"))
                        {
                            fullDeliveryText += $"'{textInDiv}';";
                            freeDeliveryExist = "YES";
                        }

                    }
                }
                else
                {
                    freeShippingExist = "Can't get data from URL";
                    Console.WriteLine("Can't get data from URL");
                    freeDeliveryExist = "";
                }
                string anyPhraseExist = "No";
                if (freeShippingExist == "YES" || freeDeliveryExist == "YES")
                {
                    anyPhraseExist = "YES";
                }

                dynamic currentShop = new
                {
                    ShopID = t.ShopID,
                    URL = t.Url1,
                    Free_Shipping = freeShippingExist,
                    Free_Delivery = freeDeliveryExist,
                    Total = anyPhraseExist,
                    FullShippingTexts = fullFreeShippingText,
                    FullDeliveryTexts = fullDeliveryText,
                };
                result.Add(currentShop);
            }
            Console.WriteLine($"Added to the table {result.Count()}/{total}");
            List<System.Net.Mail.Attachment> attachments = result.ToCSVAttatchment("c:\\temp\\CheckAllActiveTbsDomains.csv");
            EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "RunLookupForDeliveryPhrases finished", "See attachment.", attachments: attachments);
        }

        private static SearchResults Search(List<string> promotionDictionary, List<string> excludes, string html)
        {
            html = TextManipulationHelper.RemoveImagesAndIframesAndScripts(html, true, true);
            SearchResults searchResults = new SearchResults();
            string[] lines = html.Split(Environment.NewLine.ToCharArray());
            var lineNumber = 0;
            foreach (string line in lines)
            {
                lineNumber++;
                foreach (var keys in promotionDictionary)
                {
                    string[] words = keys.Split(' ');
                    string textToAdd = IfPhraseExistsGetPromotionFromLine(line, words);
                    if (textToAdd != null)
                    {
                        bool exclude = false;
                        foreach (var item in excludes)
                        {
                            if (textToAdd.ToLower().Contains(item))
                            {
                                Console.WriteLine($"Excluding due to use of :{item} - {textToAdd}");
                                exclude = true;
                                break;
                            }
                        }
                        if (exclude)
                        {
                            break;
                        }
                        if (!searchResults.ContainsValue(textToAdd))
                        {
                            searchResults.Add(lineNumber, keys, textToAdd);
                        }
                        break;
                    }
                }
            }
            return searchResults;
        }

        public static void RunTest(string configFilePath, string pathToSave)
        {
            List<string> promotionDictionary = LoadPromotionKeys(out List<string> excludes, configFilePath);
            var shops = File.ReadAllLines($"{configFilePath}\\shops.txt");
            StringBuilder textForCsv = new StringBuilder();
            textForCsv.AppendLine("ShopId,Url,LineNumber,Keys,Promotion");

            foreach (var shop in shops)
            {
                var s = shop.Split(",".ToCharArray());
                try
                {
                    string filePath = $"{pathToSave}\\{s[0]}.html";
                    string html = null;
                    if (File.Exists(filePath))
                    {
                        html = File.ReadAllText(filePath);
                    }
                    else
                    {
                        html = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(s[1]);
                        File.WriteAllText(filePath, html);
                    }
                    SearchResults searchResults = Search(promotionDictionary, excludes, html);
                    foreach (var item in searchResults.Results)
                    {
                        textForCsv.AppendLine($"{s[0]},{s[1]},{item.LineNumber},{item.Key},{item.Promotion}");
                    }
                    if (searchResults.Results.Count == 0)
                    {
                        Console.WriteLine("No Promotions were found");
                        textForCsv.AppendLine($"{s[0]},{s[1]},0");
                    }
                    else
                    {
                        Console.WriteLine($"{searchResults.Results.Count} Promotions were found");
                    }
                }
                catch (Exception ex)
                {
                    textForCsv.AppendLine($"{s[0]},{s[1]},-1,{ex.Message}");
                }

            }
            if (pathToSave != null)
            {
                string dateWithTime = String.Format("{0:s}", DateTime.Now).Replace(":", "-");
                File.WriteAllText($"{configFilePath}\\Results_{dateWithTime}.csv", textForCsv.ToString());
                Console.WriteLine($"CSV File was created: {$"{configFilePath}\\Results_{dateWithTime}.csv"}");
            }
        }

        public static List<string> FindAndGetPromotionsFromFile(string filePath, string pathToSave = null)
        {
            Console.WriteLine($"Search for promotions in {filePath}");
            StringBuilder textForCsv = new StringBuilder();
            textForCsv.AppendLine("LineNumber;Promotion;Keys");
            List<string> promotionDictionary = LoadPromotionKeys(out List<string> excludes);
            //List<string> promotionDictionary = new List<string>
            //{
            //    "buy one % off",
            //    "free shipping over $",
            //    "free shipping $ +",
            //    "% off more",
            //    "use code free",
            //    "sale use code",
            //    "only $ all new",
            //    "save % use code",
            //    "% off use code",
            //    "return over $",
            //    "free postage over $",
            //    "subscribe save",
            //    "shop now pay later"
            //};
            List<string> promotions = new List<string>();
            string[] lines = File.ReadAllLines(filePath);
            var lineNumber = 0;
            foreach (string line in lines)
            {
                lineNumber++;
                foreach (var keys in promotionDictionary)
                {
                    string[] words = keys.Split(' ');
                    string textToAdd = IfPhraseExistsGetPromotionFromLine(line, words);
                    if (textToAdd != null)
                    {
                        promotions.Add(textToAdd);
                        textForCsv.AppendLine($"{lineNumber};{textToAdd};{keys}");
                        break;
                    }
                }
            }
            if (promotions.Count == 0)
            {
                textForCsv = new StringBuilder();
                textForCsv.AppendLine("Promotions not found");
            }
            Console.WriteLine(textForCsv.ToString());
            if (pathToSave != null)
            {
                var splitedPath = filePath.Split('\\');
                var nameOfFile = splitedPath[splitedPath.Length - 1].Split('.')[0];
                string dateWithTime = String.Format("{0:s}", DateTime.Now).Replace(":", "-");
                var fileToSave = pathToSave + "\\" + nameOfFile + dateWithTime + ".csv";
                File.WriteAllText(fileToSave, textForCsv.ToString());
                Console.WriteLine($"Was created file: {fileToSave}");
            }
            return promotions;
        }
        private static string IfPhraseExistsGetPromotionFromLine(string line, string[] words)
        {
            var promotionText = RegexHelper.StripHTML(line);

            foreach (var word in words)
            {
                //!promotionText.ToLower().Contains(word)
                string list = RegexHelper.GetSingleValue(@"^(.*?)(\b" + word + @"\b)(.*)$", promotionText);
                if (promotionText.Length > 500 || string.IsNullOrEmpty(list.Trim()))
                {
                    return null;
                }
                if (list.Contains($"-{word}") || list.Contains($"_{word}") || list.Contains($".{word}") ||
                    list.Contains($"{word}-") || list.Contains($"{word}_") || list.Contains($"{word}."))
                {
                    return null;
                }
            }
            //More then 2 words
            if (promotionText != null && promotionText.Trim().Split(" ".ToCharArray()).Count() > 2)
            {
                return promotionText.Trim();
            }
            return null;
        }
    }

}
