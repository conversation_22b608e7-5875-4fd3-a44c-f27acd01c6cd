﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Helpers
{
    public class RandomNumberHelper
    {
        public static int Get(int max = 1000, int min = 0, int seed = 0)
        {
            seed = (seed == 0) ? DateTime.Now.Millisecond : seed;
            Random random = new Random(seed);
            return random.Next(min, max);
        }


        public static int GenerateDemoViews(int shopID, DateTime date)
        {
            int lastDigitShop = Convert.ToInt16(shopID.ToString().Substring(shopID.ToString().Length - 1));
            string day = date.AddDays(lastDigitShop).Day.ToString();
            int lastDigitDayInMonth = Convert.ToInt16(day.Substring(day.Length - 1));

            int divValue = 0;
            if (date > new DateTime(2014, 03, 31))
            {
                if (lastDigitShop == 0)
                {
                    string div = (((double)lastDigitDayInMonth) / 5).ToString();
                    divValue = Convert.ToInt16(div.Substring(div.Length - 1)) - 5;
                }
                else
                {
                    string div = (((double)lastDigitDayInMonth) / lastDigitShop).ToString();
                    divValue = Convert.ToInt16(div.Substring(div.Length - 1)) - 5;
                }

            }

            int result = ((int)date.DayOfWeek + lastDigitShop + lastDigitDayInMonth + divValue - 6);
            return (result < 0) ? 0 : result;
        }

        public static int GenerateViews2(int shopID, DateTime date)
        {
            Random random = new Random(shopID + date.Day);
            return random.Next(0, 15);
        }

    }
}
