﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Helpers
{
    public class SequenceHelper
    {
        public static string Encode(string value)
        {
            if (int.TryParse(value, out int val))
            {
                return Encode(val);
            }
            return null;
        }
        public static string Encode(int value)
        {
            //int prefix = RandomNumberHelper.Get(99, 10, DateTime.Now.Hour); //get two digits
            //int suffix = RandomNumberHelper.Get(9);
            string text = value.ToString();
            string firstDigit = text[0].ToString();
            string secondDigit = text[Math.Min(1, text.Length - 1)].ToString();
            string lastDigit = text[text.Length - 1].ToString();

            string hexValue = value.ToString("X");

            return secondDigit + firstDigit + hexValue + lastDigit;
        }

        public static int Decode(string value)
        {
            string hexValue = value.Substring(2, value.Length - 3);

            return Convert.ToInt32(hexValue, 16);
        }


        private static void Test()
        {
            for (int i = 10; i < 1000; i++)
            {
                System.Threading.Thread.Sleep(10);
                string hex = SequenceHelper.Encode(i);
                Console.WriteLine(hex + " " + SequenceHelper.Decode(hex));
            }
        }
        

        public static int EncodeStringToInt(string value)
        {
            byte[] bytes = Encoding.Default.GetBytes(value);
            if (bytes.Length < 4)
            {
                byte[] temp = new byte[4];
                bytes.CopyTo(temp, 0);
                bytes = temp;
            }

            int result = BitConverter.ToInt32(bytes, 0);
            return result;
        }

        public static string DecodeIntToString(int value)
        {            
            byte[] bytes = BitConverter.GetBytes(value);
            string result = Encoding.Default.GetString(bytes);            
            return result.TrimEnd('\0');;
        }

        public static string EncodeStringToBase64String(string value)
        {
            byte[] bytes = Encoding.Default.GetBytes(value);
            string result = Convert.ToBase64String(bytes);
            return result;
        }

        public static string DecodeFromBase64String(string value)
        {
            byte[] bytes = Convert.FromBase64String(value);
            string result = Encoding.Default.GetString(bytes);
            return result;
        }

        public static string CreateKeyToExtractOrders(int shopID, string domain)
        {
            string elapsedTicks = DateTime.Now.Ticks.ToString();
            string encodedShopID = SequenceHelper.Encode(shopID);
            string keyToEncode = string.Format("{0}_{1}_{2}", elapsedTicks, encodedShopID, domain);
            string key = SequenceHelper.EncodeStringToBase64String(keyToEncode);
            return key;
        }

    }
}
