﻿using Storeya.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class ShopifyAppstoreReviewsHelper
    {
        public static List<ShopifyAppstoreReview> GetReviewsInLastDays(int daysAgo = 7, int? appType = null)
        {
            var db = new StoreYaEntities();
            DateTime reviewDate = DateTime.Now.AddDays(-daysAgo);
            var query = db.ShopifyAppstoreReviews.Where(x => x.ReviewDate >= reviewDate);
            if (appType != null)
            {
                query = query.Where(x => x.AppType == appType);
            }
            return query.OrderByDescending(x => x.ID).ToList();
        }
        public static bool CheckIfLeftReview(int shopID, AppTypes Type)
        {
            var db = new StoreYaEntities();
            if (db.ShopifyAppstoreReviews.Where(x => x.ShopID == shopID && x.AppType == (int)Type).Any())
            {
                return true;
            }
            else
            {
                return false;
            }
        }

    }
}
