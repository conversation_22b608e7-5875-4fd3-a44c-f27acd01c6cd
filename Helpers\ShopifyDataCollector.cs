﻿using Storeya.Core.Entities;
using Storeya.Core.Models;
using Storeya.Core.Models.AppStore;
using Storeya.Core.Models.DataProviders;
using Storeya.Core.Models.Marketplaces;
using Storeya.Core.Models.Reports;
using Storeya.Core.Models.Shopify;
using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.IO;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Security.AccessControl;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static Storeya.Core.GlobalEFeedManager;
using PostmarkDotNet.Model;
using static Storeya.Core.Models.DataProviders.ShopifyApiProvider;
using ShopifyAPIAdapterLibrary;

namespace Storeya.Core.Helpers
{
    public static class ShopifyDataCollector
    {
        public static void GetTotalsByUtmSource(List<string> shopIds, int appId, int daysBack, int getDaysBefore, string csvPath, bool saveShopOrders = false)
        {
            DateTime from = DateTime.Now.AddDays(-daysBack - getDaysBefore);
            DateTime to = DateTime.Now.AddDays(-getDaysBefore);
            int count = shopIds.Count;
            Console.WriteLine($"Total: {count} - From:{from} - To:{to} ");
            List<OrdersRow> orders = new List<OrdersRow>();
            foreach (var item in shopIds)
            {
                if (int.TryParse(item, out int shopId))
                {
                    try
                    {
                        List<OrdersRow> shopOrder = GetTotalsByUtmSource(shopId, appId, from, to, out int countUrlUtm, out int countUtm, out int countUtmTotal, saveShopOrders);
                        orders.AddRange(shopOrder);
                        Console.WriteLine($"{shopId} Total:{shopOrder.Count} ,countUrlUtm:{countUrlUtm},countUtm:{countUtm},countUtmTotal:{countUtmTotal}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"{shopId} - {ex.Message}");
                    }

                }
                Console.WriteLine($"Left:{count--}");
            }
            orders.ToCSV(csvPath);
        }
        public static List<OrdersRow> GetTotalsByUtmSource(int shopId, int appId, DateTime from, DateTime to, out int countUrlUtm, out int countUtm, out int countUtmTotal, bool saveOrdersData = false)
        {

            List<ShopifyOrder> shopifyOrders = ShopifyApiProvider.GetAllOrders(shopId, from, to, out countUrlUtm, out countUtm, out countUtmTotal, (AppTypes)appId);

            if (saveOrdersData)
            {
                File.WriteAllText($"C:\\Temp\\{shopId}-{from.ToString("yyyy-MMM-dd")}.Json", shopifyOrders.ToJson());
            }
            shopifyOrders = shopifyOrders.Where(s => s.SourceName != "pos").ToList();
            var orders = ShopifyOrdersSourceService.GetOrdersRows(shopifyOrders);

            return orders.GroupBy(x => new { x.UtmSource })
              .Select(g => new OrdersRow
              {
                  Source = $"{from} - {to}",
                  Name = shopId.ToString(),
                  UtmSource = g.Key.UtmSource,
                  Revenue = g.Sum(x => x.Revenue),
                  Transactions = g.Sum(x => x.Transactions),
              })
              .ToList();
        }



        public static void test(DateTime from, DateTime to)
        {
            Storeya.Core.Models.DataProviders.ShopifyApiProvider shopifyApiProvider = ShopifyConnector.GetShopifyApiClient(480570, (int)AppTypes.TrafficBooster);

            List<ShopifyOrder> shopifyOrdersUtm = shopifyApiProvider.GetOrdersUtmsGraphQL(from, to);
            List<ShopifyOrder> shopifyOrders = shopifyApiProvider.GetOrders2(updated_at_min: from, updated_at_max: to, orderFields: ORDERS_FIELDS.FORCOLLECT);
            foreach (var order in shopifyOrders)
            {
                var o = shopifyOrdersUtm.FirstOrDefault(x => x.gid == order.gid);
                if (o != null)
                {
                    order.UtmCampaign = o.UtmCampaign;
                    order.UtmMedium = o.UtmMedium;
                    order.UtmSource = o.UtmSource;
                }
            }
            //r = "{\"query\":\"" + r + " \",\"variables\":{}}";

            //string querys = "{  query:  order(id: \"gid://shopify/Order/5933327810709\") { name createdAt customerJourneySummary { firstVisit { landingPage utmParameters { source medium campaign} } customerOrderIndex } } }";
            //string query = "{  query:  order(id: \"gid://shopify/Order/5941444870293\") { name createdAt customerJourneySummary { firstVisit { landingPage utmParameters { source medium campaign} } customerOrderIndex } } }";
            //string sss = shopifyApiProvider.RunGraphHql(query, "graphql").ToString();

            string s = File.ReadAllText(@"C:\Temp\getOrdersUtm.gql").Replace(Environment.NewLine, " ");
            dynamic trrr = shopifyApiProvider.RunGraphHql(s, "graphql");//it's test
        }
        public static void Collect(int daysBack = 2, int getDaysBefore = 0, int? shopId = null, int lastPaymeentValidDays = 90, bool collectCampaignsPerformanceByUtm = true)
        {
            Collect(DateTime.Today.AddDays(-daysBack - getDaysBefore), DateTime.Today.AddDays(-getDaysBefore - 1).EndOfDay(), shopId, lastPaymeentValidDays, collectCampaignsPerformanceByUtm);
        }
        public static void Collect(DateTime from, DateTime? to = null, int? shopId = null, int lastPaymeentValidDays = 45, bool collectCampaignsPerformanceByUtm = true)
        {
            try
            {
                var lastPaymeentValidDate = DateTime.Now.AddDays(-lastPaymeentValidDays);
                var db = new StoreYaEntities();

                var reportingTbs = (from t in db.TrafficBoosters.Where(t =>
                            t.Status != (int)TB_AW_STATUS.CANCELED && t.Status > (int)TB_AW_STATUS.WAITING_TO_RUN_FIRST_TIME
                            && t.LastPaymentDate.Value > lastPaymeentValidDate
                            )
                                    join s in db.Shops on t.ShopID equals s.ID
                                    join u in db.Users on s.UserID equals u.ID
                                    join sp in db.ShopifyConnectedShops.Where(shp => shp.PermissionsScope == 1
                                    && shp.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster) on t.ShopID equals sp.ShopID
                                    select new { ShopId = t.ShopID.Value, Url = t.Url1, Platform = s.CatalogSourcePlatform }
                    ).ToList();
                if (shopId.HasValue)
                {
                    reportingTbs = reportingTbs.Where(d => d.ShopId == shopId.Value).ToList();
                }
                int count = reportingTbs.Count;
                Console.WriteLine($"Total Active Shopify accounts to update: {count}");
                foreach (var tb in reportingTbs)
                {
                    Console.WriteLine($"{tb.ShopId} - Start Collect: {count}");
                    try
                    {
                        List<ShopifyOrdersTotals> shopifyOrdersTotals = ShopifyApiProvider.GetTotalOrdersTbApp(tb.ShopId, from, to, collectCampaignsPerformanceByUtm);
                        shopifyOrdersTotals = shopifyOrdersTotals.Where(o => o.Total > 0).ToList();
                        if (shopifyOrdersTotals.Count > 0)
                        {
                            UpdateTbReport(tb.ShopId, shopifyOrdersTotals);
                        }

                    }
                    catch (Exception ex)
                    {
                        ConsoleAppHelper.WriteErrorWithDB("Failed to update shopify order data", ex, tb.ShopId);
                    }
                    count--;
                    Console.WriteLine($"{tb.ShopId} Done - Left: {count}");
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Failed to Run Shopify Order Collect", ex.ToString());
            }
        }

        private static void UpdateTbReport(int shopId, List<ShopifyOrdersTotals> shopifyOrdersTotals)
        {
            bool saveChanges = false;
            var db = DataHelper.GetStoreYaEntities();
            foreach (var shopifyOrdersTotal in shopifyOrdersTotals)
            {
                var rows = db.TBReports.Where(r => r.ShopID == shopId && r.ReportDate.HasValue && DbFunctions.TruncateTime(r.ReportDate.Value) == shopifyOrdersTotal.Date).ToList();
                if (rows == null || rows.Count == 0)
                {
                    var newRow = new TBReport()
                    {
                        ShopID = shopId,
                        ReportDate = shopifyOrdersTotal.Date,
                        InsertedAt = DateTime.Now,
                        CampaignType = -1,
                        StryShopifyTransactions = shopifyOrdersTotal.StoreYaTotal,
                        StryShopifyTransactionRevenue = shopifyOrdersTotal.StoreyaRevenue,
                        AllShopifyTransactionRevenue = shopifyOrdersTotal.AllRevenue,
                        AllShopifyTransactions = shopifyOrdersTotal.Total,
                        StryShopifyAllStoreyaTransactions = shopifyOrdersTotal.StoreYaAllChannelsTotal,
                        StryShopifyAllStoreyaTransactionRevenue = shopifyOrdersTotal.StoreYaAllChannelsRevenue,
                    };
                    saveChanges = true;
                    db.TBReports.Add(newRow);
                    Console.WriteLine($"Row was added {shopifyOrdersTotal.Date} with revenue:{shopifyOrdersTotal.StoreyaRevenue}");
                }
                else
                {
                    if (rows.Count() == 1)
                    {
                        var row = rows.Single();
                        if (!row.StryShopifyTransactionRevenue.HasValue ||
                            row.StryShopifyTransactionRevenue < shopifyOrdersTotal.StoreyaRevenue)
                        {
                            Console.WriteLine($"Row will be updated {shopifyOrdersTotal.Date} revenue:{shopifyOrdersTotal.StoreyaRevenue} from {row.StryShopifyTransactionRevenue}");
                            row.StryShopifyTransactions = shopifyOrdersTotal.StoreYaTotal;
                            row.StryShopifyTransactionRevenue = shopifyOrdersTotal.StoreyaRevenue;

                            saveChanges = true;
                        }

                        if (!row.StryShopifyAllStoreyaTransactionRevenue.HasValue
                            || row.StryShopifyAllStoreyaTransactionRevenue < shopifyOrdersTotal.StoreYaAllChannelsRevenue)
                        {
                            Console.WriteLine($"Row StryShopifyAllStoreyaTransactionRevenue will be updated {shopifyOrdersTotal.Date} revenue:{shopifyOrdersTotal.StoreYaAllChannelsRevenue} from {row.StryShopifyAllStoreyaTransactionRevenue}");
                            row.StryShopifyAllStoreyaTransactions = shopifyOrdersTotal.StoreYaAllChannelsTotal;
                            row.StryShopifyAllStoreyaTransactionRevenue = shopifyOrdersTotal.StoreYaAllChannelsRevenue;

                            saveChanges = true;
                        }

                        if (!row.AllShopifyTransactionRevenue.HasValue
                            || row.AllShopifyTransactionRevenue < shopifyOrdersTotal.AllRevenue)
                        {
                            Console.WriteLine($"Row AllShopifyTransactionRevenue will be updated {shopifyOrdersTotal.Date} revenue:{shopifyOrdersTotal.AllRevenue} from {row.AllShopifyTransactionRevenue}");
                            row.AllShopifyTransactionRevenue = shopifyOrdersTotal.AllRevenue;
                            row.AllShopifyTransactions = shopifyOrdersTotal.Total;

                            saveChanges = true;
                        }

                        if (saveChanges)
                        {
                            row.UpdatedAt = DateTime.Now;
                        }

                    }
                    else
                    {
                        Console.WriteLine("Duplicate rows found.");
                    }
                }
            }
            if (saveChanges)
            {

                db.SaveChanges();
            }

        }

        public static void UnsubscribeWebhook(int? shopId = null)
        {
            try
            {
                var db = new StoreYaEntities();
                List<ShopifyConnectedShop> shopifyConnectedApps = null;

                if (shopId.HasValue)
                {
                    shopifyConnectedApps = db.ShopifyConnectedShops.Where(shp => shp.ShopID == shopId.Value).ToList();
                }
                else
                {
                    shopifyConnectedApps = db.ShopifyConnectedShops.Where(shp => shp.PermissionsScope != -1).ToList();
                }

                int count = shopifyConnectedApps.Count;
                int total = count;
                Console.WriteLine($"Total Active Shopify connection:{count}");
                int done = 0;
                foreach (var app in shopifyConnectedApps)
                {
                    try
                    {
                        string res = "";
                        if (shopId == 0)
                        {
                            ConnectionData data = new ConnectionData();
                            data.StoreName = app.ShopifyShopName;
                            data.Token = app.AppToken;
                            ShopifyConnector client = new ShopifyConnector(data);
                            res = AppStoreManager.UnSubscribToShopifyWebHook(client, (Shopify_StoreyaApp)app.StoreyaAppTypeID, "update");
                        }
                        else
                        {
                            res = AppStoreManager.UnSubscribToShopifyWebHook(app.ShopID, (Shopify_StoreyaApp)app.StoreyaAppTypeID, "update");
                        }



                        ConsoleColor cc = Console.ForegroundColor;
                        if (res.Contains("Done,"))
                        {

                            Console.ForegroundColor = ConsoleColor.Green;
                            Console.WriteLine(res);
                            Console.ForegroundColor = cc;
                            done++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"{app.ShopID},{(Shopify_StoreyaApp)app.StoreyaAppTypeID} - {ex.Message}");
                    }
                    Console.WriteLine($"Left to run: {count--} out of {total}");
                    if (count % 1000 == 0)
                    {
                        Console.WriteLine($"Sleep 15 sec");
                        Thread.Sleep(15000);
                    }
                }
                Console.WriteLine($"Unsubscribe: {done} out of {total}");
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Failed to run Shopify Unsubscribe Webhooks ", ex.ToString());
            }

        }

        public static bool FillShopifyMerchants(int shopID, ShopifyMerchant merchantData, StoreYaEntities db, bool update = false)
        {
            ShopifyMerchant shopifyMerchantData = db.ShopifyMerchants.Where(s => s.ShopID == shopID && s.AppTypeID == merchantData.AppTypeID).SingleOrDefault();//s.MyshopifyDomain == connectedShop.ShopifyShopName || s.Domain == connectedShop.ShopifyShopName) 
            if (shopifyMerchantData == null)
            {
                merchantData.InsertedAt = DateTime.Now;
                db.ShopifyMerchants.Add(merchantData);
                return true;
            }
            else if (update && merchantData != null)
            {
                return UpdateShopifyMerchant(shopifyMerchantData, merchantData, db);
            }
            return false;
        }

        private static bool UpdateShopifyMerchant(ShopifyMerchant shopifyMerchantData, ShopifyMerchant merchantData, StoreYaEntities db)
        {
            bool wasChanged = false;
            if (merchantData.Domain != null && merchantData.Domain != shopifyMerchantData.Domain)
            {
                shopifyMerchantData.Domain = merchantData.Domain;
                wasChanged = true;
            }
            if (merchantData.MyshopifyDomain != null && merchantData.MyshopifyDomain != shopifyMerchantData.MyshopifyDomain)
            {
                shopifyMerchantData.MyshopifyDomain = merchantData.MyshopifyDomain;
                wasChanged = true;
            }
            if (merchantData.Name != null && merchantData.Name != shopifyMerchantData.Name)
            {
                shopifyMerchantData.Name = merchantData.Name;
                wasChanged = true;
            }
            if (merchantData.ShopOwner != null && merchantData.ShopOwner != shopifyMerchantData.ShopOwner)
            {
                shopifyMerchantData.ShopOwner = merchantData.ShopOwner;
                wasChanged = true;
            }
            if (merchantData.Email != null && merchantData.Email != shopifyMerchantData.Email)
            {
                shopifyMerchantData.Email = merchantData.Email;
                wasChanged = true;
            }
            if (merchantData.CustomerEmail != null && merchantData.CustomerEmail != shopifyMerchantData.CustomerEmail)
            {
                shopifyMerchantData.CustomerEmail = merchantData.CustomerEmail;
                wasChanged = true;
            }
            if (wasChanged)
            {
                db.SaveChanges();
            }
            return wasChanged;

        }
        public static ShopifyConnector SetShopifyClientSettingsInDb(int shopID, int appType, int userID, out bool reloadUserForCom)
        {
            reloadUserForCom = false;
            ShopifyConnector client = new ShopifyConnector(shopID, appType);
            try
            {
                ShopifyMerchant merchantData = client.GetShopDetails(shopID, appType);
                if (merchantData != null)
                {
                    UpdateClientDataInDb(shopID, userID, merchantData);
                    reloadUserForCom = true;
                    //CookiesHelper.CreateCookie("stry_locale", merchantData.Locale, 365);
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.ErrorWithDB("Failed on getting Shopify shop data and updating Storeya client with it.", ex, shopID);
            }
            return client;
        }
        private static void UpdateClientDataInDb(int shopID, int userID, ShopifyMerchant merchantData)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            ShopifyDataCollector.FillShopifyMerchants(shopID, merchantData, db);

            FillShopDetailsIfEmpty(shopID, merchantData, db);

            Shop currentShop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            string siteUrl = merchantData.Domain;//merchantData.MyshopifyDomain;
            siteUrl = SetShopUrls(userID, currentShop, siteUrl);
            if (string.IsNullOrEmpty(currentShop.Name))
            {
                currentShop.Name = merchantData.Name;
            }
            currentShop.StoreLocale = merchantData.Locale;
            currentShop.Currency = merchantData.Currency;

            SetUserData(merchantData, currentShop);

            if (merchantData.AppTypeID == (int)Shopify_StoreyaApp.TrafficBooster)
            {
                //domain data will be collected
                LeadDetailsProvider.AddLead(currentShop.ID, siteUrl);
            }

            db.SaveChanges();
        }
        private static void FillShopDetailsIfEmpty(int shopID, ShopifyMerchant merchantData, StoreYaEntities db)
        {
            ShopDetail details = db.ShopDetails.Where(s => s.ShopID == shopID).SingleOrDefault();
            if (details == null)
            {
                ShopDetail newDetails = new ShopDetail();
                newDetails.ShopID = shopID;
                newDetails.Phone = merchantData.Phone;
                newDetails.Country = merchantData.CountryCode;
                db.ShopDetails.Add(newDetails);
            }
        }
        private static string SetShopUrls(int userID, Shop currentShop, string siteUrl)
        {
            if (!string.IsNullOrEmpty(siteUrl))
            {
                if (!siteUrl.Contains("http://") && !siteUrl.Contains("https://"))
                    siteUrl = "https://" + siteUrl;

                if (string.IsNullOrEmpty(currentShop.BaseUrl) || currentShop.BaseUrl.Contains("myshopify.com"))
                    currentShop.BaseUrl = siteUrl;

                if (string.IsNullOrEmpty(currentShop.ShopUrl) || currentShop.ShopUrl.Contains("myshopify.com"))
                    currentShop.ShopUrl = siteUrl;

                //SystemEventHelper.Add(userID, currentShop.ID, null, SystemEventTypes.UserActivity, SystemEventActions.TBCountryChange, siteUrl);
            }
            return siteUrl;
        }
        private static void SetUserData(ShopifyMerchant merchantData, Shop currentShop)
        {
            StoreYaEntities db = new StoreYaEntities();
            User currentUser = db.Users.Where(u => u.ID == currentShop.UserID).SingleOrDefault();

            if (string.IsNullOrEmpty(currentUser.Name))
            {
                var userName = merchantData.ShopOwner;
                bool validName = EmailHelper.CheckNameValid(userName, currentShop.Name);
                if (validName)
                {
                    //if user name valid - save to DB
                    currentUser.Name = userName;
                }
            }

            if (!string.IsNullOrEmpty(merchantData.Locale))
            {
                if (string.IsNullOrEmpty(currentUser.Locale))
                {
                    currentUser.Locale = merchantData.Locale;
                }
            }
            db.SaveChanges();
        }
        //public static void SubscribeUninstallWebhook(int shopID, int appType, ShopifyConnector client = null, string domain = null, bool ignoreDomain = false)
        //{
        //    SubscribeUninstallWebhookAsync(shopID, appType, client, domain, ignoreDomain).GetAwaiter().GetResult();
        //}
        public static void SubscribeUninstallWebhook(int shopID, int appType, ShopifyConnector client = null, string domain = null, bool ignoreDomain = false)
        {
            bool addWebhook = false;
            if (!ignoreDomain)
            {
                if (domain == null)
                {
                    try
                    {
                        domain = HttpHelper.GetCurrentDomainHttps();
                    }
                    catch
                    {
                        domain = ".storeya.com";
                    }
                }
                if (domain.ToLower().Contains(".storeya.com") || domain.ToLower().Contains(".fluxas.com"))
                {
                    addWebhook = true;
                }
            }
            else
            {
                addWebhook = true;
            }

            if (addWebhook)
            {
                if (client == null)
                {
                    client = new ShopifyConnector(shopID, appType);
                }
                try
                {
                    var domainForWebhook = ConfigHelper.GetValue("Storeya.Com", "https://www.storeya.com");
                    string subscribe_to_app_uninstall_webhook_url = string.Format("{0}/shopify/WebHookAppUninstalled?appType={1}", domainForWebhook, appType);
                    string subscribe_uninstall_response = client.SubscribeToWebhook("app/uninstalled", subscribe_to_app_uninstall_webhook_url);
                    Log4NetLogger.Info(string.Format("Shopify Subscribed to app/uninstalled responce: {0}", subscribe_uninstall_response), shopID);
                    Console.WriteLine($"Shopify Subscribed to app/uninstalled");
                }
                catch (Exception ex)
                {
                    if (ex.ToString().Contains("(422) Unprocessable Entity"))
                    {
                        Log4NetLogger.Error($"Shopify Failed to Subscribed to app/uninstalled.", ex, shopID);
                        Console.WriteLine($"Shopify Failed to Subscribed to app/uninstalled");
                    }
                    else
                    {
                        Log4NetLogger.Error($"Unexpected error. Shopify Failed to Subscribed to app/uninstalled.", ex, shopID);
                        Console.WriteLine($"Unexpected error. Shopify Failed to Subscribed to app/uninstalled.");
                    }
                }
            }
        }

        public static void UpdatePermissionList(int connectedShopId)
        {
            try
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                var connectedShop = db.ShopifyConnectedShops.Where(x => x.ID == connectedShopId).Single();
                connectedShop.PermissionsList = ShopifyConnector.GetScopesStringByApp(connectedShop.StoreyaAppTypeID);
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "UpdatePermissionList failed", $"connectedShopId: {connectedShopId}. {ex.ToString()}");
            }
        }

        //run through Shopify connected shops and update access token       
        //public static void UpdateAllAccessToken(int appType, string refreshToken, int? shopId = null)
        //{
        //    try
        //    {
               
        //        StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //        var connectedShops = db.ShopifyConnectedShops.Where(x => x.PermissionsScope == 1 && x.StoreyaAppTypeID == appType && x.ShopID > 0  ).ToList();
        //        if (shopId.HasValue)
        //        {
        //            connectedShops = connectedShops.Where(c => c.ShopID == shopId).ToList();
        //        }
        //        int count = connectedShops.Count();
        //        Console.WriteLine($"Running update token for {connectedShops.Count()} shops. Press Enter to continue.");
        //        Console.Read();
        //        foreach (var connectedShop in connectedShops)
        //        {
        //            try
        //            {
        //                Console.WriteLine($"Checking access token for shop {connectedShop.ShopID} with app type {connectedShop.StoreyaAppTypeID}.");
        //                ShopifyConnector client = new ShopifyConnector(connectedShop.ShopID, connectedShop.StoreyaAppTypeID);
        //                client.GetShopifyMerchant(connectedShop.ShopID, connectedShop.StoreyaAppTypeID);

        //                ShopifyAuthorizationState newToken = ShopifyConnector.GetRefreshedToken(connectedShop.ShopifyShopName, appType, refreshToken, connectedShop.AppToken);
        //                string oldToken = connectedShop.AppToken;
        //                if (!string.IsNullOrEmpty(newToken.AccessToken))
        //                {
        //                    connectedShop.AppToken = newToken.AccessToken;
        //                    connectedShop.UpdatedAt = DateTime.Now;
        //                    db.SaveChanges();

        //                    ConsoleAppHelper.WriteLog($"Updated access token for shop {connectedShop.ShopID} with app type {connectedShop.StoreyaAppTypeID}.Token changed from:{oldToken},to: {newToken.AccessToken}", connectedShop.ShopID);
        //                }
        //                else
        //                {
        //                    ConsoleAppHelper.WriteError($"Failed to update access token for shop {connectedShop.ShopID} with app type {connectedShop.StoreyaAppTypeID}. New token is empty.", null, connectedShop.ShopID);
        //                }
        //            }
        //            catch (Exception ex)
        //            {
        //                ConsoleAppHelper.WriteError($"Failed to update access token for shop :{connectedShop.ShopID}", ex, connectedShop.ShopID);
        //            }
        //            finally
        //            {
        //                Console.WriteLine($"Left to run: {--count} out of {connectedShops.Count()}");
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Failed to run UpdateAccessToken", ex.ToString());
        //    }

        //}
    }


}
