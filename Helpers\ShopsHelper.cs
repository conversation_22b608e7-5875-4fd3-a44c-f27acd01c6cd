﻿using Org.BouncyCastle.Tls.Crypto;
using Storeya.Core.Entities;
using Storeya.Core.Models;
using Storeya.Core.Models.Account;
using Storeya.Core.Models.AppStore;
using Storeya.Core.Models.CRM;
using Storeya.Core.Models.Payments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class ShopsHelper
    {
        public static void RenameShop(int shopId, string name, string updatedBy)
        {
            var db = DataHelper.GetStoreYaEntities();
            var shop = db.Shops.SingleOrDefault(s => s.ID == shopId);
            string cName = shop.Name;
            shop.Name = name;
            db.SaveChanges();
            Log4NetLogger.InfoWithDB(string.Format("Shop with ID:{0} was renamed by:{3} old name:{1} ,new name:{2}", shopId, cName, name, updatedBy), null, shopId);
        }

        public static List<Shop> GetLatestShopsByEmail(string email, bool onlyActive = false)
        {
            if (string.IsNullOrEmpty(email))
            {
                return null;
            }
            var db = DataHelper.GetStoreYaEntities();
            var user = db.Users.Where(s => s.Email == email && s.FbProfileID > -1).OrderByDescending(u => u.ID).ToList();
            if (user == null || user.Count == 0)
            {
                user = db.Users.Where(s => s.Email2 == email && s.FbProfileID > -1).OrderByDescending(u => u.ID).ToList();
            }
            if (user != null && user.Count() > 0)
            {
                var userId = user.First().ID;
                if (onlyActive)
                {
                    return db.Shops.Where(s => s.UserID == userId && s.IsDisabled != 1).OrderByDescending(i => i.InsertedAt).ToList();

                }
                return db.Shops.Where(s => s.UserID == userId).OrderByDescending(i => i.InsertedAt).ToList();
            }
            return null;
        }

        public static List<Shop> GetLatestShopsByUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return null;
            }
            url = url.TrimEnd(new char[] { '/' });
            var db = DataHelper.GetStoreYaEntities();
            //var shops = db.Shops.Where(s => s.BaseUrl.ToLower().EndsWith(url.ToLower())).OrderByDescending(i => i.InsertedAt).ToList();
            var shops = db.Shops.Where(s => s.BaseUrl.ToLower().Contains(url.ToLower()) || s.ShopUrl.ToLower().Contains(url.ToLower())).OrderByDescending(i => i.InsertedAt).ToList();
            if (shops != null && shops.Count() > 0)
            {
                //var shop = shops.Where(s => s.IsDisabled != 1).OrderByDescending(i => i.InsertedAt).First();
                //if (shop == null)
                //{
                //    shop = shops.First();
                //}
                return shops.ToList();
            }
            if (shops == null)
            {
                var bshops = db.Shops.Where(s => s.IsDisabled != 1 && s.BaseUrl.ToLower().StartsWith(url.ToLower())).OrderByDescending(i => i.InsertedAt).ToList();
                return shops.ToList();
            }
            return null;
        }
        public static Shop GetShop(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            var shop = db.Shops.Where(s => s.ID == shopId).SingleOrDefault();
            if (shop == null)
            {
                return null;
            }

            return shop;
        }
        public static User GetUser(Shop shop, StoreYaEntities db = null)
        {
            if (db == null)
            {
                db = DataHelper.GetStoreYaEntities();
            }
            if (shop == null)
            {
                return null;
            }
            var user = db.Users.Where(u => u.ID == shop.UserID).SingleOrDefault();
            return user;
        }
        public static User GetUser(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            var shop = db.Shops.Where(s => s.ID == shopId).SingleOrDefault();
            return GetUser(shop, db);
        }
        public static string GetUsersResponseEmail(int shopId)
        {
            User user = ShopsHelper.GetUser(shopId);
            List<string> emails = new List<string>();
            if (user.Email != null)
            {
                emails.Add(user.Email);
            }
            if (user.Email2 != null)
            {
                emails.Add(user.Email2);
            }
            string email = emails.Where(x => !x.Contains("no-reply") && !x.Contains("noreply") && !x.Contains("no_reply")).FirstOrDefault();
            return email;
        }
        public static string GetUserEmail(int shopId)
        {
            var user = ShopsHelper.GetUser(shopId);
            string email = user.Email;
            if (!email.Contains("@"))
            {
                email = user.Email2;
            }
            return email;
        }
        public static List<string> GetShopEmails(int shopId,out int userId)
        {
            userId = -1;
            var db = DataHelper.GetStoreYaEntities();
            var q = (from s in db.Shops
                     join a in db.ShopAdmins on s.ID equals a.ShopID
                     join u in db.Users on a.UserID equals u.ID
                     where s.ID == shopId
                     select u.Email).ToList();
            var user = GetUser(shopId);
            userId = user.ID;
            q.Insert(0, user.Email);
            if (!string.IsNullOrEmpty(user.Email2))
            {
                q.Insert(1, user.Email2);
            }
            q.ForEach(c => c = c.ToLower());
            return q.Distinct().ToList();

        }
        public static CatalogSourcePlatforms GetCatalogPlatform(Shop shop)
        {
            if (shop == null)
            {
                return CatalogSourcePlatforms.None;
            }
            if (shop.CatalogSourcePlatform == null)
            {
                return CatalogSourcePlatforms.None;
            }
            CatalogSourcePlatforms catalogSourcePlatforms = (CatalogSourcePlatforms)shop.CatalogSourcePlatform;
            return catalogSourcePlatforms;
        }
        public static string GetCurrency(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            var shop = db.Shops.Where(s => s.ID == shopId).SingleOrDefault();
            if (shop == null)
            {
                return null;
            }
            if (string.IsNullOrEmpty(shop.Currency))
            {
                return "USD";
            }
            return shop.Currency;
        }
        public static CatalogSourcePlatforms GetCatalogPlatform(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            var shop = db.Shops.Where(s => s.ID == shopId).SingleOrDefault();
            return GetCatalogPlatform(shop);
        }
        public static bool IsShopifyInstalled(int shopId, out bool notShopify)
        {
            notShopify = true;
            var db = DataHelper.GetStoreYaEntities();
            var shopifyConnectedShop = db.ShopifyConnectedShops.Where(o => o.ShopID == shopId && o.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster).FirstOrDefault();
            if (shopifyConnectedShop != null)
            {
                notShopify = false;
                if (shopifyConnectedShop.PermissionsScope.HasValue && shopifyConnectedShop.PermissionsScope.Value > -1)
                {
                    return true;
                }
                return false;
            }
            return false;
        }

        public static bool UpdateShopUserRelationStatus(int shopId, RelationStatus relationStatus)
        {
            var db = DataHelper.GetStoreYaEntities();
            var shop = db.Shops.Where(s => s.ID == shopId).SingleOrDefault();
            if (shop == null)
            {
                return false;
            }
            var user = db.Users.Where(u => u.ID == shop.UserID).SingleOrDefault();
            if (user == null)
            {
                return false;
            }
            user.RelationStatus = (int)relationStatus;
            user.UpdatedAt = DateTime.Now;
            db.SaveChanges();
            if (relationStatus == RelationStatus.DoNotContact)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, $"Account For ShopId:{shopId} was marked as don't contact again", $"{EmailHelper.GetBoLinkHrefAndAM(shopId)}");
            }
            return true;
        }
        public static List<int> GetShopsWithPaidAccountInLast5Days()
        {
            DateTime fromDate = DateTime.Today.AddDays(-5);
            DateTime toDate = DateTime.Now;
            List<int> newPaidShops = new List<int>();
            var events = SystemEventHelper.Get(fromDate, toDate, AppTypes.TrafficBooster, SystemEventTypes.IPNCalls, SystemEventActions.NewPaidAccount);
            foreach (var shopEvent in events)
            {
                User user = null;
                if (shopEvent.UserID == null)
                {
                    user = GetUser(shopEvent.ShopID.Value);
                }
                else
                {
                    var db = DataHelper.GetStoreYaEntities();
                    user = db.Users.Where(x => x.ID == shopEvent.UserID).First();
                }
                if (GoodAccountHelper.IsGoodRevenue(user.RevenueRank.Value))
                {
                    newPaidShops.Add(shopEvent.ShopID.Value);
                }
            }
            return newPaidShops;
        }
        public static int? GetLastVisitedBoAtInDays(int shopID)
        {
            DateTime? lastVisitedBo = ShopsHelper.GetUser(shopID).LastVisitedBoAt;
            return GetLastVisitedBoAtInDays(lastVisitedBo);
        }

        public static int? GetLastVisitedBoAtInDays(DateTime? lastVisitedBo)
        {
            if (lastVisitedBo != null)
            {
                var today = DateTime.Now;
                var delta = (today - lastVisitedBo).Value.Days;
                return delta;
            }
            else
            {
                return null;
            }
        }
        public static ShopifyConnectedShop UninstallPaidShopifyApp(int shopId, int appType)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var connectedShopQuery = db.ShopifyConnectedShops.FirstOrDefault(cs => cs.ShopID == shopId && cs.StoreyaAppTypeID == (int)appType);
            if (connectedShopQuery != null)
            {
                connectedShopQuery.PermissionsScope = -1;
                connectedShopQuery.PaymentPlan = null;
                connectedShopQuery.UpdatedAt = DateTime.Now;
                db.SaveChanges();
                ConsoleAppHelper.WriteLogWithDB($"Unistalling Shopify connected app {(Shopify_StoreyaApp)appType}", connectedShopQuery.ShopID);
                return connectedShopQuery;
            }
            return null;
        }

        public static string DowngradeApp(User user, int shopID, int appID, string source, out bool success)
        {
            bool hasAgreement = false;
            try
            {
                Log4NetLogger.Info($"{source} action: {AppStoreManager.GetAppKeyForView(appID)} shop app was disabled by {user.Name}", shopID);
                List<string> canceleldSubs = new List<string>();
                if (AppStoreManager.IsLastAppInSubscription(appID, shopID))
                {
                    var app = AppStoreManager.GetAppSettings(shopID, appID);

                    if (app != null)
                    {
                        if (app.HasAgreements.HasValue)
                        {
                            string body = string.Format("Cancelled the last paid app {0} with agreement - {1}. The subscription {2} will be cancelled automatically.", appID, EmailHelper.GetBoLinkHref(shopID), app.SubscriptionID);
                            EmailHelper.SendEmail("<EMAIL>", $"Paid Shop App with agreement cancelation {source} " + user.Name + " (" + user.ID + ")" + " - shop " + shopID, body, null, null, true, "SYSTEM");
                            hasAgreement = true;
                            var agreementSub = SubscriptionManager.GetShopAgreementSubscription(shopID);
                            if (agreementSub != null)
                            {
                                AccountManager.CancelPaidAppSubscription(agreementSub, user.ID);
                                canceleldSubs.Add(agreementSub.OriginalSubscriptionID);
                            }

                        }
                        if (app.PaymentPlan > 0 && SubscriptionManager.IsPaymentGateWaySubscription(app.SubscriptionID))//SubscriptionManager.UseBlueSnapPayment(shopID)
                        {
                            StoreYaEntities db = DataHelper.GetStoreYaEntities();
                            Shop shop = db.Shops.Where(s => s.ID == shopID).Single();
                            User userToBeDowngraded = db.Users.Where(u => u.ID == shop.UserID).Single();
                            string bsSubsId = AppStoreManager.CancelPaymentForLastAppInSubscription(shop, userToBeDowngraded, appID);
                            canceleldSubs.Add(bsSubsId);
                        }

                    }
                }
                else
                {
                    var installation = AppStoreManager.GetAppSettings(shopID, appID);
                    if (installation != null)
                    {
                        if ((installation.PaymentPlan ?? 0) != 0)
                        {
                            //Remove app from subscription (not for TB)

                            var subscription = SubscriptionManager.GetLastShopSubscription(shopID);
                            if (subscription != null && AppStoreManager.HasChangebleSubscription(shopID))
                            {
                                canceleldSubs.Add(AppStoreManager.RemoveAppFromBlueSnapSubscription(shopID, appID));
                            }
                            else
                            {
                                string body = $"An app was disabled from {source}. ShopID: {shopID}<br/>App: {AppStoreManager.GetAppByID(appID).AppName}<br/>PaymentAdapterType: {(subscription != null ? (PaymentAdapterTypes)subscription.PaymentAdapterType : 0)}. The subscription was not changed automatically, so the charge for this app should be removed from subscription manually.";

                                string email = "<EMAIL>";
                                EmailHelper.SendEmail(email, "Change Plan Required! - shop " + shopID, body, null, null, true, "SYSTEM");
                            }
                            if (installation.HasAgreements.HasValue)
                            {
                                var agreementSub = SubscriptionManager.GetShopAgreementSubscription(shopID);
                                AccountManager.CancelPaidAppSubscription(agreementSub, user.ID);
                                canceleldSubs.Add(agreementSub.OriginalSubscriptionID);
                                string body = string.Format("Cancelled the last paid app {0} with agreement - {1}. The subscription {2} will be cancelled automatically.", appID, EmailHelper.GetBoLinkHref(shopID), installation.SubscriptionID);
                                EmailHelper.SendEmail("<EMAIL>", $"Paid Shop App with agreement cancelation {source} " + user.Name + " (" + user.ID + ")" + " - shop " + shopID, body, null, null, true, "SYSTEM");
                                hasAgreement = true;
                            }
                        }
                    }
                }
                var shopSubscriptions = SubscriptionManager.GetActiveShopAppSubscriptions(shopID, appID);
                foreach (var shopSubscription in shopSubscriptions)
                {
                    if (shopSubscription.BlueSnapSubscriptionID.HasValue || !string.IsNullOrEmpty(shopSubscription.FsAccountID))
                    {
                        if (!canceleldSubs.Contains(shopSubscription.OriginalSubscriptionID))
                        {
                            AccountManager.CancelPaidAppSubscription(shopSubscription, user.ID);
                        }
                    }
                }
                AppStoreManager.DisableShopApp(shopID, appID);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error($"Failed to disabled app from {source}.", ex, shopID);
                success = false;
                return "Unable to disable app.";
            }
            if (hasAgreement)
            {
                success = true;
                return "Shop subscription was not cancelled since It has an Agreement, Please contact Dev.";
            }

            success = true;
            return "Shop app was successfully disabled.";
        }
    }
}
