﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace Storeya.Core.Helpers
{
    public static class SslHelper
    {
        public static bool IsSecureConnection()
        {
            return IsSecureInAws() || HttpContext.Current.Request.IsSecureConnection;
        }

        private static bool IsSecureInAws()
        {
            return string.Equals(HttpContext.Current.Request
              .Headers["X-Forwarded-Proto"], "https", StringComparison.OrdinalIgnoreCase);
        }

        public static bool HealthcheckRequest()
        {
            return HttpContext.Current.Request.Url.ToString().ToLower().EndsWith("healthcheck");
        }


        public static string FetchUserIP()
        {
            if (HttpContext.Current == null || HttpContext.Current.Request == null)
            {
                return null;
            }

            string ip = HttpContext.Current.Request.Headers["X-Forwarded-For"];
            if (string.IsNullOrEmpty(ip))
            {
                ip = HttpContext.Current.Request.UserHostAddress;
            }
            if (ip.Contains(","))
            {
                try
                {
                    return ip.Split(',')[0];
                }
                catch 
                {
                    return ip;
                }                
            }
            return ip;
        }

        public static bool IsValidIp(string ipAddress)
        {
            if (string.IsNullOrEmpty(ipAddress))
            {
                return false;
            }

            // Split the IP address string into its components
            string[] ipComponents = ipAddress.Split('.');

            // Ensure that there are exactly 4 components
            if (ipComponents.Length != 4)
            {
                return false;
            }

            // Ensure that each component is a valid integer between 0 and 255
            foreach (string component in ipComponents)
            {
                if (!int.TryParse(component, out int componentInt) || componentInt < 0 || componentInt > 255)
                {
                    return false;
                }
            }

            // If all checks pass, the IP address is valid
            return true;
        }
    }
}
