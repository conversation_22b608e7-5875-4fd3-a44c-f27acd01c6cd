﻿using Newtonsoft.Json.Linq;
using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;

namespace Storeya.Core.Helpers
{
    public static class StoreyaApiHelper
    {
        private static string _internalApiKey = ConfigHelper.GetValue("StoreyaPrivateKey");
        private static string _apiUrl = ConfigHelper.GetValue("Api_Url");
        private static WebClient wc;
        private static string webhookUrl;
        private static int AppID;
        private static string UrlParams;

        public static void sendWebHook(int appID, string urlParams, string hookName)
        {
            AppID = appID;
            UrlParams = urlParams;

            webhookUrl = getWebhookUrlFromStoryaApi(hookName) + "?" + UrlParams;
            wc = new WebClient();
            string resultString = wc.DownloadString(webhookUrl);
        }

        private static string getWebhookUrlFromStoryaApi(string hookName)
        {

            wc = new WebClient();
            wc.Headers.Add("Authorization", _internalApiKey);

            string requestUrl = _apiUrl.TrimEnd('/') + "/webhooks/" + SequenceHelper.Encode(AppID);
            string result = wc.DownloadString(requestUrl);

            JObject hooks = JObject.Parse(result);
            return hooks[hookName].ToString();
        }
    }
}