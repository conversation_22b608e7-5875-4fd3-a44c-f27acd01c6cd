﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.UI;

namespace Storeya.Core.Helpers
{
    public static class StringFormatingExtention
    {
        public static string FormatWith(this string format, object source)
        {

            return FormatWith(format, null, source);

        }



        public static List<string> GetCleanupListNew(string format, string dummy_data)
        {
            List<string> f = null;

            ArrayList list = StripTagsCharArrayNew(format, dummy_data);

            if (list != null)
                f = list.Cast<string>().ToList();

            return f;
        }


        public static List<string> GetCleanupList(string format)
        {
            List<string> f = null;

            ArrayList list = StripTagsCharArray(format);

            if (list != null)
                f = list.Cast<string>().ToList();

            return f;
        }

        private static ArrayList StripTagsCharArrayNew(string format, string dummy_data)
        {
            ArrayList list = null;

            var sb = new StringBuilder();

            if (!string.IsNullOrEmpty(format))
            {
                char[] array = new char[format.Length];
                int arrayIndex = 0;
                bool inside = false;


                int startIndex = 0;

                for (int i = 0; i < format.Length; i++)
                {
                    char let = format[i];
                    if (let == '{')
                    {
                        inside = true;

                        if (i > 0)
                        {
                            list = Add(list, sb, dummy_data);
                            sb = new StringBuilder();
                        }

                        continue;
                    }

                    if (let == '}')
                    {
                        inside = false;

                        sb.Append(dummy_data);

                        if (i == format.Length-1)
                        {
                            list = Add(list, sb, dummy_data);
                        }

                        if (arrayIndex + 1 < format.Length - 1)
                        {
                            startIndex = i + 1;
                        }

                        continue;
                    }
                    if (!inside)
                    {
                        if (let == '(')
                        {
                            list = Add(list, sb, dummy_data);
                            sb = new StringBuilder();
                        }

                        sb.Append(let);

                        if(sb.ToString()==(dummy_data + " "))
                        {
                            list = Add(list, sb, dummy_data);
                            sb = new StringBuilder();
                        }

                    }

                    if (i == (format.Length - 1) && !inside)
                    {
                        list = Add(list, sb, dummy_data);
                    }
                }
            }

            //if (list != null)
            //{
            //    if (list.Contains(dummy_data))
            //    {
            //        list.Remove(dummy_data);
            //    }

            //    if (list.Contains(" " + dummy_data))
            //    {
            //        list.Remove(" " + dummy_data);
            //    }

            //    if (list.Contains(dummy_data + " "))
            //    {
            //        list.Remove(dummy_data + " ");
            //    }
            //}

            return list;
        }


        /// <summary>
        /// Remove property in {} from string using char array.
        /// </summary>
        public static ArrayList StripTagsCharArray(string source)
        {
            ArrayList list = null;

            var sb = new StringBuilder();

            if (!string.IsNullOrEmpty(source))
            {
                char[] array = new char[source.Length];
                int arrayIndex = 0;
                bool inside = false;


                int startIndex = 0;

                for (int i = 0; i < source.Length; i++)
                {
                    char let = source[i];
                    if (let == '{')
                    {
                        inside = true;

                        if (i > 0)
                        {
                            list = Add(list, sb);
                            sb = new StringBuilder();
                        }

                        continue;
                    }

                    if (let == '}')
                    {
                        inside = false;
                        if (arrayIndex + 1 < source.Length - 1)
                        {
                            startIndex = i + 1;
                        }

                        continue;
                    }
                    if (!inside)
                    {
                        sb.Append(let);
                    }

                    if (i == (source.Length - 1) && !inside)
                    {
                        list = Add(list, sb);
                    }
                }
            }
            return list;
        }

        private static ArrayList Add(ArrayList list, StringBuilder sb, string dummy_data = null)
        {
            string a = sb.ToString();

            if (list == null && a.StartsWith(","))
            {
                a = a.TrimStart(',');
            }

            if (a.Length > 0)
            {
                if (list == null)
                    list = new ArrayList();

                string prev = null;

                if (list.Count > 0)
                {
                    prev = list[list.Count - 1].ToString();
                }
                if (prev != null && !prev.EndsWith("'s ") && !prev.EndsWith(",") && !prev.EndsWith(", ") && !prev.EndsWith(dummy_data + " ")
                    && ((a == ", " || a == " " || a == dummy_data)
                       || prev == " "
                       || (prev != dummy_data + " " && prev.EndsWith(" "))
                       || (prev.Contains("(") && !prev.Contains(")") && (a == ")" || a == dummy_data + ")"))))
                {
                    string fixed_item = prev + a;
                    if (prev == " " && (a.StartsWith(",") || a.StartsWith("(") || a.StartsWith(" ")))
                        fixed_item = a;

                    list[list.Count - 1] = fixed_item;
                }
                else
                {
                    if (!list.Contains(a))
                        list.Add(a);
                }
            }
            return list;
        }

        public static string CleanupFormattedTitleNew(List<string> cleanupList, string formattedTitle)
        {
            string fixed_title = formattedTitle;
            if (cleanupList != null && cleanupList.Count > 0)
            {
                foreach (var item in cleanupList.OrderByDescending(x=>x.Length))
                {
                    if (formattedTitle.Contains(item) && item != ", " && item != " ")
                    {
                        int comma_count = item.Count(c => c == ',');

                        //if (item == " by " && !fixed_title.EndsWith(item))
                        //{
                        //    string temp_item = item + " ";
                        //    if (formattedTitle.Contains(temp_item))
                        //        fixed_title = fixed_title.Replace(temp_item, " ");
                        //}
                        //else                        

                        if (comma_count > 1)
                        {
                            fixed_title = fixed_title.Replace(item, ", ");
                        }
                        else
                            fixed_title = fixed_title.Replace(item, " ");
                    }
                }
            }

            fixed_title = fixed_title.Replace(" ,", ",");
            fixed_title = fixed_title.Replace(", ,", ",");
            fixed_title = fixed_title.Replace(",,,,,", ",");
            fixed_title = fixed_title.Replace(",,,,", ",");
            fixed_title = fixed_title.Replace(",,,", ",");
            fixed_title = fixed_title.Replace(",,", ",");

            fixed_title = fixed_title.Replace("      ", " ");
            fixed_title = fixed_title.Replace("     ", " ");
            fixed_title = fixed_title.Replace("    ", " ");
            fixed_title = fixed_title.Replace("   ", " ");
            fixed_title = fixed_title.Replace("  ", " ");

            fixed_title = fixed_title.Trim();

            if (fixed_title.EndsWith(","))
                fixed_title = fixed_title.TrimEnd(',');

            fixed_title = fixed_title.Trim();

            return fixed_title;
        }

        public static string CleanupFormattedTitle(string format, string formattedTitle)
        {
            string fixed_title = formattedTitle;

            fixed_title = fixed_title.Replace("(Size )", "");
            fixed_title = fixed_title.Replace(" ()", "");
            fixed_title = fixed_title.Replace(" 's ", " ");
            fixed_title = fixed_title.Replace(" -Piece", "");
            fixed_title = fixed_title.Replace(" ,", ",");
            fixed_title = fixed_title.Replace(", ,", ",");
            fixed_title = fixed_title.Replace(",,", ",");
            fixed_title = fixed_title.Replace("  ", " ");

            fixed_title = fixed_title.Trim();

            if (fixed_title.EndsWith(" by"))
                fixed_title = fixed_title.Replace(" by", "");

            if (fixed_title.EndsWith(","))
                fixed_title = fixed_title.TrimEnd(',');

            fixed_title = fixed_title.Trim();

            return fixed_title;
        }

        private static string FormatWithBase(this string format, IFormatProvider provider, object source)
        {
            if (format == null)
            {
                throw new ArgumentNullException("format");
            }
            Regex r = new Regex(@"(?<start>\{)+(?<property>[\w\.\[\]]+)(?<format>:[^}]+)?(?<end>\})+", RegexOptions.Compiled | RegexOptions.CultureInvariant | RegexOptions.IgnoreCase);

            List<object> values = new List<object>();
            string rewrittenFormat = r.Replace(format, delegate (Match m)
            {
                Group startGroup = m.Groups["start"];
                Group propertyGroup = m.Groups["property"];
                Group formatGroup = m.Groups["format"];
                Group endGroup = m.Groups["end"];

                values.Add((propertyGroup.Value == "0")
                    ? source
                    : DataBinder.Eval(source, propertyGroup.Value));

                return new string('{', startGroup.Captures.Count) + (values.Count - 1) + formatGroup.Value
                       + new string('}', endGroup.Captures.Count);
            });
            return string.Format(provider, rewrittenFormat, values.ToArray());
        }

        public static string FormatWith(this string format, IFormatProvider provider, object source)
        {
            if (format == null)
            {
                throw new ArgumentNullException("format");
            }
            int index = 0;
            Dictionary<int, string> styleList = new Dictionary<int, string>();
            //Check if Style exists in template and put it aside until template is formatted 
            Match match = Regex.Match(format, @"{OpenBracket}.*?", RegexOptions.Singleline);
            bool hasStyle = !match.Success;
            if (hasStyle)
            {
                string patternStyle = @"<style>.*?</style>";
                MatchCollection styleItems = Regex.Matches(format, patternStyle, RegexOptions.Singleline | RegexOptions.IgnoreCase);
                foreach (Match styleItem in styleItems)
                {
                    styleList.Add(index, styleItem.Value);
                    format = format.Replace(styleItem.Value, "@@_" + index);
                    index++;
                }
            }
            
            string results = format.FormatWithBase(provider, source);

            if (hasStyle)
            {
                foreach (var style in styleList)
                {
                    results = results.Replace("@@_" + style.Key, style.Value);
                }
            }
            return results;
        }
    }
}
