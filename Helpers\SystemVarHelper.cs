﻿using Storeya.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace Storeya.Core.Helpers
{
    public class SystemVarHelper
    {
        public static int GetValue(VariableTypes type)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            SystemVariable currentValue = db.SystemVariables.Where(c => c.VariableType == (int)type).SingleOrDefault();
            if (currentValue != null)
            {
                return currentValue.Value.Value;    
            }
            else
            {
                throw new Exception("Missing variable value for " + type.ToString());
            }
            
        }

        public static void SetValue(VariableTypes variableType, int newValue)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            SystemVariable variable = db.SystemVariables.Where(c => c.VariableType == (int)variableType).SingleOrDefault();
            if (variable == null)
            {
                throw new Exception("Missing variable value for " + variableType.ToString());
            }
            else
            {
                variable.Value = newValue;
                variable.UpdatedAt = DateTime.Now;
                db.SaveChanges();
            }
        }


    }

}
