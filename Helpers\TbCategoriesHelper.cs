﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;


namespace Storeya.Core.Helpers
{
    public enum BusinessTypes
    {
        None = 0,
        MyServices = 1,
        OnlineStore = 2
    }

    public class TbCategoriesHelper
    {
        static List<Category> _list;

        static List<Category> _onlineStoreCategories;
        static List<Category> _myServicesCategories;

        static List<Category> _myColombianServicesCategories;


        static TbCategoriesHelper()
        {
            FillMyServicesCategories();
            FillList();
            FillOnlineStoreCategories();

            FillColombianServiceList_New();
        }

        private static void FillColombianServiceList_New()
        {
            _myColombianServicesCategories = new List<Category>();
            _myColombianServicesCategories.Add(new Category() { ID = 101, Name = "Marketing publicitario", CallTitle = "Agencia de publicidad", CallDescription1 = "Manejamos su publicidad", CallDescription2 = "¡Consulta gratis disponible hoy!", SearchHeadline1 = "Respuesta rápida", SearchHeadline2 = "Consultas gratuitas ", SearchDescription = "Agencia de marketing  con años de experiencia en la industria." });
            _myColombianServicesCategories.Add(new Category() { ID = 102, Name = "Automotriz y Automóviles", CallTitle = "Servicios de autopartes", CallDescription1 = "Hasta 30% de descuento en repuestos", CallDescription2 = "Todas las piezas estan disponibles!", SearchHeadline1 = "Descuentos increíbles ", SearchHeadline2 = "Hasta 30% de descuento ", SearchDescription = "Gran variedad, entrega rápida, 100% de satisfacción garantizada." });
            _myColombianServicesCategories.Add(new Category() { ID = 103, Name = "Abogado de quiebras", CallTitle = "Abogado de quiebras", CallDescription1 = "Nos especializamos en quiebras", CallDescription2 = "Llama para hacer una cita ¡Hoy!", SearchHeadline1 = "Experiencia en quiebras", SearchHeadline2 = "Haga una cita hoy", SearchDescription = "Consulta gratis, flexibilidad de citas, horarios nocturnos, precios competitivos" });
            _myColombianServicesCategories.Add(new Category() { ID = 104, Name = "Alquiler de embarcaciones", CallTitle = "Alquiler de embarcaciones", CallDescription1 = "Descuentos en alquiler de botes", CallDescription2 = "Reserve bote para el fin de semana!", SearchHeadline1 = "Alquiler de embarcaciones", SearchHeadline2 = "Reserve uno hoy", SearchDescription = "Espaciosas cubiertas y una atmósfera incomparable.El lugar perfecto para grupos" });
            _myColombianServicesCategories.Add(new Category() { ID = 105, Name = "Detallando el carro", CallTitle = "Detallando el coche", CallDescription1 = "25 años de experiencia en detalles de autos.", CallDescription2 = "Haga su cita ¡Hoy!", SearchHeadline1 = "El mejor servicio de detallado automático", SearchHeadline2 = "Programe su cita", SearchDescription = "Programe su cita" });
            _myColombianServicesCategories.Add(new Category() { ID = 106, Name = "Taller de autos", CallTitle = "Taller de autos", CallDescription1 = "35 años en la industria automotriz", CallDescription2 = "Llame ya para atención inmediata!", SearchHeadline1 = "Taller de autos", SearchHeadline2 = "Solicite su presupuesto ", SearchDescription = "Profesionales expertos dispuestos a brindar a su automóvil atención de calidad" });
            _myColombianServicesCategories.Add(new Category() { ID = 108, Name = "Tapicería automotriz", CallTitle = "Tapicería de automóviles", CallDescription1 = "25 años en tapicería de automóviles", CallDescription2 = "¡Llama ahora para cotizar!", SearchHeadline1 = "Mejor taller taller tapicería ", SearchHeadline2 = "Reserve su cita", SearchDescription = "Consulta gratuita, flexibilidad de citas, precios competitivos." });
            _myColombianServicesCategories.Add(new Category() { ID = 110, Name = "Polarizado de ventanas", CallTitle = "Polarización de ventanas ", CallDescription1 = "Ventanas polarizadas para vehículos", CallDescription2 = "¡Llama ahora para cotizar!", SearchHeadline1 = "Mejor taller de polarización", SearchHeadline2 = "Reserve su cita hoy", SearchDescription = "Consulta gratuita, flexibilidad de citas, precios competitivos." });
            _myColombianServicesCategories.Add(new Category() { ID = 111, Name = "Limpieza de alfombra", CallTitle = "Limpieza de alfombras", CallDescription1 = "25 años de experiencia", CallDescription2 = "¡Llama ahora para cotizar!", SearchHeadline1 = "Mejor Limpieza de alfombras", SearchHeadline2 = "Reserve en línea hoy", SearchDescription = "Propiedad y operación familiar. Consultas y presupuestos gratuitos" });
            _myColombianServicesCategories.Add(new Category() { ID = 112, Name = "Centro de aprendizaje para niños", CallTitle = "Centro de aprendizaje ", CallDescription1 = "Educación de calidad para sus niños", CallDescription2 = "Llama hoy para reservar tu visita", SearchHeadline1 = "Centro de aprendizaje infantil", SearchHeadline2 = "Ayude a los niños a prosperar ", SearchDescription = "La inscripción está abierta para todos. Maestros altamente capacitados" });
            _myColombianServicesCategories.Add(new Category() { ID = 113, Name = "Reparación de ordenador", CallTitle = "Reparación computadoras", CallDescription1 = "Reparación de computadoras, hoy!", CallDescription2 = "Llame ya para atención inmediata!", SearchHeadline1 = "Reparación de computadoras", SearchHeadline2 = "Obtenga su cotización gratis", SearchDescription = "Experto IT Gestión y soporte. Sin cargo." });
            _myColombianServicesCategories.Add(new Category() { ID = 114, Name = "Consultoría y Entrenamiento", CallTitle = "Consultoría ", CallDescription1 = "Consultores y Entrenadores ", CallDescription2 = "Reserve su cita ¡Ahora!", SearchHeadline1 = "Consultoria de negocios", SearchHeadline2 = "Reserve en línea hoy", SearchDescription = "Profesional con experiencia. ¡accesible y confiable!" });
            _myColombianServicesCategories.Add(new Category() { ID = 115, Name = "Escuela de baile", CallTitle = "Escuela de baile", CallDescription1 = "Enseñanza de baile desde 2000.", CallDescription2 = "Reserve clase de baile hoy!", SearchHeadline1 = "Escuela de baile", SearchHeadline2 = "Reserve su primera clase hoy", SearchDescription = "Instructores experimentados. Precios accesible.Disponible para todos los niveles" });
            _myColombianServicesCategories.Add(new Category() { ID = 117, Name = "Diseñador - Sitio web", CallTitle = "Diseñador web", CallDescription1 = "Años de experiencia en diseño web", CallDescription2 = "Llame ahora para cotización gratis!", SearchHeadline1 = "Genial diseñador de sitios web", SearchHeadline2 = "Obtenga su cotización gratis", SearchDescription = "Diseño de sitio web a medida. Tarifas accesibles. Satifacción garantizada" });
            _myColombianServicesCategories.Add(new Category() { ID = 118, Name = "Diseñador - Interior / Hogar", CallTitle = "Diseñador de interiores", CallDescription1 = "Permítanos diseñar su hogar.", CallDescription2 = "Llame ahora para cotización gratis!", SearchHeadline1 = "Diseñador de interiores", SearchHeadline2 = "Obtenga su cotización gratis", SearchDescription = "¡Convierta su casa en el hogar de sus sueños! Consultas gratuitas disponibles." });
            _myColombianServicesCategories.Add(new Category() { ID = 119, Name = "Disc jockey", CallTitle = "Disc jockey", CallDescription1 = "Paquetes de DJ a media disponibles.", CallDescription2 = "¡Llama para una cotización hoy!", SearchHeadline1 = "Increibles evento de DJ", SearchHeadline2 = "Obtenga su consulta gratis", SearchDescription = "Diversión, DJs disponibles para su próximo evento. Paquetes personalizados" });
            _myColombianServicesCategories.Add(new Category() { ID = 120, Name = "Abogado de divorcios", CallTitle = "Abogado de divorcios", CallDescription1 = "Abogados de Divorcio de familia", CallDescription2 = "Llámenos ahora para una consulta!", SearchHeadline1 = "Abogado de divorcios", SearchHeadline2 = "Haga su cita hoy mismo", SearchDescription = "Consulta gratuita, confidencialidad, horarios nocturnos, precios competitivos." });
            _myColombianServicesCategories.Add(new Category() { ID = 121, Name = "Asesoramiento sobre drogadicción", CallTitle = "Consejero de Drogadicción", CallDescription1 = "La adicción de drogas es tratable.", CallDescription2 = "Llame ya para atención inmediata!", SearchHeadline1 = "Consulta sobre drogadicción", SearchHeadline2 = "Comience su recuperación hoy", SearchDescription = "Personal experimentado. Asistencia las 24 horas. Confidencialidad completa." });
            _myColombianServicesCategories.Add(new Category() { ID = 122, Name = "Limpieza en seco", CallTitle = "Limpieza en seco", CallDescription1 = "Tintorería servicio 24 horas", CallDescription2 = "Llámenos para recoger ahora!", SearchHeadline1 = "Increíble limpeza en seco", SearchHeadline2 = "Programe su limpieza hoy", SearchDescription = "Limpieza en seco de alta calidad. Sastrería" });
            _myColombianServicesCategories.Add(new Category() { ID = 123, Name = "Electricista", CallTitle = "Electricista", CallDescription1 = "Experiencia Electricistas.", CallDescription2 = "Llame ya para un servicio inmediato", SearchHeadline1 = "Increíble electricista", SearchHeadline2 = "Obtenga su presupuesto grátis", SearchDescription = "Con licencias y seguros . Tarifas accesibles. Servicio  5 estrellas garantizado!" });
            _myColombianServicesCategories.Add(new Category() { ID = 125, Name = "Orientación familiar", CallTitle = "Orientación familiar", CallDescription1 = "Asesoramiento de familia", CallDescription2 = "Llame hoy para reservar", SearchHeadline1 = "Asesoramiento con expertos", SearchHeadline2 = "Reserve su cita hoy mismo", SearchDescription = "Asesoramiento familiar privado, conveniente y accesible. Consultas gratis!" });
            _myColombianServicesCategories.Add(new Category() { ID = 126, Name = "Accesorios de moda", CallTitle = "Viste a la moda", CallDescription1 = "Para cualquier evento especial ", CallDescription2 = "¡Llama ahora para cotizar!", SearchHeadline1 = "Vamos a cuidar de ti", SearchHeadline2 = "Obtenga una cotización hoy", SearchDescription = "Mira nuestros increíbles diseños. Podemos adaptarnos a cualquier evento!" });
            _myColombianServicesCategories.Add(new Category() { ID = 127, Name = "Asesor financiero", CallTitle = "Asesor financiero", CallDescription1 = "Planifiquemos su futuro financiero.", CallDescription2 = "¡Habla con un asesor hoy!", SearchHeadline1 = "Asesoriamento financiero", SearchHeadline2 = "Haga su consulta gratis", SearchDescription = "Horarios flexibles  y precios competitivos." });
            _myColombianServicesCategories.Add(new Category() { ID = 128, Name = "Restauración por incendios", CallTitle = "Restauración por incendio", CallDescription1 = "Expertos restauración por incendio", CallDescription2 = "¡Llámanos hoy para aprender más!", SearchHeadline1 = "Restauración de incendios", SearchHeadline2 = "Obtenga su cotización gratis", SearchDescription = "Técnicos experimentados y capacitados. ¡Servicio rápido, amistoso y minucioso!" });
            _myColombianServicesCategories.Add(new Category() { ID = 129, Name = "Instalación de pisos", CallTitle = "Instalación de pisos", CallDescription1 = "Suelos e instalación de calidad.", CallDescription2 = "Llame para su estimación gratuita!", SearchHeadline1 = "Instalación de pisos", SearchHeadline2 = "Obtenga su cotización gratis", SearchDescription = "Más de 30 años de experiencia.  Precios accesibles" });
            _myColombianServicesCategories.Add(new Category() { ID = 130, Name = "Floreria", CallTitle = "Floreria", CallDescription1 = "Entrega de flores el mismo día.", CallDescription2 = "¡Ordene flores hoy!", SearchHeadline1 = "Increíble florista", SearchHeadline2 = "Ordene sus flores hoy", SearchDescription = "Entrega el mismo día. Gran variedad de hermosos arreglos!" });
            _myColombianServicesCategories.Add(new Category() { ID = 131, Name = "Servicios fúnebres", CallTitle = "Servicios fúnebres", CallDescription1 = "Funeraria 24 horas.", CallDescription2 = "Llámenos hoy, podemos ayudarlo.", SearchHeadline1 = "Servicio funerario", SearchHeadline2 = "Aprende más", SearchDescription = "Servicios funerarios profesionales a un precio accesible" });
            _myColombianServicesCategories.Add(new Category() { ID = 132, Name = "Tapicería de muebles", CallTitle = "Tapicería de muebles", CallDescription1 = "25 años de experiencia en tapicería", CallDescription2 = "¡Consiga su cotización grátis!", SearchHeadline1 = "Excelente tienda de tapicería", SearchHeadline2 = "¡Consiga su cotización grátis!", SearchDescription = "Hecho a mano para adaptarse a su estilo y gustos! Más de 30 años de experiencia." });
            _myColombianServicesCategories.Add(new Category() { ID = 133, Name = "Reparación de Puertas de Garaje", CallTitle = "Reparación Puertas Garaje", CallDescription1 = "Puertas de garaje con licencia.", CallDescription2 = "Reserve una cita el mismo día!", SearchHeadline1 = "Reparación puertas de garaje", SearchHeadline2 = "Reserve una cita el mismo día", SearchDescription = "Profesionales con licencia. Precios accesibles y flexibilidad " });
            _myColombianServicesCategories.Add(new Category() { ID = 134, Name = "Contratista general", CallTitle = "Contratista general", CallDescription1 = "20 años de experiencia ", CallDescription2 = "Consulta gratis, llámenos hoy!", SearchHeadline1 = "Contratista general", SearchHeadline2 = "Reserve su consulta gratis", SearchDescription = "Servicio de alta calidad y accesible desde 1984. Totalmente regulado" });
            _myColombianServicesCategories.Add(new Category() { ID = 136, Name = "Peluquería", CallTitle = "Peluquería", CallDescription1 = "Haga que su cabello se vea genial.", CallDescription2 = "LLame para reservar un turno", SearchHeadline1 = "Peluquería", SearchHeadline2 = "LLame para reservar un turno", SearchDescription = "Cortes de calidad. Permítanos ayudarlo a verse lo mejor posible!" });
            _myColombianServicesCategories.Add(new Category() { ID = 137, Name = "Personal de mantenimiento", CallTitle = "Personal de mantenimiento", CallDescription1 = "25 años de experiencia", CallDescription2 = "Llame hoy para reservar ", SearchHeadline1 = "Increíble Handyman", SearchHeadline2 = "Obtenga su cotización gratis", SearchDescription = "¡Haga el trabajo bien! Precios accesibles y servicio el mismo día." });
            _myColombianServicesCategories.Add(new Category() { ID = 140, Name = "Hogar & Jardín", CallTitle = "Hogar & Jardín", CallDescription1 = "Lo  que necesites, lo tenemos.", CallDescription2 = "Llame hoy para obtener cotización", SearchHeadline1 = "Vamos a cuidar de ti", SearchHeadline2 = "Obtenga una cotización hoy", SearchDescription = "¡Tenemos todo lo que necesita para su casa o jardín!" });
            _myColombianServicesCategories.Add(new Category() { ID = 141, Name = "Hotel", CallTitle = "Reserva de hotel", CallDescription1 = "Hasta 80% de descuento", CallDescription2 = "Llame ya y reserve una habitación!", SearchHeadline1 = "Increíbles ofertas de hoteles", SearchHeadline2 = "Ahorre hasta 80%", SearchDescription = "Servicio al cliente 24/7. Rápido, y seguro. Reserve su habitación hoy!" });
            _myColombianServicesCategories.Add(new Category() { ID = 142, Name = "Empleada doméstica", CallTitle = "Empleadas Domésticas", CallDescription1 = "9 años de experiencia", CallDescription2 = "Haga su reservación!", SearchHeadline1 = "Empleada doméstica", SearchHeadline2 = "Reserve en línea hoy", SearchDescription = "Más de 30 años de experiencia. Precios accesibles" });
            _myColombianServicesCategories.Add(new Category() { ID = 143, Name = "HVAC", CallTitle = "Servicio de climatización", CallDescription1 = "Contratistas de HVAC con licencia.", CallDescription2 = "¡Llámanos ahora!", SearchHeadline1 = "Contratistas de climatización", SearchHeadline2 = "Obtenga una cotización hoy", SearchDescription = "Obtenga una cotización gratis hoy" });
            _myColombianServicesCategories.Add(new Category() { ID = 144, Name = "Diseñador de interiores", CallTitle = "Diseñador de interiores", CallDescription1 = "Permítanos diseñar su hogar.", CallDescription2 = "Llame para su cotización gratis hoy", SearchHeadline1 = "Diseñador de interiores", SearchHeadline2 = "¡Consiga su cotización grátis!", SearchDescription = "¡Convierta su casa en el hogar de sus sueños! Consultas gratuitas disponibles." });
            _myColombianServicesCategories.Add(new Category() { ID = 145, Name = "Servicio de limpieza", CallTitle = "Conserjeria", CallDescription1 = "Conserjes experimentados.", CallDescription2 = "Pida su cotización gratis hoy!", SearchHeadline1 = "Servicios de limpieza", SearchHeadline2 = "Obtenga una cotización hoy", SearchDescription = "Limpiadores de confianza.Servicio 5 estrellas. ¡Disponibilidad al día siguiente!" });
            _myColombianServicesCategories.Add(new Category() { ID = 146, Name = "Paisajismo", CallTitle = "Paisajismo y jardinería", CallDescription1 = "Hasta 30% descuento primera visita", CallDescription2 = "Llame hoy para pedir una visita", SearchHeadline1 = "Increíble paisajista", SearchHeadline2 = "Obtenga presupuesto grátis", SearchDescription = "Embellece tu espacio exterior. Ofrecemos una gran variedad de servicios" });
            _myColombianServicesCategories.Add(new Category() { ID = 147, Name = "Depilación láser", CallTitle = "La depilación láser", CallDescription1 = "Depilación láser de alta calidad.", CallDescription2 = "Llame hoy para pedir un turno", SearchHeadline1 = "La depilación láser", SearchHeadline2 = "Reserve su cita hoy", SearchDescription = "Años de experiencia. Turnos nocturnos y  durante fines de semana disponibles" });
            _myColombianServicesCategories.Add(new Category() { ID = 148, Name = "Cuidado del césped", CallTitle = "Cuidado del césped", CallDescription1 = "Gran experiencia - Llame Ya!", CallDescription2 = "Llame ya para su cotización gratis", SearchHeadline1 = "Servicio de cuidado del césped", SearchHeadline2 = "Obtenga una cotización hoy", SearchDescription = "Experiencia y precios accesibles. ¡Servicio rápido, amistoso y minucioso!" });
            _myColombianServicesCategories.Add(new Category() { ID = 149, Name = "Servicio de limosina", CallTitle = "Servicio de limosina", CallDescription1 = "Pasea con estilo en una limusina.", CallDescription2 = "¡Ordene su limusina hoy!", SearchHeadline1 = "Servicio de limosina", SearchHeadline2 = "Ordene su limusina hoy", SearchDescription = "Tarifas especiales para grupos. Más de 25 años de excelente servicio." });
            _myColombianServicesCategories.Add(new Category() { ID = 151, Name = "Consejería Matrimonial", CallTitle = "Consejería Matrimonial", CallDescription1 = "30 años de experiencia", CallDescription2 = "Reserve su cita ¡Hoy!", SearchHeadline1 = "Gran Consejero Matrimonial ", SearchHeadline2 = "Reserve su cita ahora", SearchDescription = "Asesoramiento matrimonial conveniente y accesible. Consultas gratis!" });
            _myColombianServicesCategories.Add(new Category() { ID = 153, Name = "Profesor de matemáticas", CallTitle = "Profesor de matemáticas", CallDescription1 = "Tutor 1-a-1 accesible.", CallDescription2 = "Cierre la primera clase de su hijo!", SearchHeadline1 = "Increíble tutor de matemáticas", SearchHeadline2 = "Éxito académico y menos estrés", SearchDescription = "Tutoría personalizada, accesible 1-a-1. Garantía de satisfacción !" });
            _myColombianServicesCategories.Add(new Category() { ID = 154, Name = "Agente hipotecario", CallTitle = "Agente hipotecario", CallDescription1 = "Nos especializamos en hipotecas.", CallDescription2 = "¡Llámenos hoy!", SearchHeadline1 = "Increíble  agente hipotecario", SearchHeadline2 = "Reserva una cita ahora", SearchDescription = "Agendar una cita de trabajo" });
            _myColombianServicesCategories.Add(new Category() { ID = 156, Name = "Músico", CallTitle = "Músico", CallDescription1 = "Profesor de guitarra", CallDescription2 = "¡Reserve su evento!", SearchHeadline1 = "Increíble Músico", SearchHeadline2 = "Vea nuestra disponibilidad", SearchDescription = "Diversión, músicos salientes disponibles para su próximo evento!" });
            _myColombianServicesCategories.Add(new Category() { ID = 157, Name = "Profesor de música", CallTitle = "Profesor de música", CallDescription1 = "Clases privadas de música", CallDescription2 = "¡Reserve su primera lección hoy!", SearchHeadline1 = "Increíble profesor de música", SearchHeadline2 = "Reserve su primera lección hoy", SearchDescription = "Instructores experimentados y divertidos. Todas las edades y niveles" });
            _myColombianServicesCategories.Add(new Category() { ID = 158, Name = "Manicura", CallTitle = "Manicura", CallDescription1 = "30% descuento para clientes nuevos", CallDescription2 = "Llame hoy  para reservar su turno", SearchHeadline1 = "Manicura", SearchHeadline2 = "Reserve su turno ahora", SearchDescription = "Más de 30 años brindando un excelente servicio para hacerla ver lo mejor posible" });
            _myColombianServicesCategories.Add(new Category() { ID = 163, Name = "Pintor", CallTitle = "Pintor", CallDescription1 = "Pintor - 15 años de experiencia.", CallDescription2 = "¡Llama para una cotización hoy!", SearchHeadline1 = "Pintor", SearchHeadline2 = "Obtenga su presupuesto grátis", SearchDescription = "Mejor Compañía de pintura para el hogar. Satifacción garantizada" });
            _myColombianServicesCategories.Add(new Category() { ID = 164, Name = "Entrenador personal", CallTitle = "Entrenador personal", CallDescription1 = "Entrenador personal experimentado.", CallDescription2 = "Reserve su entrenador personal hoy!", SearchHeadline1 = "Entrenador personal", SearchHeadline2 = "Obtenga su consulta gratis", SearchDescription = "Visite nuestro sitio ahora para ver nuestro increíble gimnasio" });
            _myColombianServicesCategories.Add(new Category() { ID = 165, Name = "Control de plagas", CallTitle = "Control de plagas", CallDescription1 = "Control de plagas con experiencia", CallDescription2 = "Llame para su inspección gratuita!", SearchHeadline1 = "Control de plagas", SearchHeadline2 = "Obtenga su inspección gratuita", SearchDescription = "Eliminación rápida de plagas. Costos accesibles. ¡Satisfacción garantizada!" });
            _myColombianServicesCategories.Add(new Category() { ID = 167, Name = "Fotógrafo", CallTitle = "Fotógrafo", CallDescription1 = "Fotógrafo: Estudio o exteriores", CallDescription2 = "Llame y reserve hoy!", SearchHeadline1 = "Fotógrafo", SearchHeadline2 = "Programe su cita", SearchDescription = "25 años de experiencia en Estudio y exteriores. Tarifas accesibles." });
            _myColombianServicesCategories.Add(new Category() { ID = 170, Name = "Fontanero", CallTitle = "Fontanero", CallDescription1 = "Años de experiencia como fontanero.", CallDescription2 = "Llame ahora y haga su reserva!", SearchHeadline1 = "Fontanero", SearchHeadline2 = "Obtenga una cotización hoy", SearchDescription = "Servicio profesional y rápido. Tarifas accesibles!" });
            _myColombianServicesCategories.Add(new Category() { ID = 171, Name = "Cuidador privado", CallTitle = "Cuidador privado", CallDescription1 = "Cuidadores  profesionales.", CallDescription2 = "¡Llame hoy para aprender más!", SearchHeadline1 = "Cuidador privado", SearchHeadline2 = "Obtenga una consulta gratis", SearchDescription = "Cuidadores privados con experiencia  disponibles para ayudarlo!" });
            _myColombianServicesCategories.Add(new Category() { ID = 172, Name = "Investigador privado", CallTitle = "Investigador privado", CallDescription1 = "Años en investigaciones privadas", CallDescription2 = "Llame para una consulta grátis!", SearchHeadline1 = "Investigador privado", SearchHeadline2 = "Bajas tasas por hora", SearchDescription = "Investigaciones Domésticas, Penales y Civiles. ¡Más de 30 años de experiencia!" });
            _myColombianServicesCategories.Add(new Category() { ID = 173, Name = "Psíquico", CallTitle = "Psíquico", CallDescription1 = "Psíquico -10% de descuento", CallDescription2 = "Llame para reservar su lectura hoy!", SearchHeadline1 = "Psíquico", SearchHeadline2 = "Reserve su lectura hoy", SearchDescription = "¡Descubre la verdad sobre todo lo que sucede en tu vida!" });
            _myColombianServicesCategories.Add(new Category() { ID = 175, Name = "Agente de bienes raíces", CallTitle = "Agente de bienes raíces", CallDescription1 = "Agente inmobiliario con licencia.", CallDescription2 = "¡Llame hoy para aprender más!", SearchHeadline1 = "Agente inmobiliario", SearchHeadline2 = "Encuentra tu casa de ensueño", SearchDescription = "¡Visite nuestro sitio ahora para ver casas en venta y excelentes testimonios!" });
            _myColombianServicesCategories.Add(new Category() { ID = 176, Name = "Restaurante", CallTitle = "Restaurante", CallDescription1 = "Cene  en nuestro restaurante.", CallDescription2 = "Haga su reserva para esta noche!", SearchHeadline1 = "Restaurante", SearchHeadline2 = "Haga su reserva", SearchDescription = "¡Visite nuestro sitio ahora para ver nuestro menú!" });
            _myColombianServicesCategories.Add(new Category() { ID = 177, Name = "Techos", CallTitle = "Reparación de techos", CallDescription1 = "Excelente reparación de techos ", CallDescription2 = "¡Llama a nuestro equipo ahora!", SearchHeadline1 = "Reparador de techos", SearchHeadline2 = "Obtenga su presupuesto grátis", SearchDescription = "Más de 25 años de experiencia en techado, servicio rápido, confiable y accesible" });
            _myColombianServicesCategories.Add(new Category() { ID = 178, Name = "Sistema de seguridad", CallTitle = "Sistema de seguridad", CallDescription1 = "Sistema de seguridad 24/7.", CallDescription2 = "¡Llama ahora para aprender más!", SearchHeadline1 = "Sistema de seguridad", SearchHeadline2 = "Protección 24/7", SearchDescription = "Más de 25 años de experiencia . Sin contratos a largo plazo. ¡Aprende más!" });
            _myColombianServicesCategories.Add(new Category() { ID = 179, Name = "Almacenamiento", CallTitle = "Almacenamiento", CallDescription1 = "Unidades de Almacenamiento", CallDescription2 = "Llámenos para tarifas mensuales!", SearchHeadline1 = "Almacenamiento", SearchHeadline2 = "Reserve su unidad en línea hoy", SearchDescription = "Unidades de almacenamiento con control climático. Sin contratos y seguro." });
            _myColombianServicesCategories.Add(new Category() { ID = 180, Name = "Spa", CallTitle = "Servicios de spa", CallDescription1 = "Date un capricho en nuestro Spa.", CallDescription2 = "Hacer una cita ¡Hoy!", SearchHeadline1 = "Increíble spa", SearchHeadline2 = "Reserve en línea hoy", SearchDescription = "Visite nuestro sitio ahora para ver nuestros excelentes servicios y testimonios!" });
            _myColombianServicesCategories.Add(new Category() { ID = 181, Name = "Deportes", CallTitle = "Vamos a cuidar de ti", CallDescription1 = "Los mejores entrenadores en el área", CallDescription2 = "Llame para fijar un entrenamiento", SearchHeadline1 = "Vamos a cuidar de ti", SearchHeadline2 = "Reserve un entrenador hoy", SearchDescription = "Visite nuestro sitio ahora para ver nuestros servicios y excelentes testimonios!" });
            _myColombianServicesCategories.Add(new Category() { ID = 182, Name = "Piscina", CallTitle = "Piscina", CallDescription1 = "Reparación de piscinas.", CallDescription2 = "¡Llame para su consulta gratuita!", SearchHeadline1 = "Servicio para piscinas", SearchHeadline2 = "Obtenga una cotización hoy", SearchDescription = "Servicio de Piscina. Mantenimiento y Reparación." });
            _myColombianServicesCategories.Add(new Category() { ID = 183, Name = "Bronceado", CallTitle = "Salón de bronceado", CallDescription1 = "Obtenga hasta 30% de descuento ", CallDescription2 = "¡Llame para reservar su turno!", SearchHeadline1 = "Salón de bronceado", SearchHeadline2 = "Programe su cita", SearchDescription = "Visite nuestro sitio para ver nuestras promociones y excelentes testimonios" });
            _myColombianServicesCategories.Add(new Category() { ID = 186, Name = "Viajar", CallTitle = "Agencia de viajes", CallDescription1 = "20 años de experiencia en viajes", CallDescription2 = "Las mejores ofertas de vuelo", SearchHeadline1 = "Planificamos su próximo viaje", SearchHeadline2 = "Reserve en línea hoy", SearchDescription = "¡Ofertas increíbles para vuelos, hoteles y renta de autos!" });
            _myColombianServicesCategories.Add(new Category() { ID = 187, Name = "Veterinario", CallTitle = "Veterinario", CallDescription1 = "Veterinarios certificados", CallDescription2 = "Llame para reservar su turno", SearchHeadline1 = "Veterinario", SearchHeadline2 = "Programe su visita", SearchDescription = "Cuidado Veterinario calificado y accesible!" });

        }

        public static Category GetCategory(TrafficBooster tbSettings)
        {
            Category category = null;

            int? categoryID = tbSettings.TrafficCategoryID;
            if (!categoryID.HasValue || categoryID == 0)
            {
                if (tbSettings.IsServiceProvider == (int)BusinessTypes.MyServices
                    || tbSettings.IsServiceProvider == (int)BusinessTypes.None)
                    categoryID = 138;//set health and beauty as default for 
                else
                    categoryID = 506;
            }

            if (tbSettings.IsServiceProvider == (int)BusinessTypes.OnlineStore)
                category = GetOnlineStoreCategoryByID(categoryID);
            else
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                Shop shop = db.Shops.Where(x => x.ID == tbSettings.ShopID).SingleOrDefault();
                if (shop != null)
                {
                    if ((shop.CatalogSourcePlatform.HasValue && shop.CatalogSourcePlatform.Value == (int)CatalogSourcePlatforms.Colombian)
                        || (!string.IsNullOrEmpty(shop.StoreLocale) && shop.StoreLocale == "es"))
                    {
                        category = TbCategoriesHelper.GetMyColombianServicesCategoryByID(categoryID);
                    }
                    else
                    {
                        category = TbCategoriesHelper.GetMyServicesCategoryByID(categoryID);
                    }
                }
            }

            return category;
        }

        public static Category GetCategory(int isServiceProvider, int categoryID, int? shopID = null, int? catalogSourcePlatform = null, string locale = null)
        {
            Category category = null;

            try
            {
                if (isServiceProvider == (int)BusinessTypes.MyServices || isServiceProvider == (int)BusinessTypes.None)
                {
                    if ((catalogSourcePlatform.HasValue && catalogSourcePlatform.Value == (int)CatalogSourcePlatforms.Colombian)
                        || (!string.IsNullOrEmpty(locale) && locale == "es"))
                    {
                        category = TbCategoriesHelper.GetMyColombianServicesCategoryByID(categoryID);
                    }
                    else
                    {
                        category = TbCategoriesHelper.GetMyServicesCategoryByID(categoryID);
                    }
                }
                else if (isServiceProvider == (int)BusinessTypes.OnlineStore)
                    category = GetOnlineStoreCategoryByID(categoryID);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to extract category object for categoryID: {0}", categoryID), ex, (shopID ?? 0));
            }

            return category;
        }

        public static void FillColombianServiceList()
        {
            _myColombianServicesCategories = new List<Category>();
            _myColombianServicesCategories.Add(new Category() { ID = 100, SearchDescription = "¡Visite nuestro sitio para ver nuestra genial clínica y excelentes testimonios!", SearchHeadline2 = "Alivio natural del dolor", SearchHeadline1 = "Increíble acupunturista", Name = "Acupuntura", CallDescription1 = "Acupunturista delicado y efectivo.", CallDescription2 = "¡Llame para aliviar el dolor de forma natural!", CallTitle = "Servicios de acupuntura" });
            _myColombianServicesCategories.Add(new Category() { ID = 105, SearchDescription = "Profesionales experimentados. Económicos y confiables. ¡Servicio 5 estrellas!", SearchHeadline2 = "Solicite su presupuesto gratis", SearchHeadline1 = "Documentos de automóviles", Name = "Trámite de documentos de autos (llevar al auto para pruebas)", CallDescription1 = "Años de experiencia en la industria automotriz.", CallDescription2 = "¡Llame para obtener su presupuesto gratis hoy!", CallTitle = "Trámites para autos" });
            _myColombianServicesCategories.Add(new Category() { ID = 107, SearchDescription = "Precios más bajos garantizados. Asistencia las 24 h. ¡Gran variedad de autos!", SearchHeadline2 = "30 % menos en su alquiler", SearchHeadline1 = "Increíble alquiler de autos", Name = "Alquiler de autos", CallDescription1 = "Hasta 30 % de descuento en su alquiler de automóviles.", CallDescription2 = "¡Llame para alquilar un auto hoy!", CallTitle = "Alquiler de autos" });
            _myColombianServicesCategories.Add(new Category() { ID = 109, SearchDescription = "Servicio completo y autoservicio. ¡Abierto 7 días a la semana para su comodidad!", SearchHeadline2 = "Precios más bajos garantizados", SearchHeadline1 = "Increíble lavado de autos", Name = "Lavado de autos", CallDescription1 = "Servicio de lavado rápido y completo.", CallDescription2 = "¡Haga su cita hoy mismo!", CallTitle = "Lavado de autos completo" });
            _myColombianServicesCategories.Add(new Category() { ID = 116, SearchDescription = "¡Obtenga su sonrisa soñada! Se aceptan nuevos pacientes. Precios accesibles.", SearchHeadline2 = "Reserve su cita hoy", SearchHeadline1 = "Odontólogo con experiencia", Name = "Odontología", CallDescription1 = "Cuidado dental de alta calidad.", CallDescription2 = "¡Llame para consultar un odontólogo hoy!", CallTitle = "Odontólogo" });
            _myColombianServicesCategories.Add(new Category() { ID = 124, SearchDescription = "Personal experimentado y buenos precios. ¡Déjenos organizar su próximo evento!", SearchHeadline2 = "Obtenga su consulta gratis", SearchHeadline1 = "Organizo eventos inolvidables", Name = "Organización de eventos", CallDescription1 = "¡Permítanos planear su próximo evento!", CallDescription2 = "Llame para su consulta gratuita.", CallTitle = "Planificador de eventos" });
            _myColombianServicesCategories.Add(new Category() { ID = 135, SearchDescription = "Noches y fines de semana disponibles. ¡Se aceptan nuevos pacientes!", SearchHeadline2 = "Reserve una cita el mismo día", SearchHeadline1 = "Médicos general de calidad", Name = "Médico general", CallDescription1 = "Consulte con un médico general", CallDescription2 = "Reserve una cita hoy mismo!", CallTitle = "Médico general" });
            _myColombianServicesCategories.Add(new Category() { ID = 113, SearchDescription = "¡Obtenga un trabajo bien hecho! Precios accesibles y servicio el mismo día.", SearchHeadline2 = "Obtenga su cotización gratis", SearchHeadline1 = "Increíbles mejoras del hogar", Name = "Mantenimiento y Reparaciones Domésticas", CallDescription1 = "Servicio y experiencia inigualables.", CallDescription2 = "Llame para reservar una visita hoy mismo!", CallTitle = "Reparaciones domésticas" });
            _myColombianServicesCategories.Add(new Category() { ID = 138, SearchDescription = "¡Visitenos para conocer nuestras instalaciones y excelente servicio!", SearchHeadline2 = "Obtenga una cotización hoy", SearchHeadline1 = "Permítanos cuidar de usted.", Name = "Salud y belleza", CallDescription1 = "Nosotros hacemos magia.", CallDescription2 = "¡Llame ahora para obtener una cotización!", CallTitle = "Déjenos cuidarle" });
            _myColombianServicesCategories.Add(new Category() { ID = 139, SearchDescription = "Cobertura de calidad. Cotización en 2 minutos. ¡Encuentre su plan adecuado!", SearchHeadline2 = "La mejor cobertura", SearchHeadline1 = "Empresa de seguros de salud", Name = "Seguro de salud", CallDescription1 = "Asegurándonos de que esté cubierto.", CallDescription2 = "¡Llame hoy para una cotización!", CallTitle = "Aseguradora de salud" });
            _myColombianServicesCategories.Add(new Category() { ID = 150, SearchDescription = "Disponible 24 h para emergencias. Servicio de calidad y confiable. Tarifa plana.", SearchHeadline2 = "Cerrajero local las 24 horas", SearchHeadline1 = "El mejor cerrajero", Name = "Cerrajero", CallDescription1 = "Servicio de cerrajería 24/7.", CallDescription2 = "¡Llame ahora y obtenga un 15 % de descuento!", CallTitle = "Mejor cerrajero" });
            _myColombianServicesCategories.Add(new Category() { ID = 152, SearchDescription = "¡Visite nuestro sitio para ver nuestra genial clínica y excelentes testimonios!", SearchHeadline2 = "Programe su cita", SearchHeadline1 = "El mejor masajista", Name = "Terapia de masajes", CallDescription1 = "Terapeutas de masaje con licencia.", CallDescription2 = "Reserve su cita hoy mismo.", CallTitle = "Masajista" });
            _myColombianServicesCategories.Add(new Category() { ID = 175, SearchDescription = "Servicio rápido y confiable. Amplia experiencia. Precios accesibles.", SearchHeadline2 = "Obtenga su presupuesto gratis", SearchHeadline1 = "Increíble servicio de mudanzas", Name = "Agentes de mudanza", CallDescription1 = "Empresa de mudanzas con servicio completo.", CallDescription2 = "¡Llámenos para un presupuesto gratuito!", CallTitle = "Mudanzas increíbles" });
            _myColombianServicesCategories.Add(new Category() { ID = 153, SearchDescription = "Instructores experimentados y divertidos. ¡Todas las edades y niveles!", SearchHeadline2 = "Reserve su primera clase hoy", SearchHeadline1 = "Increíble profesor", Name = "Clases privadas", CallDescription1 = "Lecciones privadas disponibles.", CallDescription2 = "¡Reserve su primera lección hoy!", CallTitle = "Expertos profesionales" });
            _myColombianServicesCategories.Add(new Category() { ID = 159, SearchDescription = "¡Visite nuestra web y vea nuestra increíble clínica y excelentes testimonios!", SearchHeadline2 = "Programe su cita", SearchHeadline1 = "El mejor neurólogo", Name = "Neurólogo", CallDescription1 = "Atención neurológica personalizada.", CallDescription2 = "¡Reserve su cita hoy mismo!", CallTitle = "Neurólogo de primera" });
            _myColombianServicesCategories.Add(new Category() { ID = 160, SearchDescription = "¡Visite nuestra web y vea nuestra increíble clínica y excelentes testimonios!", SearchHeadline2 = "Reserve su cita en línea", SearchHeadline1 = "Ginecólogo obstetra de calidad", Name = "Ginecobstetra", CallDescription1 = "Gineco-obstetra - Nos importa su salud.", CallDescription2 = "Llame para reservar su cita", CallTitle = "Gineco-obstetra experto" });
            _myColombianServicesCategories.Add(new Category() { ID = 161, SearchDescription = "¡Visite nuestra web y vea nuestra increíble clínica y excelentes testimonios!", SearchHeadline2 = "Reserve su cita", SearchHeadline1 = "Optometrista de calidad", Name = "Optometrista", CallDescription1 = "Atención oftalmológica integral para la familia.", CallDescription2 = "¡Reserve una cita con su optometrista hoy!", CallTitle = "Optometrista de primera" });
            _myColombianServicesCategories.Add(new Category() { ID = 162, SearchDescription = "¡Visite nuestra web y vea nuestra increíble clínica y excelentes testimonios!", SearchHeadline2 = "Programe un examen ocular hoy", SearchHeadline1 = "El mejor oculista", Name = "Oculista", CallDescription1 = "Óptico - Nos importa su salud.", CallDescription2 = "¡Programe un examen de la vista hoy!", CallTitle = "Óptico increíble" });
            _myColombianServicesCategories.Add(new Category() { ID = 166, SearchDescription = "Visite nuestra web y vea nuestras magníficas instalaciones y buenas críticas", SearchHeadline2 = "Reserve una cita hoy", SearchHeadline1 = "Permítanos cuidar a su mascota", Name = "Mascotas y animales", CallDescription1 = "Instalaciones increíbles, excelentes reseñas", CallDescription2 = "¡Llame para obtener una cotización hoy!", CallTitle = "Colegio y Guardería" });
            _myColombianServicesCategories.Add(new Category() { ID = 168, SearchDescription = "¡Visite nuestra web y vea nuestra increíble clínica y excelentes testimonios!", SearchHeadline2 = "Programe su cita", SearchHeadline1 = "Fisioterapeuta de calidad", Name = "Fisioterapia", CallDescription1 = "Terapeutas físicos con licencia.", CallDescription2 = "Citas disponibles para el mismo día, llame ahora.", CallTitle = "Fisioterapeuta de primera" });
            _myColombianServicesCategories.Add(new Category() { ID = 169, SearchDescription = "¡Visite nuestra web y vea nuestra increíble clínica y excelentes testimonios!", SearchHeadline2 = "Programe su consulta", SearchHeadline1 = "El mejor cirujano plástico", Name = "Cirujano plástico", CallDescription1 = "Cirujano certificado por el Consejo de los Estados Unidos.", CallDescription2 = "¡Llame para su consulta gratuita!", CallTitle = "Mejor Cirujano Plástico" });
            _myColombianServicesCategories.Add(new Category() { ID = 174, SearchDescription = "Obtenga ayuda de un psicólogo con licencia. ¡Más de 30 años de experiencia!", SearchHeadline2 = "Programe su consulta", SearchHeadline1 = "Psicólogo de calidad superior", Name = "Psicólogo", CallDescription1 = "Psicólogos con licencia.", CallDescription2 = "¡Llame para su consulta gratuita!", CallTitle = "Psicólogo de primera" });
            _myColombianServicesCategories.Add(new Category() { ID = 184, SearchDescription = "Ofrecemos muchos años de servicio confiable las 24 horas.", SearchHeadline2 = "Reserve en línea ahora", SearchHeadline1 = "El mejor servicio de taxi", Name = "Taxi", CallDescription1 = "Compañía de taxis las 24 horas.", CallDescription2 = "¡Llame ahora!", CallTitle = "Compañía de taxis" });
            _myColombianServicesCategories.Add(new Category() { ID = 188, SearchDescription = "¡Lo ayudaremos a planear su boda! ¡Visite nuestro sitio y nuestros testimonios!", SearchHeadline2 = "Programe una consulta gratis", SearchHeadline1 = "Increíble organizador de bodas", Name = "Organización de bodas", CallDescription1 = "Permítanos ayudarle a planear su boda.", CallDescription2 = "¡Llame para reservar su cita hoy!", CallTitle = "Coordinadora de bodas" });
            _myColombianServicesCategories.Add(new Category() { ID = 189, SearchDescription = "Visite nuestro sitio ahora para ver nuestros servicios y testimonios", SearchHeadline2 = "Obtenga una cotización hoy", SearchHeadline1 = "Buen servicio y buenos precios", Name = "Otro", CallDescription1 = "Increíble servicio y excelentes tarifas", CallDescription2 = "Mencione este anuncio, ahorre un 10 %.", CallTitle = "Mi Marca LTD." });
            _myColombianServicesCategories.Add(new Category() { ID = 190, SearchDescription = "Consulta gratis, horarios flexibles, horarios nocturnos, precios competitivos.", SearchHeadline2 = "Reserve una cita hoy", SearchHeadline1 = "Abogado con experiencia", Name = "Abogado", CallDescription1 = "Servicio y experiencia inigualables", CallDescription2 = "¡Llame para hacer una cita hoy!", CallTitle = "Abogado con experiencia" });
            _myColombianServicesCategories.Add(new Category() { ID = 186, SearchDescription = "Servicio rápido y confiable. ¡Años de experiencia! Precios accesibles.", SearchHeadline2 = "Estamos aquí para ayudar", SearchHeadline1 = "Buena asistencia en carreteras", Name = "Auxilio en carretera", CallDescription1 = "Estamos aquí para ayudar", CallDescription2 = "¡Llámenos ahora!", CallTitle = "Asistencia de carretera" });
            _myColombianServicesCategories.Add(new Category() { ID = 136, SearchDescription = "Personal profesional y amable, visítenos!", SearchHeadline2 = "Haga un cita hoy", SearchHeadline1 = "Peluquería de alta calidad", Name = "Salud y belleza -Peluquería", CallDescription1 = "Expertos en cuidado capilar", CallDescription2 = "¡Llame ahora para reservar una visita!", CallTitle = "Peluquería" });
            _myColombianServicesCategories.Add(new Category() { ID = 158, SearchDescription = "Personal profesional y amable, visítenos!", SearchHeadline2 = "Haga un cita hoy", SearchHeadline1 = "Manicure y Pedicure", Name = "Salud y belleza- Manicure y Pedicure", CallDescription1 = "Cuidado estético de manos y pies", CallDescription2 = "¡Llame ahora para reservar una visita!", CallTitle = "Manicure y Pedicure" });
            _myColombianServicesCategories.Add(new Category() { ID = 114, SearchDescription = "Entrenador con mucha experiencia, experto en fitness. Precios accesibles.", SearchHeadline2 = "Reserve una clase hoy", SearchHeadline1 = "El mejor entrenador", Name = "Salud y belleza- Entrenadores", CallDescription1 = "Entrenador personal con amplia experiencia en fitness", CallDescription2 = "¡Llame ahora para reservar una visita!", CallTitle = "Entrenadores" });
            _myColombianServicesCategories.Add(new Category() { ID = 164, SearchDescription = "Cuidado estético profesional para señores. Masaje, manicure y más. Visítenos.", SearchHeadline2 = "Haga un cita hoy", SearchHeadline1 = "Belleza Hombres", Name = "Salud y belleza- Hombres", CallDescription1 = "Cuidado estético para hombres, masaje, manicure, etc.", CallDescription2 = "¡Llame ahora para reservar una visita!", CallTitle = "Belleza Hombres" });
            _myColombianServicesCategories.Add(new Category() { ID = 180, SearchDescription = "Ultimas tendencias en arreglo de Novias. Visite nuestra web.", SearchHeadline2 = "Ultimas tendencias en arreglo de novias", SearchHeadline1 = "Novias", Name = "Salud y belleza- Novias", CallDescription1 = "Ultimas tendencias en arreglo de novias", CallDescription2 = "¡Llame ahora para reservar una visita!", CallTitle = "Novias" });
            _myColombianServicesCategories.Add(new Category() { ID = 187, SearchDescription = "La mejor calidad de vida para mascotas y sus amos. Llame ya!", SearchHeadline2 = "Consienta su mascota", SearchHeadline1 = "Baño y Peluquería", Name = "Mascotas y animales", CallDescription1 = "La mejor calidad de vida para mascotas y sus amos", CallDescription2 = "¡Llámenos ahora!", CallTitle = "Baño y Peluquería" });
        }

        private static void FillMyServicesCategories()
        {
            _myServicesCategories = new List<Category>();
            _myServicesCategories.Add(new Category() { ID = 0, Name = "Choose industry" });
            _myServicesCategories.Add(new Category() { ID = 100, Name = "Acupuncture", CallTitle = "Acupuncture Services", CallDescription1 = "Gentle & Effective Acupuncturist.", CallDescription2 = "Call To Relieve Pain Naturally!", SearchHeadline1 = "Amazing Acupuncturist", SearchHeadline2 = "Relieves Pain Naturally", SearchDescription = "Check Out Our Site Now to See Our Awesome Clinc and Great Testimonials!" });
            _myServicesCategories.Add(new Category() { ID = 101, Name = "Advertising & Marketing", CallTitle = "Advertising Agency", CallDescription1 = "Let Us Advertise For You.", CallDescription2 = "Free Consultation Available Today!", SearchHeadline1 = "Fast Turn Around", SearchHeadline2 = "Free Consultations Available", SearchDescription = "Professional marketing agency with years of experience in the industry. " });
            _myServicesCategories.Add(new Category() { ID = 102, Name = "Automotive & Cars", CallTitle = "Auto Parts Services", CallDescription1 = "Up to 30% Off All Auto Parts.", CallDescription2 = "Parts are In-Stock, Ready to Ship!", SearchHeadline1 = "Amazing Deals On Cars", SearchHeadline2 = "Up to 30% Off Retail Prices", SearchDescription = "Large variety, fast delivery, 100% satisfaction guaranteed. " });
            _myServicesCategories.Add(new Category() { ID = 103, Name = "Bankruptcy Lawyer", CallTitle = "Bankruptcy Lawyer", CallDescription1 = "We Specialize In Bankruptcy Law. ", CallDescription2 = "Call to Make an Appt. Today!", SearchHeadline1 = "Experienced Bankruptcy Lawyer", SearchHeadline2 = "Make An Appointment Today", SearchDescription = "Free Consultation, Appointment Flexibility, Evening Hours, Competitive Pricing. " });

            _myServicesCategories.Add(new Category() { ID = 104, Name = "Boat Rental", CallTitle = "Boat Rental Service", CallDescription1 = "Up to 30% Off Your Boat Rental.", CallDescription2 = "Order a Boat For This Weekend!", SearchHeadline1 = "Boat Rental", SearchHeadline2 = "Book One Today", SearchDescription = "Spacious Decks And Unmatched Atmosphere, It's The Perfect Venue For Groups." });

            _myServicesCategories.Add(new Category() { ID = 105, Name = "Car Detailing", CallTitle = "Car Detailing", CallDescription1 = "25 yrs Experience In Car Detailing.", CallDescription2 = "Make Your Appt. Today!", SearchHeadline1 = "Best Auto Detailing Service", SearchHeadline2 = "Schedule Your Appointment ", SearchDescription = "We are experience professionals who treat your car to our quality care! " });
            _myServicesCategories.Add(new Category() { ID = 106, Name = "Car Engine Services", CallTitle = "Car Engine Services", CallDescription1 = "35 yrs In The Auto Industry. ", CallDescription2 = "Call For Your Free Estimate Today!", SearchHeadline1 = "Car Engine Services", SearchHeadline2 = "Request Your Free Estimate", SearchDescription = "Experienced Professionals. Affordable & Reliable. 5-Star Service Guaranteed!" });
            _myServicesCategories.Add(new Category() { ID = 107, Name = "Car Rental", CallTitle = "Car Rental Service", CallDescription1 = "Up to 30% Off Your Car Rental.", CallDescription2 = "Call To Rent a Car Today!", SearchHeadline1 = "Car Rental ", SearchHeadline2 = "Up to 30% Off Your Next Rental", SearchDescription = "Lowest Prices Guaranteed. 24/7 Support. Large Variety of Cars Available!" });
            _myServicesCategories.Add(new Category() { ID = 108, Name = "Car Upholstery", CallTitle = "Car Upholstery Service", CallDescription1 = "25 yrs Experience In Car Upholstery", CallDescription2 = "Call To Book Your Appt. Today!", SearchHeadline1 = "Best Auto Upholstery Shop", SearchHeadline2 = "Book Your Appointment", SearchDescription = "Free Consultation, Appointment Flexibility, Evening Hours, Competitive Pricing. " });
            _myServicesCategories.Add(new Category() { ID = 109, Name = "Car Wash", CallTitle = "Full Service Car Wash", CallDescription1 = "Quick & Full Service Car Wash. ", CallDescription2 = "Make Your Appt. Today!", SearchHeadline1 = "Amazing Car Wash ", SearchHeadline2 = "Lowest Prices Guaranteed", SearchDescription = "Full Service & Self Service Available. Open 7 Days A Week For Your Convenience!" });
            _myServicesCategories.Add(new Category() { ID = 110, Name = "Car Window Tinting", CallTitle = "Car Window Tinting", CallDescription1 = "Your One Stop Auto Shop.", CallDescription2 = "Book Your Appt. Today!", SearchHeadline1 = "Your One Stop Auto Shop", SearchHeadline2 = "Book Your Appointment Today", SearchDescription = "Free Consultation, Appointment Flexibility, Evening Hours, Competitive Pricing. " });
            _myServicesCategories.Add(new Category() { ID = 111, Name = "Carpet Cleaning", CallTitle = "Carpet Cleaning Service", CallDescription1 = "25 yrs In Carpet Cleaning Industry.", CallDescription2 = "Call Us Now To Learn More!", SearchHeadline1 = "Amazing Carpet Clearner", SearchHeadline2 = "Book Online Today", SearchDescription = "Family Owned & Operated. Free Consulations & Quotes Available.  " });
            _myServicesCategories.Add(new Category() { ID = 112, Name = "Children Learning Center", CallTitle = "Children Learning Center", CallDescription1 = "Providing Quality Education.", CallDescription2 = "Call Today To Book Your Visit!", SearchHeadline1 = "Children Learning Center", SearchHeadline2 = "Helping Kids Thrive & Learn", SearchDescription = "Enrollment is Open For All Ages. Highly Trained Teachers. Book Your Tour!" });

            _myServicesCategories.Add(new Category() { ID = 113, Name = "Computer Repair", CallTitle = "Computer Repair Service", CallDescription1 = "Same Day Computer Repair. ", CallDescription2 = "Book Your Appt. Today!", SearchHeadline1 = "Amazing Computer Repair", SearchHeadline2 = "Get Your Free Quote", SearchDescription = "If We Can't Fix It, You Will Not Be Charged!" });

            _myServicesCategories.Add(new Category() { ID = 114, Name = "Consulting & Coaching", CallTitle = "Consulting & Coaching", CallDescription1 = "Top Rated Consultants & Coaches. ", CallDescription2 = "Book Your Appt. Now!", SearchHeadline1 = "Experienced Consultant & Coach", SearchHeadline2 = "Book Online Today", SearchDescription = "Experienced Professionals. Affordable & Reliable! " });
            _myServicesCategories.Add(new Category() { ID = 115, Name = "Dance School", CallTitle = "Dance School", CallDescription1 = "Teaching Dance Since 2000.", CallDescription2 = "Book Your First Dance Class Today!", SearchHeadline1 = "Dance School", SearchHeadline2 = "Book Your First Class Today", SearchDescription = "Experienced Instructors. Affordable Pricing. Classes Available For All Levels!" });

            _myServicesCategories.Add(new Category() { ID = 116, Name = "Dentist", CallTitle = "General Dentist Services", CallDescription1 = "High-quality Dental Care.", CallDescription2 = "Call To See A Dentist Today!", SearchHeadline1 = "High-quality Dental Care", SearchHeadline2 = "Book Your Appointment Today", SearchDescription = "Get The Smile You Have Dreamed! New Patients Welcome. Affordable Prices. " });
            _myServicesCategories.Add(new Category() { ID = 117, Name = "Designer - Website", CallTitle = "Website Designer", CallDescription1 = "10 yrs Of Experience In Web Design.", CallDescription2 = "Call Now For A Free Quote!", SearchHeadline1 = "Amazing Website Designer", SearchHeadline2 = "Get Your Free Quote", SearchDescription = "Tailor-made Website Design. Affordable Rates. 100% Satisfaction Guarateed!" });
            _myServicesCategories.Add(new Category() { ID = 118, Name = "Designer - Interior/Home", CallTitle = "Interior Designer", CallDescription1 = "Let Us Design Your Home.", CallDescription2 = "Call Now For A Free Quote!", SearchHeadline1 = "Amazing Interior Designer", SearchHeadline2 = "Get Your Free Quote", SearchDescription = "Turn Your House Into Your Dream Home! Free Consultations Available. " });
            _myServicesCategories.Add(new Category() { ID = 119, Name = "Disc Jockey", CallTitle = "Disc Jockey", CallDescription1 = "Custom DJ Packages Available.", CallDescription2 = "Call For A Quote Today!", SearchHeadline1 = "Amazing Disc Jockey", SearchHeadline2 = "Get Your Free Consultation", SearchDescription = "Fun, Outgoing DJ's Available For Your Next Event! Custom Packages Available." });
            _myServicesCategories.Add(new Category() { ID = 120, Name = "Divorce Lawyer", CallTitle = "Divorce Lawyer", CallDescription1 = "Experienced Family Divorce Lawyers.", CallDescription2 = "Call Us Now For A Consultation!", SearchHeadline1 = "Divorce Lawyer", SearchHeadline2 = "Book Your Appointment Today", SearchDescription = "Free Consultation, Appointment Flexibility, Evening Hours, Competitive Pricing. " });
            _myServicesCategories.Add(new Category() { ID = 121, Name = "Drug Addiction Counseling", CallTitle = "Drug Addiction Couneling", CallDescription1 = "Drug Addition Is Treatable.", CallDescription2 = "To Learn More, Call Us.", SearchHeadline1 = "Drug Addiction Counseling", SearchHeadline2 = "Begin Your Recovery Today", SearchDescription = "Experienced Staff. 24 Hour Assistance. Personalized Treatment." });
            _myServicesCategories.Add(new Category() { ID = 122, Name = "Dry Cleaning", CallTitle = "Dry Cleaning Service", CallDescription1 = "24-Hour Dry Cleaning Service.", CallDescription2 = "Call Us For Pick Up Now!", SearchHeadline1 = "Amazing Dry Cleaner", SearchHeadline2 = "Schedule Your Pick Up Today", SearchDescription = "Top Quality Dry Cleaner. Tailoring & Alterations Services Available. " });
            _myServicesCategories.Add(new Category() { ID = 123, Name = "Electrician", CallTitle = "Electrician", CallDescription1 = "Experience Electricians. ", CallDescription2 = "Call Us Now For Immediate Service!", SearchHeadline1 = "Amazing Electrician", SearchHeadline2 = "Get Your Free Estimate", SearchDescription = "Fully Licensed & Insured. Affordable Rates. 5-Star Service Guaranteed!" });
            _myServicesCategories.Add(new Category() { ID = 124, Name = "Event Planning", CallTitle = "Event Planner", CallDescription1 = "Let Us Plan Your Next Event!", CallDescription2 = "Call For Your Free Consultation. ", SearchHeadline1 = "Amazing Event Planner", SearchHeadline2 = "Get Your Free Consultation", SearchDescription = "Experienced Staff & Affordable Prices. Let Us Plan Your Next Event! " });
            _myServicesCategories.Add(new Category() { ID = 125, Name = "Family Counseling", CallTitle = "Family Counseling", CallDescription1 = "Experienced Family Counselors. ", CallDescription2 = "Call To Book Your Appt. Today!", SearchHeadline1 = "Experienced Family Counselor", SearchHeadline2 = "Book Your Appointment", SearchDescription = "Private, Convenient, & Affordable Family Counselor. Free Consultations!" });
            _myServicesCategories.Add(new Category() { ID = 126, Name = "Fashion & Accessories", CallTitle = "Let Us Take Care of You", CallDescription1 = "We Can Tailor Anything to Any Event", CallDescription2 = "Call Now to Get a Quote!", SearchHeadline1 = "Let Us Take Care of You", SearchHeadline2 = "Get a Quote Today", SearchDescription = "Checl Out Our Amazing Designs. We Can Tailor Anything to Any Event!" });
            _myServicesCategories.Add(new Category() { ID = 127, Name = "Financial Advisor", CallTitle = "Financial Advisor", CallDescription1 = "Let's Plan Your Financial Future.", CallDescription2 = "Talk To An Advisor Today!", SearchHeadline1 = "Experienced Financial Advisor", SearchHeadline2 = "Book Your Free Consultation", SearchDescription = "Appointment Flexibility, Evening Hours, & Competitive Pricing. " });
            _myServicesCategories.Add(new Category() { ID = 128, Name = "Fire Damage Restoration", CallTitle = "Fire Damage Restoration", CallDescription1 = "Fire Damage Restoration Specialist.", CallDescription2 = "Call Us Today To Learn More!", SearchHeadline1 = "Fire Damage Restoration", SearchHeadline2 = "Get Your Free Quote", SearchDescription = "Experienced & Trained Technicians. Fast, Friendly, & Thorough Service!" });
            _myServicesCategories.Add(new Category() { ID = 129, Name = "Flooring Installation", CallTitle = "Flooring Installation ", CallDescription1 = "Quality Flooring & Installation.", CallDescription2 = "Call For Your Free Estimation!", SearchHeadline1 = "Flooring Installation ", SearchHeadline2 = "Get Your Free Quote", SearchDescription = "Over 30 Years of Experience. Family Owned & Opperated. Affordable Pricing. " });
            _myServicesCategories.Add(new Category() { ID = 130, Name = "Florist", CallTitle = "Florist", CallDescription1 = "Same Day Flower Delivery.", CallDescription2 = "Order Flowers Today!", SearchHeadline1 = "Amazing Florist", SearchHeadline2 = "Order Flowers Today", SearchDescription = "Same Day Delivery Available. Large Variety & Fast Delivery!" });

            _myServicesCategories.Add(new Category() { ID = 131, Name = "Funeral Services", CallTitle = "Funeral Services", CallDescription1 = "Family Owned & Operated", CallDescription2 = "Call Us Today, We Can Help.", SearchHeadline1 = "Funeral Service", SearchHeadline2 = "Learn More", SearchDescription = "Professional Funeral Services At An Affordable Price. " });

            _myServicesCategories.Add(new Category() { ID = 132, Name = "Furniture Upholstery", CallTitle = "Furniture Upholstery", CallDescription1 = "25 yrs Experience In Upholstery.", CallDescription2 = "Get Your Free Estimate Today!", SearchHeadline1 = "Best Furniture Upholstery Shop", SearchHeadline2 = "Get Your Free Estimate Today", SearchDescription = "Hand-Tailored To Fit Your Style & Tastes! Over 30 Years of Experience." });
            _myServicesCategories.Add(new Category() { ID = 133, Name = "Garage Doors Repair", CallTitle = "Garage Doors Repair", CallDescription1 = "Licensed Garage Door Installation.", CallDescription2 = "Book A Same-Day Appointment!", SearchHeadline1 = "Garage Door Repair", SearchHeadline2 = "Book A Same-Day Appointment", SearchDescription = "Licensed & Insured Professionals. Affordable Prices & Appointment Flexibility. " });
            _myServicesCategories.Add(new Category() { ID = 134, Name = "General Contractor", CallTitle = "General Contractor", CallDescription1 = "20 yrs Experience As A Contractor.", CallDescription2 = "Free Consult, Call Us Today!", SearchHeadline1 = "General Contractor", SearchHeadline2 = "Book Your Free Consultation", SearchDescription = "High Quality Service Since 1984. Fully Licensed & Insured. " });
            _myServicesCategories.Add(new Category() { ID = 135, Name = "General Practitioner", CallTitle = "General Practitioner", CallDescription1 = "Get More Time With Your Doctor.", CallDescription2 = "Book A Same-Day Appointment!", SearchHeadline1 = "General Practitioner", SearchHeadline2 = "Book A Same-Day Appointment", SearchDescription = "Evening & Weekend Appointments Available. New Patients Welcome!" });
            _myServicesCategories.Add(new Category() { ID = 136, Name = "Hair Salon", CallTitle = "Hair Salon", CallDescription1 = "Let Us Make Your Hair Look Great.", CallDescription2 = "Give Us A Call To Schedule An Appt!", SearchHeadline1 = "Hair Salon ", SearchHeadline2 = "Schedule an Appointment", SearchDescription = "Providing Excellent Service For Over 30 years. Let Us Make You Look Your Best!" });
            _myServicesCategories.Add(new Category() { ID = 137, Name = "Handyman", CallTitle = "Handyman", CallDescription1 = "25 yrs Experience As A Handyman.", CallDescription2 = "Call To Book Your Appt. Today.", SearchHeadline1 = "Amazing Handman", SearchHeadline2 = "Get Your Free Quote", SearchDescription = "Get The Job Done Right! Affordable Prices & Same Day Service Available. " });

            _myServicesCategories.Add(new Category() { ID = 138, Name = "Health & Beauty", CallTitle = "Let Us Take Care of You", CallDescription1 = "Licensed & Experience", CallDescription2 = "Call Now to Get a Quote!", SearchHeadline1 = "Let Us Take Care of You", SearchHeadline2 = "Get a Quote Today", SearchDescription = "Check Out Our Site Now to See Our Amazing Facilities and Great Testimonials!" });

            _myServicesCategories.Add(new Category() { ID = 139, Name = "Health Insurance", CallTitle = "Health Insurance Company", CallDescription1 = "Providing Insurance Since 2000.", CallDescription2 = "Call Today For Your Quote!", SearchHeadline1 = "Health Insurance Company", SearchHeadline2 = "Get Started Today", SearchDescription = "Top Quality Coverage. 2-Minute Quick Quote. Find the Plan Right For You!" });
            _myServicesCategories.Add(new Category() { ID = 140, Name = "Home & Garden", CallTitle = "Home & Garden", CallDescription1 = "Whatever You Need, We Have It.", CallDescription2 = "Call Now to Get a Quote!", SearchHeadline1 = "Let Us Take Care of You", SearchHeadline2 = "Get a Quote Today", SearchDescription = "Whatever You Need for Your House or Garden, We can Provide It!" });
            _myServicesCategories.Add(new Category() { ID = 141, Name = "Hotel", CallTitle = "Hotel Booking", CallDescription1 = "Amazing Hotel Deals, Up to 80% Off.", CallDescription2 = "Call & Book a Room Now!", SearchHeadline1 = "Amazing Hotel Deals", SearchHeadline2 = "Save Up to 80% Off", SearchDescription = "24/7 Customer Service. Fast, Secure, No Cancellation Fee. Book Your Room Today!" });
            _myServicesCategories.Add(new Category() { ID = 142, Name = "House Cleaning", CallTitle = "House Cleaning Service", CallDescription1 = "9 Yrs Experience In House Cleaning.", CallDescription2 = "Book Your Cleaner Today!", SearchHeadline1 = "House Cleaner", SearchHeadline2 = "Book Online Today", SearchDescription = "Over 30 Years of Experience. Family Owned & Opperated. Affordable Pricing. " });
            _myServicesCategories.Add(new Category() { ID = 143, Name = "HVAC", CallTitle = "HVAC Contractors", CallDescription1 = "Licensed HVAC Contractors.", CallDescription2 = "Call Us Now!", SearchHeadline1 = "HVAC", SearchHeadline2 = "Get a Free Quote Today", SearchDescription = "Licensed & Insured. Services include repair, installation, & maintenance." });
            _myServicesCategories.Add(new Category() { ID = 144, Name = "Interior Designer", CallTitle = "Interior Designer", CallDescription1 = "Let Us Design Your Home.", CallDescription2 = "Call For Your Free Quote Today!", SearchHeadline1 = "Amazing Interior Designer", SearchHeadline2 = "Get a Free Quote Today", SearchDescription = "Turn Your House Into Your Dream Home! Free Consultations Available. " });
            _myServicesCategories.Add(new Category() { ID = 145, Name = "Janitorial", CallTitle = "Janitorial Cleaning", CallDescription1 = "Experienced Janitors.", CallDescription2 = "Call For Your Free Quote Today!", SearchHeadline1 = "Janitorial Services", SearchHeadline2 = "Request Your Free Estimate", SearchDescription = "Trusted Cleaners. 5-Star Service. Next-day availability!" });
            _myServicesCategories.Add(new Category() { ID = 146, Name = "Landscaping", CallTitle = "Landscaping", CallDescription1 = "Up To 30% Off First Tree Tirming.", CallDescription2 = "Call To Book Your Appt. Today.", SearchHeadline1 = "Amazing Landscaper", SearchHeadline2 = "Get Your Free Estimate", SearchDescription = "Beautify Your Outdoor Space With Our Help! Offering a Large Variety of Services." });

            _myServicesCategories.Add(new Category() { ID = 147, Name = "Laser Hair Removal", CallTitle = "Laser Hair Removal", CallDescription1 = "High-Quality Laser Hair Removal", CallDescription2 = "Nights & Weekend Appts. Available.", SearchHeadline1 = "Laser Hair Removal", SearchHeadline2 = "Book Your Appointment Today", SearchDescription = "Over 20 Years of Experience. Night & Weekend Appointments Available. " });
            _myServicesCategories.Add(new Category() { ID = 148, Name = "Lawn Care", CallTitle = "Lawn Care", CallDescription1 = "20+ Yrs Of Experience In Lawn Care.", CallDescription2 = "Call For Your Free Quote Today!", SearchHeadline1 = "Lawn Care Services", SearchHeadline2 = "Get Your Free Quote", SearchDescription = "Expert Service. Affordable Prices. 100% Satisfaction Guaranteed!" });

            _myServicesCategories.Add(new Category() { ID = 190, Name = "Lawyer", CallTitle = "Lawyer", CallDescription1 = "20+ Years Of Experience.", CallDescription2 = "Call to Make an Appt. Today!", SearchHeadline1 = "Experienced Lawyer", SearchHeadline2 = "Make An Appointment Today", SearchDescription = "Free Consultation, Appointment Flexibility, Evening Hours, Competitive Pricing. " });

            _myServicesCategories.Add(new Category() { ID = 149, Name = "Limousine Service", CallTitle = "Limousine Service", CallDescription1 = "Get Around In Style With A Limo.", CallDescription2 = "Order Your Limo Today!", SearchHeadline1 = "Limousine Service", SearchHeadline2 = "Order Your Limo Today", SearchDescription = "Free Online Quote. Special Group Rates. Over 25 years of excellent service." });
            _myServicesCategories.Add(new Category() { ID = 150, Name = "Locksmith", CallTitle = "Locksmith", CallDescription1 = "24/7 Locksmith Service.", CallDescription2 = "Call Now & Get 15% Off!", SearchHeadline1 = "Locksmith", SearchHeadline2 = "24 Hour Local Locksmith", SearchDescription = "Available 24/7 for Emergencies. Quality Service Your Can Trust. Flat Rate Fee." });
            _myServicesCategories.Add(new Category() { ID = 151, Name = "Marriage Counseling", CallTitle = "Marriage Counseling", CallDescription1 = "30 yrs Experience In Counseling.", CallDescription2 = "Book Your Appt. Today!", SearchHeadline1 = "Experienced Marriage Counselor", SearchHeadline2 = "Book Your Appointment", SearchDescription = "Private, Convenient, & Affordable Family Counselor. Free Consultations!" });
            _myServicesCategories.Add(new Category() { ID = 152, Name = "Massage Therapy", CallTitle = "Massage Therapist", CallDescription1 = "Licensed Massage Therapists.", CallDescription2 = "Book Your Appt. Today.", SearchHeadline1 = "Massage Therapist", SearchHeadline2 = "Schedule Your Appointment ", SearchDescription = "Check Out Our Site Now to See Our Awesome Clinc and Great Testimonial!" });
            _myServicesCategories.Add(new Category() { ID = 153, Name = "Math Teacher", CallTitle = "Math Tutor", CallDescription1 = "Affordable 1-On-1 Tutor.", CallDescription2 = "Schedule Your Child's First Class!", SearchHeadline1 = "Amazing Math Tutor", SearchHeadline2 = "Academic Success & Less Stress", SearchDescription = "Custom, Affordable 1-on-1 Tutoring. First Session Satisfaction Guaranteed. " });
            _myServicesCategories.Add(new Category() { ID = 154, Name = "Mortgage Broker", CallTitle = "Mortgage Broker", CallDescription1 = "We Specialize In Mortgages.", CallDescription2 = "Call Us Today!", SearchHeadline1 = "Experienced Mortgage Broker", SearchHeadline2 = "Schedule an Appointment", SearchDescription = "No Application Fees, No PMI Required, Flexible Down Payments, & Fast Financing!" });
            _myServicesCategories.Add(new Category() { ID = 155, Name = "Movers", CallTitle = "Moving Company", CallDescription1 = "Full Service Moving Company.", CallDescription2 = "Call Us For Your Free Estimate!", SearchHeadline1 = "Amazing Movers", SearchHeadline2 = "Get Your Free Estimate", SearchDescription = "Fast & Reliable Service. Over 30 Years of Experience. Affordable Pricing. " });

            _myServicesCategories.Add(new Category() { ID = 156, Name = "Musician", CallTitle = "Musician", CallDescription1 = "Outgoing Musician", CallDescription2 = "Book Us For Your Event!", SearchHeadline1 = "Amazing Musican", SearchHeadline2 = "Check Out Our Availability", SearchDescription = "Fun, Outgoing Musicians Available For Your Next Event! " });

            _myServicesCategories.Add(new Category() { ID = 157, Name = "Music Teacher", CallTitle = "Music Teacher", CallDescription1 = "Private Music Lessons Available.", CallDescription2 = "Book Your First Lesson Today!", SearchHeadline1 = "Amazing Music Teacher", SearchHeadline2 = "Book Your First Lesson Today", SearchDescription = "Experienced & Fun Instructors. All Ages & Skill Levels Welcome!" });

            _myServicesCategories.Add(new Category() { ID = 158, Name = "Nail Salon", CallTitle = "Nail Salon", CallDescription1 = "30% Off New Clients", CallDescription2 = "Call To Book Your Appt. Today.", SearchHeadline1 = "Nail Salon", SearchHeadline2 = "Book Your Appointment", SearchDescription = "Providing Excellent Service For Over 30 years. Let Us Make You Look Your Best!" });

            _myServicesCategories.Add(new Category() { ID = 159, Name = "Neurologist", CallTitle = "Neurologist", CallDescription1 = "Neurologist Personal Care.", CallDescription2 = "Book Your Appt. Today!", SearchHeadline1 = "Neurologist", SearchHeadline2 = "Schedule Your Appointment ", SearchDescription = "Check Out Our Site Now to See Our Awesome Clinc and Great Testimonials!" });
            _myServicesCategories.Add(new Category() { ID = 160, Name = "OBGYN", CallTitle = "OBGYN", CallDescription1 = "OBGYN - Over 25 Years of Experience", CallDescription2 = "Call To Book Your Appt.", SearchHeadline1 = "OBGYN", SearchHeadline2 = "Book Your Appoinment Online", SearchDescription = "Check Out Our Site Now to See Our Awesome Clinc and Great Testimonials!" });
            _myServicesCategories.Add(new Category() { ID = 161, Name = "Optometrist ", CallTitle = "Optometrist ", CallDescription1 = "One Stop Family Eye Care.", CallDescription2 = "Book Your Optometrist Appt. Today!", SearchHeadline1 = "Optometrist ", SearchHeadline2 = "Book Your Appoinment ", SearchDescription = "Check Out Our Site Now to See Our Awesome Clinc and Great Testimonials!" });
            _myServicesCategories.Add(new Category() { ID = 162, Name = "Optician", CallTitle = "Optician", CallDescription1 = "Optician - 30 Years Of Experience.", CallDescription2 = "Schedule An Eye Exam Today!", SearchHeadline1 = "Optician", SearchHeadline2 = "Schedule An Eye Exam Today", SearchDescription = "Check Out Our Site Now to See Our Awesome Clinc and Great Testimonials!" });
            _myServicesCategories.Add(new Category() { ID = 163, Name = "Painter", CallTitle = "Painter", CallDescription1 = "Painter - 15 Years of Experience.", CallDescription2 = "Call For a Quote Today!", SearchHeadline1 = "Painter", SearchHeadline2 = "Get Your Free Estimate", SearchDescription = "Top-Rated Home Painting Company. Insured Painters With 100% Guarantee!" });
            _myServicesCategories.Add(new Category() { ID = 164, Name = "Personal Trainer", CallTitle = "Personal Trainer", CallDescription1 = "Experienced Personal Trainer.", CallDescription2 = "Book Your Personal Trainer Today!", SearchHeadline1 = "Personal Trainer", SearchHeadline2 = "Get Your Free Consultation", SearchDescription = "Check Out Our Site Now to See Our Awesome Gym and Great Testimonials!" });
            _myServicesCategories.Add(new Category() { ID = 165, Name = "Pest Control", CallTitle = "Pest Control", CallDescription1 = "Pest Control 35 Years Of Experience", CallDescription2 = "Call For Your Free Inspection!", SearchHeadline1 = "Pest Control", SearchHeadline2 = "Get Your Free Inspection", SearchDescription = "Fast & Experienced Pest Elimiation. Affordable Rate. Satisfaction Guaranteed!" });
            _myServicesCategories.Add(new Category() { ID = 166, Name = "Pets & Animals", CallTitle = "The Best for Your Pet", CallDescription1 = "Amazing Facilities, Great Reviews", CallDescription2 = "Call to Get an Appointment Today!", SearchHeadline1 = "Let Us Take Care of Your Pet", SearchHeadline2 = "Get an Appointment Today", SearchDescription = "Visit Our Site to Check Out on Our Amazing Facilities and Great Reviews" });
            _myServicesCategories.Add(new Category() { ID = 167, Name = "Photographer", CallTitle = "Photographer", CallDescription1 = "Photographer: Studio or On Location", CallDescription2 = "Call & Schedule Appt. Today!", SearchHeadline1 = "Photographer", SearchHeadline2 = "Schedule Your Appointment ", SearchDescription = "25 Years Experience In Studio or On Location. Affordable Rates. " });
            _myServicesCategories.Add(new Category() { ID = 168, Name = "Physical Therapy", CallTitle = "Physical Therapy", CallDescription1 = "Licensed Physical Therapists.", CallDescription2 = "Same Day Appt. Available, Call Now.", SearchHeadline1 = "Physical Therapist", SearchHeadline2 = "Schedule Your Appointment ", SearchDescription = "Check Out Our Site Now to See Our Awesome Clinc and Great Testimonial!" });
            _myServicesCategories.Add(new Category() { ID = 169, Name = "Plastic Surgeon", CallTitle = "Plastic Surgeon", CallDescription1 = "US Board Certified Surgeon.", CallDescription2 = "Call For Your Free Consultation!", SearchHeadline1 = "Plastic Surgeon", SearchHeadline2 = "Schedule Your Consultation", SearchDescription = "Check Out Our Site Now to See Our Awesome Clinc and Great Testimonial!" });
            _myServicesCategories.Add(new Category() { ID = 170, Name = "Plumber", CallTitle = "Plumber", CallDescription1 = "15 Yrs Experience As A Plumber.", CallDescription2 = "Same Day Appt. Available, Call Now.", SearchHeadline1 = "Plumber", SearchHeadline2 = "Schedule a Free Estimate Now", SearchDescription = "Professional & Fast Service. Affordable Rates. We Can Handle All Plumbing Needs!" });
            _myServicesCategories.Add(new Category() { ID = 171, Name = "Private Caregiver", CallTitle = "Private Caregiver", CallDescription1 = "Personal & Professional Caregivers.", CallDescription2 = "Call Today To Learn More!", SearchHeadline1 = "Private Caregiver", SearchHeadline2 = "Get a Free Consultation", SearchDescription = "Experienced & Reliable Private Caregivers Available to Help You!" });
            _myServicesCategories.Add(new Category() { ID = 172, Name = "Private Investigator", CallTitle = "Private Investigator", CallDescription1 = "30+ Years In Private Investigations", CallDescription2 = "Call For A Free Quote!", SearchHeadline1 = "Private Investigator", SearchHeadline2 = "Low Hourly Rates", SearchDescription = "Handling Domestic, Criminal & Civil Investigations. Over 30 Years of Experience!" });

            _myServicesCategories.Add(new Category() { ID = 191, Name = "Promotional Products", SearchDescription = "Leading promotional product suppliers in your area", SearchHeadline1 = "Promotional Products and Gifts", SearchHeadline2 = "Customize with your logo", CallTitle = "Business Name: Promotional Products", CallDescription1 = "Gifts under $10, Free Samples", CallDescription2 = "On-time shipping guaranteed" });


            _myServicesCategories.Add(new Category() { ID = 173, Name = "Psychic", CallTitle = "Psychic", CallDescription1 = "Psychic -10% Off First Reading.", CallDescription2 = "Call To Book Your Reading Today!", SearchHeadline1 = "Psychic", SearchHeadline2 = "Book Your Reading Today", SearchDescription = "Find Now True Answers to What is Going on in Your Life! " });
            _myServicesCategories.Add(new Category() { ID = 174, Name = "Psychologist", CallTitle = "Psychologist", CallDescription1 = "Licensed Psychologists.", CallDescription2 = "Call For Your Free Consultation!", SearchHeadline1 = "Psychologist", SearchHeadline2 = "Schedule Your Consultation", SearchDescription = "Get Help From a Liscenced Psychologist. Over 30 Years of Experience!" });
            _myServicesCategories.Add(new Category() { ID = 175, Name = "Real Estate Agent", CallTitle = "Real Estate Agent", CallDescription1 = "Licensed Real Estate Agents.", CallDescription2 = "Call Today To Learn More!", SearchHeadline1 = "Real Estate Agent", SearchHeadline2 = "Find Your Dream Home", SearchDescription = "Check Out Our Site Now to See Houses For Sale and Great Testimonials!" });
            _myServicesCategories.Add(new Category() { ID = 176, Name = "Restaurant", CallTitle = "Restaurant", CallDescription1 = "Dine With Us At Our Restaurant.", CallDescription2 = "Book Your Reservation For Tonight!", SearchHeadline1 = "Resturant", SearchHeadline2 = "Book Your Reservation ", SearchDescription = "Check Out Our Site Now to See Our Menu!" });
            _myServicesCategories.Add(new Category() { ID = 177, Name = "Roofing", CallTitle = "Roofing Service", CallDescription1 = "Free Roof Inspection.", CallDescription2 = "Call Our Team Now!", SearchHeadline1 = "Roofing Contractor", SearchHeadline2 = "Get Your Free Estimate", SearchDescription = "Over 25 Years of Roofing Experience. Fast, Reliable, & Affordable Service." });
            _myServicesCategories.Add(new Category() { ID = 178, Name = "Security System", CallTitle = "Security System", CallDescription1 = "24/7 Security System Protection.", CallDescription2 = "Call Now To Learn More!", SearchHeadline1 = "Security System", SearchHeadline2 = "24/7 Protection", SearchDescription = "Trusted for 25 Years. No Long-Term Contracts. Learn More!" });
            _myServicesCategories.Add(new Category() { ID = 179, Name = "Self Storage", CallTitle = "Self Storage", CallDescription1 = "Climate Controled Storage Units. ", CallDescription2 = "Call Us For Monthly Rates!", SearchHeadline1 = "Self Storage", SearchHeadline2 = "Reserve Now Online", SearchDescription = "Climate Controled Storage Units. No Contracts. Safe & Secure." });
            _myServicesCategories.Add(new Category() { ID = 180, Name = "Spa", CallTitle = "Spa Services", CallDescription1 = "Treat Yourself At Our Spa.", CallDescription2 = "Make An Appt. Today!", SearchHeadline1 = "Amazing Spa", SearchHeadline2 = "Book Online Today", SearchDescription = "Visit Our Site Now to Check Out Our Great Services & Testimonials!" });

            _myServicesCategories.Add(new Category() { ID = 181, Name = "Sports", CallTitle = "Let Us Help You Train", CallDescription1 = "The Best Trainers in The Area.", CallDescription2 = "Call to Schedule a Workout Today!", SearchHeadline1 = "Let Us Take Care of You", SearchHeadline2 = "Book a Trainer Online Today", SearchDescription = "Check Out Our Site Now to See Our Services & Great Testimonials!" });

            _myServicesCategories.Add(new Category() { ID = 182, Name = "Swimming Pool ", CallTitle = "Swimming Pool ", CallDescription1 = "Pool Repair & Remodeling.", CallDescription2 = "Call For Your Free Consultation!", SearchHeadline1 = "Swimming Pool Service ", SearchHeadline2 = "Get An Instant Service Quote", SearchDescription = "Commercial & Residential Swimming Pool Service & Maintenance & Repair" });
            _myServicesCategories.Add(new Category() { ID = 183, Name = "Tanning", CallTitle = "Tanning Salon", CallDescription1 = "Get Up To 30% Off Your First Tan.", CallDescription2 = "Call To Book Your Appt.!", SearchHeadline1 = "Tanning Salon", SearchHeadline2 = "Schedule Your Appointment ", SearchDescription = "Check Out Our Site Now to See Our Promotions and Great Testimonials!" });
            _myServicesCategories.Add(new Category() { ID = 184, Name = "Taxi", CallTitle = "Taxi Company", CallDescription1 = "24-Hour Taxi Company.", CallDescription2 = "Call Now!", SearchHeadline1 = "Taxi Service", SearchHeadline2 = "Book Online Now", SearchDescription = "Offering 30 Years of Reliable 24/7 Service. " });
            _myServicesCategories.Add(new Category() { ID = 185, Name = "Towing Services", CallTitle = "Towing Services", CallDescription1 = "24-Hour Towing Service.", CallDescription2 = "Call Now!", SearchHeadline1 = "Towing Services", SearchHeadline2 = "Get a Free Quote", SearchDescription = "Family-Owned Business With Over 50 Years Of Experience. Available 24/7!" });
            _myServicesCategories.Add(new Category() { ID = 186, Name = "Travel", CallTitle = "Travel Agency", CallDescription1 = "Travel Agent: 20 yrs Of Experience.", CallDescription2 = "Call for Great Deals on Flights!", SearchHeadline1 = "Let Us Plan Your Next Trip", SearchHeadline2 = "Book Online Today", SearchDescription = "Amazing Deals for Flights, Hotels and Car Rentals!" });
            _myServicesCategories.Add(new Category() { ID = 187, Name = "Veterinarian", CallTitle = "Veterinarian", CallDescription1 = "Board Certified Veterinarians.", CallDescription2 = "Call to Book Your Appt. Today!", SearchHeadline1 = "Veterinarian", SearchHeadline2 = "Schedule Your Visit", SearchDescription = "Compassionate, Skilled, and Cost-Conscious Veterinary Care." });
            _myServicesCategories.Add(new Category() { ID = 188, Name = "Wedding Planning", CallTitle = "Wedding Planner ", CallDescription1 = "Let Us Help You Plan Your Wedding.", CallDescription2 = "Call to Book Your Appt. Today!", SearchHeadline1 = "Wedding Planner", SearchHeadline2 = "Schedule A Free Consultation", SearchDescription = "Let Us Help You Plan Your Wedding! Visit Our Site to See Our Great Testimonials!" });
            //_myServicesCategories.Add(new Category() { ID = 189, Name = "Other", CallTitle = "", CallDescription1 = "Mention This Ad, Save 10%.", CallDescription2 = "", SearchHeadline1 = "Amazing Service Great Rates", SearchHeadline2 = "Get a Quote Today", SearchDescription = "Visit Our Site Now to Check Out Our Services and Testimonials" });
            _myServicesCategories.Add(new Category() { ID = 189, Name = "Other", CallTitle = "My Brand LTD.", CallDescription1 = "Mention This Ad, Save 10%.", CallDescription2 = "Mention This Ad, Save 10%.", SearchHeadline1 = "Amazing Service Great Rates", SearchHeadline2 = "Get a Quote Today", SearchDescription = "Visit Our Site Now to Check Out Our Services and Testimonials" });

         

        }

        private static void FillOnlineStoreCategories()
        {
            _onlineStoreCategories = new List<Category>();
            _onlineStoreCategories.Add(new Category() { ID = 0, Name = "Choose industry" });
            _onlineStoreCategories.Add(new Category() { ID = 501, Name = "Books" });
            _onlineStoreCategories.Add(new Category() { ID = 500, Name = "Cars, Parts & Accessories" });
            _onlineStoreCategories.Add(new Category() { ID = 515, Name = "Cosmetics" });

            _onlineStoreCategories.Add(new Category() { ID = 521, Name = "Beauty - Cosmetics" });
            _onlineStoreCategories.Add(new Category() { ID = 522, Name = "Skin care - Cosmetics" });

            _onlineStoreCategories.Add(new Category() { ID = 502, Name = "Electronics & Computers" });
            _onlineStoreCategories.Add(new Category() { ID = 503, Name = "Fashion & Accessories" });

            _onlineStoreCategories.Add(new Category() { ID = 516, Name = "Fashion - Men's" });
            _onlineStoreCategories.Add(new Category() { ID = 517, Name = "Fashion - Women's" });
            _onlineStoreCategories.Add(new Category() { ID = 518, Name = "Fashion - Teen" });
            _onlineStoreCategories.Add(new Category() { ID = 519, Name = "Fashion - Kids" });
            _onlineStoreCategories.Add(new Category() { ID = 520, Name = "Fashion - Babies" });

            _onlineStoreCategories.Add(new Category() { ID = 504, Name = "Flowers" });
            _onlineStoreCategories.Add(new Category() { ID = 505, Name = "Gifts" });
            _onlineStoreCategories.Add(new Category() { ID = 506, Name = "Health & Beauty" });
            _onlineStoreCategories.Add(new Category() { ID = 507, Name = "Home & Garden" });
            _onlineStoreCategories.Add(new Category() { ID = 508, Name = "Jewelry" });
            _onlineStoreCategories.Add(new Category() { ID = 509, Name = "Music" });
            _onlineStoreCategories.Add(new Category() { ID = 510, Name = "Pet Shop" });
            _onlineStoreCategories.Add(new Category() { ID = 511, Name = "Photos" });
            _onlineStoreCategories.Add(new Category() { ID = 512, Name = "Sports & Outdoors" });
            _onlineStoreCategories.Add(new Category() { ID = 513, Name = "Toys, Kids & Babies" });
            _onlineStoreCategories.Add(new Category() { ID = 514, Name = "Other" });
        }

        public static List<Category> GetOnlineStoreCategories()
        {
            return _onlineStoreCategories;
        }

        public static List<Category> GetMyServicesCategories()
        {
            return _myServicesCategories;
        }

        public static List<Category> GetMyServicesColombianCategories()
        {
            return _myColombianServicesCategories;
        }

        public static List<Category> GetCategories()
        {
            return _list;
        }

        public static Category GetCategoryByID(int? id)
        {
            Category category = (from c in _list where c.ID == id select c).FirstOrDefault();
            return category;
        }

        public static Category GetCategory(string name)
        {
            Category category = (from c in _list where c.Name == name select c).FirstOrDefault();
            return category;
        }

        public static Category GetMyServicesCategoryByID(int? id)
        {
            Category category = (from c in _myServicesCategories where c.ID == id select c).FirstOrDefault();
            return category;
        }
        public static Category GetMyColombianServicesCategoryByID(int? id)
        {
            Category category = (from c in _myColombianServicesCategories where c.ID == id select c).FirstOrDefault();
            return category;
        }

        public static Category GetMyServicesCategory(string name)
        {
            Category category = (from c in _myServicesCategories where c.Name == name select c).FirstOrDefault();
            return category;
        }

        public static Category GetOnlineStoreCategoryByID(int? id)
        {
            Category category = (from c in _onlineStoreCategories where c.ID == id select c).FirstOrDefault();
            return category;
        }

        public static Category GetOnlineStoreCategory(string name)
        {
            Category category = (from c in _onlineStoreCategories where c.Name == name select c).FirstOrDefault();
            return category;
        }

        public class Category
        {
            public int? ID { get; set; }
            public string Name { get; set; }
            public string CallDescription1 { get; set; }
            public string CallDescription2 { get; set; }
            public string CallTitle { get; set; }

            public string SearchHeadline1 { get; set; }
            public string SearchHeadline2 { get; set; }
            public string SearchDescription { get; set; }
        }



        public static void FillList()
        {
            _list = new List<Category>();
            _list.Add(new Category() { ID = 0, Name = "Acupuncture", CallDescription1 = "Gentle & Effective Acupuncturist.", CallDescription2 = "Call To Relieve Pain Naturally!", CallTitle = "Acupuncture Services" });
            _list.Add(new Category() { ID = 1, Name = "Advertising & Marketing", CallDescription1 = "Let Us Advertise For You.", CallDescription2 = "Free Consultation Available Today!", CallTitle = "Advertising Agency" });
            _list.Add(new Category() { ID = 2, Name = "Automotive & Cars", CallDescription1 = "Up to 30% Off All Auto Parts.", CallDescription2 = "Parts are In-Stock, Ready to Ship!", CallTitle = "Discount Auto Parts" });
            _list.Add(new Category() { ID = 3, Name = "Bankruptcy Lawyer", CallDescription1 = "We Specialize In Bankruptcy Law.", CallDescription2 = "Call to Make an Appt. Today!", CallTitle = "Bankruptcy Lawyer" });
            _list.Add(new Category() { ID = 4, Name = "Boat Rental", CallDescription1 = "Up to 30% Off Your Boat Rental.", CallDescription2 = "Order a Boat For This Weekend!", CallTitle = "Boat Rental Service" });
            _list.Add(new Category() { ID = 5, Name = "Books", CallDescription1 = "Description 1", CallDescription2 = "Description 2" });
            _list.Add(new Category() { ID = 6, Name = "Blogger", CallDescription1 = "Description 1", CallDescription2 = "Description 2" });
            _list.Add(new Category() { ID = 7, Name = "Car Detailing", CallDescription1 = "25 yrs Experience In Car Detailing.", CallDescription2 = "Make Your Appt. Today!", CallTitle = "Car Detailing" });
            _list.Add(new Category() { ID = 8, Name = "Car Engine Services", CallDescription1 = "35 yrs In The Auto Industry.", CallDescription2 = "Call For Your Free Estimate Today!", CallTitle = "Car Engine Services" });
            _list.Add(new Category() { ID = 9, Name = "Car Rental", CallDescription1 = "Up to 30% Off Your Car Rental.", CallDescription2 = "Call To Rent a Car Today!", CallTitle = "Car Rental Service" });
            _list.Add(new Category() { ID = 10, Name = "Car Upholstery", CallDescription1 = "25 yrs Experience In Car Upholstery", CallDescription2 = "Call To Book Your Appt. Today!", CallTitle = "Car Upholstery Service" });
            _list.Add(new Category() { ID = 11, Name = "Car Wash", CallDescription1 = "Quick & Full Service Car Wash.", CallDescription2 = "Make Your Appt. Today!", CallTitle = "Full Service Car Wash" });
            _list.Add(new Category() { ID = 12, Name = "Car Window Tinting", CallDescription1 = "Your One Stop Auto Shop.", CallDescription2 = "Book Your Appt. Today!", CallTitle = "Car Window Tinting" });
            _list.Add(new Category() { ID = 13, Name = "Carpet Cleaning", CallDescription1 = "25 yrs In Carpet Cleaning Industry.", CallDescription2 = "Call Us Now To Learn More!", CallTitle = "Carpet Cleaning Service" });
            _list.Add(new Category() { ID = 14, Name = "Children Learning Center", CallDescription1 = "Providing Quality Education.", CallDescription2 = "Call Today To Book Your Visit!", CallTitle = "Children Learning Center" });
            _list.Add(new Category() { ID = 15, Name = "Computer Repair", CallDescription1 = "Same Day Computer Repair.", CallDescription2 = "Book Your Appt. Today!", CallTitle = "Computer Repair Service" });
            _list.Add(new Category() { ID = 16, Name = "Consulting & Coaching", CallDescription1 = "Top Rated Consultants & Coaches.", CallDescription2 = "Book Your Appt. Now!", CallTitle = "Consulting & Coaching" });
            _list.Add(new Category() { ID = 17, Name = "Dance School", CallDescription1 = "Teaching Dance Since 2000.", CallDescription2 = "Book Your First Dance Class Today!", CallTitle = "Dance School" });
            _list.Add(new Category() { ID = 18, Name = "Dentist", CallDescription1 = "High-quality Dental Care.", CallDescription2 = "Call To See A Dentist Today!", CallTitle = "General Dentist Services" });
            _list.Add(new Category() { ID = 19, Name = "Designer - Website", CallDescription1 = "10 yrs Of Experience In Web Design.", CallDescription2 = "Call Now For A Free Quote!", CallTitle = "Website Designer" });
            _list.Add(new Category() { ID = 20, Name = "Designer - Interior/Home", CallDescription1 = "Let Us Design Your Home.", CallDescription2 = "Call Now For A Free Quote!", CallTitle = "Interior Designer" });
            _list.Add(new Category() { ID = 21, Name = "Disc Jockey", CallDescription1 = "Custom DJ Packages Available.", CallDescription2 = "Call For A Quote Today!", CallTitle = "Disc Jockey" });
            _list.Add(new Category() { ID = 22, Name = "Divorce Lawyer", CallDescription1 = "Experienced Family Divorce Lawyers.", CallDescription2 = "Call Us Now For A Consultation!", CallTitle = "Divorce Lawyer" });
            _list.Add(new Category() { ID = 23, Name = "Drug Addiction Counseling", CallDescription1 = "Drug Addition Is Treatable.", CallDescription2 = "To Learn More, Call Us.", CallTitle = "Drug Addiction Couneling" });
            _list.Add(new Category() { ID = 24, Name = "Dry Cleaning", CallDescription1 = "24-Hour Dry Cleaning Service.", CallDescription2 = "Call Us For Pick Up Now!", CallTitle = "Dry Cleaning Service" });
            _list.Add(new Category() { ID = 25, Name = "Electrician", CallDescription1 = "Experience Electricians.", CallDescription2 = "Call Us Now For Immediate Service!", CallTitle = "Electrician" });
            _list.Add(new Category() { ID = 26, Name = "Electronics", CallDescription1 = "Description 1", CallDescription2 = "Description 2" });
            _list.Add(new Category() { ID = 27, Name = "Event Planning", CallDescription1 = "Let Us Plan Your Next Event!", CallDescription2 = "Call For Your Free Consultation.", CallTitle = "Event Planner" });
            _list.Add(new Category() { ID = 28, Name = "Family Counseling", CallDescription1 = "Experienced Family Counselors.", CallDescription2 = "Call To Book Your Appt. Today!", CallTitle = "Family Counseling" });
            _list.Add(new Category() { ID = 29, Name = "Fashion & Accessories", CallDescription1 = "Description 1", CallDescription2 = "Description 2" });
            _list.Add(new Category() { ID = 30, Name = "Financial Advisor", CallDescription1 = "Let's Plan Your Financial Future.", CallDescription2 = "Talk To An Advisor Today!", CallTitle = "Financial Advisor" });
            _list.Add(new Category() { ID = 31, Name = "Fire Damage Restoration", CallDescription1 = "Fire Damage Restoration Specialist.", CallDescription2 = "Call Us Today To Learn More!", CallTitle = "Fire Damage Restoration" });
            _list.Add(new Category() { ID = 32, Name = "Flooring Installation", CallDescription1 = "Quality Flooring & Installation.", CallDescription2 = "Call For Your Free Estimation!", CallTitle = "Flooring Installation" });
            _list.Add(new Category() { ID = 33, Name = "Florist", CallDescription1 = "Same Day Flower Delivery.", CallDescription2 = "Order Flowers Today!", CallTitle = "Florist" });
            _list.Add(new Category() { ID = 34, Name = "Funeral Services", CallDescription1 = "24-Hour Funeral Home.", CallDescription2 = "Call Us Today, We Can Help.", CallTitle = "Funeral Services" });
            _list.Add(new Category() { ID = 35, Name = "Furniture Upholstery", CallDescription1 = "25 yrs Experience In Upholstery.", CallDescription2 = "Get Your Free Estimate Today!", CallTitle = "Furniture Upholstery" });
            _list.Add(new Category() { ID = 36, Name = "Garage Doors Repair", CallDescription1 = "Licensed Garage Door Installation.", CallDescription2 = "Book A Same-Day Appointment!", CallTitle = "Garage Doors Repair" });
            _list.Add(new Category() { ID = 37, Name = "General Contractor", CallDescription1 = "20 yrs Experience As A Contractor.", CallDescription2 = "Free Consult, Call Us Today!", CallTitle = "General Contractor" });
            _list.Add(new Category() { ID = 38, Name = "General Practitioner", CallDescription1 = "Get More Time With Your Doctor.", CallDescription2 = "Book A Same-Day Appointment!", CallTitle = "General Practitioner" });
            _list.Add(new Category() { ID = 39, Name = "Gifts", CallDescription1 = "Description 1", CallDescription2 = "Description 2" });
            _list.Add(new Category() { ID = 40, Name = "Hair Salon", CallDescription1 = "Let Us Make Your Hair Look Great.", CallDescription2 = "Give Us A Call To Schedule An Appt!", CallTitle = "Hair Salon" });
            _list.Add(new Category() { ID = 41, Name = "Handyman", CallDescription1 = "25 yrs Experience As A Handyman.", CallDescription2 = "Call To Book Your Appt. Today.", CallTitle = "Handyman" });
            _list.Add(new Category() { ID = 42, Name = "Health & Beauty", CallDescription1 = "Description 1", CallDescription2 = "Description 2" });
            _list.Add(new Category() { ID = 43, Name = "Health Insurance", CallDescription1 = "Providing Insurance Since 2000.", CallDescription2 = "Call Today For Your Quote!", CallTitle = "Health Insurance Company" });
            _list.Add(new Category() { ID = 44, Name = "Home & Garden", CallDescription1 = "Description 1", CallDescription2 = "Description 2" });
            _list.Add(new Category() { ID = 45, Name = "Hotel", CallDescription1 = "Amazing Hotel Deals, Up to 80% Off.", CallDescription2 = "Call & Book a Room Now!", CallTitle = "Hotel Booking" });
            _list.Add(new Category() { ID = 46, Name = "House Cleaning", CallDescription1 = "9 Yrs Experience In House Cleaning.", CallDescription2 = "Book Your Cleaner Today!", CallTitle = "House Cleaning Service" });
            _list.Add(new Category() { ID = 47, Name = "HVAC", CallDescription1 = "Licensed HVAC Contractors.", CallDescription2 = "Call Us Now!", CallTitle = "HVAC Contractors" });
            _list.Add(new Category() { ID = 48, Name = "Interior Designer", CallDescription1 = "Let Us Design Your Home.", CallDescription2 = "Call For Your Free Quote Today!", CallTitle = "Interior Designer" });
            _list.Add(new Category() { ID = 49, Name = "Janitorial", CallDescription1 = "Experienced Janitors.", CallDescription2 = "Call For Your Free Quote Today!", CallTitle = "Janitorial Cleaning" });
            _list.Add(new Category() { ID = 50, Name = "Landscaping", CallDescription1 = "Up To 30% Off First Tree Tirming.", CallDescription2 = "Call To Book Your Appt. Today.", CallTitle = "Landscaping" });
            _list.Add(new Category() { ID = 51, Name = "Laser Hair Removal", CallDescription1 = "High-quality Laser Hair Removal.", CallDescription2 = "Nights & Weekend Appts. Available.", CallTitle = "Laser Hair Removal" });
            _list.Add(new Category() { ID = 52, Name = "Lawn Care", CallDescription1 = "20+ Yrs Of Experience In Lawn Care.", CallDescription2 = "Call For Your Free Quote Today!", CallTitle = "Lawn Care" });
            _list.Add(new Category() { ID = 53, Name = "Limousine Service", CallDescription1 = "Get Around In Style With A Limo.", CallDescription2 = "Order Your Limo Today!", CallTitle = "Limousine Service" });
            _list.Add(new Category() { ID = 54, Name = "Locksmith", CallDescription1 = "24/7 Locksmith Service.", CallDescription2 = "Call Now & Get 15% Off!", CallTitle = "Locksmith" });
            _list.Add(new Category() { ID = 55, Name = "Marriage Counseling", CallDescription1 = "30 yrs Experience In Counseling.", CallDescription2 = "Book Your Appt. Today!", CallTitle = "Marriage Counseling" });
            _list.Add(new Category() { ID = 56, Name = "Massage Therapy", CallDescription1 = "Licensed Massage Therapists.", CallDescription2 = "Book Your Appt. Today.", CallTitle = "Massage Therapist" });
            _list.Add(new Category() { ID = 57, Name = "Math Teacher", CallDescription1 = "Affordable 1-On-1 Tutor.", CallDescription2 = "Schedule Your Child's First Class!", CallTitle = "Math Tutor" });
            _list.Add(new Category() { ID = 58, Name = "Mortgage Broker", CallDescription1 = "We Specialize In Mortgages.", CallDescription2 = "Call Us Today!", CallTitle = "Mortgage Broker" });
            _list.Add(new Category() { ID = 59, Name = "Movers", CallDescription1 = "Full Service Moving Company.", CallDescription2 = "Call Us For Your Free Estimate!", CallTitle = "Moving Company" });
            _list.Add(new Category() { ID = 60, Name = "Musician", CallDescription1 = "Experienced Guitar Teacher.", CallDescription2 = "Book Us For Your Event!", CallTitle = "Musician" });
            _list.Add(new Category() { ID = 61, Name = "Music Teacher", CallDescription1 = "Private Music Lessons Available.", CallDescription2 = "Book Your First Lesson Today!", CallTitle = "Music Teacher" });
            _list.Add(new Category() { ID = 62, Name = "Nail Salon", CallDescription1 = "Nail Salon - 30% Off New Clients.", CallDescription2 = "Call To Book Your Appt. Today.", CallTitle = "Nail Salon" });
            _list.Add(new Category() { ID = 63, Name = "Neurologist", CallDescription1 = "Neurologist Personal Care.", CallDescription2 = "Book Your Appt. Today!", CallTitle = "Neurologist" });
            _list.Add(new Category() { ID = 64, Name = "OBGYN", CallDescription1 = "OBGYN - Over 25 Years of Experience", CallDescription2 = "Call To Book Your Appt.", CallTitle = "OBGYN" });
            _list.Add(new Category() { ID = 65, Name = "Optometrist", CallDescription1 = "One Stop Family Eye Care.", CallDescription2 = "Book Your Optometrist Appt. Today!", CallTitle = "Optometrist" });
            _list.Add(new Category() { ID = 66, Name = "Optician", CallDescription1 = "Optician - 30 Years Of Experience.", CallDescription2 = "Schedule An Eye Exam Today!", CallTitle = "Optician" });
            _list.Add(new Category() { ID = 67, Name = "Painter", CallDescription1 = "Painter - 15 Years of Experience.", CallDescription2 = "Call For a Quote Today!", CallTitle = "Painter" });
            _list.Add(new Category() { ID = 68, Name = "Personal Trainer", CallDescription1 = "Experienced Personal Trainer.", CallDescription2 = "Book Your Personal Trainer Today!", CallTitle = "Personal Trainer" });
            _list.Add(new Category() { ID = 69, Name = "Pest Control", CallDescription1 = "Pest Control 35 Years Of Experience", CallDescription2 = "Call For Your Free Inspection!", CallTitle = "Pest Control" });
            _list.Add(new Category() { ID = 70, Name = "Pets & Animals", CallDescription1 = "Description 1", CallDescription2 = "Description 2" });
            _list.Add(new Category() { ID = 71, Name = "Photographer", CallDescription1 = "Photographer: Studio or On Location", CallDescription2 = "Call & Schedule Appt. Today!", CallTitle = "Photographer" });
            _list.Add(new Category() { ID = 72, Name = "Physical Therapy", CallDescription1 = "Licensed Physical Therapists.", CallDescription2 = "Same Day Appt. Available, Call Now.", CallTitle = "Physical Therapy" });
            _list.Add(new Category() { ID = 73, Name = "Plastic Surgeon", CallDescription1 = "US Board Certified Surgeon.", CallDescription2 = "Call For Your Free Consultation!", CallTitle = "Plastic Surgeon" });
            _list.Add(new Category() { ID = 74, Name = "Plumber", CallDescription1 = "15 Yrs Experience As A Plumber.", CallDescription2 = "Same Day Appt. Available, Call Now.", CallTitle = "Plumber" });
            _list.Add(new Category() { ID = 75, Name = "Private Caregiver", CallDescription1 = "Personal & Professional Caregivers.", CallDescription2 = "Call Today To Learn More!", CallTitle = "Private Caregiver" });
            _list.Add(new Category() { ID = 76, Name = "Private Investigator", CallDescription1 = "30+ Years In Private Investigations", CallDescription2 = "Call For A Free Quote!", CallTitle = "Private Investigator" });
            _list.Add(new Category() { ID = 777, Name = "Psychic", CallDescription1 = "Psychic -10% Off First Reading.", CallDescription2 = "Call To Book Your Reading Today!", CallTitle = "Psychic" });
            _list.Add(new Category() { ID = 78, Name = "Psychologist", CallDescription1 = "Licensed Psychologists.", CallDescription2 = "Call For Your Free Consultation!", CallTitle = "Psychologist" });
            _list.Add(new Category() { ID = 79, Name = "Real Estate Agent", CallDescription1 = "Licensed Real Estate Agents.", CallDescription2 = "Call Today To Learn More!", CallTitle = "Real Estate Agent" });
            _list.Add(new Category() { ID = 80, Name = "Restaurant", CallDescription1 = "Dine With Us At Our Restaurant.", CallDescription2 = "Book Your Reservation For Tonight!", CallTitle = "Restaurant" });
            _list.Add(new Category() { ID = 81, Name = "Roofing", CallDescription1 = "Free Roof Inspection.", CallDescription2 = "Call Our Team Now!", CallTitle = "Roofing Service" });
            _list.Add(new Category() { ID = 82, Name = "Security System", CallDescription1 = "24/7 Security System Protection.", CallDescription2 = "Call Now To Learn More!", CallTitle = "Security System" });
            _list.Add(new Category() { ID = 83, Name = "Self Storage", CallDescription1 = "Climate Controled Storage Units.", CallDescription2 = "Call Us For Monthly Rates!", CallTitle = "Self Storage" });
            _list.Add(new Category() { ID = 84, Name = "Spa", CallDescription1 = "Treat Yourself At Our Spa.", CallDescription2 = "Make An Appt. Today!", CallTitle = "Spa Services" });
            _list.Add(new Category() { ID = 85, Name = "Sports", CallDescription1 = "Description 1", CallDescription2 = "Description 2" });
            _list.Add(new Category() { ID = 86, Name = "Swimming Pool", CallDescription1 = "Pool Repair & Remodeling.", CallDescription2 = "Call For Your Free Consultation!", CallTitle = "Swimming Pool" });
            _list.Add(new Category() { ID = 87, Name = "Tanning", CallDescription1 = "Get Up To 30% Off Your First Tan.", CallDescription2 = "Call To Book Your Appt.!", CallTitle = "Tanning Salon" });
            _list.Add(new Category() { ID = 88, Name = "Taxi", CallDescription1 = "24-Hour Taxi Company.", CallDescription2 = "Call Now!", CallTitle = "Taxi Company" });
            _list.Add(new Category() { ID = 89, Name = "Towing Services", CallDescription1 = "24-Hour Towing Service.", CallDescription2 = "Call Now!", CallTitle = "Towing Services" });
            _list.Add(new Category() { ID = 90, Name = "Travel", CallDescription1 = "Travel Agency - 20 yrs Of Experience.", CallDescription2 = "Call for Great Deals on Flights!", CallTitle = "Travel Agency" });
            _list.Add(new Category() { ID = 91, Name = "Veterinarian", CallDescription1 = "Board Certified Veterinarians.", CallDescription2 = "Call to Book Your Appt. Today!", CallTitle = "Veterinarian" });
            _list.Add(new Category() { ID = 92, Name = "Wedding Planning", CallDescription1 = "Let Us Help You Plan Your Wedding.", CallDescription2 = "Call to Book Your Appt. Today!", CallTitle = "Wedding Planner" });
            _list.Add(new Category() { ID = 93, Name = "Other", CallDescription1 = "Description 1", CallDescription2 = "Description 2", CallTitle = "Other" });

        }

    }
}
