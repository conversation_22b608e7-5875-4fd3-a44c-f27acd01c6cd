﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Storeya.Core.Models;
using Storeya.Core.Models.AdWords;
using Storeya.Core.Models.AuditTool;
using Storeya.Core.Models.BI;
using Storeya.Core.Models.FbAds;
using Storeya.Core.Models.GrowthHeroModels;
using Storeya.Core.Models.OnPageAnalysis;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models.TrafficBoosterModels.TrafficChannels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI;

namespace Storeya.Core.Helpers
{
    public static class TbFacebookAdsHelper
    {
        public enum SCHEDULEAD_STATUS
        {
            ERROR = -1,           
            CREATED = 0,
            IN_PROGRESS = 2,
            ADDED_TO_CAMPAIGNS = 3,
            ACTIVE = 1,
            PAUSED = 10,   
            DISABLED = 100,
        }
        public enum PROGRESS_STATUS
        {
            ERROR = -1,
            NONE = 0,
            ANALYSIS_DONE = 1,
            WAITING = 3,
            WAITING_FOR_ACCOUNT_TO_BE_SELECTED = 5,
            WAITING_ACCOUNT_HAS_CHANGED = 7,
            SETUP = 9,
            OPTIMIZATION = 11,
            CHECKOUT = 13,
            SHARE_ACCESS = 15,
            UPDATE_CAMPAIGN = 17,
            CAMPAIGNS_INPROGRESS = 19,
            CAMPAIGNS_PARTLY_DONE = 20,
            CAMPAIGNS_DONE = 21,
        }
        public static List<TbFacebookAdsSetup> GetTbFacebookAdsSetups()
        {
            var db = DataHelper.GetStoreYaEntities();
            List<TbFacebookAdsSetup> tbFacebookAdsSetups = db.TbFacebookAdsSetups.ToList();
            return tbFacebookAdsSetups;
        }

        public static TbFacebookAdsSetup GetTbFacebookAdsSetupById(int id)
        {
            var db = DataHelper.GetStoreYaEntities();
            TbFacebookAdsSetup tbFacebookAdsSetup = db.TbFacebookAdsSetups.Where(c => c.ID == id).SingleOrDefault();
            return tbFacebookAdsSetup;
        }
        public static List<TbAccountTrafficChannel> GetPartlyDoneAddAccounts(int? shopId = null, bool newAccount = true)
        {
            var db = DataHelper.GetStoreYaEntities();
            var q = from c in db.TbAccountTrafficChannels
                    join s in db.TbFacebookAdsSetups
                    on c.ShopID equals s.ShopID
                    where c.ChannelType == (int)TrafficChannelsTypes.Facebook
                    && (shopId.HasValue ? c.ShopID == shopId : 1 == 1)
                    && c.PurchasedBudget > 0
                    && c.Status == (int)TrafficChannelsStatuses.Active
                    && s.ProgressStatus == (int)PROGRESS_STATUS.CAMPAIGNS_PARTLY_DONE
                    && string.IsNullOrEmpty(s.ScheduleAds) == newAccount
                    select c;
            if (q == null && q.Count() == 0)
            {
                return null;
            }
            return q.ToList();
        }
        public static List<TbAccountTrafficChannel> GetScheduleAdsChannels(int? shopId = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var q = from c in db.TbAccountTrafficChannels
                    join s in db.TbFacebookAdsSetups
                    on c.ShopID equals s.ShopID
                    where c.ChannelType == (int)TrafficChannelsTypes.Facebook
                    && (shopId.HasValue ? c.ShopID == shopId : 1 == 1)
                    && c.PurchasedBudget > 0
                    && c.Status == (int)TrafficChannelsStatuses.Active
                    && s.ProgressStatus == (int)PROGRESS_STATUS.CAMPAIGNS_DONE
                     && s.ScheduleAds != null
                    select c;
            if (q == null && q.Count() == 0)
            {
                return null;
            }
            return q.ToList();
        }
        public static TbFacebookAdsSetup GetTbFacebookAdsSetup(int shopId, List<PROGRESS_STATUS> statues = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            return GetTbFacebookAdsSetup(db, shopId, statues);
        }
        public static bool CheckIfFbeExists(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            var f = GetTbFacebookAdsSetup(db, shopId, null);
            if (f == null)
            {
                return false;
            }
            if (string.IsNullOrEmpty(f.FbeToken))
            {
                return false;
            }
            return true;
        }
        public static TbFacebookAdsSetup GetTbFacebookAdsSetup(StoreYaEntities db, int shopId, List<PROGRESS_STATUS> statues = null)
        {
            TbFacebookAdsSetup tbFacebookAdsSetup = null;
            if (statues == null)
            {
                tbFacebookAdsSetup = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
            }
            else
            {
                tbFacebookAdsSetup = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId && statues.Contains((PROGRESS_STATUS)c.ProgressStatus)).SingleOrDefault();
            }
            return tbFacebookAdsSetup;
        }
        public static TbFacebookAdsSetup UpdateStatus(int shopId, PROGRESS_STATUS status)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                TbFacebookAdsSetup tbFacebookAds = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
                if (tbFacebookAds != null)
                {
                    tbFacebookAds.ProgressStatus = (int)status;
                    db.SaveChanges();
                }
                Log4NetLogger.Info(string.Format("UpdateStatus TbFacebookAdsSetup for shopID: {0} new status {1}", shopId, ((PROGRESS_STATUS)tbFacebookAds.ProgressStatus).ToString()), shopId);
                return tbFacebookAds;

            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to UpdateStatus TbFacebookAdsSetup for shopID: {0} status {1}", shopId, status.ToString()), ex, shopId);
                return null;
            }

        }
        public static PROGRESS_STATUS GetStatus(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            TbFacebookAdsSetup tbFacebookAds = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
            if (tbFacebookAds != null)
            {
                if (tbFacebookAds.ProgressStatus == null)
                {
                    return PROGRESS_STATUS.NONE;
                }
                return (PROGRESS_STATUS)tbFacebookAds.ProgressStatus;
            }
            return PROGRESS_STATUS.NONE;
        }
        public static TbFacebookAdsSetup UpdateAccountPermissions(int shopId, string accountId = null, string accountName = null, string fbToken = null, string fbAccountID = null, string pageToken = null, string fbeToken = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            TbFacebookAdsSetup tbFacebookAds = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
            bool newRow = false;
            if (tbFacebookAds == null)
            {
                tbFacebookAds = new TbFacebookAdsSetup();
                tbFacebookAds.InsertedAt = DateTime.Now;
                newRow = true;
            }
            if (!string.IsNullOrEmpty(fbAccountID))
            {
                tbFacebookAds.FbAccountID = fbAccountID;
            }
            else
            {
                if (ValidFBUserIdFromToken(shopId, fbToken, out string userId))
                {
                    tbFacebookAds.FbAccountID = userId;
                }



            }
            if (!string.IsNullOrEmpty(fbToken))
            {
                tbFacebookAds.FbToken = fbToken;
            }
            if (!string.IsNullOrEmpty(accountId))
            {
                tbFacebookAds.AccountID = accountId;
            }
            if (!string.IsNullOrEmpty(accountName))
            {
                tbFacebookAds.AccountName = accountName;
            }
            if (!string.IsNullOrEmpty(pageToken))
            {
                tbFacebookAds.PageToken = pageToken;
            }
            if (!string.IsNullOrEmpty(fbeToken))
            {
                tbFacebookAds.FbeToken = fbeToken;
            }
            tbFacebookAds.UpdatedAt = DateTime.Now;
            tbFacebookAds.ProgressStatus = PROGRESS_STATUS.SHARE_ACCESS.GetHashCode();

            if (newRow)
            {
                tbFacebookAds.ShopID = shopId;
                db.TbFacebookAdsSetups.Add(tbFacebookAds);
            }
            db.SaveChanges();
            Log4NetLogger.Info(string.Format("UpdateAccountPermissions TbFacebookAdsSetup for shopID: {0} new status {1}", shopId, ((PROGRESS_STATUS)tbFacebookAds.ProgressStatus).ToString()), shopId);
            return tbFacebookAds;

        }

        private static bool ValidFBUserIdFromToken(int shopid, string fbToken, out string userId)
        {
            userId = null;
            try
            {
                // string adminToken = Models.FbAds.FbAdsBusinesses.GetAdminToken(1);
                FbAdsSDK sdk = new FbAdsSDK(fbToken, false);
                sdk.ShopID = shopid;
                bool res = sdk.IsTokenValid(fbToken, out userId);
                if (string.IsNullOrEmpty(userId))
                {
                    throw new Exception("Failed to get FB user id from token:" + fbToken);
                }
                return res;
                //string res = FeedsHelper.HttpGet($"https://graph.facebook.com/debug_token?input_token={fbToken}&access_token={adminToken}", "", "", FeedsHelper.CONTENT_TYPES.JSON);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Failed to get FB user id from token:" + fbToken, ex, shopid);
            }
            return false;


        }

        public static TbFacebookAdsSetup UpdateFBCampaignSetup(int shopId, string primaryText, string primaryText1, string imageHash, string adType, string campaignHeadLine,
            string collectionid = null, DateTime? startAdsDate = null, DateTime? endAdsdate = null, PROGRESS_STATUS? status = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            TbFacebookAdsSetup tbFacebookAds = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
            bool newRow = false;
            if (tbFacebookAds == null)
            {
                tbFacebookAds = new TbFacebookAdsSetup();
                tbFacebookAds.InsertedAt = DateTime.Now;
                tbFacebookAds.UpdatedAt = DateTime.Now;
                tbFacebookAds.ShopID = shopId;
                newRow = true;
            }
            //if (tbFacebookAds.CampaignFrameImage != null && tbFacebookAds.CampaignFrameImage.ToUpper().Contains(adType.ToUpper()))
            //{
            //    string[] t = tbFacebookAds.CampaignFrameImage.Split(',');
            //    string p = string.Empty;
            //    foreach (string item in t)
            //    {
            //        if (!item.ToUpper().Contains(adType.ToUpper()))
            //        {
            //            p = p + "," + item;
            //        }
            //    }
            //    tbFacebookAds.CampaignFrameImage = p;"collection:0"
            //}
            tbFacebookAds.CampaignFrameImage = "collection:0";
            tbFacebookAds.CampaignText = primaryText;
            tbFacebookAds.CampaignText1 = primaryText1;
            tbFacebookAds.CampaignHeadLine = campaignHeadLine;
            if (status.HasValue)
            {
                tbFacebookAds.ProgressStatus = status.Value.GetHashCode();
            }
            //string image = string.Empty;
            //if (!string.IsNullOrEmpty(imageHash))
            //{
            //    image = string.Format("{0}:{1},{2}", adType, imageHash, tbFacebookAds.CampaignFrameImage);
            //}
            //if (!string.IsNullOrEmpty(collectionid) && !image.ToUpper().Contains("COLLECTION"))
            //{
            //    image = string.Format("{0}:{1},{2}", "collection", collectionid, image);
            //}

            if (newRow)
            {
                db.TbFacebookAdsSetups.Add(tbFacebookAds);
            }
            db.SaveChanges();
            Log4NetLogger.Info(string.Format("UpdateFBCampaignSetup TbFacebookAdsSetup for shopID: {0} new status {1}", shopId, ((PROGRESS_STATUS)tbFacebookAds.ProgressStatus).ToString()), shopId);
            return tbFacebookAds;
        }
        public static TbFacebookAdsSetup UpdateAccount(int shopId, string accountId, string accountName, string fbToken, string fbAccountID, PROGRESS_STATUS status)
        {
            var db = DataHelper.GetStoreYaEntities();
            TbFacebookAdsSetup tbFacebookAds = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
            bool newRow = false;
            if (tbFacebookAds == null)
            {
                tbFacebookAds = new TbFacebookAdsSetup();
                tbFacebookAds.InsertedAt = DateTime.Now;
                newRow = true;
            }
            tbFacebookAds.FbAccountID = fbAccountID;
            tbFacebookAds.FbToken = fbToken;
            if (!string.IsNullOrEmpty(tbFacebookAds.AccountID) && tbFacebookAds.AccountID != accountId)
            {
                status = PROGRESS_STATUS.WAITING_ACCOUNT_HAS_CHANGED;
                tbFacebookAds.AccountScore = null;
                tbFacebookAds.AccountScore = null;
                tbFacebookAds.AgeJson = null;
                tbFacebookAds.GenderJson = null;
                tbFacebookAds.MissingOpportunitiesJson = null;
                tbFacebookAds.TotalAccountBudget = null;
                tbFacebookAds.CampaignsCount = null;
                tbFacebookAds.AdSetsCount = null;
                tbFacebookAds.AdsCount = null;

            }
            tbFacebookAds.AccountID = accountId;
            tbFacebookAds.AccountName = accountName;
            tbFacebookAds.UpdatedAt = DateTime.Now;
            tbFacebookAds.ShopID = shopId;
            if (status != PROGRESS_STATUS.NONE)
            {
                tbFacebookAds.ProgressStatus = (int)status;
            }
            if (newRow)
            {
                db.TbFacebookAdsSetups.Add(tbFacebookAds);
            }
            db.SaveChanges();
            Log4NetLogger.Info(string.Format("UpdateAccount TbFacebookAdsSetup for shopID: {0} new status {1}", shopId, ((PROGRESS_STATUS)tbFacebookAds.ProgressStatus).ToString()), shopId);

            return tbFacebookAds;

        }
        public static TbFacebookAdsSetup ManageTbFacebookAdsSetup(TbFacebookAdsSetup tbFacebookAdsSetup)
        {
            int shopID = tbFacebookAdsSetup.ShopID;
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                TbFacebookAdsSetup tbFacebookAds = db.TbFacebookAdsSetups.Where(c => c.ID == tbFacebookAdsSetup.ID).SingleOrDefault();
                if (tbFacebookAds == null)
                {
                    tbFacebookAdsSetup.UpdatedAt = DateTime.Now;
                    tbFacebookAdsSetup.Active = true;
                    tbFacebookAdsSetup.InsertedAt = DateTime.Now;
                    tbFacebookAds.ProgressStatus = PROGRESS_STATUS.NONE.GetHashCode();
                    db.TbFacebookAdsSetups.Add(tbFacebookAdsSetup);
                }
                else
                {
                    tbFacebookAds.UpdatedAt = DateTime.Now;
                    tbFacebookAds.Active = tbFacebookAdsSetup.Active;
                    tbFacebookAds.AccountID = tbFacebookAdsSetup.AccountID;
                    tbFacebookAds.Age = tbFacebookAdsSetup.Age;
                    tbFacebookAds.CallCategory = tbFacebookAdsSetup.CallCategory;
                    tbFacebookAds.ChangedBy = tbFacebookAdsSetup.ChangedBy;
                    tbFacebookAds.Country = tbFacebookAdsSetup.Country;
                    tbFacebookAds.CouponCode = tbFacebookAdsSetup.CouponCode;
                    tbFacebookAds.Description = tbFacebookAdsSetup.Description;
                    tbFacebookAds.FreeShipping = tbFacebookAdsSetup.FreeShipping;
                    tbFacebookAds.Gender = tbFacebookAdsSetup.Gender;
                    tbFacebookAds.ShopID = tbFacebookAdsSetup.ShopID;
                    tbFacebookAds.OtherPromotion = tbFacebookAdsSetup.OtherPromotion;
                    tbFacebookAds.ProgressStatus = tbFacebookAdsSetup.ProgressStatus;
                    tbFacebookAds.PromotionAmount = tbFacebookAdsSetup.PromotionAmount;
                    tbFacebookAds.PromotionType = tbFacebookAdsSetup.PromotionType;
                    tbFacebookAds.Url1 = tbFacebookAdsSetup.Url1;
                    tbFacebookAds.FbToken = tbFacebookAdsSetup.FbToken;
                }
                db.SaveChanges();
                Log4NetLogger.Info(string.Format("ManageTbFacebookAdsSetup TbFacebookAdsSetup for shopID: {0} new status {1}", tbFacebookAdsSetup.ShopID, ((PROGRESS_STATUS)tbFacebookAds.ProgressStatus).ToString()), tbFacebookAdsSetup.ShopID);
                return tbFacebookAdsSetup;
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to update TbFacebookAdsSetup object for shopID: {0}", shopID), ex, shopID);
                throw ex;
            }

        }

        public static string HasNewResults(TbFacebookAdsSetup tbFacebookAdsSetup)
        {
            if (tbFacebookAdsSetup != null && tbFacebookAdsSetup.ProgressStatus != null)
            {
                if (tbFacebookAdsSetup.ProgressStatus == (int)TbFacebookAdsHelper.PROGRESS_STATUS.ANALYSIS_DONE)
                {
                    return "1";
                }
                if (tbFacebookAdsSetup.ProgressStatus == (int)TbFacebookAdsHelper.PROGRESS_STATUS.ERROR)
                {
                    return "-1";
                }
            }
            return "0";
        }

        private static FbAdsSDK PrepareRulesData(TbFacebookAdsSetup facebookAdsSetup, out FbAdsSDK.CustomAudiencesResponse.Response elements, out string errors)
        {
            errors = string.Empty;
            FbAdsSDK sdk = new FbAdsSDK(facebookAdsSetup.FbToken);
            sdk.ShopID = facebookAdsSetup.ShopID;
            if (facebookAdsSetup.AccountID != null) sdk.AccountID = Convert.ToInt64(facebookAdsSetup.AccountID);

            elements = sdk.GetRulesData();
            if (StoreyaErrors.Instance.HasErrors)
            {
                errors = StoreyaErrors.Instance.ToString();
            }
            return sdk;
        }

        private static GrowthHeroMissingOpportunitiesModel PrepareMissingOpporHeroMissingOpportunitiesModel(FbAdsSDK.CustomAudiencesResponse.Response elements)
        {
            GrowthHeroMissingOpportunities missingOpportunities = GrowthHeroManager.CalculateMissingApportunities(elements);

            GrowthHeroMissingOpportunitiesModel heroMissingOpportunitiesModel =
                new GrowthHeroMissingOpportunitiesModel(missingOpportunities.DpaMissingOpportunities,
                    missingOpportunities.RetargetingMissingOpportunities, missingOpportunities.LookALikeMissingOpportunities);
            return heroMissingOpportunitiesModel;
        }

        private static FbAdsBreakdown CreateBreakdown(string label, List<ParsedInsight> rows)
        {
            FbAdsBreakdown b = new FbAdsBreakdown();
            b.Label = label;
            b.Rows = rows;
            return b;
        }

        private static void CalculateMaxGenderProportion(FbAdsBreakdown dateAge, GrowthHeroModel tempStorage)
        {
            if (dateAge.Rows.Count > 0)
            {
                double maxAge = dateAge.Rows.Max(p => double.Parse(p.Clicks));
                foreach (ParsedInsight insight in dateAge.Rows)
                {
                    double sum = 0;
                    double.TryParse(insight.Clicks, out sum);
                    double per = (sum / maxAge) * 100d;
                    tempStorage.Ages.Add(new GrowthHeroAgeModel()
                    {
                        BreakDownLabel = insight.breakdownLabel,
                        Percentage = per
                    });
                }
            }
        }

        private static decimal CalculatePercentage(FbAdsBreakdown ageGender, out decimal femalePercent)
        {
            var females = ageGender.Rows.Where(p => p.breakdownLabel.StartsWith("female"));
            var males = ageGender.Rows.Where(p => p.breakdownLabel.StartsWith("male"));
            var femaleSpent = females.Sum(p => decimal.Parse(p.AmountSpentUSD));
            var maleSpent = males.Sum(p => decimal.Parse(p.AmountSpentUSD));
            var totalSpent = femaleSpent + maleSpent;
            decimal malePercent = 0;
            femalePercent = 0;
            if (totalSpent != 0)
            {
                malePercent = Math.Round((maleSpent / totalSpent) * 100, 0);
                femalePercent = Math.Round((femaleSpent / totalSpent) * 100, 0);
            }

            return malePercent;
        }

        public static void SetWinners(FbAdsBreakdown breakdown, InsightsViewModel model)
        {
            BiAssistant bi = new BiAssistant();
            var rows = GetRowToAnalyze(breakdown);

            BiAnalysisResponse response = bi.Analyze(rows);
            if (response.Winners.Count > 0)
            {
                foreach (var item in response.Winners)
                {
                    model.Winners.Add(breakdown.Label + " - " + item);
                }

            }

            if (response.Losers.Count > 0)
            {
                foreach (var item in response.Losers)
                {
                    model.Loosers.Add(item);
                }
            }
        }

        private static List<BiDataRow> GetRowToAnalyze(FbAdsBreakdown list)
        {
            List<BiDataRow> rows = new List<BiDataRow>();

            if (list != null)
            {
                foreach (var item in list.Rows)
                {
                    BiDataRow r = new BiDataRow()
                    {
                        AdId = item.ad_id,
                        Dimension = item.breakdownLabel,
                        ConversionsAmount = Convert.ToInt32(item.WebsitePurchases),
                        ConversionsValue = Convert.ToDecimal(item.WebsitePurchasesConversionValue),
                        EnergySpend = Convert.ToDecimal(item.AmountSpentUSD),
                        Label = list.Label
                    };

                    rows.Add(r);
                }
            }
            return rows;
        }

        private static decimal CalculateTotalBudget(InsightsViewModel model)
        {
            decimal totalAccountBudget = 0;

            var totalBreakdown = model.Breakdowns.Where(b => b.Label == "Platform");

            foreach (var insight in totalBreakdown)
            {
                foreach (ParsedInsight row in insight.Rows)
                {
                    decimal currentSum = 0;
                    if (decimal.TryParse(row.AmountSpentUSD, out currentSum)) totalAccountBudget += currentSum;
                }
            }

            return totalAccountBudget;
        }

        private static InsightsViewModel GetModel(MarketingAdsApiProvider provider, string id, string from = null, string to = null, string datePreset = null)
        {
            InsightsViewModel model = new InsightsViewModel();
            var ads = CreateBreakdown("ads", provider.GetData(id, "ad", datePreset, from, to));
            model.Breakdowns.Add(ads);
            SetWinners(ads, model);

            //var dataCountry = CreateBreakdown("Country", provider.GetData(id, "country", datePreset, from, to));
            //model.Breakdowns.Add(dataCountry);
            //SetWinners(dataCountry, model);

            var dataGender = CreateBreakdown("Gender", provider.GetData(id, "gender", datePreset, from, to));
            model.Breakdowns.Add(dataGender);
            SetWinners(dataGender, model);

            var dataAge = CreateBreakdown("Age", provider.GetData(id, "age", datePreset, from, to));
            model.Breakdowns.Add(dataAge);
            SetWinners(dataAge, model);

            //var dataDevicePlatform = CreateBreakdown("Device platform", provider.GetData(id, "device_platform", datePreset, from, to));
            //model.Breakdowns.Add(dataDevicePlatform);
            //SetWinners(dataDevicePlatform, model);

            //var dataImpressionDevice = CreateBreakdown("Impression Device", provider.GetData(id, "impression_device", datePreset, from, to));
            //model.Breakdowns.Add(dataImpressionDevice);
            //SetWinners(dataImpressionDevice, model);

            var dataAgeGender = CreateBreakdown("Gender and Age", provider.GetData(id, "age,gender", datePreset, from, to));
            model.Breakdowns.Add(dataAgeGender);
            SetWinners(dataAgeGender, model);

            var dataPublisherPlatform = CreateBreakdown("Platform", provider.GetData(id, "publisher_platform", datePreset, from, to));
            model.Breakdowns.Add(dataPublisherPlatform);
            SetWinners(dataPublisherPlatform, model);

            return model;
        }

        private static int? CalculateAccountScore(GrowthHeroMissingOpportunitiesModel heroMissingOpportunitiesModel, decimal totalAccountBudget, List<BiDataRow> modelLoosers)
        {
            int total = 100;

            if (!heroMissingOpportunitiesModel.DpaMissingOpportunities.VisitorsLast15)
                total -= 5;
            if (!heroMissingOpportunitiesModel.DpaMissingOpportunities.AddCartLast4Days)
                total -= 5;
            if (!heroMissingOpportunitiesModel.DpaMissingOpportunities.AddCartLast15Days)
                total -= 5;

            if (!heroMissingOpportunitiesModel.RetargetingMissingOpportunities.Visitors30Secs)
                total -= 5;
            if (!heroMissingOpportunitiesModel.RetargetingMissingOpportunities.AddToCard30Days)
                total -= 5;
            if (!heroMissingOpportunitiesModel.RetargetingMissingOpportunities.Purchased90Days)
                total -= 5;

            if (!heroMissingOpportunitiesModel.LookALikeMissingOpportunities.Purchased)
                total -= 5;
            if (!heroMissingOpportunitiesModel.LookALikeMissingOpportunities.PastVisitor)
                total -= 5;
            if (!heroMissingOpportunitiesModel.LookALikeMissingOpportunities.AddToCard)
                total -= 5;


            decimal loosThreshold = (totalAccountBudget * 5) / 100;
            foreach (BiDataRow looser in modelLoosers)
            {
                if (looser.EnergySpend > loosThreshold)
                    total -= 5;
            }

            return total;
        }

        private static List<GrowthHeroLoosers> PrepareLoosersSection(InsightsViewModel model, out List<GrowthHeroLoosers> otherLoosers)
        {
            var loosersGrouped = (from i in model.Loosers
                                  group i by i.Label
                into g
                                  select new { Name = g.Key, Values = g.ToList().OrderByDescending(p => p.EnergySpend).Take(3) });


            var enumerable = loosersGrouped.ToList();
            List<GrowthHeroLoosers> loosersFinal = (from i in enumerable
                                                    orderby i.Values.Sum(p => p.EnergySpend) descending
                                                    select new GrowthHeroLoosers(i.Name, i.Values.ToList())).Take(3).ToList();

            otherLoosers = (from i in enumerable
                            orderby i.Values.Sum(p => p.EnergySpend) descending
                            select new GrowthHeroLoosers(i.Name, i.Values.ToList())).Skip(3).ToList();
            return loosersFinal;
        }

        private static void PrepareDbObject(Storeya.Core.TbFacebookAdsSetup tbFacebookAdsSetup, GrowthHeroMissingOpportunitiesModel heroMissingOpportunitiesModel,
          decimal totalAccountBudget, InsightsViewModel model, GrowthHeroModel tempStorage, List<GrowthHeroLoosers> otherLoosers,
          List<GrowthHeroLoosers> loosersFinal, int campaignsCount, int adSetsCount, int adsCount)
        {
            tbFacebookAdsSetup.AccountScore = CalculateAccountScore(heroMissingOpportunitiesModel, totalAccountBudget, model.Loosers);
            tbFacebookAdsSetup.AccountScore = Math.Max(tbFacebookAdsSetup.AccountScore ?? 0, 40); // set total mark to min to 40%
            tbFacebookAdsSetup.AgeJson = JsonConvert.SerializeObject(tempStorage.Ages);
            tbFacebookAdsSetup.GenderJson = JsonConvert.SerializeObject(tempStorage.Gender);
            tbFacebookAdsSetup.MissingOpportunitiesJson = JsonConvert.SerializeObject(heroMissingOpportunitiesModel);

            //List<BiDataRow> otherList = new List<BiDataRow>();
            //foreach (GrowthHeroLoosers looser in otherLoosers) otherList.AddRange(looser.Items);


            //decimal topLoosersSum = loosersFinal.Sum(p => p.Items.Sum(px => px.EnergySpend));
            //decimal allLoosersSum = model.Loosers.Sum(p => p.EnergySpend);
            //var diff = allLoosersSum - topLoosersSum;
            //loosersFinal.Add(new GrowthHeroLoosers("Other", otherList));
            //  tbFacebookAdsSetup.LoosingSegmentsJson = JsonConvert.SerializeObject(loosersFinal);

            // tbFacebookAdsSetup.WastedAmount3Month = topLoosersSum + diff;            
            tbFacebookAdsSetup.TotalAccountBudget = totalAccountBudget;

            tbFacebookAdsSetup.CampaignsCount = campaignsCount;
            tbFacebookAdsSetup.AdSetsCount = adSetsCount;
            tbFacebookAdsSetup.AdsCount = adsCount;

            tbFacebookAdsSetup.ProgressStatus = (int)PROGRESS_STATUS.ANALYSIS_DONE;
            tbFacebookAdsSetup.UpdatedAt = DateTime.Now;
        }

        public static bool WasExecuted(int shopID)
        {
            var db = DataHelper.GetStoreYaEntities();
            TbFacebookAdsSetup tbFacebookAdsSetup = GetTbFacebookAdsSetup(shopID);
            if (tbFacebookAdsSetup.ProgressStatus == PROGRESS_STATUS.SETUP.GetHashCode())
            {
                if (!tbFacebookAdsSetup.CampaignsCount.HasValue || tbFacebookAdsSetup.CampaignsCount == 0)
                {
                    return false;
                }
            }
            if (tbFacebookAdsSetup.ProgressStatus == PROGRESS_STATUS.WAITING_ACCOUNT_HAS_CHANGED.GetHashCode())
            {
                tbFacebookAdsSetup.ProgressStatus = PROGRESS_STATUS.WAITING.GetHashCode();
                db.SaveChanges();
                return false;
            }
            if (tbFacebookAdsSetup.ProgressStatus == PROGRESS_STATUS.WAITING.GetHashCode())
            {
                if (tbFacebookAdsSetup.InsertedAt.Value.Date == DateTime.Now.Date
                    && tbFacebookAdsSetup.CampaignsCount.HasValue
                    && tbFacebookAdsSetup.CampaignsCount > 0
                    )
                {

                    tbFacebookAdsSetup.ProgressStatus = PROGRESS_STATUS.ANALYSIS_DONE.GetHashCode();
                    db.SaveChanges();
                    return true;
                }
                return false;
            }
            List<PROGRESS_STATUS> statues = new List<PROGRESS_STATUS>();
            statues.Add(PROGRESS_STATUS.ANALYSIS_DONE);
            statues.Add(PROGRESS_STATUS.OPTIMIZATION);
            statues.Add(PROGRESS_STATUS.CHECKOUT);
            if (statues.Contains((PROGRESS_STATUS)tbFacebookAdsSetup.ProgressStatus))
            {
                if (!tbFacebookAdsSetup.CampaignsCount.HasValue || tbFacebookAdsSetup.CampaignsCount == 0)
                {
                    return false;
                }
                if (tbFacebookAdsSetup.InsertedAt.Value.Date == DateTime.Now.Date)
                {
                    return true;
                }
            }
            return true;
        }

        public static bool RunAnalysis(int shopID)
        {
            string errors = string.Empty;
            Log4NetLogger.Info("FB RunAnalysis Start For Shop:", shopID);
            List<PROGRESS_STATUS> statues = new List<PROGRESS_STATUS>();
            statues.Add(PROGRESS_STATUS.SETUP);
            statues.Add(PROGRESS_STATUS.WAITING);
            statues.Add(PROGRESS_STATUS.WAITING_ACCOUNT_HAS_CHANGED);
            TbFacebookAdsSetup tbFacebookAdsSetup = GetTbFacebookAdsSetup(shopID, statues);

            var db = DataHelper.GetStoreYaEntities();


            if (tbFacebookAdsSetup == null)
            {
                errors = "FB RunAnalysis No TbFacebookAdsSetup in Waitting status For Shop:" + shopID;
                Log4NetLogger.Error("FB RunAnalysis No TbFacebookAdsSetup in Waitting status For Shop:", shopID);
                //tbFacebookAdsSetup.Description = errors;
                //tbFacebookAdsSetup.ProgressStatus = (int)PROGRESS_STATUS.ERROR;
                return false;
            }


            //Console.WriteLine("Working with " + hero.FbAccountID);
            GrowthHeroModel tempStorage = new GrowthHeroModel();
            MarketingAdsApiProvider provider = new MarketingAdsApiProvider(tbFacebookAdsSetup.FbToken);
            string id = string.Format("act_{0}", tbFacebookAdsSetup.AccountID);

            var sdk = PrepareRulesData(tbFacebookAdsSetup, out var elements, out errors);
            if (!string.IsNullOrEmpty(errors))
            {
                tbFacebookAdsSetup.ProgressStatus = (int)PROGRESS_STATUS.ERROR;
                tbFacebookAdsSetup.Description = errors;
                db.SaveChanges();
                return false;
            }

            var heroMissingOpportunitiesModel = PrepareMissingOpporHeroMissingOpportunitiesModel(elements);

            try
            {
                var today = tbFacebookAdsSetup.ToDate.HasValue ? tbFacebookAdsSetup.ToDate.Value.Date : DateTime.Now.Date.AddDays(-1);
                var fromDate = tbFacebookAdsSetup.FromDate.HasValue ? tbFacebookAdsSetup.FromDate.Value.Date : today.AddDays(-90);

                var tillDateString = today.ToString("yyyy-MM-dd");
                var fromDateString = fromDate.ToString("yyyy-MM-dd");

                string datePreset = null;
                if (!tbFacebookAdsSetup.ToDate.HasValue)
                {
                    datePreset = "last_90d";
                }

                FbAdsBreakdown ageGender = CreateBreakdown("Gender Age", provider.GetData(id, "age,gender", datePreset, fromDateString, tillDateString));
                FbAdsBreakdown dateAge = CreateBreakdown("Age", provider.GetData(id, "age", datePreset, fromDateString, tillDateString));

                var campaigns = sdk.GetCampaings(fromDateString, tillDateString, true);
                int campaignsCount = campaigns.Count;

                var adSets = sdk.GetAdSets(fromDateString, tillDateString, true);
                int adSetsCount = adSets.Count;

                var ads = sdk.GetAds(fromDateString, tillDateString, true);
                int adsCount = ads.Count;

                CalculateMaxGenderProportion(dateAge, tempStorage);

                decimal calculatedPercentage = CalculatePercentage(ageGender, out var femalePercent);
                tempStorage.Gender = new GrowthHeroGenderModel(Math.Round(calculatedPercentage, 2), Math.Round(femalePercent));

                //}
                //catch (Exception ex)
                //{
                //    hero.Status = -1;
                //    Errors.Add(new StoreyaError(hero.ShopID, ex.Message));
                //}

                //try
                //{
                InsightsViewModel model = GetModel(provider, id, fromDateString, tillDateString, datePreset);

                var totalAccountBudget = CalculateTotalBudget(model);

                foreach (var row in model.Loosers)
                {
                    if (!string.IsNullOrEmpty(row.AdId))
                    {
                        row.AdDetails = sdk.GetAdDetailsById(row.AdId);
                    }
                }

                //  var loosersFinal = PrepareLoosersSection(model, out var otherLoosers);

                //foreach (GrowthHeroLoosers loosers in loosersFinal)
                //    foreach (BiDataRow item in loosers.Items)
                //        item.EnergySpend = Math.Round(item.EnergySpend, 0);

                PrepareDbObject(tbFacebookAdsSetup, heroMissingOpportunitiesModel, totalAccountBudget, model, tempStorage, null, null, campaignsCount, adSetsCount, adsCount);
                Log4NetLogger.Info("FB RunAnalysis Done For Shop:", shopID);
                tbFacebookAdsSetup.ProgressStatus = (int)PROGRESS_STATUS.ANALYSIS_DONE;
                Log4NetLogger.Info(string.Format("RunAnalysis TbFacebookAdsSetup for shopID: {0} new status {1}", shopID, ((PROGRESS_STATUS)tbFacebookAdsSetup.ProgressStatus).ToString()), shopID);
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                tbFacebookAdsSetup.ProgressStatus = (int)PROGRESS_STATUS.ERROR;
                Log4NetLogger.Error("FB RunAnalysis Faild For Shop:" + shopID, ex);
                errors = ex.ToString();
                tbFacebookAdsSetup.Description = errors;
                db.SaveChanges();
                return false;
            }
        }
        public static TbFacebookAdsSetup UpdateError(int shopId, string error, bool append = false)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                TbFacebookAdsSetup tbFacebookAds = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
                if (tbFacebookAds != null)
                {
                    if (append)
                    {
                        tbFacebookAds.Errors = string.Format("{0}, {1}", tbFacebookAds.Errors, error.SubString2(0, 4000));
                    }
                    else
                    {
                        tbFacebookAds.Errors = error.SubString2(0, 4000);
                    }

                    tbFacebookAds.LastCheckedAt = DateTime.Now;
                    db.SaveChanges();
                }
                Log4NetLogger.Info(string.Format("UpdateError TbFacebookAdsSetup for shopID: {0}", shopId), shopId);
                return tbFacebookAds;

            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to UpdateError TbFacebookAdsSetup for shopID: {0} status {1}", shopId, error), ex, shopId);
                return null;
            }

        }
        public class PageResult { public string PageId { get; set; } public bool Removed { get; set; } public string Error { get; set; } }
        public static void DeletePagesFromBM(string filePath)
        {
            try
            {
                List<PageResult> results = new List<PageResult>();

                string adminToken = Models.FbAds.FbAdsBusinesses.GetAdminToken(1);
                FbAdsSDK sdk = new FbAdsSDK(adminToken, false);
                sdk.BusinessID = Convert.ToInt64(Helpers.ConfigHelper.GetValue("FacebookAPI_BusinessID", "759278250814092"));
                List<string> pages = System.IO.File.ReadAllLines(filePath).ToList();
                int count = pages.Count;
                Console.WriteLine($"Total:{count}");
                pages.ForEach(p =>
                {
                    try
                    {
                        bool res = sdk.DeletePageFromBM(long.Parse(p), out string error);
                        results.Add(new PageResult()
                        {
                            Error = error,
                            PageId = p,
                            Removed = res
                        });
                        ConsoleAppHelper.WriteLog($"{count--} : {p} - {res} - {error}");
                    }
                    catch (Exception ex)
                    {
                        ConsoleAppHelper.WriteError($"{count--} : {p} - {ex.Message}", ex);
                    }
                });
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, "List Of FB pages removed from FB BM.", results.ListToHtmlTable(), null, results.ToCSVAttatchment("Results.csv"));

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception:{ex}");
            }
        }

        public static ScheduleAds GetScheduleAds(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            TbFacebookAdsSetup tbFacebookAdsSetup = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
            if (tbFacebookAdsSetup != null && !string.IsNullOrEmpty(tbFacebookAdsSetup.ScheduleAds))
            {
                return tbFacebookAdsSetup.ScheduleAds.FromJson<ScheduleAds>();
            }
            ScheduleAds scheduleAds = new ScheduleAds()
            {
                ShopId = shopId,
                Ads = new List<ScheduleAd>()
            };
            return scheduleAds;
        }

        public static ScheduleAds SetScheduleAds(int shopId, ScheduleAds scheduleAds)
        {
            var db = DataHelper.GetStoreYaEntities();
            TbFacebookAdsSetup tbFacebookAdsSetup = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
            tbFacebookAdsSetup.ScheduleAds = scheduleAds.ToJson();
            tbFacebookAdsSetup.UpdatedAt = DateTime.Now;
            db.SaveChanges();
            return scheduleAds;
        }
        public static ScheduleAds ManageScheduleAds(int shopId, string prefix, string text, DateTime startAdsAt, DateTime? endAdsAt = null, string headLIne = null, string text1 = null, SCHEDULEAD_STATUS? status = SCHEDULEAD_STATUS.CREATED)
        {
            if (!status.HasValue) {
                status = SCHEDULEAD_STATUS.CREATED;
            }
            prefix = prefix.ToUpper();
            var db = DataHelper.GetStoreYaEntities();
            TbFacebookAdsSetup tbFacebookAdsSetup = db.TbFacebookAdsSetups.Where(c => c.ShopID == shopId).SingleOrDefault();
            ScheduleAds scheduleAds = new ScheduleAds()
            {
                ShopId = shopId,
                Ads = new List<ScheduleAd>()
            };

            if (tbFacebookAdsSetup != null && !string.IsNullOrEmpty(tbFacebookAdsSetup.ScheduleAds))
            {
                scheduleAds = tbFacebookAdsSetup.ScheduleAds.FromJson<ScheduleAds>();
            }
            ScheduleAd ad = scheduleAds.Ads.SingleOrDefault(a => a.Prefix.ToUpper() == prefix);
            if (ad == null)
            {
                scheduleAds.Ads.Add(new ScheduleAd
                {
                    Prefix = prefix,
                    EndAdsAt = endAdsAt,
                    HeadLine = headLIne,
                    StartAdsAt = startAdsAt,
                    Text = text,
                    Text1 = text1,
                    UpdateAt = DateTime.Now,
                    Status = status.Value,
                });
            }
            else
            {              
                ad.StartAdsAt = startAdsAt;
                ad.EndAdsAt = endAdsAt;
                ad.HeadLine = headLIne;
                ad.Text = text;
                ad.Text1 = text1;
                ad.UpdateAt = DateTime.Now;
                ad.Status = status.Value;
            }
            tbFacebookAdsSetup.ScheduleAds = scheduleAds.ToJson();
            db.SaveChanges();
            return scheduleAds;

        }

        public class ScheduleAds
        {
            public int ShopId { get; set; }
            public List<ScheduleAd> Ads { get; set; }
        }
        public class ScheduleAd
        {
            public string Prefix { get; set; }
            public string Text { get; set; }
            public string Text1 { get; set; }
            public string HeadLine { get; set; }
            public DateTime? StartAdsAt { get; set; }
            public DateTime? EndAdsAt { get; set; }
            public DateTime UpdateAt { get; set; }
            public SCHEDULEAD_STATUS Status { get; set; }
        }
    }
}
