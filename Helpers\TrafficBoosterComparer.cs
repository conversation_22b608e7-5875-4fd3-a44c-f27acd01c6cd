﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class TrafficBoosterComparer : IEqualityComparer<TrafficBooster>
    {
        public bool Equals(TrafficBooster x, TrafficBooster y)
        {
            if (Object.ReferenceEquals(x, y)) return true;
            if (x == null || y == null) return false;
            return x.ID == y.ID;
        }

        public int GetHashCode(TrafficBooster obj)
        {
            return obj.ShopID.GetHashCode();
        }
    }
}
