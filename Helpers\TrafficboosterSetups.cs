﻿using Storeya.Core.Entities;
using Storeya.Core.Models.GA;
using Storeya.Core.Models.ShopAttributes;
using Storeya.Core.Models.Shopify;
using Storeya.Core.Models.TrafficBoosterModels.TrafficChannels;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using static Storeya.Core.Models.DashboardProDataModel;
using Storeya.Core.Models.AdWords;
using System.Windows.Forms;

namespace Storeya.Core.Helpers
{
    public class TrafficBoosterSetups
    {
        private StoreYaEntities db { get; set; }
        public bool IgnoreSmallShop { get; set; }
        private Shop CurrentShop { get; set; }
        private User CurrentUser { get; set; }
        public string Page_1 { get; set; }
        public string Page_2 { get; set; }
        public string Page_3 { get; set; }
        public string Page_4 { get; set; }
        public decimal PurchasedAmountInUSD { get; set; }
        private TrafficBooster Settings { get; set; }
        public ShopAttributesManager ShopAttributesManager { get; internal set; }
        public TrafficBoosterSetups(Shop shop, User user, TrafficBooster trafficBooster, ShopAttributesManager shopAttr = null, bool ignoreSmallShops = true, bool forcePixelPopup = false)
        {
            this.ForcePixelPopup = forcePixelPopup;
            this.IgnoreSmallShop = ignoreSmallShops;
            CurrentShop = shop;
            CurrentUser = user;
            if (shopAttr != null)
            {
                ShopAttributesManager = shopAttr;
            }
            else
            {
                ShopAttributesManager = new Storeya.Core.Models.ShopAttributes.ShopAttributesManager(CurrentShop.ID);
            }
            db = DataHelper.GetStoreYaEntities();
            if (trafficBooster != null)
            {
                Settings = trafficBooster;
                Page_1 = Settings.Url1;
                Page_2 = Settings.Url2;
                Page_3 = Settings.Url3;
                Page_4 = Settings.Url4;

                PurchasedAmountInUSD = TbSettingsHelper.GetBillingAmount_IgnoreOverwriteAmount(trafficBooster.PurchasedAmount == null ? 0 : trafficBooster.PurchasedAmount.Value, trafficBooster.PaymentSystem);
                SetGaSettings();
                if (this.Settings.AdWordsPixel != null)
                {
                    this.ConversionId = $"AW-{ShopifyThemesManager.ExtractGoogleConversionID(this.Settings.AdWordsPixel, out string lab)}";
                }
            }
            this.ShopifyConnnected = db.ShopifyConnectedShops.SingleOrDefault(s => s.ShopID == this.CurrentShop.ID && s.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster);
            if (this.ShopifyConnnected != null)
            {
                this.ShopifyShopUrl = this.ShopifyConnnected.ShopifyShopName;

            }
            this.ExistingStats = db.GAConnectedAccountsStats.SingleOrDefault(s => s.ShopID == this.CurrentShop.ID);
            if (this.CurrentShop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi)
            {
                SetShopifySettings();
                SetShoppingAlert();
                //SetRemarketingAlerts();
            }
        }

        private void SetGaSettings()
        {
            if (GaManager.GetActiveProfileInfo(CurrentShop.ID) != null)
            {
                HasConnectedGaAccount = true;
            }
            else
            {
                HasConnectedGaAccount = false;
            }


        }
        public bool HasNotConnectedGaAccountAlert
        {
            get
            {
                if (HasConnectedGaAccount)
                {
                    return false;
                }

                //not connected
                if (this.IsMarketplace && !this.Page_1.ToLower().Contains(".myshopify.com"))
                {
                    //ignore for marketplaces
                    return false;
                }
                else if (this.ShopAttributesManager != null && this.ShopAttributesManager.IsExists(Storeya.Core.Models.ShopAttributes.Attributes.Alerts.DontAskToConnectGA))
                {
                    return false;
                }
                else if (ConfigHelper.IsValueInList("DontAskToConnectGA", this.Settings.ShopID.ToString()))
                {
                    return false;
                }
                else
                {
                    return true;
                }

            }
        }
        public bool IsMarketplace
        {
            get
            {
                if (!string.IsNullOrEmpty(this.Page_1))
                {
                    if (MarketplaceUrlHelper.IsMarketplace(this.Page_1.ToLower()))
                        return true;
                }

                return false;
            }
        }
        public bool HasConnectedGaAccount { get; set; }
        public bool IsCancelledAlert
        {
            get
            {
                if (this.Settings.AppStatus.HasValue ? this.Settings.AppStatus == (int)TB_APP_STATUS.CANCELED : this.Settings.Status == (byte)TB_AW_STATUS.CANCELED)
                {
                    return true;
                }
                return false;
            }
        }
        public bool AdWordsPixelAlertShouldBeShown
        {
            get
            {
                if (this.ForcePixelPopup)
                {
                    return true;
                }
                else if (!this.IsMarketplace
                   && !string.IsNullOrEmpty(this.Settings.AdWordsAccount)
                   && !string.IsNullOrEmpty(this.Settings.AdWordsPixel)
                   && IsPixelIssueRelevantForUserNotification(this.Settings.AdWordsPixelInstalled))
                {
                    return true;
                }
                return false;
            }
        }
        public bool AskToInstallShopifyApp { get; private set; }
        public bool ShopifyAppInstalled { get; private set; }
        public bool CrossSellAppInstaled { get; private set; }
        public bool ShowShoppingAndDynamicRemarketingAlert { get; private set; }
        //public bool ShowRemarketingCodeAlert { get; private set; }
        public bool ShowCustomPixelAlert
        {
            get
            {
                if (this.CurrentShop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi
                    && AdWordsPixelAlertShouldBeShown)
                {
                    return true;
                }
                return false;
            }
        }
        public string ShopifyShopUrl { get; private set; }
        public string ConversionId { get; private set; }
        public ShopifyConnectedShop ShopifyConnnected { get; set; }
        public GAConnectedAccountsStat ExistingStats { get; private set; }
        public bool ForcePixelPopup { get; set; }
        public static bool IsPixelIssueRelevantForUserNotification(int? pixelStatus)
        {
            if (pixelStatus == null ||
                (pixelStatus != (int)AdWordsPixelStatuses.UserSaidInstalled
                   && pixelStatus != (int)AdWordsPixelStatuses.PixelDataNotFound
                   && pixelStatus != (int)AdWordsPixelStatuses.PixelReporting
                   && pixelStatus != (int)AdWordsPixelStatuses.GiveUp))
            {
                return true;
            }
            return false;
        }
        private void SetShopifySettings()
        {

            bool donTAskToInstall = this.ShopAttributesManager.IsExists(Storeya.Core.Models.ShopAttributes.Attributes.Alerts.DontAskToInstallShopifyApp);
            if (!donTAskToInstall && (this.ShopifyConnnected == null || this.ShopifyConnnected.PermissionsScope < 0))
            {
                bool upgradedInLast30Days = (this.Settings.FirstUpgradedAt.HasValue && this.Settings.FirstUpgradedAt.Value.AddMonths(1) >= DateTime.Now);
                if (upgradedInLast30Days)
                {
                    this.AskToInstallShopifyApp = true;
                }
                else
                {
                    var stats = db.GAConnectedAccountsStats.Where(s => s.ShopID == this.CurrentShop.ID).SingleOrDefault();
                    if (stats != null && stats.Transactions > 10)
                    {
                        this.AskToInstallShopifyApp = true;
                    }
                    else
                    {
                        if ((this.CurrentUser != null && this.CurrentUser.RevenueRank > 1000) || !IgnoreSmallShop)
                        {
                            this.AskToInstallShopifyApp = true;
                        }
                    }
                }
            }
            else
            {
                this.AskToInstallShopifyApp = false;
            }
            if (this.ShopifyConnnected != null && this.ShopifyConnnected.PermissionsScope > 0)
            {
                this.ShopifyAppInstalled = true;
                //if (this.ShopAttributesManager != null)
                //{
                //    this.CrossSellAppInstaled = this.ShopAttributesManager.IsExists(Attributes.Apps.TrafficBooster.ShopifyCrossSellAppExist);
                //}
            }
            else
            {
                this.ShopifyAppInstalled = false;
                //this.CrossSellAppInstaled = false;
            }

        }

        private void SetShoppingAlert()
        {
            if (!this.Settings.MerchantCenterAccountID.HasValue)
            {
                //if (this.ShopifyConnnected != null && this.ShopifyConnnected.PermissionsScope > 0)
                //{
                if ((this.ExistingStats != null && GoodAccountHelper.IsGoodRevenue(this.ExistingStats.Revenue ?? 0))
                    || PurchasedAmountInUSD > 354 || !this.IgnoreSmallShop) //revenue > 5000 or paid >=355
                {
                    ShowShoppingAndDynamicRemarketingAlert = true;
                }
                //}
            }
        }

        //private void SetRemarketingAlerts()
        //{
        //    if (!string.IsNullOrEmpty(this.Settings.AdWordsPixel) &&
        //        Settings.AdWordsRemarketingCodeStatus == AdWordsRemarketingCodeStatus.NotInstalled.GetHashCode())
        //    {
        //        if (this.ShopifyConnnected != null && this.ShopifyConnnected.PermissionsScope > 0)
        //        {
        //            this.ShowRemarketingCodeAlert = true;
        //        }
        //    }
        //}
        public static List<Age> GetAges()
        {
            List<Age> list = new List<Age>();
            list.Add(new Age() { Name = "18-24", Value = "18-24" });
            list.Add(new Age() { Name = "25-34", Value = "25-34" });
            list.Add(new Age() { Name = "35-44", Value = "35-44" });
            list.Add(new Age() { Name = "45-54", Value = "45-54" });
            list.Add(new Age() { Name = "55-64", Value = "55-64" });
            list.Add(new Age() { Name = "65 or more", Value = "65 or more" });
            return list;
        }

    }
    public class Age
    {
        public string Name { get; set; }
        public string Value { get; set; }
    }
}
