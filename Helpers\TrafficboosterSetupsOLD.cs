﻿using Storeya.Core.Entities;
using Storeya.Core.Models.GA;
using Storeya.Core.Models.ShopAttributes;
using Storeya.Core.Models.Shopify;
using Storeya.Core.Models.TrafficBoosterModels.TrafficChannels;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using static Storeya.Core.Models.DashboardProDataModel;
using Storeya.Core.Models.AdWords;

namespace Storeya.Core.Helpers
{
    public class TrafficBoosterSetups
    {
        private StoreYaEntities db { get; set; }
        private Shop CurrentShop { get; set; }
        private User CurrentUser { get; set; }
        public string Page_1 { get; set; }
        public string Page_2 { get; set; }
        public string Page_3 { get; set; }
        public string Page_4 { get; set; }
        private TrafficBooster Settings { get; set; }
        public ShopAttributesManager ShopAttributesManager { get; internal set; }
        public TrafficBoosterSetups(Shop shop, User user, TrafficBooster trafficBooster, ShopAttributesManager shopAttr = null)
        {
            CurrentShop = shop;
            CurrentUser = user;
            if (shopAttr != null)
            {
                ShopAttributesManager = shopAttr;
            }
            else
            {
                ShopAttributesManager = new Storeya.Core.Models.ShopAttributes.ShopAttributesManager(CurrentShop.ID);
            }
            db = DataHelper.GetStoreYaEntities();
            if (trafficBooster != null)
            {
                Settings = trafficBooster;
                Page_1 = Settings.Url1;
                Page_2 = Settings.Url2;
                Page_3 = Settings.Url3;
                Page_4 = Settings.Url4;
                SetGaSettings();
            }


            SetShopifySettings();
            SetShoppingAndRemarketingAlerts();
        }

        private void SetGaSettings()
        {
            if (GaManager.GetActiveProfileInfo(CurrentShop.ID) != null)
            {
                HasConnectedGaAccount = true;
            }
            else
            {
                HasConnectedGaAccount = false;
            }


        }
        public bool HasNotConnectedGaAccountAlert
        {
            get
            {
                if (HasConnectedGaAccount)
                {
                    return false;
                }

                //not connected
                if (this.IsMarketplace && !this.Page_1.ToLower().Contains(".myshopify.com"))
                {
                    //ignore for marketplaces
                    return false;
                }
                else if (this.ShopAttributesManager != null && this.ShopAttributesManager.IsExists(Storeya.Core.Models.ShopAttributes.Attributes.Alerts.DontAskToConnectGA))
                {
                    return false;
                }
                else if (ConfigHelper.IsValueInList("DontAskToConnectGA", this.Settings.ShopID.ToString()))
                {
                    return false;
                }
                else
                {
                    return true;
                }

            }
        }
        public bool IsMarketplace
        {
            get
            {
                if (!string.IsNullOrEmpty(this.Page_1))
                {
                    if (MarketplaceUrlHelper.IsMarketplace(this.Page_1.ToLower()))
                        return true;
                }

                return false;
            }
        }
        public bool HasConnectedGaAccount { get; set; }
        public bool IsCancelledAlert
        {
            get
            {
                if (this.Settings.AppStatus.HasValue ? this.Settings.AppStatus == (int)TB_APP_STATUS.CANCELED : this.Settings.Status == (byte)TB_AW_STATUS.CANCELED)
                {
                    return true;
                }
                return false;
            }
        }
        public bool AdWordsPixelAlertShouldBeShown
        {
            get
            {
                if (!this.IsMarketplace
                   && !string.IsNullOrEmpty(this.Settings.AdWordsAccount)
                   && !string.IsNullOrEmpty(this.Settings.AdWordsPixel)
                   && this.Settings.AdWordsPixelInstalled != (int)AdWordsPixelStatuses.UserSaidInstalled
                   && this.Settings.AdWordsPixelInstalled != (int)AdWordsPixelStatuses.PixelReporting
                   && this.Settings.AdWordsPixelInstalled != (int)AdWordsPixelStatuses.GiveUp)
                {
                    return true;
                }
                return false;
            }
        }
        public bool AskToInstallShopifyApp { get; private set; }
        public bool ShopifyAppInstalled { get; private set; }
        public bool CrossSellAppInstaled { get; private set; }
        public bool ShowShoppingAndDynamicRemarketingAlert { get; private set; }
        public bool ShowRemarketingCodeAlert { get; private set; }
        public bool ShowCustomPixelAlert
        {
            get
            {
<<<<<<< .mine
                if (this.CurrentShop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi && HasNotAdWordsPixelInstalledShopifyAlert
                    && this.Settings.AdWordsPixelInstalled != (int)AdWordsPixelStatuses.UserSaidInstalled)
||||||| .r38436
                if (this.CurrentShop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi && HasNotAdWordsPixelInstalledShopifyAlert
                    && this.Settings.AdWordsPixelInstalled != (int)AdWordsPixelStatuses.UserSaidInstalled && this.Settings.AdWordsRemarketingCodeStatus != (int)AdWordsRemarketingCodeStatus.UserSaidInstalled)
=======
                if (this.CurrentShop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi 
                    && AdWordsPixelAlertShouldBeShown)
>>>>>>> .r38446
                {
                    return true;
                }
                return false;
            }
        }
        public string ShopifyShopUrl { get; private set; }
        public string ConversionId { get; private set; }

        private void SetShopifySettings()
        {
            if (this.CurrentShop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi)
            {
                ShopifyConnectedShop shopifyConnectedShop = db.ShopifyConnectedShops.Where(s => s.ShopID == this.CurrentShop.ID && s.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster).SingleOrDefault();
                bool donTAskToInstall = this.ShopAttributesManager.IsExists(Storeya.Core.Models.ShopAttributes.Attributes.Alerts.DontAskToInstallShopifyApp);
                if (!donTAskToInstall && (shopifyConnectedShop == null || shopifyConnectedShop.PermissionsScope < 0))
                {
                    bool upgradedInLast30Days = (this.Settings.FirstUpgradedAt.HasValue && this.Settings.FirstUpgradedAt.Value.AddMonths(1) >= DateTime.Now);
                    if (upgradedInLast30Days)
                    {
                        this.AskToInstallShopifyApp = true;
                    }
                    else
                    {
                        var stats = db.GAConnectedAccountsStats.Where(s => s.ShopID == this.CurrentShop.ID).SingleOrDefault();
                        if (stats != null && stats.Transactions > 10)
                        {
                            this.AskToInstallShopifyApp = true;
                        }
                        else
                        {
                            if (this.CurrentUser != null && this.CurrentUser.RevenueRank > 1000)
                            {
                                this.AskToInstallShopifyApp = true;
                            }
                        }
                    }
                }
                else
                {
                    this.AskToInstallShopifyApp = false;
                }
                if (shopifyConnectedShop != null && shopifyConnectedShop.PermissionsScope > 0)
                {
                    this.ShopifyAppInstalled = true;
                    if (this.ShopAttributesManager != null)
                    {
                        this.CrossSellAppInstaled = this.ShopAttributesManager.IsExists(Attributes.Apps.TrafficBooster.ShopifyCrossSellAppExist);
                    }
                }
                else
                {
                    this.ShopifyAppInstalled = false;
                    this.CrossSellAppInstaled = false;
                }
            }
        }

        private void SetShoppingAndRemarketingAlerts()
        {
            if (!this.Settings.MerchantCenterAccountID.HasValue || (this.Settings.AdWordsRemarketingCodeStatus == Storeya.Core.Models.AdWordsRemarketingCodeStatus.NotInstalled.GetHashCode()))
            {
                if (this.Settings.PurchasedAt > DateTime.Parse("2019-12-15")) //Only New Shops 
                {
                    if (!db.TbCampaigns.Any(x => x.ShopID == this.CurrentShop.ID && x.CampaignType == (int)TbCampaignTypes.Shopping)) //Dont Have Shopping Campaign
                    {
                        if (this.CurrentShop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi) //Has Shopify App installed
                        {
                            var shopifyConnnected = db.ShopifyConnectedShops.FirstOrDefault(s => s.ShopID == this.CurrentShop.ID && s.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster && s.PermissionsScope > 0);
                            if (shopifyConnnected != null)
                            {
                                GAConnectedAccountsStat existingStats = db.GAConnectedAccountsStats.SingleOrDefault(s => s.ShopID == this.CurrentShop.ID);
                                if ((existingStats != null && existingStats.Revenue > 5000) || (this.Settings.PurchasedAmount > 354) && !this.Settings.MerchantCenterAccountID.HasValue) //revenue > 10000 or paid >=355
                                {
                                    this.ShowShoppingAndDynamicRemarketingAlert = true;
                                }
                                if (!string.IsNullOrEmpty(this.Settings.AdWordsPixel) &&
                                    this.Settings.AdWordsRemarketingCodeStatus != Storeya.Core.Models.AdWordsRemarketingCodeStatus.Installed.GetHashCode()
                                    && this.Settings.AdWordsRemarketingCodeStatus != Storeya.Core.Models.AdWordsRemarketingCodeStatus.UserSaidInstalled.GetHashCode()
                                    && this.Settings.AdWordsRemarketingCodeStatus != Storeya.Core.Models.AdWordsRemarketingCodeStatus.GiveUp.GetHashCode())
                                {
                                    this.ShowRemarketingCodeAlert = true;
                                    this.ShopifyShopUrl = shopifyConnnected.ShopifyShopName;
                                    this.ConversionId = $"AW-{ShopifyThemesManager.ExtractGoogleConversionID(this.Settings.AdWordsPixel, out string lab)}";
                                }


                            }
                        }
                    }
                }
            }
        }

    }
}
