﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;
using System.Resources;
using System.Threading;
using System.Web;
using System.Web.UI.WebControls;
using static Storeya.Core.Models.GA.GA4Helper;
using Storeya.Core.Models.DataProviders.ShopifyEntities;
using Amazon.Runtime.Internal.Transform;

namespace Storeya.Core.Helpers
{
    public class TranslationHelper
    {
        public enum PagesType
        {
            TbSettings,
            Checkout,
            TbDashboard,
            Layout
        }
        private readonly ResourceManager resourceManager;
        private readonly CultureInfo culture;
        private readonly PagesType pageType;
        public string LanguageCode { get; private set; }
        public bool SupportMultiLanguage { get; private set; }

        public List<string> SupportedLanguages { get; private set; }
        public TranslationHelper(PagesType page, string locale = null)
        {
            SupportMultiLanguage = false;
            SupportedLanguages = ConfigHelper.GetValue("SupportedLanguages", "fr,es,en").Split(",".ToCharArray()).ToList();
            if (ConfigHelper.GetValue("SupportMultiLanguage", "False").ToLower() == "true")
            {
                SupportMultiLanguage = true;
            }
            //if (!SupportMultiLanguage)
            //{
            //    DeleteCookie("stry_locale");
            //    return;
            //}
            pageType = page;
            string twoLetterISOLanguageName = locale;
            if (string.IsNullOrEmpty(twoLetterISOLanguageName))
            {
                twoLetterISOLanguageName = GetCulture(null, null);
            }
            //string[] cultureNames = { "en-US", "fr-FR", "es-ES", "sv-SE" };            

            string nameSpace = $"Storeya.Core.Properties.Resource.{page}";
            resourceManager = new ResourceManager(nameSpace, typeof(TranslationHelper).Assembly);


            if (string.IsNullOrEmpty(twoLetterISOLanguageName))
            {
                twoLetterISOLanguageName = "en";
            }
            culture = CultureInfo.CreateSpecificCulture(twoLetterISOLanguageName);
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            LanguageCode = twoLetterISOLanguageName;
        }

        public static void SetToEnCulture()
        {
            CultureInfo culture = CultureInfo.CreateSpecificCulture("en");
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
        }
        public Dictionary<string, int> GetCategoriesForClientsEdit()
        {
            Dictionary<string, int> categories = new Dictionary<string, int>()
            {
                {GetString("String8"), 0 },
                {GetString("StringCategory1"), 501 },
                {GetString("StringCategory2"), 500 },
                {GetString("StringCategory16"), 666 },
                {GetString("StringCategory17"), 666 },
                {GetString("StringCategory3"), 502 },
                {GetString("StringCategory4"), 503 },
                {GetString("StringCategory18"), 516 },
                {GetString("StringCategory19"), 517 },
                {GetString("StringCategory20"), 518 },
                {GetString("StringCategory21"), 519 },
                {GetString("StringCategory22"), 520 },
                {GetString("StringCategory5"), 504 },
                {GetString("StringCategory6"), 505 },
                {GetString("StringCategory7"), 506 },
                {GetString("StringCategory23"), 515 },
                {GetString("StringCategory24"), 521 },
                {GetString("StringCategory25"), 522 },
                {GetString("StringCategory8"), 507 },
                {GetString("StringCategory9"), 508 },
                {GetString("StringCategory10"), 509 },
                {GetString("StringCategory11"), 510 },
                {GetString("StringCategory12"), 511 },
                {GetString("StringCategory13"), 512 },
                {GetString("StringCategory14"), 513 },
                {GetString("StringCategory15"), 514 }

            };
            return categories;
        }
        public string GetString(string keyName, string defaultValue = null)
        {
            try
            {
                string res = resourceManager.GetString(keyName, culture);
                Console.WriteLine("{0} {1} {2:M}.\n", keyName, res, DateTime.Now);
                return res;
            }
            catch (Exception ex)
            {
                if (string.IsNullOrEmpty(defaultValue))
                {
                    throw new Exception($"Check if {keyName} exists in {pageType} culture:{LanguageCode} ", ex);
                }
            }
            return defaultValue;
        }
        public static string GetCultureUrl(string url, User user, Shop shop)
        {
            if (HttpContext.Current?.Request?.RequestContext?.RouteData?.Values["culture"] != null)
            {
                return url;
            }
            string culture = GetCulture(user, shop);
            if (string.IsNullOrEmpty(culture))
            {
                return url;
            }
            if (Uri.IsWellFormedUriString(url, UriKind.Relative))
            {
                return $"/{culture}/{url}";
            }
            if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                Uri uri = new Uri(url);
                return $"{uri.Scheme}://{uri.Host}/{culture}/{uri.PathAndQuery}";
            }
            return url;
        }
        public static bool IsSupported(string culture)
        {
            List<string> supportedLanguages = ConfigHelper.GetValue("SupportedLanguages", "fr,es,en").Split(",".ToCharArray()).ToList();
            if (supportedLanguages.Count == 0)
            {
                return false;
            }
            if (string.IsNullOrEmpty(culture))
            {
                return false;
            }
            if (supportedLanguages.Contains(culture))
            {
                return true;
            }
            return false;
        }
        public static bool SetUserCulture(User user)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                string locale = GetCulture(user);
                var usr = db.Users.SingleOrDefault(u => u.ID == user.ID);
                if (usr.Locale != locale)
                {
                    usr.Locale = locale;
                    db.SaveChanges();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError($"Failed to updated user:{user.ID} locale", ex);
            }
            return false;
        }

        public static string GetCulture(User user, Shop shop = null)
        {
            try
            {
                string stryCookieValue = null;
                var Request = HttpContext.Current.Request;
                var stryCookie = Request.Cookies["stry_locale"];
                if (stryCookie != null)
                {
                    stryCookieValue = stryCookie.Value;
                }
                string locale = stryCookieValue;
                string urlCulture = null;
                if (Request.RequestContext.RouteData.Values["culture"] != null)
                {
                    urlCulture = Request.RequestContext.RouteData.Values["culture"].ToString();
                }
                if (urlCulture != null)
                {
                    if (locale != urlCulture)
                    {
                        locale = urlCulture;
                    }
                }
                if (string.IsNullOrEmpty(locale))
                {
                    if (user == null)
                    {
                        if (shop != null)
                        {
                            locale = shop.StoreLocale;
                        }
                    }
                    else
                    {
                        locale = user.Locale;
                    }
                }
                if (IsSupported(locale))
                {
                    if (stryCookie == null)
                    {
                        HttpCookie Cookie = new HttpCookie("stry_locale", locale);
                        Cookie.Expires = DateTime.Now.AddDays(365);
                        HttpContext.Current.Response.Cookies.Add(Cookie);
                        return locale;
                    }
                    if (urlCulture != null && stryCookieValue != urlCulture)
                    {
                        HttpCookie Cookie = new HttpCookie("stry_locale", urlCulture);
                        Cookie.Expires = DateTime.Now.AddDays(365);
                        HttpContext.Current.Response.Cookies.Add(Cookie);
                    }
                    return locale;
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError($"Failed to get locale", ex);
            }
            return null;
        }

        public static void DeleteCookie(string cookieName, string keyName = null, string domain = null)
        {
            //https://www.ryadel.com/en/asp-net-cookie-read-write-delete-c-sharp-tutorial-guide-how-to/
            try
            {
                if (HttpContext.Current.Request.Cookies[cookieName] != null)
                {
                    HttpCookie cookie = HttpContext.Current.Request.Cookies[cookieName];

                    // SameSite.None Cookies won't be accepted by Google Chrome and other modern browsers if they're not secure, which would lead in a "non-deletion" bug.
                    // in this specific scenario, we need to avoid emitting the SameSite attribute to ensure that the cookie will be deleted.
                    if (cookie.SameSite == SameSiteMode.None && !cookie.Secure)
                        cookie.SameSite = (SameSiteMode)(-1);

                    if (String.IsNullOrEmpty(keyName))
                    {
                        cookie.Expires = DateTime.UtcNow.AddYears(-1);
                        if (!String.IsNullOrEmpty(domain)) cookie.Domain = domain;
                        HttpContext.Current.Response.Cookies.Add(cookie);
                        HttpContext.Current.Request.Cookies.Remove(cookieName);
                    }
                    else
                    {
                        cookie.Values.Remove(keyName);
                        if (!String.IsNullOrEmpty(domain)) cookie.Domain = domain;
                        HttpContext.Current.Response.Cookies.Add(cookie);
                    }
                }
            }
            catch
            {


            }
        }
    }
}
