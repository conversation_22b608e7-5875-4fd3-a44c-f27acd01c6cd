﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Text.RegularExpressions;
using System.Net.Http;

namespace Storeya.Core.Helpers
{
    public class UrlPathHelper
    {
        public static string GetFullPath(string baseUrl, string path)
        {
            if (!string.IsNullOrEmpty(path))
                path = TextManipulationHelper.CleanFromSimpleEscapeSequenceCharacters(path);
            if (Uri.IsWellFormedUriString(path, UriKind.Absolute) || (path != null && path.StartsWith("http")))
            {
                return path;
            }
            return Combine(baseUrl, path);
        }

        public static string Combine(string firstPart, string secondPart)
        {
            if (string.IsNullOrEmpty(firstPart) || string.IsNullOrEmpty(secondPart))
            {
                return "";
            }
            return firstPart.TrimEnd('/') + "/" + secondPart.TrimStart('/');
        }

        public static string GetFullUrl(string relativePath)
        {
            string host = HttpContext.Current.Request.Url.Host;
            return UrlPathHelper.Combine(host, relativePath);
        }

        public static bool IsIP(string urlHost)
        {
            try
            {
                IPAddress address = IPAddress.Parse(urlHost);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static string BuildValidUri(string link)
        {
            if (!string.IsNullOrEmpty(link))
            {
                if (link.Contains(" "))
                    link = link.Replace(" ", "");

                if (link.Contains("http//"))
                    link = link.Replace("http//", "");

                if (link.Contains("http/"))
                    link = link.Replace("http/", "");

                if (!link.ToLower().StartsWith("http://") && !link.ToLower().StartsWith("https://"))
                {
                    return "http://" + link;
                }
            }
            return link;
        }
        public static string BuildValidHttpsUri(string link)
        {
            if (!string.IsNullOrEmpty(link))
            {
                if (link.Contains(" "))
                    link = link.Replace(" ", "");

                if (link.Contains("http//"))
                    link = link.Replace("http//", "");

                if (link.Contains("http/"))
                    link = link.Replace("http/", "");

                if (!link.ToLower().StartsWith("http://") && !link.ToLower().StartsWith("https://"))
                {
                    return "https://" + link;
                }
            }
            return link;
        }
        public static string GetParameters(string path)
        {
            if (path.Contains("?"))
            {
                return "?" + path.Split('?')[1];
            }
            return string.Empty;
        }
        public static string AddParameter(string path, string param, bool ifNotExists = false)
        {
            if (ifNotExists)
            {
                if (!path.Contains(param))
                {
                    if (path.Contains("?"))
                    {
                        return path + "&" + param;
                    }
                    else
                    {
                        return path + "?" + param;
                    }
                }
                return path;
            }
            else
            {
                if (path.Contains("?"))
                {
                    return path + "&" + param;
                }
                else
                {
                    return path + "?" + param;
                }
            }

        }

        public static string GetDomainName(string url)
        {
            if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                Uri uri = new Uri(url);
                var host = uri.Host;
                if (!host.StartsWith("www."))
                {
                    host = "www." + host;
                }
                url = host;
            }
            else
            {
                url = GetCleanHostName(url);
            }
            if (!string.IsNullOrEmpty(url) && url.EndsWith("/"))
            {
                url = url.TrimEnd('/');
            }
            return url;
        }

        public static string GetHostName(string url)
        {
            if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                Uri uri = new Uri(url);
                var host = uri.Host;
                return host;
            }

            return url + "_BAD_FORMATTED";
        }
        public static string GetServerName(string url)
        {
            if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                Uri uri = new Uri(url);
                string host = uri.Scheme + Uri.SchemeDelimiter + uri.Host;
                return host;
            }

            return url + "_BAD_FORMATTED";
        }

        public static string GetCleanHostName(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return null;
            }

            url = url.ToLower();
            if (url.Contains("https://"))
            {
                url = url.Replace("https://", "");
            }
            if (url.Contains("http://"))
            {
                url = url.Replace("http://", "");
            }

            if (url.Contains("?"))
            {
                int index = url.IndexOf("?");
                url = url.Remove(index);
            }
            url = url.Replace("www.", "");
            return url;
        }

        public static string GetOgUrl(string url)
        {
            try
            {
                HttpWebResponse response = HttpRequestResponseHelper.GetHttpWebResponse(url, true);
                if (response != null && response.StatusCode == HttpStatusCode.OK)
                {
                    string content = HttpRequestResponseHelper.GetContent(response);
                    //<meta property="og:url" content="https://www.xn--prbu-5na7e.com/"/>
                    string extracted = RegexHelper.GetFirstGroupValue(content, @"<meta property=""og:url"" content=""?(.*?)""/>");
                    if (!string.IsNullOrEmpty(extracted) && Uri.IsWellFormedUriString(extracted, UriKind.Absolute))
                    {
                        return extracted;
                    }
                }

            }
            catch (Exception)
            {

            }
            return null;
        }

        public static string GetCanonicalUrl(string url)
        {
            try
            {
                HttpWebResponse response = HttpRequestResponseHelper.GetHttpWebResponse(url, true);
                if (response != null && response.StatusCode == HttpStatusCode.OK)
                {
                    Uri responseUri = response.ResponseUri;
                    string absoluteUri = responseUri.AbsoluteUri;

                    if (!string.IsNullOrEmpty(absoluteUri) && absoluteUri.TrimEnd('/') == url.TrimEnd('/'))
                    {
                        string content = HttpRequestResponseHelper.GetContent(response);
                        string extracted = RegexHelper.GetFirstGroupValue(content, @"<link rel=""canonical"" href=""?(.*?)"" />");
                        if (!string.IsNullOrEmpty(extracted) && Uri.IsWellFormedUriString(extracted, UriKind.Absolute))
                        {
                            return extracted;
                        }

                        extracted = RegexHelper.GetFirstGroupValue(content, @"<link rel=""canonical"" href=""?(.*?)""");
                        if (!string.IsNullOrEmpty(extracted) && Uri.IsWellFormedUriString(extracted, UriKind.Absolute))
                        {
                            return extracted;
                        }

                        extracted = RegexHelper.GetFirstGroupValue(content, @"<link rel='canonical' href='?(.*?)' />");
                        if (!string.IsNullOrEmpty(extracted) && Uri.IsWellFormedUriString(extracted, UriKind.Absolute))
                        {
                            return extracted;
                        }

                        extracted = RegexHelper.GetFirstGroupValue(content, @"<link rel='canonical' href='?(.*?)'");
                        if (!string.IsNullOrEmpty(extracted) && Uri.IsWellFormedUriString(extracted, UriKind.Absolute))
                        {
                            return extracted;
                        }
                    }

                    return absoluteUri;
                }
            }
            catch (Exception)
            {

            }

            return null;
        }
        public static string ReplaceUrlParameter(string url, string key, string value)
        {
            return Regex.Replace(
                url,
                @"([?&]" + key + ")=[^?&]+",
                "$1=" + value);
        }

        public static string GetUrlParam(string uri, string param)
        {
            Dictionary<string, string> dic = GetUrlParams(uri);
            param = param.ToLower();
            if (dic.ContainsKey(param))
            {
                return dic[param];
            }
            return null;
        }

        public static Dictionary<string, string> GetUrlParams(string uri)
        {
            uri = uri.ToLower();
            var matches = Regex.Matches(uri, @"[\?&](([^&=]+)=([^&=#]*))", RegexOptions.Compiled);
            var keyValues = new Dictionary<string, string>(matches.Count);
            foreach (Match m in matches)
            {
                if (!keyValues.ContainsKey(Uri.UnescapeDataString(m.Groups[2].Value)))
                {
                    keyValues.Add(Uri.UnescapeDataString(m.Groups[2].Value), Uri.UnescapeDataString(m.Groups[3].Value));
                }
            }

            return keyValues;
        }

        public static string GetUrlWithoutQueryString(string url)
        {
            var uri = new Uri(url);
            return uri.GetLeftPart(UriPartial.Path);
        }


        public static string CareAboutWwwOrNot(string domain)
        {
            try
            {
                if (domain != null)
                    domain = domain.Replace("www.", "");

                string withWww = $"https://www.{domain}";
                string withoutWww = $"https://{domain}";

                if (DomainExists(withWww))
                {
                    return withWww.Replace("https://", "");
                }
                else if (DomainExists(withoutWww))
                {
                    return withoutWww.Replace("https://", "");
                }
                else
                {
                    //return what it was
                    return domain;
                }
            }
            catch (Exception)
            {

                return domain;
            }
        }

        private static bool DomainExists(string domain)
        {
            WebClient client = new WebClient();
            try
            {
                client.DownloadString(domain);
            }
            catch
            {
                return false;
            }

            return true;
        }
    }
}