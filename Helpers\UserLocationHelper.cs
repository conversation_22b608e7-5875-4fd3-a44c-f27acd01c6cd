﻿using RestSharp.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class UserLocationHelper
    {
        public static string GetState(User user)
        {
            var state = user.SignupState;
            return state?.Trim();
        }
        public static string GetZipCode(User user)
        {
            var zip = user.ZipCode;
            return zip?.Trim();
        }

        public static string GetCountry(User user)
        {
            var state = user.SignupCountry;
            return state;
        }

        public static string GetBillingCountry(User user)
        {
            if (!string.IsNullOrEmpty(user.BillingCountry))
            {
                return user.BillingCountry;
            }
            
            return user.SignupCountry;
        }
    }
}
