﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace Storeya.Core.Helpers
{


    public class VerificationCodeGenerator
    {
        public static int CodeRefreshIntervalInSeconds { get; set; } = 60;
        public static int ExpirationTimeMinutes { get; set; } = 10;

        private static readonly string SecretKey = ConfigHelper.GetValue("VerificationCodeSecretKey", "Wv6i7Xqyk1DSnBraPVjGzlYw5");

        // Method to generate a 6-digit verification code based on user ID and current time
        public static string GenerateVerificationCode(int userId, out DateTime expirationTime)
        {
            if (userId.ToString().Length > 6)
            {
                throw new ArgumentException("User ID must be a 6-digit number.");
            }
            expirationTime = DateTime.Now.AddMinutes(ExpirationTimeMinutes);
            // Get the current timestamp rounded to the nearest 10 minutes
            long timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds() / CodeRefreshIntervalInSeconds;

            // Combine userId and timestamp
            string data = $"{userId}{timestamp}";

            // Generate a HMACSHA256 hash of the data with the secret key
            using (HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(SecretKey)))
            {
                byte[] hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
                int hashInt = BitConverter.ToInt32(hashBytes, 0);

                // Generate a 6-digit code using modulo operation
                int verificationCode = Math.Abs(hashInt % 1000000);

                return verificationCode.ToString("D6");
            }
        }

        // Method to validate a verification code
        public static bool ValidateVerificationCode(int userId, string verificationCode, out string error)
        {
            error = null;
            if (verificationCode.Length != 6 || !int.TryParse(verificationCode, out int code))
            {
                error = "The verification code is in an incorrect format.";
                return false;
            }
            // Get the current timestamp rounded to the nearest 10 minutes
            long currentTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds() / CodeRefreshIntervalInSeconds;
            for (int i = 0; i <= ExpirationTimeMinutes; i++)
            {
                long timestamp = currentTimestamp - i;
                string data = $"{userId}{timestamp}";
                // Generate the expected code
                using (HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(SecretKey)))
                {
                    byte[] hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
                    int hashInt = BitConverter.ToInt32(hashBytes, 0);
                    int expectedCode = Math.Abs(hashInt % 1000000);
                    if (expectedCode.ToString("D6") == verificationCode)
                    {
                        return true;
                    }
                }
            }
            error = "The verification code is invalid or has expired.";
            return false;
        }

        //public static void Main(string[] args)
        //{
        //    // Example usage
        //    int userId = 123456; // Example 6-digit user ID
        //    string verificationCode = GenerateVerificationCode(userId);
        //    Console.WriteLine($"Generated Verification Code for User {userId}: {verificationCode}");

        //    // Validate the verification code
        //    bool isValid = ValidateVerificationCode(userId, verificationCode);
        //    Console.WriteLine($"Is the Verification Code valid? {isValid}");

        //    // Simulate waiting for more than 10 minutes
        //    System.Threading.Thread.Sleep(2000); // Sleep for 2 seconds for demonstration

        //    // Check if the code is expired
        //    isValid = ValidateVerificationCode(userId, verificationCode);
        //    Console.WriteLine($"Is the Verification Code valid after waiting? {isValid}");
        //}
    }

}
