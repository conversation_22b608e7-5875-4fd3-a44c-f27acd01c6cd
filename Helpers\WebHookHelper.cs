﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Storeya.Core.Helpers
{
    public static class WebHookHelper
    {
        public async static Task<string> GetBodyContentAsStringAsync(HttpRequestBase request)
        {
            string content = string.Empty;

            using (Stream receiveStream = request.InputStream)
            {
                using (StreamReader readStream = new StreamReader(receiveStream, request.ContentEncoding))
                {
                    content = await readStream.ReadToEndAsync();
                }
            }           

            return content;
        }

        public static string GetBodyContentAsString(HttpRequestBase request)
        {
            string content = string.Empty;

            using (Stream receiveStream = request.InputStream)
            {
                using (StreamReader readStream = new StreamReader(receiveStream, request.ContentEncoding))
                {
                    content = readStream.ReadToEnd();
                }
            }

            return content;
        }


        public static string GetRequestBody(System.Web.HttpRequestBase request)
        {
            request.InputStream.Position = 0;
            string content = null;
            //if (HttpContext.Request.InputStream.Length > int.MaxValue)
            //    throw new ArgumentException("HTTP InputStream too large.");

            int streamLength = Convert.ToInt32(request.InputStream.Length);
            byte[] byteArray = new byte[streamLength];
            const int startAt = 0;

            request.InputStream.Read(byteArray, startAt, streamLength);
            request.InputStream.Seek(0, SeekOrigin.Begin);

            content = request.ContentEncoding.GetString(byteArray);
            return content;
        }

        public static string GetContent(System.Web.HttpRequestBase request, Storeya.Core.Helpers.Log4NetLogger.SpecialShopIDs logUnder)
        {
            string json_data = null;
            try
            {
                Stream inputStream = request.InputStream;

                string encodingType = request.ContentEncoding.EncodingName;

                if (encodingType.ToLower().Contains("gzip"))
                    inputStream = new GZipStream(inputStream, CompressionMode.Decompress);

                //Encoding enc = System.Text.Encoding.GetEncoding("UTF-8");
                using (StreamReader streamReader = new StreamReader(inputStream, request.ContentEncoding))
                {
                    json_data = streamReader.ReadToEnd();
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Failed to parse json payload.", ex, (int)logUnder);
            }
            return json_data;
        }

        public static Dictionary<string, string> GetHeadersData(HttpRequestBase request)
        {
            Dictionary<string, string> headers_data = new Dictionary<string, string>();
            foreach (string key in request.Headers.Keys)
            {
                headers_data.Add(key, request.Headers[key]);
            }
            return headers_data;
        }

        public static Dictionary<string, string> GetQueryStringData(HttpRequestBase request)
        {
            Dictionary<string, string> queryString_data = new Dictionary<string, string>();
            foreach (string key in request.QueryString.Keys)
            {
                queryString_data.Add(key, request.QueryString[key]);
            }
            return queryString_data;
        }

        public static Dictionary<string, string> GetFormData(HttpRequestBase request)
        {
            Dictionary<string, string> form_data = new Dictionary<string, string>();
            foreach (string key in request.Form.Keys)
            {
                form_data.Add(key, request.Form[key]);
            }
            return form_data;
        }



        public static void LogWebhook(HttpRequestBase request, Storeya.Core.Helpers.Log4NetLogger.SpecialShopIDs logUnder)
        {
            var values = GetWebhookString(request);

            Log4NetLogger.Info("Webhook: " + values, (int)logUnder);
        }

        public static string GetWebhookString(HttpRequestBase request)
        {
            StringBuilder builder = new StringBuilder();
            builder.AppendLine("Headers values:");
            foreach (string key in request.Headers.Keys)
            {
                builder.Append("  ").Append(key).Append(" = ").AppendLine(request.Headers[key]);
            }

            builder.AppendLine("QueryString values:");
            foreach (string key in request.QueryString.Keys)
            {
                builder.Append("  ").Append(key).Append(" = ").AppendLine(request.QueryString[key]);
            }

            builder.AppendLine("Form values:");
            foreach (string key in request.Form.Keys)
            {
                builder.Append("  ").Append(key).Append(" = ").AppendLine(request.Form[key]);
            }

            builder.AppendLine("Content: ");
            string content = GetRequestBody(request);
            builder.Append(content);

            string values = builder.ToString();
            return values;
        }
    }
}
