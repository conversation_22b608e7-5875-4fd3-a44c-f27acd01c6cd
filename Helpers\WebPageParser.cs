﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Storeya.Core.Helpers
{
    public class WebPageParser
    {
        public string GetImagesFromWebPageJson(string pageUrl)
        {
            List<ImagesFromWebPage> model = GetImages(pageUrl);
            string json = Newtonsoft.Json.JsonConvert.SerializeObject(model);
            return json;
        }

        private List<ImagesFromWebPage> GetImages(string pageUrl)
        {
            List<ImagesFromWebPage> images = new List<ImagesFromWebPage>();
            string content = HttpRequestResponseHelper.GetHttpWebResponseWithoutTryCatch(pageUrl);
            if (!string.IsNullOrEmpty(content))
            {
                Regex regxForImageTag = new Regex("(?<=<img.*?src=\")[^\"]*", RegexOptions.IgnoreCase);
                foreach (Match item in regxForImageTag.Matches(content))
                {
                    if (!images.Where(i => i.src == item.Value).Any())
                        images.Add(new ImagesFromWebPage() { src = item.Value });
                }

            }
            return images;
        }
    }

    public class ImagesFromWebPage
    {
        public string src { get; set; }
    }
}
