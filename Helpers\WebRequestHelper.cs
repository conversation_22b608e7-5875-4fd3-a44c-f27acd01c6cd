﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.IO;
using Storeya.Core.Models.DataProviders;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class WebRequestHelper
    {
        public static string GetResponse(string url, string user, string pass)
        {
            try
            {
                using (WebClient client = new WebClient())
                {
                    client.Credentials = new NetworkCredential(user, pass);
                    return client.DownloadString(url);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("Error executing request with :url={0}, user={1}, pass={2}", url, user, pass), ex);
            }
        }

        public async  static Task<string> GetResponseContentAsync(string url, string user, string pass, int? page = null)
        {
            //test only
            //string logFile = AppDomain.CurrentDomain.BaseDirectory + @"\" + "audit_tool_urls_" + page + ".txt";
            DateTime startedAt = DateTime.Now;
            
            WebRequest request = WebRequest.Create(url);
            request.Credentials = new NetworkCredential(user, pass);        
            Stream responseStream = null;
            string responseTxt = string.Empty;

            try
            {
                using (WebResponse response = await request.GetResponseAsync())
                {
                    Encoding enc = System.Text.Encoding.GetEncoding("UTF-8");
                    responseStream = response.GetResponseStream();               
                    using (StreamReader streamReader = new StreamReader(responseStream, enc))
                    {
                        responseTxt = await streamReader.ReadToEndAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("Error executing request with :url={0}, user={1}, pass={2}", url, user, pass), ex);
            }

            //responseTxt = SpecialCharactersHandler.ReplaceSpecialSymbols(responseTxt);
            //responseTxt = SpecialCharactersHandler.ReplaceSpecialSymbols(responseTxt);

            //DateTime endedAt = DateTime.Now;
            //System.IO.File.AppendAllText(logFile, string.Format("{0} seconds to get page {1}{2}", endedAt.Subtract(startedAt).TotalSeconds, page, System.Environment.NewLine));


            return responseTxt;
        }

        public static string GetResponseContent(string url, string user, string pass)
        {
            WebRequest request = WebRequest.Create(url);
            request.Credentials = new NetworkCredential(user, pass);
            // request.Headers.Add("Accept-Encoding", "gzip");        
            //request.Timeout = 30000;
            Stream responseStream = null;
            string responseTxt = string.Empty;

            try
            {
                using (WebResponse response = request.GetResponse())
                {
                    Encoding enc = System.Text.Encoding.GetEncoding("UTF-8");
                    responseStream = response.GetResponseStream();
                    //if (((HttpWebResponse)response).ContentEncoding.ToLower().Contains("gzip"))
                    //    responseStream = new GZipStream(responseStream, CompressionMode.Decompress);
                    using (StreamReader streamReader = new StreamReader(responseStream, enc))
                    {
                        responseTxt = streamReader.ReadToEnd();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("Error executing request with :url={0}, user={1}, pass={2}", url, user, pass), ex);
            }

            responseTxt = SpecialCharactersHandler.ReplaceSpecialSymbols(responseTxt);
            responseTxt = SpecialCharactersHandler.ReplaceSpecialSymbols(responseTxt);

            return responseTxt;
        }

        public static string GetResponse(string url)
        {
            using (WebClient client = new WebClient())
            {
                return client.DownloadString(url);
            }
        }

        public static bool IsAvailable(string url)
        {          
            Uri urlCheck = new Uri(url);
            WebRequest request = WebRequest.Create(urlCheck);
            request.Timeout = 15000;
            WebResponse response;
            try
            {
                response = request.GetResponse();
                return true;
            }
            catch (Exception)
            {
                return false; //url does not exist
            }

            
        }

    }
}
