﻿using Amazon.Runtime.Internal.Transform;
using Newtonsoft.Json;
using Storeya.Core.Models.ChatGPT;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class WebSitesSignalsHelper
    {
        public class WebSitesModels
        {

            public List<WebSitesResultsModels> ResultsTable { get; set; }
            public string[] gptModels { get; set; }
            public Action[] actions { get; set; }
            public string[] sites { get; set; }


            public class Action
            {
                public string name { get; set; }
                public string gpt { get; set; }
                public string google { get; set; }
                public bool AllPages { get; set; } = true;
                public bool StopIfFound { get; set; } = true;
                public string PostFilter { get; set; }
            }


        }

        public class WebSitesResultsModels
        {
            public WebSitesResultsModels()
            {
                actions = new List<Action>();
            }
            public string site { get; set; }
            public List<Action> actions { get; set; }
            public class Action
            {
                public string uid { get; set; }
                public string name { get; set; }
                public string postResults { get; set; }

                public string results { get; set; }
                public int calls { get; set; }
            }
        }

        public static List<WebSitesResultsModels> FilterJsonResults(string jsonResults, string[] aSites = null, string[] aActions = null)
        {

            List<WebSitesResultsModels> ResultsTable = new List<WebSitesResultsModels>();
            if (System.IO.File.Exists(jsonResults))
            {
                string jsonResultsText = System.IO.File.ReadAllText(jsonResults);
                ResultsTable = jsonResultsText.FromJson<List<WebSitesResultsModels>>();

                if (aSites != null && aSites.Length > 0)
                {
                    // var aSites = sites.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);

                    ResultsTable = ResultsTable.Where(c => aSites.Contains(c.site)).ToList();
                }
                if (aActions != null && aActions.Length > 0)
                {
                    //   var aActions = actions.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (var item in ResultsTable)
                    {
                        item.actions = item.actions.Where(c => aActions.Contains(c.name)).ToList();
                    }
                }

            }
            return ResultsTable;
        }

        public static List<WebSitesResultsModels> ExecuteSitesActions(string baseDirectory, string[] aSites, string[] aActions, string gptModel, bool save = false, bool overWrite = false, bool gptDebug = false, bool runFilters = false)
        {
            string json = baseDirectory + "/App_Data/GoogleSitesTest.json";
            string jsonResults = baseDirectory + "/App_Data/GoogleSitesResults.json";
            string jsonText = System.IO.File.ReadAllText(json);
            WebSitesModels model = jsonText.FromJson<WebSitesModels>();
            List<WebSitesResultsModels> resultsModels = new List<WebSitesResultsModels>();



            List<WebSitesResultsModels> resultsTable = FilterJsonResults(jsonResults, aSites, aActions);

            bool saveOverWrite = overWrite;
            int siteCount = aSites.Length;
            foreach (var site in aSites)
            {
                WebSitesResultsModels resultModel = new WebSitesResultsModels();
                resultModel.site = site;
                Console.WriteLine($"Executing on {site} , {siteCount} of {aSites.Length}");
                siteCount--;
                List<WebSitesResultsModels.Action> productsHighlights = new List<WebSitesResultsModels.Action>();
                foreach (var action in aActions)
                {
                    int calls = 0;
                    string results = string.Empty;
                    var act = model.actions.FirstOrDefault(c => c.name == action);
                    string postResults = string.Empty;
                    bool foundInResultsFile = false;
                    if (!overWrite)
                    {

                        var eSite = resultsTable.FirstOrDefault(x => x.site == site);
                        if (eSite != null)
                        {
                            var acs = eSite.actions.Where(a => a.name == action).ToList();
                            foreach (var ac in acs)
                            {
                                results = ac.results;
                                postResults = ac.postResults;
                                calls = ac.calls;
                                foundInResultsFile = true;
                                Console.WriteLine($"{DateTime.Now} {site} - action :{action} from results ");

                                if (!string.IsNullOrEmpty(act.PostFilter) && runFilters)
                                {
                                    postResults = null;
                                    results.Split(new string[] { "</br>", "<br/>" }, StringSplitOptions.RemoveEmptyEntries).ToList().ForEach(c =>
                                    {
                                        string r = FilterPostResulsts(act.PostFilter, c);
                                        if (!string.IsNullOrEmpty(r))
                                        {
                                            postResults += r + "<br/>";
                                        }
                                    });
                                }
                                if (ac.postResults != postResults)
                                {
                                    Console.WriteLine($"{DateTime.Now} {site} - action :{action} post filter ");
                                    saveOverWrite = true;
                                }
                            }
                        }
                        if (foundInResultsFile)
                        {
                            resultModel.actions.Add(new WebSitesResultsModels.Action { name = action, results = results, postResults = postResults, calls = calls });
                            continue;
                        }
                    }
                    if (!foundInResultsFile)
                    {
                        saveOverWrite = true;
                    }

                    try
                    {

                        if (gptModel == "Google")
                        {
                            var gResults = RunGoogleSearchAsync($"{act.google} {site}");
                            results = gResults.ToJson();
                            calls++;
                        }
                        if (gptModel == "Both")
                        {
                            var gResults = RunGoogleSearchAsync($"{act.google} {site}");
                            results = CallGPTFunction(gResults.ToJson(), act.gpt, debug: gptDebug);
                            calls++;
                        }
                        if (gptModel == "SiteInfo")
                        {
                            Console.WriteLine($"{DateTime.Now} {site} - action :{action} started ");
                            bool foundInHome = false;

                            if (action == "Product highlights")
                            {
                                bool foundInFile = false;
                                var productsFiles = Directory.GetFiles(baseDirectory + "/App_Data/ProjectData/" + site + @"/Products");
                                foreach (var productFile in productsFiles)
                                {
                                    if (!string.IsNullOrEmpty(productFile))
                                    {
                                        string pResults = string.Empty;
                                        string productPostResults = string.Empty;
                                        GetGPTResults(gptDebug, site, out int pCalls, ref pResults, act, ref foundInFile, productFile);
                                        if (!string.IsNullOrEmpty(pResults))
                                        {
                                            if (!string.IsNullOrEmpty(act.PostFilter))
                                            {
                                                productPostResults = FilterResults(results, act, productPostResults);
                                            }
                                            productsHighlights.Add(new WebSitesResultsModels.Action { uid = $"{Path.GetFileNameWithoutExtension(productFile).Replace("info-pid-", "")}", name = action, results = pResults, postResults = productPostResults, calls = pCalls });

                                        }
                                    }
                                }
                            }
                            else
                            {
                                string homeInforFile = baseDirectory + "/App_Data/ProjectData/" + site + @"/homeinfo.txt";
                                if (System.IO.File.Exists(homeInforFile))
                                {
                                    GetGPTResults(gptDebug, site, out calls, ref results, act, ref foundInHome, homeInforFile);

                                    //List<string> arr = SplitFile(homeInforFile, 20);
                                    //foreach (var item in arr)
                                    //{
                                    //    if (string.IsNullOrEmpty(item))
                                    //    {
                                    //        continue;
                                    //    }
                                    //    string fResults = CallGPTFunction(item, $"{act.gpt.Replace("%SITE%", site)}", debug: gptDebug);
                                    //    calls++;
                                    //    if (!IsNull(fResults))
                                    //    {

                                    //        results += fResults + "<br/>";
                                    //        foundInHome = true;
                                    //        if (calls > 5)
                                    //        {
                                    //            continue;
                                    //        }
                                    //        if (act.StopIfFound)
                                    //        {
                                    //            continue;
                                    //        }
                                    //    }
                                    //}
                                }
                                if (act.AllPages)
                                {
                                    foundInHome = false;
                                }
                                if (!foundInHome)
                                {
                                    string siteInforFile = baseDirectory + "/App_Data/ProjectData/" + site + @"/siteinfo.txt";
                                    if (System.IO.File.Exists(siteInforFile))
                                    {
                                        GetGPTResults(gptDebug, site, out calls, ref results, act, ref foundInHome, siteInforFile);
                                    }
                                    //List<string> arr = SplitFile(baseDirectory + "/App_Data/ProjectData/" + site + @"/siteinfo.txt", 20);
                                    //foreach (var item in arr)
                                    //{
                                    //    if (string.IsNullOrEmpty(item))
                                    //    {
                                    //        continue;
                                    //    }
                                    //    string fResults = CallGPTFunction(item, $"{act.gpt.Replace("%SITE%", site)}", debug: gptDebug);
                                    //    calls++;
                                    //    if (!IsNull(fResults))
                                    //    {

                                    //        results += fResults + "<br/>";
                                    //        if (calls > 5)
                                    //        {
                                    //            continue;
                                    //        }
                                    //        if (act.StopIfFound)
                                    //        {
                                    //            continue;
                                    //        }
                                    //    }
                                    //}
                                }
                            }
                            Console.WriteLine($"{DateTime.Now} {site} - action :{action} ended -calls:{calls} ");
                        }
                        //
                        else
                        {
                            results = CallGPT(site, act.gpt, gptModel);
                            calls++;
                        }
                    }
                    catch (Exception ex)
                    {
                        results = ex.Message;
                    }

                    if (!string.IsNullOrEmpty(act.PostFilter))
                    {
                        postResults = FilterResults(results, act, postResults);
                    }
                    if (productsHighlights.Count > 0)
                    {
                        foreach (var item in productsHighlights)
                        {
                            resultModel.actions.Add(item);
                        }
                    }
                    else
                    {
                        resultModel.actions.Add(new WebSitesResultsModels.Action { name = action, results = results, postResults = postResults, calls = calls });
                    }
                }
                resultsModels.Add(resultModel);
                if (save)
                {
                    resultsModels = SaveResults(jsonResults, resultsModels, saveOverWrite);
                }
            }
            Console.WriteLine($"{DateTime.Now} Done. ");
            return resultsModels;
        }

        private static string FilterResults(string results, WebSitesModels.Action act, string postResults)
        {
            results.Split(new string[] { "</br>", "<br/>" }, StringSplitOptions.RemoveEmptyEntries).ToList().ForEach(c =>
            {
                string r = FilterPostResulsts(act.PostFilter, c);
                if (!string.IsNullOrEmpty(r))
                {
                    postResults += r + "<br/>";
                }

            });
            return postResults;
        }

        private static void GetGPTResults(bool gptDebug, string site, out int calls, ref string results, WebSitesModels.Action act, ref bool foundInFile, string productFile)
        {
            calls = 0;
            List<string> arr = SplitFile(productFile, 20);
            foreach (var item in arr)
            {
                if (string.IsNullOrEmpty(item))
                {
                    continue;
                }
                string fResults = CallGPTFunction(item, $"{act.gpt.Replace("%SITE%", site)}", debug: gptDebug);
                calls++;
                if (!IsNull(fResults))
                {
                    results += fResults + "<br/>";
                    foundInFile = true;
                    if (calls > 5)
                    {
                        continue;
                    }
                    if (act.StopIfFound)
                    {
                        continue;
                    }
                }
            }


        }

        public static string FilterPostResulsts(string postFilter, string fResults)
        {
            string postResults = "";
            if (!string.IsNullOrEmpty(postFilter))
            {
                bool and = false;
                if (postFilter.StartsWith("and,"))
                {
                    and = true;
                    postFilter = postFilter.Replace("and,", "");
                }
                bool found = false;
                postFilter.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList().ForEach(c =>
                {

                    if (fResults.ToLower().Contains(c.ToLower()))
                    {
                        postResults = fResults;
                        found = true;
                    }
                    else
                    {
                        found = false;
                    }
                });
                if (and && !found)
                {
                    postResults = "";
                }
            }
            return postResults;
        }

        private static List<WebSitesResultsModels> SaveResults(string jsonResults, List<WebSitesResultsModels> model, bool overWrite = false)
        {


            if (System.IO.File.Exists(jsonResults))
            {
                string jsonResultsText = System.IO.File.ReadAllText(jsonResults);
                List<WebSitesResultsModels> cModel = jsonResultsText.FromJson<List<WebSitesResultsModels>>();
                foreach (var item in model)
                {
                    var cItem = cModel.FirstOrDefault(c => c.site == item.site);
                    if (cItem == null)
                    {
                        cModel.Add(item);
                    }
                    else
                    {
                        if (cItem.actions == null)
                        {
                            cItem.actions = item.actions;
                        }
                        else
                        {
                            foreach (var itemAction in item.actions)
                            {
                                var cItemAction = cItem.actions.FirstOrDefault(c => c.name == itemAction.name && c.uid == itemAction.uid);

                                if (cItemAction == null)
                                {
                                    cItem.actions.Add(itemAction);
                                }
                                else
                                {
                                    if (overWrite)
                                    {
                                        cItemAction.results = itemAction.results;
                                        cItemAction.postResults = itemAction.postResults;
                                        cItemAction.calls = itemAction.calls;
                                        cItemAction.uid = itemAction.uid;
                                    }
                                }


                            }
                        }
                    }
                }
                System.IO.File.WriteAllText(jsonResults, cModel.ToJson());
                return cModel;
            }
            else
            {
                System.IO.File.WriteAllText(jsonResults, model.ToJson());
                return model;
            }


        }
        private static bool IsNull(string results)
        {
            if (results.ToLower().Trim() == "null" || results.ToLower().EndsWith(" null"))
            {
                return true;
            }
            return false;
        }


        private static string CallGPT(string site, string query, string gptModel = null)
        {
            try
            {
                DateTime now = DateTime.Now;
                query = $"{query} https://{site}";
                string newDescription = ChatGPTManager.GetGptString(query, gptModel, throwException: true);
                newDescription = newDescription.TrimStart('\r', '\n').Replace("\n\n", "</br>").Replace("\n", "</br>");

                return newDescription;
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }

        private static string CallGPTFunction(string googleSearchResults, string query, string gptModel = "gpt-4", bool debug = false)
        {
            try
            {
                DateTime now = DateTime.Now;
                query = $"{query} {googleSearchResults}";
                string newDescription = ChatGPTManager.GetGptString(query, gptModel, throwException: true, debug: debug);
                newDescription = newDescription.TrimStart('\r', '\n').Replace("\n\n", "</br>").Replace("\n", "</br>");

                return newDescription;
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }
        private static List<string> SplitFile(string filePath, int targetSizeInKB = 20)
        {
            List<string> chunks = new List<string>();
            int chunkSize = targetSizeInKB * 1024; // 20 KB in bytes

            // Check file size
            FileInfo fileInfo = new FileInfo(filePath);
            if (fileInfo.Length <= chunkSize)
            {
                chunks.Add(System.IO.File.ReadAllText(filePath));
                return chunks;
            }

            StringBuilder currentChunk = new StringBuilder();
            StringBuilder word = new StringBuilder();

            using (StreamReader reader = new StreamReader(filePath))
            {
                int bytesRead = 0;
                while (!reader.EndOfStream)
                {
                    char c = (char)reader.Read();
                    bytesRead++;

                    if (char.IsWhiteSpace(c))
                    {
                        // End of a word
                        if (currentChunk.Length + word.Length > chunkSize)
                        {
                            chunks.Add(currentChunk.ToString());
                            currentChunk.Clear();
                        }
                        currentChunk.Append(word);
                        word.Clear();
                    }

                    word.Append(c);

                    // Check if current chunk exceeds the chunk size
                    if (currentChunk.Length + word.Length > chunkSize)
                    {
                        chunks.Add(currentChunk.ToString());
                        currentChunk.Clear();
                        currentChunk.Append(word);
                        word.Clear();
                    }
                }

                // Add any remaining content as the last chunk
                if (currentChunk.Length > 0)
                {
                    chunks.Add(currentChunk.ToString());
                }
            }

            return chunks;
        }
        private static string[] SplitFile1(string inputFilePath, int targetSizeInKB)
        {
            // Read the entire content of the input file
            string content = System.IO.File.ReadAllText(inputFilePath);
            System.IO.FileInfo info = new FileInfo(inputFilePath);
            long targetSizeInBytes = targetSizeInKB * 1024;
            if (info.Length < targetSizeInBytes)
            {
                return new string[] { content };
            }

            // Calculate the target size in bytes


            // Split the content into chunks
            List<string> chunks = new List<string>();
            int startPos = 0;
            while (startPos < content.Length)
            {
                int endPos = FindEndOfSentence(content, startPos, targetSizeInBytes);
                if (endPos == -1)
                {
                    endPos = content.Length - 1; // If no suitable end of sentence found, use the end of content
                }

                // Add the chunk to the list
                string chunkContent = content.Substring(startPos, endPos - startPos + 1);
                chunks.Add(chunkContent);

                // Move to the next chunk
                startPos = endPos + 1;
            }

            return chunks.ToArray();
        }

        private static int FindEndOfSentence(string content, int startPos, long targetSize)
        {
            int endPos = Math.Min(startPos + (int)targetSize, content.Length - 1);
            while (endPos > startPos)
            {
                if (content[endPos] == '.' || content[endPos] == '!' || content[endPos] == '?')
                {
                    return endPos;
                }
                endPos--;
            }
            return -1; // No suitable end of sentence found
        }
        public class GoogleResult
        {
            public string url { get; set; }
            public string title { get; set; }
            public string snippet { get; set; }
        }

        public static List<GoogleResult> RunGoogleSearchAsync(string query)
        {
            List<GoogleResult> results = new List<GoogleResult>();
            try
            {

                string apiKey = "AIzaSyCIptLCkpuP37PUuGaH4CelxAlnqaN6RlI";

                // Your Google Custom Search Engine ID
                string cx = "973669cb8f59c49b7";

                // Search query

                // Number of search results to retrieve
                int numResults = 3;

                // Construct the URL for the Google Custom Search API
                string apiUrl = $"https://www.googleapis.com/customsearch/v1?key={apiKey}&cx={cx}&q={Uri.EscapeDataString(query)}&num={numResults}";

                string newDescription = string.Empty;
                // Send a GET request to the API endpoint
                using (HttpClient client = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                    request.Headers.Add("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.120 Safari/537.36");

                    HttpResponseMessage response = client.SendAsync(request).GetAwaiter().GetResult();


                    // Send the request and parse the response
                    //  HttpResponseMessage response = client.GetAsync(apiUrl).GetAwaiter().GetResult();


                    if (response.IsSuccessStatusCode)
                    {
                        // Read the response content as a string
                        string jsonResponse = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();

                        // Parse the JSON response
                        dynamic result = JsonConvert.DeserializeObject(jsonResponse);

                        //newDescription = $"{result}";
                        if (result.items != null)
                        {
                            foreach (var item in result.items)
                            {
                                results.Add(new GoogleResult() { title = item.title, url = item.link, snippet = item.snippet });

                            }
                        }
                        // Extract and display the top search results
                        //for (int i = 0; i < numResults; i++)
                        //{
                        //    string title = result.items[i].title;
                        //    string link = result.items[i].link;

                        //    newDescription = $"{link} -  {title} </br>{newDescription}";
                        ////    Console.WriteLine($"Title: {title}");
                        ////      Console.WriteLine($"Link: {link}\n");
                        //}
                    }
                    else
                    {
                        // Handle the error
                        //    Console.WriteLine("Error: " + response.StatusCode);
                    }
                }


                return results;
            }
            catch (Exception ex)
            {
                results.Add(new GoogleResult() { title = ex.Message });
                return results;
            }

        }
    }
}
