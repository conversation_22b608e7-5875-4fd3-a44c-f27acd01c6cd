﻿using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Helpers
{
    public class WeeblyAuthHelper
    {
        static string _clientID = ConfigHelper.GetValue("Weebly_TB_ClientID");// "1818576482";
        static string _secret = ConfigHelper.GetValue("Weebly_TB_SecretKey"); //"f59b2688a357cdae9d3fc7aa645d672eafd1ae04747df6a76ccf96660e902163";
        static string _baseUrl = "https://www.weebly.com/";
        static string _apiBaseUrl = "https://api.weebly.com";



        public static string GetAccessToken(string authorization_code)
        {
            string baseUrl = _baseUrl;

            object objectToPost = new { client_id = _clientID, client_secret = _secret, authorization_code = authorization_code };

            var client = new RestSharp.RestClient(baseUrl);
            var request = new RestRequest("app-center/oauth/access_token", Method.POST);
            request.AddHeader("Accept", "application/json");
            request.Parameters.Clear();

            request.RequestFormat = RestSharp.DataFormat.Json;
            request.AddBody(objectToPost);

            IRestResponse response = client.Execute(request);

            return response.Content;
        }

        public static string GetAuthUrl(string userID, string siteID, string version, string callback_url, string scope)
        {
            string manageUrl = HttpHelper.GetCurrentDomain() + "/weebly/AuthStep2";
            string pattern = "{0}?reg=true&client_id={1}&user_id={2}&site_id={3}&scope={4}&redirect_uri={5}&version={6}";

            string url = string.Format(pattern, callback_url, _clientID, userID, siteID, scope, manageUrl, version);

            return url;
        }

        public static string GetFinishUrl(string callback_url)
        {
            string url = string.Format("{0}/app-center/oauth/finish?{1}&client_id={2}", _baseUrl, callback_url, _clientID);
            return url;
        }


        public static string DecodeToken(string token, bool useSecret = true)
        {
            //byte[] secretKey = GetBytes(_secret);
            byte[] bytes = null;
            if (useSecret)
            {
                bytes = Encoding.UTF8.GetBytes(_secret);
            }
            string json = Jose.JWT.Decode(token, bytes);
            return json;

            /*
              "user_id": "49064695",
              "site_id": "175305873626560707",
              "callback_url": "https://www.weebly.com/app-center/traffic-booster",
              "iat": 1459541799,
              "jti": "56fed72717e01"
             */

        }

        public static string GetUser(string token)
        {
            var client = new RestSharp.RestClient(_apiBaseUrl);

            var request = new RestRequest("v1/user", Method.GET);

            request.AddHeader("Accept", "application/vnd.weebly.v1+json");
            request.AddHeader("X-Weebly-Access-Token", token);

            IRestResponse response = client.Execute(request);

            return response.Content;
        }

        public static string GetSite(string token, string site_id)
        {
            var client = new RestSharp.RestClient(_apiBaseUrl);

            var request = new RestRequest("/v1/user/sites/" + site_id, Method.GET);

            request.AddHeader("Accept", "application/vnd.weebly.v1+json");
            request.AddHeader("X-Weebly-Access-Token", token);

            IRestResponse response = client.Execute(request);

            return response.Content;
        }


        public static string ReportPayment(string token, string user_id, string site_id, decimal amount)
        {
            var client = new RestSharp.RestClient(_apiBaseUrl);


            object objectToPost = new
            {
                user_id = user_id,
                site_id = site_id,
                name = "Traffic Booster monthly payment",
                method = "purchase",
                kind = "recurring",
                term = "month",
                gross_amount = amount,
                payable_amount = Math.Round(amount * 0.03M, 2).ToString(),
                currency = "USD",
                detail = "Payment for StoreYa"
            };


            var request = new RestRequest("/v1/admin/app/payment_notifications", Method.POST);

            request.RequestFormat = RestSharp.DataFormat.Json;
            request.AddBody(objectToPost);

            request.AddHeader("Accept", "application/vnd.weebly.v1+json");
            request.AddHeader("X-Weebly-Access-Token", token);

            IRestResponse response = client.Execute(request);

            return response.Content;
        }

        public static string ReportRefund(string token, string user_id, string site_id, decimal amount)
        {
            var client = new RestSharp.RestClient(_apiBaseUrl);

            object objectToPost = new
            {
                user_id = user_id,
                site_id = site_id,
                name = "Traffic Booster monthly payment",
                method = "refund",
                kind = "single",
                term = "forever",
                gross_amount = amount,
                payable_amount = Math.Round(amount * 0.03M, 2).ToString(),
                currency = "USD",
                detail = "Payment for StoreYa"
            };


            var request = new RestRequest("/v1/admin/app/payment_notifications", Method.POST);

            request.RequestFormat = RestSharp.DataFormat.Json;
            request.AddBody(objectToPost);

            request.AddHeader("Accept", "application/vnd.weebly.v1+json");
            request.AddHeader("X-Weebly-Access-Token", token);

            IRestResponse response = client.Execute(request);

            return response.Content;
        }

    }
    public class WeeblySiteDetails
    {
        public int user_id { get; set; }
        public long site_id { get; set; }
        public string user_name { get; set; }
        public string user_email { get; set; }
        public string user_language { get; set; }
        public string site_url { get; set; }
        public string site_title { get; set; }

        public static WeeblySiteDetails Load(string userJson, string siteJson)
        {
            var data = new WeeblySiteDetails();


            dynamic parsedUser = Newtonsoft.Json.JsonConvert.DeserializeObject(userJson);
            //{"user_id":"49064695","email":"<EMAIL>","name":"Olga","language":"en"} 
            data.user_id = Convert.ToInt32(parsedUser.user_id.ToString());
            data.user_email = parsedUser.email.ToString();
            data.user_name = parsedUser.name.ToString();
            data.user_language = parsedUser.language.ToString();

            dynamic parsedSite = Newtonsoft.Json.JsonConvert.DeserializeObject(siteJson);
            //{"user_id":"49064695","site_id":"175305873626560707","site_title":"My Site","domain":"freefbstore.weebly.com","is_published":true,"site_background":null,"plan_level":0}
            data.site_id = Convert.ToInt64(parsedSite.site_id.ToString());
            data.site_url = parsedSite.domain.ToString();
            data.site_title = parsedSite.site_title.ToString();
            return data;
        }

    }

}
