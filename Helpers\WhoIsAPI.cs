﻿using Amazon.Util;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;
using static Storeya.Core.Helpers.BlackListHelper;

namespace Storeya.Core.Helpers
{
    public class WhoIsAPI
    {
        public static WhoisInfo GetWhoisInfo(string domain)
        {
            if (string.IsNullOrEmpty(domain))
            {
                return null;
            }
            try
            {
                if (domain.Contains("myshopify"))
                {
                    return null;
                }
                WhoIsRoot whoIsRoot = GetWhoIsAPIResponse(domain);
                if (whoIsRoot != null && whoIsRoot.result != null)
                {
                    WhoisInfo whoisInfo = new WhoisInfo();
                    if (whoIsRoot.result.creation_date != null)
                    {
                        whoisInfo.RegistrationDate = DateTime.Parse(whoIsRoot.result.creation_date);
                    }
                    if (whoIsRoot.result.country != null)
                    {
                        whoisInfo.RegistrationCountry = whoIsRoot.result.country.ToString();
                    }

                    return whoisInfo;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("GetWhoisInfo failed " + ex.Message);
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "GetWhoisInfo failed", $"Domain - {domain}, error: {ex.ToString()}");
            }

            return null;
        }
        public static void PrintWhoisInfo(WhoisInfo whoisInfo)
        {
            Console.WriteLine($"RegistrationDate: {whoisInfo.RegistrationDate}, RegistrationCountry: {whoisInfo.RegistrationCountry}, Error: {whoisInfo.Error}");
        }

        public static WhoIsRoot GetWhoIsAPIResponse(string domain)
        {
            try
            {
                domain = UrlPathHelper.GetHostName(domain).ToLower().Replace("www.", "").Replace("_BAD_FORMATTED", "").Replace("_bad_formatted", "");
                string apiKey = "so6AiO2di4Fy7nG6CHDTnJS8OsSX8GD6";
                var client = new RestClient($"https://api.apilayer.com/whois/query?domain={domain}");
                client.Timeout = -1;

                var request = new RestRequest(Method.GET);
                request.AddHeader("apikey", apiKey);
                IRestResponse response = new RestResponse();
                try
                {
                    response = client.Execute(request);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("client.Execute failed!");
                    throw new Exception("client.Execute failed. " + ex.ToString());
                }
                string r = response.Content;
                if (r != null && !r.Contains($": \"error\", \"message\":") && !r.Contains("\"message\": \"No match for")) //&& !r.Contains($"No match for {domain}") && !r.Contains("Invalid authentication credentials") && !r.Contains("TLD not supported") && !r.Contains("Not a valid domain name")
                {
                    var whoIsResult = JsonConvert.DeserializeObject<WhoIsRoot>(r);
                    return whoIsResult;
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine("GetWhoIsAPIResponse failed " + ex.Message);
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "GetWhoIsAPIResponse failed", $"Domain - {domain}, error: {ex.ToString()}");
                return null;
            }

        }
    }
    public class WhoIsResult
    {
        public object address { get; set; }
        public object city { get; set; }
        public object country { get; set; }
        public string creation_date { get; set; }
        public object dnssec { get; set; }
        public object domain_name { get; set; }
        public object emails { get; set; }
        public string expiration_date { get; set; }
        public object name { get; set; }
        public object name_servers { get; set; }
        public object org { get; set; }
        public object referral_url { get; set; }
        public object registrar { get; set; }
        public object state { get; set; }
        public object status { get; set; }
        public string updated_date { get; set; }
        public string whois_server { get; set; }
        public object zipcode { get; set; }
    }

    public class WhoIsRoot
    {
        public WhoIsResult result { get; set; }
    }
}
