﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    public class WidgetStatus
    {
        public int? ID { get; set; }
        public string Name { get; set; }
    }

    public static class WidgetStatusHelper
    {
        public static List<WidgetStatus> GetStatus()
        {
            List<WidgetStatus> list = new List<WidgetStatus>();
            list.Add(new WidgetStatus() { ID = null, Name = "- Select Status -" });
            list.Add(new WidgetStatus() { ID = 1, Name = "Enabled" + "(1)" });
            list.Add(new WidgetStatus() { ID = 0, Name = "Disabled" + "(0)" });
            list.Add(new WidgetStatus() { ID = 100, Name = "Deleted" + "(100)" });
            return list;
        }
        public static string GetStatusString(int? status)
        {
            if (status == null)
                return null;
            string statusString = GetStatus().Where(x => x.ID == status).First().Name;
            return statusString;
        }
    }
}
