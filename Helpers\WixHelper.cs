﻿using Newtonsoft.Json.Linq;
using Storeya.Core.Models.AdWords;
using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;


namespace Storeya.Core.Helpers
{
    public class WixActivePackage
    {
        public string VendorProductId { get; set; }
        public string Cycle { get; set; }
        public string Error { get; set; }
    }

    public static class WixHelper
    {
        private static string _wixAPIEndpointUri = "https://openapi.wix.com";
        //private static string _wixAPITokenUri = "http://openapi.wix.com/v1/auth/token";
        private static string _wixAPISecret = ConfigHelper.GetValue("Wix_SecretKey");
        private static string _wixAPIKey = ConfigHelper.GetValue("Wix_AppID", "1435936f-207f-b916-405d-6830e23e1e21"); //"1435967d-8d5b-fb06-ce29-6828d080baa7");

        private static string unreservedChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_.~";

        //private static string GetAuthToken()
        //{
        //    // headers vars
        //    var ts = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
        //    var nonce = Guid.NewGuid().ToString();
        //    // canonical signature
        //    StringBuilder canonical = new StringBuilder();
        //    canonical.Append("GET\n");
        //    canonical.Append("/auth/token\n");
        //    canonical.Append("x-wix-auth-nonce:" + nonce + "\n");
        //    canonical.Append("x-wix-auth-ts:" + ts);
        //    // create the HMACSHA256 signature  and convert it to Base64
        //    var MAC = new HMACSHA256(System.Text.Encoding.ASCII.GetBytes(_wixAPISecret));
        //    var signature = Convert.ToBase64String(MAC.ComputeHash(System.Text.Encoding.ASCII.GetBytes(canonical.ToString()))).Replace('+', '-').Replace('/', '_');
        //    // get token request
        //    var request = WebRequest.Create(_wixAPITokenUri);
        //    request.Headers[HttpRequestHeader.Authorization] = string.Format("WIX {0}:{1}", _wixAPIKey, signature);
        //    request.Headers.Add("x-wix-auth-nonce", nonce);
        //    request.Headers.Add("x-wix-auth-ts", ts);
        //    JObject resjson = JObject.Parse(ResponseAsDynamicJson(request));
        //    return resjson["scheme"] + " " + resjson["token"];
        //}



        private static string StreamToString(Stream stream)
        {
            using (var sr = new StreamReader(stream))
            {
                return sr.ReadToEnd();
            }
        }
        //private static WebRequest RequestBuilder(string uri)
        //{
        //    var req = WebRequest.Create(uri);
        //    req.Headers[HttpRequestHeader.Authorization] = GetAuthToken();
        //    return req;
        //}
        private static dynamic ResponseAsDynamicJson(WebRequest req)
        {
            req.Method = "GET";
            var response = req.GetResponse();
            return StreamToString(response.GetResponseStream());
        }

        public static JObject GetPurchaseHistory(string wix_instance_id)
        {
            string url_withoutHost = "/v1/premium/oneTimePurchases";
            string response_string = GetResponse(url_withoutHost, wix_instance_id);
            JObject data = JObject.Parse(response_string);
            return data;
        }

        public static JObject GetInstance(string wix_instance_id)
        {
            //https://www.wixapis.com/apps/v1/instance
            string url_withoutHost = "/v1/instance";
            string response_string = GetResponse(url_withoutHost, wix_instance_id);
            JObject data = JObject.Parse(response_string);
            return data;
        }

        public static WixActivePackage GetActivePackage(string wix_instance_id)
        {
            WixActivePackage active_package = null;

            string url_withoutHost = "/v1/billing/active";

            try
            {
                string response_string = GetResponse(url_withoutHost, wix_instance_id);
                if (!string.IsNullOrEmpty(response_string)) //Blank response means no active package;
                {
                    //JObject data = JObject.Parse(response_string);
                    dynamic parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(response_string);
                    //data = {
                    //  "vendorProductId": "wixplan",
                    //  "cycle": "MONTHLY"
                    //}
                    active_package = new WixActivePackage() { VendorProductId = parsedJson.vendorProductId, Cycle = parsedJson.cycle };
                }
                else
                {
                    active_package = new WixActivePackage() { Error = "No active package were found" };
                }
            }
            catch (WebException ex)
            {
                string errorMessage = null;
                HttpWebResponse webResponse = (HttpWebResponse)ex.Response;
                if (webResponse != null)
                {
                    errorMessage = HttpRequestResponseHelper.GetContent(webResponse);
                }

                if (!string.IsNullOrEmpty(errorMessage) && errorMessage.ToLower().StartsWith("<!doctype html>"))
                    errorMessage = "The response contains an HTML content.";
                errorMessage = errorMessage + " " + ex.ToString();
                active_package = new WixActivePackage() { Error = errorMessage };
            }
            catch (Exception ex)
            {
                active_package = new WixActivePackage() { Error = ex.ToString() };
            }

            return active_package;
        }


        public static string GetResponse(string url_withoutHost, string wix_instance_id)
        {

            string url = _wixAPIEndpointUri + url_withoutHost + "?version=1.0.0";

            var ts = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"); //"2018-08-05T13:29:55.921Z"; 
            var nonce = Guid.NewGuid().ToString();

            Dictionary<String, String> parameters = new Dictionary<String, String>();
            parameters.Add("x-wix-application-id", _wixAPIKey);
            parameters.Add("x-wix-instance-id", wix_instance_id);
            parameters.Add("x-wix-timestamp", ts);


            StringBuilder canonical = new StringBuilder();
            canonical.Append("GET\n");
            canonical.Append(url_withoutHost + "\n");
            canonical.Append("1.0.0\n");
            foreach (KeyValuePair<String, String> item in parameters.OrderBy(x => x.Key))
            {
                canonical.Append(item.Value).Append("\n");
                //canonical.Append(item.Key).Append(":").Append(item.Value).Append("\n");
            }

            string output_to_sigh = canonical.ToString().TrimEnd('\n');

            //Compute the HMACSHA-256 of the combined information using your app secret key.
            string appSecretkey = ConfigHelper.GetValue("Wix_SecretKey");
            var hash = new HMACSHA256(Encoding.ASCII.GetBytes(appSecretkey));
            byte[] computeHash = hash.ComputeHash(Encoding.ASCII.GetBytes(output_to_sigh));

            //Encode the hash to a Base64 string.
            string signature = System.Convert.ToBase64String(computeHash);
            if (signature.EndsWith("="))
                signature = signature.TrimEnd('=');

            signature = signature.Replace('+', '-').Replace('/', '_');

            parameters.Add("x-wix-signature", signature);

            var sb1 = new System.Text.StringBuilder();
            foreach (KeyValuePair<String, String> item in parameters.OrderBy(x => x.Key))
            {
                // even "empty" params need to be encoded this way.
                sb1.AppendFormat("{0}={1}&", item.Key, item.Value);
            }

            var request = WebRequest.Create(url);

            foreach (KeyValuePair<String, String> item in parameters.OrderBy(x => x.Key))
            {
                request.Headers.Add(item.Key, item.Value);
            }

            string response_string = ResponseAsDynamicJson(request);


            return response_string;
        }

        public static string EncodeRequestParameters(ICollection<KeyValuePair<String, String>> p)
        {
            var sb = new System.Text.StringBuilder();
            foreach (KeyValuePair<String, String> item in p.OrderBy(x => x.Key))
            {
                if (!String.IsNullOrEmpty(item.Value)
                    // && !item.Key.EndsWith("secret")
                    )
                    sb.AppendFormat("{0}:{1},",
                                    item.Key,
                                    item.Value);
            }

            return sb.ToString().TrimEnd(' ').TrimEnd(',');
        }


        public static string UrlEncode(string value)
        {
            var result = new StringBuilder();
            foreach (char symbol in value)
            {
                if (unreservedChars.IndexOf(symbol) != -1)
                    result.Append(symbol);
                else
                {
                    foreach (byte b in Encoding.UTF8.GetBytes(symbol.ToString()))
                    {
                        result.Append('%' + String.Format("{0:X2}", b));
                    }
                }
            }
            return result.ToString();
        }




        //public static string GetPurchaseHistory()
        //{
        //    string url = "/premium/oneTimePurchases";
        //    WebRequest requestBuilder = RequestBuilder(_wixAPIEndpointUri + url);
        //    var response = ResponseAsDynamicJson(requestBuilder);
        //    JObject urijson = JObject.Parse(response);
        //    return urijson["upload_url"].ToString();
        //}


        public static string CreateDashboardUrlByStoreYaShopID(int shopID, bool addCurrentDomain = true)
        {
            string dashboardUrl = null;

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            WixConnectedSite site = db.WixConnectedSites.Where(s => s.ShopID == shopID).SingleOrDefault();
            if (site != null)
            {
                string signed_instance_data = CreateInstance(site.WixInstance, site.WixSiteOwnerID, site.WixPlanID);

                //https://dev.wix.com/docs/development/dashboard#deep-linking
                //[endpoint]?instance=[signed-instance-data]&locale=[locale]&cacheKiller=[cacheKiller]&siteUrl=[site-url]&isPublish=[isPublish]&viewMode=dashboard

                string locale = "en";

                // var cacheKiller = Wix.Utils.getCacheKiller(); ?? available with javascript sdk

                string siteUrl = (!string.IsNullOrEmpty(site.WixUrl) ? (string.Format("&siteUrl={0}", site.WixUrl)) : null);
                string currentDomain = string.Empty;
                if (addCurrentDomain)
                {
                    currentDomain = HttpHelper.GetCurrentDomain();
                }
                dashboardUrl = string.Format("{0}/wixtraffic?instance={1}&isPublish=true&locale={2}&viewMode=dashboard{3}", currentDomain, signed_instance_data, locale, siteUrl);
            }
            return dashboardUrl;
        }

        public static string CreateDashboardDeepLink(int shopID)
        {
            string dashboardUrl = null;

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            WixConnectedSite site = db.WixConnectedSites.Where(s => s.ShopID == shopID).SingleOrDefault();
            if (site != null)
            {
                string wix_AppID = ConfigHelper.GetValue("Wix_AppID", "1435967d-8d5b-fb06-ce29-6828d080baa7");
                dashboardUrl = string.Format("https://www.wix.com/my-account/app/{0}/{1}/", wix_AppID, site.WixInstance); //<appState>    &appState=
            }
            return dashboardUrl;
        }

        public static string CreateInstance(string instanceID, string siteOwnerID, string vendorProductId = "null", DateTime? signDate = null)
        {
            DateTime uDat = (signDate != null) ? signDate.Value.ToUniversalTime() : DateTime.Now.ToUniversalTime();
            string date_string = string.Format("{0:O}", uDat);

            string json = "{\"instanceId\":\"" + instanceID + "\",\"signDate\":\"" + date_string + "\",\"uid\":\"" + siteOwnerID + "\",\"permissions\":\"OWNER\",\"vendorProductId\":\"" + vendorProductId + "\",\"demoMode\":false,\"siteOwnerId\":\"" + siteOwnerID + "\"}";

            //2 Use Base64 to get an encoded_instance.
            byte[] textAsBytes = System.Text.Encoding.ASCII.GetBytes(json);
            string encoded_instance = System.Convert.ToBase64String(textAsBytes);

            //3. Sign it with your App Secret.
            string appSecretkey = ConfigHelper.GetValue("Wix_SecretKey");
            var hash = new HMACSHA256(Encoding.ASCII.GetBytes(appSecretkey));

            int index = encoded_instance.IndexOf("==");
            string encoded_instance_1 = null;
            if (index != -1)
            {
                encoded_instance_1 = encoded_instance.Remove(index);
            }
            else
            {
                encoded_instance_1 = encoded_instance;
            }

            encoded_instance_1 = encoded_instance_1.Replace("/", "_").Replace("+", "-");

            byte[] computeHash = hash.ComputeHash(Encoding.ASCII.GetBytes(encoded_instance_1));
            string s = Encoding.ASCII.GetString(computeHash);
            string digest = System.Convert.ToBase64String(computeHash);

            //4. Construct the instance parameter: [digest].[encoded_instance].
            string signed_instance_data = digest + "." + encoded_instance_1;

            return signed_instance_data;
        }


        public static void SendEmailAfterPurchase(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(x => x.ID == shopID).Single();
            User user = db.Users.Where(x => x.ID == shop.UserID).Single();
            if (user.Email != null)
            {
                TrafficBooster tb_settings = TrafficBoostersDbHelper.GetSettings(shopID);
                string shopContentTemplate = "";
                if (tb_settings.IsServiceProvider.HasValue && tb_settings.IsServiceProvider == (int)BusinessTypes.OnlineStore)
                {
                    //string shopContentPath = "payments/thank_you_tb_wix_shop_content.html";
                    string contentTemplate1 = EmailHelper.GetTemplateContentFromResource("thank_you_tb_wix_shop_content");
                    shopContentTemplate = contentTemplate1.FormatWith(new { ShopKey = SequenceHelper.Encode(shop.ID), WixDashboardUrl = WixHelper.CreateDashboardDeepLink(shopID) });

                }

                //string contentPath = "payments/thank_you_tb_wix.html";
                string contentTemplate = EmailHelper.GetTemplateContentFromResource("thank_you_tb_wix");
                contentTemplate = contentTemplate.FormatWith(new { ShopContent = shopContentTemplate });

                EmailHelper.SendEmail(user.Email, "Traffic Generator - thank you for your purchase", contentTemplate, null, null, true);
            }

        }


        public static string GetWixCustomCode(int shopID)
        {
            var tagTemplate = Storeya.Core.Properties.Resources.Wix_CustomCodeForGoogle;
            var tb = TrafficBoostersDbHelper.GetSettings(shopID);

            if (tb != null && tb.AdWordsAccount != null && tb.AdWordsPixel != null)
            {
                var googleEventSnippet = tb.AdWordsPixel;

                ConversionData data = AdWordsPixelParser.ExtractGoogleConversionID_FromOldPixel(googleEventSnippet, shopID, "GetWixCustomCode");
                tagTemplate = tagTemplate.Replace("XXXXXXXXXX", data.conversion_id);
                tagTemplate = tagTemplate.Replace("YYYYYYYYYY", data.conversion_label);
            }
            else
            {
                throw new Exception("Data is missing in tbSetings");
            }

            return tagTemplate;
        }

        public static List<WixWebhooksLog> GetWixWebhooksLogs(int shopId, string eventName = null)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            WixConnectedSite site = db.WixConnectedSites.Where(s => s.ShopID == shopId).SingleOrDefault();
            if (site == null)
            {
                return null;
            }
            List<WixWebhooksLog> logs = db.WixWebhooksLogs.Where(i => i.WixInstance == site.WixInstance).OrderByDescending(i => i.InsertedAt).ToList();
            if (!string.IsNullOrEmpty(eventName))
            {
                logs = logs.Where(l => l.Event == eventName).OrderByDescending(i => i.InsertedAt).ToList();
            }
            return logs;
        }


        public static bool IsReturningCustomer(int shopId, int notMoreThenDays = 3, int cancelWasMoreThenInDays = 360)
        {
            try
            {
                var logs = GetWixWebhooksLogs(shopId);
                bool cancelled = false;
                for (int i = 0; i < logs.Count() - 1; i++)
                {
                    if (logs[i].Event == "CANCEL_IMMEDIATE")
                    {
                        cancelled = true;
                    }
                    if (logs[i].Event == "PURCHASE_IMMEDIATE" && cancelled == false)
                    {
                        if (i + 1 < logs.Count() - 1 && logs[i + 1].Event == "CANCEL_IMMEDIATE")
                        {
                            if ((logs[i].InsertedAt.Value - logs[i + 1].InsertedAt.Value).TotalDays > cancelWasMoreThenInDays)
                            {
                                if (logs[i].InsertedAt > DateTime.Now.AddDays(-notMoreThenDays))
                                {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteErrorWithDB("Failed To check if a returning wix customer", ex, shopId);
            }
            return false;
        }
    }
}
