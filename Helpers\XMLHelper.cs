﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;
using System.Xml.XPath;
namespace Storeya.Core.Helpers
{
    public static class XmlHelper
    {

        public static T Deserialize<T>(string xmlInput) where T : class
        {
            System.Xml.Serialization.XmlSerializer ser = new System.Xml.Serialization.XmlSerializer(typeof(T));

            using (StringReader sr = new StringReader(xmlInput))
            {
                return (T)ser.Deserialize(sr);
            }
        }

        public static string Serialize<T>(T ObjectToSerialize)
        {
            XmlSerializer xmlSerializer = new XmlSerializer(ObjectToSerialize.GetType());

            using (StringWriter textWriter = new Utf8StringWriter())
            {
                xmlSerializer.Serialize(textWriter, ObjectToSerialize);
                return textWriter.ToString();
            }
        }

        public static string GetXMLValue(string xmlString, string xpath)
        {

            try
            {
                var regex = @"(xmlns:?[^=]*=[""][^""]*[""])";
                xmlString = Regex.Replace(xmlString, regex, "", RegexOptions.IgnoreCase | RegexOptions.Multiline);                
                XDocument doc = XDocument.Parse(xmlString);

                var e = doc.XPathSelectElement(xpath, doc.Root.CreateNavigator());
                if (e == null)
                {
                    return null;
                }
                return e.Value;
            }
            catch
            {

                return null; ;
            }
        }
        public class Utf8StringWriter : StringWriter
        {
            // Use UTF8 encoding but write no BOM to the wire
            public override Encoding Encoding
            {
                get { return new UTF8Encoding(false); } // in real code I'll cache this encoding.
            }
        }

        public static bool IsWellFormatted(string xmlInput)
        {
            try
            {
                var settings = new XmlReaderSettings { DtdProcessing = DtdProcessing.Ignore, XmlResolver = null };
                using (var reader = XmlReader.Create(new StringReader(xmlInput), settings))
                {
                    var document = new XmlDocument();
                    document.Load(reader);
                }
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception("XML is not well formatted :" + ex.Message, ex);
            }
            
        }
    }
}