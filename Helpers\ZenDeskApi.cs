﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RestSharp;
using System.Web;
using RestSharp.Authenticators;
using System.Net;
using Storeya.Core.Models.CRM;
using Storeya.Core.Models.Account;
using Storeya.Core.Entities;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models.TbInternalTasks;
using Newtonsoft.Json;
using System.IO;

namespace Storeya.Core.Helpers
{
    public struct ZendeskAuthorId
    {
        public const string StoreYa = "********";
    }
    public class ZendeskTicket
    {
        public string Url { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Subject { get; set; }
        public HtmlString Description { get; set; }
        public string Status { get; set; }
    }


    public class ZendDeskTicketUpdateObject
    {
        public ZendDeskTicketUpdate ticket { get; set; }
    }

    public class ZendDeskTicketUpdate
    {
        public ZendDeskComment comment { get; set; }
        public ZendeskCustomFields[] custom_fields { get; set; }
    }

    public class ZendDeskComment
    {
        public string html_body { get; set; }
        [Newtonsoft.Json.JsonProperty("public")]
        public bool _public { get; set; } = false;
    }
    public class ZendDeskUserObject
    {
        public ZendDeskUser user { get; set; }
    }
    public class ZendDeskUser
    {
        public long id { get; set; }
        public string url { get; set; }
        public string name { get; set; }
        public string email { get; set; }
        public DateTime? created_at { get; set; }
        public DateTime? updated_at { get; set; }
        public string time_zone { get; set; }
        public string iana_time_zone { get; set; }
        public object phone { get; set; }
        public object shared_phone_number { get; set; }
        public object photo { get; set; }
        public int locale_id { get; set; }
        public string locale { get; set; }
        public object organization_id { get; set; }
        public string role { get; set; }
        public bool verified { get; set; }
        public object external_id { get; set; }
        public List<object> tags { get; set; }
        public object alias { get; set; }
        public bool active { get; set; }
        public bool shared { get; set; }
        public bool shared_agent { get; set; }
        public object last_login_at { get; set; }
        public string two_factor_auth_enabled { get; set; }
        public object signature { get; set; }
        public object details { get; set; }
        public object notes { get; set; }
        public object role_type { get; set; }
        public object custom_role_id { get; set; }
        public bool moderator { get; set; }
        public string ticket_restriction { get; set; }
        public bool only_private_comments { get; set; }
        public bool restricted_agent { get; set; }
        public bool suspended { get; set; }
        public object default_group_id { get; set; }
        public bool report_csv { get; set; }
        public UserFields user_fields { get; set; }
    }
    public class UserFields
    {
        public DateTime? systemembeddable_last_seen { get; set; }
    }



    public class ZenDeskApi
    {
        string _apiEndPoint = "https://storeya.zendesk.com/";
        string _apiUsername = ConfigHelper.GetValue("ZenDeskApi_Username");
        string _api_token = ConfigHelper.GetValue("ZenDesk_API_Token");
        private const long CUSTOM_FIELD_URL = ********;
        private const long CUSTOM_FIELD_SHOPID = ********;
        private const long CUSTOM_FIELD_ACCOUNTMANAGER = ************;
        private const string EXLUDED_DOMAIN = "@storeya.com";
        public void SetNewTicketsCustomFields(string ticketId = null)
        {
            try
            {


                ZendeskTicketObject ticketObject = GetNewTickets(ticketId);
                if (ticketObject != null)
                {
                    if (!string.IsNullOrEmpty(ticketId))
                    {
                        int id = int.Parse(ticketId);
                        ticketObject.results = ticketObject.results.Where(s => s.id == id).ToArray();
                    }
                    List<ZendeskResult> results = null;
                    if (!string.IsNullOrEmpty(ConfigHelper.GetValue("ZenDeskTiketsTest")))
                    {
                        results = ticketObject.results.Where(t => t.custom_fields.Any(c => c.id == CUSTOM_FIELD_SHOPID)).ToList();
                    }
                    else
                    {
                        results = ticketObject.results.Where(t => t.custom_fields.Any(c => c.id == CUSTOM_FIELD_SHOPID && string.IsNullOrEmpty(c.value))).ToList();
                    }
                    foreach (var result in results)
                    {
                        try
                        {
                            ticketId = result.id.ToString();
                            bool noShopId = true;
                            var customUrl = result.custom_fields.SingleOrDefault(c => c.id == CUSTOM_FIELD_URL);
                            string email = result.via.source.from.address;
                            if (email == null)
                            {
                                var zendeskUser = GetUser(result.requester_id.ToString());
                                if (zendeskUser != null)
                                {
                                    email = zendeskUser.email;
                                }
                            }
                            List<Shop> shops = null;
                            if (!string.IsNullOrEmpty(email) && !email.ToLower().EndsWith(EXLUDED_DOMAIN))
                            {
                                if (ConfigHelper.IsValueInList("ZenDeskExcludEmails", email.ToLower()))
                                {
                                    continue;
                                }
                                shops = ShopsHelper.GetLatestShopsByEmail(email);
                            }
                            //if (shops == null)
                            //{
                            //    ConsoleAppHelper.WriteLog("Cannot find Shop for email " + email);
                            //    Log4NetLogger.ErrorWithDB($"{ticketId} Cannot find Shop for email {email}", null, (int)Log4NetLogger.SpecialShopIDs.ZendescNoShopID);
                            //}
                            if (shops != null)
                            {
                                UpdateNewTicketCustomFields(result.id.ToString(), out int? shopID, shops, "Shops where found by email:" + email);
                                noShopId = false;
                                HandleDuplicateTickets(result.id, email);
                            }
                            else if (customUrl != null)
                            {
                                if (!string.IsNullOrEmpty(customUrl.value))
                                {
                                    shops = ShopsHelper.GetLatestShopsByUrl(customUrl.value);
                                    if (shops == null)
                                    {
                                        if (customUrl.value.ToLower().StartsWith("http"))
                                        {
                                            var url = customUrl.value.Split(':')[1].Replace("/", "");
                                            shops = ShopsHelper.GetLatestShopsByUrl(url);
                                            if (shops == null)
                                            {
                                                url = "www." + url;
                                                shops = ShopsHelper.GetLatestShopsByUrl(url);
                                            }
                                        }
                                    }

                                    if (shops == null)
                                    {
                                        ConsoleAppHelper.WriteLog("Cannot find shop for Url " + customUrl.value);
                                        Log4NetLogger.ErrorWithDB($"{ticketId} Cannot find Shop for Url {customUrl.value}", null, (int)Log4NetLogger.SpecialShopIDs.ZendescNoShopID);
                                    }
                                    else
                                    {
                                        UpdateNewTicketCustomFields(result.id.ToString(), out int? shopID, shops, "Shops where found by Url:" + customUrl.value);
                                        noShopId = false;
                                    }

                                    HandleDuplicateTickets(result.id, null, customUrl.value);
                                }
                            }
                            if (noShopId)
                            {
                                AddTagToTheTicketIfItSLeadsForSales(email, ticketId);
                                UpdateNewTicketCustomFields(result.id.ToString(), out int? shopID, null, null);
                            }
                        }
                        catch (Exception ex)
                        {
                            ConsoleAppHelper.WriteError("Failed to update ticket id:" + result.id, ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Failed to Set New Tickets Custom Fields", ex.ToString());
                ConsoleAppHelper.WriteError("Failed to Set New Tickets Custom Fields", ex);
            }
        }

        public static void ReadAllTickets(int daysBack)
        {
            ZenDeskApi zenDeskApi = new ZenDeskApi();
            var tickets = zenDeskApi.GetNewTicketsLoop(null, daysBack);

            var selectedFields = tickets.Select(ticket => new {
                ticket.subject,
                ticket.description,
                ticket.requester_id,
            });

            // Serialize to JSON
            string json = JsonConvert.SerializeObject(selectedFields, Formatting.Indented);

            // Save to file
            File.WriteAllText("C:\\temp\\tickets.json", json);

        }

        private void AddTicketToRemarksTable(int? shopID, string remark, string remarkedBy)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                SuperAdminRemark superAdminRemark = new SuperAdminRemark()
                {
                    ShopID = shopID.Value,
                    InsertedAt = DateTime.Now,
                    Remarks = remark,
                    RemarkedBy = remarkedBy,
                    RemarkType = (int)RemarkTypes.Zendesk,
                };
                db.SuperAdminRemarks.Add(superAdminRemark);
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "AddTicketToRemarksTable failed", ex.ToString());
            }

        }

        private void AddTagToTheTicketIfItSLeadsForSales(string email, string ticketId)
        {
            if (string.IsNullOrEmpty(email))
            {
                return;
            }
            var db = DataHelper.GetStoreYaEntities();
            bool thereIsLead = db.LeadsForSales.Where(s => s.Email.ToLower() == email.ToLower()).Any();
            if (thereIsLead)
            {
                AddTagToTheTicket(ticketId, "SocialLead");
            }
        }

        public void SaveTiketsToRemarksAndSendEmailsToManagers(string ticketId = null, string[] managers = null)
        {
            if (managers == null)
            {
                managers = new string[] { "Mushon", "Yafit" };
            }
            Console.WriteLine("SendEmailsForNewCommentsForManager run");
            try
            {
                DateTime startDate = DateTime.Parse("01.01.2023");
                int lastEventID = SystemVarHelper.GetValue(VariableTypes.ZendeskCommentDateInSeconds);
                ZendeskTicketObject ticketObject = GetLastUpdatedTickets(ticketId);
                if (ticketObject != null && ticketObject.results != null)
                {
                    List<ZendeskResult> results = ticketObject.results.Where(t => t.custom_fields.Any(c => c.id == CUSTOM_FIELD_SHOPID && !string.IsNullOrEmpty(c.value) && c.value != "0") && DateTime.Now.AddMinutes(-5) > t.updated_at).ToList();
                    var resultCounter = 0;
                    var totalResults = results.Count;
                    if (totalResults > 0)
                    {
                        foreach (var result in results)
                        {
                            resultCounter++;
                            Console.WriteLine($"Progress {resultCounter}/{totalResults}");
                            string shopID = result.custom_fields.Where(x => x.id == CUSTOM_FIELD_SHOPID).Select(x => x.value).FirstOrDefault();
                            ticketId = result.id.ToString();
                            CommentZendesK lastComment = GetLastCommentByTicket(ticketId, out int count);
                            int dateDifferenceInSeconds = (int)(lastComment.created_at - startDate).TotalSeconds;
                            int savedLastDate = 0;
                            if (dateDifferenceInSeconds > lastEventID)
                            {
                                CreateTextForRemarkAndAddItToRemarks(result, lastComment, count);
                                if (lastComment.author_id != ZendeskAuthorId.StoreYa && count > 1 && result.custom_fields.Any(c => c.id == CUSTOM_FIELD_ACCOUNTMANAGER && !string.IsNullOrEmpty(c.value)))
                                {
                                    foreach (var manager in managers)
                                    {
                                        if (SendAnEmailToTheManager(manager, result, shopID))
                                        {
                                            break;
                                        }
                                    }
                                }

                                if (savedLastDate < dateDifferenceInSeconds)
                                {
                                    SystemVarHelper.SetValue(VariableTypes.ZendeskCommentDateInSeconds, dateDifferenceInSeconds);
                                    savedLastDate = dateDifferenceInSeconds;
                                }
                            }
                            else
                            {
                                Console.WriteLine($"Ticket skipped");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "SendEmailsForNewCommentsForManager failed.", $"TicketId: {ticketId} " + ex.ToString());
            }
        }

        private bool SendAnEmailToTheManager(string manager, ZendeskResult result, string shopID)
        {
            string ticketId = result.id.ToString();
            Console.WriteLine(result.custom_fields.Where(c => c.id == CUSTOM_FIELD_ACCOUNTMANAGER).Select(x => x.value.ToString()).FirstOrDefault());
            if (result.custom_fields.Any(c => c.id == CUSTOM_FIELD_ACCOUNTMANAGER && c.value == manager))
            {
                string emailAddress = null;
                if (manager == "Mushon")
                {
                    emailAddress = "<EMAIL>";
                }
                else if (manager == "Yafit")
                {
                    emailAddress = "<EMAIL>";
                }
                Console.WriteLine($"Sending email to {manager}");
                if (shopID != null)
                {
                    try
                    {
                        int shopIDint = Int32.Parse(shopID);
                        Log4NetLogger.InfoWithDB("Zendesk ticket email was sent", null, shopIDint);
                    }
                    catch { }
                }
                EmailHelper.SendEmail(emailAddress, $"There is a new ZenDesk comment in ticket for you {shopID}", $"<a href=\"https://storeya.zendesk.com/access/normal?return_to=https%3A%2F%2Fstoreya.zendesk.com/agent/tickets/{ticketId}\">Click here to view the ticket</a>");
                return true;
            }
            return false;
        }

        private void CreateTextForRemarkAndAddItToRemarks(ZendeskResult result, CommentZendesK lastComment, int count)
        {
            string ticketId = result.id.ToString();
            string linkToTheTiket = $"<a href=\"https://storeya.zendesk.com/access/normal?return_to=https%3A%2F%2Fstoreya.zendesk.com/agent/tickets/{ticketId}\">Click here to view the ticket</a>";
            string subject = ShowOnlyFirst50CharactersFromText(result.raw_subject);
            string remarkText = "";
            string remarkedBy = "";
            string shopIDString = result.custom_fields.Where(x => x.id == CUSTOM_FIELD_SHOPID).FirstOrDefault().value;
            if (lastComment.author_id != ZendeskAuthorId.StoreYa)
            {
                remarkedBy = "Client";
                if (count < 2)
                {
                    remarkText += "merchant opened ticket. ";
                }
                else
                {
                    remarkText += "merchant replied ticket. ";
                }

            }
            else
            {
                remarkedBy = "StoreYa";
                if (count > 1)
                {
                    remarkText += "StoreYa replied ticket. ";
                }
                else
                {
                    remarkText += "StoreYa opened ticket. ";
                }
            }
            remarkText += " Subject: " + subject;
            Console.WriteLine(remarkText);
            remarkText = remarkText + linkToTheTiket;
            int shopID = Int32.Parse(shopIDString);
            AddTicketToRemarksTable(shopID, remarkText, remarkedBy);
        }
        private static string ShowOnlyFirst50CharactersFromText(string subject)
        {
            if (subject != null && subject.Length > 50)
            {
                subject = subject.Substring(0, 47) + "...";
            }
            return subject;
        }
        public string UpdateNewTicketCustomFields(string ticketId, out int? shopID, List<Shop> shops = null, string source = null)
        {
            shopID = null;
            string comment = string.Empty;
            string accountManager = string.Empty;
            int shopid = 0;
            if (shops != null)
            {
                foreach (var shop in shops.OrderByDescending(o => o.ID).Take(10))
                {
                    if (shopid == 0)
                    {
                        shopid = shop.ID;
                    }
                    string shopBoUrl = EmailHelper.GetBoLinkHrefAndAM(shop.ID, out string am);
                    if (shop.IsDisabled == 1)
                    {
                        comment = shopBoUrl + "(Canceled)," + comment;
                    }
                    else
                    {
                        comment = shopBoUrl + "," + comment;
                    }
                    if (string.IsNullOrEmpty(accountManager))
                    {
                        if (string.IsNullOrEmpty(am))
                        {
                            var accountOwner = StoreYaEmployeeHelper.GetAccountManagerByShopID(shop.ID);
                            am = ((StoreYaEmployee)accountOwner).ToString();
                        }
                        accountManager = am;
                    }
                }
                if (!string.IsNullOrEmpty(source))
                {
                    comment = source + " ," + comment + ", ";
                }
            }
            ZendDeskTicketUpdateObject ticketObj = new ZendDeskTicketUpdateObject()
            {
                ticket = new ZendDeskTicketUpdate()
                {

                    custom_fields = new ZendeskCustomFields[]
                    {
                        new ZendeskCustomFields() { id = CUSTOM_FIELD_SHOPID, value = $"{shopid}"  },
                        new ZendeskCustomFields() { id = CUSTOM_FIELD_ACCOUNTMANAGER, value = accountManager }
                    },
                }
            };
            shopID = shopid;
            if (!string.IsNullOrEmpty(comment))
            {
                ticketObj.ticket.comment = new ZendDeskComment()
                {
                    html_body = "Open shop(s) in BO: " + comment,
                    _public = false
                };
            }
            if (accountManager != null && accountManager == "Mushon")
            {
                EmailHelper.SendEmail("<EMAIL>", "There is a new ZenDesk ticket for you", $"<a href=\"https://storeya.zendesk.com/access/normal?return_to=https%3A%2F%2Fstoreya.zendesk.com/agent/tickets/{ticketId}\">Click here to view the ticket</a>");
            }
            else if (accountManager != null && accountManager == "Yafit")
            {
                EmailHelper.SendEmail("<EMAIL>", "There is a new ZenDesk ticket for you", $"<a href=\"https://storeya.zendesk.com/access/normal?return_to=https%3A%2F%2Fstoreya.zendesk.com/agent/tickets/{ticketId}\">Click here to view the ticket</a>");
            }
            IRestResponse response = GetResponse("api/v2/tickets/" + ticketId + ".json", Method.PUT, ticketObj);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                return response.Content;
            }
            else
            {
                return null;
            }
        }
        public List<ZendeskResult> GetNewTicketsLoop(string ticketId = null, double daysBack = 2)
        {
            // Build the part of the query that never changes for this call
            var rootQuery = ticketId ??
                            $"created>{DateTime.UtcNow.AddDays(-daysBack):yyyy-MM-dd} type:ticket";

            var allTickets = new List<ZendeskResult>();
            var page = 1;

            while (true)
            {
                // SearchFor already prefixes “/api/v2/search.json?query=”.
                // We only need to append &page=X after the query itself.
                var response = SearchFor($"{rootQuery}&page={page}");

                if (response == null || string.IsNullOrWhiteSpace(response.Content))
                    break;                                              // network / server error

                var pageObj = response.Content.FromJson<ZendeskTicketObject>();

                // Nothing on this page?  We’re done.
                if (pageObj?.results == null || pageObj.results.Count() == 0)
                    break;

                allTickets.AddRange(pageObj.results);

                // Zendesk’s Search response includes a “next_page” URL; if it’s null, there’s no more data.
                if (string.IsNullOrEmpty(pageObj.next_page?.ToString()))
                    break;

                page++;                                                 // try the next page
            }

            return allTickets;
        }


        public ZendeskTicketObject GetNewTickets(string ticketId = null, double daysBack = 2)
        {
            string query = string.Format("created>{0} type:ticket", DateTime.Now.AddDays(-daysBack).ToString("yyyy-MM-dd"));
            if (ticketId != null)
            {
                query = ticketId;
            }
            IRestResponse response = SearchFor(query);
            if (response != null && !string.IsNullOrEmpty(response.Content))
            {
                ZendeskTicketObject tickets = response.Content.FromJson<ZendeskTicketObject>();
                return tickets;
            }
            return null;
        }
        public ZendeskTicketObject GetLastUpdatedTickets(string ticketId = null)
        {
            string query = string.Format("updated_at>{0} type:ticket", DateTime.Now.AddDays(-2).ToString("yyyy-MM-dd"));
            if (ticketId != null)
            {
                query = ticketId;
            }
            Console.WriteLine($"GetLastUpdatedTickets query={query}");
            IRestResponse response = SearchFor(query);
            if (response != null && !string.IsNullOrEmpty(response.Content))
            {
                ZendeskTicketObject tickets = response.Content.FromJson<ZendeskTicketObject>();
                return tickets;
            }
            Console.WriteLine("GetLastUpdatedTickets failed");
            return null;
        }
        public bool AddTagToTheTicket(string ticketId, string tagText)
        {
            try
            {
                Tags tags = new Tags();
                tags.tags = new List<string> { tagText };
                IRestResponse response = GetResponse($"/api/v2/tickets/{ticketId}/tags", Method.PUT, tags);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "AddTagToTheTicket failed", $"TicketId: {ticketId}, tag: {tagText}. <br/> {ex.ToString()}");
                return false;
            }

        }
        public List<CommentZendesK> GetCommentsByTiketID(string ticketId, out int count)
        {
            Console.WriteLine($"Getting last comment by ticket id");
            count = 0;
            IRestResponse response = GetResponse($"/api/v2/tickets/{ticketId}/comments?sort_by=created_at&sort_order=desc", Method.GET);
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                if (response != null && !string.IsNullOrEmpty(response.Content))
                {
                    try
                    {
                        ZendesKCommentsObject comment = response.Content.FromJson<ZendesKCommentsObject>();
                        count = comment.comments.Count();
                        List<CommentZendesK> comments = comment.comments;
                        return comments;
                    }
                    catch (Exception ex)
                    {
                        string errorText = $"TicketId: {ticketId}. Json: {response.Content} Error: {ex.ToString()}";
                        EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "SendEmailsForNewCommentsForManager failed (JsonReaderException).", errorText);
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"Error! JsonReaderException {errorText}");
                        Console.ResetColor();
                        return null;
                    }

                }
                return null;
            }
            else
            {
                string headers = "";
                foreach (var h in response.Headers)
                {
                    headers += $"Name: {h.Name} Value: {h.Value} <br/>";
                }
                string errorText = $"TicketId: {ticketId}." +
                    $"<br/> response.ResponseUri={response.ResponseUri}" +
                    $"<br/>response.Content={response.Content}" +
                    $"<br/>Headers: {headers}";
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, $"SendEmailsForNewCommentsForManager failed (response.StatusCode={response.StatusCode}).", errorText);
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"Error! Response.StatusCode={response.StatusCode} {errorText}");
                Console.ResetColor();
                return null;
            }
        }
        public CommentZendesK GetLastCommentByTicket(string ticketId, out int count)
        {
            List<CommentZendesK> comments = GetCommentsByTiketID(ticketId, out int commentsCount);
            count = commentsCount;
            if (comments != null && comments.Count > 0)
            {
                CommentZendesK lastComment = comments.OrderByDescending(x => x.created_at).FirstOrDefault();
                return lastComment;
            }
            return null;
        }
        public string AddTicketComment(int ticketId, string comment, bool internalComment = true)
        {
            ZendDeskTicketUpdateObject ticketObj = new ZendDeskTicketUpdateObject()
            {
                ticket = new ZendDeskTicketUpdate(),
            };
            if (!string.IsNullOrEmpty(comment))
            {
                ticketObj.ticket.comment = new ZendDeskComment()
                {
                    html_body = comment,
                    _public = !internalComment
                };
            }
            IRestResponse response = GetResponse($"api/v2/tickets/{ticketId}.json", Method.PUT, ticketObj);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                return response.Content;
            }
            else
            {
                return null;
            }
        }

        public void HandleDuplicateTickets(int originalTicketId, string email = null, string url = null)
        {
            ZendeskTicketObject tickets = GetUserOpenTickets(email, url);
            if (tickets.count > 1)
            {
                List<int> ids = new List<int>();
                var tk = tickets.results.Where(t => t.id != originalTicketId).OrderByDescending(t => t.created_at).Take(4);
                foreach (var ticket in tk)
                {
                    AddTicketComment(ticket.id, $"This customer has more open tickets:<a href=\"https://storeya.zendesk.com/agent/tickets/{originalTicketId}\" >{originalTicketId}</a>");
                    ids.Add(ticket.id);
                }
                string comment = string.Empty;
                foreach (int id in ids)
                {
                    comment = $"<a href=\"https://storeya.zendesk.com/agent/tickets/{id}\" >{id}</a>,{comment}";
                }
                if (!string.IsNullOrEmpty(comment))
                {
                    AddTicketComment(originalTicketId, "This customer has more open tickets:" + comment);
                }
            }
        }
        public ZendeskTicketObject GetUserOpenTickets(string email = null, string url = null)
        {
            string query = $"status<solved requester:{email} type:ticket";
            if (string.IsNullOrEmpty(email) && !string.IsNullOrEmpty(url) && url.ToLower() != "none" && url.ToLower() != "na")
            {
                query = $"status<solved custom_field_{CUSTOM_FIELD_URL}:{url} type:ticket";
            }
            IRestResponse response = SearchFor(query);
            if (response != null && !string.IsNullOrEmpty(response.Content))
            {
                ZendeskTicketObject tickets = response.Content.FromJson<ZendeskTicketObject>();
                return tickets;
            }
            return null;
        }
        public List<ZendeskTicket> GetTicketsOf(string email)
        {
            List<ZendeskTicket> tickets = null;

            string query = string.Format("requester:{0} type:ticket", email);
            IRestResponse response = SearchFor(query);
            if (response != null && !string.IsNullOrEmpty(response.Content))
            {
                dynamic parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(response.Content);

                //count	integer	The total number of results matching this query
                //next_page	string	URL to the next page of results
                //prev_page	string	URL to the previous page of results

                if (parsedJson.results != null)
                {
                    foreach (var item in parsedJson.results)
                    {
                        if (tickets == null)
                            tickets = new List<ZendeskTicket>();

                        ZendeskTicket ticket = new ZendeskTicket();
                        ticket.Url = item.url.ToString();
                        ticket.CreatedAt = DateTime.Parse(item.created_at.ToString());
                        ticket.Subject = item.subject.ToString();
                        ticket.Description = new HtmlString(item.description.ToString());
                        ticket.Status = item.status.ToString();
                        tickets.Add(ticket);
                    }
                }
            }
            return tickets;
        }
        public ZendDeskUser GetUser(string user_id)
        {
            ZendDeskUser user = null;
            string query = $"/api/v2/users/{user_id}"; ///{user_id}  /identities
            IRestResponse response = GetResponse(query, Method.GET);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                ZendDeskUserObject zendDeskUserObject = response.Content.FromJson<ZendDeskUserObject>();
                if (zendDeskUserObject.user != null && zendDeskUserObject.user.id != 0)
                {
                    user = zendDeskUserObject.user;
                }
                else
                {
                    return null;
                }
            }
            return user;
        }

        public IRestResponse SearchFor(string query)
        {
            //https://developer.zendesk.com/rest_api/docs/core/search

            IRestResponse response = GetResponse(string.Format("/api/v2/search.json?query={0}", query), Method.GET);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                return response;
            }
            else
            {
                if (response.Content != null && response.Content.ToLower().Contains("error"))
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "ZenDeskApi. Can't get response.", response.Content);
                }
                return null;
            }
        }


        public static bool OpenTicketFromContactForm(string userEmail, string userName, string message, string subject = null)
        {

            string requestUri = "https://storeya.zendesk.com/";
            string apiUsername = ConfigHelper.GetValue("ZenDeskApi_Username");
            string api_token = ConfigHelper.GetValue("ZenDesk_API_Token");

            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            var client = new RestSharp.RestClient(requestUri);
            client.Authenticator = new HttpBasicAuthenticator(apiUsername + "/token", api_token);

            var request = new RestRequest("/api/v2/tickets.json", Method.POST);
            request.AddHeader("Accept", "application/json");
            request.Parameters.Clear();


            NewTicket createTicket = new NewTicket();
            createTicket.ticket = new Ticket
            {
                subject = (!string.IsNullOrEmpty(subject) ? subject : ("Contact form application from " + userEmail)),
                status = "open",
                type = "incedent",
                //priority = "high",
                comment = new Comment { value = message },
                requester = new Requester { email = userEmail, name = userName }
            }
            //requester = new RestRequest(
            ;

            request.RequestFormat = RestSharp.DataFormat.Json;
            request.AddBody(createTicket);

            IRestResponse response = client.Execute(request);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.Created)
            {
                return true;
            }
            else
            {
                if (response != null)
                {
                    Log4NetLogger.Error(string.Format("Failed to Open Ticket From Contact Form for Name: {0}, Email: {1}, Message: {2}. StatusCode: {3}, RequestContent:{4}", userName, userEmail, message, response.StatusCode, response.Content));
                }
                else
                {
                    Log4NetLogger.Error(string.Format("Failed to Open Ticket From Contact Form for Name: {0}, Email: {1}, Message: {2}.", userName, userEmail, message));
                }
                return false;
            }
        }

        public IRestResponse GetCategories()
        {
            IRestResponse response = GetResponse("/api/v2/help_center/categories.json", Method.GET);//GetResponse("/api/v2/categories.json");
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.Created)
            {
                return response;
            }
            else
            {
                return null;
            }
        }


        public IRestResponse CreateCategory(HelpCenterCategory category)
        {
            //POST /api/v2/help_center/categories.json
            IRestResponse response = GetResponse("/api/v2/help_center/categories.json", Method.POST, category);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.Created)
            {
                return response;
            }
            else
            {
                return null;
            }
        }

        public IRestResponse CreateSection(int categoryID, HelpCenterSection section)
        {
            //POST /api/v2/help_center/categories/{id}/sections.json
            IRestResponse response = GetResponse(string.Format("/api/v2/help_center/categories/{0}/sections.json", categoryID), Method.POST, section);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.Created)
            {
                return response;
            }
            else
            {
                return null;
            }
        }

        public IRestResponse CreateArticleInSection(int sectionID, HelpCenterArticle article)
        {
            //POST /api/v2/help_center/sections/{id}/articles.json         

            IRestResponse response = GetResponse(string.Format("/api/v2/help_center/sections/{0}/articles.json", sectionID), Method.POST, article);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.Created)
            {
                return response;
            }
            else
            {
                return null;
            }
        }



        public IRestResponse GetForums()
        {
            IRestResponse response = GetResponse("/api/v2/forums.json", Method.GET);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.Created)
            {
                return response;
            }
            else
            {
                return null;
            }
        }

        public IRestResponse GetTopics()
        {
            IRestResponse response = GetResponse("/api/v2/topics.json", Method.GET);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.Created)
            {
                return response;
            }
            else
            {
                return null;
            }
        }
        private Macros UpdateMacros(int id, string action, string actionTitile)
        {
            UpdateMacrosAction macrosAction = new UpdateMacrosAction()
            {
                macro = new UpdateMacroAction()
                {
                    actions = new Action[] {
                        new Action() {
                            field = actionTitile,
                            value = action
                        }
                    },
                }
            };
            IRestResponse response = GetResponse($"/api/v2/macros/{id}.json", Method.PUT, macrosAction);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                return response.Content.FromJson<Macros>();
            }
            else
            {
                return null;
            }
        }
        private Macros GetMacro(int page = 1)
        {
            IRestResponse response = GetResponse($"/api/v2/macros.json?page={page}", Method.GET);
            if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
            {

                return response.Content.FromJson<Macros>();
            }
            else
            {
                return null;
            }
        }
        private List<Macros> GetMacros()
        {
            List<Macros> allPages = new List<Macros>();

            var mc = GetMacro();
            if (mc != null)
            {
                allPages.Add(mc);
                while (mc.next_page != null)
                {
                    int pageId = int.Parse(mc.next_page.Replace("https://storeya.zendesk.com/api/v2/macros.json?page=", ""));
                    mc = GetMacro(pageId);
                    allPages.Add(mc);
                }
            }
            return allPages;
        }

        public List<MacroAction> GetMacrosAction()
        {
            var macrosList = GetMacros();
            List<MacroAction> actions = new List<MacroAction>();
            foreach (var macros in macrosList)
            {
                var mc = macros.macros;
                foreach (var item in mc)
                {
                    MacroAction action = new MacroAction();
                    action.id = item.id;
                    action.title = item.title;
                    action.Url = $"https://storeya.zendesk.com/admin/workspaces/agent-workspace/macros/{item.id}";
                    //if (item.actions.First().value is string)
                    //{
                    //    action.action = item.actions.First().value;
                    //}
                    //else
                    //{
                    //    action.action = item.actions.First().value[1];
                    //}
                    actions.Add(action);
                }
            }
            return actions;
        }

        public IRestResponse GetResponse(string requestUrl, Method method, object objectToPost = null)
        {
            var client = new RestSharp.RestClient(_apiEndPoint);
            client.Authenticator = new HttpBasicAuthenticator(_apiUsername + "/token", _api_token);
            var request = new RestRequest(requestUrl, method);
            request.AddHeader("Accept", "application/json");
            request.Parameters.Clear();

            if (method == Method.POST || method == Method.PUT)
            {
                string json = objectToPost.ToJson();
                request.AddParameter("application/json; charset=utf-8", json, ParameterType.RequestBody);
            }

            IRestResponse response = client.Execute(request);
            return response;
        }



    }

    public class NewTicket
    {
        public Ticket ticket { get; set; }
    }
    public class Ticket
    {
        public string subject { get; set; }
        public Comment comment { get; set; }
        public Requester requester { get; set; }
        public string priority { get; set; }
        public string type { get; set; }
        public string status { get; set; }
    }
    public class Comment
    {
        public string value { get; set; }
    }

    public class Requester
    {
        public string email { get; set; }
        public string name { get; set; }
    }

    public class HelpCenterArticle
    {
        public string url { get; set; }
        public int id { get; set; }
        public string title { get; set; }
        public string body { get; set; }
        public int section_id { get; set; }
        public string locale { get; set; }
    }

    public class HelpCenterCategory
    {
        public string name { get; set; }
        public int id { get; set; }
        public string description { get; set; }
        public string locale { get; set; }
        public string source_locale { get; set; }
        public int position { get; set; }
    }

    public class HelpCenterSection
    {
        public int id { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public string locale { get; set; }
        public string source_locale { get; set; }
        public int category_id { get; set; }
    }



    public class ZendeskTicketObject
    {
        public ZendeskResult[] results { get; set; }
        public object facets { get; set; }
        public object next_page { get; set; }
        public object previous_page { get; set; }
        public int count { get; set; }
    }
    public class ZendeskTicketUsers
    {
        List<ZendescUser> users { get; set; }
    }
    public class ZendescUser
    {
        public int id { get; set; }
        public string name { get; set; }
    }
    public class ZendeskResult
    {
        public string url { get; set; }
        public int id { get; set; }
        public object external_id { get; set; }
        public ZendeskVia via { get; set; }
        public DateTime created_at { get; set; }
        public DateTime updated_at { get; set; }
        public string type { get; set; }
        public string subject { get; set; }
        public string raw_subject { get; set; }
        public string description { get; set; }
        public object priority { get; set; }
        public string status { get; set; }
        public string recipient { get; set; }
        public long requester_id { get; set; }
        public long submitter_id { get; set; }
        public int assignee_id { get; set; }
        public object organization_id { get; set; }
        public int group_id { get; set; }
        public object[] collaborator_ids { get; set; }
        public object[] follower_ids { get; set; }
        public object[] email_cc_ids { get; set; }
        public object forum_topic_id { get; set; }
        public object problem_id { get; set; }
        public bool has_incidents { get; set; }
        public bool is_public { get; set; }
        public object due_at { get; set; }
        public string[] tags { get; set; }
        public ZendeskCustomFields[] custom_fields { get; set; }
        public object satisfaction_rating { get; set; }
        public object[] sharing_agreement_ids { get; set; }
        public ZendeskField[] fields { get; set; }
        public object[] followup_ids { get; set; }
        public int brand_id { get; set; }
        public bool allow_channelback { get; set; }
        public bool allow_attachments { get; set; }
        public string result_type { get; set; }
    }

    public class ZendeskVia
    {
        public string channel { get; set; }
        public ZendeskSource source { get; set; }
    }

    public class ZendeskSource
    {
        public ZendeskFrom from { get; set; }
        public ZendeskTo to { get; set; }
        public string rel { get; set; }
    }

    public class ZendeskFrom
    {
        public string address { get; set; }
        public string name { get; set; }
    }

    public class ZendeskTo
    {
        public string name { get; set; }
        public string address { get; set; }
    }

    public class ZendeskCustomFields
    {
        public long id { get; set; }
        public string value { get; set; }
    }

    public class ZendeskField
    {
        public long id { get; set; }
        public string value { get; set; }
    }



    public class Macros
    {
        public List<Macro> macros { get; set; }
        public string next_page { get; set; }
        public object previous_page { get; set; }
        public int count { get; set; }
    }

    public class Macro
    {
        public string url { get; set; }
        public long id { get; set; }
        public string title { get; set; }
        public bool active { get; set; }
        public DateTime updated_at { get; set; }
        public DateTime created_at { get; set; }
        public bool _default { get; set; }
        public int position { get; set; }
        public string description { get; set; }
        public Action[] actions { get; set; }
        public string restriction { get; set; }
        public string raw_title { get; set; }
    }

    public class Action
    {
        public string field { get; set; }
        public dynamic value { get; set; }
    }

    public class MacroAction
    {
        public long id { get; set; }
        public string title { get; set; }
        public string Url { get; set; }

        public string action { get; set; }
    }


    public class UpdateMacrosAction
    {
        public UpdateMacroAction macro { get; set; }
    }

    public class UpdateMacroAction
    {
        public Action[] actions { get; set; }
        //public string title { get; set; }
    }







}
