﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Helpers
{
    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class ZendesKCommentsObject
    {
        public List<CommentZendesK> comments { get; set; }
        public string next_page { get; set; }
        public string previous_page { get; set; }
        public int? count { get; set; }
    }
    public class _3
    {
        public bool trusted { get; set; }
    }

    public class CommentZendesK
    {
        public object id { get; set; }
        public string type { get; set; }
        public string author_id { get; set; }
        public string body { get; set; }
        public string html_body { get; set; }
        public string plain_body { get; set; }
        public bool @public { get; set; }
        public List<object> attachments { get; set; }
        public string audit_id { get; set; }
        public Via via { get; set; }
        public DateTime created_at { get; set; }
        public Metadata metadata { get; set; }
    }

    public class Custom
    {
    }

    public class FlagsOptions
    {
        public _3 _3 { get; set; }
    }

    public class From
    {
        public string address { get; set; }
        public string name { get; set; }
        public List<string> original_recipients { get; set; }
    }

    public class Metadata
    {
        public CommentZendesKSystem system { get; set; }
        public Custom custom { get; set; }
        public List<int> flags { get; set; }
        public FlagsOptions flags_options { get; set; }
        public bool trusted { get; set; }
        public object suspension_type_id { get; set; }
    }


    public class Source
    {
        public From from { get; set; }
        public To to { get; set; }
        public string rel { get; set; }
    }

    public class CommentZendesKSystem
    {
        public string client { get; set; }
        public string ip_address { get; set; }
        public string location { get; set; }
        public double latitude { get; set; }
        public double longitude { get; set; }
        public string message_id { get; set; }
        public string email_id { get; set; }
        public string raw_email_identifier { get; set; }
        public string json_email_identifier { get; set; }
        public bool? eml_redacted { get; set; }
    }

    public class To
    {
        public string name { get; set; }
        public object address { get; set; }
    }

    public class Via
    {
        public string channel { get; set; }
        public Source source { get; set; }
    }
    public class Tags
    {
        public List<string> tags { get; set; }
    }
}
