﻿using Storeya.Core.Helpers;
using Storeya.Core.Models.ProductDescriber;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static Storeya.Core.Models.AISeoProduct.AIProduct;

namespace Storeya.Core.Models.AISeoProduct
{
    public class AIProductEmphasePhrase
    {
        public class EmphasePhraseData
        {
            public bool Show { get; set; }
            public string Value { get; set; }
            public string Text { get; set; }
            public int Sort { get; set; }
        }
        private int lastIndex = 0;

        private static Random random = new Random();

        private static AIProductEmphasePhrase instance = null;

        public static AIProductEmphasePhrase GetInstance()
        {
            if (instance == null)
            {
                instance = new AIProductEmphasePhrase();
            }
            return instance;
        }

        public string GetRandom(bool show = false)
        {
            List<EmphasePhraseData> phrases = Phrases.Where(c => c.Show == show).ToList();

            int randomIndex = random.Next(phrases.Count);
            while (randomIndex == lastIndex)
            {
                randomIndex = random.Next(phrases.Count);
            }
            lastIndex = randomIndex;
            string randomString = phrases[randomIndex].Value;
            Console.WriteLine($"{randomIndex}- {randomString}");
            return randomString;

        }
        public static List<EmphasePhraseData> Phrases = new List<EmphasePhraseData>() {
            new EmphasePhraseData(){
                Show = false,
                Value = "Write it as a formal description.",
            },
            new EmphasePhraseData(){
                Show = false,
                Value = "Write it as an innovative description.",
            },
            new EmphasePhraseData(){
                Show = false,
                Value = "Suggest a stylish description for an attractive e-commerce store.",
            },
            new EmphasePhraseData(){
                Show = false,
                Value = "Write a reliable description.",
            },
            new EmphasePhraseData(){
                Show = false,
                Value = "Write a Multi-Purpose description for this eCommerce store.",
            },
            new EmphasePhraseData(){
                Show = false,
                Value = "Draft a clean and clear description.",
            },
            new EmphasePhraseData(){
                Show = true,
                Value = "",
                Text = "Standard tone",
                Sort = 0
            },
            new EmphasePhraseData(){
                Show = true,
                Value = "emphasize the product's fast delivery.",
                Text = "Focus on fast delivery",
                Sort = 1
            },
            new EmphasePhraseData(){
                Show = true,
                Value = "emphasize the product's price.",
                Text = "Focus on price",
                Sort = 2
            },
            new EmphasePhraseData(){
                Show = true,
                Value = "emphasize the product's uniqueness.",
                Text = "Focus on the product’s uniqueness",
                Sort = 3
            },
            new EmphasePhraseData(){
                Show = true,
                Value = "write products description as if Shakespeare wrote it.",
                Text = "Shakespearean tone",
                Sort = 4
            },
            new EmphasePhraseData(){
                Show = true,
                Value = "write the product description as if a comedian wrote a joke.",
                Text = "Funny tone",
                Sort = 5
            },
             new EmphasePhraseData(){
                Show = true,
                Value = "write the product description in a romantic tone.",
                Text = "Romantic tone",
                Sort = 6
            },



        };


    }
    public class AIProduct
    {

        public enum PromptType
        {
            All = 0,
            Title = 1,
            MetaTitle = 2,
            MetaDescription = 3,
            Description = 4
        }

        public long PID { get; set; }
        public int ShopId { get; set; }
        public string Name { get; set; }
        public string SiteData { get; set; }
        public string Intro { get; set; }
        public string Url { get; set; }
        public string UrlText { get; set; }
        public string Text { get; set; }
        public string GenerateText { get; set; }
        public string EmphasePhrase { get; set; }
        public string TextStyle { get; set; }
        public string Prefix { get; set; }
        public string Query { get; set; }
        public bool Error { get; set; }
        public int RetryCount { get; set; }
        public PromptType SelectedPromptType { get; set; }
        public string PromptTypeName { get; set; }

        private string GetIntro(PromptType promptType)
        {

            switch (promptType)
            {
                case PromptType.All:
                    return "for the following product info:";
                case PromptType.Title:
                    return "for the following product info:";
                case PromptType.MetaTitle:
                    return "for the following product info:";
                case PromptType.MetaDescription:
                    return "for the following product info:";
                case PromptType.Description:
                    return "write a total of 90 words split into paragraphs describing the product.";

                default:
                    return "for the following product info:";
            }
        }

        private string GetTextStyle(PromptType promptType)
        {
            switch (promptType)
            {
                case PromptType.All:
                    return ConfigHelper.GetValue("AISEO_GPT_All_TextStyle", $"Suggest revised text for title, description and meta description separately - all in order to make the most SEO-wise and explain why you changed what you changed.Limitations and output:Title – up to 60 characters;Description - up to 90 words split into paragraphs; Tone: Write it as a formal description; Structure the output as HTML but do not add links or image tags and use the same language used in the title; Meta description - up to 155 characters of plain text. Output:a list showing subject and suggested text");
                case PromptType.Title:
                    return ConfigHelper.GetValue($"AISEO_GPT_Title_TextStyle", $"Suggest revised text for title all in order to make the most SEO-wise and explain why you changed what you changed.\r\nLimitations and output:Title – up to 60 characters use the same language used in the title;  Output: suggested text");
                case PromptType.MetaTitle:
                    return ConfigHelper.GetValue($"AISEO_GPT_MetaTitle_TextStyle", $"Suggest revised meta text for title all in order to make the most SEO-wise and explain why you changed what you changed.\r\nLimitations and output:Title – up to 60 characters use the same language used in the title;  Output: suggested text");
                case PromptType.MetaDescription:
                    return ConfigHelper.GetValue($"AISEO_GPT_MetaDescription_TextStyle", $"Suggest revised text for meta description all in order to make the most SEO-wise and explain why you changed what you changed.\r\nLimitations and output:Meta description - up to 155 characters of plain text use the same language used in the title;  Output: suggested text");
                case PromptType.Description:
                    return ConfigHelper.GetValue("AISEO_GPT_TextStyle", $"Structure the output as html but do not add links or image tags and use same language as title");
                default:
                    return ConfigHelper.GetValue("AISEO_GPT_All_TextStyle", $"Suggest revised text for title, description and meta description separately - all in order to make the most SEO-wise and explain why you changed what you changed.Limitations and output:Title – up to 60 characters;Description - up to 90 words split into paragraphs; Tone: Write it as a formal description; Structure the output as HTML but do not add links or image tags and use the same language used in the title; Meta description - up to 155 characters of plain text. Output:a list showing subject and suggested text");
            }
        }
        private string GetPrefix(PromptType promptType)
        {
            switch (promptType)
            {

                case PromptType.Title:
                    return "Product title: ";
                case PromptType.MetaTitle:
                    return "Meta title: ";
                case PromptType.MetaDescription:
                    return "Meta description: ";
                case PromptType.Description:
                    return "Description: ";
                case PromptType.All:
                default:
                    return "";
            }

        }

        public AIProduct(long pid, int shopID, string name, string url, string text,
        PromptType promptType,
        string emphasePhrase, string textStyle = "",
        string intro = null, string siteData = "", string prefix = null)
        {
            this.PromptTypeName = $"{promptType}";
            this.SelectedPromptType = promptType;
            this.PID = pid;
            this.ShopId = shopID;
            if (name == null)
            {
                name = "";
            }
            if (name.ToLower().StartsWith("product title:"))
            {
                this.Name = name;
            }
            else
            {
                this.Name = $"Product title: {name}.";
            }
            this.Url = url;
            this.UrlText = $"product URL: {url} .";

            if (text == null)
            {
                text = "";
            }
            string textPrefix = GetPrefix(promptType);
            if (text.ToLower().StartsWith(textPrefix))
            {
                this.Text = text;
            }
            else
            {
                this.Text = $"{textPrefix} {text}";
            }

            this.EmphasePhrase = emphasePhrase;
            if (string.IsNullOrEmpty(textStyle))
            {
                this.TextStyle = GetTextStyle(promptType);
            }
            else
            {
                this.TextStyle = textStyle;
            }
            this.Prefix = prefix;
            if (string.IsNullOrEmpty(intro))
            {
                this.Intro = GetIntro(promptType);
            }
            else
            {
                this.Intro = intro;
            }
            this.SiteData = "";
            if (string.IsNullOrEmpty(siteData))
            {
                var pdRecord = AISeoProductOptimizerManager.GetAISeoProductOptimizer(shopID);
                if (pdRecord != null && string.IsNullOrEmpty(pdRecord.MetaTitle))
                {
                    if (!string.IsNullOrEmpty(pdRecord.MetaTitle))
                    {
                        this.SiteData = $"Site title: {pdRecord.MetaTitle}.";
                    }
                }
            }
            else
            {
                if (SiteData.ToLower().StartsWith("site title:"))
                {
                    this.SiteData = siteData;
                }
                else
                {
                    this.SiteData = $"Site title: {siteData}";
                }
            }
            if (string.IsNullOrEmpty(this.EmphasePhrase))
            {
                this.EmphasePhrase = AIProductEmphasePhrase.GetInstance().GetRandom();
            }
        }
        public Dictionary<PromptType, string> GetAllResults()
        {
            return ParseAllResults(this.GenerateText);
        }

        public string GetPrompt()
        {

            string productData = $"{this.Name} {this.UrlText}";
            if (!string.IsNullOrEmpty(this.Text))
            {
                productData = $"{productData} {this.Text}.";
            }
            string query = $"{this.Intro}  {productData} {this.SiteData}. {this.EmphasePhrase} {this.TextStyle} {this.Prefix}";
            this.Query = query;
            return query;
        }
        static Dictionary<PromptType, string> ParseAllResults(string input)
        {
            input = input.Replace("\"", "");
            Dictionary<PromptType, string> dictionary = new Dictionary<PromptType, string>()
            {
                { PromptType.Title, "" },
                { PromptType.Description, "" },
                { PromptType.MetaDescription, ""},
                { PromptType.MetaTitle, "" }
            };

            // Define prefixes to split on
            string[] prefixes = { "Title:", "Description:", "Meta Description:" };

            // Keep track of the current position in the input string
            int currentPosition = 0;

            foreach (string prefix in prefixes)
            {
                // Find the start of the current prefix
                int startIndex = input.IndexOf(prefix, currentPosition);
                if (startIndex != -1)
                {
                    // Find the end of the current prefix
                    int endIndex = -1;
                    foreach (string nextPrefix in prefixes)
                    {
                        if (nextPrefix != prefix)
                        {
                            int nextIndex = input.IndexOf(nextPrefix, startIndex + prefix.Length);
                            if (nextIndex != -1 && (endIndex == -1 || nextIndex < endIndex))
                            {
                                endIndex = nextIndex;
                            }
                        }
                    }

                    if (endIndex == -1)
                    {
                        endIndex = input.Length;
                    }

                    // Extract the key and value
                    string key = prefix.Replace(":", "").Trim();
                    PromptType promptType = PromptType.All;
                    if (key.ToLower() == "title")
                    {
                        promptType = PromptType.Title;
                    }
                    else if (key.ToLower() == "description")
                    {
                        promptType = PromptType.Description;
                    }
                    else if (key.ToLower() == "meta description")
                    {
                        promptType = PromptType.MetaDescription;
                    }
                    string value = input.Substring(startIndex + prefix.Length, endIndex - startIndex - prefix.Length).Trim().Trim('"');

                    // Add to the dictionary
                    dictionary[promptType] = value;

                    // Update the current position
                    currentPosition = endIndex;
                }
            }
            dictionary[PromptType.MetaTitle] = dictionary[PromptType.Title];
            return dictionary;
        }
        public static Dictionary<PromptType, string> ParseAllResultsREGEX(string input)
        {
            input = input.Replace("\"", "");
            Dictionary<PromptType, string> dictionary = new Dictionary<PromptType, string>()
            {
                { PromptType.Title, "" },
                { PromptType.Description, "" },
                { PromptType.MetaDescription, ""},
                { PromptType.MetaTitle, "" }
            };

            // Define the regex pattern to match key-value pairs
            // string pattern = @"^(Title|Description|Meta Description):\s*""([^""]*)""";
            string pattern = @"^(Title|Description|Meta Description):\s*(.*?)(?=(\r?\n[A-Z][a-z]+:|$))";

            // Use regex to find matches in the input string
            MatchCollection matches = Regex.Matches(input, pattern, RegexOptions.Singleline);
            // Process each match
            foreach (Match match in matches)
            {
                if (match.Groups.Count > 3)
                {
                    string key = match.Groups[1].Value;
                    PromptType promptType = PromptType.All;
                    if (key.ToLower() == "title")
                    {
                        promptType = PromptType.Title;
                    }
                    else if (key.ToLower() == "description")
                    {
                        promptType = PromptType.Description;
                    }
                    else if (key.ToLower() == "meta description")
                    {
                        promptType = PromptType.MetaDescription;
                    }
                    string value = match.Groups[2].Value;
                    // Add to the dictionary
                    dictionary[promptType] = value;
                }
            }
            dictionary[PromptType.MetaTitle] = dictionary[PromptType.Title];
            return dictionary;
        }
    }
}

