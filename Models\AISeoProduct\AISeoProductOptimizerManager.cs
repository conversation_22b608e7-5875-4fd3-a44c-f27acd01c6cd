﻿using Storeya.Core.Entities;
using Storeya.Core.Helpers;
using Storeya.Core.Models.ChatGPT;
using Storeya.Core.Models.DataProviders;
using Storeya.Core.Models.Marketplaces;
using Storeya.Core.Models.ShopAttributes;
using System;
using System.Collections.Generic;
using System.Data.Entity.Validation;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.ComTypes;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static Storeya.Core.Models.AISeoProduct.AIProduct;

namespace Storeya.Core.Models.AISeoProduct
{
    public enum AISeoProductOptimizerAppStatus
    {
        Install = 1,
        Canceled = -1
    }

    public enum AISeoProductOptimizerHistoryStatus
    {
        Failed = -1,
        Generated = 1,
        Regenerated = 2,
        Updated = 3,
        Restored = 4,
    }
    public class AISeoProductOptimizerManager
    {
        public static AISeoProductOptimizer GetAISeoProductOptimizer(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            return db.AISeoProductOptimizers.SingleOrDefault(x => x.ShopId == shopId);
        }
        public static AISeoProductOptimizerHistory GetAISeoProductOptimizerHistory(int shopId, long productId)
        {
            var db = DataHelper.GetStoreYaEntities();
            return db.AISeoProductOptimizerHistories.SingleOrDefault(x => x.ShopId == shopId && x.ProductID == productId);
        }
        public static List<AISeoProductOptimizerHistory> GetProducts(int shopId, AISeoProductOptimizerHistoryStatus status)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                return db.AISeoProductOptimizerHistories.Where(x => x.ShopId == shopId && x.Status == (int)status).ToList();
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public static AISeoProductOptimizerHistory ManageAISeoProductOptimizerHistory(int shopId, long productId,
            string description = null, string newDescription = null,
              string title = null, string newTitle = null,
                string metaDescription = null, string newMetaDescription = null,
                  string metaTitle = null, string newMetaTitle = null,
            string typeOfPrompt = null, decimal? apiResponseTime = null,
            AISeoProductOptimizerHistoryStatus status = AISeoProductOptimizerHistoryStatus.Generated,
            string aIProductJson = null, int? retryCount = null, string productUrl = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var aISeoProductOptimizer = db.AISeoProductOptimizerHistories.SingleOrDefault(x => x.ShopId == shopId && x.ProductID == productId);
            try
            {
                if (aISeoProductOptimizer == null)
                {
                    AISeoProductOptimizerHistory AISeoProductOptimizer = new AISeoProductOptimizerHistory()
                    {
                        InsertedAt = DateTime.Now,
                        ShopId = shopId,
                        ProductID = productId,
                        ProductUrl = productUrl,
                        Description = description,
                        GeneratedDescription = newDescription,
                        GeneratedMetaDescription = newMetaDescription,
                        GeneratedMetaTitle = newMetaTitle,
                        GeneratedTitle = newTitle,
                        MetaDescription = metaDescription,
                        MetaTitle = metaTitle,
                        Title = title,
                        TypeOfPrompt = typeOfPrompt,
                        ApiResponseTime = apiResponseTime,
                        Status = status.GetHashCode(),
                        AIProduct = aIProductJson,
                        RetryCount = retryCount
                    };
                    db.AISeoProductOptimizerHistories.Add(AISeoProductOptimizer);
                    db.SaveChanges();
                    return AISeoProductOptimizer;
                }
                else
                {
                    aISeoProductOptimizer.UpdatedAt = DateTime.Now;
                    if (status == AISeoProductOptimizerHistoryStatus.Generated)
                    {
                        aISeoProductOptimizer.Status = AISeoProductOptimizerHistoryStatus.Regenerated.GetHashCode();
                    }
                    else
                    {
                        aISeoProductOptimizer.Status = (int)status;
                    }
                    //if (string.IsNullOrEmpty(productDescriber.Description))
                    //{
                    //    productDescriber.Description = description;
                    //}
                    if (!string.IsNullOrEmpty(newTitle))
                    {
                        aISeoProductOptimizer.GeneratedTitle = newTitle;
                    }
                    if (!string.IsNullOrEmpty(newMetaTitle))
                    {
                        aISeoProductOptimizer.GeneratedMetaTitle = newMetaTitle;
                    }
                    if (!string.IsNullOrEmpty(newMetaDescription))
                    {
                        aISeoProductOptimizer.GeneratedMetaDescription = newMetaDescription;
                    }
                    if (!string.IsNullOrEmpty(newDescription))
                    {
                        aISeoProductOptimizer.GeneratedDescription = newDescription;
                    }
                    if (!string.IsNullOrEmpty(typeOfPrompt))
                    {
                        aISeoProductOptimizer.TypeOfPrompt = typeOfPrompt;
                    }

                    if (apiResponseTime.HasValue)
                    {
                        aISeoProductOptimizer.ApiResponseTime = apiResponseTime;
                    }
                    if (retryCount.HasValue)
                    {
                        aISeoProductOptimizer.RetryCount = retryCount;
                    }
                    if (!string.IsNullOrEmpty(aIProductJson))
                    {
                        aISeoProductOptimizer.AIProduct = aIProductJson;
                    }
                    if (!string.IsNullOrEmpty(productUrl))
                    {
                        aISeoProductOptimizer.ProductUrl = productUrl;
                    }
                }
                db.SaveChanges();

            }
            catch (DbEntityValidationException e)
            {
                var newException = new FormattedDbEntityValidationException(e);
                Log4NetLogger.Error(newException.Message, newException, shopId);
                throw (e);
            }
            return aISeoProductOptimizer;
        }
        public static bool AddAISeoProductOptimizer(int shopId, string shopUrl, string shopifyShopName, string metaTitle, string metaDescription, AISeoProductOptimizerAppStatus status = AISeoProductOptimizerAppStatus.Install)
        {
            var db = DataHelper.GetStoreYaEntities();
            var productDescriber = db.AISeoProductOptimizers.SingleOrDefault(x => x.ShopId == shopId);
            if (productDescriber == null)
            {
                AISeoProductOptimizer productDescribers = new AISeoProductOptimizer()
                {
                    InsertedAt = DateTime.Now,
                    ShopId = shopId,
                    MetaTitle = metaTitle,
                    MetaDescription = metaDescription,
                    ShopifyShopName = shopifyShopName,
                    ShopUrl = shopUrl,
                    Status = status.GetHashCode(),
                };
                db.AISeoProductOptimizers.Add(productDescribers);
                db.SaveChanges();
                return true;
            }
            return false;
        }
        public static void ManageAISeoProductOptimizer(int shopId, string shopUrl, string shopifyShopName, string metaTitle, string metaDescription, AISeoProductOptimizerAppStatus status = AISeoProductOptimizerAppStatus.Install)
        {
            var db = DataHelper.GetStoreYaEntities();
            var productDescriber = db.AISeoProductOptimizers.SingleOrDefault(x => x.ShopId == shopId);
            if (productDescriber == null)
            {
                AISeoProductOptimizer productDescribers = new AISeoProductOptimizer()
                {
                    InsertedAt = DateTime.Now,
                    ShopId = shopId,
                    MetaTitle = metaTitle,
                    MetaDescription = metaDescription,
                    ShopifyShopName = shopifyShopName,
                    ShopUrl = shopUrl,
                    Status = status.GetHashCode(),
                };
                db.AISeoProductOptimizers.Add(productDescribers);
            }
            else
            {
                productDescriber.UpdatedAt = DateTime.Now;
                productDescriber.MetaTitle = metaTitle;
                productDescriber.MetaDescription = metaDescription;
                productDescriber.ShopifyShopName = shopifyShopName;
                productDescriber.ShopUrl = shopUrl;
                productDescriber.Status = status.GetHashCode();
            }
            db.SaveChanges();
        }
        public static void UpdateAISeoProductOptimizer(int shopId, AISeoProductOptimizerAppStatus status)
        {
            var db = DataHelper.GetStoreYaEntities();
            var productDescriber = db.AISeoProductOptimizers.SingleOrDefault(x => x.ShopId == shopId);
            if (productDescriber != null)
            {
                productDescriber.UpdatedAt = DateTime.Now;
                productDescriber.Status = (int)status;
            }
            db.SaveChanges();
        }
        //get all Descriptor(sh
        //
        public static IQueryable<AISeoProductOptimizer> GetAllAISeoProductOptimizersQuery(int? shopID = null, int? status = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var query = db.AISeoProductOptimizers.Where(x => 1 == 1);
            if (shopID != null)
            {
                query = query.Where(x => x.ShopId == shopID);
            }
            if (status != null)
            {
                query = query.Where(x => x.Status == status);
            }
            return query.OrderByDescending(x => x.Id);
        }
        public static List<AISeoProductOptimizer> GetAllAISeoProductOptimizers(int? shopID = null, int? status = null)
        {
            var query = GetAllAISeoProductOptimizersQuery(shopID, status);
            List<AISeoProductOptimizer> productDescribers = query.ToList();
            return productDescribers;
        }
        public static IQueryable<AISeoProductOptimizerHistory> GetAllAISeoProductOptimizerHistoryQuery(int? shopID = null, int? status = null, string productId = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var query = db.AISeoProductOptimizerHistories.Where(x => 1 == 1);
            if (!string.IsNullOrEmpty(productId))
            {
                if (long.TryParse(productId, out long p))
                {
                    query = db.AISeoProductOptimizerHistories.Where(x => x.ProductID == p).OrderByDescending(x => x.Id);
                    return query;
                }

            }
            if (shopID.HasValue)
            {
                query = query.Where(x => x.ShopId == shopID);
            }
            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status);
                return query.OrderByDescending(x => x.Id);
            }
            return query.OrderByDescending(x => x.Id);
        }
        public static List<AISeoProductOptimizerHistory> GetAllAISeoProductOptimizerHistory(int? shopID = null, int? status = null, string productId = null)
        {

            return GetAllAISeoProductOptimizerHistoryQuery(shopID, status, productId).ToList();
        }


        //public static void TestSimpleProductsList(string typeOfPrompt = "")
        //{
        //    var db = DataHelper.GetStoreYaEntities();
        //    var shopsForShopify = db.ShopifyConnectedShops.Where(x => x.ShopID > 100000 && x.StoreyaAppTypeID == 4).OrderByDescending(x => x.ID).Select(x => x.ShopID).Take(10).ToArray();
        //    List<AIProduct> pDProducts = new List<AIProduct>();
        //    foreach (var shopID in shopsForShopify)
        //    {
        //        StringBuilder sb = new StringBuilder();
        //        List<Storeya.Core.Models.DataProviders.ShopifyEntities.ProductEntity> products = new List<Storeya.Core.Models.DataProviders.ShopifyEntities.ProductEntity>();
        //        try
        //        {
        //            ShopifyApiProvider api = ShopifyConnector.GetShopifyApiClient(shopID, (int)AppTypes.TrafficBooster);
        //            products = api.GetProducts(3);
        //        }

        //        catch
        //        {
        //            continue;
        //        }
        //        if (products.Count > 0)
        //        {
        //            foreach (var p in products)
        //            {
        //                try
        //                {
        //                    var res = GetDescriptionForProductTest(long.Parse(p.id), shopID, typeOfPrompt, AppTypes.TrafficBooster);
        //                    pDProducts.Add(res);
        //                    if (res.Text == "GPTchat overloaded")
        //                    {
        //                        break;
        //                    }
        //                }
        //                catch
        //                {
        //                    continue;
        //                }

        //            }
        //        }
        //    }
        //    pDProducts.ToCSV("c:\\test\\TestSimpleProductsList.csv");
        //    //List<string>lines=new List<string>();
        //    //lines.Add("ShopID^URL^Description^NewDescription");
        //    //foreach(var p in pDProducts)
        //    //{
        //    //    lines.Add($"{p.ID}^{p.Url}^{p.Description}^{p.NewDescription}");
        //    //}
        //    //File.WriteAllLines("c:\\test\\TestSimpleProductsList.csv", lines);
        //}
        public static AIProduct GetDescriptionForProductTest(long pid, int shopID, string emphasePhrase, AppTypes appType = AppTypes.AISeoProductOptimizer)
        {
            ShopifyApiProvider api = ShopifyConnector.GetShopifyApiClient(shopID, (int)appType);
            var p = api.GetProductDetails(pid.ToString());
            AIProduct productToChange = new AIProduct(pid, shopID, p.Name, p.Url, p.ShortDescription, AIProduct.PromptType.Description, emphasePhrase, null);
            string prompt = productToChange.GetPrompt();
            string newProductDescription;
            try
            {
                newProductDescription = ChatGPTManager.GetGptString(prompt);
            }
            catch (Exception ex)
            {
                newProductDescription = "An error occurred";
                Log4NetLogger.Error("Error gettting data from GPT. Shop - " + shopID, ex, (int)Log4NetLogger.SpecialShopIDs.AISeoProductOptimizer);

            }
            productToChange.GenerateText = newProductDescription.Replace("\n ", "").Replace("\n", "").Replace("         ", "").Replace("    ", "").Replace("  ", "");
            return productToChange;
        }

        public static AIProduct GetDescriptionForProduct(long pid, int shopID, string name, string url, string text,
            AIProduct.PromptType promptType,
            string emphasePhrase, string textStyle = "", int retryCount = 6, bool testError = false,
            string intro = null, string siteData = null, string prefix = null)
        {

            AIProduct productToChange = new AIProduct(pid, shopID, name, url, text, promptType, emphasePhrase, textStyle, intro, siteData: siteData);
            string prompt = productToChange.GetPrompt();
            string generateText = "";
            productToChange.RetryCount = 0;
            for (int i = 0; i < retryCount; i++)
            {
                productToChange.RetryCount++;
                try
                {
                    if (testError)
                    {
                        throw new Exception("Test GPT failed");
                    }
                    generateText = ChatGPTManager.GetGptString(prompt);
                    productToChange.Error = false;
                    productToChange.GenerateText = generateText;//.Replace("\n ", "</br>").Replace("\n", "").Replace("         ", "").Replace("    ", "").Replace("  ", "");

                    return productToChange;
                }
                catch (Exception ex)
                {
                    productToChange.Error = true;
                    Log4NetLogger.Error($"Error gettting data try:{i} from GPT. Shop - " + shopID + $" {productToChange.ToJson()}", ex, (int)Log4NetLogger.SpecialShopIDs.AISeoProductOptimizer);
                }

                System.Threading.Thread.Sleep(600);
            }
            productToChange.GenerateText = generateText;//.Replace("\n ", "</br>").Replace("\n", "").Replace("         ", "").Replace("    ", "").Replace("  ", "");
            return productToChange;
        }
        public static bool ProductsLimitWasReached(int shopID, int limit = 1000)
        {
            var db = DataHelper.GetStoreYaEntities();
            int count = db.AISeoProductOptimizerHistories.Count(c => c.ShopId == shopID);
            if (ShopAttributesManager.GetInstance(shopID).IsExists(Attributes.Apps.ProductDescriptionWizard.AllowUnlimitedProducts, out ShopAttributesValue shopAttributesValue))
            {
                if (int.TryParse(shopAttributesValue.Comment, out int addLimit))
                {
                    limit = addLimit;
                }
                else
                {
                    return false;
                }
            }
            if (count > limit)
            {
                return true;
            }
            return false;

        }
        public static List<AIProduct> GenerateProductAIDescription(int shopID, long pid, string title, string url,
            string description, string descriptionHtml,
             string metaTitle, string metaDescription,
            string typeOfPrompt, bool outpuAsHtml, string prefix, bool testError = false, bool readOnly = false, PromptType promptType = PromptType.All)
        {
            List<AIProduct> p = new List<AIProduct>();
            AISeoProductOptimizerHistoryStatus historyStatus = AISeoProductOptimizerHistoryStatus.Generated;
            DateTime now = DateTime.Now;
            int retryCount = 0;
            decimal apiResponseTime = 0;
            switch (promptType)
            {
                case PromptType.All:
                    AIProduct pAll = AISeoProductOptimizerManager.GetDescriptionForProduct(pid, shopID, title, url, title, PromptType.All, typeOfPrompt, testError: testError, prefix: prefix);
                    FixResults(outpuAsHtml, pAll);
                    retryCount += pAll.RetryCount;
                    if (pAll.Error)
                    {
                        pAll.GenerateText = "Our wizard is overloaded with description requests.<br/>Please try again in a few minutes.";
                        historyStatus = AISeoProductOptimizerHistoryStatus.Failed;
                    }
                    p.Add(pAll);
                    apiResponseTime = (decimal)DateTime.Now.Subtract(now).TotalMilliseconds;
                    Dictionary<PromptType, string> allResults = pAll.GetAllResults();
                    if (!readOnly)
                    {
                        ManageAISeoProductOptimizerHistory(shopID, pid,
                            title: title,
                            newTitle: allResults[PromptType.Title],
                            description: descriptionHtml,
                            newDescription: allResults[PromptType.Description],
                            metaTitle: metaTitle,
                            newMetaTitle: allResults[PromptType.MetaTitle],
                            metaDescription: descriptionHtml,
                            newMetaDescription: allResults[PromptType.MetaDescription],
                            apiResponseTime: apiResponseTime,
                            status: historyStatus,
                            aIProductJson: p.ToJson(),
                            retryCount: retryCount,
                            productUrl: url);
                    }
                    break;
                case PromptType.Title:
                    AIProduct pTitle = AISeoProductOptimizerManager.GetDescriptionForProduct(pid, shopID, title, url, title, PromptType.Title, typeOfPrompt, testError: testError, prefix: prefix);
                    FixResults(outpuAsHtml, pTitle);
                    retryCount += pTitle.RetryCount;
                    if (pTitle.Error)
                    {
                        pTitle.GenerateText = "Our wizard is overloaded with description requests.<br/>Please try again in a few minutes.";
                        historyStatus = AISeoProductOptimizerHistoryStatus.Failed;
                    }
                    p.Add(pTitle);
                    apiResponseTime = (decimal)DateTime.Now.Subtract(now).TotalMilliseconds;

                    if (!readOnly)
                    {
                        ManageAISeoProductOptimizerHistory(shopID, pid, title: title, newTitle: pTitle.GenerateText
                            , apiResponseTime: apiResponseTime,
                           status: historyStatus, aIProductJson: p.ToJson(), retryCount: retryCount, productUrl: url);
                    }
                    break;
                case PromptType.MetaTitle:
                    AIProduct pMetaTitle = AISeoProductOptimizerManager.GetDescriptionForProduct(pid, shopID, title, url, title, PromptType.MetaTitle, typeOfPrompt, testError: testError, prefix: prefix);
                    FixResults(outpuAsHtml, pMetaTitle);
                    retryCount += pMetaTitle.RetryCount;
                    if (pMetaTitle.Error)
                    {
                        pMetaTitle.GenerateText = "Our wizard is overloaded with description requests.<br/>Please try again in a few minutes.";
                        historyStatus = AISeoProductOptimizerHistoryStatus.Failed;
                    }
                    p.Add(pMetaTitle);
                    apiResponseTime = (decimal)DateTime.Now.Subtract(now).TotalMilliseconds;

                    if (!readOnly)
                    {
                        ManageAISeoProductOptimizerHistory(shopID, pid, metaDescription: metaTitle, newMetaTitle: pMetaTitle.GenerateText
                            , apiResponseTime: apiResponseTime,
                           status: historyStatus, aIProductJson: p.ToJson(), retryCount: retryCount, productUrl: url);
                    }
                    break;
                case PromptType.MetaDescription:
                    AIProduct pMetaDesc = AISeoProductOptimizerManager.GetDescriptionForProduct(pid, shopID, title, url, title, PromptType.MetaDescription, typeOfPrompt, testError: testError, prefix: prefix);
                    FixResults(outpuAsHtml, pMetaDesc);
                    retryCount += pMetaDesc.RetryCount;
                    if (pMetaDesc.Error)
                    {
                        pMetaDesc.GenerateText = "Our wizard is overloaded with description requests.<br/>Please try again in a few minutes.";
                        historyStatus = AISeoProductOptimizerHistoryStatus.Failed;
                    }
                    p.Add(pMetaDesc);

                    apiResponseTime = (decimal)DateTime.Now.Subtract(now).TotalMilliseconds;

                    if (!readOnly)
                    {
                        ManageAISeoProductOptimizerHistory(shopID, pid, metaDescription: metaDescription, newMetaDescription: pMetaDesc.GenerateText
                            , apiResponseTime: apiResponseTime,
                           status: historyStatus, aIProductJson: p.ToJson(), retryCount: retryCount, productUrl: url);
                    }

                    break;
                case PromptType.Description:
                    AIProduct pDescription = AISeoProductOptimizerManager.GetDescriptionForProduct(pid, shopID, title, url, description, PromptType.Description, typeOfPrompt, testError: testError, prefix: prefix);
                    FixResults(outpuAsHtml, pDescription);
                    retryCount = pDescription.RetryCount;
                    if (pDescription.Error)
                    {
                        pDescription.GenerateText = "Our wizard is overloaded with description requests.<br/>Please try again in a few minutes.";
                        historyStatus = AISeoProductOptimizerHistoryStatus.Failed;
                    }
                    p.Add(pDescription);
                    apiResponseTime = (decimal)DateTime.Now.Subtract(now).TotalMilliseconds;

                    if (!readOnly)
                    {
                        ManageAISeoProductOptimizerHistory(shopID, pid, description: descriptionHtml, newDescription: pDescription.GenerateText
                            , apiResponseTime: apiResponseTime,
                           status: historyStatus, aIProductJson: p.ToJson(), retryCount: retryCount, productUrl: url);
                    }
                    break;
                default:
                    break;
            }




            //  decimal apiResponseTime = (decimal)DateTime.Now.Subtract(now).TotalMilliseconds;

            //if (!readOnly)
            //{
            //    ManageAISeoProductOptimizerHistory(shopID, pid, descriptionHtml, pDescription.GenerateText,
            //        title, pTitle.GenerateText, metaDescription, pMetaDesc.GenerateText, metaTitle, pMetaTitle.GenerateText,
            //        typeOfPrompt, apiResponseTime,
            //        historyStatus, p.ToJson(), retryCount, url);
            //}
            return p;
        }

        private static void FixResults(bool outpuAsHtml, AIProduct p)
        {
            if (outpuAsHtml)
            {
                p.GenerateText = p.GenerateText.TrimStart('\r', '\n').Replace("\n\n", "").Replace("\n", "");
            }
            else
            {
                p.GenerateText = p.GenerateText.TrimStart('\r', '\n').Replace("\n\n", "</br>").Replace("\n", "</br>");
            }
            p.GenerateText = RemoveImgTag(p.GenerateText);
        }

        public static void FixProductDescriptions(int shopId, bool updateDescription = false, int? limit = null)
        {
            var shopifyApi = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.AISeoProductOptimizer);
            var histList = GetAllAISeoProductOptimizerHistory(shopId, AISeoProductOptimizerHistoryStatus.Updated.GetHashCode());
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"productId;pdProduct.Url;updatedRes;gptDescription;description;shopifyDescription");
            int count = 0;
            foreach (var item in histList)
            {
                try
                {
                    long productId = item.ProductID;
                    bool res = CompareDescription(updateDescription, shopifyApi, sb, item, productId);
                    if (!res)
                    {
                        count++;
                        if (limit.HasValue && count > limit)
                        {
                            Console.WriteLine($"Limit {limit} was reached.");
                            break;
                        }
                    }

                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{item.Id} -  {ex}");
                }
            }

            File.WriteAllText("c:\\temp\\FixProductDescriptions.csv", sb.ToString());
            Stream stream = new MemoryStream(File.ReadAllBytes("c:\\temp\\" + $"FixProductDescriptions.csv"));
            System.Net.Mail.Attachment amAttachment = new System.Net.Mail.Attachment(stream, $"FixProductDescriptions.csv");
            List<System.Net.Mail.Attachment> attachments = new List<System.Net.Mail.Attachment>();
            attachments.Add(amAttachment);
            EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"FixProductDescriptions, Update Description:{updateDescription} : {shopId}, {DateTime.Now.ToString("MMM - yyyy")} ", $"{EmailHelper.GetBoLinkHref(shopId)}, found:{count} products See Attchment", attachments: attachments);
        }

        public static void FixProductDescriptionsFromFile(int shopId, bool updateDescription = false)
        {
            string[] pids = File.ReadAllLines("c:\\temp\\ProductIds15.txt");

            var shopifyApi = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.AISeoProductOptimizer);
            var histList = GetAllAISeoProductOptimizerHistory(shopId, AISeoProductOptimizerHistoryStatus.Updated.GetHashCode());
            StringBuilder sb = new StringBuilder();

            int count = 0;
            foreach (var productId in pids)
            {
                Console.WriteLine($"{count} of {pids.Count()} To Update");
                count++;
                long pid = long.Parse(productId);
                AISeoProductOptimizerHistory item = histList.Single(c => c.ProductID == pid);
                UpdateBadDescription(updateDescription, shopifyApi, sb, item, pid);
                //CompareDescription(updateDescription, shopifyApi, sb, item, pid);
            }
            File.WriteAllText("c:\\temp\\UpdatedProductDescriptions.txt", sb.ToString());
        }

        private static void UpdateBadDescription(bool updateDescription, ShopifyApiProvider shopifyApi, StringBuilder sb, AISeoProductOptimizerHistory item, long productId)
        {
            AIProduct pdProduct = item.AIProduct.FromJson<AIProduct>();
            if (updateDescription)
            {
                string htmlDescription = pdProduct.GenerateText;
                htmlDescription = htmlDescription.Replace("\"", "'");
                htmlDescription = htmlDescription.Replace("\\", "\\\\");
                sb.AppendLine($"{pdProduct.Url} -{productId}");
                shopifyApi.UpdateProductDescription(productId, htmlDescription);
            }
        }
        private static bool CompareDescription(bool updateDescription, ShopifyApiProvider shopifyApi, StringBuilder sb, AISeoProductOptimizerHistory item, long productId)
        {
            AIProduct pdProduct = item.AIProduct.FromJson<AIProduct>();

            //
            if (pdProduct != null)
            {
                //var productItem = product.FirstOrDefault();
                //string description = productItem.DescriptionHtml;
                string description = pdProduct.GenerateText;
                string gptDescription = item.GeneratedDescription;
                bool compResults = CompareStrings(FixString(gptDescription), FixString(description));
                if (compResults)
                {
                    Console.WriteLine($"{productId} - {pdProduct.PID} - Description are the same.");
                    return true;
                }
                else
                {
                    var product = shopifyApi.GetProductsListAdvanced(1, out string endC, out string startC, productId: productId.ToString());
                    if (product != null && product.Count > 0)
                    {
                        string shopifyDescription = product.First().DescriptionHtml;
                        bool compResultsSite = CompareStrings(FixString(description), FixString(shopifyDescription));
                        if (!compResultsSite)
                        {
                            Console.WriteLine($"{productId} - {pdProduct.PID} - Description are not the same.");
                            string updatedRes = "";
                            if (updateDescription)
                            {
                                updatedRes = shopifyApi.UpdateProductDescription(productId, description);
                            }
                            sb.AppendLine($"{productId};{pdProduct.Url};{updatedRes};{gptDescription.SubString2(0, 200, true)};{description.SubString2(0, 200, true)};{shopifyDescription.SubString2(0, 200, true)}");

                        }
                        return false;
                    }
                }
            }
            return true;
        }

        public static string FixString(string str)
        {
            return str.StripHTMLTags().Replace("\n", "").Replace("\r", "").Replace("&nbsp;", " ").Replace("&amp;", "&").Trim().SubString2(0, 150);

        }

        public static bool CompareStrings(string str1, string str2)
        {
            if (str1 == null || str2 == null) // check for null strings
            {
                return false;
            }
            if (str1.Length != str2.Length) // check for different lengths
            {
                return false;
            }
            for (int i = 0; i < str1.Length; i++) // compare character by character
            {
                if (str2[i] == '`' && str1[i] == "'".ToCharArray()[0])
                {
                    continue;
                }
                if (str1[i] == '`' && str2[i] == "'".ToCharArray()[0])
                {
                    continue;
                }
                if (str2[i] == '"' && str1[i] == "'".ToCharArray()[0])
                {
                    continue;
                }
                if (str1[i] == '"' && str2[i] == "'".ToCharArray()[0])
                {
                    continue;
                }
                if ((short)str1[i] == 32 && (short)str2[i] == 160)
                {
                    continue;
                }
                if ((short)str1[i] == 160 && (short)str2[i] == 32)
                {
                    continue;
                }
                if (str1[i] != str2[i])
                {

                    return false;
                }
            }
            return true; // strings are equal
        }

        public static void FixProductDescriptionsByPIDs(int shopId, List<string> productIds, bool updateDescription = false)
        {
            var shopifyApi = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.AISeoProductOptimizer);
            var histList = GetAllAISeoProductOptimizerHistory(shopId, AISeoProductOptimizerHistoryStatus.Updated.GetHashCode());
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"productI;updatedRes;shopifyDescription;fixDescription;");
            int count = productIds.Count;
            foreach (var sProductId in productIds)
            {
                string updatedRes = "";
                try
                {
                    long productId = long.Parse(sProductId);

                    ProductRawData productRawData = shopifyApi.GetProductDetails(sProductId);
                    string description = FixProductDescriptions(productRawData.Description);
                    bool changed = false;
                    if (description == null || productRawData.Description == description)
                    {
                        updatedRes = $"No change required!";
                    }
                    else
                    {
                        changed = true;
                        if (updateDescription)
                        {
                            updatedRes = shopifyApi.UpdateProductDescription(productId, description);
                            if (string.IsNullOrEmpty(updatedRes))
                            {
                                updatedRes = "Updated";
                            }

                        }
                        else
                        {
                            updatedRes = $"{updatedRes}, Demo mode.";
                        }
                    }
                    Console.WriteLine($"{shopId} - {sProductId} {count--}: {updatedRes} ");
                    if (changed)
                    {
                        sb.AppendLine($"{productId};{updatedRes};{productRawData.Description};{description}");
                    }
                    else
                    {
                        sb.AppendLine($"{productId};{updatedRes};");
                    }

                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{shopId}, {sProductId} -  {ex}");
                    sb.AppendLine($"{sProductId};Exception;{ex.Message}");
                }
                Thread.Sleep(500);
            }


            File.WriteAllText($"c:\\temp\\FixProductDescriptions{shopId}.csv", sb.ToString());
            Stream stream = new MemoryStream(File.ReadAllBytes("c:\\temp\\" + $"FixProductDescriptions{shopId}.csv"));
            System.Net.Mail.Attachment amAttachment = new System.Net.Mail.Attachment(stream, $"FixProductDescriptions{shopId}.csv");
            List<System.Net.Mail.Attachment> attachments = new List<System.Net.Mail.Attachment>();
            attachments.Add(amAttachment);
            EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"FixProductDescriptions, Update Description:{updateDescription} : {shopId}, {DateTime.Now.ToString("MMM - yyyy")} ", $"{EmailHelper.GetBoLinkHref(shopId)}, found:{count} products See Attchment", attachments: attachments);
        }

        private static string FixProductDescriptions(string html)
        {
            if (string.IsNullOrEmpty(html))
            {
                return html;
            }
            if (html.Contains("product-image") || html.Contains("product_image"))
            {
                string pattern = "<img[^>]*?src\\s*=\\s*['\"]product-image\\.jpg['\"][^>]*?>";
                html = Regex.Replace(html, pattern, string.Empty, RegexOptions.IgnoreCase);
                string pattern2 = "<img[^>]*?src\\s*=\\s*['\"]product_image\\.jpg['\"][^>]*?>";
                html = Regex.Replace(html, pattern2, string.Empty, RegexOptions.IgnoreCase);
                html = html.Replace("Product Image:", "");
                return html;
            }
            return null;
        }

        public static string RemoveImgTag(string html)
        {
            if (string.IsNullOrEmpty(html))
            {
                return html;
            }
            if (html.Contains("<img"))
            {
                string pattern = @"<img\b[^>]*>";
                html = RegexHelper.Replace(html, pattern, "");
                return html;
            }
            return html;
        }
    }
}
