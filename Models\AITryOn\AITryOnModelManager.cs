﻿using Org.BouncyCastle.Tsp;
using Storeya.Core.Entities;
using Storeya.Core.Helpers;
using Storeya.Core.Models.ImageOptimizer;
using Storeya.Core.Models.Marketplaces;
using Storeya.Core.Models.ProductDescriber;
using Storeya.Core.Models.ShoppingFeed;

using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using System.Text;


namespace Storeya.Core.Models.AITryOn
{

    public class AITModelItem
    {
        public int id { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public string type { get; set; }
        public string status { get; set; }
        public float accuracy { get; set; }
        public int trainingProgress { get; set; }
        public string createdAt { get; set; }
        public string lastUsed { get; set; }
        public int imageCount { get; set; }
        public string image { get; set; }
        public string ethnicity { get; set; }
        public string ageRange { get; set; }
        public string bodyType { get; set; }
        public string height { get; set; }
        public string hairColor { get; set; }
        public string eyeColor { get; set; }
        public bool useAsBrandModel { get; set; }
        public string generatedImageUrl { get; set; }
        public string gender { get; set; }
    }

    public class AITCategoryItem
    {
        public string value { get; set; }
        public string label { get; set; }
    }
    public class AITProductItem
    {
        public string id { get; set; }
        public string name { get; set; }
        public string url { get; set; }
        public string brand { get; set; }
        public string sku { get; set; }
        public string category { get; set; }
        public string price { get; set; }
        public string status { get; set; }
        public string image { get; set; }
    }
    public class AITryOnModelManager
    {
        public static List<AITProductItem> GetProductsItems(int appTypeId, string shop, int page, string next, string previous, string title,
            string collection, bool filterMaxDescLength,
           bool onlyPublished, bool showOnlyWithImage, bool showUpdatedProducts, int shopID, out string endCursor, out string startCursor,
           out bool lastPage, out int filterTotalProducts,
             DateTime? from = null, DateTime? to = null, int? productLimit = null, int descriptionMaxLength = 1000
           )
        {
            filterTotalProducts = 0;
            lastPage = false;
            int stryAppId = (int)StoreyaAppHelper.GetAppType(appTypeId);
            var shopifyApi = ShopifyConnector.GetShopifyApiClient(shopID, stryAppId);
            var appAT = GetAppSettings(shopID);
            if (appAT == null)
            {
                shopifyApi.SiteUrl = $"https://{shop}/";// shop.ShopUrl;
            }
            else
            {
                // shopifyApi.SiteUrl = string.IsNullOrEmpty(appAT.ShopUrl) ? $"https://{shop}/" : pd.ShopUrl;
            }

            string shopUrl = shopifyApi.SiteUrl;
            List<string> excludeKeyWords = new List<string>() {
                "text/javascript",
                "<script ",
                "<table"
                };



            List<ProductRawData> mProducts = ProductDescriberManager.GetProductsFilterd(shopifyApi, page, next, previous, title, collection, filterMaxDescLength, out endCursor, out startCursor, out filterTotalProducts, onlyPublished, showOnlyWithImage, excludeKeyWords, descriptionMaxLength, from, to, productLimit);

            List<AITProductItem> products = ConvertToProducts(showUpdatedProducts, shopID, shopUrl, mProducts, "");
            //if (ConfigHelper.GetBoolValue("LogWasUpdatedProductsIds", true))
            //{
            //    Log4NetLogger.Info($"WasUpdated products:{shop} - {string.Join(",", products.Where(c => c.WasUpdated).Select(x => x.Id).ToArray())}", shopID);
            //}
            //m.Collections = shopifyApi.GetCollectionsWithProducts();
            if (string.IsNullOrEmpty(endCursor))
            {
                lastPage = true;
                endCursor = "0";
            }
            if (string.IsNullOrEmpty(startCursor))
            {
                startCursor = "0";
            }

            return products;
        }

        private static string GetAppSettings(int shopID)
        {
            return null;
        }

        public static List<AITCategoryItem> GetCategories(int appTypeId, string shop, int shopID)
        {
            int stryAppId = (int)StoreyaAppHelper.GetAppType(appTypeId);
            var shopifyApi = ShopifyConnector.GetShopifyApiClient(shopID, stryAppId);
            var collections = shopifyApi.GetCollectionsWithProducts();

            List<AITCategoryItem> aITCategoryItems = new List<AITCategoryItem>();
            foreach (var item in collections)
            {
                aITCategoryItems.Add(new AITCategoryItem()
                {
                    label = item.URL,
                    value = ExtentionsHelper.SubString2(item.Name, 0, 50, true)

                });
            }
            return aITCategoryItems;
        }

        private static List<AITProductItem> ConvertToProducts(bool showUpdatedProducts, int shopID, string shopUrl, List<ProductRawData> mProducts, string imageSize = "_100x100")
        {
            List<AITProductItem> products = new List<AITProductItem>();
            foreach (var item in mProducts)
            {
                products.Add(new AITProductItem()
                {
                    id = item.OriginalID,
                    name = item.Name,
                    url = shopUrl + "/products/" + item.Url,
                    image = ImageNamer.AddSuffixBeforeImageExtension(item.Images[0].ExternalUrl, imageSize),
                    brand = item.Brand,
                    category = item.Category,
                    price = item.Price.ToString("F2"),
                    sku = item.GTIN,
                    status = item.Status.ToString()

                });
            }
            return products;
        }
        public static string SaveImageToS3(int shopId, string imagePath)
        {
            string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
            string productImagesPath = ConfigHelper.GetValue("ATTryOnModelImages.Path");
            string aWSStrys3Domain = ConfigHelper.GetValue("AWSStrys3Domain");
            string s3Url = FeedsManager.SaveImageFile(shopId, imagePath, rootFolderPath, productImagesPath, "ATTryOnModelImages.Path", subFolder: null);
            if (!string.IsNullOrEmpty(s3Url))
            {
                try
                {
                    File.Delete(imagePath); //Delete the local file after uploading to S3
                }
                catch { }
            }
            return $"{aWSStrys3Domain}{s3Url}";
        }

        public static string SaveImageFromUrlToS3(int shopId, string imageUrl, string imageName = null)
        {
            string rootFolderPath, productImagesPath, aWSStrys3Domain, imagePath;
            imagePath = SaveImageLocaly(shopId, imageUrl, out rootFolderPath, out productImagesPath, out aWSStrys3Domain, imageName);
            string s3Url = FeedsManager.SaveImageFile(shopId, imagePath, rootFolderPath, productImagesPath, "ATTryOnModelImages.Path", subFolder: null);
            if (!string.IsNullOrEmpty(s3Url))
            {
                try
                {
                    File.Delete(imagePath); //Delete the local file after uploading to S3
                }
                catch { }
            }
            return $"{aWSStrys3Domain}{s3Url}";
        }
        private static string SaveImageLocaly(int shopId, string imageUrl, string imageName = null)
        {
            string rootFolderPath, productImagesPath, aWSStrys3Domain;
            return SaveImageLocaly(shopId, imageUrl, out rootFolderPath, out productImagesPath, out aWSStrys3Domain, imageName);
        }
        private static string SaveImageLocaly(int shopId, string imageUrl, out string rootFolderPath, out string productImagesPath, out string aWSStrys3Domain, string imageName = null)
        {
            rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
            productImagesPath = ConfigHelper.GetValue("ATTryOnModelImages.Path");
            aWSStrys3Domain = ConfigHelper.GetValue("AWSStrys3Domain");
            string shopPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}";
            string suffix = "_AT";
            if (!string.IsNullOrEmpty(imageName))
            {
                suffix = imageName;
            }
            var imageFile = ImageNamer.GetFileName(imageUrl, out string extention, out string orignalImageName, suffix);
            string imagePath = $@"{shopPath}\{imageFile}";
            ImageOptimizer.ImageOptimizer.DownloadImage(imageUrl, imagePath, shopId);
            return imagePath;
        }

        public static AITModelItem ManageAITryOnModel(
            int shopId,
            string name,
            string description = null,
            string type = null,
            string status = null,
            double? accuracy = null,
            int? trainingProgress = null,
            DateTime? lastUsed = null,
            int? imageCount = null,
            string image = null,
            string ethnicity = null,
            string ageRange = null,
            string bodyType = null,
            string height = null,
            string gender = null,
            string hairColor = null,
            string eyeColor = null,
            bool? useAsBrandModel = null,
            IAIApiService aIApiService = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var model = db.AITryOnModels.FirstOrDefault(x => x.ShopId == shopId && x.Name.ToLower() == name.ToLower());


            if (string.IsNullOrEmpty(image))
            {
                if(aIApiService == null)
                {
                    aIApiService = new RunwareApiService();
                }                 
                string prompt = $"Professional {gender} fashion model half top body image wearing white t-shirt and blue jeans, {ethnicity} ethnicity, age {ageRange}, {bodyType} body type, {hairColor} hair, {eyeColor} eyes, studio lighting, high fashion photography, clean background";
                var res = aIApiService.GenerateModelImage(prompt);
                image = AITryOnModelManager.SaveImageFromUrlToS3(shopId, res.ImageURL);
            }
            if (model == null)
            {

                model = new AITryOnModel
                {
                    ShopId = shopId,
                    Name = name,
                    Description = description,
                    Type = type,
                    Status = status,
                    Accuracy = accuracy,
                    TrainingProgress = trainingProgress,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = null,
                    LastUsed = lastUsed,
                    ImageCount = imageCount,
                    Image = image,
                    Ethnicity = ethnicity,
                    AgeRange = ageRange,
                    BodyType = bodyType,
                    Height = height,
                    Gender = gender,
                    HairColor = hairColor,
                    EyeColor = eyeColor,
                    UseAsBrandModel = useAsBrandModel ?? false
                };
                db.AITryOnModels.Add(model);
            }
            else
            {
                model.UpdatedAt = DateTime.Now;
                if (description != null) model.Description = description;
                if (type != null) model.Type = type;
                if (status != null) model.Status = status;
                if (accuracy.HasValue) model.Accuracy = accuracy;
                if (trainingProgress.HasValue) model.TrainingProgress = trainingProgress;
                if (lastUsed.HasValue) model.LastUsed = lastUsed;
                if (imageCount.HasValue) model.ImageCount = imageCount;
                if (image != null) model.Image = image;
                if (ethnicity != null) model.Ethnicity = ethnicity;
                if (ageRange != null) model.AgeRange = ageRange;
                if (bodyType != null) model.BodyType = bodyType;
                if (height != null) model.Height = height;
                if (gender != null) model.Gender = gender;
                if (hairColor != null) model.HairColor = hairColor;
                if (eyeColor != null) model.EyeColor = eyeColor;
                if (useAsBrandModel.HasValue) model.UseAsBrandModel = useAsBrandModel.Value;
            }

            db.SaveChanges();
            return ConvertToAITModelItem(model);
        }

        public static AITModelItem ConvertToAITModelItem(AITryOnModel model)
        {
            if (model == null) return null;

            return new AITModelItem
            {
                id = model.Id,
                name = model.Name,
                description = model.Description,
                type = model.Type,
                status = model.Status,
                accuracy = (float)(model.Accuracy ?? 0),
                trainingProgress = model.TrainingProgress ?? 0,
                createdAt = model.CreatedAt.ToString("yyyy-MM-dd"),
                lastUsed = model.LastUsed?.ToString("yyyy-MM-dd"),
                imageCount = model.ImageCount ?? 0,
                image = model.Image,
                ethnicity = model.Ethnicity,
                ageRange = model.AgeRange,
                bodyType = model.BodyType,
                height = model.Height,
                hairColor = model.HairColor,
                eyeColor = model.EyeColor,
                useAsBrandModel = model.UseAsBrandModel,
                gender = model.Gender,
                generatedImageUrl = null // Placeholder for any additional logic to populate this field
            };
        }


        public static Storeya.Core.AITryOnModelUsage ManageAITryOnModelUsage(
            int shopId,
            int modelId,
            long productId,
            string imageUrl = null,
            string aiData = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            //var usage = db.AITryOnModelUsages
            //    .FirstOrDefault(x => x.ShopId == shopId && x.ModelId == modelId && x.ProductId == productId);

            //if (usage == null)
            //{
            var usage = new Storeya.Core.AITryOnModelUsage
            {
                ShopId = shopId,
                ModelId = modelId,
                ProductId = productId,
                ImageUrl = imageUrl,
                AIData = aiData,
                CreatedAt = DateTime.Now,
                UpdatedAt = null
            };
            db.AITryOnModelUsages.Add(usage);
            //}
            //else
            //{
            //    usage.UpdatedAt = DateTime.Now;
            //    if (imageUrl != null) usage.ImageUrl = imageUrl;
            //    if (aiData != null) usage.AIData = aiData;
            //}

            db.SaveChanges();
            return usage;
        }

        public static List<AITModelItem> GetModels(int shopID)
        {
            var db = DataHelper.GetStoreYaEntities();
            return db.AITryOnModels
                .Where(x => x.ShopId == shopID || x.ShopId == 0)
                .OrderByDescending(x => x.CreatedAt)
                .ToList()
                .Select(ConvertToAITModelItem)
                .ToList();
        }

        public static string TryOn(int shopId, int appTypeId, int modelId, string productId, string background, string pose, string resolution, IAIApiService aIApiService)
        {
            var db = DataHelper.GetStoreYaEntities();
            var aItModel = db.AITryOnModels.FirstOrDefault(x => x.Id == modelId);

            int stryAppId = (int)StoreyaAppHelper.GetAppType(appTypeId);
            var shopifyApi = ShopifyConnector.GetShopifyApiClient(shopId, stryAppId);
            var products = shopifyApi.GetProductsListAdvanced(1, out string endC, out string startC, out int totalProducts, productId: productId);
            if (products == null || products.Count == 0)
            {
                throw new Exception($"Product with ID {productId} not found.");
            }
            var product = products.First();
            List<string> imagesUri = new List<string>();


            foreach (var pImage in product.Images)
            {
                string path = SaveImageLocaly(shopId, pImage.ExternalUrl);
                imagesUri.Add(GetImageDataUri(path));
            }
            string modelImagePath = SaveImageLocaly(shopId, aItModel.Image);
            string modelImageUri = GetImageDataUri(modelImagePath);
            string prompt = BuildFashionModelPrompt(aItModel.Ethnicity, aItModel.AgeRange, background, pose, aItModel.BodyType, aItModel.HairColor,
                aItModel.EyeColor, resolution, aItModel.Gender, aItModel.Height, product.Name);
            

            // Use runwayTryOnService if needed, otherwise use runwareApiService
            var res = aIApiService.TryOn(aItModel.Image, product.Images.First()?.ExternalUrl, prompt, 1024, 1024);
            string imageName = null;
            if (aIApiService is FalAITryOnService)
            {
                imageName = res.TaskUUID;
            }
            //RunwareApiService runwareApiService = new RunwareApiService();
            //var res = runwareApiService.TryOn(modelImageUri, imagesUri.First(), prompt);

            string image = AITryOnModelManager.SaveImageFromUrlToS3(shopId, res.ImageURL, imageName);

            ManageAITryOnModelUsage(shopId, modelId, long.Parse(productId), imageUrl: image, aiData: res.ToJson());
            return image;
        }

        public static string BuildFashionModelPrompt(
           string ethnicity,
           string age_range,
           string backgroundStyle,
           string poseStyle,
           string body_type,
           string hair_color,
           string eye_color,
           string resolution,
           string gender,
           string hight,
           string productName)
        {
            // Gender assumption
            string genderTerm = $"{gender} fashion model";

            // Ethnicity mapping
            string ethnicityDetail;
            switch (ethnicity)
            {
                case "caucasian": ethnicityDetail = $"Caucasian {gender}"; break;
                case "african": ethnicityDetail = $"African {gender}"; break;
                case "asian": ethnicityDetail = $"Asian {gender}"; break;
                case "hispanic": ethnicityDetail = $"Hispanic {gender}"; break;
                case "middle-eastern": ethnicityDetail = $"Middle Eastern {gender}"; break;
                default: ethnicityDetail = $"mixed ethnicity {gender}"; break;
            }

            // Age mapping
            string ageDetail;
            string h = gender == "female" ? "her" : "his";
            switch (age_range)
            {
                case "18-25": ageDetail = $"young adult {gender}"; break;
                case "26-35": ageDetail = $"{gender} in {h} twenties to thirties"; break;
                case "36-45": ageDetail = $"mature {gender}"; break;
                case "46-55": ageDetail = $"middle aged {gender}"; break;
                case "65+": ageDetail = $"old {gender}"; break;
                default: ageDetail = $"{gender} in {h} twenties to thirties"; break;
            }

            // Body type mapping
            string bodyDetail;
            switch (body_type)
            {
                case "petite": bodyDetail = "petite build"; break;
                case "slim": bodyDetail = "slim figure"; break;
                case "athletic": bodyDetail = "athletic build"; break;
                case "curvy": bodyDetail = "curvy figure"; break;
                case "plus-size": bodyDetail = "plus-size figure"; break;
                default: bodyDetail = "average build"; break;
            }

            // Resolution mapping
            string resolutionOptions;
            switch (resolution)
            {
                case "2K": resolutionOptions = "2K"; break;
                case "4K": resolutionOptions = "4K"; break;
                case "8K": resolutionOptions = "8K"; break;
                default: resolutionOptions = "high"; break;
            }

            // Hair & Eye details
            string hairDetail = !string.IsNullOrWhiteSpace((string)hair_color)
                ? hair_color + " hair"
                : "natural hair";

            string eyeDetail = !string.IsNullOrWhiteSpace((string)eye_color)
                ? eye_color + " eyes"
                : "natural eyes";

            // Background mapping
            string backgroundDetail;
            switch (backgroundStyle)
            {
                case "studio_white": backgroundDetail = "clean white studio background"; break;
                case "studio_gray": backgroundDetail = "professional gray studio background"; break;
                case "lifestyle_outdoor": backgroundDetail = "natural outdoor lifestyle setting"; break;
                case "lifestyle_indoor": backgroundDetail = "modern indoor lifestyle setting"; break;
                case "fashion_runway": backgroundDetail = "fashion runway background"; break;
                case "urban": backgroundDetail = "urban street style background"; break;
                default: backgroundDetail = "neutral background"; break;
            }

            // Pose mapping
            string poseDetail;
            switch (poseStyle)
            {
                case "classic": poseDetail = "classic fashion pose"; break;
                case "dynamic": poseDetail = "dynamic energetic pose"; break;
                case "casual": poseDetail = "relaxed casual pose"; break;
                case "editorial": poseDetail = "high fashion editorial pose"; break;
                case "commercial": poseDetail = "commercial modeling pose"; break;
                default: poseDetail = "modeling pose"; break;
            }

            // Build final prompt
            string prompt =
                //"use the seedImage as the fashion model and the referenceImages to replace the white T-shirt " +
                "Professional fashion photography of a beautiful " + ethnicityDetail + ", " +
                ageDetail + " with " + bodyDetail + ", " +
                "wearing the referenceImages[0] describe as " + productName + ", " +
                "hight " + hight + ", " +
                hairDetail + ", " +
                eyeDetail + ", " +
                backgroundDetail + ", " +
                poseDetail + ", " +
                "high-end fashion photography, studio lighting, detailed fabric textures, photorealistic, " +
                resolutionOptions + " quality, commercial fashion shoot style, " +
                genderTerm.ToUpper() + " ONLY";

            return prompt;
        }


        internal static string GetImageDataUri(string imageUrl)
        {
            if (string.IsNullOrEmpty(imageUrl))
            {
                return null;
            }
            try
            {
                byte[] imageBytes = File.ReadAllBytes(imageUrl);
                string base64Image = Convert.ToBase64String(imageBytes);
                return $"data:image/png;base64,{base64Image}";
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error($"Error converting image to Data URI: {ex.Message}", ex);
                return null;
            }
        }
    }
}
