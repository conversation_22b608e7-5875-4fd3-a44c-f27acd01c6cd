﻿using Org.BouncyCastle.Tsp;
using Storeya.Core.Entities;
using Storeya.Core.Helpers;
using Storeya.Core.Models.ImageOptimizer;
using Storeya.Core.Models.Marketplaces;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Storeya.Core.GlobalEFeedManager;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.TrayNotify;
//using static Storeya.Core.Models.AITryOn.AITryOnModelManager;

namespace Storeya.Core.Models.AITryOn
{
    public class AITryOnSandBox
    {
        private static string SaveImageLocaly(string path, string imageUrl, string prefix)
        {
            var imageFile = ImageNamer.GetFileName(imageUrl, out string extention, out string orignalImageName);
            string imagePath = $@"{path}\{prefix}_{imageFile}";
            ImageOptimizer.ImageOptimizer.DownloadImage(imageUrl, imagePath, 0);
            return imagePath;
        }

        public enum AITryOnApiService
        {
            Runware = 1,
            RunWay = 2,
            Fal = 3,
            BitStudio = 4,
        }
        public static void RunTryOnTest(string path, AITryOnApiService service)
        {
            string modelsJson = File.ReadAllText(Path.Combine(path, "models.json"));
            string productsJson = File.ReadAllText(Path.Combine(path, "products.json"));
            List<AITProductItem> productItems = productsJson.FromJson<List<AITProductItem>>();
            List<AITModelItem> modelItems = modelsJson.FromJson<List<AITModelItem>>();
            string background = "Fashion Runway";
            string pose = "classic";
            string resolution = "4K";

            IAIApiService aiApiService = null;

            switch (service)
            {
                case AITryOnApiService.Runware:
                    aiApiService = new RunwareApiService();
                    break;
                case AITryOnApiService.RunWay:
                    aiApiService = new RunwayTryOnService();
                    break;
                case AITryOnApiService.Fal:
                        aiApiService = new FalAITryOnService();
                    break;
                case AITryOnApiService.BitStudio:
                    aiApiService = new BitStudioApi();
                    break;
                default:
                    break;
            }

            foreach (var aItModel in modelItems)
            {
                string imagePath = SaveImageLocaly(path, aItModel.image, aItModel.id.ToString());
                string mdImagesUri = AITryOnModelManager.GetImageDataUri(imagePath);
                foreach (var pdItem in productItems)
                {
                    string pdImagePath = SaveImageLocaly(path, pdItem.image, pdItem.id);

                    string pdImageUri = AITryOnModelManager.GetImageDataUri(pdImagePath);

                    string prompt = AITryOnModelManager.BuildFashionModelPrompt(aItModel.ethnicity, aItModel.ageRange, background, pose, aItModel.bodyType, aItModel.hairColor,
                    aItModel.eyeColor, resolution, aItModel.gender, aItModel.height, pdItem.name);

                    var res = aiApiService.TryOn(mdImagesUri, pdImageUri, prompt, 1024, 1024);
                    Console.WriteLine(SaveImageLocaly(path, res.ImageURL, $"{aItModel.id.ToString()}_{pdItem.id}"));
                }
            }
        }
    }
}
