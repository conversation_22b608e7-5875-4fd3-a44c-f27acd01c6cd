﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static Storeya.Core.Models.AITryOn.AITryOnBase;

namespace Storeya.Core.Models.AITryOn
{
    public class FalAITryOnService : IAIApiService
    {


        //private readonly string apiKeyStoreYadev = "9dd2f6dd-047f-4a99-8aba-dbf19a63499e:468c22d50139429f4a75d3dae2ff43f5";
        private readonly string apiKey = "7c26b065-af3f-4934-a1db-6428d17e1bad:15d563de7153e2329e902b9f047e9e80";
        //private readonly string apiKeyRonnyC = "b8b4e155-ec0e-4eb0-9ff3-eb256fcf49a6:2d7acacac362787ff7846db60bb6108b";
        private readonly string baseUrl = "https://queue.fal.run";
        private readonly string host = "queue.fal.run";


        public string Call(string action, object payload, string method, bool throwException, bool debug)
        {
            ServicePointManager.Expect100Continue = true;

            ServicePointManager.SecurityProtocol =
              SecurityProtocolType.Tls |
              SecurityProtocolType.Tls11 |
              SecurityProtocolType.Tls12 |
              SecurityProtocolType.Ssl3;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create($"{baseUrl}/{action}");

            request.ContentType = "application/json";
            request.Method = method;
            request.Headers["Authorization"] = "Key " + apiKey;
            //request.Headers.Add("Authorization", $"Bearer {apiKey}");
            request.Host = host;
            //request.ContentType = "application/x-www-form-urlencoded;charset=UTF-8";
            if (payload != null)
            {
                string pl = payload.ToJson();
                byte[] arr = Encoding.UTF8.GetBytes(pl);
                request.ContentLength = arr.Length;
                Stream dataStream = request.GetRequestStream();
                dataStream.Write(arr, 0, arr.Length);
                dataStream.Close();
            }
            HttpWebResponse response = null;
            try
            {
                response = (HttpWebResponse)request.GetResponse();
                Stream responseStream = response.GetResponseStream();
                if (response.ContentEncoding.ToLower().Contains("gzip"))
                    responseStream = new GZipStream(responseStream, CompressionMode.Decompress);

                Encoding enc = System.Text.Encoding.GetEncoding("UTF-8");
                string stringResult = "";
                using (StreamReader streamReader = new StreamReader(responseStream, enc))
                {
                    stringResult = streamReader.ReadToEnd();

                }
                if (debug)
                {
                    Console.WriteLine(stringResult);
                }
                return stringResult;
            }
            catch (WebException ex)
            {
                //Console.WriteLine(method + " " + url);
                //Console.WriteLine(requestDetails);
                Console.WriteLine(ex.ToString());
                response = (HttpWebResponse)ex.Response;
                if (throwException)
                {
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                if (throwException)
                {
                    throw ex;
                }
            }

            return null;
        }

        public ApiImageResponse GenerateImageFromText(object payload)
        {
            string tryOnModel = "fal-ai/flux-pro/v1.1-ultra";

            var jsonResponse = Call(tryOnModel, payload, "POST", false, false);
            if (string.IsNullOrWhiteSpace(jsonResponse))
            {
                throw new Exception("No response from API");
            }
            var result = jsonResponse.FromJson<RequestResponse>();
            var statusUrl = $"fal-ai/flux-pro/requests/{result.request_id}/status";
           
            while (true)
            {
                var statusJson = Call(statusUrl, null, "GET", false, false);
                var status = statusJson.FromJson<QueueStatusResponse>();
                if (status.status == "COMPLETED" || status.status == "FAILED" || status.status == "CANCELED")
                {
                    if (status.status != "COMPLETED")
                        throw new Exception($"Fal job {tryOnModel} ended with status: {status.status}");
                    break;
                }
              
            }
            var resultUrl = $"fal-ai/flux-pro/requests/{result.request_id}";
            var resultJson = Call(resultUrl, null, "GET", false, false);
            var res = resultJson.FromJson<dynamic>();
            if (res != null )
            {
                return new ApiImageResponse
                {
                    Success = true,
                    ImageURL = res.images[0].url,
                    TaskUUID = result.request_id,
                    Payload = payload
                };
            }
            return null;
            //if (result?.data != null && result.data.Length > 0 && !string.IsNullOrWhiteSpace(result.data[0].imageURL))
            //{
            //    return new ApiImageResponse
            //    {
            //        Success = true,
            //        ImageURL = result.data[0].imageURL,
            //        TaskUUID = result.data[0].taskUUID ?? Guid.NewGuid().ToString(),
            //        Payload = payload
            //    };
            //}
            //else
            //{
            //    throw new Exception("No image generated from Runware API");
            //}

            
        }

        public ApiImageResponse GenerateModelImage(string positivePrompt)
        {
            var submitPayload = new
            {

                prompt = positivePrompt
                // You can add more control parameters if available

            };
            return GenerateImageFromText(submitPayload);
        }

        public ApiImageResponse TryOn(string modelImageUrl, string productImageUrl, string prompt, int width = 1024, int height = 1024, int numberResults = 1)
        {

            var payload = new
            {

                model_image = modelImageUrl,
                garment_image = productImageUrl
                // You can add more control parameters if available

            };
            string tryOnModel = "fal-ai/fashn/tryon/v1.6";

            var jsonResponse = Call(tryOnModel, payload, "POST", false, false);
            if (string.IsNullOrWhiteSpace(jsonResponse))
            {
                throw new Exception("No response from API");
            }
            var result = jsonResponse.FromJson<RequestResponse>();
            var statusUrl = $"fal-ai/fashn/requests/{result.request_id}/status";

            while (true)
            {
                var statusJson = Call(statusUrl, null, "GET", false, false);
                var status = statusJson.FromJson<QueueStatusResponse>();
                if (status.status == "COMPLETED" || status.status == "FAILED" || status.status == "CANCELED")
                {
                    if (status.status != "COMPLETED")
                        throw new Exception($"Fal job {tryOnModel} ended with status: {status.status}");
                    break;
                }

            }
            var resultUrl = $"fal-ai/fashn/requests/{result.request_id}";
            var resultJson = Call(resultUrl, null, "GET", false, false);
            var res = resultJson.FromJson<dynamic>();
            if (res != null)
            {
                return new ApiImageResponse
                {
                    Success = true,
                    ImageURL = res.images[0].url,
                    TaskUUID = result.request_id,
                    Payload = payload
                };
            }
            return null;
        }
        private class QueueStatusResponse
        {
            public string status { get; set; }

        }

        public class RequestResponse
        {
            public string cancel_url { get; set; }
            public object logs { get; set; }
            public Metrics metrics { get; set; }
            public int queue_position { get; set; }
            public string request_id { get; set; }
            public string response_url { get; set; }
            public string status { get; set; }
            public string status_url { get; set; }
        }

        public class Metrics
        {
            public float inference_time { get; set; }
        }



    }
}
