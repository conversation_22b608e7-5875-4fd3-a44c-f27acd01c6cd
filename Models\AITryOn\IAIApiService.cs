﻿using static Storeya.Core.Models.AITryOn.AITryOnBase;

namespace Storeya.Core.Models.AITryOn
{
    public interface IAIApiService
    {
       
        ApiImageResponse GenerateModelImage(string positivePrompt);
        string Call(string action, object payload = null, string method = "POST", bool throwException = false, bool debug = false);
        ApiImageResponse TryOn(string modelImageUrl, string productImageUrl, string prompt,  int width = 1024, int height = 1024, int numberResults = 1);
    }
}