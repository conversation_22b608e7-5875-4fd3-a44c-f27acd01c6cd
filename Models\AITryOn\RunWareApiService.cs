﻿
using Storeya.Core.Helpers;
using System;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using static Storeya.Core.Models.AITryOn.AITryOnBase;
namespace Storeya.Core.Models.AITryOn
{
    public class RunwareApiService : IAIApiService
    {
        private readonly string _runwareApiKey;
        private readonly string _runwareBaseUrl = "https://api.runware.ai/v1";

     

        public RunwareApiService()
        {
            _runwareApiKey = ConfigHelper.GetValue("RunwareApiKey");

        }
       
        
        public string Call(string action, object payload, string method, bool throwException, bool debug)
        {


            ServicePointManager.Expect100Continue = true;

            ServicePointManager.SecurityProtocol =
              SecurityProtocolType.Tls |
              SecurityProtocolType.Tls11 |
              SecurityProtocolType.Tls12 |
              SecurityProtocolType.Ssl3;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create($"{_runwareBaseUrl}/{action}");

            request.ContentType = "application/json";
            request.Method = method;
            request.Headers.Add("Authorization", $"Bearer {_runwareApiKey}");
            request.Host = "api.runware.ai";
            //request.ContentType = "application/x-www-form-urlencoded;charset=UTF-8";
            if (payload != null)
            {
                string pl = payload.ToJson();
                byte[] arr = Encoding.UTF8.GetBytes(pl);
                request.ContentLength = arr.Length;
                Stream dataStream = request.GetRequestStream();
                dataStream.Write(arr, 0, arr.Length);
                dataStream.Close();
            }
            HttpWebResponse response = null;
            try
            {
                response = (HttpWebResponse)request.GetResponse();
                Stream responseStream = response.GetResponseStream();
                if (response.ContentEncoding.ToLower().Contains("gzip"))
                    responseStream = new GZipStream(responseStream, CompressionMode.Decompress);

                Encoding enc = System.Text.Encoding.GetEncoding("UTF-8");
                string stringResult = "";
                using (StreamReader streamReader = new StreamReader(responseStream, enc))
                {
                    stringResult = streamReader.ReadToEnd();

                }
                if (debug)
                {
                    Console.WriteLine(stringResult);
                }
                return stringResult;
            }
            catch (WebException ex)
            {
                //Console.WriteLine(method + " " + url);
                //Console.WriteLine(requestDetails);
                Console.WriteLine(ex.ToString());
                response = (HttpWebResponse)ex.Response;
                if (throwException)
                {
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                if (throwException)
                {
                    throw ex;
                }
            }

            return null;
        }
        //  TryOn(string modelImageUrl, string productImageId, string prompt, string aiModel, int width = 1024, int height = 1024, int numberResults = 1);

        public ApiImageResponse TryOn(
        string modelImageUrl,
        string productImageUrl,
        string prompt,       
        int width = 1024,
        int height = 1024,
        int numberResults = 1
       )
        {
            string aiModel = "gen4_image";
            double strength = 0.7;
            string outputFormat = "PNG";
            var payload = new
            {
                taskType = "imageInference",
                taskUUID = Guid.NewGuid().ToString(),
                // seedImage = modelImageId,
                referenceImages = new[] { productImageUrl },
                //maskImage = productImageId, // Or apply product as mask or overlay
                positivePrompt = prompt,
                model = aiModel,
                width = width,
                height = height,
                strength = strength,
                numberResults = numberResults,
                outputType = new[] { "URL" },
                outputFormat = outputFormat
            };
            return GenerateImage(payload);
        }

        public ApiImageResponse GenerateModelImage(string positivePrompt)
        {
            object payload = new
            {
                taskType = "imageInference",
                taskUUID = Guid.NewGuid().ToString(),
                positivePrompt = positivePrompt,
                width = 1024,
                height = 1024,
                model = "google:2@2",
                numberResults = 1,
                outputType = new[] { "URL" },
                outputFormat = "PNG"
            };
            return GenerateImage(payload);
        }
        public ApiImageResponse GenerateImage(object payload)
        {

            var requestBody = new[]
            {
                payload
            };
            var jsonResponse = Call("inference", requestBody, "POST", false, false);
            if (string.IsNullOrWhiteSpace(jsonResponse))
            {
                throw new Exception("No response from Runware API");
            }
            var result = jsonResponse.FromJson<RunwareApiResult>();

            if (result?.data != null && result.data.Length > 0 && !string.IsNullOrWhiteSpace(result.data[0].imageURL))
            {
                return new ApiImageResponse
                {
                    Success = true,
                    ImageURL = result.data[0].imageURL,
                    TaskUUID = result.data[0].taskUUID ?? Guid.NewGuid().ToString(),
                    Payload = payload
                };
            }
            else
            {
                throw new Exception("No image generated from Runware API");
            }
        }

    }
}

public class RunwareApiResult
{
    public RunwareImageData[] data { get; set; }
}

public class RunwareImageData
{
    public string imageURL { get; set; }
    public string taskUUID { get; set; }
}

public class RunwareImageResponse : ApiImageResponse
{

    public RunwareImageData[] Data { get; set; }


}
