﻿using Storeya.Core.Helpers;
using System;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Net.Http;
using System.Text;

using System.Threading.Tasks;
using static Storeya.Core.Models.AITryOn.AITryOnBase;
using static Storeya.Core.Models.FbAds.FbAdsSDK.CustomAudiencesResponse;

namespace Storeya.Core.Models.AITryOn
{
    public class RunwayTryOnService : IAIApiService
    {

        private readonly string apiKey;
        private readonly string baseUrl = "https://api.runwayml.com/v1";
        private readonly string host = "api.runwayml.com";


        public RunwayTryOnService()
        {
            apiKey = ConfigHelper.GetValue("RunwayTryOnApiKey");
        }


        public ApiImageResponse TryOn(string modelImageUrl, string productImageUrl, string prompt,  int width = 1024, int height = 1024, int numberResults = 1)
        {

            if (string.IsNullOrWhiteSpace(modelImageUrl) || string.IsNullOrWhiteSpace(productImageUrl))
                throw new ArgumentException("Both modelImageUrl and garmentImageUrl are required");
            string aiModel = "runware:100@1";
            var request = new
            {
                model = aiModel,
                ratio = $"{height}:{width}",
                promptText = prompt,
                reference = new[]
                 {
                    new { type ="image", uri = productImageUrl  },
                    new { type ="image",  uri = modelImageUrl  }
                },
                numberOfOutputs = numberResults,
                contentModeration = new
                {
                    publicFigureThreshold = "auto"
                }

            };
            string response = Call("text_to_image", request, "POST", false, false);



            var result = response.FromJson<RunwayApiResult>();

            var outputUrl = result?.outputs?[0]?.uri;
            if (string.IsNullOrWhiteSpace(outputUrl))
                throw new Exception("No output generated");

            return new ApiImageResponse
            {
                Success = true,
                ImageURL = outputUrl
            };
        }


        public ApiImageResponse GenerateModelImage(string positivePrompt)
        {
            throw new NotImplementedException();
        }


        public string Call(string action, object payload, string method, bool throwException, bool debug)
        {
            ServicePointManager.Expect100Continue = true;

            ServicePointManager.SecurityProtocol =
              SecurityProtocolType.Tls |
              SecurityProtocolType.Tls11 |
              SecurityProtocolType.Tls12 |
              SecurityProtocolType.Ssl3;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create($"{baseUrl}/{action}");

            request.ContentType = "application/json";
            request.Method = method;
            request.Headers.Add("X-Runway-Version", "2024-11-06");
            request.Headers.Add("Authorization", $"Bearer {apiKey}");
            request.Host = host;
            //request.ContentType = "application/x-www-form-urlencoded;charset=UTF-8";
            if (payload != null)
            {
                string pl = payload.ToJson();
                byte[] arr = Encoding.UTF8.GetBytes(pl);
                request.ContentLength = arr.Length;
                Stream dataStream = request.GetRequestStream();
                dataStream.Write(arr, 0, arr.Length);
                dataStream.Close();
            }
            HttpWebResponse response = null;
            try
            {
                response = (HttpWebResponse)request.GetResponse();
                Stream responseStream = response.GetResponseStream();
                if (response.ContentEncoding.ToLower().Contains("gzip"))
                    responseStream = new GZipStream(responseStream, CompressionMode.Decompress);

                Encoding enc = System.Text.Encoding.GetEncoding("UTF-8");
                string stringResult = "";
                using (StreamReader streamReader = new StreamReader(responseStream, enc))
                {
                    stringResult = streamReader.ReadToEnd();

                }
                if (debug)
                {
                    Console.WriteLine(stringResult);
                }
                return stringResult;
            }
            catch (WebException ex)
            {
                //Console.WriteLine(method + " " + url);
                //Console.WriteLine(requestDetails);
                Console.WriteLine(ex.ToString());
                response = (HttpWebResponse)ex.Response;
                if (throwException)
                {
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                if (throwException)
                {
                    throw ex;
                }
            }

            return null;
        }


    }

    public class RunwayApiResult
    {
        public OutputItem[] outputs { get; set; }
    }

    public class OutputItem
    {
        public string uri { get; set; }
    }



}
