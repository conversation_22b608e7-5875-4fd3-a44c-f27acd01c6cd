﻿using Storeya.Core.Entities;
using Storeya.Core.Helpers;
using Storeya.Core.Models.AppStore;
using Storeya.Core.Models.DataProviders;
using Storeya.Core.Models.FastSpring;
using Storeya.Core.Models.Marketplaces;
using Storeya.Core.Models.Payments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace Storeya.Core.Models.Account
{
    public enum UserTypes
    {
        //Regulal = null
        FbShop = 1,
        OnlyForContent = 2,
        SocialTabs = 3,
        CouponPop = 4,
        MarketingTools = 5,
        Lexity = 6,
        ReferAFriend = 7,
        ExitPop = 8,
        //Tictail = 7 - no need in 
        //ShopAndMarketingTools = 6,
        TrafficBooster = 10,
        BunnerLite = 11,
        FacebookAdsGrader = 12,
        BenchmarkHero = 13,
        PayPal = 14,
        GrowthHero = 15,
        PayPalUS = 16,
        YouTube = 17,
        FbLead = 18,
        ProductDescriber = 19,
        GoogleAds = 20,
        TrustBadges = 21,

        //shopify apps starts from 100
        AISeoProductOptimizer = 111, //Shopify_StoreyaApp + 100
        WhatsApp = 112,
    }

    public enum RelationStatus
    {
        NotResponsive = -1,
        OK = 0,
        DoNotContact = -10,
        WebsiteOffline = -100
    }
    public enum LoginType
    {
        EmailPassword = 1,
        GoogleAuth = 2
    }

    public static class AccountManager
    {
        public static void CancelPayment(Shop shop, User user, List<ShopApp> apps)
        {
            Log4NetLogger.Info("All subscriptions of paid shop apps will be cancelled.", shop.ID);
            var shopSubscriptions = SubscriptionManager.GetActiveShopSubscriptions(shop.ID);
            foreach (var shopSubscription in shopSubscriptions)
            {
                CancelPaidAppSubscription(shopSubscription, user.ID);
            }

            //string messageAboutIsCancelled = ""; // "A subscription/charge was NOT cancelled.";
            //string bsSubscriptionToCancel = null;

            //if (shop.IsPaid.HasValue && AppStoreManager.IsFbShopVeteran(shop.IsPaid.Value))
            //{
            //    Log4NetLogger.Info("Extracting BsSubscriptionId for veteran client to cancel.", shop.ID);
            //    bsSubscriptionToCancel = SubscriptionManager.GetBlueSnapSubscriptionIDForVeteran(shop.ID);
            //    if (string.IsNullOrEmpty(bsSubscriptionToCancel))
            //    {
            //        messageAboutIsCancelled = "BsSubscriptionId to cancel was not discovered for veteran client. Please, check all client's subscriptions.";
            //        Log4NetLogger.Info(messageAboutIsCancelled, shop.ID);
            //        SendEmail(shop.ID, user.ID, messageAboutIsCancelled);
            //    }
            //    else
            //    {
            //        bool isCancelled = SubscriptionManager.UpdateSubscriptionStatus(bsSubscriptionToCancel, BluesnapSubscriptionStatus.Cancelled, shop.ID);
            //        if (isCancelled)
            //        {
            //            messageAboutIsCancelled = string.Format("The veteran BsSubscription {0} was cancelled in BlueSnap.", bsSubscriptionToCancel);
            //            Log4NetLogger.Info(messageAboutIsCancelled, shop.ID);
            //        }
            //        else
            //        {
            //            messageAboutIsCancelled = string.Format("The veteran BsSubscription {0} was NOT cancelled in BlueSnap.", bsSubscriptionToCancel);
            //            Log4NetLogger.Info(messageAboutIsCancelled, shop.ID);
            //            SendEmail(shop.ID, user.ID, messageAboutIsCancelled);
            //        }
            //    }
            //}
            //else
            //{
            //    Log4NetLogger.Info("All subscriptions of paid shop apps will be cancelled.", shop.ID);

            //    //TODO:agree get all BS subscriptions and cancel them (we migh to check if has multiple subscriptins per app)

            //    //foreach (ShopApp paidApp in apps)
            //    //{
            //    //    CancelPaidAppSubscription(shop.ID, user.ID, paidApp.SubscriptionID, paidApp.AppTypeID);
            //    //    if (paidApp.HasAgreements.HasValue && paidApp.HasAgreements.Value == 1)
            //    //    {
            //    //        var sub = SubscriptionManager.GetShopAgreementSubscription(shop.ID);
            //    //        Log4NetLogger.Info(string.Format("Subscription {1} With Agreement of {0} will be cancelled ", AppStoreManager.GetAppByID(paidApp.AppTypeID).AppName, sub.BlueSnapSubscriptionID), shop.ID);
            //    //        CancelPaidAppSubscription(shop.ID, user.ID, sub.ID, paidApp.AppTypeID);
            //    //    }
            //    //}
            //    var shopSubscriptions = SubscriptionManager.GetActiveShopSubscriptions(shop.ID);
            //    foreach (var shopSubscription in shopSubscriptions)
            //    {
            //        CancelPaidAppSubscription(shopSubscription, user.ID);
            //    }
            //}

        }
        public static void CancelPaidAppSubscription(int shopID, int userID, int? subscriptionID, int appTypeID)
        {
            string messageAboutIsCancelled = string.Empty;
            if (SubscriptionManager.IsPaymentGateWaySubscription(subscriptionID))
            {
                ShopSubscription shopSubscription = SubscriptionManager.GetShopSubscription(subscriptionID.Value);
                if (shopSubscription != null)
                {
                    CancelPaidAppSubscription(shopSubscription, userID);
                }

                else
                {
                    messageAboutIsCancelled = string.Format("The StoreYa ShopSubscription {0} was not found in DB when there is a paid shop app record referencing to this ID. SHOULD BE CHECKED.", subscriptionID.ToString());
                    Log4NetLogger.Info(messageAboutIsCancelled, shopID);
                    SendEmail(shopID, userID, messageAboutIsCancelled);
                }
            }
            else
            {
                messageAboutIsCancelled = string.Format("{0} paid app has no subscription to cancel. This paid app SHOULD BE CHECKED.", AppStoreManager.GetAppByID(appTypeID).AppName);
                Log4NetLogger.Info(messageAboutIsCancelled, shopID);
                SendEmail(shopID, userID, messageAboutIsCancelled);
            }

        }
        public static void CancelPaidAppSubscription(ShopSubscription shopSubscription, int userID)
        {
            int shopID = shopSubscription.ShopID;
            PaymentAdapterTypes paymentAdapterType = FastSpringHelper.ConvertToPaymentProvider(shopSubscription.PaymentAdapterType);
            if (!int.TryParse(shopSubscription.AppIDs, out int appTypeID))
            {
                string[] AppIDs = shopSubscription.AppIDs.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                appTypeID = int.Parse(AppIDs[0]);
                Log4NetLogger.Info(string.Format("Subscription of the following apps ids {0} will be cancelled in BlueSnap. ", shopSubscription.AppIDs), shopID);
            }
            if (shopSubscription.AgreeID.HasValue && shopSubscription.AgreeID > 0)
            {
                Log4NetLogger.Info(string.Format("Subscription {1} With Agreement of {0} will be cancelled ", AppStoreManager.GetAppByID(appTypeID).AppName, shopSubscription.OriginalSubscriptionID), shopID);
            }
            string messageAboutIsCancelled = string.Empty;
            if (SubscriptionManager.IsPaymentGateWaySubscription(shopSubscription.ID))
            {

                bool isCancelled = false;

                //if (shopSubscription.PaymentAdapterType == (int)PaymentAdapterTypes.BlueSnap)
                //{
                Log4NetLogger.Info($"Subscription of {AppStoreManager.GetAppByID(appTypeID).AppName} will be cancelled in {paymentAdapterType}.", shopID);

                isCancelled = SubscriptionManager.UpdateSubscriptionStatus(shopSubscription.OriginalSubscriptionID.ToString(), BluesnapSubscriptionStatus.Cancelled, shopID, paymentAdapterType);
                //}

                if (isCancelled)
                {
                    messageAboutIsCancelled = $"The Subscription {shopSubscription.OriginalSubscriptionID} was cancelled in {paymentAdapterType}.";
                    Log4NetLogger.Info(messageAboutIsCancelled, shopID);
                }
                else
                {
                    messageAboutIsCancelled = $"The Subscription {shopSubscription.OriginalSubscriptionID} was NOT cancelled in {paymentAdapterType}.";

                    Log4NetLogger.Info(messageAboutIsCancelled, shopID);
                    SendEmail(shopID, userID, messageAboutIsCancelled);
                }

            }
            else
            {
                messageAboutIsCancelled = string.Format("{0} paid app has no subscription to cancel. This paid app SHOULD BE CHECKED.", AppStoreManager.GetAppByID(appTypeID).AppName);
                Log4NetLogger.Info(messageAboutIsCancelled, shopID);
                SendEmail(shopID, userID, messageAboutIsCancelled);
            }
        }
        public static void SendEmail(int shopID, int userID, string messageAboutIsCancelled)
        {
            string body = $"User just cancelled his paid account - https://bo.storeya.com/Users/<USER>/{shopID}?userId={userID}. {messageAboutIsCancelled}";
            string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
            EmailHelper.SendEmail(email, "Paid Account cancelation - shop " + shopID, body, null, null, true, "SYSTEM");
        }


        public static void RemoveAllScriptsAssociatedWithAccount(Shop shop, User user)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            if (user.OriginMarketplace == (int)OriginMarketplaces.Shopify)
            {
                DeleteShopifyScript(shop, db);
            }

            //if (user.UserType == (int)UserTypes.Lexity)
            //{
            //    DeleteLexityScriptsOfAllCouponPopsAssociatedWithAccount(shop, user, db);
            //}

            //if (user.OriginMarketplace == (int)OriginMarketplaces.Storenvy)
            //{
            //    DeleteStoreYaScriptFromStorenvyStoreLayout(shop, db);
            //}

            if (user.OriginMarketplace == (int)OriginMarketplaces.Wix)
            {
                WixConnectedSite site = db.WixConnectedSites.Where(s => s.ShopID == shop.ID).SingleOrDefault();
                if (site != null)
                {
                    db.WixConnectedSites.Remove(site);
                    db.SaveChanges();
                }
            }

            if (user.OriginMarketplace == (int)OriginMarketplaces.Yext)
            {
                YextConnectedSite site = db.YextConnectedSites.Where(s => s.ShopID == shop.ID).SingleOrDefault();
                if (site != null)
                {
                    db.YextConnectedSites.Remove(site);
                    db.SaveChanges();
                }
            }
        }


        //private static void DeleteLexityScriptsOfAllCouponPopsAssociatedWithAccount(Shop shop, User user, StoreYaEntities db)
        //{
        //    LexityStore store = db.LexityStores.Where(s => s.ShopID == shop.ID && s.UserID == user.ID).SingleOrDefault();
        //    LexityApiProvider lexityApiProvider = new LexityApiProvider(store.StoreID, store.ConnectionPassword);
        //    try
        //    {
        //        var q = from lexityScript in db.LexityScripts
        //                join couponPop in db.CouponPops on lexityScript.StoreyaScriptID equals couponPop.ID into scripts
        //                from script in scripts.DefaultIfEmpty()
        //                select new { LexityScript = lexityScript, CouponPopId = script.ID };

        //        foreach (var item in q)
        //        {
        //            if (item.LexityScript.LexityScriptID.HasValue)
        //            {
        //                bool deleted = lexityApiProvider.DeletScriptBlob(item.LexityScript.LexityScriptID.Value);
        //                if (deleted)
        //                {
        //                    Log4NetLogger.Info(string.Format("The scriptID: {0} was deleted via Lexity API. couponPopID: {1}", item.LexityScript.LexityScriptID, item.CouponPopId), shop.ID);

        //                    LexityScript scriptToDelete = db.LexityScripts.Where(s => s.LexityScriptID == item.LexityScript.LexityScriptID.Value).SingleOrDefault();
        //                    if (scriptToDelete != null)
        //                    {
        //                        db.LexityScripts.Remove(scriptToDelete);
        //                        db.SaveChanges();
        //                        Log4NetLogger.Info(string.Format("The lexity script {0} was deleted from DB. couponPopID: {1} ", item.LexityScript.LexityScriptID.Value, item.CouponPopId), shop.ID);

        //                    }
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log4NetLogger.Error(string.Format("Failed on deletion lexity script via Lexity."), ex, shop.ID);
        //    }
        //}

        //private static void DeleteStoreYaScriptFromStorenvyStoreLayout(Shop shop, StoreYaEntities db)
        //{
        //    StorenvyConnectedShop connectedShop = db.StorenvyConnectedShops.Where(s => s.ShopID == shop.ID).SingleOrDefault();
        //    if (connectedShop != null)
        //    {
        //        Log4NetLogger.Info(string.Format("Deleting storeya script via Storenvy API."), shop.ID);

        //        StorenvyConnector storenvyConnector = new StorenvyConnector(connectedShop.AccessToken, shop.ID);
        //        try
        //        {
        //            if (storenvyConnector.UpdateStoreLayout(false))
        //            {
        //                Log4NetLogger.Info("Store layout was updated via Storenvy, the storeya script was deleted.", shop.ID);
        //            }
        //        }
        //        catch (Exception ex)
        //        {
        //            Log4NetLogger.Error("Failed on updating store layout via Storenvy.", ex, shop.ID);
        //        }
        //    }
        //}

        private static void DeleteShopifyScript(Shop shop, StoreYaEntities db)
        {
            List<ShopifyConnectedShop> shopifyConnectedShopSettings = db.ShopifyConnectedShops.Where(s => s.ShopID == shop.ID).ToList();
            if (shopifyConnectedShopSettings != null && shopifyConnectedShopSettings.Count > 0)
            {
                foreach (ShopifyConnectedShop settings in shopifyConnectedShopSettings)
                {
                    if (settings.ScriptID.HasValue && settings.ScriptID > 0)
                    {
                        string scripIDToDetete = settings.ScriptID.ToString();
                        try
                        {
                            ShopifyConnector shopifyConnector = new ShopifyConnector(shop.ID, settings.StoreyaAppTypeID);
                            shopifyConnector.DeleteScript(scripIDToDetete);
                            settings.ScriptID = null;
                            db.SaveChanges();
                            Log4NetLogger.Info(string.Format("The Shopify script {0} was deleted via Shopify API and deleted from DB at the account cancellation for {1} application", scripIDToDetete, settings.StoreyaAppTypeID.ToString()), shop.ID);
                        }
                        catch (Exception ex)
                        {
                            Log4NetLogger.Info(string.Format("Failed to delete scriptID: {0} via Shopify API at the account cancellation for {1} application", scripIDToDetete, settings.StoreyaAppTypeID.ToString()), ex, shop.ID);
                        }
                    }
                }

                foreach (ShopifyConnectedShop shopifyConnectedShop in shopifyConnectedShopSettings)
                {
                    shopifyConnectedShop.ShopID = -shop.ID;
                    shopifyConnectedShop.UpdatedAt = DateTime.Now;
                    db.SaveChanges();
                }

            }
        }
        public static List<ShopifyAppCharge> GetShopifyAppActiveChargesByNameAndAppID(string shopName, int appID)
        {
            var db = DataHelper.GetStoreYaEntities();
            return db.ShopifyAppCharges.Where(x => x.ShopifyShopName == shopName && x.StoreyaAppTypeID == appID && x.ChargeStatus == "active").ToList();
        }

    }
}
