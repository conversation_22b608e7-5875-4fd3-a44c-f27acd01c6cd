﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;
using System.Collections.Specialized;
using System.Web;


namespace Storeya.Core.Models.Account
{
    public class EmailAccountManager
    {
        //private static string SEED = "TYUOJHGJHTJHJGHRHJNKL";


        public enum EmailAccountErrors
        {
            EmailAlreadyExists
        }

        public enum EmailAccountValidationStatuses
        {
            OK,
            Failed,
            FailedButEmailFoundAsFacebookAccount,
            ShouldBeBOLoggedIN
        }

        public UserCreationReponse Create(string email, string password, byte? userType = null, int agencyID = 0, int refererID = 0, byte? originMarketplace = null, string name = null, string email2 = null, string locale = null)
        {
            if (!string.IsNullOrEmpty(email))
                email = email.Trim();

            UserCreationReponse response = new UserCreationReponse();
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            try
            {

                if (!EmailHelper.IsValidEmail(email))
                {
                    return ResponseWithError("Invalid email address");
                }

                //validation
                var existingEmailUser = GetActiveEmailUser(email);

                if (existingEmailUser != null && existingEmailUser.ID != 0)
                {
                    return ResponseWithError("This email address is already registered with another account.");
                }

                if (IsActiveFacebookUserExist(email))
                {
                    return ResponseWithError("You already have a facebook account with us. Please log in using Facebook.");
                }

                //add new
                User user = new User();
                user.FbProfileID = 1; //use 1 to mark user as email user              
                user.Email = email;
                user.Email2 = email2;
                if (!string.IsNullOrEmpty(name))
                {
                    user.Name = name;
                }
                //user.Name = !string.IsNullOrEmpty(name)? name : email;
                user.Password = PasswordHelper.EncodeString(password);
                user.InsertedAt = DateTime.Now;
                user.AgencyID = agencyID;
                user.RefererID = refererID;
                user.UserType = userType;
                user.OriginMarketplace = originMarketplace;
                user.SignupIP = HttpHelper.GetIP();
                user.Locale = locale;
                //create storeya user in DB
                var u = db.Users.Add(user);
                db.SaveChanges();
                response.User = user;
                try
                {
                    LauncherHelper.InitCommand(null, user, "UPDATE_SINGLE_USER_DATA", u.ID.ToString());
                }
                catch
                {

                }
                return response;
            }
            catch (Exception ex)
            {
                response.Status = EmailAccountValidationStatuses.Failed;
                response.Message = "Failed to generate user";
                Log4NetLogger.Error("Failed to generate user", ex);
                return response;
            }
        }



        private UserCreationReponse ResponseWithError(string message)
        {
            UserCreationReponse response = new UserCreationReponse();
            response.Status = EmailAccountValidationStatuses.Failed;
            response.Message = message;
            return response;
        }

        public static User GetActiveEmailUser(string email)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            if (db.Users.Where(u => u.FbProfileID == 1 && u.Email == email).Any())
            {
                return db.Users.Where(u => u.FbProfileID == 1 && u.Email == email).SingleOrDefault();
            }
            else
            {
                if (db.Users.Where(u => u.FbProfileID == 1 && u.Email == "DONT_SEND_" + email).Any())
                {
                    User user = db.Users.Where(u => u.FbProfileID == 1 && u.Email == "DONT_SEND_" + email).SingleOrDefault();
                    user.Email = user.Email.Remove(0, 10);
                    db.SaveChanges();
                }

                return db.Users.Where(u => u.FbProfileID == 1 && u.Email == email).SingleOrDefault();
            }
        }
        public static User ConvertFacebookUserToEmailUser(string email)
        {
            if (IsActiveFacebookUserExist(email))
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                var fbUsers = db.Users.Where(u => u.Email == email && u.FbProfileID > 0 && u.FbProfileID != 1 && ((u.FbProfileID % 10000) != 0)).OrderByDescending(u => u.InsertedAt).ToList();
                foreach (var fbUser in fbUsers)
                {
                    //add shopID
                    int shopId = Log4NetLogger.SpecialShopIDs.FBLoginRemove.GetHashCode();
                    var shop = db.Shops.Where(s => s.UserID == fbUser.ID).OrderByDescending(o => o.InsertedAt).FirstOrDefault();
                    if (shop != null)
                    {
                        shopId = shop.ID;
                    }
                    Log4NetLogger.Info($"User {email} was converted from Facebook user {fbUser.FbProfileID} to email user.", null, shopId);
                    fbUser.FbProfileID = 1;
                }
                db.SaveChanges();
                return fbUsers.FirstOrDefault();
            }
            return null;
        }
        public static bool IsActiveFacebookUserExist(string email)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            if (db.Users.Where(u => u.Email == email && u.FbProfileID > 0 && u.FbProfileID != 1 && ((u.FbProfileID % 10000) != 0)).Any())
            {
                return true;
            }
            else
                return false;
        }

        public static bool IsEmailHasActiveAccount(string email)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            if (db.Users.Where(u => u.Email == email && u.FbProfileID > 0 && ((u.FbProfileID % 10000) != 0)).Any())
            {
                return true;
            }
            else
                return false;
        }

        public static User GetActiveAccount(string email)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            if (db.Users.Where(u => u.Email == email && u.FbProfileID > 0 && ((u.FbProfileID % 10000) != 0)).Any())
            {
                return db.Users.Where(u => u.Email == email && u.FbProfileID > 0 && ((u.FbProfileID % 10000) != 0)).SingleOrDefault();

            }
            else
                return null;
        }


        public UserValidationReponse Validate(string email, string password, bool includeFacebook = false)
        {
            UserValidationReponse response = new UserValidationReponse();
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            string passwordEncripted = PasswordHelper.EncodeString(password);

            var singleUser = GetActiveEmailUser(email);
            if (includeFacebook && singleUser == null)
            {
                singleUser = GetActiveAccount(email);
            }
            if (singleUser != null && singleUser.Password == passwordEncripted)
            {
                response.User = singleUser;
                if (singleUser.BoRole != null)
                {
                    response.Status = EmailAccountValidationStatuses.ShouldBeBOLoggedIN;
                }
                else
                {
                    response.Status = EmailAccountValidationStatuses.OK;
                }
                return response;
            }

            if (singleUser == null && IsActiveFacebookUserExist(email))
            {
                response.Message = "For security reasons,</br>We have shifted your Facebook login to an email login.</br>Kindly click <a href='https://www.storeya.com/home/<USER>'>here</a> to reset your password";
                //response.Message = "Click \"Log in using Facebook\". (this email is related to a Facebook profile you have opened an account with).";
                response.User = singleUser;
                response.Status = EmailAccountValidationStatuses.FailedButEmailFoundAsFacebookAccount;
                return response;
            }

            response.Status = EmailAccountValidationStatuses.Failed;
            return response;
        }

        public static string GetEmailFromToken(string t)
        {
            string token = PasswordHelper.DecodeToken(t);
            NameValueCollection tokenValue = HttpUtility.ParseQueryString(token);

            DateTime validTillDatetime = new DateTime(Convert.ToInt64(tokenValue["d"]));
            if (validTillDatetime >= DateTime.Now)
            {
                return tokenValue["e"];
            }
            return "";
        }

        public void SetPassword(string email, string password)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            string passwordEncripted = PasswordHelper.EncodeString(password);

            var user = GetActiveAccount(email);
            if (user != null)
            {
                user.Password = passwordEncripted;
            }
            else if (email.Contains(" "))
            {
                email = email.Replace(" ", "+");
                user = GetActiveAccount(email);
                if (user != null)
                {
                    user.Password = passwordEncripted;
                }
            }

            db.SaveChanges();

            return;
        }

        public static string CreateResetPasswordLink(string email)
        {
            string tokenPattern = "e={0}&d={1}";
            string token = PasswordHelper.EncodeToken(string.Format(tokenPattern, email, DateTime.Now.AddHours(48).Ticks));

            string link = "https://www.storeya.com/emailaccount/resetpassword?t=" + HttpUtility.UrlEncode(token);
            return link;
            //var mailData = new { link = HttpHelper.GetCurrentDomain() + "/emailaccount/resetpassword?t=" + HttpUtility.UrlEncode(token) };

        }


        ///// <summary>
        ///// Gets the token for invitation.
        ///// </summary>
        ///// <param name="email">The email.</param>
        ///// <returns></returns>
        //public static string GetTokenForInvitation(string email)
        //{
        //    if (String.IsNullOrEmpty(email))
        //        throw new ArgumentException("The email cannot be null");

        //    string token = PasswordHelper.EncodeMessageWithPassword(String.Format("{0}#{1}", email, DateTime.Now), SEED);

        //    return token;
        //}


        ///// <summary>
        ///// Gets the email from token.
        ///// </summary>
        ///// <param name="token">The token.</param>
        ///// <param name="email">The email.</param>
        ///// <returns></returns>
        //public static bool GetEmailFromToken(string token, out string email)
        //{
        //    email = String.Empty;


        //    string message = PasswordHelper.DecodeMessageWithPassword(token, SEED);
        //    string[] messageParts = message.Split('#');

        //    if (messageParts.Count() != 2)
        //    {
        //        return false;
        //        // the token was not generated correctly.
        //    }
        //    else
        //    {
        //        email = messageParts[0];
        //        return true;
        //    }
        //}



        ///// <summary>
        ///// Helper function used to generate a token to be used in the message sent to users when registered the first time to confirm their email address.
        ///// </summary>
        ///// <param name="email">The email address to encode.</param>
        ///// <returns>The token generated from the email address, timestamp, and SEED value.</returns>
        //public static string GetTokenForValidation(string email)
        //{
        //    if (String.IsNullOrEmpty(email))
        //        throw new ArgumentException("The email cannot be null");

        //    string token = PasswordHelper.EncodeMessageWithPassword(String.Format("{0}#{1}", email, DateTime.Now), SEED);

        //    return token;
        //}


        ///// <summary>
        ///// Validates whether a given token is valid for a determined email address.
        ///// </summary>
        ///// <param name="token">The token to validate.</param>
        ///// <param name="email">The email address to use in the validation.</param>
        ///// <returns><c>true</c> if the token is valid, <c>false</c> otherwise.</returns>
        //public static bool IsTokenValid(string token, string email)
        //{
        //    return IsTokenValid(token, email, DateTime.Now);
        //}


        ///// <summary>
        ///// Core method to validate a token that also offers a timestamp for testing.  In production mode should always be DateTime.Now.
        ///// </summary>
        ///// <param name="token">The token to validate.</param>
        ///// <param name="email">the email address to use in the validation.</param>
        ///// <param name="timestamp">The timestamp representing the time in which the validation is performed.</param>
        ///// <returns><c>true</c> if the token is valid, <c>false</c> otherwise.</returns>
        //public static bool IsTokenValid(string token, string email, DateTime timestamp)
        //{
        //    if (String.IsNullOrEmpty(token))
        //        throw new ArgumentException("The token cannot be null");

        //    try
        //    {
        //        string message = PasswordHelper.DecodeMessageWithPassword(token, SEED);
        //        string[] messageParts = message.Split('#');

        //        if (messageParts.Count() != 2)
        //        {
        //            return false;
        //            // the token was not generated correctly.
        //        }
        //        else
        //        {
        //            string messageEmail = messageParts[0];
        //            string messageDate = messageParts[1];

        //            // If the emails are the same and the date in which the token was created is no longer than 5 days, then it is valid. Otherwise, it is not. 
        //            return (String.Compare(email, messageEmail, true) == 0 && timestamp.Subtract(DateTime.Parse(messageDate)).Days < 5);
        //        }
        //    }
        //    catch (Exception)
        //    {
        //        // could not decrypt the message. The token has been tampered with.
        //        return false;
        //    }
        //}

        public class UserValidationReponse
        {
            public User User { get; set; }
            public EmailAccountValidationStatuses Status { get; set; }
            public string Message { get; set; }
        }


        public class UserCreationReponse
        {
            public User User { get; set; }
            public EmailAccountValidationStatuses Status { get; set; }
            public string Message { get; set; }
        }
    }
}
