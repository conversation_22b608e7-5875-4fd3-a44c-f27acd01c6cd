﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    public class AdCopyValidator
    {

        public static string ToSearchHeadline(string text)
        {
            text = ToAdText(text);
            if (string.IsNullOrWhiteSpace(text))
            {
                return null;
            }

            text = text.Replace("!", "");

            return text;
        }

        public static string ToAdText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return "";
            }

            string fixed_text = null;

            text = text.Replace("iPhone", "Phone"); //trademark

            string[] words = text.Split(' ');
            if (words != null && words.Length > 0)
            {
                List<string> fixed_words = new List<string>();
                for (int i = 0; i < words.Length; i++)
                {
                    string fixed_word = "";
                    string wordToTreat = words[i];

                    if (string.IsNullOrEmpty(wordToTreat))
                        continue;

                    if (!wordToTreat.StartsWith("-") && !wordToTreat.EndsWith("-") && wordToTreat.Contains('-')) //more then 3 chars
                    {
                        string[] words_1 = wordToTreat.Split('-');

                        List<string> fixed_words_1 = new List<string>();
                        for (int j = 0; j < words_1.Length; j++)
                        {
                            string fixed_word_1 = Improve(words_1[j]);
                            if (!string.IsNullOrEmpty(fixed_word_1))
                                fixed_words_1.Add(fixed_word_1);
                        }
                        fixed_word = String.Join("-", fixed_words_1);
                    }
                    else
                    {
                        fixed_word = Improve(wordToTreat);
                    }

                    if (!string.IsNullOrEmpty(fixed_word))
                        fixed_words.Add(fixed_word);
                }
                fixed_text = String.Join(" ", fixed_words);

            }
            else
            {
                fixed_text = Improve(text);
            }

            if (fixed_text.Length > 0 && Char.IsLower(fixed_text.First()))
                fixed_text = Char.ToUpper(fixed_text.First()) + ((fixed_text.Length > 1) ? fixed_text.Substring(1) : "");

            if (fixed_text.Contains(". "))
            {
                List<string> fixed_sentences = new List<string>();

                string[] words_2 = fixed_text.Split(new string[] { ". " }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < words_2.Length; i++)
                {
                    string sentence = words_2[i];
                    if (!string.IsNullOrEmpty(sentence))
                    {
                        sentence = Char.ToUpper(sentence.First()) + ((sentence.Length > 1) ? sentence.Substring(1) : "");
                        fixed_sentences.Add(sentence);
                    }                
                      
                }

                fixed_text = String.Join(". ", fixed_sentences);
            }

            return fixed_text;
        }

        public static string Improve(string word)
        {
            /*
            - Capital letters only for first letter of the word (note languages)
                Except: a, to, were, was, for, the, or, and, in, on, at, up, that, of, by, any
            - No such symbols - ~, *, ^, and |
            - No repeating symbols $$
             */

            if (string.IsNullOrEmpty(word))
                return "";

            if (word.ToLower() == "a"
                || word.ToLower() == "to"
                || word.ToLower() == "were"
                || word.ToLower() == "was"
                || word.ToLower() == "for"
                || word.ToLower() == "the"
                || word.ToLower() == "or"
                || word.ToLower() == "and"
                || word.ToLower() == "in"
                || word.ToLower() == "on"
                || word.ToLower() == "at"
                || word.ToLower() == "up"
                || word.ToLower() == "that"
                || word.ToLower() == "of"
                || word.ToLower() == "by"
                || word.ToLower() == "any"
                || word.ToLower() == "is"
                || word.ToLower() == "are")
            {
                return word.ToLower();
            }

            string fixed_word = "";

            char prev_char = word.First();

            for (int i = 0; i < word.Length; i++)
            {
                char char_to_fix = word[i];

                if (char_to_fix == '-' || char_to_fix == '~' || char_to_fix == '*' || char_to_fix == '^' || char_to_fix == '|')
                {
                    continue;
                }
                if (i != 0
                    && (char_to_fix == '!'
                    || char_to_fix == '@'
                    || char_to_fix == '#'
                    || char_to_fix == '$'
                    || char_to_fix == '%'
                    || char_to_fix == '&'
                    || char_to_fix == '@'
                    || char_to_fix == '('
                    || char_to_fix == ')'
                    || char_to_fix == '_'
                    || char_to_fix == '='
                    || char_to_fix == '+'
                    || char_to_fix == '\\'
                    || char_to_fix == '/'
                    || char_to_fix == '?'
                    || char_to_fix == '.'
                    || char_to_fix == ','
                    || char_to_fix == ';'
                    || char_to_fix == ':'
                    || char_to_fix == '<'
                    || char_to_fix == '>')
                    && prev_char == char_to_fix)
                {
                    continue;
                }

                // improve

                if (i != 0 && Char.IsUpper(char_to_fix) && word.Length > 3)
                {
                    char_to_fix = Char.ToLower(char_to_fix);
                }

                fixed_word += char_to_fix;
                prev_char = char_to_fix;
            }

            if (fixed_word.Length > 0)
                fixed_word = Char.ToUpper(fixed_word.First()) + ((fixed_word.Length > 1) ? fixed_word.Substring(1) : "");

            return fixed_word;
        }
    }
}
