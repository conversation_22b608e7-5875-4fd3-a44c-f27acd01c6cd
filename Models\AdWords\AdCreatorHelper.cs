﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Models.AdWords
{
    public class AdCreatorHelper
    {
        public static RSAAdsData GetAdDTOForSearchAndRLSACampaigns(TrafficBooster tbSettings, string domain, bool useAdCopiesIfExist)
        {
            RSAAdsData rSAAdsData = null;
            if (useAdCopiesIfExist == true)
            {
                rSAAdsData = GetRSAAdsDataFilledByAdCopies(tbSettings.ShopID.Value);
                if (rSAAdsData != null)
                {
                    return rSAAdsData;
                }
            }
            TbCategoriesHelper.Category category = TbCategoriesHelper.GetCategory(tbSettings);
            rSAAdsData = SearchAdWordsCampaign.GetRSAdsByCategory(category?.ID, domain, tbSettings.Country);
            rSAAdsData.DefaultAds = true;
            return rSAAdsData;
        }
        public static RSAAdsData GetRSAAdsDataFilledByAdCopies(int shopID)
        {
            var customCopies = AdCopiesManager.GetAll(shopID);
            if (customCopies != null)
            {
                var headers = customCopies.Where(c => c.CopyType == (int)CopyType.Header && c.Text != null && c.Text != "").Select(x => x.Text).ToList();
                var descriotions = customCopies.Where(c => c.CopyType == (int)CopyType.Description && c.Text != null && c.Text != "").Select(x => x.Text).ToList();
                if (headers.Count > 2 && descriotions.Count > 1)
                {
                    RSAAdsData rSAAdsData = new RSAAdsData();
                    rSAAdsData.Description1 = descriotions.ElementAtOrDefault(0);
                    rSAAdsData.Description2 = descriotions.ElementAtOrDefault(1);
                    rSAAdsData.Description3 = descriotions.ElementAtOrDefault(2);
                    rSAAdsData.Description4 = descriotions.ElementAtOrDefault(3);

                    rSAAdsData.Headline1 = headers.ElementAtOrDefault(0);
                    rSAAdsData.Headline2 = headers.ElementAtOrDefault(1);
                    rSAAdsData.Headline3 = headers.ElementAtOrDefault(2);
                    rSAAdsData.Headline4 = headers.ElementAtOrDefault(3);
                    rSAAdsData.Headline5 = headers.ElementAtOrDefault(4);
                    rSAAdsData.Headline6 = headers.ElementAtOrDefault(5);
                    rSAAdsData.Headline7 = headers.ElementAtOrDefault(6);
                    rSAAdsData.Headline8 = headers.ElementAtOrDefault(7);
                    rSAAdsData.Headline9 = headers.ElementAtOrDefault(8);
                    rSAAdsData.Headline10 = headers.ElementAtOrDefault(9);
                    rSAAdsData.Headline11 = headers.ElementAtOrDefault(10);
                    rSAAdsData.Headline12 = headers.ElementAtOrDefault(11);
                    rSAAdsData.Headline13 = headers.ElementAtOrDefault(12);
                    rSAAdsData.Headline14 = headers.ElementAtOrDefault(13);
                    rSAAdsData.Headline15 = headers.ElementAtOrDefault(14);
                    rSAAdsData.DefaultAds = false;
                    return rSAAdsData;
                }
            }
            return null;
        }
    }
}
