﻿
using Microsoft.SqlServer.Server;
using Storeya.Core.Helpers;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models.UserIntelligence;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace Storeya.Core.Models.AdWords
{
    public static class AdWordsPixelParser
    {

        public static string GetGuideUrl(string platform)
        {
            string url = "";
            platform = platform.ToLower();

            if (platform == "shopify") //(int)CatalogSourcePlatforms.ShopifyApi)
            {
                url = "https://storeya.zendesk.com/hc/en-us/articles/221190467";
            }
            else if (platform == "bigcommerce")//(int)CatalogSourcePlatforms.BigCommerce)
            {
                //url = "https://storeya.zendesk.com/hc/en-us/articles/221161468";
                url = "https://storeya.zendesk.com/hc/en-us/articles/19765531980946";
            }
            else if (platform == "magento") // (int)CatalogSourcePlatforms.MagentoApi)
            {
                url = "https://storeya.zendesk.com/hc/en-us/articles/221190947";
            }
            else if (platform == "ecwid") // (int)CatalogSourcePlatforms.Ecwid)
            {
                url = "https://storeya.zendesk.com/hc/en-us/articles/221162348";
            }
            else if (platform == "shift4shop") 
            {
                url = "https://storeya.zendesk.com/hc/en-us/articles/20229717597714";
            }
            else if (platform == "woocommerce")
            {
                //old -url = "https://storeya.zendesk.com/hc/en-us/articles/221429827";
                url = "https://storeya.zendesk.com/hc/en-us/articles/360019540160";
            }
            else if (platform == "easystore")
            {
                url = "https://storeya.zendesk.com/hc/en-us/articles/225406687";
            }
            else if (platform == "opencart")
            {
                url = "https://storeya.zendesk.com/hc/en-us/articles/115000569268";
            }
            else if (platform == "wix")
            {
                url = "https://storeya.zendesk.com/hc/en-us/articles/4404294947218-How-to-Track-Revenues-with-a-Wix-Store-";
            }
            else if (platform == "neto")
            {
                url = "https://storeya.zendesk.com/knowledge/articles/4405238245906";
            }
            else if (platform == "any site")
            {
                url = "https://storeya.zendesk.com/hc/en-us/articles/19917642047378";
            }

            return url;
        }


        //<!-- Google Code for Thank you page convesion tracker Conversion Page -->
        //<script type="text/javascript">
        ///* <![CDATA[ */
        //var google_conversion_id = 928249416;
        //var google_conversion_language = "en";
        //var google_conversion_format = "3";
        //var google_conversion_color = "ffffff";
        //var google_conversion_label = "oPXPCIbH1mcQyOzPugM";
        //var google_conversion_value = 1.00;
        //var google_conversion_currency = "USD";
        //var google_remarketing_only = false;
        ///* ]]> */
        //</script>
        //<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
        //</script>
        //<noscript>
        //<div style="display:inline;">
        //<img height="1" width="1" style="border-style:none;" alt="" src="//www.googleadservices.com/pagead/conversion/928249416/?value=1.00&amp;currency_code=USD&amp;label=oPXPCIbH1mcQyOzPugM&amp;guid=ON&amp;script=0"/>
        //</div>
        //</noscript>

        //public static bool UseGTag(int shopID)
        //{
        //    int shopIDToStartInt = 10000000;
        //    var shopToStartFrom = ConfigHelper.GetValue("StartTestingGTagFromShopID");
        //    if (!string.IsNullOrEmpty(shopToStartFrom))
        //    {
        //        shopIDToStartInt = Convert.ToInt32(shopToStartFrom);
        //    }
        //    //return(shopID % 2 != 0) && (shopID > shopIDToStartInt) && (ConfigHelper.GetBoolValue("DontUseGtagConversion") == false);
        //    return (shopID > shopIDToStartInt) && (ConfigHelper.GetBoolValue("DontUseGtagConversion") == false);
        //}


        public static string GetShopifyCustomPixel(int shopId)
        {
            string conversionID = Shopify.ShopifyThemesManager.ExtractGoogleConversionID(shopId, out string googleConversionLabel);
            string customPixelTemplate = Properties.Resources.ShopifyCustomPixel;


            customPixelTemplate = customPixelTemplate.Replace("XXXXXXXXX", conversionID);
            string finalCode = customPixelTemplate.Replace("YYYYYYYYY", googleConversionLabel);

            return finalCode;
        }

        public static string GetCode(string platform, string originCode, out bool needToEncode, int shopId)
        {
            needToEncode = false;
            if (platform.ToLower() == "shopify") //(int)CatalogSourcePlatforms.ShopifyApi)
            {
                var db = DataHelper.GetStoreYaEntities();

                //var shop = db.Shops.Where(s => s.ID == shopId).SingleOrDefault();
                //var currency = shop?.Currency;

                string conversionID = Shopify.ShopifyThemesManager.ExtractGoogleConversionID(shopId, out string googleConversionLabel);
                string code = Properties.Resources.AdWordsPixelShopify___Enhanced___Gtag;
                originCode = MakeReplacementsForShopifyGTag(code, conversionID, googleConversionLabel);
                needToEncode = true;
            }
            else if (platform.ToLower() == "bigcommerce")//(int)CatalogSourcePlatforms.BigCommerce)
            {
                originCode = MakeReplacementsForBigcommerce(originCode);
            }
            else if (platform.ToLower() == "magento") // (int)CatalogSourcePlatforms.MagentoApi)
            {
                originCode = MakeReplacementsForMagento(originCode);
            }
            //else if (platform.ToLower() == "ecwid") // (int)CatalogSourcePlatforms.Ecwid)
            //{
            //    originCode = MakeReplacementsForEcwid(originCode);
            //}
            //else if (platform.ToLower() == "easystore") // (int)CatalogSourcePlatforms.EasyStore)
            //{
            //    originCode = MakeReplacementsForEasyStore(originCode);
            //}
            else if (platform.ToLower() == "woocommerce")
            {
                originCode = MakeReplacementsForWooCommerce(originCode, shopId);
            }
            else if (platform.ToLower() == "wix")
            {
                originCode = MakeReplacementsForWix(shopId);
            }
            //else if (platform.ToLower() == "neto")
            //{
            //    originCode = MakeReplacementsForNeto(shopId);
            //}
            return originCode;
        }

        private static string CreateFirstCode(ConversionData tags)
        {
            string code1 = Properties.Resources.GoogleAdsGeneric_GlobalTag;
            return ReplaceAccountSetings(code1, tags.conversion_id, tags.conversion_label);
        }
        private static string CreateSecondCode(ConversionData tags)
        {
            string code2 = Properties.Resources.GoogleAdsGeneric_ConvrsionTag;
            return ReplaceAccountSetings(code2, tags.conversion_id, tags.conversion_label);
        }

        public static AdwordsTrackingCodes GetTrackingCodes(string platform, string originCode, int shopId)
        {
            AdwordsTrackingCodes resultCode = new AdwordsTrackingCodes();
            var tags = ExtractGoogleConversionID_FromOldPixel(originCode, shopId);
            resultCode.ConversionID = tags.conversion_id;
            resultCode.ConversionLabel = tags.conversion_label;

            platform = platform.ToLower();
            
            if (platform == "shopify")
            {
                resultCode.Code = "";
            }
            else if (platform == "bigcommerce")//(int)CatalogSourcePlatforms.BigCommerce)
            {
                //string code1 = Properties.Resources.GoogleAdsGeneric_GlobalTag;
                //string code2 = Properties.Resources.GoogleAdsGeneric_ConvrsionTag;

                //code1 = ReplaceAccountSetings(code1, tags.conversion_id, tags.conversion_label );
                //code2 = ReplaceAccountSetings(code2, tags.conversion_id, tags.conversion_label);

                string code1 = CreateFirstCode(tags);
                string code2 = CreateSecondCode(tags);

                code2 = ReplacementsForBigcommerceSynax(code2); //replace to BC syntax

                resultCode.Code = HttpUtility.JavaScriptStringEncode(code1); 
                resultCode.SecondCode = HttpUtility.JavaScriptStringEncode(code2);
                //originCode = MakeReplacementsForBigcommerce(originCode);
            }
            else if (platform == "shift4shop")
            {
                string code1 = CreateFirstCode(tags);
                string code2 = CreateSecondCode(tags);

                code2 = ReplacementsForShip4ShopSynax(code2); //replace order values

                resultCode.Code = HttpUtility.JavaScriptStringEncode(code1);
                resultCode.SecondCode = HttpUtility.JavaScriptStringEncode(code2);
            }
            else if (platform  == "magento") // (int)CatalogSourcePlatforms.MagentoApi)
            {
                resultCode.Code = tags.conversion_id; 
                resultCode.SecondCode = tags.conversion_label; 
                //originCode = MakeReplacementsForMagento(originCode);
            }
            else if (platform == "woocommerce")
            {
                originCode = MakeReplacementsForWooCommerce(originCode, shopId);
                resultCode.Code = originCode;
            }
            else if (platform == "wix")
            {
                originCode = MakeReplacementsForWix(shopId);
                resultCode.Code = originCode;
            }
            else
            {
                string code1 = Properties.Resources.GoogleAdsGeneric_GlobalTag;
                string code2 = Properties.Resources.GoogleAdsGeneric_ConvrsionTag;

                code1 = ReplaceAccountSetings(code1, tags.conversion_id, tags.conversion_label);
                code2 = ReplaceAccountSetings(code2, tags.conversion_id, tags.conversion_label);

                resultCode.Code = HttpUtility.JavaScriptStringEncode(code1);
                resultCode.SecondCode = HttpUtility.JavaScriptStringEncode(code2);
            }
            
            return resultCode;
        }

        public static string GetShopifyConversionCode(string originCode, string scripCode = null)
        {
            string conversionID = Shopify.ShopifyThemesManager.ExtractGoogleConversionID(originCode, out string googleConversionLabel);
            if (scripCode == null)
            {
                scripCode = Properties.Resources.AdWordsPixelShopify___Enhanced___Gtag;
            }
            originCode = MakeReplacementsForShopifyGTag(scripCode, conversionID, googleConversionLabel, false);
            return originCode;
        }


        private static string MakeReplacementsForWooCommerce(string originCode, int? shopIDForLog = null)
        {
            string code = Storeya.Core.Properties.Resources.WooCommerceGtag;
            var tags = ExtractGoogleConversionID_FromOldPixel(originCode, shopIDForLog, "MakeReplacementsForWooCommerce");
            code = code.Replace("google_conversion_id", tags.conversion_id);
            code = code.Replace("google_conversion_label", tags.conversion_label.Replace(@"\", ""));
            return HttpUtility.JavaScriptStringEncode(code);
        }
        private static string MakeReplacementsForWix(int shopid)
        {
            string code = WixHelper.GetWixCustomCode(shopid);
            return HttpUtility.JavaScriptStringEncode(code);
        }
        private static string MakeReplacementsForNeto(int shopid)
        {
            string code = Neto.NetoApiHelper.GetNetoCustomCode(shopid);
            return HttpUtility.JavaScriptStringEncode(code);
        }
        private static string MakeReplacementsForEasyStore(string originCode)
        {
            originCode = ReplaceConversionValue(originCode, " {{cart.total_amount_include_transaction}}");

            originCode = ReplaceCurrency(originCode, "{{cart.currency}}");

            return originCode;
        }

        private static string MakeReplacementsForEcwid(string originCode)
        {
            originCode = ReplaceConversionValue(originCode, "%order_total%");
            return originCode;
        }

        private static string MakeReplacementsForMagento(string originCode)
        {
            originCode = ReplaceConversionValue(originCode, "<?php echo $adwords_saleamt; ?>");

            return originCode;
        }

        private static string ReplaceAccountSetings(string originCode, string conversionID, string conversionLabel)
        {
            originCode = originCode.Replace("XXXXXXXXX", conversionID);
            originCode = originCode.Replace("YYYYYYYYY", conversionLabel);
            return originCode;
        }

        private static string ReplacementsForBigcommerceSynax(string originCode)
        {
            originCode = originCode.Replace("REPLACE_WITH_ORDER_VALUE", "{{checkout.order.total}}");
            originCode = originCode.Replace("REPLACE_WITH_ORDER_ID", "{{checkout.order.id}}");
            return originCode;
        }
        private static string ReplacementsForShip4ShopSynax(string originCode)
        {
            originCode = originCode.Replace("REPLACE_WITH_ORDER_VALUE", "[total_noformat]");
            originCode = originCode.Replace("REPLACE_WITH_ORDER_ID", "[invoice-prefix][invoice-number]");
            return originCode;
        }

        private static string MakeReplacementsForBigcommerce(string originCode)
        {
            originCode = ReplaceConversionValue(originCode, "%%ORDER_AMOUNT%%");

            int part2index = originCode.IndexOf("var google_conversion_value");

            if (part2index >= 0)
            {
                string origin_part_1 = originCode.Remove(part2index);
                string origin_part_2 = originCode.Remove(0, part2index);

                string additionforOrderID = "var google_conversion_order_id = %%ORDER_ID%%;\\r\\n";

                StringBuilder sb = new StringBuilder();
                sb.Append(origin_part_1).Append(additionforOrderID).Append(origin_part_2);

                originCode = sb.ToString();
            }

            string orderIdPattern = "%%ORDER_ID%%";
            originCode = AddOrderIdToNoScriptSection(originCode, orderIdPattern);

            return originCode;
        }
        public static string MakeReplacementsForShopifyGTag(string code, string googleConversionID, string googleConversionLabel, bool encodeForJs = true)
        {
            //string code = Storeya.Core.Properties.Resources.AdWordsPixelShopify___Gtag;

            code = code.Trim('\"');
            if (encodeForJs)
            {
                code = code.Replace("<", "\\u003c").Replace(">", "\\u003e").Replace("'", "\\u0027");
                code = RegexHelper.Replace(code, @"\r\n?|\n", "\\r\\n");
                code = RegexHelper.Replace(code, @"\t", "\\t");

                //suround the code with quotes
                code = "\"" + code + "\"";
            }

            code = code.Replace("XXXXXXXXX", googleConversionID);
            code = code.Replace("LLLLLLLLL", googleConversionLabel);

            //if (currency == "EUR")
            //{
            //    code = code.Replace("| remove:\\u0027,\\u0027", "");
            //}

            return code;
        }


        //public static string MakeReplacementsForShopify(string originCode, string currency)
        //{


        //    string code = "{% if order.order_number %}{% assign order_number_var = order.order_number %} {% elsif order_number %} {% assign order_number_var = order_number %} {% endif %}\\r\\n" +
        //                 "{% if total_price %}{% assign orderValue = total_price %} {% elsif order.total_price %}{% assign orderValue = order.total_price %} {% endif %}\\r\\n" +
        //                 "var google_conversion_value";

        //    //if (currency == "EUR")
        //    //{
        //    //    originCode = ReplaceConversionValue(originCode, "{{ orderValue | money_without_currency }}");
        //    //}
        //    //else
        //    //{
        //    //    originCode = ReplaceConversionValue(originCode, "{{ orderValue | money_without_currency | replace: ',', '' }}");
        //    //}

        //    originCode = ReplaceConversionValue(originCode, "{{ orderValue  | divided_by: 100.0 }}");


        //    // originCode = originCode.Replace("orderValue |", "total_price |");
        //    originCode = originCode.Replace("var google_conversion_value", code);


        //    originCode = ReplaceCurrency(originCode, "{{ shop.currency }}");


        //    int part2index = originCode.IndexOf("var google_conversion_value");

        //    if (part2index >= 0)
        //    {
        //        string origin_part_1 = originCode.Remove(part2index);
        //        string origin_part_2 = originCode.Remove(0, part2index);


        //        string additionforShopifyOrderID = "var google_conversion_order_id = {{ order_number_var }};\\r\\n";

        //        StringBuilder sb = new StringBuilder();
        //        sb.Append(origin_part_1).Append(additionforShopifyOrderID).Append(origin_part_2);

        //        originCode = sb.ToString();

        //    }

        //    string orderIdPattern = "{{ order_number_var }}";
        //    originCode = AddOrderIdToNoScriptSection(originCode, orderIdPattern);

        //    return originCode;
        //}

        private static string AddOrderIdToNoScriptSection(string originCode, string orderIdPattern)
        {
            //\u0026amp;guid=ON\u0026amp;script=0

            originCode = originCode.Replace(@"\u0026", "&");

            originCode = originCode.Replace("&amp;script=0", "&amp;script=0&amp;oid=" + orderIdPattern + "");

            originCode = originCode.Replace("&", @"\u0026");


            return originCode;
        }


        private static string ReplaceConversionValue(string originCode, string platformValue)
        {
            originCode = originCode.Replace("google_conversion_value = 1.00;", "google_conversion_value = " + platformValue + ";");
            originCode = originCode.Replace("?value=1.00", "?value=" + platformValue);

            return originCode;
        }

        private static string ReplaceCurrency(string originCode, string platformValue)
        {
            //originCode = originCode.Replace("google_conversion_currency = \"USD\"", "google_conversion_currency = \"" + platformValue + "\"");
            //originCode = originCode.Replace("currency_code=USD", "currency_code=" + platformValue);
            originCode = originCode.Replace("USD", platformValue);
            return originCode;
        }

        public static string GetOldAdWordsThankYouPageConversionTrackerFromNewGoogleEventSnippet(string googleEventSnippet)
        {
            //googleEventSnippet	"<!-- Event snippet for Thank you page conversion tracker for StoreYa conversion page -->\n<script>\n  gtag('event', 'conversion', {\n      'send_to': 'AW-928262079/lo9VCMKI9n4Qv8_QugM',\n      'value': 1.0,\n      'currency': 'USD',\n      'transaction_id': ''\n  });\n</script>\n"

            //extract send_to': 'AW-928262079/lo9VCMKI9n4Qv8_QugM',
            string code = null;
            ConversionData data = ExtractGoogleConversionID(googleEventSnippet);
            if (data != null)
            {
                //string path = HttpContext.Current.Server.MapPath("/ExternalScriptsTemplates");
                string path = ConfigHelper.GetValue("emailTemplatesPath");
                path = path.ToLower().Replace("emailtemplates", "ExternalScriptsTemplates");
                string file = path + "\\AdWords Thank you page conversion tracker.txt";
                string oldAdWordsThankYouPageConversionTracker = File.ReadAllText(file);

                if (!string.IsNullOrEmpty(oldAdWordsThankYouPageConversionTracker))
                {
                    oldAdWordsThankYouPageConversionTracker = oldAdWordsThankYouPageConversionTracker.Replace("XXXXXXXXX", data.conversion_id);
                    oldAdWordsThankYouPageConversionTracker = oldAdWordsThankYouPageConversionTracker.Replace("YYYYYYYYY", data.conversion_label);
                    code = oldAdWordsThankYouPageConversionTracker;
                }
            }

            return code;
        }

        public static ConversionData ExtractGoogleConversionID(string googleEventSnippet)
        {
            ConversionData data = null;
            try
            {
                string[] splitted = googleEventSnippet.Split(new string[] { "'send_to': '" }, StringSplitOptions.RemoveEmptyEntries);
                string part_2 = splitted[1];
                int index = part_2.IndexOf("'");
                part_2 = part_2.Remove(index);

                if (!string.IsNullOrEmpty(part_2))
                {
                    string[] splitted_2 = part_2.Split('/');

                    data = new ConversionData() { conversion_id = splitted_2[0].Replace("AW-", ""), conversion_label = splitted_2[1] };
                }
            }
            catch
            {
                return null;
            }

            return data;
        }


        public static ConversionData ExtractGoogleConversionID_FromOldPixel(string oldGoogleEventSnippet, int? shopIDForLog = null, string moreInfo = null)
        {
            ConversionData data = new ConversionData();

            if (!oldGoogleEventSnippet.Contains("google_conversion_id =") || !oldGoogleEventSnippet.Contains("google_conversion_label = "))
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Can't extract ConversionID_FromOldPixel", $"Please check trafficbooster , ShopID: {shopIDForLog}, moreInfo:{moreInfo} , AdWordsPixel = {oldGoogleEventSnippet}",isHtml:false);
                return null;
            }
            string[] splitted = oldGoogleEventSnippet.Split(new string[] { "google_conversion_id = " }, StringSplitOptions.RemoveEmptyEntries);
            string part_2 = splitted[1];
            int index = part_2.IndexOf(";");
            part_2 = part_2.Remove(index);
            data.conversion_id = part_2;

            splitted = oldGoogleEventSnippet.Split(new string[] { "google_conversion_label = " }, StringSplitOptions.RemoveEmptyEntries);
            part_2 = splitted[1];
            index = part_2.IndexOf(";");
            part_2 = part_2.Remove(index);
            data.conversion_label = part_2.Replace("\"", "");

            return data;
        }
        public static string MakePostPurchaseTrackingCode(string oldGoogleEventSnippet, int shopIDForLog)
        {
            ConversionData conversionData = ExtractGoogleConversionID_FromOldPixel(oldGoogleEventSnippet, shopIDForLog, "MakePostPurchaseTrackingCode");
            string contentTemplate = Storeya.Core.Properties.Resources.PostPurchaseCode;
            return contentTemplate.FormatWith(conversionData).Replace("cBraceOpen", "{").Replace("cBraceClose", "}");
        }
    }
    public class ConversionData
    {
        public string conversion_id { get; set; }
        public string conversion_label { get; set; }
    }

    public class AdwordsTrackingCodes
    {
        public string ConversionID { get; set; }
        public string ConversionLabel { get; set; }

        public string Code { get; set; }
        public string SecondCode { get; set; }

        public bool NeedsEncoding { get; set; }
    }
}
