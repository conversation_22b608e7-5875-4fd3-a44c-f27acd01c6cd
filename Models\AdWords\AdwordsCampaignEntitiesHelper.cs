﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Amazon.S3.Util.S3EventNotification;

namespace Storeya.Core.Models.AdWords
{
    public class AdwordsCampaignEntitiesHelper
    {
        public enum EntityType
        {
            SearchTerm = 1,
            LandingPage = 2,
            Products = 3
        }
        public enum Status
        {
            Ignore = -1,
            New = 1,
            Exclude = 2,
            Excluded = 3,
            Failed = 4,
        }
        public static int AddRange(int shopId, List<AdwordsCampaignEntity> adwordsCampaignEntities, AdwordsCampaignEntitiesHelper.EntityType entityType)
        {
            if (adwordsCampaignEntities == null)
            {
                return 0;
            }
            bool updateDb = false;
            var db = DataHelper.GetStoreYaEntities();
            var list = db.AdwordsCampaignEntities.Where(a => a.ShopId == shopId).ToList();
            if (list.Count() > 0)
            {
                foreach (var item in list)
                {

                    var entity = adwordsCampaignEntities.SingleOrDefault(c => c.EntityResourceName == item.EntityResourceName && c.Type == (int)entityType);
                    if (entity != null)
                    {
                        if (item.Status == (int)Status.New)
                        {
                            item.Value = entity.Value;
                            item.Clicks = entity.Clicks;
                            item.Conversions = entity.Conversions;
                            item.Cost = entity.Cost;
                            item.Revenues = entity.Revenues;
                            item.Roas = entity.Roas;
                            item.UpdatedAt = DateTime.Now;
                            updateDb = true;
                            adwordsCampaignEntities.Remove(entity);
                        }
                        else
                        {
                            adwordsCampaignEntities.Remove(entity);
                        }
                    }
                }
            }
            if (adwordsCampaignEntities.Count() > 0)
            {
                db.AdwordsCampaignEntities.AddRange(adwordsCampaignEntities);
                updateDb = true;
            }
            if (updateDb)
            {
                db.SaveChanges();
            }
            return adwordsCampaignEntities.Count();
        }

        public static void Add(int shopId, long campaignId, string campaignName, string entityResourceName,
                                Status status, EntityType entityType, string entityValue, string entityStatus, int? clicks, decimal? cost,
            int? conversions, decimal cpaInUsd, int removeStatus, decimal averageCpa, decimal revenues, decimal roas)
        {
            var db = DataHelper.GetStoreYaEntities();
            AdwordsCampaignEntity adwordsCampaignEntity = new AdwordsCampaignEntity()
            {
                ShopId = shopId,
                CampaignId = campaignId,
                CampaignName = campaignName,
                EntityResourceName = entityResourceName,
                Status = (int)status,
                AverageCpa = averageCpa,
                Clicks = clicks,
                Cost = cost,
                Conversions = conversions,
                CpaInUsd = cpaInUsd,
                RemoveStatus = removeStatus,
                EntityStatus = entityStatus,
                InsertedAt = DateTime.Now,
                Roas = roas,
                Value = entityValue,
                Revenues = revenues,
                Type = (int)entityType,
            };
            db.AdwordsCampaignEntities.Add(adwordsCampaignEntity);
            db.SaveChanges();
        }

        public static void UpdateStatus(int id, Status status)
        {
            var db = DataHelper.GetStoreYaEntities();
            var entity = db.AdwordsCampaignEntities.SingleOrDefault(e => e.Id == id);
            if (entity != null)
            {
                if (entity.Status != (int)status)
                {
                    entity.Status = (int)status;
                    entity.UpdatedAt = DateTime.Now;
                    db.SaveChanges();
                }
            }
        }
        public static void UpdateStatus(long campaignId, Status currentStatus, Status status)
        {
            var db = DataHelper.GetStoreYaEntities();
            var entities = db.AdwordsCampaignEntities.Where(e => e.CampaignId == campaignId);
            if (entities != null && entities.Count() > 0)
            {
                foreach (var entity in entities)
                {
                    if (entity.Status == (int)currentStatus)
                    {
                        entity.Status = (int)status;
                        entity.UpdatedAt = DateTime.Now;
                    }
                }
                db.SaveChanges();
            }
        }
        public class SearchTermsCampaignRequest
        {
            public List<string> SearchTerms { get; set; }
            public bool UseKeywordMatchExact { get; set; }
            public SearchTermsCampaignRequest()
            {
                SearchTerms = new List<string>();
            }
        }
        public static Dictionary<long, SearchTermsCampaignRequest> GetSearchTermsToExclude(int shopId)
        {
            Dictionary<long, SearchTermsCampaignRequest> dic = new Dictionary<long, SearchTermsCampaignRequest>();
            var db = DataHelper.GetStoreYaEntities();
            var entities = db.AdwordsCampaignEntities.Where(e => e.ShopId == shopId && e.Status == (int)Status.Exclude && e.Type == (int)EntityType.SearchTerm);
            foreach (var item in entities)
            {
                SearchTermsCampaignRequest request = new SearchTermsCampaignRequest();
                request.UseKeywordMatchExact = false;
                if (item.CampaignName.ToLower().EndsWith("_search b"))
                {
                    request.UseKeywordMatchExact = true;
                }
                if (dic.ContainsKey(item.CampaignId.Value))
                {
                    dic[item.CampaignId.Value].SearchTerms.Add(item.Value);
                }
                else
                {
                    request.SearchTerms.Add(item.Value);
                    dic.Add(item.CampaignId.Value, request);
                }
            }
            return dic;
        }
    }
    public class AdwordsKeyword
    {
        public string Link { get; set; }
        public string KeywordText { get; set; }
    }
    public class KeywordsRemark
    {
        public int ShopID { get; set; }
        public List<AdwordsKeyword> AdwordsKeywords { get; set; }
    }
}
