﻿using Storeya.Core.Helpers;
using Storeya.Core.Models.DataProviders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    public class AdwordsKeywordsHelper
    {
        public static List<string> ToKeywordsList(string keywords)
        {
            //no symbols in headlines
            if (string.IsNullOrWhiteSpace(keywords))
            {
                return new List<string>();
            }
            string[] lines = keywords.Split(new string[] { "\r\n", "\n", "," }, StringSplitOptions.None);
            return lines.ToList();
        }

        public static string ToValidKeyword(string keyword)
        {
            //no symbols
            return keyword;
        }
        public static List<string> ExtractKeywordsFromUrl(string url)
        {
            //Clients URL: https://www.blushandbirchpaper.com/

            //Keywords: 
            //www.blushandbirchpaper.com 
            //www.blushandbirchpaper
            //blushandbirchpaper.com 
            //blushandbirchpaper               

            string fixed_url = url.TrimEnd('/').Trim();
            List<string> keywords = new List<string>();

            if (url.Contains("http://"))
            {
                fixed_url = fixed_url.Replace("http://", "");
                if (!string.IsNullOrEmpty(fixed_url))
                {
                    keywords.Add(fixed_url);
                }
            }
            else if (url.Contains("https://"))
            {
                fixed_url = fixed_url.Replace("https://", "");
                if (!string.IsNullOrEmpty(fixed_url) && !keywords.Contains(fixed_url))
                {
                    keywords.Add(fixed_url);
                }
            }

            string[] splitted = fixed_url.Split('.');
            if (splitted != null && splitted.Length > 0)
            {
                if (splitted.Length == 2)
                {
                    if (splitted[0] == "www")
                    {
                        string keyword = splitted[1];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }
                    }
                    else
                    {
                        string keyword = splitted[0];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }
                    }
                }

                else if (splitted.Length == 3)
                {
                    if (splitted[0] == "www")
                    {
                        string keyword = splitted[1];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }

                        string keyword_2 = splitted[1] + "." + splitted[2];
                        if (!string.IsNullOrEmpty(keyword_2) && !keywords.Contains(keyword_2))
                        {
                            keywords.Add(keyword_2);
                        }

                        string keyword_3 = splitted[0] + "." + splitted[1];
                        if (!string.IsNullOrEmpty(keyword_3) && !keywords.Contains(keyword_3))
                        {
                            keywords.Add(keyword_3);
                        }
                    }
                    else
                    {
                        string keyword = splitted[0];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }

                        string keyword_2 = splitted[0] + "." + splitted[1];
                        if (!string.IsNullOrEmpty(keyword_2) && !keywords.Contains(keyword_2))
                        {
                            keywords.Add(keyword_2);
                        }
                    }
                }
                else
                {
                    if (splitted[0] == "www")
                    {
                        string keyword = splitted[1];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }
                    }
                    else
                    {
                        string keyword = splitted[0];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }
                    }
                }
            }

            return keywords;
        }

        public static List<string> ExtractKeyWordsFromTitle(string url, string title)
        {
            //"blush and birch paper"
            //+blush +birch +paper   

            List<string> keywords = new List<string>();

            if (title.Contains('|'))
            {
                title = title.Replace("|", " ");
            }

            if (title.Contains(" - "))
            {
                title = title.Replace(" - ", " ");
            }

            string[] title_splitted = title.Split(' ');
            if (title_splitted != null && title_splitted.Length > 0)
            {
                string url_without_punctuatuon = RemovePunctuationSymbols(url);

                string url_left = url_without_punctuatuon;

                List<string> keywords_from_title = new List<string>();
                List<string> list_of_branded = new List<string>();

                for (int j = 0; j < title_splitted.Length; j++)
                {
                    string word = title_splitted[j];

                    if (word != " " && word != "")
                    {
                        // remove special symbol
                        word = RemoveTradeMarkSymbol(word);

                        word = RemovePunctuationSymbols(word);

                        if (word.Length > 1 && (url_left.Contains(word) || url_left.ToLower().Contains(word.ToLower())))
                        {
                            if (keywords_from_title.Count == 0 && !url_left.StartsWith(word) && !url_left.StartsWith(word.ToLower()))
                                continue;


                            if (!keywords_from_title.Contains(word) && !keywords_from_title.Contains(word.ToLower()))
                            {
                                string combined = string.Join("", keywords_from_title) + word;

                                if (url_without_punctuatuon.ToLower() != combined.ToLower() && url_without_punctuatuon.ToLower().Contains("-"))
                                {
                                    url_without_punctuatuon = url_without_punctuatuon.Replace("-", "");
                                }

                                if (url_without_punctuatuon.ToLower() != combined.ToLower() && combined.ToLower().Contains("-"))
                                {
                                    combined = combined.Replace("-", "");
                                }

                                if (url_without_punctuatuon.ToLower().Contains(combined.ToLower()))
                                {
                                    keywords_from_title.Add(word);

                                    if (url_left.Contains(word))
                                        url_left = url_left.Replace(word, "");
                                    else
                                    {
                                        url_left = url_left.Replace(word.ToLower(), "");
                                    }
                                }
                            }
                        }
                        else if (word.Contains("-"))
                        {
                            string[] word_splitted = word.Split('-');
                            for (int i = 0; i < word_splitted.Length; i++)
                            {
                                string word_2 = word_splitted[i];
                                if (word_2 != " " && word_2 != "")
                                {
                                    if (!keywords_from_title.Contains(word_2) && !keywords_from_title.Contains(word_2.ToLower()))
                                    {
                                        string combined_1 = string.Join("", keywords_from_title) + word_2;

                                        if (url_without_punctuatuon.ToLower() != combined_1.ToLower() && url_without_punctuatuon.ToLower().Contains("-"))
                                        {
                                            url_without_punctuatuon = url_without_punctuatuon.Replace("-", "");
                                        }

                                        if (url_without_punctuatuon.ToLower() != combined_1.ToLower() && combined_1.ToLower().Contains("-"))
                                        {
                                            combined_1 = combined_1.Replace("-", "");
                                        }

                                        if (url_without_punctuatuon.ToLower().Contains(combined_1.ToLower()))
                                        {
                                            keywords_from_title.Add(word_2);

                                            if (url_left.Contains(word_2))
                                                url_left = url_left.Replace(word_2, "");
                                            else
                                            {
                                                url_left = url_left.Replace(word_2.ToLower(), "");
                                            }
                                        }
                                    }
                                }                               
                            }
                        }
                    }


                }

                if (keywords_from_title.Count > 1)
                {
                    string combined_2 = string.Join("", keywords_from_title);

                    if (url_without_punctuatuon.ToLower() != combined_2.ToLower() && url_without_punctuatuon.ToLower().Contains("-"))
                    {
                        url_without_punctuatuon = url_without_punctuatuon.Replace("-", "");
                    }

                    if (url_without_punctuatuon.ToLower() != combined_2.ToLower() && combined_2.ToLower().Contains("-"))
                    {
                        combined_2 = combined_2.Replace("-", "");
                    }

                    if (url_without_punctuatuon.ToLower() == combined_2.ToLower())
                    {
                        keywords.Add(string.Join(" ", keywords_from_title));

                        foreach (var keyword in keywords_from_title)
                        {
                            //if (IsAllowedKeyword(keyword))
                            // {
                            string branded_word = "+" + keyword;
                            if (!list_of_branded.Contains(branded_word) && !list_of_branded.Contains(branded_word.ToLower()))
                                list_of_branded.Add(branded_word);
                            //}
                        }

                        if (list_of_branded.Count > 0)
                            keywords.Add(string.Join(" ", list_of_branded));
                    }
                }
            }

            return keywords;
        }

        private static string RemoveTradeMarkSymbol(string word)
        {
            //™	&trade;	&#8482;	&#x2122;	trademark symbol
            if (word.Contains("™"))
                word = word.Replace("™", "");

            //®	&reg;	&#174;	&#x00AE;	registered trademark symbol
            if (word.Contains("®"))
                word = word.Replace("®", "");

            return word;
        }

        private static string RemovePunctuationSymbols(string word)
        {
            if (word.Contains("!"))
                word = word.Replace("!", "");

            if (word.Contains(";"))
                word = word.Replace(";", "");

            if (word.Contains(":"))
                word = word.Replace(":", "");

            if (word.Contains(","))
                word = word.Replace(",", "");

            if (word.Contains("?"))
                word = word.Replace("?", "");

            if (word.Contains("'"))
                word = word.Replace("'", "");

            //if (word.Contains("-"))
            //    word = word.Replace("-", "");

            //if (word.Contains("|"))
            //    word = word.Replace("|", "");

            return word;
        }

        public static bool IsAllowedKeyword(string word)
        {
            string fixed_word = word.ToLower();

            if (IsConnectingWord(fixed_word))
                return false;

            if (IsIndefiniteArticle(fixed_word))
                return false;

            if (IsDefiniteArticle(fixed_word))
                return false;

            if (IsDemonstrative(fixed_word))
                return false;

            if (IsPossessive(fixed_word))
                return false;

            if (IsQuantifier(fixed_word))
                return false;

            if (IsNumber(fixed_word))
                return false;

            if (IsDistributive(fixed_word))
                return false;

            if (IsDifferenceWord(fixed_word))
                return false;

            if (IsQuestionOrDefiningWord(fixed_word))
                return false;

            if (IsPrepositionWord(fixed_word))
                return false;

            return true;
        }

        public static bool IsDefiniteArticle(string word)
        {
            if (word == "the")
                return true;

            return false;
        }

        public static bool IsIndefiniteArticle(string word)
        {
            if (word == "a" || word == "an")
                return true;

            return false;
        }

        public static bool IsConnectingWord(string word)
        {
            if (word == "and")
                return true;

            return false;
        }


        public static bool IsDifferenceWord(string word)
        {
            if (word == "other" || word == "another")
                return true;

            return false;
        }

        public static bool IsPrepositionWord(string word)
        {
            if (word == "of" || word == "to" || word == "for" || word == "on" || word == "at" || word == "in"
                || word == "with" || word == "over" || word == "by")
                return true;

            return false;
        }

        public static bool IsQuestionOrDefiningWord(string word)
        {
            if (word == "which" || word == "what" || word == "whose")
                return true;

            return false;
        }

        public static bool IsDistributive(string word)
        {
            if (word == "all" || word == "both" || word == "half" || word == "either" || word == "neither" || word == "each" || word == "every")
                return true;

            return false;
        }

        public static bool IsQuantifier(string word)
        {
            if (word == "few" || word == "little" || word == "much" || word == "many" || word == "lot" || word == "most" || word == "any" || word == "some" || word == "enough") //etc.
                return true;

            return false;
        }

        public static bool IsDemonstrative(string word)
        {
            if (word == "this" || word == "that" || word == "these" || word == "those")
                return true;

            return false;
        }

        public static bool IsPossessive(string word)
        {
            if (word == "my" || word == "your" || word == "his" || word == "her" || word == "its" || word == "our" || word == "their")
                return true;

            return false;
        }

        public static bool IsNumber(string word)
        {
            if (word == "one" || word == "ten" || word == "thirty") //etc.
                return true;

            return false;
        }

        public static string GetTitleFromHtml(string url)
        {
            string title = null;

            string collectionPageContent = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
            if (!string.IsNullOrEmpty(collectionPageContent))
            {
                string titleFromHtml = RegexHelper.GetFirstGroupValue(collectionPageContent, @"<meta property=\""og:title\"" content=\""?(.*?)\""");
                if (!string.IsNullOrEmpty(titleFromHtml))
                {
                    titleFromHtml = SpecialCharactersHandler.ReplaceSpecialSymbols(titleFromHtml);
                    title = titleFromHtml;
                }
                else
                {
                    titleFromHtml = RegexHelper.GetFirstGroupValue(collectionPageContent, @"<title>?(.*?)</title>");
                    if (!string.IsNullOrEmpty(titleFromHtml))
                    {
                        titleFromHtml = SpecialCharactersHandler.ReplaceSpecialSymbols(titleFromHtml);
                        title = titleFromHtml;
                    }
                }
            }

            return title;
        }
    }
}

