﻿using Storeya.Core.Helpers;
using Storeya.Core.Models.DataProviders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Storeya.Core.Models.AdWords
{
    public class AdwordsKeywordsHelper
    {
        //Phrase match: Symbol: "keyword",  Example keyword: "women's hats"
        public static List<string> ToPhraseMatchKeywords(List<string> keywords)
        {
            List<string> phrase_match_keywords = null;
            if (keywords != null)
            {
                phrase_match_keywords = new List<string>();
                foreach (var keyword_item in keywords)
                {
                    string phrase_match_k = "\"" + keyword_item + "\"";

                    if (!phrase_match_keywords.Where(x => x == phrase_match_k).Any())
                        phrase_match_keywords.Add(phrase_match_k);
                }
            }
            return phrase_match_keywords;
        }


        //Exact match:  Symbol: [keyword], Example keyword: [women's hats]
        public static List<string> ToExactMatchKeywords(List<string> keywords)
        {
            List<string> exact_match_keywords = null;
            if (keywords != null)
            {
                exact_match_keywords = new List<string>();
                foreach (var keyword_item in keywords)
                {
                    string exact_match_modifier = "[" + keyword_item + "]";

                    if (!exact_match_keywords.Where(x => x == exact_match_modifier).Any())
                        exact_match_keywords.Add(exact_match_modifier);
                }
            }
            return exact_match_keywords;
        }

        //Broad match modifier: Symbol: Plus sign, for example +keyword,  Example keyword: +women's +hats
        public static List<string> ToBroadMatchKeywords(List<string> keywords)
        {
            List<string> broad_match_keywords = null;
            if (keywords != null)
            {
                broad_match_keywords = new List<string>();
                foreach (var keyword_item in keywords)
                {
                    string broad_match_modifier = keyword_item;
                    if (keyword_item.Contains(" "))
                    {
                        broad_match_modifier = broad_match_modifier.Replace(" ", " +");
                    }
                    else
                    {
                        broad_match_modifier = "+" + keyword_item;
                    }

                    if (!broad_match_keywords.Where(x => x == broad_match_modifier).Any())
                        broad_match_keywords.Add(broad_match_modifier);
                }
            }
            return broad_match_keywords;
        }

        public static List<string> GetBrandedKeywordsFromTitleAndHtml(string url)
        {
            Console.WriteLine("GetBrandedKeywordsFromTitleAndHtml:" + url);
            List<string> brandedKeywordsFromTitleAndHtml = new List<string>();

            string title = null;
            List<string> keywords_html = new List<string>();
            List<string> keywords_title = new List<string>();

            string domain = GetOnlyDomain(url);

            var site_data = MarketplaceUrlHelper.GetSiteUrl(url);

            if (site_data != null && !string.IsNullOrEmpty(site_data.URL))
            {
                url = site_data.URL;
                keywords_html = AdwordsKeywordsHelper.ExtractKeywordsFromUrl(url);
                Console.WriteLine("keywords_html: count:" + keywords_html.Count());
                title = AdwordsKeywordsHelper.GetTitleFromHtml(url);
                Console.WriteLine("title:" + title);
                if (!string.IsNullOrEmpty(title))
                {
                    List<string> keywords_from_title = AdwordsKeywordsHelper.ExtractKeyWordsFromTitle(domain, title);
                    if (keywords_from_title.Count > 0)
                    {
                        foreach (var keyword_from_title in keywords_from_title)
                        {
                            if (!keywords_title.Contains(keyword_from_title))
                                keywords_title.Add(keyword_from_title);
                        }
                    }
                }
            }

            if (keywords_html.Count > 0)
            {

                foreach (var keyword_item in keywords_html)
                {
                    if (!brandedKeywordsFromTitleAndHtml.Where(x => x == keyword_item).Any())
                        brandedKeywordsFromTitleAndHtml.Add(keyword_item);
                }
            }

            if (keywords_title.Count > 0)
            {
                foreach (var keyword_item in keywords_title)
                {
                    if (!brandedKeywordsFromTitleAndHtml.Where(x => x == keyword_item).Any())
                        brandedKeywordsFromTitleAndHtml.Add(keyword_item);
                }
            }
            Console.WriteLine("GetBrandedKeywordsFromTitleAndHtml:Done");
            return brandedKeywordsFromTitleAndHtml;
        }

        public static string GetOnlyDomain(string url)
        {
            string domain = UrlPathHelper.GetDomainName(url);

            if (domain.Contains("www."))
                domain = domain.Replace("www.", "");

            string[] splitted = domain.Split('.');
            if (splitted != null && splitted.Length > 0)
            {
                domain = splitted[0];
                if (splitted.Length > 2 && splitted[1].Length > 2
                    && !AdwordsKeywordsHelper.IsTopLevelDomain(splitted[1])
                    && !AdwordsKeywordsHelper.IsCountryCodeTopLevelDomain(splitted[1]))
                    domain = domain + "." + splitted[1];
            }

            return domain;
        }

        public static string ExtractSiteAdNameFromTitle(string url)
        {
            try
            {
                string domain = GetOnlyDomain(url);
                string title = AdwordsKeywordsHelper.GetTitleFromHtml(url);
                string name = domain.ToTitleCase();
                Console.WriteLine("title:" + title);
                if (!string.IsNullOrEmpty(title))
                {
                    List<string> keywords_from_title = AdwordsKeywordsHelper.ExtractKeyWordsFromTitle(domain, title);

                    if (keywords_from_title.Count > 0)
                    {
                        keywords_from_title = keywords_from_title.Distinct().ToList();
                        name = keywords_from_title.First().ToTitleCase().Replace(" ", "");
                    }
                }
                return name;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed to run ExtractSiteAdNameFromTitle for " + url + " Ex:" + ex.Message);
            }
            return null;
        }

        public static List<string> ToKeywordsList(string keywords, int? limit = null)
        {
            //no symbols in headlines
            if (string.IsNullOrWhiteSpace(keywords))
            {
                return new List<string>();
            }
            string[] lines = keywords.Split(new string[] { "\r\n", "\n", "," }, StringSplitOptions.None);
            List<string> linesList = lines.ToList();
            if (limit.HasValue)
            {
                List<string> keys = new List<string>();
                foreach (var item in linesList)
                {
                    keys.Add(item.SubString2(0, limit.Value));
                }
                return keys;
            }
            return linesList;
        }

        public static string ToValidKeyword(string keyword)
        {
            //no symbols
            return keyword;
        }
        public static List<string> ExtractKeywordsFromUrl(string url)
        {
            //Clients URL: https://www.blushandbirchpaper.com/

            //Keywords: 
            //www.blushandbirchpaper.com 
            //www.blushandbirchpaper
            //blushandbirchpaper.com 
            //blushandbirchpaper               

            string fixed_url = url.TrimEnd('/').Trim();
            List<string> keywords = new List<string>();

            if (url.Contains("http://"))
            {
                fixed_url = fixed_url.Replace("http://", "");
                if (!string.IsNullOrEmpty(fixed_url))
                {
                    keywords.Add(fixed_url);
                }
            }
            else if (url.Contains("https://"))
            {
                fixed_url = fixed_url.Replace("https://", "");
                if (!string.IsNullOrEmpty(fixed_url) && !keywords.Contains(fixed_url))
                {
                    keywords.Add(fixed_url);
                }
            }

            string[] splitted = fixed_url.Split('.');
            if (splitted != null && splitted.Length > 0)
            {
                if (splitted.Length == 2)
                {
                    if (splitted[0] == "www")
                    {
                        string keyword = splitted[1];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }
                    }
                    else
                    {
                        string keyword = splitted[0];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }
                    }
                }

                else if (splitted.Length == 3)
                {
                    if (splitted[0] == "www")
                    {
                        string keyword = splitted[1];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }

                        string keyword_2 = splitted[1] + "." + splitted[2];
                        if (!string.IsNullOrEmpty(keyword_2) && !keywords.Contains(keyword_2))
                        {
                            keywords.Add(keyword_2);
                        }

                        string keyword_3 = splitted[0] + "." + splitted[1];
                        if (!string.IsNullOrEmpty(keyword_3) && !keywords.Contains(keyword_3))
                        {
                            keywords.Add(keyword_3);
                        }
                    }
                    else
                    {
                        string keyword = splitted[0];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }

                        string keyword_2 = splitted[0] + "." + splitted[1];
                        if (!string.IsNullOrEmpty(keyword_2) && !keywords.Contains(keyword_2))
                        {
                            keywords.Add(keyword_2);
                        }
                    }
                }
                else
                {
                    if (splitted[0] == "www")
                    {
                        string keyword = splitted[1];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }
                    }
                    else
                    {
                        string keyword = splitted[0];
                        if (!string.IsNullOrEmpty(keyword) && !keywords.Contains(keyword))
                        {
                            keywords.Add(keyword);
                        }
                    }
                }
            }

            return keywords;
        }

        public static List<string> ExtractKeyWordsFromTitle(string url, string title)
        {
            //"blush and birch paper"
            //+blush +birch +paper   

            List<string> keywords = new List<string>();

            if (title.Contains('|'))
            {
                title = title.Replace("|", " ");
            }

            if (title.Contains(" - "))
            {
                title = title.Replace(" - ", " ");
            }

            string[] title_splitted = title.Split(' ');
            if (title_splitted != null && title_splitted.Length > 0)
            {
                string url_without_punctuatuon = RemovePunctuationSymbols(url);

                string url_left = url_without_punctuatuon;

                List<string> keywords_from_title = new List<string>();
                List<string> list_of_branded = new List<string>();

                for (int j = 0; j < title_splitted.Length; j++)
                {
                    string word = title_splitted[j];

                    if (word != " " && word != "")
                    {
                        // remove special symbol
                        word = RemoveTradeMarkSymbol(word);

                        word = RemovePunctuationSymbols(word);

                        if (word.Length > 1 && (url_left.Contains(word) || url_left.ToLower().Contains(word.ToLower())))
                        {
                            if (keywords_from_title.Count == 0 && !url_left.StartsWith(word) && !url_left.StartsWith(word.ToLower()))
                                continue;


                            if (!keywords_from_title.Contains(word) && !keywords_from_title.Contains(word.ToLower()))
                            {
                                string combined = string.Join("", keywords_from_title) + word;

                                if (url_without_punctuatuon.ToLower() != combined.ToLower() && url_without_punctuatuon.ToLower().Contains("-"))
                                {
                                    url_without_punctuatuon = url_without_punctuatuon.Replace("-", "");
                                }

                                if (url_without_punctuatuon.ToLower() != combined.ToLower() && combined.ToLower().Contains("-"))
                                {
                                    combined = combined.Replace("-", "");
                                }

                                if (url_without_punctuatuon.ToLower().Contains(combined.ToLower()))
                                {
                                    keywords_from_title.Add(word);

                                    if (url_left.Contains(word))
                                        url_left = url_left.Replace(word, "");
                                    else
                                    {
                                        url_left = url_left.Replace(word.ToLower(), "");
                                    }
                                }
                            }
                        }
                        else if (word.Contains("-"))
                        {
                            string[] word_splitted = word.Split('-');
                            for (int i = 0; i < word_splitted.Length; i++)
                            {
                                string word_2 = word_splitted[i];
                                if (word_2 != " " && word_2 != "")
                                {
                                    if (!keywords_from_title.Contains(word_2) && !keywords_from_title.Contains(word_2.ToLower()))
                                    {
                                        string combined_1 = string.Join("", keywords_from_title) + word_2;

                                        if (url_without_punctuatuon.ToLower() != combined_1.ToLower() && url_without_punctuatuon.ToLower().Contains("-"))
                                        {
                                            url_without_punctuatuon = url_without_punctuatuon.Replace("-", "");
                                        }

                                        if (url_without_punctuatuon.ToLower() != combined_1.ToLower() && combined_1.ToLower().Contains("-"))
                                        {
                                            combined_1 = combined_1.Replace("-", "");
                                        }

                                        if (url_without_punctuatuon.ToLower().Contains(combined_1.ToLower()))
                                        {
                                            keywords_from_title.Add(word_2);

                                            if (url_left.Contains(word_2))
                                                url_left = url_left.Replace(word_2, "");
                                            else
                                            {
                                                url_left = url_left.Replace(word_2.ToLower(), "");
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }


                }

                if (keywords_from_title.Count > 1)
                {
                    string combined_2 = string.Join("", keywords_from_title);

                    if (url_without_punctuatuon.ToLower() != combined_2.ToLower() && url_without_punctuatuon.ToLower().Contains("-"))
                    {
                        url_without_punctuatuon = url_without_punctuatuon.Replace("-", "");
                    }

                    if (url_without_punctuatuon.ToLower() != combined_2.ToLower() && combined_2.ToLower().Contains("-"))
                    {
                        combined_2 = combined_2.Replace("-", "");
                    }

                    if (url_without_punctuatuon.ToLower() == combined_2.ToLower())
                    {
                        keywords.Add(string.Join(" ", keywords_from_title));

                        foreach (var keyword in keywords_from_title)
                        {
                            //if (IsAllowedKeyword(keyword))
                            // {
                            string branded_word = keyword; //"+" + keyword; 
                            if (!list_of_branded.Contains(branded_word) && !list_of_branded.Contains(branded_word.ToLower()))
                                list_of_branded.Add(branded_word);
                            //}
                        }

                        if (list_of_branded.Count > 0)
                            keywords.Add(string.Join(" ", list_of_branded));
                    }
                }
            }

            return keywords;
        }

        public static bool IsTopLevelDomain(string domain_part)
        {
            List<string> topLevelDomains = new List<string>();
            topLevelDomains.Add("com");
            topLevelDomains.Add("org");
            topLevelDomains.Add("net");
            topLevelDomains.Add("int");
            topLevelDomains.Add("edu");
            topLevelDomains.Add("gov");
            topLevelDomains.Add("mil");
            topLevelDomains.Add("arpa");

            foreach (var topLevelDomain in topLevelDomains)
            {
                if (domain_part == topLevelDomain)
                    return true;
            }
            return false;
        }

        public static bool IsCountryCodeTopLevelDomain(string domain_part)
        {
            List<string> countryCodetopLevelDomains = new List<string>();

            countryCodetopLevelDomains.Add("ac");
            countryCodetopLevelDomains.Add("ad");
            countryCodetopLevelDomains.Add("ae");
            countryCodetopLevelDomains.Add("af");
            countryCodetopLevelDomains.Add("ag");
            countryCodetopLevelDomains.Add("ai");
            countryCodetopLevelDomains.Add("al");
            countryCodetopLevelDomains.Add("am");
            countryCodetopLevelDomains.Add("an");
            countryCodetopLevelDomains.Add("ao");
            countryCodetopLevelDomains.Add("aq");
            countryCodetopLevelDomains.Add("ar");
            countryCodetopLevelDomains.Add("as");
            countryCodetopLevelDomains.Add("at");
            countryCodetopLevelDomains.Add("au");
            countryCodetopLevelDomains.Add("aw");
            countryCodetopLevelDomains.Add("ax");
            countryCodetopLevelDomains.Add("az");
            countryCodetopLevelDomains.Add("bb");
            countryCodetopLevelDomains.Add("bd");
            countryCodetopLevelDomains.Add("be");
            countryCodetopLevelDomains.Add("bf");
            countryCodetopLevelDomains.Add("bg");
            countryCodetopLevelDomains.Add("bh");
            countryCodetopLevelDomains.Add("bi");
            countryCodetopLevelDomains.Add("bj");
            countryCodetopLevelDomains.Add("bl");
            countryCodetopLevelDomains.Add("bm");
            countryCodetopLevelDomains.Add("bn");
            countryCodetopLevelDomains.Add("bo");
            countryCodetopLevelDomains.Add("bq");
            countryCodetopLevelDomains.Add("br");
            countryCodetopLevelDomains.Add("bs");
            countryCodetopLevelDomains.Add("bt");
            countryCodetopLevelDomains.Add("bv");
            countryCodetopLevelDomains.Add("bw");
            countryCodetopLevelDomains.Add("by");
            countryCodetopLevelDomains.Add("bz");
            countryCodetopLevelDomains.Add("ca");
            countryCodetopLevelDomains.Add("cc");
            countryCodetopLevelDomains.Add("cd");
            countryCodetopLevelDomains.Add("cf");
            countryCodetopLevelDomains.Add("cg");
            countryCodetopLevelDomains.Add("ch");
            countryCodetopLevelDomains.Add("ci");
            countryCodetopLevelDomains.Add("ck");
            countryCodetopLevelDomains.Add("cl");
            countryCodetopLevelDomains.Add("cm");
            countryCodetopLevelDomains.Add("cn");
            countryCodetopLevelDomains.Add("co");
            countryCodetopLevelDomains.Add("cr");
            countryCodetopLevelDomains.Add("cu");
            countryCodetopLevelDomains.Add("cv");
            countryCodetopLevelDomains.Add("cw");
            countryCodetopLevelDomains.Add("cx");
            countryCodetopLevelDomains.Add("cy");
            countryCodetopLevelDomains.Add("cz");
            countryCodetopLevelDomains.Add("de");
            countryCodetopLevelDomains.Add("dj");
            countryCodetopLevelDomains.Add("dk");
            countryCodetopLevelDomains.Add("dm");
            countryCodetopLevelDomains.Add("do");
            countryCodetopLevelDomains.Add("dz");
            countryCodetopLevelDomains.Add("ec");
            countryCodetopLevelDomains.Add("ee");
            countryCodetopLevelDomains.Add("eg");
            countryCodetopLevelDomains.Add("eh");
            countryCodetopLevelDomains.Add("er");
            countryCodetopLevelDomains.Add("es");
            countryCodetopLevelDomains.Add("et");
            countryCodetopLevelDomains.Add("eu");
            countryCodetopLevelDomains.Add("fi");
            countryCodetopLevelDomains.Add("fj");
            countryCodetopLevelDomains.Add("fk");
            countryCodetopLevelDomains.Add("fm");
            countryCodetopLevelDomains.Add("fo");
            countryCodetopLevelDomains.Add("fr");
            countryCodetopLevelDomains.Add("ga");
            countryCodetopLevelDomains.Add("gb");
            countryCodetopLevelDomains.Add("gd");
            countryCodetopLevelDomains.Add("ge");
            countryCodetopLevelDomains.Add("gf");
            countryCodetopLevelDomains.Add("gg");
            countryCodetopLevelDomains.Add("gh");
            countryCodetopLevelDomains.Add("gi");
            countryCodetopLevelDomains.Add("gl");
            countryCodetopLevelDomains.Add("gm");
            countryCodetopLevelDomains.Add("gn");
            countryCodetopLevelDomains.Add("gp");
            countryCodetopLevelDomains.Add("gq");
            countryCodetopLevelDomains.Add("gr");
            countryCodetopLevelDomains.Add("gs");
            countryCodetopLevelDomains.Add("gt");
            countryCodetopLevelDomains.Add("gu");
            countryCodetopLevelDomains.Add("gw");
            countryCodetopLevelDomains.Add("gy");
            countryCodetopLevelDomains.Add("hk");
            countryCodetopLevelDomains.Add("hm");
            countryCodetopLevelDomains.Add("hn");
            countryCodetopLevelDomains.Add("hr");
            countryCodetopLevelDomains.Add("ht");
            countryCodetopLevelDomains.Add("hu");
            countryCodetopLevelDomains.Add("id");
            countryCodetopLevelDomains.Add("ie");
            countryCodetopLevelDomains.Add("il");
            countryCodetopLevelDomains.Add("im");
            countryCodetopLevelDomains.Add("in");
            countryCodetopLevelDomains.Add("io");
            countryCodetopLevelDomains.Add("iq");
            countryCodetopLevelDomains.Add("ir");
            countryCodetopLevelDomains.Add("is");
            countryCodetopLevelDomains.Add("it");
            countryCodetopLevelDomains.Add("je");
            countryCodetopLevelDomains.Add("jm");
            countryCodetopLevelDomains.Add("jo");
            countryCodetopLevelDomains.Add("jp");
            countryCodetopLevelDomains.Add("ke");
            countryCodetopLevelDomains.Add("kg");
            countryCodetopLevelDomains.Add("kh");
            countryCodetopLevelDomains.Add("ki");
            countryCodetopLevelDomains.Add("km");
            countryCodetopLevelDomains.Add("kn");
            countryCodetopLevelDomains.Add("kp");
            countryCodetopLevelDomains.Add("kr");
            countryCodetopLevelDomains.Add("kw");
            countryCodetopLevelDomains.Add("ky");
            countryCodetopLevelDomains.Add("kz");
            countryCodetopLevelDomains.Add("la");
            countryCodetopLevelDomains.Add("lb");
            countryCodetopLevelDomains.Add("lc");
            countryCodetopLevelDomains.Add("li");
            countryCodetopLevelDomains.Add("lk");
            countryCodetopLevelDomains.Add("lr");
            countryCodetopLevelDomains.Add("ls");
            countryCodetopLevelDomains.Add("lt");
            countryCodetopLevelDomains.Add("lu");
            countryCodetopLevelDomains.Add("lv");
            countryCodetopLevelDomains.Add("ly");
            countryCodetopLevelDomains.Add("ma");
            countryCodetopLevelDomains.Add("mc");
            countryCodetopLevelDomains.Add("md");
            countryCodetopLevelDomains.Add("me");
            countryCodetopLevelDomains.Add("mf");
            countryCodetopLevelDomains.Add("mg");
            countryCodetopLevelDomains.Add("mh");
            countryCodetopLevelDomains.Add("mk");
            countryCodetopLevelDomains.Add("ml");
            countryCodetopLevelDomains.Add("mm");
            countryCodetopLevelDomains.Add("mn");
            countryCodetopLevelDomains.Add("mo");
            countryCodetopLevelDomains.Add("mp");
            countryCodetopLevelDomains.Add("mq");
            countryCodetopLevelDomains.Add("mr");
            countryCodetopLevelDomains.Add("ms");
            countryCodetopLevelDomains.Add("mt");
            countryCodetopLevelDomains.Add("mu");
            countryCodetopLevelDomains.Add("mv");
            countryCodetopLevelDomains.Add("mw");
            countryCodetopLevelDomains.Add("mx");
            countryCodetopLevelDomains.Add("my");
            countryCodetopLevelDomains.Add("mz");
            countryCodetopLevelDomains.Add("na");
            countryCodetopLevelDomains.Add("nc");
            countryCodetopLevelDomains.Add("ne");
            countryCodetopLevelDomains.Add("nf");
            countryCodetopLevelDomains.Add("ng");
            countryCodetopLevelDomains.Add("ni");
            countryCodetopLevelDomains.Add("nl");
            countryCodetopLevelDomains.Add("no");
            countryCodetopLevelDomains.Add("np");
            countryCodetopLevelDomains.Add("nr");
            countryCodetopLevelDomains.Add("nu");
            countryCodetopLevelDomains.Add("nz");
            countryCodetopLevelDomains.Add("om");
            countryCodetopLevelDomains.Add("pa");
            countryCodetopLevelDomains.Add("pe");
            countryCodetopLevelDomains.Add("pf");
            countryCodetopLevelDomains.Add("pg");
            countryCodetopLevelDomains.Add("ph");
            countryCodetopLevelDomains.Add("pk");
            countryCodetopLevelDomains.Add("pl");
            countryCodetopLevelDomains.Add("pm");
            countryCodetopLevelDomains.Add("pn");
            countryCodetopLevelDomains.Add("pr");
            countryCodetopLevelDomains.Add("ps");
            countryCodetopLevelDomains.Add("pt");
            countryCodetopLevelDomains.Add("pw");
            countryCodetopLevelDomains.Add("py");
            countryCodetopLevelDomains.Add("qa");
            countryCodetopLevelDomains.Add("re");
            countryCodetopLevelDomains.Add("ro");
            countryCodetopLevelDomains.Add("rs");
            countryCodetopLevelDomains.Add("ru");
            countryCodetopLevelDomains.Add("rw");
            countryCodetopLevelDomains.Add("sa");
            countryCodetopLevelDomains.Add("sb");
            countryCodetopLevelDomains.Add("sc");
            countryCodetopLevelDomains.Add("sd");
            countryCodetopLevelDomains.Add("se");
            countryCodetopLevelDomains.Add("sg");
            countryCodetopLevelDomains.Add("sh");
            countryCodetopLevelDomains.Add("si");
            countryCodetopLevelDomains.Add("sj");
            countryCodetopLevelDomains.Add("sk");
            countryCodetopLevelDomains.Add("sl");
            countryCodetopLevelDomains.Add("sm");
            countryCodetopLevelDomains.Add("sn");
            countryCodetopLevelDomains.Add("so");
            countryCodetopLevelDomains.Add("sr");
            countryCodetopLevelDomains.Add("ss");
            countryCodetopLevelDomains.Add("st");
            countryCodetopLevelDomains.Add("su");
            countryCodetopLevelDomains.Add("sv");
            countryCodetopLevelDomains.Add("sx");
            countryCodetopLevelDomains.Add("sy");
            countryCodetopLevelDomains.Add("sz");
            countryCodetopLevelDomains.Add("tc");
            countryCodetopLevelDomains.Add("td");
            countryCodetopLevelDomains.Add("tf");
            countryCodetopLevelDomains.Add("tg");
            countryCodetopLevelDomains.Add("th");
            countryCodetopLevelDomains.Add("tj");
            countryCodetopLevelDomains.Add("tk");
            countryCodetopLevelDomains.Add("tl");
            countryCodetopLevelDomains.Add("tm");
            countryCodetopLevelDomains.Add("tn");
            countryCodetopLevelDomains.Add("to");
            countryCodetopLevelDomains.Add("tp");
            countryCodetopLevelDomains.Add("tr");
            countryCodetopLevelDomains.Add("tt");
            countryCodetopLevelDomains.Add("tv");
            countryCodetopLevelDomains.Add("tw");
            countryCodetopLevelDomains.Add("tz");
            countryCodetopLevelDomains.Add("ua");
            countryCodetopLevelDomains.Add("ug");
            countryCodetopLevelDomains.Add("uk");
            countryCodetopLevelDomains.Add("um");
            countryCodetopLevelDomains.Add("us");
            countryCodetopLevelDomains.Add("uy");
            countryCodetopLevelDomains.Add("uz");
            countryCodetopLevelDomains.Add("va");
            countryCodetopLevelDomains.Add("vc");
            countryCodetopLevelDomains.Add("ve");
            countryCodetopLevelDomains.Add("vg");
            countryCodetopLevelDomains.Add("vi");
            countryCodetopLevelDomains.Add("vn");
            countryCodetopLevelDomains.Add("vu");
            countryCodetopLevelDomains.Add("wf");
            countryCodetopLevelDomains.Add("ws");
            countryCodetopLevelDomains.Add("ye");
            countryCodetopLevelDomains.Add("yt");
            countryCodetopLevelDomains.Add("za");
            countryCodetopLevelDomains.Add("zm");
            countryCodetopLevelDomains.Add("zw");

            foreach (var topLevelDomain in countryCodetopLevelDomains)
            {
                if (domain_part == topLevelDomain)
                    return true;
            }
            return false;
        }

        private static string RemoveTradeMarkSymbol(string word)
        {
            //™	&trade;	&#8482;	&#x2122;	trademark symbol
            if (word.Contains("™"))
                word = word.Replace("™", "");

            //®	&reg;	&#174;	&#x00AE;	registered trademark symbol
            if (word.Contains("®"))
                word = word.Replace("®", "");

            return word;
        }

        private static string RemovePunctuationSymbols(string word)
        {
            if (word.Contains("!"))
                word = word.Replace("!", "");

            if (word.Contains(";"))
                word = word.Replace(";", "");

            if (word.Contains(":"))
                word = word.Replace(":", "");

            if (word.Contains(","))
                word = word.Replace(",", "");

            if (word.Contains("?"))
                word = word.Replace("?", "");

            if (word.Contains("'"))
                word = word.Replace("'", "");

            //if (word.Contains("-"))
            //    word = word.Replace("-", "");

            //if (word.Contains("|"))
            //    word = word.Replace("|", "");

            return word;
        }

        public static bool IsAllowedKeyword(string word)
        {
            string fixed_word = word.ToLower();

            if (IsConnectingWord(fixed_word))
                return false;

            if (IsIndefiniteArticle(fixed_word))
                return false;

            if (IsDefiniteArticle(fixed_word))
                return false;

            if (IsDemonstrative(fixed_word))
                return false;

            if (IsPossessive(fixed_word))
                return false;

            if (IsQuantifier(fixed_word))
                return false;

            if (IsNumber(fixed_word))
                return false;

            if (IsDistributive(fixed_word))
                return false;

            if (IsDifferenceWord(fixed_word))
                return false;

            if (IsQuestionOrDefiningWord(fixed_word))
                return false;

            if (IsPrepositionWord(fixed_word))
                return false;

            return true;
        }

        public static bool IsDefiniteArticle(string word)
        {
            if (word == "the")
                return true;

            return false;
        }

        public static bool IsIndefiniteArticle(string word)
        {
            if (word == "a" || word == "an")
                return true;

            return false;
        }

        public static bool IsConnectingWord(string word)
        {
            if (word == "and")
                return true;

            return false;
        }


        public static bool IsDifferenceWord(string word)
        {
            if (word == "other" || word == "another")
                return true;

            return false;
        }

        public static bool IsPrepositionWord(string word)
        {
            if (word == "of" || word == "to" || word == "for" || word == "on" || word == "at" || word == "in"
                || word == "with" || word == "over" || word == "by")
                return true;

            return false;
        }

        public static bool IsQuestionOrDefiningWord(string word)
        {
            if (word == "which" || word == "what" || word == "whose")
                return true;

            return false;
        }

        public static bool IsDistributive(string word)
        {
            if (word == "all" || word == "both" || word == "half" || word == "either" || word == "neither" || word == "each" || word == "every")
                return true;

            return false;
        }

        public static bool IsQuantifier(string word)
        {
            if (word == "few" || word == "little" || word == "much" || word == "many" || word == "lot" || word == "most" || word == "any" || word == "some" || word == "enough") //etc.
                return true;

            return false;
        }

        public static bool IsDemonstrative(string word)
        {
            if (word == "this" || word == "that" || word == "these" || word == "those")
                return true;

            return false;
        }

        public static bool IsPossessive(string word)
        {
            if (word == "my" || word == "your" || word == "his" || word == "her" || word == "its" || word == "our" || word == "their")
                return true;

            return false;
        }

        public static bool IsNumber(string word)
        {
            if (word == "one" || word == "ten" || word == "thirty") //etc.
                return true;

            return false;
        }

        public static string GetTitleFromHtml(string url)
        {
            string title = null;

            string collectionPageContent = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
            if (!string.IsNullOrEmpty(collectionPageContent))
            {
                title = ExtractTitleFromContent(collectionPageContent);
            }

            return title;
        }


        public static string ExtractDescriptionFromContent(string collectionPageContent)
        {
            string description = RegexHelper.GetFirstGroupValue(collectionPageContent, @"<meta property=\""og:description\"" content=\""?(.*?)\""");
            if (!string.IsNullOrEmpty(description))
            {
                description = SpecialCharactersHandler.ReplaceSpecialSymbols(description);
            }
            else
            {
                description = RegexHelper.GetFirstGroupValue(collectionPageContent, @"<meta name='description' content='?(.*?)\'");
                if (!string.IsNullOrEmpty(description))
                {
                    description = SpecialCharactersHandler.ReplaceSpecialSymbols(description);
                }
                else
                {
                    description = RegexHelper.GetFirstGroupValue(collectionPageContent, @"<meta name=\""description\"" content=\""?(.*?)\""");
                    if (!string.IsNullOrEmpty(description))
                    {
                        description = SpecialCharactersHandler.ReplaceSpecialSymbols(description);
                    }
                }
            }

            return description;
        }

        public static string ExtractTitleFromContent(string collectionPageContent)
        {
            string title = null;
            string siteNameFromHtml = RegexHelper.GetFirstGroupValue(collectionPageContent, @"<meta property=\""og:site_name\"" content=\""?(.*?)\""");
            if (!string.IsNullOrEmpty(siteNameFromHtml))
            {
                siteNameFromHtml = SpecialCharactersHandler.ReplaceSpecialSymbols(siteNameFromHtml);
                title = siteNameFromHtml;
            }
            else
            {
                string titleFromHtml = RegexHelper.GetFirstGroupValue(collectionPageContent, @"<meta property=\""og:title\"" content=\""?(.*?)\""");
                if (!string.IsNullOrEmpty(titleFromHtml))
                {
                    titleFromHtml = SpecialCharactersHandler.ReplaceSpecialSymbols(titleFromHtml);
                    title = titleFromHtml;
                }
                else
                {
                    titleFromHtml = RegexHelper.GetFirstGroupValue(collectionPageContent, @"<title>?(.*?)</title>");
                    if (!string.IsNullOrEmpty(titleFromHtml))
                    {
                        titleFromHtml = SpecialCharactersHandler.ReplaceSpecialSymbols(titleFromHtml);
                        title = titleFromHtml;
                    }
                }
            }
            return title;
        }
    }
}

