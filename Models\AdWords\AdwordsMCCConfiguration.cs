﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Models.AdWords
{
    public class AdwordsMCCConfiguration : IAdwordsMCCConfiguration
    {
        public string PrimaryBillingId => "8402-1803-1599";
        public string BillingAccountId => "5097-4714-2575-1128";
        public string MerchantClientCustomerId => "495-725-3189";
        public string AccountCurrency => "USD";
        public long DailyAmount => 5000000;
        public string OrderStartDateTime => string.Format("{0} Asia/Jerusalem", DateTime.Now.AddHours(8).ToString("yyyyMMdd HHmmss"));
        public string OrderEndDateTime => "******** 235959 Asia/Jerusalem";
        public string DateTimeZone => "Asia/Jerusalem";
        public bool UseFirstUrlAsIs => false;
        public string CampaignDailyBudget => null;
    }

    public class AdwordsWallShop5MCCConfiguration : IAdwordsMCCConfiguration
    {
        public string PrimaryBillingId => "8402-1803-1599";
        public string BillingAccountId => "9295-9748-5844-4848";
        public string MerchantClientCustomerId => "495-725-3189";
        public string AccountCurrency => "ILS";
        public long DailyAmount => 7700000;
        public string OrderStartDateTime => string.Format("{0} Asia/Jerusalem", DateTime.Now.AddHours(8).ToString("yyyyMMdd HHmmss"));
        public string OrderEndDateTime => "******** 235959 Asia/Jerusalem";
        public string DateTimeZone => "Asia/Jerusalem";
        public bool UseFirstUrlAsIs => true;

        public string CampaignDailyBudget => "0.01";
    }
}
