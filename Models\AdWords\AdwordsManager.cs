﻿using CsvHelper;
using Storeya.Core.Entities;
using Storeya.Core.Helpers;
using Storeya.Core.Models.DataProviders;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models.TbInternalTasks;
using Storeya.Core.Models.WebsiteAnalyser;
using System.Web;
using System.Data.Entity.Infrastructure;
using Storeya.Core.Models.Marketplaces;
using Storeya.Core.Models.GA;
using static Storeya.Core.Helpers.CountriesHelper;
using Storeya.Core.Models.ShopAttributes;
using Newtonsoft.Json.Linq;
using System.Reflection;
using static System.Net.Mime.MediaTypeNames;
using static Storeya.Core.Models.GA.GA4Helper;
using static Storeya.Core.Models.AdWords.SearchAdWordsCampaign;
using System.Runtime;
using System.Security.Policy;
using nQuant;
using Storeya.Core.Models.Account;

namespace Storeya.Core.Models.AdWords
{
    public class SearchCampaignPattern
    {
        public string CollectionName { get; set; }
        public string Pattern { get; set; }
        public int PatternID { get; set; }
    }
    public enum AdwordsPixelResults
    {
        NotFoundOnPage = 0,
        OK = 1,
        NoOrders = 10
    }


    public enum AdwordsUploadStatus
    {
        IncludedToExcel = 1,
        UploadedToAdwords = 10
    }

    public enum LinkPriority
    {
        Low = 1,
        Medium = 5,
        High = 10
    }

    public class TBLink
    {
        public string Link { get; set; }
        public int Priority { get; set; }
    }

    public class AdwordsManager
    {
        //public static GmailAdCampaignSettings GetGmailAdCampaign(int shopID)
        //{
        //    GmailAdCampaignSettings rgspCampaign = null;
        //    TrafficBooster tb_settings = TrafficBoostersDbHelper.GetSettings(shopID);
        //    DsaCampaign dsaCampaign = GetDsaCampaign(tb_settings, false, true);
        //    if (isValidCampaign(dsaCampaign))
        //    {
        //        rgspCampaign = SetRgspCampaign(dsaCampaign, shopID);
        //        if (rgspCampaign.ProductImages == null || rgspCampaign.ProductImages.Count == 0)
        //        {
        //            throw new Exception(string.Format("ShopID: {0} Not valid RGSP campaign, - no images", shopID));
        //        }

        //        if (string.IsNullOrEmpty(rgspCampaign.Description))
        //        {
        //            throw new Exception(string.Format("ShopID: {0} Not valid RGSP campaign, - No description", shopID));
        //        }
        //    }
        //    return rgspCampaign;
        //}

        //public static GmailAdCampaignSettings GetGmailAdCampaignNew(int shopID)
        //{
        //    GmailAdCampaignSettings rgspCampaign = null;
        //    TrafficBooster tb_settings = TrafficBoostersDbHelper.GetSettings(shopID);
        //    DsaCampaign dsaCampaign = GetDsaCampaign(tb_settings, false, true);
        //    if (isValidCampaign(dsaCampaign))
        //    {
        //        rgspCampaign = GetRgspCampaign(dsaCampaign, shopID);
        //        if (rgspCampaign.ProductImages == null || rgspCampaign.ProductImages.Count == 0)
        //        {
        //            throw new Exception(string.Format("ShopID: {0} Not valid RGSP campaign, - no images", shopID));
        //        }

        //        //if (string.IsNullOrEmpty(rgspCampaign.Description))
        //        //{
        //        //    throw new Exception(string.Format("ShopID: {0} Not valid RGSP campaign, - No description", shopID));
        //        //}
        //    }
        //    else
        //    {
        //        throw new Exception(string.Format("ShopID: {0} RGSP campaign can not be created, - DSA campaign is not valid: {1} ", shopID, dsaCampaign.CreationError));
        //    }
        //    return rgspCampaign;
        //}

        //public static GmailAdCampaignSettings GetRgspCampaign(DsaCampaign dsaCampaign, int shopID)
        //{
        //    Console.WriteLine("GetRgspCampaign started...");

        //    GmailAdCampaignSettings rgspCampaign = new GmailAdCampaignSettings();
        //    rgspCampaign.Name = dsaCampaign.Name.Replace("_Dynamic", "_RGSP");
        //    rgspCampaign.LocationsCodes = dsaCampaign.LocationsCodes;
        //    rgspCampaign.AllLanguages = dsaCampaign.AllLanguages;

        //    string businessUrl = null;
        //    if (MarketplaceUrlHelper.IsMarketplace(dsaCampaign.DSAWebsite))
        //    {
        //        var urls = MarketplaceUrlHelper.GetDsaUrls(dsaCampaign.DSAWebsite);
        //        businessUrl = urls.Display;
        //    }

        //    rgspCampaign.BusinessName = AdwordsManager.GetBusinessName(!string.IsNullOrEmpty(businessUrl) ? businessUrl : dsaCampaign.DSAWebsite);
        //    string IMAGES_PLACEHOLDER_IMAGES_URL = "https://strys3.s3.amazonaws.com/images";
        //    rgspCampaign.DefaultMarketingImage = IMAGES_PLACEHOLDER_IMAGES_URL + "/600x314.png";
        //    rgspCampaign.DefaultLogoImage = IMAGES_PLACEHOLDER_IMAGES_URL + "/144x144.png";
        //    rgspCampaign.DefaultProductImage = IMAGES_PLACEHOLDER_IMAGES_URL + "/300x300.png";

        //    rgspCampaign.FinalUrls = new string[] { dsaCampaign.FinalUrl };

        //    Console.WriteLine("Extracting url content. URL: " + dsaCampaign.FinalUrl);

        //    string homePageHtml = GetHomePageContent(dsaCampaign.FinalUrl);
        //    if (!string.IsNullOrEmpty(homePageHtml))
        //    {
        //        HtmlParser parser = new HtmlParser(homePageHtml);

        //        Console.WriteLine("Parsing url content. URL: " + dsaCampaign.FinalUrl);
        //        parser.Parse();

        //        rgspCampaign.Headline = FixHeadline(!string.IsNullOrEmpty(parser.Title) ? parser.Title : (!string.IsNullOrEmpty(parser.MetaOgTitle) ? parser.MetaOgTitle : parser.MetaOgSiteName));
        //        rgspCampaign.Description = FixDescription(!string.IsNullOrEmpty(parser.MetaDescription) ? parser.MetaDescription : parser.MetaOgDescription);

        //        var validLogoImage = GetValidLogoImage(dsaCampaign.DSAWebsite, parser.LogoImages, shopID);
        //        rgspCampaign.LogoImage = (!string.IsNullOrEmpty(validLogoImage) ? validLogoImage : rgspCampaign.DefaultLogoImage);

        //        int? platform = GetShopPlatform(shopID);
        //        List<string> hpProductImages = GetProductImages(dsaCampaign.DSAWebsite, parser.Images, rgspCampaign.DefaultProductImage);
        //        if ((hpProductImages == null || hpProductImages.Count < 10) && platform.HasValue && platform.Value == (int)CatalogSourcePlatforms.ShopifyApi)
        //        {
        //            string collectionsUrl = dsaCampaign.FinalUrl.TrimEnd('/') + "/collections/all?sort_by=best-selling";

        //            Console.WriteLine("Extracting url content. URL: " + collectionsUrl);

        //            try
        //            {
        //                string collectionsPageHtml = GetHomePageContent(collectionsUrl);
        //                if (!string.IsNullOrEmpty(collectionsPageHtml))
        //                {
        //                    parser = new HtmlParser(collectionsPageHtml);

        //                    Console.WriteLine("Parsing images only. URL: " + dsaCampaign.FinalUrl);
        //                    parser.ParseImagesOnly();
        //                    hpProductImages = GetProductImages(dsaCampaign.DSAWebsite, parser.Images, rgspCampaign.DefaultProductImage);
        //                }
        //            }
        //            catch (Exception)
        //            {
        //                Console.WriteLine("Failed to collecting best selling products for images. Use images from HP.");
        //            }
        //        }

        //        rgspCampaign.ProductImages = GetValidProductImages(hpProductImages, platform, shopID, rgspCampaign.LogoImage);
        //        rgspCampaign.MarketingImage = rgspCampaign.ProductImages != null ? rgspCampaign.ProductImages.First() : null;
        //    }

        //    return rgspCampaign;
        //}

        public static string GetBusinessName(string dsaWebsite)
        {
            string businessName = null;
            if (dsaWebsite.Length > 20)
            {
                string domain = UrlPathHelper.GetDomainName(dsaWebsite);

                if (domain.Contains("www."))
                    domain = domain.Replace("www.", "");

                if (domain.Length > 20 && domain.Contains("."))
                {
                    int index = domain.IndexOf('.');
                    domain = domain.Remove(index);
                }

                if (domain.Length > 20 && domain.Contains("-"))
                {
                    int index = domain.LastIndexOf('-');
                    domain = domain.Remove(index);
                }

                businessName = domain;
            }
            else
            {
                businessName = dsaWebsite;
            }

            return businessName;
        }

        public static string GetValidLogoImage(string domain, List<string> logoImages, int shopID)
        {
            string validLogoImage = null;
            string logoImage = ExtractLogoImageUrl(domain, logoImages);
            if (!string.IsNullOrEmpty(logoImage))
            {
                try
                {
                    validLogoImage = GetValidImage(logoImage, 150, 150, shopID);
                }
                catch (Exception ex)
                {
                    Console.Write(ex.ToString());
                }
            }
            return validLogoImage;
        }

        public static GmailAdCampaignSettings SetRgspCampaign(DsaCampaign dsaCampaign, int shopID)
        {
            string IMAGES_PLACEHOLDER_IMAGES_URL = "https://strys3.s3.amazonaws.com/images";
            GmailAdCampaignSettings rgspCampaign = new GmailAdCampaignSettings();
            rgspCampaign.Name = dsaCampaign.Name.Replace("_Dynamic", "_RGSP");
            rgspCampaign.LocationsCodes = dsaCampaign.LocationsCodes;
            rgspCampaign.AllLanguages = dsaCampaign.AllLanguages;

            if (dsaCampaign.DSAWebsite.Length > 20)
            {
                string domain = UrlPathHelper.GetDomainName(dsaCampaign.DSAWebsite);

                if (domain.Contains("www."))
                    domain = domain.Replace("www.", "");

                if (domain.Length > 20 && domain.Contains("."))
                {
                    int index = domain.IndexOf('.');
                    domain = domain.Remove(index);
                }

                rgspCampaign.BusinessName = domain;
            }
            else
            {
                rgspCampaign.BusinessName = dsaCampaign.DSAWebsite;
            }

            rgspCampaign.FinalUrls = new string[] { dsaCampaign.FinalUrl };

            //Marketing image. An image must first be created using the MediaService, and Image.mediaId must be populated when creating a GmailAd.Valid image types are GIF, JPEG, and PNG. 
            //The minimum size is 600x314 and the aspect ratio must be 600:314 (+-1%). 
            //For square marketing image, the minimum size is 300x300 and the aspect ratio must be 1:1 (+-1%).
            //Either productVideos or marketingImage must be specified.
            rgspCampaign.DefaultMarketingImage = IMAGES_PLACEHOLDER_IMAGES_URL + "/600x314.png";

            //Required. Logo image. An image must first be created using the MediaService, and Image.mediaId must be populated when creating a GmailTeaser. 
            //Valid image types are GIF, JPEG, and PNG. 
            //The minimum size is 144x144 and the aspect ratio must be 1:1 (+-1%). Required.
            rgspCampaign.DefaultLogoImage = IMAGES_PLACEHOLDER_IMAGES_URL + "/144x144.png";

            string homePageHtml = GetHomePageContent(dsaCampaign.FinalUrl);
            if (!string.IsNullOrEmpty(homePageHtml))
            {
                rgspCampaign.Headline = GetHeadline(homePageHtml);
                rgspCampaign.Description = GetDescription(homePageHtml);

                string validLogoImage = null;
                string logoImage = GetLogoImageUrl(homePageHtml, dsaCampaign.DSAWebsite);

                try
                {
                    validLogoImage = GetValidImage(logoImage, 150, 150, shopID);
                }
                catch (Exception ex)
                {
                    Console.Write(ex.ToString());
                }

                if (string.IsNullOrEmpty(validLogoImage))
                    validLogoImage = rgspCampaign.DefaultLogoImage;

                rgspCampaign.LogoImage = validLogoImage;


                int? platform = GetShopPlatform(shopID);
                List<string> hp_images = GetHpImages(dsaCampaign.DSAWebsite, homePageHtml);
                if ((hp_images == null || hp_images.Count < 10) && platform.HasValue && platform.Value == (int)CatalogSourcePlatforms.ShopifyApi)
                {
                    homePageHtml = GetHomePageContent(dsaCampaign.FinalUrl.TrimEnd('/') + "/collections/all?sort_by=best-selling");
                    if (!string.IsNullOrEmpty(homePageHtml))
                    {
                        hp_images = GetHpImages(dsaCampaign.DSAWebsite, homePageHtml);
                    }
                }

                rgspCampaign.ProductImages = GetValidProductImages(hp_images, platform, shopID, rgspCampaign.LogoImage);
                rgspCampaign.MarketingImage = rgspCampaign.ProductImages != null ? rgspCampaign.ProductImages.First() : null;
            }

            return rgspCampaign;
        }

        public static List<string> GetValidProductImages(List<string> hp_images, int? platform, int shopID, string logoImage)
        {
            Console.WriteLine("Getting valid product images...");

            List<string> validImages = null;

            //Exclamation mark in the ad's headline or more than one per ad&lt;/li&gt;
            //Repeated punctuation or symbols (such as "!!!!free!!!")&lt;/li&gt;
            //Characters that don't adhere to their true meaning (such as "4 sale" or "for $@le")&lt;/li&gt;
            //Non-standard symbols (such as ~ and *) except in a company name or standard industry use (such as "5* hotel")

            if (hp_images != null)
            {
                List<string> product_hp_images = hp_images.Where(x => x != logoImage).ToList();
                if (platform.HasValue && platform.Value == (int)CatalogSourcePlatforms.ShopifyApi)
                {
                    if (hp_images.Where(x => x.Contains("/products/")).Any())
                        product_hp_images = hp_images.Where(x => x.Contains("/products/")).ToList();
                }

                if (product_hp_images != null)
                {
                    int miminumHeight = 300;
                    int minimumWidth = 300;

                    foreach (var product_hp_image in product_hp_images)
                    {
                        //Product image. An image must first be created using the MediaService, and Image.mediaId must be populated when creating a ProductImage. 
                        //Valid image types are GIF, JPEG, and PNG. 
                        //The minimum size is 300x300 and the aspect ratio must be 1:1 (+-1%).   
                        try
                        {
                            string validImage = GetValidProductImage(product_hp_image, miminumHeight, minimumWidth, shopID, 100000, 300, platform);


                            if (validImages == null)
                                validImages = new List<string>();

                            if (!string.IsNullOrEmpty(validImage) && !validImages.Contains(validImage))
                                validImages.Add(validImage);

                            if (validImages.Count == 15)
                                break;
                        }
                        catch (Exception ex)
                        {
                            Console.Write(ex.ToString());
                        }
                    }
                }
            }
            return validImages;
        }

        private static dynamic GetImageDimensions(string imageUrl, int shopID)
        {
            // handling https requests
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            //bypass (ignore) SSL certificate
            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
            byte[] imageData = GetImageDataBytes(imageUrl, shopID);
            using (MemoryStream imgStream = new MemoryStream(imageData))
            {
                System.Drawing.Image img = System.Drawing.Image.FromStream(imgStream);
                long imageBytes = imgStream.Length;
                return new { imageBytes, img.Height, img.Width };
            }
        }

        public static string GetValidProductImage(string product_hp_image, int miminumHeight, int minimumWidth, int shopID, int maxAllowedSize = 100000, int minSize = 300, int? platform = null) // //Maximum size: 0.1MB (100 000 bytes)
        {

            Console.WriteLine("Getting valid image. Image: " + product_hp_image);
            int maxSize = 600;
            string validImage = null;
            string imageUrl = product_hp_image;
            bool shopifyApi = false;
            if (platform.HasValue && platform.Value == CatalogSourcePlatforms.ShopifyApi.GetHashCode())
            {
                shopifyApi = true;
            }
            var ImageDimensions = GetImageDimensions(imageUrl, shopID);
            int imageHeight = ImageDimensions.Height;
            int imageWidth = ImageDimensions.Width;
            long imageBytes = ImageDimensions.imageBytes;
            bool scaleChange = false;
            bool requiredChange = false;
            bool requiredSizeChange = false;
            if ((imageHeight < miminumHeight && imageWidth < minimumWidth) || (imageHeight > maxSize || imageWidth > maxSize))
            {
                requiredChange = true;
            }
            if (imageBytes > maxAllowedSize)
            {
                requiredSizeChange = true;
            }
            if (imageHeight != imageWidth)
            {
                scaleChange = true;
            }
            if (!requiredChange && !requiredSizeChange && !scaleChange)
            {
                return product_hp_image;
            }
            if (shopifyApi)
            {
                //Image Dimensions small
                if (requiredChange)
                {
                    imageUrl = ImagePathHelper.ShopifyGetImageUrlByLimit(imageUrl, maxSize, true, true);
                    ImageDimensions = GetImageDimensions(imageUrl, shopID);
                    imageHeight = ImageDimensions.Height;
                    imageWidth = ImageDimensions.Width;
                    imageBytes = ImageDimensions.imageBytes;
                }
                //Image size Is larger
                if (imageBytes > maxAllowedSize)
                {
                    maxSize = 450;
                    while (imageBytes > maxAllowedSize)
                    {
                        maxSize -= 50;
                        imageUrl = ImagePathHelper.ShopifyGetImageUrlByLimit(imageUrl, maxSize, true, true);
                        ImageDimensions = GetImageDimensions(imageUrl, shopID);
                        imageHeight = ImageDimensions.Height;
                        imageWidth = ImageDimensions.Width;
                        imageBytes = ImageDimensions.imageBytes;
                    }
                    requiredSizeChange = false;
                }
                requiredChange = false;
            }
            //aspect ratio must be 1:1
            if ((imageHeight != imageWidth) || (imageHeight < miminumHeight || imageWidth < minimumWidth) || requiredSizeChange || requiredChange)
            {
                Console.WriteLine("Rescaling... Image: " + imageUrl);
                int max = Math.Max(imageHeight, imageWidth);
                if (max <= miminumHeight)
                {
                    max = miminumHeight;
                }
                else if (max > maxSize)
                {
                    max = maxSize;
                }
                string rescaledProductImage = RescaleImage(imageUrl, shopID, max, max);
                validImage = rescaledProductImage;
            }
            else
            {
                validImage = imageUrl;
            }
            return validImage;
        }

        public static string GetValidImage(string product_hp_image, int miminumHeight, int minimumWidth, int shopID, int maxAllowedSize = 100000, int minSize = 300) // //Maximum size: 0.1MB (100 000 bytes)
        {
            Console.WriteLine("Getting valid image. Image: " + product_hp_image);

            string validImage = null;

            // handling https requests
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            //bypass (ignore) SSL certificate
            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;

            if (product_hp_image.Contains("_{width}x."))
                product_hp_image = product_hp_image.Replace("_{width}x.", "_300x.");

            //WebClient client = ImagesDownloader.SetWebClient();
            //byte[] imageData = client.DownloadData(product_hp_image);

            byte[] imageData = GetImageDataBytes(product_hp_image, shopID);

            MemoryStream imgStream = new MemoryStream(imageData);
            System.Drawing.Image img = System.Drawing.Image.FromStream(imgStream);
            long imageBytes = imgStream.Length;
            int hSize = img.Height;
            int wSize = img.Width;

            if ((hSize < miminumHeight && wSize < minimumWidth) || imageBytes > maxAllowedSize)
            {
                //https://cdn.shopify.com/s/files/1/0005/6570/6809/products/0154_camo_1_50x.jpg
                //https://cdn.shopify.com/s/files/1/0005/6570/6809/products/0154_camo_1_300x.jpg

                imgStream.Dispose();

                //try to create the image with a best resolution
                string imageWithBestResolution = null;

                if (product_hp_image.Contains("_"))
                {
                    string[] splitted = product_hp_image.Split('_');
                    if (splitted.Length > 1)
                    {
                        string lastData = splitted[splitted.Length - 1];

                        if (lastData.Contains(".jpg"))
                            imageWithBestResolution = product_hp_image.Replace(lastData, "300x.jpg");

                        else if (lastData.Contains(".JPG"))
                            imageWithBestResolution = product_hp_image.Replace(lastData, "300x.JPG");

                        else if (lastData.Contains(".png"))
                            imageWithBestResolution = product_hp_image.Replace(lastData, "300x.png");

                        else if (lastData.Contains(".PNG"))
                            imageWithBestResolution = product_hp_image.Replace(lastData, "300x.PNG");

                        else if (lastData.Contains(".JPEG"))
                            imageWithBestResolution = product_hp_image.Replace(lastData, "300x.JPEG");

                        else if (lastData.Contains(".jpeg"))
                            imageWithBestResolution = product_hp_image.Replace(lastData, "300x.jpeg");
                    }
                }

                if (!string.IsNullOrEmpty(imageWithBestResolution))
                {
                    Console.WriteLine("Getting image With Best Resolution. Image: " + imageWithBestResolution);

                    //client = ImagesDownloader.SetWebClient();
                    //imageData = client.DownloadData(imageWithBestResolution);
                    imageData = GetImageDataBytes(imageWithBestResolution, shopID);

                    imgStream = new MemoryStream(imageData);
                    img = System.Drawing.Image.FromStream(imgStream);

                    hSize = img.Height;
                    wSize = img.Width;
                    imageBytes = imgStream.Length;

                    product_hp_image = imageWithBestResolution;
                }

                while (imageBytes > maxAllowedSize)
                {
                    minSize -= 25;
                    string new_size_string = minSize.ToString() + "x";

                    string imageWithBestResolution_1 = null;
                    string[] splitted_1 = product_hp_image.Split('_');
                    if (splitted_1.Length > 1)
                    {
                        string lastData = splitted_1[splitted_1.Length - 1];

                        if (lastData.Contains(".jpg"))
                            imageWithBestResolution_1 = product_hp_image.Replace(lastData, new_size_string + ".jpg");

                        else if (lastData.Contains(".JPG"))
                            imageWithBestResolution_1 = product_hp_image.Replace(lastData, new_size_string + ".JPG");

                        else if (lastData.Contains(".png"))
                            imageWithBestResolution_1 = product_hp_image.Replace(lastData, new_size_string + ".png");

                        else if (lastData.Contains(".PNG"))
                            imageWithBestResolution_1 = product_hp_image.Replace(lastData, new_size_string + ".PNG");

                        else if (lastData.Contains(".JPEG"))
                            imageWithBestResolution_1 = product_hp_image.Replace(lastData, new_size_string + ".JPEG");

                        else if (lastData.Contains(".jpeg"))
                            imageWithBestResolution_1 = product_hp_image.Replace(lastData, new_size_string + ".jpeg");
                    }

                    if (!string.IsNullOrEmpty(imageWithBestResolution_1))
                    {
                        Console.WriteLine("Getting image With Best Resolution_1. Image: " + imageWithBestResolution_1);

                        //client = ImagesDownloader.SetWebClient();
                        //imageData = client.DownloadData(imageWithBestResolution_1);

                        imageData = GetImageDataBytes(imageWithBestResolution_1, shopID);

                        imgStream = new MemoryStream(imageData);
                        img = System.Drawing.Image.FromStream(imgStream);

                        hSize = img.Height;
                        wSize = img.Width;
                        imageBytes = imgStream.Length;

                        product_hp_image = imageWithBestResolution_1;
                    }
                    else
                    {
                        break;
                    }
                }
            }

            if (hSize < miminumHeight || wSize < minimumWidth)
            {
                Console.WriteLine("Rescaling... Image: " + product_hp_image);
                string rescaledProductImage = RescaleImage(product_hp_image, shopID, miminumHeight, minimumWidth);
                validImage = rescaledProductImage;

            }
            else if (hSize != wSize) //aspect ratio must be 1:1
            {
                Console.WriteLine("Rescaling... Image: " + product_hp_image);
                int max = Math.Max(hSize, wSize);
                string rescaledProductImage = RescaleImage(product_hp_image, shopID, max, max);
                validImage = rescaledProductImage;
            }
            else
            {
                validImage = product_hp_image;
            }

            imgStream.Dispose();

            return validImage;
        }

        private static byte[] GetImageDataBytes(string product_hp_image, int shopID)
        {
            byte[] imageData = null;

            HttpWebResponse webResponse = HttpRequestResponseHelper.GetHttpWebResponse(product_hp_image, true, shopID);
            Stream responseStream2 = webResponse.GetResponseStream();
            if (webResponse.ContentEncoding.ToLower().Contains("gzip"))
                responseStream2 = new GZipStream(responseStream2, CompressionMode.Decompress);

            using (BinaryReader br = new BinaryReader(responseStream2))
            {
                imageData = br.ReadBytes(500000);
                br.Close();
            }

            return imageData;
        }


        public static string RescaleImage(string imageUrl, int shopID, int? requiredHeight = null, int? requiredWidth = null)
        {
            string url = "";

            string originalFileName = imageUrl.Split('/').Last();
            originalFileName = "/" + originalFileName;

            try
            {
                string phicalPath = ProductImageHelper.GetFullLocalFilePath(originalFileName, shopID);
                string relativePath = ProductImageHelper.GetRelativeImagePath(originalFileName, shopID);

                ImagesDownloader.DownloadAndRescaleImage(imageUrl, phicalPath, shopID, requiredHeight, requiredWidth);

                string localImage = relativePath;

                url = Storeya.Core.Helpers.ImagePathHelper.GetProductImageUrl(localImage);
            }
            catch
            {

            }

            return url;
        }

        private int GetRandomNumber()
        {
            Random random = new Random(DateTime.Now.Second);
            return random.Next(1, 100);
        }

        public string GetImageExtentionByPath(string path)
        {
            if (path.Contains(".png")) return "png";
            if (path.Contains(".jpg")) return "jpg";
            if (path.Contains(".jpeg")) return "jpg";
            if (path.Contains(".bmp")) return "bmp";
            if (path.Contains(".gif")) return "gif";
            return "jpg";
        }

        private static string GetMarketingImage(List<string> images, string logoImage, string domain)
        {
            string marketingImage = null;
            if (images != null)
            {
                marketingImage = images.Where(x => x != logoImage).FirstOrDefault();
            }

            if (string.IsNullOrEmpty(marketingImage))
            {
                marketingImage = images.FirstOrDefault();
            }

            marketingImage = FixImageIfNeeded(marketingImage, domain);

            return marketingImage;
        }

        public static string GetHomePageContent(string url)
        {
            string homePageHtml = null;
            try
            {
                homePageHtml = ShopifyCrawler.GetContent(url);
            }
            catch (Exception)
            {
                try
                {
                    homePageHtml = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
                }
                catch (Exception ex)
                {
                    throw new Exception("Failed to get url - " + url, ex);
                }
            }
            return homePageHtml;
        }

        public static string GetLogoImageUrl(string homePageHtml, string domain)
        {
            List<string> logo_src = null;

            try
            {
                logo_src = ShopifyApiProvider.ExtractLogoImages(homePageHtml);
            }
            catch (Exception)
            {
            }

            return ExtractLogoImageUrl(domain, logo_src);
        }

        public static string ExtractLogoImageUrl(string domain, List<string> logo_src)
        {
            List<string> validLogoSrces = null;

            if (logo_src != null && logo_src.Count > 0)
            {
                List<string> tempLogoSrces = new List<string>();
                foreach (var item in logo_src)
                {
                    string temp = FixImageIfNeeded(item, domain);
                    if (!tempLogoSrces.Contains(temp))
                        tempLogoSrces.Add(temp);
                }

                validLogoSrces = tempLogoSrces
                    .Where(x => x.ToLower().EndsWith(".png") || x.ToLower().EndsWith(".jpeg") || x.ToLower().EndsWith(".jpg")).ToList();
            }

            string logoImageUrl = null;
            if (validLogoSrces != null && validLogoSrces.Count > 0)
            {
                logoImageUrl = validLogoSrces.FirstOrDefault();
            }

            //if (!string.IsNullOrEmpty(logoImageUrl))
            //{
            //    logoImageUrl = FixImageIfNeeded(logoImageUrl, domain);
            //}

            return logoImageUrl;
        }
        public static List<string> FixHeadlines(List<string> headlines, int maxLength = 25)
        {
            List<string> fixedHeadlines = new List<string>();
            foreach (var headline in headlines)
            {
                string fixedHeadline = FixHeadline(headline, maxLength);
                fixedHeadlines.Add(fixedHeadline);
            }
            return fixedHeadlines;
        }
        public static string FixHeadline(string headline, int maxLength = 25)
        {
            string fixedHeadline = headline;
            try
            {
                fixedHeadline = RemoveNonStandardPunctuation(headline);
                // Maximum display width is 25 characters
                if (fixedHeadline.Contains(","))
                {
                    fixedHeadline = fixedHeadline.Replace(",", ", ");
                }
                if (!string.IsNullOrEmpty(fixedHeadline) && fixedHeadline.Length > maxLength)
                {
                    fixedHeadline = DecreaseToRequiredLengthNew(fixedHeadline, maxLength);
                    int emojiCharactersLength = fixedHeadline.EmojiCharactersLength();
                    if (emojiCharactersLength > 0)
                    {
                        fixedHeadline = DecreaseToRequiredLengthNew(fixedHeadline, maxLength - emojiCharactersLength);
                    }
                }

                if (!string.IsNullOrEmpty(fixedHeadline) && fixedHeadline.EndsWith("and"))
                {
                    fixedHeadline = fixedHeadline.Remove(fixedHeadline.Length - 3).TrimEnd();
                }

                if (!string.IsNullOrEmpty(fixedHeadline))
                {
                    fixedHeadline = fixedHeadline.Replace("::", ":");
                    fixedHeadline = fixedHeadline.Replace("---", "-");
                    fixedHeadline = fixedHeadline.Replace("•", " ");
                    fixedHeadline = fixedHeadline.Replace("â€", " ");
                    fixedHeadline = fixedHeadline.Replace("//", " ");
                    fixedHeadline = fixedHeadline.Replace("❤", " ");
                    fixedHeadline = fixedHeadline.Replace("✨", " ");
                    fixedHeadline = fixedHeadline.Replace("Ⓥ", " ");
                    fixedHeadline = fixedHeadline.Replace("✔", " ");
                    fixedHeadline = fixedHeadline.Replace("✅", " ");
                    fixedHeadline = fixedHeadline.Replace("✓", " ");
                    fixedHeadline = fixedHeadline.Replace("☞", ">");
                }
                if (!string.IsNullOrEmpty(fixedHeadline) && fixedHeadline.EndsWith("!"))
                {
                    fixedHeadline = fixedHeadline.Replace("!", "");
                }
                if (!string.IsNullOrEmpty(fixedHeadline) && fixedHeadline.EndsWith("&"))
                {
                    fixedHeadline = fixedHeadline.Remove(fixedHeadline.Length - 1).TrimEnd();
                }

                if (!string.IsNullOrEmpty(fixedHeadline) && fixedHeadline.EndsWith("-"))
                {
                    fixedHeadline = fixedHeadline.Remove(fixedHeadline.Length - 1).TrimEnd();
                }

                if (!string.IsNullOrEmpty(fixedHeadline) && !fixedHeadline.Contains("©"))
                {
                    if (fixedHeadline.Length == maxLength)
                    {
                        fixedHeadline = DecreaseToRequiredLengthNew(fixedHeadline, maxLength - 1);
                    }
                    if (fixedHeadline.EndsWith(","))
                    {
                        fixedHeadline = fixedHeadline.TrimEnd(new char[] { ',' });
                    }
                    fixedHeadline += "©";
                }

                fixedHeadline = FixCapitalization(fixedHeadline);

            }
            catch (Exception)
            {
            }
            return fixedHeadline;
        }

        private static string RemoveNonStandardPunctuation(string description)
        {
            /*  Exclamation mark in the ad's headline or more than one per ad
                Repeated punctuation or symbols (such as "!!!!free!!!")
                Characters that don't adhere to their true meaning (such as "4 sale" or "for $@le")
                Non-standard symbols (such as ~ and *) except in a company name or standard industry use (such as "5* hotel")  */

            if (string.IsNullOrEmpty(description))
                return null;

            string fixedDescription = description;
            fixedDescription = fixedDescription.Replace("â€", " ");
            fixedDescription = fixedDescription.Replace("//", " ");
            while (fixedDescription.Contains(".."))
            {
                fixedDescription = fixedDescription.Replace("..", ".");
            }

            while (fixedDescription.Contains("♀️"))
            {
                fixedDescription = fixedDescription.Replace("♀️", "");
            }

            while (fixedDescription.Contains("⚡️"))
            {
                fixedDescription = fixedDescription.Replace("⚡️", "");
            }

            while (fixedDescription.Contains("♥"))
            {
                fixedDescription = fixedDescription.Replace("♥", "");
            }

            while (fixedDescription.Contains("~"))
            {
                fixedDescription = fixedDescription.Replace("~", "");
            }

            while (fixedDescription.Contains("*") && !fixedDescription.Contains("hotel"))
            {
                fixedDescription = fixedDescription.Replace("*", "");
            }

            while (fixedDescription.Contains("|"))
            {
                fixedDescription = fixedDescription.Replace("|", "");
            }

            while (fixedDescription.Contains("†"))
            {
                fixedDescription = fixedDescription.Replace("†", "");
            }

            while (fixedDescription.Contains("!!"))
            {
                fixedDescription = fixedDescription.Replace("!!", "!");
            }

            while (fixedDescription.Contains("\r\n"))
            {
                fixedDescription = fixedDescription.Replace("\r\n", "");
            }

            if (fixedDescription.ToLower().Contains("wordpress"))
            {
                string[] splitted = fixedDescription.Split(' ');
                if (splitted.Length > 1)
                {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < splitted.Length; i++)
                    {
                        string word = splitted[i];
                        if (word.ToLower().Contains("wordpress"))
                        {
                            word = word.ToLower().Replace("wordpress", "");
                        }

                        if (!string.IsNullOrEmpty(word))
                            sb.Append(word).Append(" ");
                    }

                    fixedDescription = sb.ToString().Trim();
                }
                else
                {
                    fixedDescription = fixedDescription.ToLower().Replace("wordpress", "");
                }

            }

            while (fixedDescription.Contains("  "))
            {
                fixedDescription = fixedDescription.Replace("  ", " ");
            }
            fixedDescription = fixedDescription.Replace("  ", " ");
            return fixedDescription;
        }

        public static string FixCapitalization(string data)
        {
            string fixedData = data;

            if (CheckIfLongWordsInUpperCase(data))
            {
                string[] splitted = fixedData.Split(' ');
                if (splitted.Length > 0)
                {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < splitted.Length; i++)
                    {
                        string fixedWord = SetWord(splitted[i]);
                        sb.Append(fixedWord);
                        if (i < splitted.Length - 1)
                            sb.Append(" ");
                    }

                    fixedData = sb.ToString();
                }
                else
                {
                    fixedData = SetWord(fixedData);
                }
            }

            return fixedData;
        }
        public static bool CheckIfLongWordsInUpperCase(string text)
        {
            string[] words = text.Split(' ');
            foreach (var word in words)
            {
                if (word.Length < 4)
                {
                    continue;
                }
                if (word.ToUpper() == word)
                {
                    return true;
                }
            }
            return false;
        }
        private static string SetWord(string fixedData)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(fixedData[0]);

            for (int i = 1; i < fixedData.Length; i++)
            {
                char c = fixedData[i];
                if (char.IsUpper(c))
                {
                    c = char.ToLower(c);
                }

                sb.Append(c);
            }

            return sb.ToString();
        }

        private static string GetHeadline(string homePageHtml)
        {
            string headline = null;
            try
            {
                headline = AdwordsKeywordsHelper.ExtractTitleFromContent(homePageHtml);
                headline = HttpUtility.HtmlDecode(headline);
                headline = FixHeadline(headline);
            }
            catch (Exception)
            {
            }
            return headline;
        }
        public static List<string> FixDescriptions(List<string> descriptions, int maxLength = 90)
        {
            List<string> fixedDescriptions = new List<string>();
            foreach (var description in descriptions)
            {
                string fixedDescription = FixDescription(description, maxLength);
                fixedDescriptions.Add(fixedDescription);
            }
            return fixedDescriptions;
        }
        public static string FixDescription(string description, int maxLength = 90)
        {
            string fixedDescription = description;
            try
            {
                fixedDescription = fixedDescription.StripEmojiCharacters();
                fixedDescription = fixedDescription.RemoveReturningChar(',');
                fixedDescription = RemoveNonStandardPunctuation(fixedDescription);
                //Maximum display width is 90 characters.               
                if (!string.IsNullOrEmpty(fixedDescription))
                {

                    if (fixedDescription.Length > maxLength)
                    {
                        fixedDescription = DecreaseToRequiredLengthNew(fixedDescription, maxLength);
                    }
                    if (fixedDescription.EndsWith("and"))
                    {
                        fixedDescription = fixedDescription.Remove(fixedDescription.Length - 3).TrimEnd();
                    }

                    if (fixedDescription.EndsWith("&"))
                    {
                        fixedDescription = fixedDescription.Remove(fixedDescription.Length - 1).TrimEnd();
                    }

                    if (fixedDescription.EndsWith("-"))
                    {
                        fixedDescription = fixedDescription.Remove(fixedDescription.Length - 1).TrimEnd();
                    }
                    if (fixedDescription.EndsWith("!"))
                    {
                        fixedDescription = fixedDescription.Replace("!", "") + "!";
                    }
                    fixedDescription = fixedDescription.Replace("::", ":");
                    fixedDescription = fixedDescription.Replace("✅", "");
                    fixedDescription = fixedDescription.Replace("✓", "");
                    fixedDescription = fixedDescription.Replace("☞", ">");
                    fixedDescription = fixedDescription.Replace("❤", " ");
                    fixedDescription = fixedDescription.Replace("✨", " ");
                    fixedDescription = fixedDescription.Replace("Ⓥ", " ");
                    fixedDescription = fixedDescription.Replace("✔", " ");
                    fixedDescription = fixedDescription.Replace("•", " ");

                    fixedDescription = FixCapitalization(fixedDescription);
                }
                fixedDescription = fixedDescription.Trim();
                fixedDescription = fixedDescription.Replace(" ‍ ", " ");
            }
            catch { }
            return fixedDescription;
        }

        public static string GetDescription(string homePageHtml)
        {
            string description = null;
            try
            {
                description = AdwordsKeywordsHelper.ExtractDescriptionFromContent(homePageHtml);
                description = HttpUtility.HtmlDecode(description);
                description = FixDescription(description);
            }
            catch { }

            return description;
        }

        public static string DecreaseToRequiredLengthNew(string description, int maxLength)
        {
            string new_description = description;

            if (new_description.Length > maxLength)
            {
                if (new_description.Contains("!"))
                    new_description = new_description.Replace("!", ".");

                if (new_description.Contains(". "))
                {
                    string[] separators = new string[] { ". " };
                    string[] sentences = new_description.Split(separators, StringSplitOptions.RemoveEmptyEntries);

                    for (int i = sentences.Length - 1; i >= 0; i--)
                    {
                        if (new_description.Length <= maxLength)
                            break;

                        if (i == 0)
                        {
                            while (new_description.Length > maxLength)
                            {
                                int index1 = new_description.LastIndexOf(' ');
                                if (index1 != -1)
                                {
                                    new_description = new_description.Remove(index1);
                                }
                                else
                                {
                                    int index2 = new_description.LastIndexOf('.');
                                    if (index2 != -1)
                                    {
                                        new_description = new_description.Remove(index2);
                                    }
                                    else
                                    {
                                        new_description = new_description.Remove(25);
                                    }
                                }
                            }
                        }
                        else
                            new_description = new_description.Replace(sentences[i] + ". ", "");
                    }
                }
                else
                {
                    while (new_description.Length > maxLength)
                    {
                        int index1 = new_description.LastIndexOf(' ');
                        if (index1 != -1)
                        {
                            new_description = new_description.Remove(index1);
                        }
                        else
                        {
                            int index2 = new_description.LastIndexOf('.');
                            if (index2 != -1)
                            {
                                new_description = new_description.Remove(index2);
                            }
                            else
                            {
                                new_description = new_description.Remove(25);
                            }
                        }
                    }
                }

            }

            return new_description;
        }

        private static string DecreaseToRequiredLength(string description, int maxLength)
        {
            string[] separators = new string[] { ",", ".", "!", "\'", " ", "\'s" };
            string[] words = description.Split(separators, StringSplitOptions.RemoveEmptyEntries);

            string new_description = "";
            for (int i = 0; i < words.Length; i++)
            {
                int charsToAdd = words[i].Length + 1; // 1 for a space
                if (new_description.Length + charsToAdd <= maxLength)
                {
                    if (i != 0)
                        new_description = new_description + " ";

                    new_description = new_description + words[i];
                }
                else
                {
                    break;
                }
            }
            return new_description;
        }

        private static string FixToRequiredLength(string data, int requiredLength)
        {
            string fixedData = data;
            if (!string.IsNullOrEmpty(data) && data.Length > requiredLength)
            {
                fixedData = data.Substring(0, requiredLength);
            }
            return fixedData;
        }

        public static List<string> GetHpImages(string domain, string homePageHtml)
        {
            List<string> images = new List<string>();
            try
            {
                Regex regxForImageTag = new Regex("(?<=<img.*?src=\")[^\"]*", RegexOptions.Singleline); //RegexOptions.IgnoreCase
                foreach (Match item in regxForImageTag.Matches(homePageHtml))
                {
                    string image = item.Value;
                    string productImage = ExtractProductImage(domain, image);

                    if (!string.IsNullOrEmpty(productImage) && images.All(i => i != productImage))
                    {
                        images.Add(productImage);
                    }
                }
            }
            catch (Exception)
            {
            }
            return images;
        }

        public static List<string> GetProductImages(string domain, List<string> hpImages, string defaultProductImage = null)
        {
            List<string> images = new List<string>();
            if (hpImages != null)
            {
                try
                {
                    foreach (var image in hpImages)
                    {
                        string productImage = ExtractProductImage(domain, image);
                        if (!string.IsNullOrEmpty(defaultProductImage))
                        {
                            productImage = defaultProductImage;
                        }
                        if (!string.IsNullOrEmpty(productImage) && images.All(i => i != productImage))
                        {
                            images.Add(productImage);
                        }
                    }
                }
                catch (Exception)
                {
                }
            }

            return images;
        }

        private static string ExtractProductImage(string domain, string image)
        {
            string fixedImage = null;
            if (!string.IsNullOrEmpty(image)
                && (image.Contains(domain)
                    || image.Contains("cdn.shopify.com/s/files")
                    || image.Contains("cdn2.shopify.com/s/files")
                    || image.Contains("img1.wsimg.com")
                    || (image.Contains(".bigcommerce.com") && !image.Contains(".js") && !image.Contains(".svg")))
                && !image.ToLower().Contains("logo"))
            {
                fixedImage = FixImageIfNeeded(image, domain);
            }
            return fixedImage;
        }

        private static string FixImageIfNeeded(string image, string domain)
        {
            string fixedImage = image;
            if (!string.IsNullOrEmpty(fixedImage))
            {
                if (fixedImage.Contains("?"))
                {
                    int index = fixedImage.IndexOf("?");
                    fixedImage = fixedImage.Remove(index);
                }

                if (fixedImage.StartsWith("//cdn.shopify.com"))
                {
                    fixedImage = fixedImage.Replace("//cdn.shopify.com", "https://cdn.shopify.com");
                }
                else if (fixedImage.StartsWith("//cdn2.shopify.com"))
                {
                    fixedImage = fixedImage.Replace("//cdn2.shopify.com", "https://cdn2.shopify.com");
                }
                else if (fixedImage.StartsWith("//img1.wsimg.com"))
                {
                    fixedImage = fixedImage.Replace("//img1.wsimg.com", "https://img1.wsimg.com");

                    fixedImage = CleanImageUrl(fixedImage, ".jpe");
                    fixedImage = CleanImageUrl(fixedImage, ".jpg");
                    fixedImage = CleanImageUrl(fixedImage, ".png");
                }
                else if (!fixedImage.StartsWith("http"))
                {
                    if (!fixedImage.Contains(domain))
                    {
                        fixedImage = "https://" + domain.Trim('/') + "/" + fixedImage.Trim('/');
                    }
                    else
                    {
                        fixedImage = "https://" + fixedImage.Trim('/');
                    }
                }
            }

            return fixedImage;
        }

        private static string CleanImageUrl(string fixedImage, string format)
        {
            string cleaned = fixedImage;

            if (cleaned.Contains(format + "/"))
            {
                int index = cleaned.IndexOf(format + "/", StringComparison.CurrentCulture);
                if (index > 0)
                {
                    cleaned = cleaned.Remove(index) + format;
                }
            }

            return cleaned;
        }


        public static int? GetShopPlatform(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(x => x.ID == shopID).Single();
            return shop.CatalogSourcePlatform;
        }

        private static ShopifyConnectedShop GetShopifyConnectedShop(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            ShopifyConnectedShop connectedShop = null;
            List<ShopifyConnectedShop> connectedShops = db.ShopifyConnectedShops.Where(
                    cs => cs.ShopID == shopID
                    && cs.PermissionsScope == 1).ToList();

            if (connectedShops.Count > 0)
            {
                if (connectedShops.Where(x => x.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster).Any())
                {
                    connectedShop = connectedShops.Where(x => x.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster).Single();
                }
                else
                {
                    connectedShop = connectedShops.First();
                }
            }
            return connectedShop;
        }

        private static bool isValidCampaign(DsaCampaign c)
        {
            return (c.Status != "ERROR");
        }


        public static DsaCampaign GetDsaCampaign(int shopID, bool useFirstUrlAsIs = false)
        {
            TrafficBooster tb_settings = TrafficBoostersDbHelper.GetSettings(shopID);
            return GetDsaCampaign(tb_settings, useFirstUrlAsIs, true);
        }

        public static DsaCampaign GetDsaCampaign(TbCampaign campaignSettings, bool useFirstUrlAsIs = false, bool findLanguage = true, string nameSuffix = "Dynamic", bool useFullUrlForName = false, bool useSubDomain = false)
        {
            TrafficBooster tb_settings = TrafficBoostersDbHelper.GetSettings(campaignSettings.ShopID.Value);
            //int? catalogSourcePlatform = TrafficBoostersDbHelper.GetShopCatalogSoursePlatform(campaignSettings.ShopID.Value);
            return GetDsaCampaign(tb_settings, useFirstUrlAsIs, findLanguage, nameSuffix: nameSuffix, useFullUrlForName: useFullUrlForName, useSubDomain: useSubDomain);
        }

        public static DsaCampaign GetDsaCampaign(TrafficBooster tb_settings, bool useFirstUrlAsIs = false, bool findLanguage = true, List<string> bestSellerUrls = null, string nameSuffix = "Dynamic", bool useFullUrlForName = false, bool useSubDomain = false)
        {
            string finalUrl = null;

            DsaCampaign campaign = null;

            if (TbSettingsHelper.IsDefaultBudgetCanceled(tb_settings))
            {
                //TB app already cancelled
                campaign = new DsaCampaign();
                campaign.Status = "ERROR";
                campaign.CreationError = "TB app already cancelled";
                return campaign;
            }

            string campaigUrl = null;
            if (ShopAttributesManager.GetInstance(tb_settings.ShopID.Value).IsExists(Attributes.Accounts.Adwords.UseFirstUrlAsIs))
            {
                useFirstUrlAsIs = true;
            }
            if (useFirstUrlAsIs)
            {
                Uri uri = new Uri(tb_settings.Url1);
                finalUrl = tb_settings.Url1;
                if (useFullUrlForName)
                {
                    campaigUrl = finalUrl;
                }
                else
                {
                    campaigUrl = uri.Host;
                }
            }
            else
            {
                bool useBingValidation = true;

                campaigUrl = GetMainUrl(tb_settings);
                Console.WriteLine("Get domains for - " + campaigUrl);

                UrlResponse site_url_check_response = MarketplaceUrlHelper.GetSiteUrl(campaigUrl, false, tb_settings.ShopID);
                if (site_url_check_response.IsAffiliateUrl)
                {
                    campaign = new DsaCampaign();
                    campaign.Status = "ERROR";
                    campaign.CreationError = string.Format("The campaign url {0} was checked and found as affiliate for {1}.", campaigUrl, site_url_check_response.URL);
                    return campaign;
                }
                else if (campaigUrl != site_url_check_response.URL)
                {
                    if (campaigUrl.Contains("www") && !site_url_check_response.URL.Contains("www"))
                    {
                        useBingValidation = false;
                    }

                    campaigUrl = site_url_check_response.URL;
                    finalUrl = site_url_check_response.URL;
                }
                else
                {
                    finalUrl = campaigUrl;
                }

                if (!string.IsNullOrEmpty(campaigUrl))
                {


                    campaigUrl = GetDomain(campaigUrl, useBingValidation);

                }
                else
                {
                    campaign = new DsaCampaign();
                    campaign.Status = "ERROR";
                    campaign.CreationError = "No campaigUrl was found for current TB.";
                    return campaign;
                }
            }

            if (!string.IsNullOrEmpty(campaigUrl))
            {
                var isService = tb_settings.IsServiceProvider == (int)BusinessTypes.MyServices;
                if (bestSellerUrls == null)
                {
                    campaign = CreateCampaign(campaigUrl, 1, tb_settings, isService, finalUrl, findLanguage, nameSuffix);
                    campaign.UrlSuffix_Campaign = "storeya10";
                }
                else
                {
                    campaign = CreateDynamicBestSellerCampaign(campaigUrl, 1, tb_settings, isService, finalUrl, findLanguage, bestSellerUrls);
                    campaign.UrlSuffix_Campaign = "storeya10c";

                }

                if (campaign == null)
                {
                    campaign = new DsaCampaign();
                    campaign.Status = "ERROR";
                }
                else
                {
                    //if (tb_settings.PrimaryTrafficMethod == (int)PrimaryTrafficMethods.OnlyCalls ||
                    //tb_settings.PrimaryTrafficMethod == (int)PrimaryTrafficMethods.AdWordsAndCalls)
                    //{
                    //    campaign.AccountHasCallCampaign = true;
                    //}

                    if (tb_settings.IsServiceProvider == (int)BusinessTypes.MyServices)
                    {
                        campaign.IsServiceProvider = (int)BusinessTypes.MyServices;
                        campaign.ServiceLocation = new ServiceLocation();
                        campaign.ServiceLocation.Radius = tb_settings.CallRadius ?? 0;
                        campaign.ServiceLocation.RadiusUnits = tb_settings.CallRadiusUnits ?? 0;
                        campaign.ServiceLocation.ZipCode = tb_settings.CallZipCode;
                    }
                }
            }

            return campaign;
        }

        public static SearchAdWordsCampaign GetBrandedSearchAdWordsCampaignFromDSA(DsaCampaign dsaCampaign, int shopID, SearchCampaignType searchCampaignType)
        {
            string nameSuffix = "Search B";
            switch (searchCampaignType)
            {
                case SearchCampaignType.Search_B_Broad:
                    nameSuffix = "Search B Broad";
                    break;
                default:
                    break;
            }

            Shop shop = ShopsHelper.GetShop(shopID);
            TrafficBooster tbSettings = TrafficBoostersDbHelper.GetSettings(shopID);
            List<string> branded_keywords = new List<string>();
            try
            {
                branded_keywords = AdwordsKeywordsHelper.GetBrandedKeywordsFromTitleAndHtml(dsaCampaign.FinalUrl);
                if (!string.IsNullOrEmpty(shop.Name))
                {
                    if (!branded_keywords.Contains(shop.Name))
                    {
                        branded_keywords.Add(shop.Name);
                    }
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.ErrorWithDB($"Failed to get branded keywords for {dsaCampaign.FinalUrl}.", ex, shopID);
            }

            SearchAdWordsCampaign c = SetSearchCampaign(dsaCampaign, tbSettings, $"_{nameSuffix}", searchCampaignType);
            c.Name = "-" + c.Name;
            c.MobileBidModifier = 1; //allow mobile traffic for Branded campaigns           
            SearchAdGroup adGroup = new SearchAdGroup();
            adGroup.AdGroupName = $"{nameSuffix} Ad group";
            adGroup.Headline1 = tbSettings.SearchAdHeadline1;
            adGroup.Headline2 = tbSettings.SearchAdHeadline2;
            adGroup.Description = tbSettings.SearchAdDescription;
            adGroup.HomepageUrl = dsaCampaign.HomepageUrl;
            adGroup.Keywords = branded_keywords;
            c.AdGroups.Add(adGroup);

            return c;
        }

        public static SearchAdWordsCampaign GetRLSA_B_AdWordsCampaignFromDSA(DsaCampaign dsaCampaign, int shopID)
        {
            Console.WriteLine("GetRLSA_B_AdWordsCampaignFromDSA: Start");
            TrafficBooster tbSettings = TrafficBoostersDbHelper.GetSettings(shopID);
            List<string> branded_keywords = new List<string>();
            try
            {
                branded_keywords = AdwordsKeywordsHelper.GetBrandedKeywordsFromTitleAndHtml(dsaCampaign.FinalUrl);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to get branded keywords for {0}.", dsaCampaign.FinalUrl), ex, shopID);
            }

            SearchAdWordsCampaign c = SetSearchCampaign(dsaCampaign, tbSettings, "_RLSA B", SearchCampaignType.RLSA_B);

            ////read custom copies from the database
            //var customRlsaData = LoadFromAdCopiesTable(shopID, c.RSAAdCategoryData);
            //if (customRlsaData != null)
            //{
            //    c.RSAAdCategoryData = customRlsaData;
            //}

            c.Name = "-" + c.Name;

            //SearchAdGroup adGroup = LoadFromAdCopiesTable(shopID);
            SearchAdGroup adGroup = new SearchAdGroup();
            adGroup.AdGroupName = "RLSA lists Search B";
            adGroup.Headline1 = tbSettings.SearchAdHeadline1;
            adGroup.Headline2 = tbSettings.SearchAdHeadline2;
            adGroup.Description = tbSettings.SearchAdDescription;
            adGroup.HomepageUrl = dsaCampaign.HomepageUrl;
            adGroup.Keywords = branded_keywords;
            c.AdGroups.Add(adGroup);
            Console.WriteLine("GetRLSA_B_AdWordsCampaignFromDSA: End");
            return c;
        }

        public static RSAAdsData LoadFromAdCopiesTable(int shopID, RSAAdsData defautData)
        {
            var db = DataHelper.GetStoreYaEntities();
            var copies = db.AdCopies.Where(c => c.ShopID == shopID).OrderBy(c => c.OrderIndex).Select(c => c).ToList();
            if (copies != null)
            {
                var headers = copies.Where(c => c.CopyType == (int)CopyType.Header).ToArray();
                var descriptions = copies.Where(c => c.CopyType == (int)CopyType.Description).ToArray();

                if (headers.Length == 0 || descriptions.Length == 0)
                {
                    return null;
                }
                var uniqueHeaders = GetUniqueHeadersOrDescriptions(defautData, headers, 10, "Headline");
                var uniqueDescriptions = GetUniqueHeadersOrDescriptions(defautData, descriptions, 4, "Description");
                defautData.Headline1 = uniqueHeaders.ElementAtOrDefault(0);
                defautData.Headline2 = uniqueHeaders.ElementAtOrDefault(1);
                defautData.Headline3 = uniqueHeaders.ElementAtOrDefault(2);
                defautData.Headline4 = uniqueHeaders.ElementAtOrDefault(3);
                defautData.Headline5 = uniqueHeaders.ElementAtOrDefault(4);
                defautData.Headline6 = uniqueHeaders.ElementAtOrDefault(5);
                defautData.Headline7 = uniqueHeaders.ElementAtOrDefault(6);
                defautData.Headline8 = uniqueHeaders.ElementAtOrDefault(7);
                defautData.Headline9 = uniqueHeaders.ElementAtOrDefault(8);
                defautData.Headline10 = uniqueHeaders.ElementAtOrDefault(9);

                defautData.Description1 = uniqueDescriptions.ElementAtOrDefault(0);
                defautData.Description2 = uniqueDescriptions.ElementAtOrDefault(1);
                defautData.Description3 = uniqueDescriptions.ElementAtOrDefault(2);
                defautData.Description4 = uniqueDescriptions.ElementAtOrDefault(3);
                if (uniqueHeaders.Count < 10 && uniqueDescriptions.Count < 4)
                {
                    string link = "ShopID=" + EmailHelper.AdminLinkHref(shopID);
                    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "RLSA campaign creation", $"There was a problem creating headers and descriptions. There were {uniqueDescriptions.Count} descriptions (should be 4) and {uniqueHeaders.Count} headers (should be 10) created. Please check Adcopies and GetRSAdsByCategory function. {link}");
                }
                return defautData;
            }

            return null;
        }
        public static List<string> GetUniqueHeadersOrDescriptions(RSAAdsData defautData, AdCopy[] values, int amount, string type)
        {
            List<string> textValues = values.Select(x => x.Text).Distinct().ToList();

            Type defaultDataType = defautData.GetType();
            PropertyInfo[] properties = defaultDataType.GetProperties();
            foreach (PropertyInfo property in properties)
            {
                string propertyName = property.Name;
                if (propertyName.Contains(type)) // check if it's header or description
                {
                    string propertyValue = property.GetValue(defautData, null)?.ToString();
                    if (!textValues.Contains(propertyValue)) // if it's unique default value 
                    {
                        textValues.Add(propertyValue);
                    }
                }
            }
            return textValues.Take(amount).ToList();

        }


        public static SearchAdWordsCampaign SetSearchCampaign(DsaCampaign dsaCampaign, TrafficBooster tbSettings, string name, SearchCampaignType searchCampaignType)
        {
            TbCategoriesHelper.Category category = TbCategoriesHelper.GetCategory(tbSettings);
            string domain = AdwordsKeywordsHelper.ExtractSiteAdNameFromTitle(dsaCampaign.FinalUrl);
            SearchAdWordsCampaign c = new SearchAdWordsCampaign(searchCampaignType);

            bool useAdCopiesIfExist = true;
            //if (searchCampaignType != SearchCampaignType.Search_B && searchCampaignType != SearchCampaignType.Search)
            //{
            //    useAdCopiesIfExist = true;
            //}
            c.RSAAdCategoryData = AdCreatorHelper.GetAdDTOForSearchAndRLSACampaigns(tbSettings, domain, useAdCopiesIfExist: useAdCopiesIfExist);
            if (useAdCopiesIfExist && c.RSAAdCategoryData.DefaultAds)
            {
                var t = $"The {searchCampaignType.ToString()} campaign was created using default ads. Try creating ad group again after extracting ads on the <a href='https://bo.storeya.com/shop/adextractor/{tbSettings.ShopID.Value}'>copyExtractor</a> page";
                TbInternalTasksManager.AddIfNew(tbSettings.ShopID.Value, TbInternalTasksCategories.General, "Campaign created with default ads instead of custom ads.", t, Attributes.GoogleAds.SearchCampaign);
            }
            c.Name = dsaCampaign.Name.Replace("_Dynamic", name);
            c.AllLocationCodes = string.Join(",", dsaCampaign.LocationsCodes);
            c.AllLanguages = dsaCampaign.AllLanguages;
            c.HomepageUrl = dsaCampaign.HomepageUrl;
            c.MobileBidModifier = dsaCampaign.MobileBidModifier;
            c.IsServiceProvider = dsaCampaign.IsServiceProvider;
            c.ServiceLocation = dsaCampaign.ServiceLocation;
            c.TrafficCategoryID = dsaCampaign.TrafficCategoryID;
            c.CampaignDailyBudget = dsaCampaign.CampaignDailyBudget;
            c.FinalUrl = dsaCampaign.FinalUrl;
            return c;
        }





        public static SearchAdWordsCampaign GetSearchAdWordsCampaignFromDSA(DsaCampaign dsaCampaign, int shopID, SearchCampaignType searchCampaignType)
        {
            TrafficBooster tbSettings = TrafficBoostersDbHelper.GetSettings(shopID);

            SearchAdWordsCampaign c = SetSearchCampaign(dsaCampaign, tbSettings, "_Search", searchCampaignType);

            var adGroup = new SearchAdGroup();
            adGroup.AdGroupName = "Search AdGroup A";
            adGroup.Headline1 = tbSettings.SearchAdHeadline1;
            adGroup.Headline2 = tbSettings.SearchAdHeadline2;
            adGroup.Description = tbSettings.SearchAdDescription;
            adGroup.Keywords = AdwordsKeywordsHelper.ToKeywordsList(tbSettings.CallKeywords);

            c.AdGroups.Add(adGroup);

            return c;
        }

        public static string GetMainUrl(TrafficBooster tb_settings)
        {
            List<TBLink> tbLinks = FillTbLinksList(tb_settings);
            tbLinks = SetPriority(tbLinks);
            string campaigUrl = SetCampaignUrl(tbLinks);
            return campaigUrl;
        }

        private static string SetCampaignUrl(List<TBLink> tbLinks)
        {
            string campaigUrl = null;
            if (tbLinks.Where(l => l.Priority == (int)LinkPriority.High).Any())
            {
                campaigUrl = tbLinks.Where(l => l.Priority == (int)LinkPriority.High).First().Link;
            }
            else if (tbLinks.Where(l => l.Priority == (int)LinkPriority.Medium).Any())
            {
                campaigUrl = tbLinks.Where(l => l.Priority == (int)LinkPriority.Medium).First().Link;
            }
            else if (tbLinks.Where(l => l.Priority == (int)LinkPriority.Low).Any())
            {
                campaigUrl = tbLinks.Where(l => l.Priority == (int)LinkPriority.Low).First().Link;
            }
            return campaigUrl;
        }

        private static List<TBLink> SetPriority(List<TBLink> tbLinks)
        {
            foreach (var link in tbLinks)
            {
                if (MarketplaceUrlHelper.IsMarketplace(link.Link))
                {
                    if (link.Link.Contains("etsy.com") || link.Link.Contains("myshopify.com"))
                    {
                        link.Priority = (int)LinkPriority.Medium;
                    }
                    else
                    {
                        link.Priority = (int)LinkPriority.Low;
                    }
                }
                else
                {
                    link.Priority = (int)LinkPriority.High;
                }
            }

            return tbLinks;
        }

        private static List<TBLink> FillTbLinksList(TrafficBooster tb_settings)
        {
            List<TBLink> tbLinks = new List<TBLink>();
            if (!string.IsNullOrEmpty(tb_settings.Url1))
            {
                tbLinks.Add(new TBLink() { Link = tb_settings.Url1 });
            }

            if (!string.IsNullOrEmpty(tb_settings.Url2))
            {
                tbLinks.Add(new TBLink() { Link = tb_settings.Url2 });
            }

            if (!string.IsNullOrEmpty(tb_settings.Url3))
            {
                tbLinks.Add(new TBLink() { Link = tb_settings.Url3 });
            }

            if (!string.IsNullOrEmpty(tb_settings.Url4))
            {
                tbLinks.Add(new TBLink() { Link = tb_settings.Url4 });
            }

            return tbLinks;
        }


        public static DsaCampaign CreateDynamicBestSellerCampaign(string shopUrl, int index, TrafficBooster tb_settings, bool isServiceProvider, string finalUrl = null, bool findLanguage = true, List<string> adGroupBSUrls = null)
        {
            //campaign = CreateDynamicBestSellerCampaign(campaigUrl, 1, tb_settings, tb_settings.ShopID.Value, tb_settings.Country, (tb_settings.PurchasedAmount ?? 0), isService, (tb_settings.PaymentSystem ?? (int)TrafficBoostersDbHelper.GetDefaultPaymentSystem()), tb_settings.TrafficCategoryID, finalUrl, findLanguage, bestSellerUrls);
            int shopID = tb_settings.ShopID.Value;
            string countryCodes = tb_settings.Country;
            long impressions = (tb_settings.PurchasedAmount ?? 0);
            int? paymentSystem = (tb_settings.PaymentSystem ?? (int)TrafficBoostersDbHelper.GetDefaultPaymentSystem());
            int? trafficCategoryID = tb_settings.TrafficCategoryID;
            DsaCampaign campaign = null;
            try
            {
                bool isEtsy = false;
                if (ProviderFactory.IsUrlEtsy(shopUrl))
                {
                    isEtsy = true;
                }

                campaign = new DsaCampaign();
                campaign.TrafficCategoryID = trafficCategoryID;

                if (shopUrl.Contains("BAD_FORMATTED"))
                {
                    if (shopUrl.Contains("_ERROR_BAD_FORMATTED_NO_INDEXED_PAGES"))
                    {
                        campaign.CreationWarning = "No indexed pages in Bing.com";
                        shopUrl = shopUrl.Replace("_ERROR_BAD_FORMATTED_NO_INDEXED_PAGES", "");
                    }
                    else
                    {
                        Log4NetLogger.Error(string.Format("Failed to create a campaign for {0}.", shopUrl), shopID);
                        campaign.Status = "ERROR";
                        campaign.Name = shopUrl;
                        return campaign;
                    }
                }

                List<string> country_codes = GetCountryCodes(countryCodes);
                campaign.AllLocationCodes = countryCodes;
                campaign.CountriesOriginSettings = countryCodes;
                campaign.LocationsCodes = country_codes;

                //campaign.Name = GetCampaignName(shopID, shopUrl, country_codes, impressions);
                campaign.Name = AdWordsCampaignFactory.CombineCampaignName(shopID, shopUrl, country_codes, impressions, "Dynamic", paymentSystem) + " BS";
                campaign.HomepageUrl = shopUrl;
                campaign.FinalUrl = finalUrl;
                campaign.Locations = GetCountriesNames(country_codes);



                // adGroup.AdGroupAds = new List<AdGroupAd>();
                string language = "en";
                if (findLanguage)
                {
                    //TODO: check if we need this
                    //language = SetLanguage(finalUrl, country_codes, shopID);
                }
                List<AdGroupCriteria> cretiteriasList = new List<AdGroupCriteria>();
                campaign.CampaignDailyBudget = GetInitBudjetByPlanAndPaymentSystem(impressions, paymentSystem);
                string dispayUrl = null;
                if (MarketplaceUrlHelper.IsMarketplace(shopUrl))
                {
                    var urls = MarketplaceUrlHelper.GetDsaUrls(shopUrl);

                    campaign.DSAWebsite = urls.Website;
                    dispayUrl = urls.Display;  //adGroupAd.DisplayURL = urls.Display;
                }
                else if (MarketplaceUrlHelper.IsSubdomain(shopUrl))
                {
                    campaign.DSAWebsite = MarketplaceUrlHelper.GetTopDomainOfSubdomain(shopUrl);
                    dispayUrl = shopUrl; // adGroupAd.DisplayURL = shopUrl;                    
                    campaign.CreationWarning = "Subdomain used";
                }
                else
                {
                    campaign.DSAWebsite = shopUrl;
                    dispayUrl = ForceWWW(shopUrl); //adGroupAd.DisplayURL = ForceWWW(shopUrl);                    
                }
                // campaign.DSAWebsite = shopUrl;
                string domain = UrlPathHelper.GetDomainName(shopUrl);
                if (domain.Contains("www."))
                    domain = domain.Replace("www.", "");

                if (domain.Length > 20 && domain.Contains("."))
                {
                    int index_0 = domain.IndexOf('.');
                    domain = domain.Remove(index_0);
                }

                if (isEtsy)
                {
                    campaign.DSAWebsite = "www.Etsy.com";
                    dispayUrl = "www.Etsy.com"; //adGroupAd.DisplayURL = "www.Etsy.com";                  
                    cretiteriasList = CreateAdGroupCriteriasForEtsy(shopUrl, shopID);
                }
                else
                {
                    campaign.AdGroups = new List<AdGroup>();
                    //Create Ad Groups Base on Bert Seller Urls from GA
                    foreach (string url in adGroupBSUrls)
                    {

                        AdGroup adGroup = new AdGroup();
                        adGroup.Name = url;
                        cretiteriasList = new List<AdGroupCriteria>();
                        AdGroupCriteria adGroupCriteria = new AdGroupCriteria();
                        adGroupCriteria.DynamicAdTargetValue1 = url;
                        cretiteriasList.Add(adGroupCriteria);


                        List<AdGroupAd> adGroupAds = AdGroupAdManager.SetDsaAdGroupAds(tb_settings, fillByAdcopiesIfExist: true);
                        foreach (AdGroupAd adGroupAd in adGroupAds)
                        {
                            adGroupAd.DisplayURL = url;
                            adGroupAd.AdGroupCriterias = cretiteriasList;
                        }

                        adGroup.AdGroupAds = adGroupAds;
                        campaign.AdGroups.Add(adGroup);
                    }
                }
                campaign.DSALanguage = language;
                campaign.AllLanguages = language;


            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to create a campaign for {0}.", shopUrl), ex, shopID);
                campaign.Status = "ERROR";
                campaign.Name += ex.Message;
            }
            return campaign;
        }

        private static List<AdGroupCriteria> CreateAdGroupCriteriaForUrl(string url)
        {
            List<AdGroupCriteria> cretiteriasList = new List<AdGroupCriteria>();
            AdGroupCriteria adGroupCriteria = new AdGroupCriteria()
            {
                DynamicAdTargetValue1 = url,
            };
            cretiteriasList.Add(adGroupCriteria);
            return cretiteriasList;
        }

        public static DsaCampaign CreateCampaign(string shopUrl, int index, TrafficBooster tb_settings, bool isServiceProvider, string finalUrl = null, bool findLanguage = true, string nameSuffix = "Dynamic")
        {
            int shopID = tb_settings.ShopID.Value;
            string countryCodes = tb_settings.Country;
            long impressions = (tb_settings.PurchasedAmount ?? 0);
            int? paymentSystem = (tb_settings.PaymentSystem ?? (int)TrafficBoostersDbHelper.GetDefaultPaymentSystem());
            int? trafficCategoryID = tb_settings.TrafficCategoryID;

            DsaCampaign campaign = null;
            try
            {
                bool isEtsy = false;
                //bool isZap = false;
                if (ProviderFactory.IsUrlEtsy(shopUrl)) //Etsy not in use
                {
                    isEtsy = true;
                }
                //if (shopUrl.ToLower().Contains(".d.co.il"))
                //{
                //    isZap = true;
                //}
                campaign = new DsaCampaign();
                campaign.TrafficCategoryID = trafficCategoryID;

                if (shopUrl.Contains("BAD_FORMATTED"))
                {
                    if (shopUrl.Contains("_ERROR_BAD_FORMATTED_NO_INDEXED_PAGES"))
                    {
                        campaign.CreationWarning = "No indexed pages in Bing.com";
                        shopUrl = shopUrl.Replace("_ERROR_BAD_FORMATTED_NO_INDEXED_PAGES", "");
                    }
                    else
                    {
                        Log4NetLogger.Error(string.Format("Failed to create a campaign for {0}.", shopUrl), shopID);
                        campaign.Status = "ERROR";
                        campaign.Name = shopUrl;
                        return campaign;
                    }
                }

                List<string> country_codes = GetCountryCodes(countryCodes);
                campaign.AllLocationCodes = countryCodes;
                campaign.CountriesOriginSettings = countryCodes;
                campaign.LocationsCodes = country_codes;

                //campaign.Name = GetCampaignName(shopID, shopUrl, country_codes, impressions);
                campaign.Name = AdWordsCampaignFactory.CombineCampaignName(shopID, shopUrl, country_codes, impressions, nameSuffix, paymentSystem);

                campaign.HomepageUrl = shopUrl;
                campaign.FinalUrl = finalUrl;

                campaign.Locations = GetCountriesNames(country_codes);

                campaign.AdGroups = new List<AdGroup>();

                AdGroup adGroup = new AdGroup();
                adGroup.Name = "Dynamic Ad " + index; // Product name (will serve us as the ad group name; if hard, just put 1,2,3 per each campaign)

                // adGroup.AdGroupAds = new List<AdGroupAd>();
                string language = "en";
                if (findLanguage)
                {

                    language = SetLanguage(country_codes);
                }

                List<AdGroupCriteria> cretiteriasList = new List<AdGroupCriteria>();

                //campaign.CampaignDailyBudget = GetInitBudjetByPlan(impressions);
                campaign.CampaignDailyBudget = GetInitBudjetByPlanAndPaymentSystem(impressions, paymentSystem);

                string dispayUrl = null;

                if (isEtsy)
                {
                    //disabled since we have an issue with API
                    campaign.DSAWebsite = "www.Etsy.com";
                    dispayUrl = "www.Etsy.com"; //adGroupAd.DisplayURL = "www.Etsy.com";                  
                    cretiteriasList = CreateAdGroupCriteriasForEtsy(shopUrl, shopID);
                }
                else
                {
                    AdGroupCriteria adGroupCriteria = new AdGroupCriteria();
                    //if (isZap)
                    //{
                    //    campaign.DSAWebsite = "www.d.co.il";
                    //    dispayUrl = shopUrl.Replace("https://", ""); // adGroupAd.DisplayURL = shopUrl;
                    //    adGroupCriteria.DynamicAdTargetValue1 = shopUrl.Replace("https://","");
                    //    //campaign.CreationWarning = "Zap Subdomain used";
                    //}
                    //else 
                    if (MarketplaceUrlHelper.IsMarketplace(shopUrl))
                    {
                        var urls = MarketplaceUrlHelper.GetDsaUrls(shopUrl);

                        campaign.DSAWebsite = urls.Website;
                        dispayUrl = urls.Display;  //adGroupAd.DisplayURL = urls.Display;
                        adGroupCriteria.DynamicAdTargetValue1 = urls.CriteriaUrl;

                        if (adGroupCriteria.DynamicAdTargetValue1.Contains("DSA_WILL_NOT_WORK"))
                        {
                            campaign.CreationWarning = "Marketplace - DSA will not work here.";
                            TbInternalTasksManager.AddIfNew(shopID, TbInternalTasksCategories.Marketplace_DSA_Will_Not_Work, "Marketplace - DSA will not work here.", null);
                        }
                    }
                    else if (MarketplaceUrlHelper.IsSubdomain(shopUrl))
                    {
                        campaign.DSAWebsite = MarketplaceUrlHelper.GetTopDomainOfSubdomain(shopUrl);

                        dispayUrl = shopUrl; // adGroupAd.DisplayURL = shopUrl;
                        adGroupCriteria.DynamicAdTargetValue1 = shopUrl;
                        campaign.CreationWarning = "Subdomain used";
                    }
                    else
                    {
                        campaign.DSAWebsite = shopUrl;
                        dispayUrl = ForceWWW(shopUrl); //adGroupAd.DisplayURL = ForceWWW(shopUrl);
                        adGroupCriteria.DynamicAdTargetValue1 = ForceNoWWW(shopUrl);
                    }

                    cretiteriasList.Add(adGroupCriteria);
                }

                string domain = UrlPathHelper.GetDomainName(shopUrl);

                if (domain.Contains("www."))
                    domain = domain.Replace("www.", "");

                if (domain.Length > 20 && domain.Contains("."))
                {
                    int index_0 = domain.IndexOf('.');
                    domain = domain.Remove(index_0);
                }

                List<AdGroupAd> adGroupAds = AdGroupAdManager.SetDsaAdGroupAds(tb_settings, fillByAdcopiesIfExist: true);
                campaign.DSALanguage = language;
                campaign.AllLanguages = language;

                foreach (AdGroupAd adGroupAd in adGroupAds)
                {
                    adGroupAd.DisplayURL = dispayUrl;
                    adGroupAd.AdGroupCriterias = cretiteriasList;
                }

                adGroup.AdGroupAds = adGroupAds;
                campaign.AdGroups.Add(adGroup);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to create a campaign for {0}.", shopUrl), ex, shopID);
                campaign.Status = "ERROR";
                campaign.Name += ex.Message;
            }
            return campaign;
        }

        private static string SetLanguage(string finalUrl, List<string> country_codes, int shopID)
        {

            string language_by_dandelon = null;
            string language_by_country = GetCampaignLanguage(country_codes);

            string language = language_by_country;
            string subject = null;
            StringBuilder sb = new StringBuilder();

            bool targetUS = country_codes.Where(x => x == "US").Any();

            LanguageDetectorManager man = new LanguageDetectorManager();
            LanguageISO_639_1 lang = null;

            string dandelon_ui = string.Format("https://dandelion.eu/semantic-text/entity-extraction-demo/?url={0}&lang=auto&min_confidence=0.6&exec=true#results", HttpUtility.UrlEncode(finalUrl));

            try
            {
                lang = LanguageDetectorManager.GetSiteLanguage(finalUrl);
                Log4NetLogger.Info(string.Format("Detected language with an external API is {0}.", lang), shopID);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to detect language with an external API for {0}.", finalUrl), ex, shopID);
            }

            if (lang != null)
            {
                language_by_dandelon = MapToIsoLangCodeToAdwordslangCode(lang);
                if (language_by_dandelon != language_by_country && lang.Confidence > 0.8)
                {
                    sb.AppendLine(string.Format("API detected language - <b>{0}</b> Confidence: {1}<br/>", language_by_dandelon.ToUpper(), lang.Confidence));
                    sb.AppendLine(string.Format("The language we set to Adwords - <b>{0}</b><br/>", language.ToUpper()));

                    subject = string.Format("The detected language does not suit to target country on {0} - shopID: {1}<br/>", finalUrl, shopID);

                    sb.AppendLine(string.Format("Url: {0}<br/>", finalUrl));
                    sb.AppendLine(string.Format("TB setting Countries: {0} <br/>", string.Join(", ", country_codes.ToArray())));
                    sb.AppendLine(string.Format("<a href=\"https://bo.storeya.com/shop/details/{0}\">https://www.storeya.com/shop/details/{0}</a><br/>", shopID));
                }

                //else if (!(targetUS && lang.Code == "en") && lang.Confidence < 0.9)
                //{
                //    subject = string.Format("The detected language confidence is less than 0.9 on {0} - shopID: {1}", finalUrl, shopID);
                //}
            }
            //else
            //{
            //    subject = string.Format("Failed to detect language on {0} - shopID: {1}", finalUrl, shopID);
            //    sb.AppendLine(string.Format("The language failed to be detected by external Dendelon API at the new campaign upload. <br/>Please, check the site's language and set it on AdWords manually, if needed. <br/> URL: <a href=\"{2}\">{2}</a>  <br/> Review an origin dendelon error: {1}  <br/>Language which is set on Adwords: {3} <br/>Shop Details: https://bo.storeya.com/shop/details/{0}", shopID, dandelon_ui, finalUrl, language));
            //}

            if (!string.IsNullOrEmpty(subject) && !string.IsNullOrEmpty(sb.ToString()))
            {
                EmailHelper.SendEmail("<EMAIL>", subject, sb.ToString());
            }
            return language;
        }
        public static string SetLanguage(string countryCodes)
        {
            List<string> country_codes = GetCountryCodes(countryCodes);
            return GetCampaignLanguage(country_codes);
        }
        public static string SetLanguage(List<string> country_codes)
        {

            return GetCampaignLanguage(country_codes);
        }

        private static string MapToIsoLangCodeToAdwordslangCode(LanguageISO_639_1 lang)
        {
            string adwords_code = AdWordsLanguagesCodes.GetAdWordsLanguagesCode(lang.Code);
            return adwords_code;
        }


        private static string GetInitBudjetByPlan(long clicks)
        {
            if (clicks < 1000)
            {
                return "4";
            }
            else if (clicks == 1000)
            {
                return "17";
            }
            else if (clicks == 3000)
            {
                return "51";
            }
            return "4";
        }

        public static string GetInitBudjetByPlanAndPaymentSystem(long clicks, int? paymentSystem)
        {
            //will run only on init campaigns creation - so not relevant for Wix upgrades
            if (paymentSystem == (int)PaymentSystems.WixPayments_NoAdServer)
            {
                return "1.2";
            }

            var purchasedSpend = TbSettingsHelper.GetPurchasedSpend(clicks, paymentSystem);
            var daily = Math.Round((decimal)purchasedSpend / (decimal)30, 2);
            return daily.ToString();
        }

        private static string ForceWWW(string url)
        {
            if (!url.Contains("www."))
            {
                url = "www." + url;
            }
            return url;
        }
        private static string ForceNoWWW(string url)
        {
            if (url.Contains("www."))
            {
                url = url.Replace("www.", "");
            }
            return url;
        }

        private static List<AdGroupCriteria> CreateAdGroupCriteriasForEtsy(string etsyShopUrl, int shopID)
        {
            List<AdGroupCriteria> cretiteriasList = new List<AdGroupCriteria>();

            string storeID = etsyShopUrl.Replace("www.Etsy.com/shop/", "");

            List<string> productUrls = ExtractTwentyProductUrlsFromEtsyAPI(storeID, shopID);
            if (productUrls != null && productUrls.Count > 0)
            {
                foreach (var item in productUrls)
                {
                    AdGroupCriteria adGroupCriteria = new AdGroupCriteria();
                    adGroupCriteria.DynamicAdTargetValue1 = GetEtsyDynamicAdTargetValueForListing(item);
                    cretiteriasList.Add(adGroupCriteria);
                }
            }

            AdGroupCriteria adGroupHpCriteria = new AdGroupCriteria();
            adGroupHpCriteria.DynamicAdTargetValue1 = "/shop/" + storeID;
            cretiteriasList.Add(adGroupHpCriteria);


            return cretiteriasList;
        }

        private static List<string> ExtractTwentyProductUrlsFromEtsyAPI(string storeID, int shopID)
        {
            List<string> productUrls = null;

            ////AbstructDataExtractorStrategy provider = DataProviderFactory.GetInstance(shopID);
            ////EtsyProvider provider = new EtsyProvider(storeID, null);
            //List<ProductRawData> products = provider.GetProducts(20, null);
            //if (products != null && products.Count > 0)
            //{
            //    foreach (ProductRawData product in products)
            //    {
            //        if (productUrls == null)
            //            productUrls = new List<string>();

            //        productUrls.Add(product.Url);
            //    }
            //}
            return productUrls;
        }

        public static string ExtractEtsyStoreName(string url)
        {
            //https://www.etsy.com/shop/tablefor5designs

            //https://www.WaterLelieJewellery.etsy.com

            //https://www.etsy.com/il-en/shop/RUIART?ref=pr_shop_more

            //https://www.etsy.com/il-en/listing/243347997/tahitian-pearl-bracelet-for-women?ref=shop_home_feat_1

            //https://www.etsy.com/people/RKAZI 


            string cleanUrl = UrlPathHelper.GetCleanHostName(url);

            var storeID = RegexHelper.GetSingleValue("/shop/[a-zA-Z0-9]+", cleanUrl);
            if (!string.IsNullOrEmpty(storeID))
            {
                return storeID.Replace("/shop/", "");
            }
            else if (cleanUrl.Contains(".etsy.com"))
            {
                return cleanUrl.Split('.')[0];
            }
            else
            {
                WebClient client = new WebClient();
                var content = client.DownloadString(url);

                var storeID2 = RegexHelper.GetSingleValue("/shop/[a-zA-Z0-9]+", content);
                if (!string.IsNullOrEmpty(storeID2))
                {
                    return storeID2.Replace("/shop/", "");
                }
            }

            return cleanUrl;
        }

        private static string GetStoreID(string tempUrl)
        {
            int index = tempUrl.IndexOf(".etsy.com");
            tempUrl = tempUrl.Remove(index);
            if (tempUrl.Contains('.'))
            {
                tempUrl = tempUrl.Split('.')[1];
            }
            return tempUrl;
        }

        private static string GetWixStoreName(string url)
        {
            //http://androidbuyer.uk.wix.com/store
            //http://agjewelery.wix.com/ag-jewellery
            //http://exotic1piel.wix.com/accessories-shop-es
            //http://ateliermarechalstu.wix.com/kara
            //http://www.clydesoap.wix.com/clydesidesoap1

            url = UrlPathHelper.GetCleanHostName(url);
            //url = url.Replace("www.", "");
            //url = url.Replace(".wix.com", "");
            return url;
        }


        private static string GetEtsyDynamicAdTargetValueForListing(string url)
        {
            ///listing/99452391/modern-freshwater-pearl-necklace-single?ref=shop_home_feat_2
            int index = url.IndexOf("/listing/");
            url = url.Remove(0, index);

            if (url.Contains('?'))
            {
                int index_1 = url.IndexOf("?");
                url = url = url.Remove(index_1);
            }
            return url;
        }

        private string GetEtsyDynamicAdTargetValue(string url)
        {
            //Product's URL , like /shop/OrchhaJewels
            //https://www.etsy.com/shop/tablefor5designs
            //https://www.WaterLelieJewellery.etsy.com

            int index = url.IndexOf("/shop/");
            if (index > 0)
            {
                url = url.Remove(0, index);
            }
            else
            {
                url = "/shop/" + ExtractEtsyStoreName(url);
            }

            return url;
        }

        public static List<string> GetConvertingCountryCodes(int shopId, string countryCodes, out bool usingGA)
        {

            List<string> countryCodesList = GetCountryCodes(countryCodes);
            try
            {
                if (countryCodesList.Count > 1)
                {
                    List<string> convertingCountryCodesList = new List<string>();
                    var db = DataHelper.GetStoreYaEntities();
                    GAConnectedAccount connectedAccount = db.GAConnectedAccounts.Where(a => a.ShopID == shopId).SingleOrDefault();
                    GaProfileInfo gaProfileInfo = GaManager.GetActiveProfileInfo(shopId);
                    if (gaProfileInfo != null)
                    {
                        string propertyUrl = UrlPathHelper.BuildValidUri(gaProfileInfo.WebsiteUrl);
                        IGaSimpleService service = GaSimpleService.GetInstanceWithRefreshToken(gaProfileInfo);

                        GADataExtractor extractor = new GADataExtractor();
                        string from = DateTime.Now.AddMonths(-1).ToString("yyy-MM-dd");
                        string to = DateTime.Now.ToString("yyy-MM-dd");

                        Dictionary<string, string> metrics = new Dictionary<string, string>();
                        metrics.Add("SESSIONS", "ga:sessions");
                        metrics.Add("TRANSACTIONS", "ga:transactions");
                        metrics.Add("TRANSACTIONREVENUE", "ga:transactionRevenue");
                        if (!gaProfileInfo.V4)
                        {
                            string purchasesGoalMetrics = $"ga:goal{gaProfileInfo.GAv3ConnectedProfile.GoalForPurchaseID}Completions";
                            if (!string.IsNullOrEmpty(gaProfileInfo.GAv3ConnectedProfile.GoalForPurchaseID))
                            {
                                metrics.Add("PURCHASES", purchasesGoalMetrics);
                            }
                        }
                        Dictionary<string, string> groupByCountry = new Dictionary<string, string>();
                        groupByCountry.Add("COUNTRY", "ga:country");
                        List<GaReportData> groupByCountryReportData = service.GetGaGroupedData(gaProfileInfo.ProfileID, metrics, groupByCountry, null, from, to);
                        if (groupByCountryReportData != null)
                        {
                            List<Country> countries = CountriesHelper.GetCountries();

                            groupByCountryReportData = groupByCountryReportData.OrderByDescending(o => o.Transactions).ThenByDescending(o => o.Purchases).ThenByDescending(o => o.Sessions).Take(10).ToList();
                            foreach (var item in groupByCountryReportData)
                            {

                                Country oCountry = countries.SingleOrDefault(c => c.Name == item.Country.ToUpper());
                                if (oCountry == null)
                                {
                                    continue;
                                }
                                string country = countryCodesList.SingleOrDefault(c => c == oCountry.Code);
                                if (country == null)
                                {
                                    continue;
                                }
                                else
                                {
                                    Console.WriteLine($"{country} - {item.Transactions}");
                                    convertingCountryCodesList.Add(country);
                                }
                            }
                            if (convertingCountryCodesList.Count() == 0)
                            {
                                usingGA = false;
                                return countryCodesList.Take(5).ToList();
                            }
                            usingGA = true;
                            return convertingCountryCodesList.Take(5).ToList();
                        }
                    }

                }
                usingGA = false;
                return countryCodesList.Take(5).ToList();
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteErrorWithDB("Failed to Get Countries from GA using TB list", ex, shopId);
                usingGA = false;
                return countryCodesList;
            }
        }

        public static List<string> GetCountryCodes(string countryCodes)
        {
            List<string> codes = new List<string>();

            if (countryCodes != null && countryCodes.Contains(",")) //if countryCode = AX,DZ,AS
            {
                string[] countries = countryCodes.Split(',');
                if (countries != null && countries.Length > 0)
                {
                    for (int i = 0; i < countries.Length; i++)
                    {
                        var code = CleanCode(countries[i]);
                        if (!string.IsNullOrEmpty(code) &&
                            code.ToLower() != "worldwide" &&
                            code.ToLower() != "europe")
                        {
                            codes.Add(CleanCode(countries[i]));
                        }
                    }
                }
            }
            else
            {
                //only single country code
                var code = CleanCode(countryCodes);
                if (!string.IsNullOrEmpty(code))
                {
                    if (code.ToLower() == "worldwide")
                    {
                        code = "US";
                        EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "worldwide changed to US", "countryCodes was " + countryCodes);
                    }
                    else if (code.ToLower() == "europe")
                    {
                        code = "GB";
                        EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "europe changed to GB", "countryCodes was " + countryCodes);
                    }
                }
                codes.Add(code);
            }

            return codes;
        }

        private static string CleanCode(string code)
        {
            if (string.IsNullOrEmpty(code) || code == "ZZ" || code.ToLower() == "null")
            {
                return "US";
            }
            else
            {
                return code.Replace("]", "").Replace("[", "").Trim();
            }
        }

        //private string GetCountryCode(string countryCode)
        //{
        //    string code = countryCode;

        //    if (string.IsNullOrEmpty(countryCode))
        //    {
        //        return "US";
        //    }


        //    if (code.Contains(",")) //if countryCode = AX,DZ,AS
        //    {
        //        string[] countries = code.Split(',');
        //        if (countries != null && countries.Length > 0)
        //        {
        //            for (int i = 0; i < countries.Length; i++)
        //            {
        //                if (!string.IsNullOrEmpty(countries[i]))
        //                {
        //                    code = countries[i];
        //                    break;
        //                }
        //            }
        //        }
        //    }
        //    return code;
        //}

        public static bool UsesEnglishLanguage(List<string> countryCodes)
        {
            foreach (var c in countryCodes)
            {
                if (Storeya.Core.Helpers.CountriesHelper.UsesEnglishLanguage(c))
                {
                    return true;
                }
            }

            return false;
        }

        public static string GetCampaignLanguage(List<string> countryCodes)
        {
            string lang = null;

            foreach (var c in countryCodes)
            {
                var currLang = CountriesHelper.GetAdwordsLanguageCodeByCountryCode(c);
                if (currLang == "en")
                {
                    lang = currLang;
                    break;
                }

                if (string.IsNullOrEmpty(lang))
                {
                    lang = currLang;
                }
            }

            if (lang == null)
            {
                lang = "en";
            }

            return lang;
        }


        private string GetCountryNameByCountryCode(string countryCode)
        {
            string countryName = Storeya.Core.Helpers.CountriesHelper.GetCountryName(countryCode);
            if (string.IsNullOrEmpty(countryName))
            {
                countryName = "United States";
            }
            return countryName;
        }

        private static List<string> GetCountriesNames(List<string> countryCodes)
        {
            List<string> countries = new List<string>();
            foreach (string c in countryCodes)
            {
                string countryName = Storeya.Core.Helpers.CountriesHelper.GetCountryName(c);
                //if (string.IsNullOrEmpty(countryName))
                //{
                //    countryName = "United States";
                //}
                if (!string.IsNullOrEmpty(countryName) && !countries.Where(i => i == countryName).Any())
                    countries.Add(countryName);
            }

            return countries;
        }

        public static string GetDomain(string url, bool useBingValidation = false)
        {

            if (ProviderFactory.IsUrlEtsy(url))
            {
                //www.etsy.com/shop/tablefor5design
                //url = GetCleanHostName(url);
                string storeID = ExtractEtsyStoreName(url);
                url = "www.Etsy.com/shop/" + storeID;

            }
            else
            {

                var cleanHostUrl = GetCleanHost(url);
                if (cleanHostUrl == null)
                {
                    return url + "_ERROR_BAD_FORMATTED";
                }

                if (IsMarketplace(url) || MarketplaceUrlHelper.IsSubdomain(cleanHostUrl))
                {
                    //get main url
                    url = GetMarketplaceStoreUrl(url);
                }
                else
                {

                    if (useBingValidation)
                    {
                        if (ConfigHelper.GetValue("BingApiKey", null) == null)
                        {
                            Console.WriteLine("Missing BING key");
                            url = cleanHostUrl;
                        }
                        else
                        {
                            //url = GetHostWithBing(cleanHostUrl);
                            url = UrlPathHelper.CareAboutWwwOrNot(cleanHostUrl);
                        }

                    }
                    else
                    {
                        url = cleanHostUrl;
                    }
                }
            }
            return url;
        }

        private static string GetMarketplaceStoreUrl(string url)
        {
            return MarketplaceUrlHelper.GetStoreHomePage(url);
        }

        private static bool IsMarketplace(string url)
        {
            return MarketplaceUrlHelper.IsMarketplace(url);
        }

        public static string GetCleanHost(string url)
        {
            string cleanHost = url;
            if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                Uri uri = new Uri(url);
                return uri.Host;
            }
            return null;
        }

        private static string GetHostWithBing(string cleanHost)
        {
            string withWww = null;
            string withoutWww = null;

            if (cleanHost.Contains("www."))
            {
                withWww = cleanHost;
                withoutWww = cleanHost.Replace("www.", "");
            }
            else
            {
                withWww = "www." + cleanHost;
                withoutWww = cleanHost;
            }

            BingSearcherHelper helper = new BingSearcherHelper();
            long indexedWithWWW = helper.GetIndexedResultsCount(withWww);
            long indexedWithoutWWW = helper.GetIndexedResultsCount(withoutWww);

            if (indexedWithWWW == 0 && indexedWithoutWWW == 0)
            {
                return withoutWww + "_ERROR_BAD_FORMATTED_NO_INDEXED_PAGES";
            }
            else if (indexedWithWWW > indexedWithoutWWW)
            {
                return withWww;
            }
            else
            {
                return withoutWww;
            }
        }

        //public static IEnumerable<BingSearch.Result> GetBingResults(string cleanHost)
        //{
        //    BingSearcherHelper helper = new BingSearcherHelper();
        //    IEnumerable<BingSearch.Result> bingSearchResults = null;

        //    bingSearchResults = helper.Search(string.Format("site:{0}", cleanHost), 0);

        //    return bingSearchResults;
        //}



        //private static string GetCampaignName(int shopID, string domain, List<string> countryCodes, long impressions)
        //{
        //    return AdWordsCampaignFactory.CombineCampaignName(shopID, domain, countryCodes, impressions);
        //}


        //public List<TBUploadCsvObject> GetObjectForCsvFileCreation(int tbID)
        //{
        //    StoreYaEntities db = new StoreYaEntities();
        //    List<TBUploadCsvObject> tbObjects = new List<TBUploadCsvObject>();
        //    TrafficBooster tbSettings = db.TrafficBoosters.Where(t => t.ID == tbID).Single();

        //    DsaCampaign campaign = GetCampaign(tbSettings);
        //    TBUploadCsvObject upOb = MapCampaignToUploadCsvObjects(campaign);
        //    tbObjects.Add(upOb);

        //    return tbObjects;
        //}

        //public List<TBUploadCsvObject> CreateCsvObjectsFromList(string ids)
        //{
        //    StoreYaEntities db = new StoreYaEntities();
        //    var shopIDs = ids.Split(',').Select(Int32.Parse).ToList();
        //    List<TBUploadCsvObject> tbObjects = new List<TBUploadCsvObject>();
        //    List<TrafficBooster> tbSettings = db.TrafficBoosters.Where(t => shopIDs.Contains(t.ShopID ?? 0)).ToList();

        //    foreach (var shop in tbSettings)
        //    {
        //        DsaCampaign campaign = GetCampaign(shop);
        //        TBUploadCsvObject upOb = MapCampaignToUploadCsvObjects(campaign);
        //        tbObjects.Add(upOb);
        //    }

        //    return tbObjects;
        //}


        ////public List<TBUploadCsvObject> GetObjectsForCsvFileCreation()
        ////{
        ////    StoreYaEntities db = new StoreYaEntities();
        ////    List<TBUploadCsvObject> tbObjects = new List<TBUploadCsvObject>();
        ////    IQueryable<TrafficBooster> tbSettingsToIncludeToExcel = TrafficBoostersDbHelper.GetActiveForAdwords();

        ////    foreach (TrafficBooster tbSettings in tbSettingsToIncludeToExcel.ToList())
        ////    {
        ////        DsaCampaign campaign = GetCampaign(tbSettings);
        ////        TBUploadCsvObject upOb = MapCampaignToUploadCsvObjects(campaign);
        ////        tbObjects.Add(upOb);

        ////        TrafficBooster tbToUpdate = db.TrafficBoosters.Where(t => t.ID == tbSettings.ID).Single();
        ////        tbToUpdate.AdwordsUploadStatus = (int)AdwordsUploadStatus.IncludedToExcel;
        ////        db.SaveChanges();
        ////        Log4NetLogger.Info("Loaded to excel", tbToUpdate.ShopID ?? 0);
        ////    }

        ////    return tbObjects;
        ////}

        //public void MarkAsUploaded()
        //{
        //    StoreYaEntities db = new StoreYaEntities();
        //    List<TBUploadCsvObject> tbObjects = new List<TBUploadCsvObject>();
        //    List<TrafficBooster> tbSettingsTomarkAsUploaded = db.TrafficBoosters.Where(t => t.AdwordsUploadStatus == (int)AdwordsUploadStatus.IncludedToExcel).ToList();

        //    foreach (var tbSettings in tbSettingsTomarkAsUploaded)
        //    {
        //        TrafficBooster tbToUpdate = db.TrafficBoosters.Where(t => t.ID == tbSettings.ID).Single();
        //        tbToUpdate.AdwordsUploadStatus = (int)AdwordsUploadStatus.UploadedToAdwords;
        //        db.SaveChanges();
        //    }
        //}

        //public string GetCsvContent(List<TBUploadCsvObject> tbObjects)
        //{
        //    StringBuilder s = new StringBuilder();
        //    s.Append("Campaign,DSA Website,Display URL,Dynamic Ad Target Value 1,Description Line 1,Description Line 2,Max CPC,Campaign Daily Budget,DSA Language,Languages,Location,Campaign Type,Networks,Bid Strategy Type,Enhanced CPC,Viewable CPM,Bid Modifier,Ad rotation,Delivery method,Targeting method,Exclusion method,Campaign Priority,Local Inventory Ads,Tracking template,Ad Group,Display Network Max CPC,Max CPM,CPA Bid,Display Network Custom Bid Type,Targeting optimization,Ad Group Type,Flexible Reach,Device Preference,Dynamic Ad Target Condition 1,Dynamic Ad Target Condition 2,Dynamic Ad Target Condition 3,Campaign Status,Ad Group Status,Status" + Environment.NewLine);

        //    foreach (var tbObject in tbObjects)
        //    {
        //        foreach (var item in tbObject.CampaignRows)
        //        {
        //            s.Append(string.Format("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},{21},{22},{23},{24},{25},{26},{27},{28},{29},{30},{31},{32},{33},{34},{35},{36},{37}",
        //           item.Campaign,
        //           item.DSAWebsite,
        //           item.DisplayURL,
        //           item.DynamicAdTargetValue1,
        //           item.DescriptionLine1,
        //           item.DescriptionLine2,
        //           item.MaxCPC,
        //           item.CampaignDailyBudget,
        //           item.DSALanguage,
        //           item.Languages,
        //           item.Location,
        //           item.CampaignType,
        //           item.Networks,
        //           item.BidStrategyType,
        //           item.EnhancedCPC,
        //           item.ViewableCPM,
        //           item.BidModifier,
        //           item.AdRotation,
        //           item.DeliveryMethod,
        //           item.TargetingMethod,
        //           item.ExclusionMethod,
        //           item.CampaignPriority,
        //           item.LocalInventoryAds,
        //           item.TrackingTemplate,
        //           item.AdGroup,
        //           item.DisplayNetworkMaxCPC,
        //           item.MaxCPM,
        //           item.CPABid,
        //           item.DisplayNetworkCustomBidType,
        //           item.TargetingOptimization,
        //           item.AdGroupType,
        //           item.FlexibleReach,
        //           item.DevicePreference,
        //           item.DynamicAdTargetCondition1,
        //           item.DynamicAdTargetCondition2,
        //           item.DynamicAdTargetCondition3,
        //           item.CampaignStatus,
        //           item.AdGroupStatus,
        //           item.Status

        //           ) + Environment.NewLine);
        //        }
        //    }

        //    return s.ToString();
        //}


        //public void CreateCsvFile(string csvFileName, List<TBUploadCsvObject> tbObjects)
        //{
        //    using (var sw = new StreamWriter(csvFileName))
        //    {
        //        var writer = new CsvWriter(sw);

        //        writer.WriteHeader(typeof(UploadCsvObject));

        //        foreach (TBUploadCsvObject tbObject in tbObjects)
        //        {
        //            for (int i = 0; i < tbObject.CampaignRows.Count; i++)
        //            {
        //                writer.WriteRecord(tbObject.CampaignRows[i]);
        //            }
        //        }
        //    }
        //}


        public static int GetShopIDFromCampaignName(string campaignName)
        {
            if (!string.IsNullOrEmpty(campaignName))
            {
                string[] delimitedByUnderscore = campaignName.Split('_');
                if (delimitedByUnderscore != null && delimitedByUnderscore.Length > 0)
                {
                    string firstParam = delimitedByUnderscore[0];
                    int shopID;
                    if (!string.IsNullOrEmpty(firstParam) && int.TryParse(firstParam, out shopID))
                    {
                        return shopID;
                    }
                }
            }

            return 0;
        }




        public static CallOnlyAdWordsCampaign GetCallCampaign(TbCampaign campaignToLoad, string cleanUrl = null)
        {

            var db = DataHelper.GetStoreYaEntities();
            var callCampaign = db.AdWordsCallsCampaigns.Where(c => c.TbID == campaignToLoad.TbID).Single();
            var ads = db.AdWordsCallsAds.Where(a => a.CallCampaignID == callCampaign.ID).Select(a => a);
            var shopID = campaignToLoad.ShopID.Value;

            var tbSettings = TrafficBoostersDbHelper.GetSettings(shopID);
            //int? catalogSourcePlatform = TrafficBoostersDbHelper.GetShopCatalogSoursePlatform(shopID);

            if (!TbSettingsHelper.IsDefaultBudgetActiveAndPaid(tbSettings))
            {
                throw new Exception("Missing payments information.");
            }

            CallOnlyAdWordsCampaign newCampaign = new CallOnlyAdWordsCampaign();

            callCampaign.DisplayUrl = UrlPathHelper.BuildValidUri(callCampaign.DisplayUrl);

            if (callCampaign.DisplayUrl == null)
            {
                throw new Exception("Missing DisplayUrl.");
            }

            newCampaign.HomepageUrl = callCampaign.DisplayUrl;
            List<string> countries = new List<string>();
            countries.Add(callCampaign.Country);

            if (string.IsNullOrEmpty(cleanUrl))
                cleanUrl = GetDomain(callCampaign.DisplayUrl, false);

            newCampaign.Name = AdWordsCampaignFactory.CombineCampaignName(shopID, cleanUrl, countries, tbSettings.PurchasedAmount.Value, "Calls", (tbSettings.PaymentSystem ?? (int)TrafficBoostersDbHelper.GetDefaultPaymentSystem()));
            newCampaign.BusinessName = callCampaign.OriginTitle;

            foreach (var ad in ads)
            {
                var adGroup = new CallOnlyAdGroup();
                adGroup.DisplayUrl = callCampaign.DisplayUrl;
                if (ad.Description1.Contains("^"))
                {
                    var a = ad.Description1.Split(new char[] { '^' });
                    adGroup.Description1 = FixDescription(a[0]);
                }
                else
                {
                    adGroup.Description1 = FixDescription(ad.Description1);
                }

                if (ad.Description2.Contains("^"))
                {
                    var a = ad.Description2.Split(new char[] { '^' });
                    adGroup.Description2 = FixDescription(a[0]);
                }
                else
                {
                    adGroup.Description2 = FixDescription(ad.Description2);
                }
                adGroup.Keywords = ad.Keywords.Split(',').ToList();
                newCampaign.AdGroups.Add(adGroup);
            }

            newCampaign.Phone = callCampaign.Phone;
            newCampaign.PhoneCountryCode = callCampaign.Country;
            newCampaign.CallZipCode = tbSettings.CallZipCode;
            newCampaign.Radius = tbSettings.CallRadius;
            newCampaign.RadiusUnits = tbSettings.CallRadiusUnits; //KILOMETERS = 0, MILES = 1,

            return newCampaign;

        }

        public static CallOnlyAdWordsCampaign GetCallCampaignFromDSA(DsaCampaign dsaCampaign, int shopID)
        {
            TrafficBooster tbSettings = TrafficBoostersDbHelper.GetSettings(shopID);
            if (tbSettings.PrimaryTrafficMethod != (int)PrimaryTrafficMethods.AdWordsAndCalls)
            {
                //no call campaign requested
                return null;
            }

            var db = DataHelper.GetStoreYaEntities();

            CallOnlyAdWordsCampaign newCampaign = new CallOnlyAdWordsCampaign();
            newCampaign.HomepageUrl = dsaCampaign.HomepageUrl;
            newCampaign.Name = dsaCampaign.Name.Replace("_Dynamic", "_Calls");
            newCampaign.BusinessName = tbSettings.CallBussnessName;

            newCampaign.AllLocationCodes = string.Join(",", dsaCampaign.LocationsCodes);
            newCampaign.IsServiceProvider = dsaCampaign.IsServiceProvider;
            newCampaign.ServiceLocation = dsaCampaign.ServiceLocation;


            var adGroup = new CallOnlyAdGroup();
            adGroup.DisplayUrl = dsaCampaign.HomepageUrl;
            if (!string.IsNullOrEmpty(tbSettings.CallDescription1))
            {
                if (tbSettings.CallDescription1.Contains("^"))
                {
                    var a = tbSettings.CallDescription1.Split(new char[] { '^' });
                    adGroup.Description1 = a[0];
                    adGroup.HeadLine1 = a[1];
                }
                else
                {
                    adGroup.Description1 = FixDescription(tbSettings.CallDescription1);
                }
            }
            if (!string.IsNullOrEmpty(tbSettings.CallDescription2))
            {
                if (tbSettings.CallDescription2.Contains("^"))
                {
                    var a = tbSettings.CallDescription2.Split(new char[] { '^' });
                    adGroup.Description2 = a[0];
                    adGroup.HeadLine2 = a[1];
                }
                else
                {
                    adGroup.Description2 = FixDescription(tbSettings.CallDescription2);
                }
            }

            adGroup.Keywords = AdwordsKeywordsHelper.ToKeywordsList(tbSettings.CallKeywords, 80);

            newCampaign.AdGroups.Add(adGroup);

            newCampaign.Phone = tbSettings.CallPhone;
            newCampaign.PhoneCountryCode = tbSettings.CallCountryCode;
            newCampaign.CallZipCode = tbSettings.CallZipCode;
            newCampaign.Radius = tbSettings.CallRadius;
            newCampaign.RadiusUnits = tbSettings.CallRadiusUnits; //KILOMETERS = 0, MILES = 1,

            return newCampaign;

        }



        //public static IQueryable<TrafficBooster> GetWaitingToUpload()
        //{
        //    StoreYaEntities db = new StoreYaEntities();
        //    IQueryable<TrafficBooster> list =
        //        db.TrafficBoosters.Where(t => t.ShopID > 0 
        //            && t.Status != (byte)TrafficBoosterStatuses.Canceled 
        //            && t.Status > (byte)TrafficBoosterStatuses.WaitingToRunFirstTime 
        //            && t.TrafficMethod == (int)TrafficMethod.AdWordAndAdServer
        //            && t.AdwordsUploadStatus !=  10
        //            );

        //    return list;
        //}
        public static AdwordsPixelResults ValidateShopifyPixel(int shopId, string pixelId, int daysBackToCheck = -7)
        {
            return ValidateShopifyPixel(shopId, pixelId, out bool customPixelInstalled, daysBackToCheck);
        }
        public static AdwordsPixelResults ValidateShopifyPixel(int shopId, string pixelId, out bool customPixelInstalled, int daysBackToCheck = -7)
        {

            var tb = TrafficBoostersDbHelper.GetSettings(shopId);
            Console.WriteLine("Custom pixel check...");

            customPixelInstalled = AdwordsManager.FindCustomShopifyAwPixel(tb.Url1, pixelId);
            if (customPixelInstalled)
            {
                Console.WriteLine("Custom pixel was found");
                return AdwordsPixelResults.OK;
            }
            Console.WriteLine("No custom pixel");

            customPixelInstalled = false;
            return ValidateNotCustomPixel(shopId, pixelId, daysBackToCheck);
        }
        public static AdwordsPixelResults ValidateNotCustomPixel(int shopId, string pixelId, int daysBackToCheck = -7)
        {
            int appID = (int)Shopify_StoreyaApp.TrafficBooster;
            int totalOrderToCheck = 10;
            var db = DataHelper.GetStoreYaEntities();
            ShopifyConnectedShop connectedShopSettings = db.ShopifyConnectedShops.Where(s => s.ShopID == shopId && s.StoreyaAppTypeID == appID).SingleOrDefault();
            ShopifyConnector shopifyConnector = new ShopifyConnector(shopId, appID);
            DateTime dateToStartFrom = DateTime.Now.AddDays(daysBackToCheck);
            string additionalRequestFilter = "&fields=order_number,order_status_url,total_line_items_price,total_price,currency,email,test";
            List<Storeya.Core.Models.Shopify.ShopifyOrder> orders = shopifyConnector.GetOrders(0, dateToStartFrom, null, false, additionalRequestFilter, totalOrderToCheck);
            if (orders == null || orders.Count == 0)
            {
                dateToStartFrom = DateTime.Now.AddDays(daysBackToCheck - 8);
                orders = shopifyConnector.GetOrders(0, dateToStartFrom, null, false, additionalRequestFilter, totalOrderToCheck);
                if (orders == null || orders.Count == 0)
                {
                    return AdwordsPixelResults.NoOrders;
                }
            }
            Console.WriteLine($"{orders.Count} orders extracted.");
            orders = orders.Take(10).ToList();
            foreach (var item in orders)
            {
                if (item.OrderStatusUrl != null)
                {
                    string content = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(item.OrderStatusUrl);
                    if (content.Contains(pixelId))
                    {
                        return AdwordsPixelResults.OK;
                    }

                }
            }
            return AdwordsPixelResults.NotFoundOnPage;
        }
        public static bool ChangePixelStatuses(int shopID, AdWordsRemarketingCodeStatus remarketingCodeStatus, AdWordsPixelStatuses adWordsPixelStatuses)
        {
            bool thereAreChanges = false;
            if (TrafficBoostersDbHelper.UpdateTbRemarketingPixelStatus(shopID, remarketingCodeStatus))
            {
                thereAreChanges = true;
            }
            if (TrafficBoostersDbHelper.UpdateTbPixelStatus(shopID, adWordsPixelStatuses))
            {
                thereAreChanges = true;
            }
            return thereAreChanges;

        }

        public static bool FindCustomShopifyAwPixel(string domain, string pixelId)
        {
            try
            {
                string content = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(domain);

                if (content != null)
                {
                    return FindCustomShopifyAwPixelUsingHTML(content, pixelId, domain);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public static bool FindCustomShopifyAwPixelUsingHTML(string content, string pixelId, string domain)
        {
            try
            {
                string script = GetPixelScriptsFromPageHTML(content);
                Console.WriteLine("script:");
                Console.WriteLine("");
                Console.WriteLine("");
                Console.WriteLine(script);
                Console.WriteLine("");
                Console.WriteLine("");
                Console.WriteLine("end of the script");
                Dictionary<string, List<string>> pixelsConfigs = GetPixelsIDAndWPMs(script);
                if (pixelsConfigs != null && pixelsConfigs.Count > 0)
                {
                    return CheckIfCustomPixelExist(pixelsConfigs, pixelId, domain);
                }
                Console.WriteLine("FindCustomShopifyAwPixelUsingHTML: no pixelsConfigs was found");
                return false;
            }
            catch
            {
                return false;
            }
        }

        private static bool CheckIfCustomPixelExist(Dictionary<string, List<string>> pixelsConfigs, string pixel, string domain)
        {
            foreach (var pixelsConfigin in pixelsConfigs)
            {
                string urls = "";
                domain = FixDomain(domain);
                foreach (var pixelId in pixelsConfigin.Value)
                {

                    //var url = $"{domain}wpm@{pixelsConfigin.Key}/web-pixel-{pixelId}/sandbox/modern/";
                    var url = $"{domain}wpm@{pixelsConfigin.Key}/custom/web-pixel-{pixelId}/sandbox/modern/";
                    try
                    {
                        string content = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
                        if (content.Contains(pixel))
                        {
                            return true;
                        }
                    }
                    catch
                    {
                        urls += url + ",";
                    }

                }
                if (urls.Length > 0)
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_Dan_EMAIL, "CheckIfCustomPixelExist failed (CheckIfCustomPixelExist)", $"Check request urls: {urls}");
                }
            }
            return false;
        }

        public static string FixDomain(string domain)
        {
            if (domain.StartsWith("http://"))
            {
                domain = domain.Replace("http://", "https://");
            }

            string domainWithoutProtocol = domain.ToLower().Replace("https://", "").Replace("http://", "");
            if (domainWithoutProtocol.Contains("/"))
            {
                domainWithoutProtocol = domainWithoutProtocol.Split('/')[0];
                domain = "https://" + domainWithoutProtocol + "/";
            }
            if (domain[domain.Length - 1] != '/')
            {
                domain += "/";
            }
            return domain;
        }

        private static Dictionary<string, List<string>> GetPixelsIDAndWPMs(string script) //GetPixelsConfigsListStrings
        {
            if (script != null)
            {
                Dictionary<string, List<string>> pixelIdAndSandBoxIDs = new Dictionary<string, List<string>>();
                string wpm = GetURLwpm(script);
                if (wpm == null)
                {
                    wpm = "0ceb83c4w1446b2afpf91b3de0m7f69eb89";
                }
                Console.WriteLine($"wpm: {wpm}");
                List<string> pixelIds = GetPixelDs(script);
                if (wpm != null && pixelIds != null)
                {
                    pixelIdAndSandBoxIDs.Add(wpm, pixelIds);
                    return pixelIdAndSandBoxIDs;
                }
            }
            return null;
        }

        private static List<string> GetPixelDs(string script)
        {
            Console.WriteLine($"Getting pixel ids...");
            if (script.Contains("webPixelsConfigList"))
            {
                Console.WriteLine($"webPixelsConfigList was found. Getting ids:");
                List<string> iDs = new List<string>();
                string pattern = @"(?<=webPixelsConfigList:\s*\[)([^[\]]*((?'open'\[)[^[\]]*)+((?'close-open'\])[^[\]]*)+)*(?(open)(?!))";// @"(?<=webPixelsConfigList:\s*\[)([\s\S]*?)\]";
                var match = Regex.Match(script, pattern);
                if (match.Success)
                {
                    string text = match.ToString();
                    string[] splittedText = text.Split('{');
                    var elementsWithCustomScriptID = splittedText.Where(x => x.Contains("\"CUSTOM\"")).ToList();
                    foreach (var el in elementsWithCustomScriptID)
                    {
                        string iDpattern = @"\""id\""\s*:\s*""(\d+)""";
                        string scriptVersion = "@1";
                        match = Regex.Match(el, iDpattern);
                        if (match.Success)
                        {
                            string scriptVersionPattern = @"\""scriptVersion\""\s*:\s*""(\d+)""";
                            var matchVersion = Regex.Match(el, scriptVersionPattern);
                            if (matchVersion.Success)
                            {
                                scriptVersion = $"@{matchVersion.Groups[1].Value}";
                            }
                            var id = match.Groups[1].Value + scriptVersion;
                            if (!iDs.Contains(id))
                            {
                                Console.WriteLine($"id: {id}");
                                iDs.Add(id);
                            }
                        }
                    }
                    return iDs;
                }
            }
            return null;

        }

        private static string GetURLwpm(string script)
        {
            Console.WriteLine("Getting wpm...");
            string[] separators = new string[]
            {
                "{webPixelsManagerAPI.publish(\"page_viewed\", {});}",
                "{webPixelsManagerAPI.publish(\"page_viewed\");}",
            };
            foreach (string separator in separators)
            {
                if (script.Contains(separator))
                {
                    var attributes = script.Split(new string[] { separator }, StringSplitOptions.None)[1];
                    var attList = attributes.Split(',');
                    List<string> wpms = new List<string>();
                    foreach (var att in attList)
                    {
                        if (att.Length > 29 && !att.Contains("https:") && !att.Contains("."))
                        {
                            wpms.Add(att.Replace("\"", ""));
                        }
                    }
                    if (wpms.Where(x => !x.Contains(":")).Any())
                    {
                        return wpms.Where(x => !x.Contains(":")).First();
                    }
                    else
                    {
                        var text = wpms.Where(x => x.Contains("hashVersion")).FirstOrDefault();
                        if (text != null)
                        {
                            return text.Split(':')[1].Replace("}", "").Replace(")", "").Replace(";", "");
                        }
                    }
                }
            }
            return null;
        }

        public static string GetPixelScriptsFromPageHTML(string content)
        {
            Console.WriteLine("Getting pixel scripts from home page...");
            HtmlParser hp = new HtmlParser(content);
            return hp.GetValueFromFirstElement("script", "id", "\"web-pixels-manager-setup\"");
        }

        public static TbUserCreative GetNewUserCreativesFilledByDefaultAds(TrafficBooster tb_settings)
        {
            RSAAdsData rSAAdsData = SearchAdWordsCampaign.GetRSAdsByCategory(tb_settings.TrafficCategoryID ?? 0, tb_settings.Url1, tb_settings.Country);
            return GetNewUserCreativesFilledByDefaultAds(rSAAdsData, tb_settings);
        }
        public static TbUserCreative GetNewUserCreativesFilledByDefaultAds(RSAAdsData rSAAdsData, TrafficBooster tb_settings)
        {
            TbUserCreative tbUserCreatives = new TbUserCreative();
            tbUserCreatives.ShopID = tb_settings.ShopID;
            tbUserCreatives.TbID = tb_settings.ID;

            tbUserCreatives.SearchAd_1_Headline_1 = rSAAdsData.Headline1;
            tbUserCreatives.SearchAd_1_Headline_2 = rSAAdsData.Headline2;
            tbUserCreatives.SearchAd_1_Description = rSAAdsData.Description1;
            tbUserCreatives.SearchAd_1_IsActive = 1;

            tbUserCreatives.SearchAd_2_Headline_1 = rSAAdsData.Headline3;
            tbUserCreatives.SearchAd_2_Headline_2 = rSAAdsData.Headline4;
            tbUserCreatives.SearchAd_2_Description = rSAAdsData.Description2;
            tbUserCreatives.SearchAd_2_IsActive = 1;

            tbUserCreatives.SearchAd_3_Headline_1 = rSAAdsData.Headline5;
            tbUserCreatives.SearchAd_3_Headline_2 = rSAAdsData.Headline6;
            tbUserCreatives.SearchAd_3_Description = rSAAdsData.Description3;
            tbUserCreatives.SearchAd_3_IsActive = 1;

            tbUserCreatives.SearchAd_4_Description = rSAAdsData.Description4;
            tbUserCreatives.SearchAd_Headline_7 = rSAAdsData.Headline7;
            tbUserCreatives.SearchAd_Headline_8 = rSAAdsData.Headline8;
            tbUserCreatives.SearchAd_Headline_9 = rSAAdsData.Headline9;
            tbUserCreatives.SearchAd_Headline_10 = rSAAdsData.Headline10;

            tbUserCreatives.DsaAd_1_Description = rSAAdsData.Description1;
            tbUserCreatives.DsaAd_1_Description2 = rSAAdsData.Description2;
            tbUserCreatives.DsaAd_1_IsActive = 1;

            tbUserCreatives.DsaAd_2_Description = rSAAdsData.Description3;
            tbUserCreatives.DsaAd_2_Description2 = rSAAdsData.Description4;
            tbUserCreatives.DsaAd_2_IsActive = 1;

            tbUserCreatives.Callouts = "Fast Delivery, 24/7 Customer Support, 100% Satisfaction, Special Offers";



            return tbUserCreatives;
        }
        public static TbUserCreative FillTbUserCreativesByAdCopies(int shopID, TbUserCreative tbUserCreative)
        {
            var adCopies = AdCopiesManager.GetAll(shopID);
            if (adCopies != null && adCopies.Count > 0)
            {
                var headers = adCopies.Where(x => x.CopyType == (int)Storeya.Core.Models.CopyType.Header).Select(x => x.Text).ToArray();
                var description = adCopies.Where(x => x.CopyType == (int)Storeya.Core.Models.CopyType.Description).Select(x => x.Text).ToArray();
                var headersLength = headers.Length;
                var descriptionLength = description.Length;
                if (headersLength > 0)
                {
                    tbUserCreative.SearchAd_1_Headline_1 = headers[0];
                    if (headersLength > 1)
                    {
                        tbUserCreative.SearchAd_1_Headline_2 = headers[1];
                    }
                    if (headersLength > 2)
                    {
                        tbUserCreative.SearchAd_2_Headline_1 = headers[2];
                    }
                    if (headersLength > 3)
                    {
                        tbUserCreative.SearchAd_2_Headline_2 = headers[3];
                    }
                    if (headersLength > 4)
                    {
                        tbUserCreative.SearchAd_3_Headline_1 = headers[4];
                    }
                    if (headersLength > 5)
                    {
                        tbUserCreative.SearchAd_3_Headline_2 = headers[5];
                    }
                    if (headersLength > 6)
                    {
                        tbUserCreative.SearchAd_Headline_7 = headers[6];
                    }
                    if (headersLength > 7)
                    {
                        tbUserCreative.SearchAd_Headline_8 = headers[7];
                    }
                    if (headersLength > 8)
                    {
                        tbUserCreative.SearchAd_Headline_9 = headers[8];
                    }
                    if (headersLength > 9)
                    {
                        tbUserCreative.SearchAd_Headline_10 = headers[9];
                    }
                }
                if (descriptionLength > 0)
                {
                    tbUserCreative.SearchAd_1_Description = description[0];
                    tbUserCreative.DsaAd_1_Description = description[0];
                    if (descriptionLength > 1)
                    {
                        tbUserCreative.SearchAd_2_Description = description[1];
                        tbUserCreative.DsaAd_1_Description2 = description[1];
                    }
                    if (descriptionLength > 2)
                    {
                        tbUserCreative.SearchAd_3_Description = description[2];
                        tbUserCreative.DsaAd_2_Description = description[2];
                    }
                    if (descriptionLength > 3)
                    {
                        tbUserCreative.SearchAd_4_Description = description[3];
                        tbUserCreative.DsaAd_2_Description2 = description[3];
                    }
                }
            }
            return tbUserCreative;
        }
        public static string FillInAllCampaignsWithDefaultValuesExceptTheOneSpecified(string type, TrafficBooster existingTbSettings, ref TbUserCreative newAd, SearchCampaignType searchCampaignType, int? index = null, bool changedByUser = true)
        {
            if (existingTbSettings != null)
            {
                List<TbDsaAd> dsaAds_default = TbSettingsHelper.GetDefaultDsaAds(existingTbSettings);

                if (type == "callouts")
                {
                    if (dsaAds_default.Count > 0)
                    {
                        newAd.DsaAd_1_Description = dsaAds_default[0].Description;
                        newAd.DsaAd_1_Description2 = dsaAds_default[0].Description2;
                        newAd.DsaAd_1_IsActive = (dsaAds_default[0].IsActive ? 1 : 0);
                    }

                    if (dsaAds_default.Count > 1)
                    {
                        newAd.DsaAd_2_Description = dsaAds_default[1].Description;
                        newAd.DsaAd_2_Description2 = dsaAds_default[1].Description2;
                        newAd.DsaAd_2_IsActive = (dsaAds_default[1].IsActive ? 1 : 0);
                    }

                    if (dsaAds_default.Count > 2)
                    {
                        newAd.DsaAd_3_Description = dsaAds_default[2].Description;
                        newAd.DsaAd_3_Description2 += dsaAds_default[2].Description2;
                        newAd.DsaAd_3_IsActive = (dsaAds_default[2].IsActive ? 1 : 0);
                    }

                    newAd = AdGroupAdManager.FillOutSeachAdsByDefaultValues(newAd, existingTbSettings, searchCampaignType);

                }
                else if (type == "dsa")
                {
                    if (index == 1)
                    {
                        if (dsaAds_default.Count > 1)
                        {
                            newAd.DsaAd_2_Description = dsaAds_default[1].Description;
                            newAd.DsaAd_2_IsActive = (dsaAds_default[1].IsActive ? 1 : 0);
                        }
                        if (dsaAds_default.Count > 2)
                        {
                            newAd.DsaAd_3_Description = dsaAds_default[2].Description;
                            newAd.DsaAd_3_IsActive = (dsaAds_default[2].IsActive ? 1 : 0);
                        }
                    }
                    else if (index == 2)
                    {
                        if (dsaAds_default.Count > 0)
                        {
                            newAd.DsaAd_1_Description = dsaAds_default[0].Description;
                            newAd.DsaAd_1_IsActive = (dsaAds_default[0].IsActive ? 1 : 0);
                        }
                        if (dsaAds_default.Count > 2)
                        {
                            newAd.DsaAd_3_Description = dsaAds_default[2].Description;
                            newAd.DsaAd_3_IsActive = (dsaAds_default[2].IsActive ? 1 : 0);
                        }
                    }
                    else if (index == 3)
                    {
                        if (dsaAds_default.Count > 0)
                        {
                            newAd.DsaAd_1_Description = dsaAds_default[0].Description;
                            newAd.DsaAd_1_IsActive = (dsaAds_default[0].IsActive ? 1 : 0);
                        }
                        if (dsaAds_default.Count > 1)
                        {
                            newAd.DsaAd_2_Description = dsaAds_default[1].Description;
                            newAd.DsaAd_2_IsActive = (dsaAds_default[1].IsActive ? 1 : 0);
                        }
                    }
                    newAd = AdGroupAdManager.FillOutSeachAdsByDefaultValues(newAd, existingTbSettings, searchCampaignType);
                }
                else if (type == "search")
                {
                    if (dsaAds_default.Count > 0)
                    {
                        newAd.DsaAd_1_Description = dsaAds_default[0].Description;
                        newAd.DsaAd_1_IsActive = (dsaAds_default[0].IsActive ? 1 : 0);
                    }
                    if (dsaAds_default.Count > 1)
                    {
                        newAd.DsaAd_2_Description = dsaAds_default[1].Description;
                        newAd.DsaAd_2_IsActive = (dsaAds_default[1].IsActive ? 1 : 0);
                    }
                    if (dsaAds_default.Count > 2)
                    {
                        newAd.DsaAd_3_Description = dsaAds_default[2].Description;
                        newAd.DsaAd_3_IsActive = (dsaAds_default[2].IsActive ? 1 : 0);
                    }
                }
                if (changedByUser)
                {
                    newAd.UpdatedByUserAt = DateTime.Now;
                }
                else
                {
                    newAd.UpdatedAtAdwordsAt = DateTime.Now;
                }

                return $"The rest of ads were set with default values. {Environment.NewLine}";
            }
            else
            {
                return null;
            }
        }
        public static bool HasRemarketingCode(TrafficBooster tbSettings)
        {
            bool hasRemarketingCode = false;
            if (tbSettings.AdWordsRemarketingCodeStatus == AdWordsRemarketingCodeStatus.Installed.GetHashCode() || tbSettings.AdWordsRemarketingCodeStatus == AdWordsRemarketingCodeStatus.InstalledNotAppBlock.GetHashCode())
            {
                hasRemarketingCode = true;
            }
            else
            {
                AdWordsRemarketingCodeStatus adWordsRemarketingCodeStatus = AdwordsManager.ManageCustomerRemarketingCodeStatus(tbSettings.ShopID, out string message);
                if (adWordsRemarketingCodeStatus == AdWordsRemarketingCodeStatus.Installed || adWordsRemarketingCodeStatus == AdWordsRemarketingCodeStatus.InstalledNotAppBlock)
                {
                    hasRemarketingCode = true;
                }
            }
            return hasRemarketingCode;
        }
        public static AdWordsRemarketingCodeStatus ManageCustomerRemarketingCodeStatus(int? shopId, out string message)
        {
            if (shopId == null)
            {
                message = "There is no shopID";
                return AdWordsRemarketingCodeStatus.Unknown;
            }
            try
            {
                bool customPixelInstalled = false;
                AdWordsRemarketingCodeStatus status = AdWordsRemarketingCodeStatus.NotInstalled;

                message = string.Empty;

                var tb = TrafficBoostersDbHelper.GetSettings(shopId.Value);
                string url = tb.Url1;
                string homePageHtml = string.Empty;
                AdWordsRemarketingCodeStatus currentStatus = AdWordsRemarketingCodeStatus.Unknown;

                if (tb == null)
                {
                    message = "TB not installed";
                    status = AdWordsRemarketingCodeStatus.Unknown;
                }
                else
                {
                    if (tb.AdWordsRemarketingCodeStatus.HasValue)
                    {
                        currentStatus = (AdWordsRemarketingCodeStatus)tb.AdWordsRemarketingCodeStatus;
                    }
                    if (string.IsNullOrEmpty(tb.AdWordsPixel))
                    {
                        message = "TB AdWordsPixel is empty!";
                        status = AdWordsRemarketingCodeStatus.Unknown;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(url) && currentStatus != AdWordsRemarketingCodeStatus.GiveUp)
                        {
                            ConversionData conversionData = AdWordsPixelParser.ExtractGoogleConversionID_FromOldPixel(tb.AdWordsPixel, shopId, "ManageCustomerRemarketingCodeStatus");
                            try
                            {
                                homePageHtml = ShopifyCrawler.GetContent(url);
                                if (homePageHtml.Length > 0 && homePageHtml[0] == '\u001f')
                                {
                                    homePageHtml = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url, true, shopId);
                                }
                            }
                            catch
                            {
                                homePageHtml = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url, true, shopId);
                            }


                            if (string.IsNullOrEmpty(homePageHtml))
                            {
                                status = AdWordsRemarketingCodeStatus.Failed;
                                message = $"Cannot access url:{url}";
                            }
                            else
                            {
                                if (homePageHtml.Contains("var google_remarketing_only = true") || homePageHtml.Contains("gtag('config', 'AW-"))  // as long as productPageHtml is not provided
                                {
                                    status = AdWordsRemarketingCodeStatus.NotInstalled;
                                    if (homePageHtml.Contains(conversionData.conversion_id))
                                    {
                                        status = AdWordsRemarketingCodeStatus.Installed;
                                        if (!homePageHtml.Contains("//StoreYa Google Ads app extension pixel applied"))
                                        {
                                            status = AdWordsRemarketingCodeStatus.InstalledNotAppBlock;
                                        }
                                    }
                                    if (status != AdWordsRemarketingCodeStatus.Installed && AdwordsManager.FindCustomShopifyAwPixelUsingHTML(homePageHtml, conversionData.conversion_id, url))
                                    {
                                        //Log4NetLogger.InfoWithDB($"Custom pixel was found on the page", null, shopId);
                                        status = AdWordsRemarketingCodeStatus.Installed;
                                        customPixelInstalled = true;
                                    }

                                }
                                else if (AdwordsManager.FindCustomShopifyAwPixelUsingHTML(homePageHtml, conversionData.conversion_id, url))
                                {
                                    //Log4NetLogger.InfoWithDB($"Custom pixel was found on the page", null, shopId);
                                    status = AdWordsRemarketingCodeStatus.Installed;
                                    customPixelInstalled = true;
                                }
                            }

                        }
                        else
                        {
                            message = $"cannot access TB Url1";
                            if (currentStatus == AdWordsRemarketingCodeStatus.GiveUp)
                            {
                                message = $"Status is GiveUp";
                            }
                        }
                    }
                }
                if (customPixelInstalled)
                {
                    if (AdwordsManager.ChangePixelStatuses(shopId.Value, AdWordsRemarketingCodeStatus.Installed, AdWordsPixelStatuses.PixelReporting))
                    {
                        ConsoleAppHelper.WriteLogDebugWithDB($"Custom pixel was found", shopId);
                    }
                }
                else if (currentStatus != status)
                {
                    ConsoleAppHelper.WriteLogDebugWithDB($"Update AdWordsRemarketingCodeStatus from: {currentStatus} to: {status}. {message}", shopId);
                    TrafficBoostersDbHelper.UpdateTbRemarketingPixelStatus(shopId.Value, status);
                    //using (StoreYaEntities db = new StoreYaEntities())
                    //{

                    //    var tbu = db.TrafficBoosters.FirstOrDefault(t => t.ID == tb.ID);
                    //    tbu.AdWordsRemarketingCodeStatus = status.GetHashCode();
                    //    db.SaveChanges();
                    //}
                }
                return status;
            }
            catch (Exception ex)
            {
                message = $"{shopId} - Failed to set remarketing code status: {ex.Message}";
                ConsoleAppHelper.WriteErrorWithDB($"Failed to Update AdWordsRemarketingCodeStatus {ex.Message}", ex, shopId.Value);
                return AdWordsRemarketingCodeStatus.Failed;
            }
        }
        public static UploadedAccountInfo FillUploadedAccountInfo(SystemEvent item)
        {
            var db = new StoreYaEntities();
            UploadedAccountInfo a = new UploadedAccountInfo();
            try
            {
                var shopID = item.ShopID.Value;
                var tb = TrafficBoostersDbHelper.GetSettings(shopID);
                var shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
                var user = db.Users.Where(s => s.ID == shop.UserID).SingleOrDefault();
                var ga = db.GAConnectedAccountsStats.Where(g => g.ShopID == shopID).SingleOrDefault();
                a.ShopID = item.ShopID.Value;
                a.Platform = (shop.CatalogSourcePlatform == null) ? "" : ((CatalogSourcePlatforms)shop.CatalogSourcePlatform).ToString();
                if (shop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi)
                {
                    var shopifyShop = db.ShopifyConnectedShops.Where(s => s.ShopID == shopID && s.StoreyaAppTypeID == (int)Shopify_StoreyaApp.TrafficBooster).SingleOrDefault();
                    if (shopifyShop != null)
                    {
                        a.PlatformTransactions = (shopifyShop.OrdersAmount ?? 0).ToString();
                    }
                }
                if (user.UserType != null)
                {
                    a.UserType = ((UserTypes)user.UserType).ToString();
                }
                if (ga != null)
                {
                    a.GaRevenue30Days = (ga.Revenue ?? 0).ToString();
                }
                if (tb == null)
                {
                    a.Url = AddSpaceBeforeExtension(shop.BaseUrl);
                    a.Plan = $"TB Was canceled ";
                }
                else
                {
                    a.Plan = $"${TbSettingsHelper.GetBillingAmount(tb)}";
                    a.Url = AddSpaceBeforeExtension(tb.Url1);
                    a.AccountId = string.IsNullOrEmpty(tb.AdWordsAccount) ? string.Empty : tb.AdWordsAccount.ConvertToAWAccountString();
                }

                var referral = db.UsersReferedFroms.Where(r => r.UserID == user.ID).SingleOrDefault();
                if (referral != null && referral.CalculatedSource != null)
                {
                    a.TrafficSource = referral.CalculatedSource;
                }

                a.UserAge = GetAgeInDays(user.InsertedAt);

            }
            catch (Exception ex)
            {
                a.Plan = "$0 - Exception- " + ex.ToString();
            }
            return a;
        }
        private static string AddSpaceBeforeExtension(string url)
        {
            if (!string.IsNullOrEmpty(url) && Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                Uri uri = new Uri(url); ;
                string domain = uri.Authority;
                int lastDotIndex = domain.LastIndexOf('.');
                if (lastDotIndex != -1 && lastDotIndex < domain.Length - 1)
                {
                    string newDomain = domain.Insert(lastDotIndex, " ");
                    // Insert a space before the dot
                    url = url.Replace(domain, newDomain);
                }
            }
            return url;
        }
        private static string GetAgeInDays(DateTime? insertedAt)
        {
            if (insertedAt != null)
            {
                DateTime now = DateTime.Now;
                TimeSpan age = now - insertedAt.Value;
                return $"{age.Days} days";
            }
            return "";
        }
    }

    public class CdrmStuff
    {
        public bool hasRemarketingCodeOnHomePage;
        public bool hasConversionIdGivenByStoreya;
        public string message;
        public string googleConversionIdGivenByStoreya;
    }

    public class TbDsaAd
    {
        public string Description { get; set; }
        public string Description2 { get; set; }
        public bool IsActive { get; set; }
    }

    public class TbSearchAd
    {
        public string Headline_1 { get; set; }
        public string Headline_2 { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
    }
    public class UploadedAccountInfo
    {
        //No.	ShopID	Paid Plan	Revenues 30 days	stars in our BO	Platform	Category	URL
        public int ShopID { get; set; }
        public string Url { get; set; }

        public string Plan { get; set; }

        public string Platform { get; set; }

        public string GaRevenue30Days { get; set; }
        public string UserAge { get; set; }

        public string PlatformTransactions { get; set; }

        public string UserType { get; set; }
        public string TrafficSource { get; set; }
        public string AccountId { get; set; }
    }
}
