﻿using LinqToTwitter;
using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{

    public class AdGroupCriteria
    {
        public AdGroupCriteria()
        {
            DynamicAdTargetCondition1 = "URL";
            DynamicAdTargetCondition2 = "None";
            DynamicAdTargetCondition3 = "None";
        }

        public string DynamicAdTargetValue1 { get; set; }

        public string DynamicAdTargetCondition1 { get; set; }

        public string DynamicAdTargetCondition2 { get; set; }

        public string DynamicAdTargetCondition3 { get; set; }

    }

    public class AdGroupAdManager
    {

        public static List<AdGroupAd> SetAdGroupAds(string adwordsLanguageCode, bool isServiceProvider, int? trafficCategoryID = null, string domain = null, int? shopID = null)
        {
            List<AdGroupAd> list = new List<AdGroupAd>();

            AdGroupAd firstAd = new AdGroupAd();
            AdGroupAd secondAd = null;
            AdGroupAd thirdAd = null;


            if (adwordsLanguageCode == "de")
            {
                firstAd.DescriptionLine1 = "Preise wurden um 10 % reduziert.";
                firstAd.DescriptionLine2 = "Neue Waren sind da. Jetzt shoppen!";
            }
            else if (adwordsLanguageCode == "nl")
            {
                firstAd.DescriptionLine1 = "Alle prijzen met 10% korting.";
                firstAd.DescriptionLine2 = "Koop nu de nieuwste collectie!";
            }
            else if (adwordsLanguageCode == "ja")
            {
                firstAd.DescriptionLine1 = "全価格が10％低下";
                firstAd.DescriptionLine2 = "新着は到着したばかり。";
            }
            else if (adwordsLanguageCode == "fr")
            {
                firstAd.DescriptionLine1 = "Réduction de 10 % sur tous les prix";
                firstAd.DescriptionLine2 = "Nouveaux arrivages. Achetez vite!";
            }
            else if (adwordsLanguageCode == "it")
            {
                firstAd.DescriptionLine1 = "Tutti i prezzi ridotti del 10%";
                firstAd.DescriptionLine2 = "Ci sono i nuovi arrivi. Compra ora!";
            }
            else if (adwordsLanguageCode == "es")
            {
                firstAd.DescriptionLine1 = "Todos los precios reducidos un 10 %";
                firstAd.DescriptionLine2 = "Las últimas novedades. ¡Compra ya!";
            }
            else
            {
                if (isServiceProvider)
                {
                    firstAd.DescriptionLine1 = "Amazing Service, Affordable Rates.";
                    firstAd.DescriptionLine2 = "Check Out Our Site Now & Learn More";
                }
                else
                {
                    if (!trafficCategoryID.HasValue)
                    {
                        firstAd.DescriptionLine1 = "All Prices Were Reduced by 10%";
                        firstAd.DescriptionLine2 = "New Arrivals Just Got In. Shop Now!";
                    }
                    else
                    {
                        SetDescriptionByCategoryNew(trafficCategoryID.Value, ref firstAd, ref secondAd, ref thirdAd, domain);
                        //SetDescriptionByCategory(trafficCategoryID.Value, ref firstAd, ref secondAd, ref thirdAd);
                    }
                }
            }

            if (shopID != null)
            {
                var customAds = AdGroupAdManager.CreateDSAAdGroupAdsFromAdCopies(shopID.Value, out string error);
                if (customAds != null)
                {
                    firstAd = customAds[0];
                    secondAd = customAds[1];
                }
            }


            list.Add(firstAd);

            if (secondAd != null)
                list.Add(secondAd);

            if (thirdAd != null)
                list.Add(thirdAd);

            return list;
        }

        public static List<SearchAd> GetSearchAdsNew(int trafficCategoryID, string domain = null)
        {
            List<SearchAd> searcgAds = new List<SearchAd>();
            SearchAd bestAd = new SearchAd() { Name = "Best Ad" };
            SearchAd qualityAd = new SearchAd() { Name = "Quality Ad" };
            SearchAd discountAd = new SearchAd() { Name = "Discount Ad" }; //is a customized ad that, for now, requires a manual change before activating it automatically

            Storeya.Core.Helpers.TbCategoriesHelper.Category category = TbCategoriesHelper.GetCategory((int)BusinessTypes.OnlineStore, trafficCategoryID);

            if (category != null && category.ID == 515) //Cosmetics
            {
                bestAd.HeadlinePart1 = "{KeyWord:Get the skin you want}";
                bestAd.HeadlinePart2 = "Top Skin Care Products";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Get the best cosmetics products online.";
                bestAd.Description2 = "We Care for Your Skin, It Deserves The Best. Get yours today!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:High Quality Cosmetic Products}";
                qualityAd.HeadlinePart3 = "High Quality Skin Care";
                qualityAd.Description = "{domain}© Official Store - High Quality Skin Care Products Made For You.";
                qualityAd.Description2 = "The Newest & Best Quality, We've Got You Covered - Shop Today!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Get the best cosmetics at the best prices online.";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            if (category != null && category.ID == 521) //Beauty - Cosmetics
            {
                bestAd.HeadlinePart1 = "{KeyWord: We've Got You Covered}";
                bestAd.HeadlinePart2 = "Top Beauty Products For You ";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - The Hottest New Products in Beauty";
                bestAd.Description2 = "Embrace The Fun Side of Beauty, Shop Our Collection Now!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord: Top Quality Products}";
                qualityAd.HeadlinePart3 = "High Quality Beauty Products";
                qualityAd.Description = "{domain}© Official Store - Indulge Your Addiction With Our Premium Beauty Products";
                qualityAd.Description2 = "The Newest & Best Quality, We've Got You Covered - Shop Today!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Shop Now and Quickly Purchase Your Favorite Products at Surprising Prices.";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            if (category != null && category.ID == 522) //Skin care - Cosmetics
            {
                bestAd.HeadlinePart1 = "{KeyWord:Best Skin Care Products}";
                bestAd.HeadlinePart2 = "Top Skin Care Products";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Enjoy a Large Variety Of Products Made For You. ";
                bestAd.Description2 = "Browse Our Collection Of Skin Care Products For All Skin Types.";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:High Quality Skin Care Items}";
                qualityAd.HeadlinePart3 = "High Quality Skin Care";
                qualityAd.Description = "{domain}© Official Store - Discover High Quality Skin Care Products For All Skin Types.";
                qualityAd.Description2 = "The Newest & Best Quality, We've Got You Covered - Shop Today!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Enjoy a {xx}% discount on our skin care products made of gentle formulas for all skin types";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 502) //Electronics & Computers
            {
                bestAd.HeadlinePart1 = "{KeyWord:Technology Today}";
                bestAd.HeadlinePart2 = "Top Products For a Tech Lover ";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Shop The Latest Trends in Gadgets and Technology";
                bestAd.Description2 = "All The New and Innovative Products on the Market Right Here, Shop Now";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord: High Quality Products}";
                qualityAd.HeadlinePart3 = "High-End Quality Electronics";
                qualityAd.Description = "{domain}© Official Store - High-End Electronics & Gadgets. 100% Satisfaction Guaranteed!";
                qualityAd.Description2 = "Shop Our Top Quality and Innovative Products Now";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - The Newest and Most Innovative Technology on the Market Today, Now With a 10% Discount";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 503) //Fashion & Accessories
            {
                bestAd.HeadlinePart1 = "{KeyWord:Fashion Exactly As It Should}";
                bestAd.HeadlinePart2 = "Top Fashion Made For You";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Get the best fashion exactly as you want.";
                bestAd.Description2 = "Limited Stock Available - What Are You Waiting For? Get Yours Today!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:High Quality Fashion Products}";
                qualityAd.HeadlinePart3 = "High Quality Designs";
                qualityAd.Description = "{domain}© Official Store - High Quality Fashion Designs Made For You.";
                qualityAd.Description2 = "Limited Stock Available - What Are You Waiting For? Get Yours Today!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Get the best fashion at the best prices exactly as you want.";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 516) //Fashion - Men's
            {
                bestAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                bestAd.HeadlinePart2 = "Top Men's Fashion";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Get The Latest Men's Fashion and Style Trends. ";
                bestAd.Description2 = "The Best Place to Upgrade Your Wardrobe. 100% Satisfaction Guaranteed, Shop Now!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:Trendy Men's Fashion}";
                qualityAd.HeadlinePart3 = "Top Quality Designs";
                qualityAd.Description = "{domain}© Official Store - Shop Our High Quality Men's Clothing & Accessories.";
                qualityAd.Description2 = "Get This Season's Latest Arrivals, Shop Our Collection Now!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your Purchase";
                discountAd.Description = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Trendy Mens Fashion Designs";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 517) //Fashion - Women's
            {
                bestAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                bestAd.HeadlinePart2 = "Top Women's Fashion";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Shop Our Trendy Women's Fashions. 100% Satisfaction Guaranteed.";
                bestAd.Description2 = "On Trend Pieces You Need To Add To Your Closet.  Large Variety, Fast Deliver, Shop Now!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:Trendy Women's Fashion}";
                qualityAd.HeadlinePart3 = "Premium Quality Designs";
                qualityAd.Description = "{domain}© Official Store - Shop Our Stunning Range of Top Quality Womenswear & Accessories";
                qualityAd.Description2 = "Get This Season's Latest Arrivals, Shop Our Collection Now!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Trendy Women's Fashion Designs";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 518) //Fashion - Teen
            {
                bestAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                bestAd.HeadlinePart2 = "Top Teen Fashion";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Get The Latest In Fashion and Style Trends. ";
                bestAd.Description2 = "Get The Latest Teen Fashion Trends, Shop Our Collection Now!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:Trendy Teen Fashion}";
                qualityAd.HeadlinePart3 = "Check Out Our High Quality Designs";
                qualityAd.Description = "{domain}© Official Store - Top Quality Teen Clothes & Accessories. ";
                qualityAd.Description2 = "Get This Season's Latest Arrivals, Shop Our Collection Now!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Trendy Teen Fashion Designs";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 519) //Fashion - Kids
            {
                bestAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                bestAd.HeadlinePart2 = "Top Kid's Fashion";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";
                bestAd.Description2 = "Trendy & Easy To Wear. Shop Now!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:Trendy Kids Fashion}";
                qualityAd.HeadlinePart3 = "Shop Top Quality Apparel";
                qualityAd.Description = "{domain}© Official Store - Top Quality Children's Clothes & Accessories. ";
                qualityAd.Description2 = "A Unique and Varied Selection of Kids High Quality Fashions.  Shop Now!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Trendy Kids Fashion Designs";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 520) //Fashion - Babies
            {
                bestAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                bestAd.HeadlinePart2 = "Top Baby Fashion";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";
                bestAd.Description2 = "Dressing Your Little One In The Latest Baby Fashions";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:Trendy Babies Fashion}";
                qualityAd.HeadlinePart3 = "Top Quality Materials";
                qualityAd.Description = "{domain}© Official Store - Top Quality Baby Clothes & Accessories, Shop Now!";
                qualityAd.Description2 = "Shop a Wide Selection of Premium Quality Baby Clothes.";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Trendy  Baby Fashion Designs";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 506) //Health & Beauty
            {
                bestAd.HeadlinePart1 = "{KeyWord:Health and Wellness}";
                bestAd.HeadlinePart2 = "Live Your Best Life";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Live Your Healthiest Life Every Day";
                bestAd.Description2 = "Health and Beauty Products Custom Made For Your Personal Needs. Buy Today";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord: A Healthy You}";
                qualityAd.HeadlinePart3 = "High Quality Health & Beauty ";
                qualityAd.Description = "{domain}© Official Store - Health & Beauty Quality Products To Treat And Pamper Yourself.";
                qualityAd.Description2 = "Promote A Healthy Lifestyle With The Highest Quality Products";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Shop Now and Take Advantage of Our Discount to You!";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 507) //Home & Garden
            {
                bestAd.HeadlinePart1 = "{KeyWord:Best Home & Outdoor Design}";
                bestAd.HeadlinePart2 = "We Have What Your Home Needs";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Your First Stop For The Best Interior Design";
                bestAd.Description2 = "Discover Inspirational Top Interior Designs For Your Home!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord: High Quality Home Design Items}";
                qualityAd.HeadlinePart3 = "Quality Designs, Best Prices";
                qualityAd.Description = "{domain}© Official Store - Shop Our High Quality Homes Interior & Outdoor Designs.";
                qualityAd.Description2 = "Find Everything You Need To Style Your Home Your Way, Shop Now!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Find Stylish Discounted Home Decor Solutions For Every Room Of Your House And Outdoor.";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 508) //Jewelry
            {
                bestAd.HeadlinePart1 = "{KeyWord:Top Unique Jewelry Design}";
                bestAd.HeadlinePart2 = "High-End Jewelry Designs";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Browse From Our Unique Top Design Collection Of Jewelry";
                bestAd.Description2 = "Discover Our Unique Range Of Top Jewelry To Match Your Style & Personality!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:High Quality Jewelry That Inspires You}";
                qualityAd.HeadlinePart3 = "Shop High Quality Jewelry";
                qualityAd.Description = "{domain}© Official Store - Enjoy High Quality Jewelry That Will Be Treasured Always.";
                qualityAd.Description2 = "Shop Our Wide Selection Of Jewelry & Discover A Range Of Fabulous And Unique Designs.";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Shop Now and Quickly Purchase Your Favorite Products at Surprising Prices.";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 510) //Pet Shop
            {
                bestAd.HeadlinePart1 = "{KeyWord: Quality Products For Your Pet}";
                bestAd.HeadlinePart2 = "Best Products For Happier Pets";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Get The Best Pet Supplies, Products & Accessories For Your Pet.";
                bestAd.Description2 = "Find The Best Gear To Keep Your Pet Healthy, Groomed and Happy. Shop Now!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord:Quality Products For Your Pet}";
                qualityAd.HeadlinePart3 = "High Quality Supplies For Pets";
                qualityAd.Description = "{domain}© Official Store - Pamper Your Pet With Top Quality Supplies, & Accessories. ";
                qualityAd.Description2 = "Find A Wide Range Of High Quality Products Especially For Your Pets Needs. ";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Shop For All Your Pet Needs, Enjoy Discounted Products And Keep Your Pet Happy & Groomed";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 512) //Sports & Outdoors
            {
                bestAd.HeadlinePart1 = "{Keyword: Live an Active Lifestyle}";
                bestAd.HeadlinePart2 = "Get Moving";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - We Have Everything You Need For Your Active Lifestyle";
                bestAd.Description2 = "Get Moving and Visit Our Site For All That You Need, Shop Now!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord: Enhance Your Performance}";
                qualityAd.HeadlinePart3 = "Quality Sport & Outdoor Items";
                qualityAd.Description = "{domain}© Official Store - Discover A Range Of High Quality Sports & Outdoors Products!";
                qualityAd.Description2 = "We Make Quality Our Top Priority, You Get 100% Satisfaction Guaranteed!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Limited Time Sale On Our Sports & Outdoor Equipment. Shop Now!";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 513) //Toys, Kids & Babies
            {
                bestAd.HeadlinePart1 = "{KeyWord:Best Toys At Any Age}";
                bestAd.HeadlinePart2 = "Stuff Your Little Ones Love";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Shop Our Incredible Range Of Products";
                bestAd.Description2 = "Guaranteed To Keep Your Little One Busy and Happy.";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{KeyWord: Top Quality Products}";
                qualityAd.HeadlinePart3 = "Premium Quality Designs";
                qualityAd.Description = "{domain}© Official Store - High Quality Toys & Accessories Perfect For Your Little One.";
                qualityAd.Description2 = "Enjoy The Best Toys For Kids At Every Age. ";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Toys For Your Little One";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else
            {
                bestAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                bestAd.HeadlinePart2 = "New Items Just Added, Shop Now";
                bestAd.HeadlinePart3 = "{domain}© Official";
                bestAd.Description = "{domain}© Official Store - Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";
                bestAd.Description2 = "We Have Everything You Need, Shop Our Collection Now!";

                qualityAd.HeadlinePart1 = "{domain}© Official";
                qualityAd.HeadlinePart2 = "{Keyword: All You Need}";
                qualityAd.HeadlinePart3 = "Quality Products, Best Prices";
                qualityAd.Description = "{domain}© Official Store - High Quality Products.  100% Satisfaction Guaranteed.";
                qualityAd.Description2 = "We Make Quality Our Top Priority, You Get 100% Satisfaction Guaranteed!";

                discountAd.HeadlinePart1 = "{xx}% Off The Entire Store";
                discountAd.HeadlinePart2 = "{domain}© Official";
                discountAd.HeadlinePart3 = "Get {xx}% Off Your purchase";
                discountAd.Description = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Products Today!";
                discountAd.Description2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }


            //'Best ad' and 'Quality ad'
            if (!string.IsNullOrEmpty(domain) && domain.Length > 15)
            {
                int index = domain.IndexOf(".", StringComparison.CurrentCulture);
                if (index > 0)
                    domain = domain.Remove(index);
            }

            if (!string.IsNullOrEmpty(domain))
            {
                // If url is marketplace - remove all part - "{domain}© Official Store - 
                bool isMarketplace = MarketplaceUrlHelper.IsMarketplace(domain.ToLower());

                if (bestAd.HeadlinePart3.Contains("{domain}©"))
                {
                    bestAd.HeadlinePart3 = bestAd.HeadlinePart3.Replace("{domain}", domain);
                }
                if (bestAd.Description.Contains("{domain}©"))
                {
                    if (isMarketplace)
                    {
                        bestAd.Description = bestAd.Description.Replace("{domain}© Official Store - ", "");
                    }
                    else
                    {
                        bestAd.Description = bestAd.Description.Replace("{domain}", domain);
                    }
                }

                if (qualityAd.HeadlinePart1.Contains("{domain}©"))
                {
                    qualityAd.HeadlinePart1 = qualityAd.HeadlinePart1.Replace("{domain}", domain);
                }
                if (qualityAd.Description.Contains("{domain}©"))
                {
                    if (isMarketplace)
                    {
                        qualityAd.Description = qualityAd.Description.Replace("{domain}© Official Store - ", "");
                    }
                    else
                    {
                        qualityAd.Description = qualityAd.Description.Replace("{domain}", domain);
                    }
                }

                if (discountAd.HeadlinePart2.Contains("{domain}©"))
                {
                    discountAd.HeadlinePart2 = discountAd.HeadlinePart2.Replace("{domain}", domain);
                }
                if (discountAd.Description.Contains("{domain}©"))
                {
                    if (isMarketplace)
                    {
                        discountAd.Description = discountAd.Description.Replace("{domain}© Official Store - ", "");
                    }
                    else
                    {
                        discountAd.Description = discountAd.Description.Replace("{domain}", domain);
                    }
                }
            }

            //https://developers.google.com/adwords/api/docs/guides/expanded-text-ads

            // headline contains 30 characters
            int headlineMaxLength = 30;

            if (bestAd.HeadlinePart1.Length > headlineMaxLength)
            {
                bestAd.HeadlinePart1 = Optimize(bestAd.HeadlinePart1, headlineMaxLength);
            }
            if (bestAd.HeadlinePart2.Length > headlineMaxLength)
            {
                bestAd.HeadlinePart2 = Optimize(bestAd.HeadlinePart2, headlineMaxLength);
            }
            if (bestAd.HeadlinePart3.Length > headlineMaxLength)
            {
                bestAd.HeadlinePart3 = Optimize(bestAd.HeadlinePart3, headlineMaxLength);
            }

            if (qualityAd.HeadlinePart1.Length > headlineMaxLength)
            {
                qualityAd.HeadlinePart1 = Optimize(qualityAd.HeadlinePart1, headlineMaxLength);
            }
            if (qualityAd.HeadlinePart2.Length > headlineMaxLength)
            {
                qualityAd.HeadlinePart2 = Optimize(qualityAd.HeadlinePart2, headlineMaxLength);
            }
            if (qualityAd.HeadlinePart3.Length > headlineMaxLength)
            {
                qualityAd.HeadlinePart3 = Optimize(qualityAd.HeadlinePart3, headlineMaxLength);
            }

            if (discountAd.HeadlinePart1.Length > headlineMaxLength)
            {
                discountAd.HeadlinePart1 = Optimize(discountAd.HeadlinePart1, headlineMaxLength);
            }
            if (discountAd.HeadlinePart2.Length > headlineMaxLength)
            {
                discountAd.HeadlinePart2 = Optimize(discountAd.HeadlinePart2, headlineMaxLength);
            }
            if (discountAd.HeadlinePart3.Length > headlineMaxLength)
            {
                discountAd.HeadlinePart3 = Optimize(discountAd.HeadlinePart3, headlineMaxLength);
            }

            //Note: Starting in v201809, a second descriptions is available. The character limit for both descriptions lines has been increased to 90.
            int maxLength = 90;
            if (bestAd.Description.Length > maxLength)
            {
                bestAd.Description = Optimize(bestAd.Description, maxLength);
            }
            if (bestAd.Description2.Length > maxLength)
            {
                bestAd.Description2 = Optimize(bestAd.Description2, maxLength);
            }
            if (qualityAd.Description.Length > maxLength)
            {
                qualityAd.Description = Optimize(qualityAd.Description, maxLength);
            }
            if (qualityAd.Description2.Length > maxLength)
            {
                qualityAd.Description2 = Optimize(qualityAd.Description2, maxLength);
            }

            if (discountAd.Description.Length > maxLength)
            {
                discountAd.Description = Optimize(discountAd.Description, maxLength);
            }
            if (discountAd.Description2.Length > maxLength)
            {
                discountAd.Description2 = Optimize(discountAd.Description2, maxLength);
            }

            bestAd.AdGroupAdStatus = 0;     // AdGroupAdStatus.ENABLED;  0
            qualityAd.AdGroupAdStatus = 0;
            discountAd.AdGroupAdStatus = 1; //AdGroupAdStatus.PAUSED = 1

            searcgAds.Add(bestAd);
            searcgAds.Add(qualityAd);
            searcgAds.Add(discountAd);

            return searcgAds;
        }

        private static string Optimize(string dataToOptimize, int maxLength)
        {
            int optimizeTo = maxLength;
            string optimized = dataToOptimize;

            if (optimized.Length > maxLength)
            {
                if (optimized.Contains("official store - "))
                {
                    optimized = optimized.Replace("official store ", "").Trim();

                    if (optimized.StartsWith("- "))
                    {
                        optimized = optimized.Replace("- ", "").Trim();
                    }
                }
            }

            if (optimized.Length > maxLength)
            {
                bool addCurlyBracket = false;
                if (optimized.EndsWith("}"))
                {
                    optimizeTo = optimizeTo - 1;
                    addCurlyBracket = true;
                }
                optimized = AdwordsManager.DecreaseToRequiredLengthNew(optimized, optimizeTo);
                if (addCurlyBracket)
                    optimized = optimized + "}";
            }

            return optimized;
        }

        public static List<SearchAd> GetSearchAds(int trafficCategoryID)
        {
            List<SearchAd> descriptions = new List<SearchAd>();
            SearchAd fisrtAd = new SearchAd();
            SearchAd secondAd = new SearchAd();
            SearchAd thirdAd = new SearchAd();

            Storeya.Core.Helpers.TbCategoriesHelper.Category category = TbCategoriesHelper.GetCategory((int)BusinessTypes.OnlineStore, trafficCategoryID);

            if (category != null && category.ID == 515)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:10% Off All Cosmetic Products}";
                fisrtAd.HeadlinePart2 = "Top Skin Care Products";
                fisrtAd.Description = "We Care for Your Skin & Commit to Your Full Satisfaction. Check Us Out Now!";

                secondAd.HeadlinePart1 = "{KeyWord:Cosmetics That Vitalize}";
                secondAd.HeadlinePart2 = "Protect Your Skin";
                secondAd.Description = "Our Cosmetic Products Will Give You The Look You Are Searching For. More Here.";

                thirdAd.HeadlinePart1 = "{KeyWord:Unique Cosmetic Products}";
                thirdAd.HeadlinePart2 = "10% Off All Collection";
                thirdAd.Description = "Our Unique Cosmetic Products Protects Your Skin. Satisfaction Guaranteed!";
            }
            else if (category != null && category.ID == 502)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                fisrtAd.HeadlinePart2 = "Lowest Prices Guaranteed";
                fisrtAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";

                secondAd.HeadlinePart1 = "{KeyWord:10% Off All Our Collections}";
                secondAd.HeadlinePart2 = "Check Out Our Top Products";
                secondAd.Description = "High-End Electronics & Gadgets. 100% Satisfaction Guaranteed!";

                thirdAd.HeadlinePart1 = "{KeyWord:Crazy Sale - 10% Off All Store}";
                thirdAd.HeadlinePart2 = "Don't Miss - Limited Time Sale";
                thirdAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";
            }
            else if (category != null && category.ID == 503)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                fisrtAd.HeadlinePart2 = "Check Out Our Collection";
                fisrtAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";

                secondAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                secondAd.HeadlinePart2 = "New Arrivals Just Added";
                secondAd.Description = "High Quality Clothes & Accessories. Shop Our Collection!";

                thirdAd.HeadlinePart1 = "{KeyWord:Trendy Fashionable Designs}";
                thirdAd.HeadlinePart2 = "View Our Collection";
                thirdAd.Description = "New Arrivals Just Added, Shop Now!";
            }
            else if (category != null && category.ID == 516)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                fisrtAd.HeadlinePart2 = "Top Men's Fashion";
                fisrtAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";

                secondAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                secondAd.HeadlinePart2 = "New Arrivals Just Added";
                secondAd.Description = "Top Quality Men's Clothes & Accessories. Shop Our Collection!";

                thirdAd.HeadlinePart1 = "{KeyWord:Trendy Fashionable Designs}";
                thirdAd.HeadlinePart2 = "Check Out Our Men's Collection";
                thirdAd.Description = "Top Quality Men's Clothes & Accessories. New Arrivals Just Added, Shop Now!";
            }
            else if (category != null && category.ID == 517)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                fisrtAd.HeadlinePart2 = "Top Women's Fashion";
                fisrtAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";

                secondAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                secondAd.HeadlinePart2 = "New Arrivals Just Added";
                secondAd.Description = "Top Quality Women's Clothes & Accessories. Shop Our Collection!";

                thirdAd.HeadlinePart1 = "{KeyWord:Trendy Fashionable Designs}";
                thirdAd.HeadlinePart2 = "Check Out Women's Collection"; // "Check Out Our Women's Collection";
                thirdAd.Description = "Top Quality Women's Clothes & Accessories. New Arrivals Just Added, Shop Now!";
            }
            else if (category != null && category.ID == 518)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                fisrtAd.HeadlinePart2 = "Top Teen Fashion";
                fisrtAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";

                secondAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                secondAd.HeadlinePart2 = "New Arrivals Just Added";
                secondAd.Description = "Top Quality Teen Clothes & Accessories. Shop Our Collection!";

                thirdAd.HeadlinePart1 = "{KeyWord:Trendy Fashionable Designs}";
                thirdAd.HeadlinePart2 = "Check Out Our Teen Collection";
                thirdAd.Description = "Top Quality Teen Clothes & Accessories. New Arrivals Just Added, Shop Now!";
            }
            else if (category != null && category.ID == 519)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                fisrtAd.HeadlinePart2 = "Top Kid's Fashion";
                fisrtAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";

                secondAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                secondAd.HeadlinePart2 = "New Arrivals Just Added";
                secondAd.Description = "Top Quality Children's Clothes & Accessories. Shop Our Collection!";

                thirdAd.HeadlinePart1 = "{KeyWord:Trendy Fashionable Designs}";
                thirdAd.HeadlinePart2 = "Check Out Our Kids Collection";
                thirdAd.Description = "Top Quality Children's Clothes & Accessories. New Arrivals Just Added, Shop Now!";
            }
            else if (category != null && category.ID == 520)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                fisrtAd.HeadlinePart2 = "Top Baby Fashion";
                fisrtAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";

                secondAd.HeadlinePart1 = "{KeyWord:Amazing Sale}";
                secondAd.HeadlinePart2 = "New Arrivals Just Added";
                secondAd.Description = "Top Quality Baby Clothes & Accessories. Shop Our Collection!";

                thirdAd.HeadlinePart1 = "{KeyWord:Trendy Fashionable Designs}";
                thirdAd.HeadlinePart2 = "Check Out Our Baby Collection";
                thirdAd.Description = "Top Quality Baby Clothes & Accessories. New Arrivals Just Added, Shop Now!";
            }

            else if (category != null && category.ID == 506)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Keep Your Health With Us}";
                fisrtAd.HeadlinePart2 = "Our Services Are Here To Help";
                fisrtAd.Description = "Check Out Our Site To See Our Services & Products.";

                secondAd.HeadlinePart1 = "{KeyWord:Quality Health&Beauty Services}";
                secondAd.HeadlinePart2 = "Health Without Compromise";
                secondAd.Description = "100% Guarantee On Our Health & Beauty Services and Products.";

                thirdAd.HeadlinePart1 = "{KeyWord:Need Health & Beauty Products?}";
                thirdAd.HeadlinePart2 = "Quality Health&Beauty Products";
                thirdAd.Description = "Check Out Our Variety of Products & Services!";
            }
            else if (category != null && category.ID == 507)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:All Prices Reduced By 10%.}";
                fisrtAd.HeadlinePart2 = "We Have What Your Home Needs";
                fisrtAd.Description = "Limited Time Sale On All Our Home & Garden Collection. Have a Look Here!";

                secondAd.HeadlinePart1 = "{KeyWord:Limited Time Sale}";
                secondAd.HeadlinePart2 = "Save 10% Off All Products.";
                secondAd.Description = "All Products Are Made With High Quality Material. 100% Satisfaction Guaranteed!";

                thirdAd.HeadlinePart1 = "{KeyWord:Limited Time Sale}";
                thirdAd.HeadlinePart2 = "Check Out Our Collection";
                thirdAd.Description = "Top Quality, Unique Designs, & Great Service. Check It Out Here!";
            }
            else if (category != null && category.ID == 508)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:10% Off Our Unique Collection}";
                fisrtAd.HeadlinePart2 = "High-End Designs. Top Quality.";
                fisrtAd.Description = "Have a look at our Unique Design Collection. Enjoy 100% Satisfaction Guaranteed";

                secondAd.HeadlinePart1 = "{KeyWord:Unique Design Sale 10% Off}";
                secondAd.HeadlinePart2 = "Don't Miss Out - A Must See";
                secondAd.Description = "All Our Products are Uniquely Designed and Of Highest Quality Material.";

                thirdAd.HeadlinePart1 = "{KeyWord:10% Off Top Quality Jewelry}";
                thirdAd.HeadlinePart2 = "Come & View Our Collection";
                thirdAd.Description = "All Our Products are Uniquely Designed and Of Highest Quality Material.";
            }
            else if (category != null && category.ID == 510)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Love Your Pet}";
                fisrtAd.HeadlinePart2 = "All Prices Are Reduced By 10%.";
                fisrtAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";

                secondAd.HeadlinePart1 = "{KeyWord:Give The Best To Your Pet}";
                secondAd.HeadlinePart2 = "Quality Products, Best Prices";
                secondAd.Description = "With Our 100% Satisfaction Guarantee Your Purchase Is Warranted!";

                thirdAd.HeadlinePart1 = "{KeyWord:Top Quality Pet Products}";
                thirdAd.HeadlinePart2 = "We Have What Your Pet Needs";
                thirdAd.Description = "With 10% Discount on All Our Products, We Give You 100% Satisfaction Guaranteed";
            }
            else if (category != null && category.ID == 512)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Special Sale, 10% Off}";
                fisrtAd.HeadlinePart2 = "High Quality Sports Equipment";
                fisrtAd.Description = "We Make Quality Our Top Priority, You Get 100% Satisfaction Guaranteed!";

                secondAd.HeadlinePart1 = "{KeyWord:10% Off Sports & Outdoor}";
                secondAd.HeadlinePart2 = "Enhance Your Performance";
                secondAd.Description = "We Spare No Expense to Make the Highest Quality Products. You Won't Regret It!";

                thirdAd.HeadlinePart1 = "{KeyWord:Sports & Outdoor Sale}";
                thirdAd.HeadlinePart2 = "See What We Have For You";
                thirdAd.Description = "Special Limited Time Sale on All Our Sports & Outdoor Equipment. Enter Here!";
            }
            else if (category != null && category.ID == 513)
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:Quality Products for Toddlers}";
                fisrtAd.HeadlinePart2 = "Now 10% Sale on All Items";
                fisrtAd.Description = "All Our Products are of Unique Design and Highest Quality Materials";

                secondAd.HeadlinePart1 = "{KeyWord:10% Sale on Toddler Items}";
                secondAd.HeadlinePart2 = "Limited Time Sale. Don't Miss";
                secondAd.Description = "All Our Products are of Unique Design and Highest Quality Materials";

                thirdAd.HeadlinePart1 = "{KeyWord:Stuff Toddlers Love}";
                thirdAd.HeadlinePart2 = "10% Off All Store Collection";
                thirdAd.Description = "We Create Stuff The Little Ones Love, and it's Now On Sale At 10% Off! ";
            }
            else
            {
                fisrtAd.HeadlinePart1 = "{KeyWord:All Prices Reduced By 10%.}";
                fisrtAd.HeadlinePart2 = "New Items Just Added, Shop Now";
                fisrtAd.Description = "Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";

                secondAd.HeadlinePart1 = "{KeyWord:Don't Miss Our Special Offers}";
                secondAd.HeadlinePart2 = "Quality Products, Best Prices";
                secondAd.Description = "Your Purchase Is Warranted With Our 100% Satisfaction Guarantee!";

                thirdAd.HeadlinePart1 = "{KeyWord:10% Off All Services & Items}";
                thirdAd.HeadlinePart2 = "Enter Here To Learn More";
                thirdAd.Description = "Free Returns & Save 10% Off On All Products";
            }

            descriptions.Add(fisrtAd);
            descriptions.Add(secondAd);
            descriptions.Add(thirdAd);

            return descriptions;
        }

        public static void SetDescriptionByCategory(int trafficCategoryID, ref AdGroupAd firstAd, ref AdGroupAd secondAd, ref AdGroupAd thirdAd)
        {
            if (secondAd == null)
                secondAd = new AdGroupAd();

            if (thirdAd == null)
                thirdAd = new AdGroupAd();

            Storeya.Core.Helpers.TbCategoriesHelper.Category category = TbCategoriesHelper.GetCategory((int)BusinessTypes.OnlineStore, trafficCategoryID);

            if (category != null && category.ID == 515)
            {
                firstAd.DescriptionLine1 = "We Care for Your Skin.";
                firstAd.DescriptionLine2 = "All Prices were Reduced by 10%.";

                secondAd.DescriptionLine1 = "Our Products Protect Your Skin";
                secondAd.DescriptionLine2 = "Special 10% Sale On All Products!";

                thirdAd.DescriptionLine1 = "Nourishing Your Skin Improved";
                thirdAd.DescriptionLine2 = "And Cheaper with a 10% Discount!";
            }
            else if (category != null && category.ID == 502)
            {
                firstAd.DescriptionLine1 = "Lowest Prices Guaranteed";
                firstAd.DescriptionLine2 = "Fast Delivery, 100% Satisfaction.";

                secondAd.DescriptionLine1 = "Crazy Sale - 10% Off All Products";
                secondAd.DescriptionLine2 = "Limited Time Sale, Shop Now!";

                thirdAd.DescriptionLine1 = "Affordable Electronics & Gadgets";
                thirdAd.DescriptionLine2 = "Quick Support & Quality Products!";
            }
            else if (category != null && category.ID == 503)
            {
                firstAd.DescriptionLine1 = "Top Fashion";
                firstAd.DescriptionLine2 = "Large Variety & Fast Delivery!";

                secondAd.DescriptionLine1 = "High Quality Clothes & Accessories.";
                secondAd.DescriptionLine2 = "Shop Our Collection!";

                thirdAd.DescriptionLine1 = "Trendy Fashionable Boutique";
                thirdAd.DescriptionLine2 = "View Our Collection!";
            }
            else if (category != null && category.ID == 516)
            {
                firstAd.DescriptionLine1 = "Top Men's Fashion";
                firstAd.DescriptionLine2 = "Large Variety & Fast Delivery!";

                secondAd.DescriptionLine1 = "Men's Clothes & Accessories.";
                secondAd.DescriptionLine2 = "Shop Our Collection!";

                thirdAd.DescriptionLine1 = "Trendy Men's Clothes";
                thirdAd.DescriptionLine2 = "View Our Collection!";
            }
            else if (category != null && category.ID == 517)
            {
                firstAd.DescriptionLine1 = "Top Women's Fashion";
                firstAd.DescriptionLine2 = "Large Variety & Fast Delivery!";

                secondAd.DescriptionLine1 = "Women's Clothes & Accessories.";
                secondAd.DescriptionLine2 = "Shop Our Collection!";

                thirdAd.DescriptionLine1 = "Trendy Women's Clothes";
                thirdAd.DescriptionLine2 = "View Our Collection!";
            }
            else if (category != null && category.ID == 518)
            {
                firstAd.DescriptionLine1 = "Top Teen Fashion";
                firstAd.DescriptionLine2 = "Large Variety & Fast Delivery!";

                secondAd.DescriptionLine1 = "Teen Clothes & Accessories.";
                secondAd.DescriptionLine2 = "Shop Our Collection!";

                thirdAd.DescriptionLine1 = "Trendy Teen's Clothes";
                thirdAd.DescriptionLine2 = "View Our Collection!";
            }
            else if (category != null && category.ID == 519)
            {
                firstAd.DescriptionLine1 = "Top Kid's Fashion";
                firstAd.DescriptionLine2 = "Large Variety & Fast Delivery!";

                secondAd.DescriptionLine1 = "Children's Clothes & Accessories.";
                secondAd.DescriptionLine2 = "Shop Our Collection!";

                thirdAd.DescriptionLine1 = "Trendy Kid's Clothes";
                thirdAd.DescriptionLine2 = "View Our Collection!";
            }
            else if (category != null && category.ID == 520)
            {
                firstAd.DescriptionLine1 = "Top Baby Fashion";
                firstAd.DescriptionLine2 = "Large Variety & Fast Delivery!";

                secondAd.DescriptionLine1 = "Baby Clothes & Accessories.";
                secondAd.DescriptionLine2 = "Shop Our Collection!";

                thirdAd.DescriptionLine1 = "Trendy Baby Clothes";
                thirdAd.DescriptionLine2 = "View Our Collection!";
            }
            else if (category != null && category.ID == 506)
            {
                firstAd.DescriptionLine1 = "Keep Your Health With Us";
                firstAd.DescriptionLine2 = "Our Services Are Here To Help";

                secondAd.DescriptionLine1 = "Quality Health & Beauty Services";
                secondAd.DescriptionLine2 = "Health With No Compromise";

                thirdAd.DescriptionLine1 = "Need Health & Beauty Products?";
                thirdAd.DescriptionLine2 = "Quality Health & Beauty Products";
            }
            else if (category != null && category.ID == 507)
            {
                firstAd.DescriptionLine1 = "10% Off Home & Garden Items";
                firstAd.DescriptionLine2 = "We Have What Your Home Needs";

                secondAd.DescriptionLine1 = "Limited Time Sale";
                secondAd.DescriptionLine2 = "Special Collection,Quality Products";

                thirdAd.DescriptionLine1 = "10% Off Limited Time Sale";
                thirdAd.DescriptionLine2 = "Check Out Our Collection";
            }
            else if (category != null && category.ID == 508)
            {
                firstAd.DescriptionLine1 = "10% Off Our Entire Collection";
                firstAd.DescriptionLine2 = "High-End Designs. Top Quality.";

                secondAd.DescriptionLine1 = "Unique Design Sale 10% Off";
                secondAd.DescriptionLine2 = "Don't Miss Out, You Must See It!";

                thirdAd.DescriptionLine1 = "10% Off Top Quality Jewelry";
                thirdAd.DescriptionLine2 = "Enter Here To See Our Collection";
            }
            else if (category != null && category.ID == 510)
            {
                firstAd.DescriptionLine1 = "All Prices Are Reduced By 10%.";
                firstAd.DescriptionLine2 = "Love Your Pets & Shop Now!";

                secondAd.DescriptionLine1 = "We Have What Your Pet Needs";
                secondAd.DescriptionLine2 = "Highest Quality Pet Products";

                thirdAd.DescriptionLine1 = "100% Satisfaction Guaranteed";
                thirdAd.DescriptionLine2 = "We Have What Your Pet Needs Here!";
            }
            else if (category != null && category.ID == 512)
            {
                firstAd.DescriptionLine1 = "Special 10% Off Sale on Collection";
                firstAd.DescriptionLine2 = "High Quality Sports Equipment";

                secondAd.DescriptionLine1 = "10% Off Sports & Outdoor Equipment";
                secondAd.DescriptionLine2 = "Enhance Your Performance";

                thirdAd.DescriptionLine1 = "Sports & Outdoor Limited Time Sale";
                thirdAd.DescriptionLine2 = "See What We Have For You";
            }
            else if (category != null && category.ID == 513)
            {
                firstAd.DescriptionLine1 = "Quality Products for Toddlers";
                firstAd.DescriptionLine2 = "We Have What Your Little One Needs";

                secondAd.DescriptionLine1 = "10% Sale on Toddler Items";
                secondAd.DescriptionLine2 = "Top Quality & Service Right Here";

                thirdAd.DescriptionLine1 = "We Have Stuff Toddlers Love in Here";
                thirdAd.DescriptionLine2 = "Quality Products for Toddlers";
            }
            else
            {
                firstAd.DescriptionLine1 = "All Prices were Reduced by 10%.";
                firstAd.DescriptionLine2 = "New Items Just Added, Shop Now!";

                secondAd.DescriptionLine1 = "Great Service & Quality Products.";
                secondAd.DescriptionLine2 = "100% Satisfaction Guaranteed!";

                thirdAd.DescriptionLine1 = "We Have The Product You Need";
                thirdAd.DescriptionLine2 = "Save 10% Off All Items!";
            }


            firstAd.AdGroupAdStatus = 0;     // AdGroupAdStatus.ENABLED;  0
            secondAd.AdGroupAdStatus = 0;
            thirdAd.AdGroupAdStatus = 0;

        }

        public static void SetDescriptionByCategoryNew(int trafficCategoryID, ref AdGroupAd bestAd, ref AdGroupAd qualityAd, ref AdGroupAd discountAd, string domain = null)
        {
            if (qualityAd == null)
                qualityAd = new AdGroupAd();

            if (discountAd == null)
                discountAd = new AdGroupAd();

            Storeya.Core.Helpers.TbCategoriesHelper.Category category = TbCategoriesHelper.GetCategory((int)BusinessTypes.OnlineStore, trafficCategoryID);

            if (category != null && category.ID == 515) //Cosmetics
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Get the best cosmetics products online.";
                bestAd.DescriptionLine2 = "We Care for Your Skin, It Deserves The Best. Get yours today!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - High Quality Skin Care Products Made For You.";
                qualityAd.DescriptionLine2 = "The Newest & Best Quality, We've Got You Covered - Shop Today!";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Get the best cosmetics at the best prices online.";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 502) //Electronics & Computers
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Shop The Latest Trends in Gadgets and Technology";
                bestAd.DescriptionLine2 = "All The New and Innovative Products on the Market Right Here, Shop Now";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - High-End Electronics & Gadgets. 100% Satisfaction Guaranteed!";
                qualityAd.DescriptionLine2 = "Shop Our Top Quality and Innovative Products Now";

                discountAd.DescriptionLine1 = "{domain}© Official Store - The Newest and Most Innovative Technology on the Market Today, Now With a 10% Discount";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 503)//Fashion & Accessories
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Get the best fashion exactly as you want.";
                bestAd.DescriptionLine2 = "Limited Stock Available - What Are You Waiting For? Get Yours Today!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - High Quality Fashion Designs Made For You.";
                qualityAd.DescriptionLine2 = "Limited Stock Available - What Are You Waiting For? Get Yours Today!";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Get the best fashion at the best prices exactly as you want.";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 516) //Fashion - Men's
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Get The Latest Men's Fashion and Style Trends.";
                bestAd.DescriptionLine2 = "The Best Place to Upgrade Your Wardrobe. 100% Satisfaction Guaranteed, Shop Now!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Shop Our High Quality Men's Clothing & Accessories.";
                qualityAd.DescriptionLine2 = "Get This Season's Latest Arrivals, Shop Our Collection Now!";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Trendy Mens Fashion Designs";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 517) //Fashion - Women's
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Shop Our Trendy Women's Fashions. 100% Satisfaction Guaranteed.";
                bestAd.DescriptionLine2 = "Large Variety & Fast Delivery!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Shop Our Stunning Range of Top Quality Womenswear & Accessories";
                qualityAd.DescriptionLine2 = "Get This Season's Latest Arrivals, Shop Our Collection Now!";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Trendy Women's Fashion Designs";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 518) //Fashion - Teen
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Get The Latest In Fashion and Style Trends.";
                bestAd.DescriptionLine2 = "On Trend Pieces You Need To Add To Your Closet.  Large Variety, Fast Deliver, Shop Now!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Top Quality Teen Clothes & Accessories.";
                qualityAd.DescriptionLine2 = "Get This Season's Latest Arrivals, Shop Our Collection Now!";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Trendy Teen Fashion Designs";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 519) //Fashion - Kids
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";
                bestAd.DescriptionLine2 = "Trendy & Easy To Wear. Shop Now!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Top Quality Children's Clothes & Accessories.";
                qualityAd.DescriptionLine2 = "Shop Our Collection!";

                discountAd.DescriptionLine1 = "A Unique and Varied Selection of Kids High Quality Fashions. Shop Now!";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 520) //Fashion - Babies
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";
                bestAd.DescriptionLine2 = "Dressing Your Little One In The Latest Baby Fashions";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Top Quality Baby Clothes & Accessories, Shop Now!";
                qualityAd.DescriptionLine2 = "Shop a Wide Selection of Premium Quality Baby Clothes.";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Trendy  Baby Fashion Designs";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 506) //Health & Beauty
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Live Your Healthiest Life Every Day";
                bestAd.DescriptionLine2 = "Health and Beauty Products Custom Made For Your Personal Needs. Buy Today";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Health & Beauty Quality Products To Treat And Pamper Yourself.";
                qualityAd.DescriptionLine2 = "Promote A Healthy Lifestyle With The Highest Quality Products";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Shop Now and Take Advantage of Our Discount to You!";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 507) //Home & Garden
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Your First Stop For The Best Interior Design";
                bestAd.DescriptionLine2 = "Discover Inspirational Top Interior Designs For Your Home!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Shop Our High Quality Homes Interior & Outdoor Designs.";
                qualityAd.DescriptionLine2 = "Find Everything You Need To Style Your Home Your Way, Shop Now!";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Find Stylish Discounted Home Decor Solutions For Every Room Of Your House And Outdoor.";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 508) //Jewelry
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Browse From Our Unique Top Design Collection Of Jewelry";
                bestAd.DescriptionLine2 = "Discover Our Unique Range Of Top Jewelry To Match Your Style & Personality!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Enjoy High Quality Jewelry That Will Be Treasured Always.";
                qualityAd.DescriptionLine2 = "Shop Our Wide Selection Of Jewelry & Discover A Range Of Fabulous And Unique Designs.";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Shop Now and Quickly Purchase Your Favorite Products at Surprising Prices.";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 510) //Pet Shop
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Get The Best Pet Supplies, Products & Accessories For Your Pet.";
                bestAd.DescriptionLine2 = "Find The Best Gear To Keep Your Pet Healthy, Groomed and Happy. Shop Now!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Pamper Your Pet With Top Quality Supplies, & Accessories.";
                qualityAd.DescriptionLine2 = "Find A Wide Range Of High Quality Products Especially For Your Pets Needs.";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Shop For All Your Pet Needs, Enjoy Discounted Products And Keep Your Pet Happy & Groomed";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 512) //Sports & Outdoors
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - We Have Everything You Need For Your Active Lifestyle";
                bestAd.DescriptionLine2 = "Get Moving and Visit Our Site For All That You Need, Shop Now!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - Discover A Range Of High Quality Sports & Outdoors Products!";
                qualityAd.DescriptionLine2 = "We Make Quality Our Top Priority, You Get 100% Satisfaction Guaranteed!";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Limited Time Sale On Our Sports & Outdoor Equipment. Shop Now!";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else if (category != null && category.ID == 513) //Toys, Kids & Babies
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Shop Our Incredible Range Of Products";
                bestAd.DescriptionLine2 = "Guaranteed To Keep Your Little One Busy and Happy.";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - High Quality Toys & Accessories Perfect For Your Little One.";
                qualityAd.DescriptionLine2 = "Enjoy The Best Toys For Kids At Every Age. ";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Toys For Your Little One";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }
            else
            {
                bestAd.DescriptionLine1 = "{domain}© Official Store - Large Variety, Fast Delivery, 100% Satisfaction Guaranteed.";
                bestAd.DescriptionLine2 = "We Have Everything You Need, Shop Our Collection Now!";

                qualityAd.DescriptionLine1 = "{domain}© Official Store - High Quality Products. 100% Satisfaction Guaranteed.";
                qualityAd.DescriptionLine2 = "We Make Quality Our Top Priority, You Get 100% Satisfaction Guaranteed!";

                discountAd.DescriptionLine1 = "{domain}© Official Store - Enjoy a {xx}% Discount On Our Products Today!";
                discountAd.DescriptionLine2 = "A {xx}% off on all products! What are you waiting for? Get yours today.";
            }

            //'Best ad' and 'Quality ad'
            if (!string.IsNullOrEmpty(domain) && domain.Length > 15)
            {
                int index = domain.IndexOf(".", StringComparison.CurrentCulture);
                if (index > 0)
                    domain = domain.Remove(index);
            }

            if (!string.IsNullOrEmpty(domain))
            {
                bool isMarketplace = MarketplaceUrlHelper.IsMarketplace(domain.ToLower());

                if (bestAd.DescriptionLine1.Contains("{domain}©"))
                {
                    if (isMarketplace)
                    {
                        bestAd.DescriptionLine1 = bestAd.DescriptionLine1.Replace("{domain}© Official Store - ", "");
                    }
                    else
                    {
                        bestAd.DescriptionLine1 = bestAd.DescriptionLine1.Replace("{domain}", domain);
                    }
                }

                if (qualityAd.DescriptionLine1.Contains("{domain}©"))
                {
                    if (isMarketplace)
                    {
                        qualityAd.DescriptionLine1 = qualityAd.DescriptionLine1.Replace("{domain}© Official Store - ", "");
                    }
                    else
                    {
                        qualityAd.DescriptionLine1 = qualityAd.DescriptionLine1.Replace("{domain}", domain);
                    }
                }

                if (discountAd.DescriptionLine1.Contains("{domain}©"))
                {
                    if (isMarketplace)
                    {
                        discountAd.DescriptionLine1 = discountAd.DescriptionLine1.Replace("{domain}© Official Store - ", "");
                    }
                    else
                    {
                        discountAd.DescriptionLine1 = discountAd.DescriptionLine1.Replace("{domain}", domain);
                    }
                }
            }

            //https://developers.google.com/adwords/api/docs/guides/expanded-text-ads
            //Note: Starting in v201809, a second descriptions is available. The character limit for both descriptions lines has been increased to 90.
            int maxLength = 90;
            if (bestAd.DescriptionLine1.Length > maxLength)
            {
                bestAd.DescriptionLine1 = Optimize(bestAd.DescriptionLine1, maxLength);
            }
            if (bestAd.DescriptionLine2.Length > maxLength)
            {
                bestAd.DescriptionLine2 = Optimize(bestAd.DescriptionLine2, maxLength);
            }
            if (qualityAd.DescriptionLine1.Length > maxLength)
            {
                qualityAd.DescriptionLine1 = Optimize(qualityAd.DescriptionLine1, maxLength);
            }
            if (qualityAd.DescriptionLine2.Length > maxLength)
            {
                qualityAd.DescriptionLine2 = Optimize(qualityAd.DescriptionLine2, maxLength);
            }

            if (discountAd.DescriptionLine1.Length > maxLength)
            {
                discountAd.DescriptionLine1 = Optimize(discountAd.DescriptionLine1, maxLength);
            }
            if (discountAd.DescriptionLine2.Length > maxLength)
            {
                discountAd.DescriptionLine2 = Optimize(discountAd.DescriptionLine2, maxLength);
            }

            bestAd.AdGroupAdStatus = 0;     // AdGroupAdStatus.ENABLED;  0
            qualityAd.AdGroupAdStatus = 0;
            discountAd.AdGroupAdStatus = 1; //AdGroupAdStatus.PAUSED = 1
        }
        public static List<AdGroupAd> CreateDSAAdGroupAdsFromAdCopies(int shopID, out string error)
        {
            var db = DataHelper.GetStoreYaEntities();
            var adCopies = db.AdCopies.Where(x => x.ShopID == shopID && x.CopyType == (int)Storeya.Core.Models.CopyType.Description);
            List<string> descriptions = new List<string>();
            if (adCopies.Count() > 0)
            {
                var descriptionsFromAdCopies = adCopies.Select(x => x.Text).ToList();
                descriptions = descriptionsFromAdCopies;//AdwordsManager.FixDescriptions(descriptionsFromAdCopies);
            }
            if (descriptions == null || (descriptions != null && (descriptions.Count() < 3 || descriptions.Count() > 4)))
            {
                error = "Error! You must have at least 3 descriptions and no more than 4, the program cannot create ads";
                return null;
            }
            else
            {
                List<AdGroupAd> adGroupAds = new List<AdGroupAd>();
                AdGroupAd firstAd = new AdGroupAd();
                firstAd.DescriptionLine1 = descriptions[0];
                firstAd.DescriptionLine2 = descriptions[1];
                AdGroupAd secondAd = new AdGroupAd();
                secondAd.DescriptionLine1 = descriptions[2];
                if (descriptions.Count() == 3)
                {
                    secondAd.DescriptionLine2 = descriptions[0];
                }
                else
                {
                    secondAd.DescriptionLine2 = descriptions[3];
                }
                firstAd.AdGroupAdStatus = 0;   //make ad active 
                secondAd.AdGroupAdStatus = 0;
                adGroupAds.Add(firstAd);
                adGroupAds.Add(secondAd);
                error = null;
                return adGroupAds;
            }

        }

    }

    public class AdGroupAd
    {
        //public bool IsEtsy;
        public AdGroupAd()
        {
            DevicePreference = "All";
            Status = "Active";
        }

        public string DisplayURL { get; set; }

        public string DescriptionLine1 { get; set; }

        public string DescriptionLine2 { get; set; }

        public string DevicePreference { get; set; }

        public string Status { get; set; }

        public int AdGroupAdStatus { get; set; }

        public List<AdGroupCriteria> AdGroupCriterias { get; set; }
    }


    public class AdCallOnlyAd
    {

        public string DisplayURL { get; set; }

        public string DescriptionLine1 { get; set; }

        public string DescriptionLine2 { get; set; }

        public string DevicePreference { get; set; }
        public string CompanyName { get; set; }
        public string PhoneNumber { get; set; }
        public string CountryOfPhone { get; set; }

        public string Status { get; set; }

        public List<AdGroupCriteria> AdGroupCriterias { get; set; }
    }


    public class SearchAd
    {
        public string Description;
        public string HeadlinePart1;
        public string HeadlinePart2;

        public string HeadlinePart3;
        public string Description2;
        public string Name;
        public int AdGroupAdStatus;

    }

    public class AdGroup
    {
        public AdGroup()
        {
            MaxCPC = "0.51";
            //if (!isEtsy)
            //{
            //    MaxCPC = "0.51";
            //   // Name = "Dynamic Ad";
            //}
            //else
            //{
            //    MaxCPC = "0.81";
            //}
            TrackingTemplate = "{unescapedlpurl}?ref=StoreYa&utm_source=stry&utm_medium=trafb&utm_campaign=storeya10";
            DisplayNetworkMaxCPC = "0";
            MaxCPM = "0.01";
            CPABid = "0";
            DisplayNetworkCustomBidType = "None";
            TargetingOptimization = "Conservative";
            Type = "Default";
            FlexibleReach = "Interests and remarketing";
            Status = "Active";

        }

        public string Name { get; set; }  //  public string AdGroup { get; set; }

        public string MaxCPC { get; set; }

        public string TrackingTemplate { get; set; }

        public string DisplayNetworkMaxCPC { get; set; }

        public string MaxCPM { get; set; }

        public string CPABid { get; set; }

        public string DisplayNetworkCustomBidType { get; set; }

        public string TargetingOptimization { get; set; }

        public string Type { get; set; } //AdGroupType

        public string FlexibleReach { get; set; }

        public string Status { get; set; } //AdGroupStatus

        public List<AdGroupAd> AdGroupAds { get; set; }
        public List<AdCallOnlyAd> CallOnlyAds { get; set; }


    }

    public class DsaCampaign : BaseAdWordsCampaign
    {

        public DsaCampaign()
        {
            CampaignDailyBudget = "4";
            Type = "Search Network only";
            Networks = "Search Partners";
            BidStrategyType = "Manual CPC";
            EnhancedCPC = "Enabled";
            ViewableCPM = "Disabled";
            //BidModifier = "-100";
            AdRotation = "Optimize for conversions";
            DeliveryMethod = "Standard";
            TargetingMethod = "Location of presence or Area of interest";
            ExclusionMethod = "Location of presence or Area of interest";
            CampaignPriority = "Low";
            LocalInventoryAds = "Disabled";
            Status = "Active";
        }

        //public string Name { get; set; }

        public string DSAWebsite { get; set; }
        public string FinalUrl { get; set; }

        public string UrlSuffix_Campaign { get; set; }

        public List<AdGroup> AdGroups { get; set; }



        public string DSALanguage { get; set; }

        //public string Languages { get; set; }

        public string Networks { get; set; }

        public string BidStrategyType { get; set; }

        public string EnhancedCPC { get; set; }

        public string ViewableCPM { get; set; }

        //public string BidModifier { get; set; }

        public string AdRotation { get; set; }

        public string DeliveryMethod { get; set; }

        public string TargetingMethod { get; set; }

        public string ExclusionMethod { get; set; }

        public string CampaignPriority { get; set; }

        public string LocalInventoryAds { get; set; }

        public string Type { get; set; } //CampaignType

        public string Status { get; set; } //CampaignStatus

        public string Location { get; set; }

        public List<string> Locations { get; set; }

        public List<string> LocationsCodes { get; set; }
        public string CountriesOriginSettings { get; set; }

        public string CreationError { get; set; }
        public string CreationWarning { get; set; }

        //public bool AccountHasCallCampaign { get; set; }
    }

    public class GmailAdCampaignStatusResponse
    {
        public string Message { get; set; }
    }

    public class GmailAdCampaignSettings : BaseAdWordsCampaign
    {
        public string BusinessName { get; set; }
        public string Headline { get; set; }
        public string Description { get; set; }
        public string LogoImage { get; set; }

        public string MarketingImage { get; set; }
        public string MarketingImageHeadline { get; set; }
        public string MarketingImageDescription { get; set; }
        public string[] FinalUrls { get; set; }
        public List<string> LocationsCodes { get; set; }

        public string DefaultLogoImage { get; set; }
        public string DefaultMarketingImage { get; set; }
        public string DefaultProductImage { get; set; }

        public List<string> ProductImages { get; set; }
    }

    public class CreatedAdGroup
    {
        public long ID { get; set; }
        public string Name { get; set; }
        public long BidMicroamount { get; set; }
        public string ResourceName { get; set; }
    }
}
