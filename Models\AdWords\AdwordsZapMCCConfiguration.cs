﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Models.AdWords
{ 
    public class AdwordsZapMCCConfiguration : IAdwordsMCCConfiguration
    {
        public string PrimaryBillingId => "8402-1803-1599";
        public string BillingAccountId => "5951-6429-0918-4563";
        public string MerchantClientCustomerId => "475-924-3925";
        public string AccountCurrency => "ILS";
        public long DailyAmount => 7700000;
        public string OrderStartDateTime => string.Format("{0} Asia/Jerusalem", DateTime.Now.AddHours(8).ToString("yyyyMMdd HHmmss"));
        public string OrderEndDateTime => "******** 235959 Asia/Jerusalem";
        public string DateTimeZone => "Asia/Jerusalem";
        public bool UseFirstUrlAsIs => true;

        public string CampaignDailyBudget => "0.01";
    }
}
