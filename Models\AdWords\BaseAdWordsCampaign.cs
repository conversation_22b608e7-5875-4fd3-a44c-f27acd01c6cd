﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    public class BaseAdWordsCampaign
    {
        public static double DefaultMobileBidModifier = 0.3;

        public BaseAdWordsCampaign()
        {
            this.MobileBidModifier = DefaultMobileBidModifier;
        }

        public string CampaignDailyBudget { get; set; }

        public string Name { get; set; }
        public string HomepageUrl { get; set; }
        //public string CampaignDailyBudget { get; set; }
        public string AllLanguages { get; set; }
        public string AllLocationCodes { get; set; }

        public double MobileBidModifier { get; set; }
        public int IsServiceProvider { get; set; }

        public ServiceLocation ServiceLocation { get; set; }

        public List<string> GetCampaignCountries()
        {
            List<string> list = new List<string>();
            if (!string.IsNullOrEmpty(this.AllLocationCodes))
            {
                list = this.AllLocationCodes.Split(',').ToList();
            }
            return list;
        }

        public int? TrafficCategoryID { get; set; }

        public string Cities { get; set; }

    }

    public class ServiceLocation
    {
        public string ZipCode { get; set; }
        public int Radius { get; set; }
        public int RadiusUnits { get; set; }
        public string City { get; set; }
        public string Address { get; set; }
        public string CountryCode { get; set; }
    }
}
