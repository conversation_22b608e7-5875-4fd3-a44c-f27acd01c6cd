﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    public class CallOnlyAdWordsCampaign : BaseAdWordsCampaign
    {
        public CallOnlyAdWordsCampaign()
        {
            this.AdGroups = new List<CallOnlyAdGroup>();
        }
        public string BusinessName { get; set; }
        public string Phone { get; set; }
        public string PhoneCountryCode { get; set; }

        public string Category { get; set; }

        public string CallZipCode { get; set; }
        public int? Radius { get; set; }
        public int? RadiusUnits { get; set; }
        public List<string> Extensions { get; set; }
        public List<CallOnlyAdGroup> AdGroups { get; set; }
    }


    public class CallOnlyAdGroup
    {
        public string DisplayUrl { get; set; }
        public string Description1 { get; set; }
        public string Description2 { get; set; }
        public string HeadLine1 { get; set; }
        public string HeadLine2 { get; set; }
        public List<string> Keywords { get; set; }
    }
}