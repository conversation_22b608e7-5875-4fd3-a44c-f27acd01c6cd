﻿using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    public class AdWordsCampaignFactory
    {
        public static string CombineCampaignName(int shopID, string cleanHomeUrl, List<string> countryCodes, long impressions, string campaignType, int? paymentSystem)
        {
            if (countryCodes.Count > 3)
            {
                List<string> temp = new List<string>();
                temp.Add(countryCodes[0]);
                temp.Add(countryCodes[countryCodes.Count - 2]);
                temp.Add(countryCodes[countryCodes.Count - 1]);
                temp.Add("");

                countryCodes = temp;
            }


            string code = string.Join<string>("+", countryCodes);

            string name = null;
            if (paymentSystem == null || paymentSystem == (int)PaymentSystems.WixPayments)
            {
                name = string.Format("{0}_{1}_{2}_{3}_{4}", shopID, cleanHomeUrl, code, impressions, campaignType);               
            }
            else if (paymentSystem == (int)PaymentSystems.WixPayments_NoAdServer
                || paymentSystem == (int)PaymentSystems.BlueSnap_NoAdServer_30days)
            {
                var amountUSD = TbSettingsHelper.GetBillingAmount_IgnoreOverwriteAmount(impressions, paymentSystem);
                name = string.Format("{0}_{1}_{2}_${3}_{4}", shopID, cleanHomeUrl, code, amountUSD, campaignType);
            }
            else //BlueSnap_NoAdServer
            {
                name = string.Format("{0}_{1}_{2}_new{3}_{4}", shopID, cleanHomeUrl, code, impressions, campaignType);
            }

            return name;
        }

    }
}
