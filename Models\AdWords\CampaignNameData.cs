﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
//using Bugsnag.Polyfills;
using System.Web.UI.WebControls;
using Storeya.Core.Helpers;
using Storeya.Core.Models.TrafficBoosterModels;

namespace Storeya.Core.Models.AdWords
{
    public class CampaignNameData
    {
        public int ShopID { get; set; }
        public string Domain { get; set; }
        public string Countries { get; set; }
        public int Plan { get; set; }
        public decimal PlanInUsd { get; set; }
        public int BilledCycles { get; set; }
        public string CampaignTypeString { get; set; }
        public TbCampaignTypes CampaignType { get; set; }
        public bool IsActive { get; set; }
        public string AdditionalSegment { get; set; }
        public string FullName { get; set; }
        public int PaymentSystem { get; set; }

        public string GetCampaignName()
        {
            string[] splitted = this.FullName.Split('_');
            if (splitted.Length != 5)
            {
                return null;
            }
            if (this.IsActive == false)
            {
                return null;
            }

            var nextBillingCycle = this.BilledCycles;
            var billingCycle = "." + nextBillingCycle;
            var plan = this.FullPlanName;
            if (this.PlanInUsd != 0)
            {
                plan = "$" + this.PlanInUsd;
            }

            return this.ShopID.ToString() + "_" + this.Domain + "_" + this.Countries + "_" + plan + billingCycle + "_" + this.CampaignTypeString + ((AdditionalSegment == null) ? "" : " " + this.AdditionalSegment);
        }

        public string GetNextBillingCampaignName()
        {
            string[] splitted = this.FullName.Split('_');
            if (splitted.Length != 5)
            {
                return null;
            }
            if (this.IsActive == false)
            {
                return null;
            }

            var nextBillingCycle = this.BilledCycles + 1;
            var billingCycle = "." + nextBillingCycle;
            var plan = this.FullPlanName;
            if (this.PlanInUsd != 0)
            {
                plan = "$" + this.PlanInUsd;
            }

            return this.ShopID.ToString() + "_" + this.Domain + "_" + this.Countries + "_" + plan + billingCycle + "_" + this.CampaignTypeString + ((AdditionalSegment == null) ? "" : " " + this.AdditionalSegment);
        }

        public string FullPlanName
        {
            get
            {
                if (this.PaymentSystem == 3)
                {
                    return "new" + this.Plan;
                }
                else if (this.PaymentSystem == 4)
                {
                    return "$" + this.Plan;
                }

                return this.Plan.ToString();
            }
        }
    }

    public class CampaignNameParser
    {
        public static CampaignNameData Parse(string name)
        {
            try
            {
                CampaignNameData data = null;
                if (!string.IsNullOrEmpty(name))
                {
                    if (data == null)
                        data = new CampaignNameData();

                    data.FullName = name;

                    if (name.StartsWith("-"))
                    {
                        data.IsActive = false;
                        name = name.TrimStart('-');
                    }
                    else
                    {
                        data.IsActive = true;
                    }

                    string[] splitted = name.Split('_');
                    if (splitted.Length > 0)
                    {

                        if (!string.IsNullOrEmpty(splitted[0]))
                            data.ShopID = Convert.ToInt32(splitted[0]);

                        if (splitted.Length > 1)
                        {
                            data.Domain = splitted[1];
                        }
                        if (splitted.Length > 2)
                        {
                            data.Countries = splitted[2];
                        }
                        if (splitted.Length > 3)
                        {
                            string plan_cicles = splitted[3];

                            string planString = null;
                            string ciclesString = null;

                            if (plan_cicles.Contains("."))
                            {
                                string[] splitted_1 = plan_cicles.Split('.');
                                planString = splitted_1[0];
                                ciclesString = splitted_1[1];
                            }
                            else
                            {
                                planString = plan_cicles;
                            }

                            if (!string.IsNullOrEmpty(planString))
                            {
                                if (planString.StartsWith("new"))
                                {
                                    planString = planString.Replace("new", "");
                                    data.PaymentSystem = 3;
                                }
                                else if (planString.StartsWith("$"))
                                {
                                    planString = planString.Replace("$", "");
                                    data.PaymentSystem = 4;
                                    data.PlanInUsd = Convert.ToInt32(planString);
                                }
                                data.Plan = Convert.ToInt32(planString);
                            }
                            if (!string.IsNullOrEmpty(ciclesString))
                            {
                                data.BilledCycles = Convert.ToInt32(ciclesString);
                            }
                            else
                            {
                                data.BilledCycles = 1;
                            }
                        }
                        if (splitted.Length > 4)
                        {
                            string campType_sub = splitted[4];

                            string campaignTypeString = null;
                            string subNameString = null;

                            if (campType_sub.Contains(' '))
                            {
                                string[] splitted_0 = campType_sub.Split(' ');
                                campaignTypeString = splitted_0[0];
                                subNameString = splitted_0[1];
                            }
                            else
                            {
                                campaignTypeString = campType_sub;
                            }

                            data.CampaignTypeString = campaignTypeString;
                            data.CampaignType = GetCampaignType(data.CampaignTypeString, subNameString);

                            if (!string.IsNullOrEmpty(subNameString))
                            {
                                data.AdditionalSegment = campType_sub.Replace(campaignTypeString + " ", "");
                            }
                        }

                    }
                }

                return data;
            }
            catch (Exception ex)
            {
                throw new Exception("Failed to parse " + name, ex);
            }

        }
        public static TbCampaignTypes GetCampaignTypeFromUTM(string utmCampaign)
        {
            if (string.IsNullOrEmpty(utmCampaign))
            {
                return TbCampaignTypes.None;
            }
            TbCampaignTypes? cType = CampaignsUtmsHelper.Instance.GetCampaignTypeFromUTM(utmCampaign);
            if (cType.HasValue)
            {
                return cType.Value;
            }
           
           

            utmCampaign = utmCampaign.ToLower();
            if (utmCampaign.Contains("_rem"))
            {
                return TbCampaignTypes.FacebookREM;
            }
            if (utmCampaign.Contains("_acq"))
            {
                return TbCampaignTypes.FacebookACQ;
            }
            if (utmCampaign.Contains("_adv"))
            {
                return TbCampaignTypes.FacebookADV;
            }

            switch (utmCampaign)
            {
                case "storeya2":
                    //return "Adwords Brand";
                    return TbCampaignTypes.SearchBranded;
                case "display1":
                    //   return "AdWords Display";
                    return TbCampaignTypes.GoogleDisplay;
                case "storeya10":
                    // return "AdWords Dynamic\\DSA\\SearchAPS";
                    return TbCampaignTypes.GoogleDsa;
                case "storeya10c":
                    // return "DSA BS";
                    return TbCampaignTypes.GoogleDsa;
                case "storeya50":
                    //return "AdWords Shopping";
                    return TbCampaignTypes.Shopping;
                case "storeya51":
                    // return "AdWords Smart Shopping";
                    return TbCampaignTypes.SmartShopping;
                case "storeya53":
                    // return "AdWords Smart Shopping BS";
                    return TbCampaignTypes.SmartShopping;
                case "storeya51n":
                    //  return "AdWords Smart Shopping New User";
                    return TbCampaignTypes.SmartShopping;
                case "storeya75":
                    // return "AdWords Dynamic Remarketing\\CDRM";
                    return TbCampaignTypes.Remarketing;
                case "storeya70":
                    // return "AdWords Remarketing (regular)";
                    return TbCampaignTypes.Remarketing;
                case "storeya60":
                case "storeya60ca":
                    // return "Shopping Pmax";
                    return TbCampaignTypes.Shopping;
                case "storeya61":
                    //  return "Shopping Pmax New Audience";
                    return TbCampaignTypes.Shopping;
                case "storeya100":
                    //  return "RDSA";
                    return TbCampaignTypes.RDSA;
                case "storeya20":
                    //  return "RDSA";
                    return TbCampaignTypes.RDSA;
                case "storeya20b":
                    //   return "RLSA B";
                    return TbCampaignTypes.RLSA;
                case "storeya55":
                    //   return "RShopping";
                    return TbCampaignTypes.RShopping;
                case "storeya80":
                    //  return "Simi";
                    return TbCampaignTypes.None;//????
                case "storeya90":
                    //  return "SDC (Smart Display Campaign)";
                    return TbCampaignTypes.SmartShopping;
                case "storeya2c":
                    // return "Search Competitors";
                    return TbCampaignTypes.GoogleSearch;
                case "storeya91":
                    //  return "DIscovery";
                    return TbCampaignTypes.None;//????
                case "storeya92":
                    //   return "DisplaNA RDA ";
                    return TbCampaignTypes.DisplaNA;
                case "storeya100g":
                    //  return "GSP";
                    return TbCampaignTypes.GSP;
                case "storeya101":
                    //   return "RGSP";
                    return TbCampaignTypes.RGSP;
                case "storeya500":
                    //  return "YouTube";
                    return TbCampaignTypes.YouTube;
                case "display5":
                    //   return "Display  - Not the default Display and not Dynamic Remarketing!";
                    return TbCampaignTypes.GoogleDisplay; //???????
                case "storeya505":
                    //  return "YouTube TrueViewShopping - Remarketing";
                    return TbCampaignTypes.YouTube;
                case "storeya506":
                    //    return "YouTube TrueViewShopping - Acquisition";
                    return TbCampaignTypes.YouTube;
                case "storeya3":
                case "storeya63":
                    //   return "Bing Search Branded";
                    return TbCampaignTypes.BingSearch;
                case "storeya50b":
                    // return "Bing Shopping";
                    return TbCampaignTypes.BingSearch;
                case "storeya3a":
                    //return "Bing Search search A";
                    return TbCampaignTypes.BingSearch;
                case "storeya310":
                    // return "Bing DSA";
                    return TbCampaignTypes.BingSearch;
                case "storeya3100":
                    //  return "Bing RDSA";
                    return TbCampaignTypes.BingSearch;
                case "storeya507":
                    // return "YouTube action Remarketing";
                    return TbCampaignTypes.YouTube;
                case "storeya508":
                    //  return "YouTube action New Audience";
                    return TbCampaignTypes.YouTube;
                case "storeya509":
                    //   return "YouTube uploaded video";
                    return TbCampaignTypes.YouTube;
                case "storeya2b":
                    //   return "AdWords Search Branded";
                    return TbCampaignTypes.SearchBranded;
                case "storeya52":
                    //  return "AdWords Shopping Branded";
                    return TbCampaignTypes.Shopping;//????
                case "storeya8":
                    // return "Smart";
                    return TbCampaignTypes.SmartDisplay;//?????
                case "storeya76":
                    // return "Display RDRM";
                    return TbCampaignTypes.RDSA;//?????
                case "storeya510":
                    // return "YouTube Product beta";
                    return TbCampaignTypes.YouTube;
                case "storeya511":
                    // return "YouTube Bumper REM";
                    return TbCampaignTypes.YouTube;
                case "storeya1500":
                    // return "Zap calls campaign";
                    return TbCampaignTypes.None; ;//?????

                default:
                    // return "(not set)";
                    return TbCampaignTypes.None; ;//?????
            }

        }
        public static TbCampaignTypes GetCampaignType(string campaignTypePart, string additionalInfo = null)
        {
            if (campaignTypePart == "Search")
            {
                return TbCampaignTypes.GoogleSearch;
            }
            else if (campaignTypePart == "Dynamic" || campaignTypePart == "Dynamic2" || campaignTypePart == "SearchAPS" || campaignTypePart == "SearchAPS2")
            {
                return TbCampaignTypes.GoogleDsa;
            }
            else if (campaignTypePart == "Display")
            {
                if (string.IsNullOrEmpty(additionalInfo))
                {
                    return TbCampaignTypes.GoogleDisplay;
                }
                else
                {
                    //if custom made Display - count it as smart
                    return TbCampaignTypes.SmartDisplay;
                }

            }
            else if (campaignTypePart == "Displa2")
            {
                return TbCampaignTypes.GoogleDispla2;
            }
            else if (campaignTypePart == "DRM")
            {
                return TbCampaignTypes.DRM;
            }
            else if (campaignTypePart == "SDC")
            {
                return TbCampaignTypes.SmartDisplay;
            }
            else if (campaignTypePart == "Calls")
            {
                return TbCampaignTypes.GoogleCalls;
            }
            else if (Enum.IsDefined(typeof(TbCampaignTypes), campaignTypePart))
            {
                return (TbCampaignTypes)Enum.Parse(typeof(TbCampaignTypes), campaignTypePart);
            }

            return TbCampaignTypes.None;

        }

        public static TbCampaignTypes TryParseCampaignType(string name)
        {
            try
            {
                var c = CampaignNameParser.Parse(name);
                return c.CampaignType;
            }
            catch (Exception)
            {
                return TbCampaignTypes.None;
            }

        }
        public static TbCampaignStatuses GetCampaignStatus(string status)
        {
            TbCampaignStatuses statusEnum = (TbCampaignStatuses)Enum.Parse(typeof(TbCampaignStatuses), status);
            return statusEnum;
        }
        public static AdCampaignStatuses GetAdCampaignStatus(string status)
        {
            status = status.ToUpper();
            try
            {
                AdCampaignStatuses statusEnum = (AdCampaignStatuses)Enum.Parse(typeof(AdCampaignStatuses), status);
                return statusEnum;
            }
            catch
            {
                return AdCampaignStatuses.UNKNOWN;
            }
        }
    }


}
