﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
  


    //public static class CampaignsTypesHelper
    //{
    //    public static CampaignsTypes GetCampaignType(string campaignTypePart)
    //    {
    //        if (campaignTypePart == "Search")
    //        {
    //            return CampaignsTypes.AdWordsSearch;
    //        }
    //        else if (campaignTypePart == "Dynamic" || campaignTypePart == "Dynamic2" || campaignTypePart == "SearchAPS" || campaignTypePart == "SearchAPS2")
    //        {
    //            return CampaignsTypes.AdWordsDSA;
    //        }
    //        else if (campaignTypePart == "Calls")
    //        {
    //            return CampaignsTypes.AdWordsCalls;
    //        }
    //        else if (campaignTypePart == "Display")
    //        {
    //            return CampaignsTypes.AdWordsDisplay;
    //        }
    //        else if (campaignTypePart == "Displa2")
    //        {
    //            return CampaignsTypes.AdWordsDispla2;
    //        }

    //        return CampaignsTypes.Unknown;

    //    }


    //}



}
