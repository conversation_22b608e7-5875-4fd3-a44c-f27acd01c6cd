﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    static public class AdWordsCountriesCodes
    {
        public static long GetCode(string countryName)
        {
            // Try to get the result in the static Dictionary
            string result;
            if (_dictNameToCode.TryGetValue(countryName, out result))
            {
                return long.Parse(result);
            }
            else
            {
                return 0;
            }
        }

        // See http://code.google.com/apis/adwords/docs/appendix/countrycodes.html
        static Dictionary<string, string> _dictNameToCode = new Dictionary<string, string>
        {
            {"AF","2004"},
            {"AL","2008"},
            {"AQ","2010"},
            {"DZ","2012"},
            {"AS","2016"},
            {"AD","2020"},
            {"AO","2024"},
            {"AG","2028"},
            {"AZ","2031"},
            {"AR","2032"},
            {"AU","2036"},
            {"AT","2040"},
            {"BS","2044"},
            {"BH","2048"},
            {"BD","2050"},
            {"AM","2051"},
            {"BB","2052"},
            {"BE","2056"},
            {"BM","2060"},
            {"BT","2064"},
            {"BO","2068"},
            {"BA","2070"},
            {"BW","2072"},
            {"BV","2074"},
            {"BR","2076"},
            {"BZ","2084"},
            {"IO","2086"},
            {"SB","2090"},
            {"VG","2092"},
            {"BN","2096"},
            {"BG","2100"},
            {"MM","2104"},
            {"BI","2108"},
            {"BY","2112"},
            {"KH","2116"},
            {"CM","2120"},
            {"CA","2124"},
            {"CV","2132"},
            {"KY","2136"},
            {"CF","2140"},
            {"LK","2144"},
            {"TD","2148"},
            {"CL","2152"},
            {"CN","2156"},
            {"TW","2158"},
            {"CX","2162"},
            {"CC","2166"},
            {"CO","2170"},
            {"KM","2174"},
            {"YT","2175"},
            {"CG","2178"},
            {"CD","2180"},
            {"CK","2184"},
            {"CR","2188"},
            {"HR","2191"},
            {"CY","2196"},
            {"CZ","2203"},
            {"BJ","2204"},
            {"DK","2208"},
            {"DM","2212"},
            {"DO","2214"},
            {"EC","2218"},
            {"SV","2222"},
            {"GQ","2226"},
            {"ET","2231"},
            {"ER","2232"},
            {"EE","2233"},
            {"FO","2234"},
            {"FK","2238"},
            {"GS","2239"},
            {"FJ","2242"},
            {"FI","2246"},
            {"FR","2250"},
            {"GF","2254"},
            {"PF","2258"},
            {"TF","2260"},
            {"DJ","2262"},
            {"GA","2266"},
            {"GE","2268"},
            {"GM","2270"},
            {"PS","2275"},
            {"DE","2276"},
            {"GH","2288"},
            {"GI","2292"},
            {"KI","2296"},
            {"GR","2300"},
            {"GL","2304"},
            {"GD","2308"},
            {"GP","2312"},
            {"GU","2316"},
            {"GT","2320"},
            {"GN","2324"},
            {"GY","2328"},
            {"HT","2332"},
            {"HM","2334"},
            {"VA","2336"},
            {"HN","2340"},
            {"HK","2344"},
            {"HU","2348"},
            {"IS","2352"},
            {"IN","2356"},
            {"ID","2360"},
            {"IQ","2368"},
            {"IE","2372"},
            {"IL","2376"},
            {"IT","2380"},
            {"CI","2384"},
            {"JM","2388"},
            {"JP","2392"},
            {"KZ","2398"},
            {"JO","2400"},
            {"KE","2404"},
            {"KR","2410"},
            {"KW","2414"},
            {"KG","2417"},
            {"LA","2418"},
            {"LB","2422"},
            {"LS","2426"},
            {"LV","2428"},
            {"LR","2430"},
            {"LY","2434"},
            {"LI","2438"},
            {"LT","2440"},
            {"LU","2442"},
            {"MO","2446"},
            {"MG","2450"},
            {"MW","2454"},
            {"MY","2458"},
            {"MV","2462"},
            {"ML","2466"},
            {"MT","2470"},
            {"MQ","2474"},
            {"MR","2478"},
            {"MU","2480"},
            {"MX","2484"},
            {"MC","2492"},
            {"MN","2496"},
            {"MD","2498"},
            {"ME","2499"},
            {"MS","2500"},
            {"MA","2504"},
            {"MZ","2508"},
            {"OM","2512"},
            {"NA","2516"},
            {"NR","2520"},
            {"NP","2524"},
            {"NL","2528"},
            //{"BQ","2530"},
            {"CW","2531"},
            {"AW","2533"},
            {"SX","2534"},
            {"BQ","2535"},
            {"NC","2540"},
            {"VU","2548"},
            {"NZ","2554"},
            {"NI","2558"},
            {"NE","2562"},
            {"NG","2566"},
            {"NU","2570"},
            {"NF","2574"},
            {"NO","2578"},
            {"MP","2580"},
            {"UM","2581"},
            {"FM","2583"},
            {"MH","2584"},
            {"PW","2585"},
            {"PK","2586"},
            {"PA","2591"},
            {"PG","2598"},
            {"PY","2600"},
            {"PE","2604"},
            {"PH","2608"},
            {"PN","2612"},
            {"PL","2616"},
            {"PT","2620"},
            {"GW","2624"},
            {"TL","2626"},
            {"PR","2630"},
            {"QA","2634"},
            {"RE","2638"},
            {"RO","2642"},
            {"RU","2643"},
            {"RW","2646"},
            {"SH","2654"},
            {"KN","2659"},
            {"AI","2660"},
            {"LC","2662"},
            {"PM","2666"},
            {"VC","2670"},
            {"SM","2674"},
            {"ST","2678"},
            {"SA","2682"},
            {"SN","2686"},
            {"RS","2688"},
            {"SC","2690"},
            {"SL","2694"},
            {"SG","2702"},
            {"SK","2703"},
            {"VN","2704"},
            {"SI","2705"},
            {"SO","2706"},
            {"ZA","2710"},
            {"ZW","2716"},
            {"ES","2724"},
            {"EH","2732"},
            {"SR","2740"},
            {"SJ","2744"},
            {"SZ","2748"},
            {"SE","2752"},
            {"CH","2756"},
            {"TJ","2762"},
            {"TH","2764"},
            {"TG","2768"},
            {"TK","2772"},
            {"TO","2776"},
            {"TT","2780"},
            {"AE","2784"},
            {"TN","2788"},
            {"TR","2792"},
            {"TM","2795"},
            {"TC","2796"},
            {"TV","2798"},
            {"UG","2800"},
            {"UA","2804"},
            {"MK","2807"},
            {"EG","2818"},
            {"GB","2826"},
            {"GG","2831"},
            {"JE","2832"},
            {"TZ","2834"},
            {"US","2840"},
            {"VI","2850"},
            {"BF","2854"},
            {"UY","2858"},
            {"UZ","2860"},
            {"VE","2862"},
            {"WF","2876"},
            {"WS","2882"},
            {"YE","2887"},
            {"ZM","2894"},
            {"XK","2900"}

        };

    }

    public class AdWordsCountriesSelectObject
    {
        public string Name { get; set; }
        public string Code { get; set; }
    }

    public static class AdWordsContriesHelper
    {
        public static List<AdWordsCountriesSelectObject> GetCountries()
        {
            List<AdWordsCountriesSelectObject> list = new List<AdWordsCountriesSelectObject>();

            list.Add(new AdWordsCountriesSelectObject() { Name = "- Select Country -" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Afghanistan", Code = "AF" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Albania", Code = "AL" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Algeria", Code = "DZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "American Samoa", Code = "AS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Andorra", Code = "AD" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Angola", Code = "AO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Anguilla", Code = "AI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Antarctica", Code = "AQ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Antigua & Barbuda", Code = "AG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Argentina", Code = "AR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Armenia", Code = "AM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Aruba", Code = "AW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Ascension Island", Code = "AC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Australia", Code = "AU" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Austria", Code = "AT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Azerbaijan", Code = "AZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Bahamas", Code = "BS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Bahrain", Code = "BH" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Bangladesh", Code = "BD" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Barbados", Code = "BB" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Belarus", Code = "BY" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Belgium", Code = "BE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Belize", Code = "BZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Benin", Code = "BJ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Bermuda", Code = "BM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Bhutan", Code = "BT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Bolivia", Code = "BO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Bosnia & Herzegovina", Code = "BA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Botswana", Code = "BW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Bouvet Island", Code = "BV" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Brazil", Code = "BR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "British Indian Ocean Territory", Code = "IO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "British Virgin Islands", Code = "VG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Brunei", Code = "BN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Bulgaria", Code = "BG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Burkina Faso", Code = "BF" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Burundi", Code = "BI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Cambodia", Code = "KH" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Cameroon", Code = "CM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Canada", Code = "CA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Canary Islands", Code = "IC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Cape Verde", Code = "CV" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Caribbean Netherlands", Code = "BQ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Cayman Islands", Code = "KY" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Central African Republic", Code = "CF" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Ceuta & Melilla", Code = "EA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Chad", Code = "TD" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Chile", Code = "CL" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "China", Code = "CN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Christmas Island", Code = "CX" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Clipperton Island", Code = "CP" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Cocos (Keeling) Islands", Code = "CC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Colombia", Code = "CO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Comoros", Code = "KM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Congo (DRC)", Code = "CD" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Congo (Republic)", Code = "CG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Cook Islands", Code = "CK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Costa Rica", Code = "CR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Croatia", Code = "HR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Cuba", Code = "CU" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Curaçao", Code = "CW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Cyprus", Code = "CY" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Czech Republic", Code = "CZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Côte d’Ivoire", Code = "CI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Denmark", Code = "DK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Diego Garcia", Code = "DG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Djibouti", Code = "DJ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Dominica", Code = "DM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Dominican Republic", Code = "DO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Ecuador", Code = "EC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Egypt", Code = "EG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "El Salvador", Code = "SV" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Equatorial Guinea", Code = "GQ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Eritrea", Code = "ER" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Estonia", Code = "EE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Ethiopia", Code = "ET" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Falkland Islands (Islas Malvinas)", Code = "FK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Faroe Islands", Code = "FO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Fiji", Code = "FJ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Finland", Code = "FI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "France", Code = "FR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "French Guiana", Code = "GF" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "French Polynesia", Code = "PF" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "French Southern Territories", Code = "TF" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Gabon", Code = "GA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Gambia", Code = "GM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Georgia", Code = "GE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Germany", Code = "DE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Ghana", Code = "GH" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Gibraltar", Code = "GI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Greece", Code = "GR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Greenland", Code = "GL" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Grenada", Code = "GD" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Guadeloupe", Code = "GP" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Guam", Code = "GU" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Guatemala", Code = "GT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Guernsey", Code = "GG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Guinea", Code = "GN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Guinea-Bissau", Code = "GW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Guyana", Code = "GY" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Haiti", Code = "HT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Heard & McDonald Islands", Code = "HM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Honduras", Code = "HN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Hong Kong", Code = "HK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Hungary", Code = "HU" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Iceland", Code = "IS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "India", Code = "IN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Indonesia", Code = "ID" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Iran", Code = "IR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Iraq", Code = "IQ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Ireland", Code = "IE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Isle of Man", Code = "IM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Israel", Code = "IL" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Italy", Code = "IT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Jamaica", Code = "JM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Japan", Code = "JP" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Jersey", Code = "JE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Jordan", Code = "JO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Kazakhstan", Code = "KZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Kenya", Code = "KE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Kiribati", Code = "KI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Kosovo", Code = "XK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Kuwait", Code = "KW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Kyrgyzstan", Code = "KG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Laos", Code = "LA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Latvia", Code = "LV" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Lebanon", Code = "LB" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Lesotho", Code = "LS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Liberia", Code = "LR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Libya", Code = "LY" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Liechtenstein", Code = "LI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Lithuania", Code = "LT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Luxembourg", Code = "LU" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Macau", Code = "MO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Macedonia (FYROM)", Code = "MK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Madagascar", Code = "MG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Malawi", Code = "MW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Malaysia", Code = "MY" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Maldives", Code = "MV" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Mali", Code = "ML" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Malta", Code = "MT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Marshall Islands", Code = "MH" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Martinique", Code = "MQ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Mauritania", Code = "MR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Mauritius", Code = "MU" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Mayotte", Code = "YT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Mexico", Code = "MX" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Micronesia", Code = "FM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Moldova", Code = "MD" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Monaco", Code = "MC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Mongolia", Code = "MN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Montenegro", Code = "ME" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Montserrat", Code = "MS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Morocco", Code = "MA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Mozambique", Code = "MZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Myanmar (Burma)", Code = "MM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Namibia", Code = "NA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Nauru", Code = "NR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Nepal", Code = "NP" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Netherlands", Code = "NL" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "New Caledonia", Code = "NC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "New Zealand", Code = "NZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Nicaragua", Code = "NI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Niger", Code = "NE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Nigeria", Code = "NG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Niue", Code = "NU" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Norfolk Island", Code = "NF" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "North Korea", Code = "KP" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Northern Mariana Islands", Code = "MP" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Norway", Code = "NO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Oman", Code = "OM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Pakistan", Code = "PK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Palau", Code = "PW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Palestine", Code = "PS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Panama", Code = "PA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Papua New Guinea", Code = "PG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Paraguay", Code = "PY" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Peru", Code = "PE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Philippines", Code = "PH" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Pitcairn Islands", Code = "PN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Poland", Code = "PL" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Portugal", Code = "PT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Puerto Rico", Code = "PR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Qatar", Code = "QA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Romania", Code = "RO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Russia", Code = "RU" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Rwanda", Code = "RW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Réunion", Code = "RE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Samoa", Code = "WS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "San Marino", Code = "SM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Saudi Arabia", Code = "SA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Senegal", Code = "SN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Serbia", Code = "RS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Seychelles", Code = "SC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Sierra Leone", Code = "SL" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Singapore", Code = "SG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Sint Maarten", Code = "SX" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Slovakia", Code = "SK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Slovenia", Code = "SI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Solomon Islands", Code = "SB" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Somalia", Code = "SO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "South Africa", Code = "ZA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "South Georgia & South Sandwich Islands", Code = "GS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "South Korea", Code = "KR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "South Sudan", Code = "SS" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Spain", Code = "ES" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Sri Lanka", Code = "LK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "St. Barthélemy", Code = "BL" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "St. Helena", Code = "SH" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "St. Kitts & Nevis", Code = "KN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "St. Lucia", Code = "LC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "St. Martin", Code = "MF" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "St. Pierre & Miquelon", Code = "PM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "St. Vincent & Grenadines", Code = "VC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Sudan", Code = "SD" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Suriname", Code = "SR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Svalbard & Jan Mayen", Code = "SJ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Swaziland", Code = "SZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Sweden", Code = "SE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Switzerland", Code = "CH" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Syria", Code = "SY" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "São Tomé & Príncipe", Code = "ST" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Taiwan", Code = "TW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Tajikistan", Code = "TJ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Tanzania", Code = "TZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Thailand", Code = "TH" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Timor-Leste", Code = "TL" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Togo", Code = "TG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Tokelau", Code = "TK" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Tonga", Code = "TO" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Trinidad & Tobago", Code = "TT" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Tristan da Cunha", Code = "TA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Tunisia", Code = "TN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Turkey", Code = "TR" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Turkmenistan", Code = "TM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Turks & Caicos Islands", Code = "TC" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Tuvalu", Code = "TV" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "U.S. Outlying Islands", Code = "UM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "U.S. Virgin Islands", Code = "VI" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Uganda", Code = "UG" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Ukraine", Code = "UA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "United Arab Emirates", Code = "AE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "United Kingdom", Code = "GB" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "United States", Code = "US" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Uruguay", Code = "UY" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Uzbekistan", Code = "UZ" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Vanuatu", Code = "VU" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Vatican City", Code = "VA" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Venezuela", Code = "VE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Vietnam", Code = "VN" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Wallis & Futuna", Code = "WF" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Western Sahara", Code = "EH" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Yemen", Code = "YE" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Zambia", Code = "ZM" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Zimbabwe", Code = "ZW" });
            list.Add(new AdWordsCountriesSelectObject() { Name = "Åland Islands", Code = "AX" });


            return list;
        }

    }
}
