﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    //public class DsaUrlsHelper
    //{
    //    public static bool IsMarketplace(string url)
    //    {
    //        List<string> marketplaces = new List<string>();
    //        marketplaces.Add("etsy.com");
    //        marketplaces.Add("wix.com");
    //        marketplaces.Add("myshopify.com");

    //        marketplaces.Add("facebook.com");
    //        marketplaces.Add("instagram.com");
    //        marketplaces.Add("www.shop.com/");
    //        marketplaces.Add("//shop.com/");
    //        marketplaces.Add("ebay.com");
    //        marketplaces.Add("twitter.com");
    //        marketplaces.Add("linkedin.com");
    //        marketplaces.Add("dawanda.com");

    //        foreach (var item in marketplaces)
    //        {
    //            if (url.Contains(item))
    //                return true;
    //        }

    //        return false;
    //    }

    //    public static string GetStoreHomePage(string url)
    //    {
    //        Uri uri = null;
    //        if (!Uri.IsWellFormedUriString(url, UriKind.Absolute))
    //        {
    //            return null;
    //        }

    //        Uri.TryCreate(url, UriKind.Absolute, out uri);

    //        if (IsSubdomain(uri))
    //        {
    //            return uri.Host;
    //        }

    //        string[] segments = uri.Segments;
    //        if (segments.Count() > 1)
    //        {
    //            return uri.Host + "/" + segments[1].TrimEnd('/');
    //        }
    //        return uri.Host;
    //    }


    //    public static DsaUrls GetDsaUrls(string url)
    //    {

    //        string cleanHost = url;
    //        if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
    //        {
    //            Uri uri = new Uri(url);
    //            cleanHost = uri.Host;
    //        }

    //        if (url.Contains("myshopify.com"))
    //        {
    //            return new DsaUrls()
    //            {
    //                Display = cleanHost,
    //                CriteriaUrl = cleanHost,
    //                Website = "myshopify.com"
    //            };
    //        }
    //        else if (url.Contains("wix.com"))
    //        {
    //            return new DsaUrls()
    //            {
    //                Display = cleanHost,
    //                CriteriaUrl = cleanHost,
    //                Website = "wix.com"
    //            };
    //        }
    //        else if (IsSubdomain(url))
    //        {
    //            return new DsaUrls()
    //            {
    //                Display = cleanHost,
    //                CriteriaUrl = cleanHost + "/DSA_WILL_NOT_WORK",
    //                Website = GetTopDomainOfSubdomain(cleanHost)
    //            };
    //        }

    //        return new DsaUrls()
    //        {
    //            Display = cleanHost,
    //            CriteriaUrl = cleanHost + "/DSA_WILL_NOT_WORK",
    //            Website = cleanHost
    //        };
    //    }

    //    private static bool IsSubdomain(Uri uri)
    //    {

    //        if (uri.HostNameType == UriHostNameType.Dns)
    //        {
    //            string host = uri.Host;
    //            return IsSubdomain(host);
    //            //var nodes = host.Split('.');

    //            //if (nodes[0] == "www" || nodes.Count() == 2)
    //            //{
    //            //    return false;
    //            //}
    //            //else
    //            //{
    //            //    return true;
    //            //}
    //        }

    //        return false;
    //    }

    //    public static bool IsSubdomain(string host)
    //    {
    //        var nodes = host.Split('.');

    //        if (nodes[0] == "www" || nodes.Count() == 2)
    //        {
    //            return false;
    //        }
    //        else
    //        {
    //            return true;
    //        }
    //    }


    //    public static string GetTopDomainOfSubdomain(string host)
    //    {
    //        var nodes = host.Split('.');
    //        string domain = nodes[1] + "." + nodes[2];
    //        return domain;
    //    }

    //    public class DsaUrls
    //    {
    //        public string Display { get; set; }
    //        public string Website { get; set; }
    //        public string CriteriaUrl { get; set; }
    //    }
    //}
}
