﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Models.AdWords
{
    public interface IAdwordsMCCConfiguration
    {
        string BillingAccountId { get; }
        string MerchantClientCustomerId { get; }
        string AccountCurrency { get; }
        string PrimaryBillingId { get; }
        long DailyAmount { get; }
        string OrderStartDateTime { get; }
        string OrderEndDateTime { get; }

        string DateTimeZone { get; }

        bool UseFirstUrlAsIs { get; }
        string CampaignDailyBudget { get; }
    }
}
