﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    static public class AdWordsLanguagesCodes
    {
        public static long GetCode(string language)
        {
            string result;
            if (_dictLangToCode.TryGetValue(language, out result))
            {
                return long.Parse(result);
            }
            else
            {
                return 0;
            }
        }

        public static string GetAdWordsLanguagesCode(string language)
        {
            List<String> myKeys = _dictLangToCode.Keys.ToList();

            if (language == "zh") //zh	 -> zh_CN	or  zh_TW
            {
                language = "zh_CN";
            }

            if (language == "fl") //Filipino	tl
            {
                language = "tl";
            }

            if (language == "he") //he	Hebrew	iw
            {
                language = "iw";
            }
            
            string adwords_code = myKeys.Where(x => x == language).FirstOrDefault();
            if (!string.IsNullOrEmpty(adwords_code))
                return adwords_code;

            return "en";
        }

        // See http://code.google.com/apis/adwords/docs/appendix/languagecodes.html
        static Dictionary<string, string> _dictLangToCode = new Dictionary<string, string>
        {
            {"ar","1019"},
            {"bg","1020"},
            {"ca","1038"},
            {"zh_CN","1017"},
            {"zh_TW","1018"},
            {"hr","1039"},
            {"cs","1021"},
            {"da","1009"},
            {"nl","1010"},
            {"en","1000"},
            {"et","1043"},
            {"tl","1042"},
            {"fi","1011"},
            {"fr","1002"},
            {"de","1001"},
            {"el","1022"},
            {"iw","1027"},
            {"hi","1023"},
            {"hu","1024"},
            {"is","1026"},
            {"id","1025"},
            {"it","1004"},
            {"ja","1005"},
            {"ko","1012"},
            {"lv","1028"},
            {"lt","1029"},
            {"ms","1102"},
            {"no","1013"},
            {"fa","1064"},
            {"pl","1030"},
            {"pt","1014"},
            {"ro","1032"},
            {"ru","1031"},
            {"sr","1035"},
            {"sk","1033"},
            {"sl","1034"},
            {"es","1003"},
            {"sv","1015"},
            {"th","1044"},
            {"tr","1037"},
            {"uk","1036"},
            {"ur","1041"},
            {"vi","1040"}
        };


    }

    public class AdWordsLanguagesSelectObject
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
    }


    public static class AdWordsLanguagesHelper
    {

        public static string SetLanguageName(string name, List<AdWordsLanguagesSelectObject> languages)
        {
            string language = name;
            //en-gb 
            try
            {
                if (name.Contains('-'))
                {

                    string[] splitted = name.Split('-');
                    string lang_code = splitted[0];
                    string country_code = splitted[1];

                    AdWordsLanguagesSelectObject languagesSelectObject = languages.Where(x => x.Code == lang_code).SingleOrDefault();
                    if (languagesSelectObject != null)
                    {
                        language = languagesSelectObject.Name;

                        //AdWordsCountriesSelectObject countriesSelectObject = countries.Where(x => x.Code == country_code.ToUpper()).SingleOrDefault();
                        //if (countriesSelectObject != null)
                        //{
                        //    language = language + " " + countriesSelectObject.Name;
                        //}
                    }
                }
                else
                {
                    AdWordsLanguagesSelectObject languagesSelectObject = languages.Where(x => x.Code == name).SingleOrDefault();
                    if (languagesSelectObject != null)
                    {
                        language = languagesSelectObject.Name;
                    }
                }

            }
            catch (Exception) { }

            return language;
        }



        public static List<AdWordsLanguagesSelectObject> GetLanguages()
        {
            List<AdWordsLanguagesSelectObject> list = new List<AdWordsLanguagesSelectObject>();

            list.Add(new AdWordsLanguagesSelectObject() { ID = 0, Name = "- Select Language -" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1019, Name = "Arabic", Code = "ar" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1020, Name = "Bulgarian", Code = "bg" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1038, Name = "Catalan", Code = "ca" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1017, Name = "Chinese (simplified)", Code = "zh_CN" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1018, Name = "Chinese (traditional)", Code = "zh_TW" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1039, Name = "Croatian", Code = "hr" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1021, Name = "Czech", Code = "cs" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1009, Name = "Danish", Code = "da" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1010, Name = "Dutch", Code = "nl" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1000, Name = "English", Code = "en" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1043, Name = "Estonian", Code = "et" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1042, Name = "Filipino", Code = "tl" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1011, Name = "Finnish", Code = "fi" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1002, Name = "French", Code = "fr" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1001, Name = "German", Code = "de" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1022, Name = "Greek", Code = "el" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1027, Name = "Hebrew", Code = "iw" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1023, Name = "Hindi", Code = "hi" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1024, Name = "Hungarian", Code = "hu" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1026, Name = "Icelandic", Code = "is" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1025, Name = "Indonesian", Code = "id" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1004, Name = "Italian", Code = "it" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1005, Name = "Japanese", Code = "ja" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1012, Name = "Korean", Code = "ko" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1028, Name = "Latvian", Code = "lv" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1029, Name = "Lithuanian", Code = "lt" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1102, Name = "Malay", Code = "ms" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1013, Name = "Norwegian", Code = "no" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1064, Name = "Persian", Code = "fa" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1030, Name = "Polish", Code = "pl" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1014, Name = "Portuguese", Code = "pt" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1032, Name = "Romanian", Code = "ro" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1031, Name = "Russian", Code = "ru" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1035, Name = "Serbian", Code = "sr" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1033, Name = "Slovak", Code = "sk" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1034, Name = "Slovenian", Code = "sl" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1003, Name = "Spanish", Code = "es" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1015, Name = "Swedish", Code = "sv" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1044, Name = "Thai", Code = "th" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1037, Name = "Turkish", Code = "tr" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1036, Name = "Ukrainian", Code = "uk" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1041, Name = "Urdu", Code = "ur" });
            list.Add(new AdWordsLanguagesSelectObject() { ID = 1040, Name = "Vietnamese", Code = "vi" });

            return list;
        }
    }
}
