﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Models.AdWords
{
    public class LocationData
    {
        public string Country { get; set; }
        public string CountryCode { get; set; }

        public string Language { get; set; }
        public string LanguageCode { get; set; }

        public string Currency { get; set; }
        public string CurrencyCode { get; set; }
    }

    public static class LocationsDataHelper
    {
        public static string GetCurrencyByCountryCodes(List<string> countryCodes)
        {
            string currency = null;

            List<LocationData> data = GetShoppingAvailableCountries();

            foreach (var c in countryCodes)
            {
                string currCurrency = null;

                LocationData current_data = data.Where(x => x.CountryCode.ToLower() == c.ToLower()).SingleOrDefault();
                if (current_data != null)
                {
                    currCurrency = current_data.CurrencyCode;
                    if (currCurrency == "USD")
                    {
                        currency = currCurrency;
                        break;
                    }
                }

                if (string.IsNullOrEmpty(currency))
                {
                    currency = currCurrency;
                }
            }

            //if (currency == null)
            //{
            //    currency = "USD";
            //}

            return currency;
        }

        //https://support.google.com/merchants/answer/160637?hl=en
        public static List<LocationData> GetShoppingAvailableCountries()
        {
            List<LocationData> list = new List<LocationData>();

            list.Add(new LocationData() { Country = "Argentina", Language = "Spanish", CurrencyCode = "ARS", Currency = "Argentinian Peso", CountryCode = "AR" });
            list.Add(new LocationData() { Country = "Australia", Language = "English", CurrencyCode = "AUD", Currency = "Australian dollar", CountryCode = "AU" });
            list.Add(new LocationData() { Country = "Austria", Language = "German", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "AT" });
            list.Add(new LocationData() { Country = "Belgium", Language = "French", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "BE" });
            list.Add(new LocationData() { Country = "Brazil", Language = "Portuguese", CurrencyCode = "BRL", Currency = "Brazilian Real", CountryCode = "BR" });
            list.Add(new LocationData() { Country = "Canada", Language = "English", CurrencyCode = "CAD", Currency = "Canadian Dollar", CountryCode = "CA" });
            list.Add(new LocationData() { Country = "Chile", Language = "Spanish", CurrencyCode = "CLP", Currency = "Chilean Peso", CountryCode = "CL" });
            list.Add(new LocationData() { Country = "Colombia", Language = "Spanish", CurrencyCode = "COP", Currency = "Colombian Peso", CountryCode = "CO" });
            list.Add(new LocationData() { Country = "Czechia", Language = "Czech", CurrencyCode = "CZK", Currency = "Czechian Koruna", CountryCode = "CZ" });
            list.Add(new LocationData() { Country = "Denmark", Language = "Danish", CurrencyCode = "DKK", Currency = "Danish Krone", CountryCode = "DK" });
            list.Add(new LocationData() { Country = "France", Language = "French", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "FR" });
            list.Add(new LocationData() { Country = "Germany", Language = "German", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "DE" });

            list.Add(new LocationData() { Country = "Greece", Language = "Modern Greek", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "GR" });

            list.Add(new LocationData() { Country = "Hong Kong", Language = "Traditional Chinese", CurrencyCode = "HKD", Currency = "Hong Kong Dollar", CountryCode = "HK" });

            list.Add(new LocationData() { Country = "Hungary", Language = "Hungarian", CurrencyCode = "HUF", Currency = "Hungarian Forint", CountryCode = "HU" });

            list.Add(new LocationData() { Country = "India", Language = "English", CurrencyCode = "INR", Currency = "Indian Rupee", CountryCode = "IN" });
            list.Add(new LocationData() { Country = "Indonesia", Language = "Indonesian", CurrencyCode = "IDR", Currency = "Indonesian Rupiah", CountryCode = "ID" });
            list.Add(new LocationData() { Country = "Ireland", Language = "English", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "IE" });
            list.Add(new LocationData() { Country = "Italy", Language = "Italian", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "IT" });
            list.Add(new LocationData() { Country = "Israel", Language = "Hebrew", CurrencyCode = "ILS", Currency = "Israeli New Shekel", CountryCode = "IL" });
            list.Add(new LocationData() { Country = "Japan", Language = "Japanese", CurrencyCode = "JPY", Currency = "Japanese Yen", CountryCode = "JP" });
            list.Add(new LocationData() { Country = "Malaysia", Language = "English", CurrencyCode = "MYR", Currency = "Malaysia Ringgit", CountryCode = "MY" });
            list.Add(new LocationData() { Country = "Mexico", Language = "Spanish", CurrencyCode = "MXN", Currency = "Mexican Peso", CountryCode = "MX" });
            list.Add(new LocationData() { Country = "Netherlands", Language = "Dutch", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "NL" });
            list.Add(new LocationData() { Country = "New Zealand", Language = "English", CurrencyCode = "NZD", Currency = "New Zealand Dollar", CountryCode = "NZ" });
            list.Add(new LocationData() { Country = "Norway", Language = "Norwegian", CurrencyCode = "NOK", Currency = "Norwegian Krone", CountryCode = "NO" });

            list.Add(new LocationData() { Country = "Peru", Language = "Spanish", CurrencyCode = "PEN", Currency = "Sol", CountryCode = "PE" });

            list.Add(new LocationData() { Country = "Philippines", Language = "English", CurrencyCode = "PHP", Currency = "Philippine Peso", CountryCode = "PH" });
            list.Add(new LocationData() { Country = "Poland", Language = "Polish", CurrencyCode = "PLN", Currency = "Poland Złoty", CountryCode = "PL" });
            list.Add(new LocationData() { Country = "Portugal", Language = "Portuguese", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "PT" });
            list.Add(new LocationData() { Country = "Russia", Language = "Russian", CurrencyCode = "RUB", Currency = "Russian Ruble", CountryCode = "RU" });
            
            list.Add(new LocationData() { Country = "Saudi Arabia", Language = "Arabic", CurrencyCode = "SAR", Currency = "Saudi Riyal", CountryCode = "SA" });
            list.Add(new LocationData() { Country = "Singapore", Language = "English", CurrencyCode = "SGD", Currency = "Singapore dollar", CountryCode = "SG" });
            list.Add(new LocationData() { Country = "Slovakia", Language = "Slovak", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "SK" });
            list.Add(new LocationData() { Country = "Spain", Language = "Spanish", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "ES" });

            list.Add(new LocationData() { Country = "South Africa", Language = "English", CurrencyCode = "ZAR", Currency = "Rand", CountryCode = "ZA" });

            list.Add(new LocationData() { Country = "Sweden", Language = "Swedish", CurrencyCode = "SEK", Currency = "Swedish Krona", CountryCode = "SE" });
            list.Add(new LocationData() { Country = "Switzerland", Language = "English", CurrencyCode = "CHF", Currency = "Swiss Franc", CountryCode = "CH" });
            list.Add(new LocationData() { Country = "Taiwan", Language = "Traditional Chinese", CurrencyCode = "TWD", Currency = "New Taiwan Dollar", CountryCode = "TW" });

            list.Add(new LocationData() { Country = "Tunisia", Language = "Arabic", CurrencyCode = "DT", Currency = "Tunisian Dinar", CountryCode = "TN" });

            list.Add(new LocationData() { Country = "Turkey", Language = "Turkish", CurrencyCode = "TRY", Currency = "Turkish Lira", CountryCode = "TR" });
            list.Add(new LocationData() { Country = "Ukraine", Language = "Ukrainian", CurrencyCode = "UAH", Currency = "Ukrainian Hryvnia", CountryCode = "UA" });
            list.Add(new LocationData() { Country = "United Arab Emirates", Language = "English", CurrencyCode = "AED", Currency = "United Arab Emirates Dirham", CountryCode = "AE" });
            list.Add(new LocationData() { Country = "United Kingdom", Language = "English", CurrencyCode = "GBP", Currency = "British Pound", CountryCode = "GB" });
            list.Add(new LocationData() { Country = "United States", Language = "English", CurrencyCode = "USD", Currency = "US Dollar", CountryCode = "US" });
            list.Add(new LocationData() { Country = "Vietnam", Language = "Vietnamese", CurrencyCode = "VND", Currency = "Vietnamese Dong", CountryCode = "VN" });


            list.Add(new LocationData() { Country = "Algeria", Language = "Arabic", CurrencyCode = "DZD", Currency = "Algerian Dinar", CountryCode = "DZ" });
            list.Add(new LocationData() { Country = "Angola", Language = "Portuguese", CurrencyCode = "AOA", Currency = "Angolan Kwanza", CountryCode = "AO" });
            list.Add(new LocationData() { Country = "Bangladesh", Language = "Bengali", CurrencyCode = "BDT", Currency = "Bangladeshi Taka", CountryCode = "BD" });
            list.Add(new LocationData() { Country = "Cameroon", Language = "French", CurrencyCode = "XAF", Currency = "Central African CFA Franc", CountryCode = "CM" });
            list.Add(new LocationData() { Country = "Cambodia", Language = "Khmer", CurrencyCode = "KHR", Currency = "Cambodian Riel", CountryCode = "KH" });
            list.Add(new LocationData() { Country = "Costa Rica", Language = "Spanish", CurrencyCode = "CRC", Currency = "Costa Rican Colón", CountryCode = "CR" });
            list.Add(new LocationData() { Country = "Côte d'Ivoire", Language = "French", CurrencyCode = "XOF", Currency = "West African CFA Franc", CountryCode = "CI" });
            list.Add(new LocationData() { Country = "Dominican Republic", Language = "Spanish", CurrencyCode = "DOP", Currency = "Dominican Peso", CountryCode = "DO" });
            list.Add(new LocationData() { Country = "El Salvador", Language = "Spanish", CurrencyCode = "SVC", Currency = "Salvadoran Colón", CountryCode = "SV" });
            list.Add(new LocationData() { Country = "Ethiopia", Language = "Amharic", CurrencyCode = "ETB", Currency = "Ethiopian Birr", CountryCode = "ET" });
            list.Add(new LocationData() { Country = "Ghana", Language = "English", CurrencyCode = "GHS", Currency = "Ghanaian Cedi", CountryCode = "GH" });
            list.Add(new LocationData() { Country = "Guatemala", Language = "Spanish", CurrencyCode = "GTQ", Currency = "Guatemalan Quetzal", CountryCode = "GT" });
            list.Add(new LocationData() { Country = "Kenya", Language = "English", CurrencyCode = "KES", Currency = "Kenyan Shilling", CountryCode = "KE" });
            list.Add(new LocationData() { Country = "Madagascar", Language = "Malagasy", CurrencyCode = "MGA", Currency = "Malagasy Ariary", CountryCode = "MG" });
            list.Add(new LocationData() { Country = "Mauritius", Language = "English", CurrencyCode = "MUR", Currency = "Mauritian Rupee", CountryCode = "MU" });
            list.Add(new LocationData() { Country = "Morocco", Language = "Arabic", CurrencyCode = "MAD", Currency = "Moroccan Dirham", CountryCode = "MA" });
            list.Add(new LocationData() { Country = "Mozambique", Language = "Portuguese", CurrencyCode = "MZN", Currency = "Mozambican Metical", CountryCode = "MZ" });
            list.Add(new LocationData() { Country = "Myanmar (Burma)", Language = "Burmese", CurrencyCode = "MMK", Currency = "Burmese Kyat", CountryCode = "MM" });
            list.Add(new LocationData() { Country = "Nepal", Language = "Nepali", CurrencyCode = "NPR", Currency = "Nepalese Rupee", CountryCode = "NP" });
            list.Add(new LocationData() { Country = "Nicaragua", Language = "Spanish", CurrencyCode = "NIO", Currency = "Nicaraguan Córdoba", CountryCode = "NI" });
            list.Add(new LocationData() { Country = "Nigeria", Language = "English", CurrencyCode = "NGN", Currency = "Nigerian Naira", CountryCode = "NG" });
            list.Add(new LocationData() { Country = "Pakistan", Language = "Urdu", CurrencyCode = "PKR", Currency = "Pakistani Rupee", CountryCode = "PK" });
            list.Add(new LocationData() { Country = "Panama", Language = "Spanish", CurrencyCode = "PAB", Currency = "Panamanian Balboa", CountryCode = "PA" });
            list.Add(new LocationData() { Country = "Puerto Rico", Language = "Spanish", CurrencyCode = "USD", Currency = "United States Dollar", CountryCode = "PR" });

            list.Add(new LocationData() { Country = "Senegal", Language = "French", CurrencyCode = "XOF", Currency = "West African CFA Franc", CountryCode = "SN" });
            list.Add(new LocationData() { Country = "Sri Lanka", Language = "Sinhala, Tamil", CurrencyCode = "LKR", Currency = "Sri Lankan Rupee", CountryCode = "LK" });
            list.Add(new LocationData() { Country = "Tanzania", Language = "Swahili, English", CurrencyCode = "TZS", Currency = "Tanzanian Shilling", CountryCode = "TZ" });
            //list.Add(new LocationData() { Country = "Tunisia", Language = "Arabic", CurrencyCode = "TND", Currency = "Tunisian Dinar", CountryCode = "TN" });
            list.Add(new LocationData() { Country = "Uganda", Language = "English, Swahili", CurrencyCode = "UGX", Currency = "Ugandan Shilling", CountryCode = "UG" });
            list.Add(new LocationData() { Country = "Venezuela", Language = "Spanish", CurrencyCode = "VES", Currency = "Venezuelan Bolívar", CountryCode = "VE" });
            list.Add(new LocationData() { Country = "Zambia", Language = "English", CurrencyCode = "ZMW", Currency = "Zambian Kwacha", CountryCode = "ZM" });
            list.Add(new LocationData() { Country = "Zimbabwe", Language = "English, Shona, Sindebele", CurrencyCode = "ZWL", Currency = "Zimbabwean Dollar", CountryCode = "ZW" });
            //Added at 2024-03-03
            list.Add(new LocationData() { Country = "Bahrain", Language = "Arabic", CurrencyCode = "BHD", Currency = "Bahraini dinar", CountryCode = "BH" });
            list.Add(new LocationData() { Country = "belarus", Language = "Belarusian", CurrencyCode = "BYN", Currency = "Belarusian Ruble", CountryCode = "BY" });
            list.Add(new LocationData() { Country = "Cote d'Ivoire", Language = "French", CurrencyCode = "XOF", Currency = "West African CFA france", CountryCode = "CI" });

            list.Add(new LocationData() { Country = "Ecuador", Language = "Spanish", CurrencyCode = "USD", Currency = "US Dollar", CountryCode = "EC" });


            list.Add(new LocationData() { Country = "Egypt", Language = "Arabic", CurrencyCode = "EGP", Currency = "Egyptian Pound", CountryCode = "EG" });
            list.Add(new LocationData() { Country = "Finland", Language = "Finnish", CurrencyCode = "EUR", Currency = "Euro", CountryCode = "FL" });
            list.Add(new LocationData() { Country = "Georgia", Language = "	Georgian", CurrencyCode = "GEL", Currency = "Georgian Lari", CountryCode = "GE" });

            list.Add(new LocationData() { Country = "Jordan", Language = "Arabic", CurrencyCode = "JOD", Currency = "Jordanian Dinar", CountryCode = "JO" });

            list.Add(new LocationData() { Country = "Kazakhstan", Language = "Kazakh", CurrencyCode = "KZT", Currency = "Kazakhstani Tenge", CountryCode = "KZ" });

            list.Add(new LocationData() { Country = "Kuwait", Language = "Arabic", CurrencyCode = "KWD", Currency = "Kuwaiti Dinar", CountryCode = "KW" });

            list.Add(new LocationData() { Country = "Lebanon", Language = "Arabic", CurrencyCode = "LBP", Currency = "Lebanese Pound", CountryCode = "LB" });

            list.Add(new LocationData() { Country = "Oman", Language = "Arabic", CurrencyCode = "OMR", Currency = "Omani Rial", CountryCode = "OM" });

            list.Add(new LocationData() { Country = "Paraguay", Language = "Spanish", CurrencyCode = "PYG", Currency = "Paraguay Guarani", CountryCode = "PY" });

            list.Add(new LocationData() { Country = "Romania", Language = "Romanian", CurrencyCode = "RON", Currency = "Romanian Leu", CountryCode = "RO" });

            list.Add(new LocationData() { Country = "South Korea", Language = "Korean", CurrencyCode = "KRW", Currency = "South Korean Won", CountryCode = "KR" });

            list.Add(new LocationData() { Country = "Thailand", Language = "Thai", CurrencyCode = "THB", Currency = "Thai Baht", CountryCode = "TH" });

            list.Add(new LocationData() { Country = "Uruguay", Language = "Spanish", CurrencyCode = "UYU", Currency = "Uruguayan Peso", CountryCode = "UY" });
            list.Add(new LocationData() { Country = "Uzbekistan", Language = "Uzbek", CurrencyCode = "UZS", Currency = "Uzbekistani Som", CountryCode = "UZ" });
            return list;
        }
    }
}
