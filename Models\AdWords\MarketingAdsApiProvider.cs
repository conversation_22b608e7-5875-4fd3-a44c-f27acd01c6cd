﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Storeya.Core.Models.FbAds;
using System.Security.Policy;
using Storeya.Core.Models.TrafficBoosterModels;
using System.EnterpriseServices;
using System.Collections.Specialized;
using Newtonsoft.Json;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

namespace Storeya.Core.Models.AdWords
{
    public class MarketingAdsApiProvider
    {
        string APP_ID = ConfigHelper.GetValue("FBAdsAppId");
        string APP_SECRET = ConfigHelper.GetValue("FBAdsAppSecret");
        private static string _fbVersion = ConfigHelper.GetValue("FBAdsAPIVersion");
        string _graph_endPoint = "https://graph.facebook.com";
        string _token = "https://graph.facebook.com";
        //int _level = 0;

        public MarketingAdsApiProvider(string token)
        {
            _token = token;
        }

        public string GetLongAccessToken(string userAcccessToken)
        {
            // Get the long-lived user access token
            string long_lived_userAccessToken_url = string.Format("{0}/oauth/access_token?grant_type=fb_exchange_token&client_id={1}&client_secret={2}&fb_exchange_token={3}", _graph_endPoint, APP_ID, APP_SECRET, userAcccessToken);
            string long_lived_userAccessToken_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(long_lived_userAccessToken_url);
            dynamic long_lived_userAccessToken_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(long_lived_userAccessToken_response);
            string long_lived_userAccessToken = long_lived_userAccessToken_parsedJson.access_token;

            // Get the code, using a long-lived user access token
            string code_url = string.Format("{0}/oauth/client_code?access_token={1}&redirect_uri={2}&client_id={3}&client_secret={4}", _graph_endPoint, long_lived_userAccessToken, HttpHelper.GetCurrentDomainHttps(), APP_ID, APP_SECRET);
            string code_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(code_url);
            dynamic code_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(code_response);
            string code = code_parsedJson.code;

            // Redeeming the code for an access token    	
            string machine_id = Environment.MachineName;       //"00330-80000-00000-AA230";
            string accessToken_url = string.Format("{0}/oauth/access_token?code={1}&client_id={2}&redirect_uri={3}&machine_id={4}", _graph_endPoint, code, APP_ID, HttpHelper.GetCurrentDomainHttps(), machine_id);
            string accessToken_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(accessToken_url);
            dynamic accessToken_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(accessToken_response);
            string access_token = accessToken_parsedJson.access_token;

            return access_token;
        }

        public ParsedUser GetUserID(string token)
        {
            string get_user_url = string.Format("{0}/{1}/me?fields=id,name&access_token={2}", _graph_endPoint, _fbVersion, token);
            string user_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(get_user_url);
            dynamic parsed_user = Newtonsoft.Json.JsonConvert.DeserializeObject(user_response);

            ParsedUser user = new ParsedUser() { id = parsed_user.id, name = parsed_user.name };
            return user;
        }

        public List<Client> GetOutstandingRequests(string businessID, string token)
        {
            List<Client> clients = null;

            //curl "https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/clients?access_token=<ACCESS_TOKEN>"
            string clients_url = string.Format("{0}/{1}/{2}/clients?access_token={3}", _graph_endPoint, _fbVersion, businessID, token);

            FbApiClient client = new FbApiClient(token);
            var response = client.Get(clients_url);

            if (response.Status != System.Net.HttpStatusCode.OK)
            {
                throw new Exception("Failed to get data from " + response.UrlUsed + "." + response.Content);
            }
            dynamic clients_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(response.Content);
            if (clients_parsedJson.data != null)
            {
                clients = new List<Client>();
                foreach (var clientItem in clients_parsedJson.data)
                {
                    Client c = new Client();
                    c.id = clientItem.id;
                    c.name = clientItem.name;

                    if (clientItem.adaccount_permissions != null)
                    {
                        List<BusinessPermissions> adaccount_permissions = new List<BusinessPermissions>();
                        foreach (var item in clientItem.adaccount_permissions)
                        {
                            BusinessPermissions per = SetPermission(item);
                            adaccount_permissions.Add(per);
                        }
                        c.adaccount_permissions = adaccount_permissions;
                    }

                    if (clientItem.page_permissions != null)
                    {
                        List<BusinessPermissions> page_permissions = new List<BusinessPermissions>();
                        foreach (var item in clientItem.page_permissions)
                        {
                            BusinessPermissions per = SetPermission(item);
                            page_permissions.Add(per);
                        }

                        c.page_permissions = page_permissions;
                    }

                    clients.Add(c);
                }
            }
            return clients;
        }

        private static BusinessPermissions SetPermission(dynamic item)
        {
            BusinessPermissions per = new BusinessPermissions();

            if (item.permitted_roles != null)
            {
                per.permitted_roles = new List<string>();
                foreach (var permitted_role in item.permitted_roles)
                {
                    per.permitted_roles.Add(permitted_role.ToString());
                }
            }

            string access_requested_time = item.access_requested_time.ToString();
            per.access_requested_time = access_requested_time;
            string access_updated_time = item.access_updated_time.ToString();
            per.access_updated_time = access_updated_time;
            per.access_status = item.access_status;
            per.id = item.id;

            return per;
        }

        //public void RequestAccessToAssets(string businessID, string token, string ad_account_id)
        //{
        //    //curl \
        //    //-F "adaccount_id=act_<AD_ACCOUNT_ID>" \
        //    //-F "access_type=AGENCY" \
        //    //-F "permitted_roles=['GENERAL_USER','REPORTS_ONLY']" // "permitted_roles=['ADVERTISER','INSIGHTS_ANALYST']"
        //    //"https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccounts?access_token=<ACCESS_TOKEN>"
        //    string accessToAssets_url = string.Format("{0}/{1}/{2}/adaccounts?access_token={3}", _graph_endPoint, _fbVersion, businessID, token);

        //    string requestDetails = string.Format("adaccount_id={0}&access_type=AGENCY&permitted_roles=['GENERAL_USER','REPORTS_ONLY']", ad_account_id);

        //    FbApiClient client = new FbApiClient(token);
        //    var response = client.Get(accessToAssets_url, "POST", requestDetails);

        //    if (response.Status != System.Net.HttpStatusCode.OK)
        //    {
        //        throw new Exception("Failed to post request for AccessToAssets from " + response.UrlUsed + "." + response.Content);
        //    }
        //}


        //public void ClaimAdAccount(string businessID, string token, string ad_account_id)
        //{
        //    // curl \
        //    //-F "adaccount_id=act_<AD_ACCOUNT_ID>" \
        //    //-F "access_type=OWNER" \
        //    //-F "access_token=<ACCESS_TOKEN>" \
        //    //"https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccounts"
        //    string accessToAssets_url = string.Format("{0}/{1}/{2}/adaccounts?access_token={3}", _graph_endPoint, _fbVersion, businessID, token);

        //    string requestDetails = string.Format("adaccount_id={0}&access_type=OWNER&access_token={1}", ad_account_id, token);

        //    FbApiClient client = new FbApiClient(token);
        //    var response = client.Get(accessToAssets_url, "POST", requestDetails);

        //    if (response.Status != System.Net.HttpStatusCode.OK)
        //    {
        //        throw new Exception("Failed to post request for ClaimAdAccount from " + response.UrlUsed + "." + response.Content);
        //    }
        //}

        public void AssignSystemUserToHaveRolesOnAdAccount(string appScopedSystemUerId, string businessID, string token, string ad_account_id)
        {

            //curl \
            //-F "user=<APP_SCOPED_SYSTEM_USER_ID>" \
            //-F "role=ADMIN" \   ///REPORTS_ONLY, GENERAL_USER or ADMIN.
            //-F "business=<BUSINESS_ID>" \
            //-F "access_token=<ACCESS_TOKEN>" \
            //"https://graph.facebook.com/<API_VERSION>/act_<AD_ACCOUNT_ID>/userpermissions"

            string accessToAssets_url = string.Format("{0}/{1}/{2}/userpermissions", _graph_endPoint, _fbVersion, ad_account_id);

            string requestDetails = string.Format("user={0}&role=ADMIN&business={1}&access_token={2}", appScopedSystemUerId, businessID, token);

            FbApiClient client = new FbApiClient(token);
            var response = client.Post(accessToAssets_url, requestDetails);

            if (response.Status != System.Net.HttpStatusCode.OK)
            {
                throw new Exception("Failed to post request for AssignSystemUserToHaveRolesOnAdAccount from " + response.UrlUsed + "." + response.Content);
            }

        }


        public BusinessProjectsResponse GetBusinessProjects(string businessID, string token)
        {
            BusinessProjectsResponse response = new BusinessProjectsResponse();
            List<BusinessProject> business_projects = null;
            string get_business_projects_url = string.Format("{0}/{1}/{2}/businessprojects?access_token={3}&fields=id,name&limit=500", _graph_endPoint, _fbVersion, businessID, token);
            response.ApiUrlUsed = get_business_projects_url;

            try
            {
                string business_projects_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(get_business_projects_url);
                dynamic business_projects_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(business_projects_response);

                if (business_projects_parsedJson.data != null)
                {
                    foreach (var item in business_projects_parsedJson.data)
                    {
                        if (business_projects == null)
                            business_projects = new List<BusinessProject>();

                        business_projects.Add(new BusinessProject() { id = item.id, name = item.name });
                    }
                }
                response.BusinessProjects = business_projects;
            }
            catch (Exception ex)
            {
                response.Error = ex.ToString();
            }

            return response;
        }


        public AdAccountsResponse GetOwnedAdAccounts(long userID, string token)
        {
            AdAccountsResponse response = new AdAccountsResponse();
            List<ParsedAdAccount> ad_accounts = new List<ParsedAdAccount>();
            string get_adAccounts_url = string.Format("{0}/{1}/{2}/owned_ad_accounts?access_token={3}&fields=id,account_id,name&limit=500", _graph_endPoint, _fbVersion, userID, token);
            response.ApiUrlUsed = get_adAccounts_url;

            try
            {
                string adAccounts_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(get_adAccounts_url);
                dynamic adAccounts_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(adAccounts_response);

                if (adAccounts_parsedJson.data != null)
                {
                    foreach (var item in adAccounts_parsedJson.data)
                    {
                        ad_accounts.Add(new ParsedAdAccount() { account_id = item.account_id, id = item.id, name = item.name });
                    }
                }
                response.Accounts = ad_accounts;
            }
            catch (Exception ex)
            {
                response.Error = ex.ToString();
            }

            return response;
        }

        public BusinessesManagerResponse GetBusinessManagerAccounts(string userId)
        {
            BusinessesManagerResponse response = new BusinessesManagerResponse();
            List<ParsedAdAccount> ad_accounts = new List<ParsedAdAccount>();
            string get_adAccounts_url = string.Format("{0}/{1}/{2}/businesses?access_token={3}&fields=name,id&limit=500", _graph_endPoint, _fbVersion, userId, _token);
            response.ApiUrlUsed = get_adAccounts_url;

            try
            {
                string adAccounts_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(get_adAccounts_url);
                dynamic adAccounts_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(adAccounts_response);

                if (adAccounts_parsedJson.data != null)
                {
                    foreach (var item in adAccounts_parsedJson.data)
                    {
                        ad_accounts.Add(new ParsedAdAccount() { id = item.id, name = item.name });
                    }
                }
                response.Accounts = ad_accounts;
            }
            catch (Exception ex)
            {
                response.Error = ex.ToString();
            }

            return response;
        }

        public PagesResponse GetPagesList(string userId)
        {
            PagesResponse response = new PagesResponse();
            List<ParsedAdAccount> ad_accounts = new List<ParsedAdAccount>();
            string get_adAccounts_url = string.Format("{0}/{1}/{2}/accounts?access_token={3}&fields=name,id,access_token&limit=500", _graph_endPoint, _fbVersion, userId, _token);
            response.ApiUrlUsed = get_adAccounts_url;

            try
            {
                string adAccounts_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(get_adAccounts_url);
                dynamic adAccounts_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(adAccounts_response);

                if (adAccounts_parsedJson.data != null)
                {
                    foreach (var item in adAccounts_parsedJson.data)
                    {
                        ad_accounts.Add(new ParsedAdAccount() { id = item.id + "@@" + item.access_token, name = item.name, account_id = item.access_token });
                    }
                }
                response.Pages = ad_accounts;
            }
            catch (Exception ex)
            {
                response.Error = ex.ToString();
            }

            return response;
        }
        public SelectResponse GetOwnedPixelsList(string bmId)
        {
            SelectResponse response = new SelectResponse();
            List<ParsedAdAccount> ad_accounts = new List<ParsedAdAccount>();

            string url = string.Format("{0}/{1}/{2}/owned_pixels?access_token={3}&fields=name,id&limit=500", _graph_endPoint, _fbVersion, bmId, _token);
            response.ApiUrlUsed = url;

            try
            {
                string fbResponse = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
                dynamic parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(fbResponse);

                if (parsedJson.data != null)
                {
                    foreach (var item in parsedJson.data)
                    {
                        ad_accounts.Add(new ParsedAdAccount() { id = item.id, name = item.name });
                    }
                }
                response.List = ad_accounts;
            }
            catch (Exception ex)
            {
                response.Error = ex.ToString();
            }

            return response;
        }
        public SelectResponse GetPixelsList(string adAccountId)
        {
            SelectResponse response = new SelectResponse();
            List<ParsedAdAccount> ad_accounts = new List<ParsedAdAccount>();

            string url = string.Format("{0}/{1}/act_{2}/adspixels?access_token={3}&fields=name,id&limit=500", _graph_endPoint, _fbVersion, adAccountId, _token);
            response.ApiUrlUsed = url;

            try
            {
                string fbResponse = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
                dynamic parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(fbResponse);

                if (parsedJson.data != null)
                {
                    foreach (var item in parsedJson.data)
                    {
                        ad_accounts.Add(new ParsedAdAccount() { id = item.id, name = item.name });
                    }
                }
                response.List = ad_accounts;
            }
            catch (Exception ex)
            {
                response.Error = ex.ToString();
            }

            return response;
        }
        public SelectResponse GetInstagramAccountsList(string businessId)
        {
            SelectResponse response = new SelectResponse();
            List<ParsedAdAccount> ad_accounts = new List<ParsedAdAccount>();

            string url = string.Format("{0}/{1}/{2}/instagram_accounts?access_token={3}&fields=username,id&limit=500", _graph_endPoint, _fbVersion, businessId, _token);
            response.ApiUrlUsed = url;

            try
            {
                string fbResponse = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
                dynamic parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(fbResponse);

                if (parsedJson.data != null)
                {
                    foreach (var item in parsedJson.data)
                    {
                        ad_accounts.Add(new ParsedAdAccount() { id = item.id, name = item.username });
                    }
                }
                response.List = ad_accounts;
            }
            catch (Exception ex)
            {
                response.Error = ex.ToString();
            }
            return response;
        }
        public string SetupFacebookAccountAccess(string businessId, string adaccountId, string pageId, string pageAccessToken, string pixelId, string instargamAccountId, string type = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var stToken = db.FbAdsBusinesses.SingleOrDefault(c => c.Name.ToLower().Equals("storeya"));
            string storeYabusinessId = stToken.BusinessID.ToString();
            bool res = false;
            switch (type.ToLower())
            {
                case "connectd":
                    //res = FbAdsSDK.SharePixelAccess(_token, pixelId, storeYabusinessId, adaccountId);
                    return FbAdsSDK.ConnectBusinessManagerAccounts(storeYabusinessId, pageId, pageAccessToken);
                case "pixel":
                    return FbAdsSDK.SharePixelAccess(_token, pixelId, storeYabusinessId, stToken.SystemUserAppScopedID.ToString());
                case "page":
                    return FbAdsSDK.ConnectBusinessManagerPageAccess(_token, storeYabusinessId, pageId);

                case "bm":
                    //return FbAdsSDK.ConnectBusinessManagerAdsAccountsAccess(stToken.AdminSystemUserToken, storeYabusinessId, adaccountId);
                    return FbAdsSDK.ConnectBusinessManagerAccounts(storeYabusinessId, pageId, pageAccessToken);
                case "instagram":
                    //TODO: Ronny IMplement 
                    res = true;
                    break;
                case "adaccount":
                    return FbAdsSDK.ConnectBusinessManagerAdsAccountsAccess(stToken.AdminSystemUserToken, storeYabusinessId, adaccountId);
                case null:
                default:
                    // FbAdsSDK.ConnectBusinessManagerAccounts(storeYabusinessId, pageId, pageAccessToken);
                    FbAdsSDK.SharePixelAccess(_token, pixelId, storeYabusinessId, adaccountId);
                    FbAdsSDK.ConnectBusinessManagerAdsAccountsAccess(stToken.AdminSystemUserToken, storeYabusinessId, adaccountId);
                    FbAdsSDK.ConnectBusinessManagerPageAccess(stToken.AdminSystemUserToken, storeYabusinessId, pageId);
                    break;
            }

            return res.ToString();
        }

        public static string SetupFacebookAccountAccessFBE(string token, string storeYabusinessToken, string storeYabusinessId, string adaccountId, string pageId, string pageAccessToken, string pixelId, out bool error)
        {
            string res = string.Empty;
            error = false;
            //try
            //{
            //    res = "Share Business Manager:" + FbAdsSDK.ConnectBusinessManagerAccounts(storeYabusinessId, pageId, pageAccessToken);
            //}
            //catch (Exception ex)
            //{
            //    res = "Share Business Manager:" + ex.Message;
            //    error = true;
            //}
            try
            {
                res = res + "Share Pixel:" + FbAdsSDK.SharePixelAccess(token, pixelId, storeYabusinessId, adaccountId);
            }
            catch (Exception ex)
            {
                res = res + ", Share Pixel:" + ex.Message;
                error = true;
            }
            try
            {
                res = res + ", Share Ad Account:" + FbAdsSDK.ConnectBusinessManagerAdsAccountsAccess(storeYabusinessToken, storeYabusinessId, adaccountId);
            }
            catch (Exception ex)
            {
                res = res + ", Share Ad Account:" + ex.Message;
                error = true;
            }
            try
            {
                res = res + ", Share Page:" + FbAdsSDK.ConnectBusinessManagerPageAccess(storeYabusinessToken, storeYabusinessId, pageId); ;
            }
            catch (Exception ex)
            {
                res = res + ", Share Page:" + ex.Message;
                error = true;
            }
            return res;
        }

        public string GetFacebookAccountAccess(int shopId, string businessId = null, string adaccountId = null, string pageId = null, string pixelId = null, string instargamAccountId = null, bool sendEmail = true, bool externalAccount = true)
        {
            string resultsStatus = "All Looks Good.";
            string body = string.Format("<h3>FaceBook Share Access Report ShopId:{0}</h3>", shopId);
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                var stToken = db.FbAdsBusinesses.SingleOrDefault(c => c.Name.ToLower().Equals("storeya"));
                string storeYabusinessId = stToken.BusinessID.ToString();
                string adminSystemUserToken = stToken.AdminSystemUserToken;
                string pendingAdAccountId = null;
                string pendingPageId = null;
                string adAccountName = null;
                string adAccountException = string.Empty;
                string pageName = null;
                string pageNameException = string.Empty;
                string agencyName = null;
                string agencyNameException = string.Empty;
                string pageAccessToken = null;
                string pixelException = null;
                try
                {
                    if (adaccountId == "10000")
                    {
                        throw new Exception("Need To Configure FB Account ID!");
                    }
                    if (adaccountId == Consts.FB_EMPTY_ACCOUNTID)
                    {
                        adAccountException = "Need To Create FB Account";
                    }
                    else
                    {
                        adAccountName = FbAdsSDK.GetClientAdAccount(adminSystemUserToken, storeYabusinessId, adaccountId, true);
                        if (adAccountName == null)
                        {
                            if (externalAccount == false)
                            {
                                adAccountName = "StoreYa Internal.";
                            }
                            else
                            {
                                pendingAdAccountId = FbAdsSDK.GetPendingClientAdAccount(adminSystemUserToken, storeYabusinessId, adaccountId, true);
                                resultsStatus = string.Empty;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    adAccountException = ex.Message;
                    resultsStatus = string.Empty;
                }
                try
                {

                    pageName = FbAdsSDK.GetSharedAccountPage(adminSystemUserToken, storeYabusinessId, pageId, true);
                    if (pageName == null)
                    {
                        pendingPageId = FbAdsSDK.GetPendingClientPages(adminSystemUserToken, storeYabusinessId, pageId, true);
                    }
                }
                catch (Exception ex)
                {
                    pageNameException = ex.Message;
                    resultsStatus = string.Empty;
                }
                try
                {
                    pageAccessToken = FbAdsSDK.GetInstance(long.Parse(adaccountId), shopId, true).GetPageAccessToken(pageId.ToString());
                    if (pageAccessToken != null)
                    {
                        agencyName = FbAdsSDK.GetSharedAgency(pageAccessToken, pageId, storeYabusinessId, true);
                    }
                    else
                    {
                        resultsStatus = string.Empty;
                    }
                }
                catch (Exception ex)
                {
                    agencyNameException = ex.Message;
                    resultsStatus = string.Empty;
                }
                try
                {
                    if (string.IsNullOrEmpty(pixelId))
                    {
                        pixelException = "Missing Pixel ID";
                        resultsStatus = string.Empty;
                    }
                    else
                    {
                        FbAdsSDK.GetPixel(adminSystemUserToken, pixelId);

                        if (adaccountId != null)
                        {
                            FbAdsSDK client = FbAdsSDK.GetInstance(Convert.ToInt64(adaccountId), shopId);
                            var connectedAccount = client.PixelAccounts_Read(long.Parse(pixelId));
                            if (connectedAccount == -1)
                            {
                                throw new Exception("Pixel ID is not connected to the Ad Account: " + adaccountId);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    pixelException = ex.Message;
                    resultsStatus = string.Empty;
                }

                body += resultsStatus + "<br/>";
                if (pendingPageId == null && pageName == null)
                {
                    body += string.Format("<b>Page:</b>:<p style='color: red'>{0} - {1}</p><br>", "Failed!", pageNameException);
                }
                else if (pendingPageId == null)
                {
                    body += string.Format("<b>&#10003; - Page Name :</b>:{0}<br>", pageName);
                }
                else
                {
                    body += string.Format("<b>Page permissions request is still in pending request:</b><p style='color: blue'>{0}</p><br>", pendingPageId);
                }


                if (pendingAdAccountId == null && adAccountName == null)
                {
                    body += string.Format("<b>Ad Account:</b><p style='color: red'>{0} - {1}</p><br>", "Failed!", adAccountException);
                }
                else if (pendingAdAccountId == null)
                {
                    body += string.Format("<b>&#10003; - Ad Account:</b>{0}<br>", adAccountName);
                }
                else
                {
                    body += string.Format("<b>Ad Account Is still in pending request:</b><p style='color: blue'>{0}</p><br>", pendingAdAccountId);
                }

                if (pageAccessToken == null)
                {
                    body += string.Format("<b>Shared Business Name:</b><p style='color: red'>{0} - {1}</p><br>", "Failed!", "Missing Page Access Token");
                }
                else
                {
                    if (agencyName == null)
                    {
                        body += string.Format("<b>Shared Business Name:</b><p style='color: red'>{0} - {1}</p><br>", "Failed!", agencyNameException);
                    }
                    else
                    {
                        body += string.Format("<b>&#10003; - Shared Business Name:</b>{0}<br>", agencyName);
                    }
                }
                if (pixelException == null)
                {
                    body += string.Format("<b>&#10003; - Pixel ID :</b>:{0}<br>", pixelId);
                }
                else
                {
                    body += string.Format("<b>Pixel Id:</b>:<p style='color: red'>{0} - {1}</p><br>", "Failed!", pixelException);
                }
                if (sendEmail)
                {
                    EmailHelper.SendEmail("<EMAIL>", "Facebook Share Access Status Report shopID:" + shopId, body);
                }
            }
            catch (Exception ex)
            {
                body += string.Format("<b>Failed To execute Excption:<br>{0}</b>", ex.ToString());
            }
            return body;
        }

        public AdAccountsResponse GetAdAccounts(string userId)
        {
            AdAccountsResponse response = new AdAccountsResponse();
            List<ParsedAdAccount> ad_accounts = null;
            string get_adAccounts_url = string.Format("{0}/{1}/{2}/adaccounts?access_token={3}&fields=name,account_id,id&limit=500", _graph_endPoint, _fbVersion, userId, _token);
            response.ApiUrlUsed = get_adAccounts_url;

            try
            {
                string adAccounts_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(get_adAccounts_url);
                dynamic adAccounts_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(adAccounts_response);

                if (adAccounts_parsedJson.data != null)
                {
                    foreach (var item in adAccounts_parsedJson.data)
                    {
                        if (ad_accounts == null)
                            ad_accounts = new List<ParsedAdAccount>();

                        ad_accounts.Add(new ParsedAdAccount() { account_id = item.account_id, id = item.id, name = item.name });
                    }
                }
                response.Accounts = ad_accounts;
            }
            catch (Exception ex)
            {
                response.Error = ex.ToString();
            }

            return response;
        }

        public bool DeleteFBEAccountAccess(int shopId, out string error)
        {

            string delete_adAccounts_url = string.Format("{0}/{1}/fbe_business/fbe_installs?fbe_external_business_id={2}&access_token={3}", _graph_endPoint, _fbVersion, shopId, _token);
            error = null;
            try
            {
                FbApiClient client = new FbApiClient(_token);
                var response = client.Delete(delete_adAccounts_url, "");

                if (response.Content != null)
                {
                    dynamic parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(response.Content);

                    if (response.Status != System.Net.HttpStatusCode.OK)
                    {
                        error = parsedJson.error.message.ToString();
                        return false;
                    }
                    return bool.Parse(parsedJson.success.ToString());
                }
                return false;
            }
            catch (Exception ex)
            {
                error = ex.ToString();
                return false;
            }
        }

        public string GetFBEClientSystemUserToken(string clientBusinessId, string appId, int shopId, out string systemUserId, out string error)
        {

            //          curl - X POST \
            //-F 'app_id={app_id}' \
            //-F 'scope=ads_management,catalog_management,manage_business_extension' \ 
            //-F 'access_token={user_access_token}' \
            //-F 'fbe_external_business_id={fbe_external_business_id}' \
            //https://graph.facebook.com/<API_VERSION>/<client_business_manager_id>/access_token
            error = null;
            systemUserId = null;
            try
            {
                string accessToAssets_url = string.Format("{0}/{1}/{2}/access_token", _graph_endPoint, _fbVersion, clientBusinessId);
                string requestDetails = string.Format("app_id={0}&scope=business_management,ads_management,catalog_management,manage_business_extension,pages_show_list&fbe_external_business_id={1}&access_token={2}", appId, shopId, _token);

                FbApiClient client = new FbApiClient(_token);
                var response = client.Post(accessToAssets_url, requestDetails);

                if (response.Status != System.Net.HttpStatusCode.OK)
                {
                    throw new Exception("Failed to post request for AssignSystemUserToHaveRolesOnAdAccount from " + response.UrlUsed + "." + response.Content);
                }
                dynamic parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(response.Content);
                string token = parsedJson.access_token.ToString();
                accessToAssets_url = string.Format("{0}/{1}/me?access_token={2}", _graph_endPoint, _fbVersion, token);
                response = client.Get(accessToAssets_url);
                parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(response.Content);
                systemUserId = parsedJson.id.ToString();
                return token;
            }
            catch (Exception ex)
            {
                error = ex.ToString();
                return null;
            }
        }
        public string GetFBEPageToken(string pageId, string fbeToken, out string error)
        {

            string get_adAccounts_url = string.Format("{0}/{1}/me/accounts?access_token={2}&limit=250", _graph_endPoint, _fbVersion, fbeToken);
            error = null;
            try
            {
                string adAccounts_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(get_adAccounts_url);
                dynamic adAccounts_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(adAccounts_response);

                if (adAccounts_parsedJson.data != null)
                {
                    foreach (var item in adAccounts_parsedJson.data)
                    {
                        if (item.id.ToString() == pageId)
                        {
                            return item.access_token.ToString();
                        }
                    }
                }
                if (adAccounts_parsedJson.error != null)
                {
                    error = adAccounts_parsedJson.error.message.ToString();
                }
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            return null;
        }

        public FBEResponse GetFBEAccountDetails(int shopId)
        {
            FBEResponse response = new FBEResponse();

            string get_adAccounts_url = string.Format("{0}/{1}/fbe_business/fbe_installs?fbe_external_business_id={2}&access_token={3}", _graph_endPoint, _fbVersion, shopId, _token);

            try
            {
                string adAccounts_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(get_adAccounts_url);
                dynamic adAccounts_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(adAccounts_response);

                if (adAccounts_parsedJson.data != null)
                {
                    response.BusinessManagerId = adAccounts_parsedJson.data[0].business_manager_id.ToString();
                    response.PixelId = adAccounts_parsedJson.data[0].installed_features[0].connected_assets.pixel_id.ToString();
                    response.AdAccountId = adAccounts_parsedJson.data[0].installed_features[0].connected_assets.ad_account_id.ToString();
                    response.UserAccountId = adAccounts_parsedJson.data[0].profiles[0].ToString();
                    response.PageId = adAccounts_parsedJson.data[0].pages[0].ToString();
                    if (adAccounts_parsedJson.data[0].instagram_profiles != null)
                    {
                        response.InstagramAccountId = adAccounts_parsedJson.data[0].instagram_profiles[0].ToString();
                    }
                }
                if (adAccounts_parsedJson.error != null)
                {
                    response.Error = adAccounts_parsedJson.error.message.ToString();
                }
            }
            catch (Exception ex)
            {
                response.Error = ex.ToString();
            }
            return response;
        }
        //public List<ParsedInsight> GetInsightsOfAdSet(string adAccountID, string token, string groupBy)
        //{
        //    string fields = "ad_id,ad_name,date_start,date_stop,reach,website_purchase_roas,clicks,frequency,ctr,cpc,impressions,spend,total_actions,total_action_value,cost_per_total_action,actions,action_values";
        //    return GetInsights(adAccountID, token, fields, groupBy);
        //}

        //public List<ParsedInsight> GetInsightsOfAccount(string adAccountID, string token, string groupBy)
        //{
        //    string fields = "campaign_id,campaign_name,date_start,date_stop,reach,website_purchase_roas,clicks,frequency,ctr,cpc,impressions,spend,total_actions,total_action_value,cost_per_total_action,actions,action_values";
        //    return GetInsights(adAccountID, token, fields, groupBy);
        //}

        //GetInsightsOfAccount 
        //Main function
        public List<ParsedInsight> GetData(string id, string groupBy, string date_preset = null, string from = null, string to = null)
        {
            //string fields = "reach,website_purchase_roas,clicks,frequency,ctr,cpc,impressions,spend,total_actions,total_action_value,cost_per_action_type,actions,action_values";
            string fields = "reach,website_purchase_roas,clicks,frequency,ctr,cpc,impressions,spend,cost_per_action_type,actions,action_values";
            return GetInsights(id, _token, fields, groupBy, date_preset, from, to);
        }

        private List<ParsedInsight> GetInsights(string id, string token, string fields, string groupBy, string date_preset = null, string from = null, string to = null)
        {
            List<ParsedInsight> insights = new List<ParsedInsight>();
            //string get_insights_url = SetGetInsightsUrl(id, token, fields, groupBy);
            string get_insights_url = GenerateApiCallUrl(id, fields, groupBy, date_preset, from, to);

            FbApiClient client = new FbApiClient(token);
            client.EnablePagingInDataError = true;
            var response = client.Get(get_insights_url);

            //string insights_response = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(get_insights_url);
            if (response.Status != System.Net.HttpStatusCode.OK)
            {
                if (!response.PagingRequired)
                {
                    throw new Exception("Failed to get data from " + response.UrlUsed + "." + response.Content);
                }
            }
            if (response.PagingRequired)
            {
                GetInsightsWithPaging(get_insights_url, token, insights, client.Limit);
            }
            else
            {
                dynamic insights_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(response.Content);
                AddToResults(insights_parsedJson.data, insights);
            }
            return insights;
        }

        public List<ParsedInsight> GetInsightsWithPaging(string url, string token, List<ParsedInsight> insights, int totalLimit, int pageLimit = 25)
        {
            if (insights.Count() >= totalLimit)
            {
                return insights;
            }
            FbApiClient client = new FbApiClient(token, pageLimit);
            url = UrlPathHelper.ReplaceUrlParameter(url, "limit", pageLimit.ToString());
            var response = client.Get(url);
            if (response.Status != System.Net.HttpStatusCode.OK)
            {
                throw new Exception("Failed to get data from " + response.UrlUsed + ". " + response.Content);
            }
            dynamic insights_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(response.Content);
            AddToResults(insights_parsedJson.data, insights);
            var paging = insights_parsedJson.paging;
            if (paging == null)
            {
                return insights;
            }
            {
                if (paging.next == null)
                {
                    return insights;
                }
                url = paging.next;
                GetInsightsWithPaging(url, token, insights, totalLimit);
            }
            return insights;
        }

        public string GetEmailFromLeadAndSaveToDb(long leadId, out string data, out string fullName, out string clientInfo, string content = null)
        {
            FbApiResponse response = new FbApiResponse();
            data = null;
            fullName = string.Empty;
            if (content == null)
            {
                FbApiClient client = new FbApiClient(_token);
                response = client.Get(leadId.ToString());
                if (response.Status != System.Net.HttpStatusCode.OK)
                {
                    throw new Exception("Failed to get lead data from " + response.UrlUsed + ". " + response.Content);
                }
                data = response.Content;
            }
            else
            {
                response.Content = content;
                data = response.Content;
            }
            dynamic lead_parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject(response.Content);
            clientInfo = GetClientInfoFromLead(lead_parsedJson);
            SaveFbLeadToDb(lead_parsedJson);
            string email = null;
            foreach (var item in lead_parsedJson.field_data)
            {
                try
                {
                    if (item.name == "full_name")
                    {
                        fullName = item.values[0].ToString();

                    }
                    if (item.name == "email")
                    {
                        email = item.values[0].ToString();
                    }
                }
                catch (Exception ex)
                {
                    data = $"Exception:{ex.Message} ,Content: {data}";
                }
            }
            return email;
        }

        private void SaveFbLeadToDb(dynamic data)
        {
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                FacebookB2BLeads b2BLead = new FacebookB2BLeads();
                foreach (var item in data.field_data)
                {
                    if (item.name == "full_name")
                    {
                        b2BLead.FullName = item.values[0].ToString();
                    }
                    if (item.name == "company_name")
                    {
                        //
                    }
                    if (item.name == "country")
                    {
                        foreach (var value in item.values)
                        {
                            b2BLead.Country += value.ToString() + ",";
                        }
                    }
                    if (item.name == "email")
                    {
                        b2BLead.Email = item.values[0].ToString();
                    }
                    if (item.name == "phone_number")
                    {
                        b2BLead.PhoneNumber = item.values[0].ToString();
                    }
                    if (item.name == "how_much_revenue_do_you_generate_each_month?")
                    {
                        if (item.values != null && item.values[0] != null)
                        {
                            b2BLead.Revenue = item.values[0].ToString();
                        }
                    }
                    if (item.name == "your_online_store_url")
                    {
                        b2BLead.Url = item.values[0].ToString();
                    }
                }
                b2BLead.InsertedAt = DateTime.Now;
                db.FacebookB2BLeads.Add(b2BLead);
                db.SaveChanges();

                SaveToLeadToSalesTable(b2BLead);

            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Can't save fb lead.", ex.ToString());
            }
        }

        private void SaveToLeadToSalesTable(FacebookB2BLeads b2BLead)
        {
            LeadsForSalesDTO lead = new LeadsForSalesDTO();
            lead.Url = b2BLead.Url;
            lead.Email = b2BLead.Email;
            lead.LastName = b2BLead.FullName;
            lead.Phone = b2BLead.PhoneNumber;
            lead.LeadCountry = b2BLead.Country;
            lead.LeadSource = LeadsForSalesSources.SocialLeads;
            lead.Revenues = b2BLead.Revenue;
            LeadsForSalesManager.Add(lead);
        }

        private string GetClientInfoFromLead(dynamic data)
        {
            try
            {
                string full_name = "";
                string company_name = "";
                string country = "";
                string email = "";
                string phone_number = "";
                string monthly_revenue = "";
                string url = "";
                foreach (var item in data.field_data)
                {
                    if (item.name == "full_name")
                    {
                        full_name = item.values[0].ToString();
                    }
                    if (item.name == "company_name")
                    {
                        company_name = item.values[0].ToString();
                    }
                    if (item.name == "country")
                    {
                        foreach (var value in item.values)
                        {
                            country += value.ToString() + ";";
                        }
                    }
                    if (item.name == "email")
                    {
                        email = item.values[0].ToString();
                    }
                    if (item.name == "phone_number")
                    {
                        phone_number = item.values[0].ToString();
                    }
                    if (item.name == "how_much_revenue_do_you_generate_each_month?")
                    {
                        monthly_revenue = item.values[0].ToString();
                    }
                    if (item.name == "your_online_store_url")
                    {
                        url = item.values[0].ToString();
                    }
                }
                string clientInfo = $"full_name - {full_name}<br/>" +
                    $"company_name - \"{company_name}\"<br/>" +
                    $"country - \"{country}\"<br/>" +
                    $"email - \"{email}\"<br/>" +
                    $"phone_number - \"{phone_number}\"<br/>" +
                    $"monthly revenue - \"{monthly_revenue}\"<br/>" +
                    $"url - \"{url}\"<br/>";
                if (url != null && url != "")
                {
                    url = url.ToLower();
                    if (!url.Contains("http"))
                    {
                        url = "https://" + url;
                    }
                    CatalogSourcePlatforms? catalogSourcePlatforms = ShopManager.FindCatalogPlatform(url);
                    if (catalogSourcePlatforms != null)
                    {
                        string platfom = catalogSourcePlatforms.ToString();
                        clientInfo += $"platform - {platfom}";
                    }
                }
                return clientInfo;

            }
            catch //(Exception ex)
            {
                return null;
            }
        }
        private void AddToResults(dynamic data, List<ParsedInsight> insights)
        {
            if (data != null)
            {
                foreach (var item in data)
                {

                    ParsedInsight insight = SetInsight(item);
                    insights.Add(insight);
                }
            }
        }


        //public List<ParsedInsight> GetInsightsOfCampaign(string campaignID, string token, string groupBy)
        //{
        //    string fields = "adset_id,adset_name,date_start,date_stop,reach,website_purchase_roas,clicks,frequency,ctr,cpc,impressions,spend,total_actions,total_action_value,cost_per_total_action,actions,action_values";
        //    return GetInsights(campaignID, token, fields, groupBy);
        //}


        //private string SetGetInsightsUrl(string id, string token, string fields, string groupBy)
        //{
        //    string get_insights_url = string.Format("{0}/{1}/{2}/insights?access_token={3}&fields={4}", _graph_endPoint, _fbVersion, id, token, fields);

        //    if (!string.IsNullOrEmpty(groupBy))
        //    {
        //        switch (groupBy)
        //        {
        //            case "ad":
        //                get_insights_url = get_insights_url + "&level=ad";
        //                break;

        //            case "adset":
        //                get_insights_url = get_insights_url + "&level=adset";
        //                break;

        //            case "campaign":
        //                get_insights_url = get_insights_url + "&level=campaign";
        //                break;

        //            case "country":
        //                get_insights_url = get_insights_url + "&breakdowns=country";
        //                break;

        //            case "gender":
        //                get_insights_url = get_insights_url + "&breakdowns=gender";
        //                break;

        //            case "age":
        //                get_insights_url = get_insights_url + "&breakdowns=age";
        //                break;

        //            case "device":
        //                get_insights_url = get_insights_url + "&breakdowns=device_platform";
        //                break;

        //            default:
        //                break;
        //        }

        //    }
        //    return get_insights_url;
        //}

        private string GenerateApiCallUrl(string id, string fields, string groupBy, string date_preset = null, string from = null, string to = null)
        {
            string get_insights_url = string.Format("/{0}/insights?fields={1}", id, fields);

            if (!string.IsNullOrEmpty(groupBy))
            {
                switch (groupBy)
                {
                    case "ad":
                        get_insights_url = get_insights_url + ",ad_id,ad_name";
                        get_insights_url = get_insights_url + "&level=ad";
                        break;

                    case "adset":
                        get_insights_url = get_insights_url + ",adset_id,adset_name";
                        get_insights_url = get_insights_url + "&level=adset";
                        break;

                    case "campaign":
                        get_insights_url = get_insights_url + ",campaign_id,campaign_name";
                        get_insights_url = get_insights_url + "&level=campaign";
                        break;

                    case "action_device":
                        get_insights_url = get_insights_url + "&action_breakdowns=action_device,action_type";
                        break;

                    default:
                        get_insights_url = get_insights_url + "&breakdowns=" + groupBy;
                        break;
                }
            }

            if (!string.IsNullOrEmpty(date_preset))
            {
                get_insights_url = get_insights_url + "&date_preset=" + date_preset;
            }
            else if (!string.IsNullOrEmpty(from) && !string.IsNullOrEmpty(to))
            {
                get_insights_url = get_insights_url + "&time_range={\"since\":\"" + from + "\",\"until\":\"" + to + "\"}";
            }


            return get_insights_url;
        }

        private ParsedInsight SetInsight(dynamic item)
        {
            ParsedInsight insight = new ParsedInsight();
            insight.ad_id = item.ad_id;
            insight.date_start = item.date_start;
            insight.date_stop = item.date_stop;

            insight.next_step_id = GetNextStepID(item);
            insight.next_step_name = GetNextStepName(item);

            insight.breakdownLabel = GetBreakdownLabel(item);

            //insight.age = item.age;
            //insight.country = item.country;
            //insight.gender = item.gender;
            //insight.device_platform = item.device_platform;

            insight.AmountSpentUSD = item.spend;
            insight.total_actions = item.total_actions;



            //insight.cost_per_total_action = item.cost_per_total_action;
            insight.total_action_value = item.total_action_value;

            insight.Impressions = item.impressions;
            insight.Frequency = item.frequency;
            insight.Reach = item.reach;
            insight.Clicks = item.clicks;
            insight.Cpc = item.cpc;
            insight.Ctr = item.ctr;

            if (item.action_values != null && item.action_values.Count > 0)
            {
                insight.action_values = item.action_values.ToObject<List<ParsedAction>>();
                string value = GetValueOfRequiredConversionData(insight.action_values);
                if (!string.IsNullOrEmpty(value))
                {
                    double purchaseConversionValue = Convert.ToDouble(value);
                    insight.WebsitePurchasesConversionValue = purchaseConversionValue.ToString();
                }
            }

            if (item.actions != null && item.actions.Count > 0)
            {
                insight.actions = item.actions.ToObject<List<ParsedAction>>();
                string value = GetValueOfRequiredConversionData(insight.actions);
                if (!string.IsNullOrEmpty(value))
                {
                    int purchases = Convert.ToInt16(value);
                    insight.WebsitePurchases = purchases.ToString();
                }
            }

            if (item.website_purchase_roas != null && item.website_purchase_roas.Count > 0)
            {
                insight.website_purchase_roas = item.website_purchase_roas.ToObject<List<ParsedAction>>();
                string value = GetValueOfRequiredConversionData(insight.website_purchase_roas);
                if (!string.IsNullOrEmpty(value))
                {
                    double roa = Convert.ToDouble(value);
                    insight.WebsitePurchaseReturnOnAdSpend = roa.ToString();
                }
            }

            if (item.cost_per_action_type != null && item.cost_per_action_type.Count > 0)
            {
                insight.cost_per_action_type = item.cost_per_action_type.ToObject<List<ParsedAction>>();
                string value = GetValueOfRequiredConversionData(insight.cost_per_action_type);
                if (!string.IsNullOrEmpty(value))
                {
                    double costPerWebsitePurchaseUSD = Convert.ToDouble(value);
                    insight.CostPerWebsitePurchaseUSD = costPerWebsitePurchaseUSD.ToString();
                }
            }


            return insight;
        }

        private string GetNextStepName(dynamic item)
        {
            if (item.ad_name != null)
                return item.ad_name;
            if (item.adset_name != null)
                return item.adset_name;
            if (item.campaign_name != null)
                return item.campaign_name;
            return "N/A";
        }


        private string GetBreakdownLabel(dynamic item)
        {
            var label = "";
            if (item.region != null)
                label += item.region + " ";
            if (item.gender != null)
                label += item.gender + " ";
            if (item.age != null)
                label += item.age + " ";
            if (item.impression_device != null)
                label += item.impression_device + " ";
            if (item.device_platform != null)
                label += item.device_platform + " ";
            if (item.action_device != null)
                label += item.action_device + " ";
            if (item.country != null)
                label += item.country + " ";
            if (item.dma != null)
                label += item.dma + " ";
            if (item.publisher_platform != null)
                label += item.publisher_platform + " ";
            if (item.ad_name != null)
                label += item.ad_name + " ";
            if (label != "")
            {
                return label;
            }
            return "N/A";
        }

        private string GetNextStepID(dynamic item)
        {
            if (item.ad_id != null)
                return item.ad_id;
            if (item.adset_id != null)
                return item.adset_id;
            if (item.campaign_id != null)
                return item.campaign_id;
            return "0";
        }

        private string GetValueOfRequiredConversionData(List<ParsedAction> action_values)
        {
            string value = null;

            var required_action_value = action_values.Where(a => a.action_type == "offsite_conversion.fb_pixel_purchase").SingleOrDefault();

            if (required_action_value != null)
            {
                value = required_action_value.value;
            }
            return value;
        }



        public string GetAdCreatives(string accountID)
        {
            NameValueCollection parameters = new NameValueCollection
            {
                { "fields", "id,name" },
                //{ "status", "PAUSED" },
                { "filtering", "[{field:\"effective_status\",operator:\"IN\",value:[\"ACTIVE\"]}]" } 
                //'effective_status': ['ACTIVE'],
                //filtering=[{"field":"effective_status","operator":"IN","value":["ACTIVE"]}]
            };


            return RunGetRequest($"/act_{accountID}/ads", parameters);

            //string url = string.Format("{0}/{1}/{2}/adcreatives?fields=id,name&access_token={3}", _graph_endPoint, _fbVersion, "act_" + accountID, _token);

            ////url = "https://graph.facebook.com/v20.0/120210466320460787/previews?ad_format=INSTAGRAM_EXPLORE_GRID_HOME&access_token=" + _token;

            //try
            //{
            //    string fbResponse = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
            //    return fbResponse;
            //}
            //catch (Exception ex)
            //{
            //    return ex.ToString();
            //}
        }
        public string GetAdPreview(string creativeID, string adFormat = "INSTAGRAM_EXPLORE_GRID_HOME")
        {
            NameValueCollection parameters = new NameValueCollection
            {
                { "ad_format", adFormat }
            };

            return RunGetRequest($"/{creativeID}/previews", parameters);
        }

        public List<string> GetAdPreviews(string accountID, string adFormat)
        {
            List<string> previews = new List<string>();
            string response = GetAdCreatives(accountID);

            dynamic previewResult = JsonConvert.DeserializeObject<dynamic>(response);
            if (previewResult.data.Count > 0)
            {
                foreach (dynamic item in previewResult.data)
                {
                    string responseHtml = GetAdPreview(item.id.ToString(), adFormat);
                    dynamic iframe = JsonConvert.DeserializeObject<dynamic>(responseHtml);
                    string iframHtml = iframe.data[0].body.ToString();
                    iframHtml = iframHtml.Replace("scrolling=", "scrollingCancelled=");
                    previews.Add(iframHtml);
                }
            }

            return previews;
        }
        private string RunGetRequest(string endPoint, NameValueCollection param)
        {
            string paramList = "?";
            foreach (string key in param)
            {
                paramList += $"{key}={param[key]}&";
            }
            string url = $"{_graph_endPoint}/{_fbVersion}{endPoint}{paramList}access_token={_token}";
            try
            {
                string fbResponse = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(url);
                return fbResponse;
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
        }
    }

    public class BusinessPermissions
    {
        public string access_status { get; set; }
        public string access_requested_time { get; set; }
        public string access_updated_time { get; set; }
        public string id { get; set; }
        public List<string> permitted_roles { get; set; }
    }


    public class Client
    {
        public List<BusinessPermissions> adaccount_permissions { get; set; }
        public List<BusinessPermissions> page_permissions { get; set; }
        public string name { get; set; }
        public string id { get; set; }
    }

    public class BusinessProject
    {
        public string id { get; set; }
        public string name { get; set; }
    }

    public class BusinessProjectsResponse
    {
        public List<BusinessProject> BusinessProjects { get; set; }
        public string ApiUrlUsed { get; set; }
        public string Error { get; set; }
    }

    public class PagesResponse
    {
        public List<ParsedAdAccount> Pages { get; set; }
        public string ApiUrlUsed { get; set; }
        public string Error { get; set; }
    }
    public class SelectResponse
    {
        public List<ParsedAdAccount> List { get; set; }
        public string ApiUrlUsed { get; set; }
        public string Error { get; set; }
    }

    public class BusinessesManagerResponse
    {
        public List<ParsedAdAccount> Accounts { get; set; }
        public string ApiUrlUsed { get; set; }
        public string Error { get; set; }
    }
    public class AdAccountsResponse
    {
        public List<ParsedAdAccount> Accounts { get; set; }
        public string ApiUrlUsed { get; set; }
        public string Error { get; set; }
    }

    public class FBEResponse
    {

        public string InstagramAccountId { get; set; }
        public string BusinessManagerId { get; set; }
        public string PixelId { get; set; }
        public string UserAccountId { get; set; }
        public string AdAccountId { get; set; }
        public string PageId { get; set; }
        public string Error { get; set; }
    }
    public class ParsedAdAccount
    {
        public string id { get; set; }
        public string account_id { get; set; }
        public string name { get; set; }

        public bool IsConnected { get; set; }
    }

    public class ParsedAction
    {
        public string action_type { get; set; }
        public string value { get; set; }
    }

    public class ParsedUser
    {
        public string id { get; set; }
        public string name { get; set; }
    }

    public class FbAdsBreakdown
    {
        private List<ParsedInsight> _rows;

        public FbAdsBreakdown()
        {
            Rows = new List<ParsedInsight>();
        }

        public string Label { get; set; }

        public List<ParsedInsight> Rows
        {
            get
            {
                return _rows;
            }
            set
            {
                if (value != null)
                    _rows = value;
            }
        }
    }

    public class ParsedInsight
    {
        public string next_step_id { get; set; }
        public string next_step_name { get; set; }

        public string date_start { get; set; }
        public string date_stop { get; set; }
        public string Reach { get; set; }
        public string Clicks { get; set; }
        public string Frequency { get; set; }
        public string Ctr { get; set; }
        public string Cpc { get; set; }
        public string Impressions { get; set; }
        public string AmountSpentUSD { get; set; }

        public decimal? AmountSpent
        {
            get
            {
                decimal? amountSpent = null;
                if (!string.IsNullOrEmpty(this.AmountSpentUSD))
                {
                    try
                    {
                        amountSpent = Convert.ToDecimal(this.AmountSpentUSD);
                    }
                    catch
                    {
                    }
                }

                return amountSpent;
            }
        }




        //public string campaign_id { get; set; }
        //public string campaign_name { get; set; }

        //public string adset_id { get; set; }
        //public string adset_name { get; set; }

        public string ad_id { get; set; }
        //public string ad_name { get; set; }

        public string WebsitePurchases { get; set; }
        public string WebsitePurchasesConversionValue { get; set; }
        public string WebsitePurchaseReturnOnAdSpend { get; set; }
        public string CostPerWebsitePurchaseUSD { get; set; }


        public string country { get; set; }
        public string gender { get; set; }
        public string age { get; set; }
        public string device_platform { get; set; }


        public string breakdownLabel { get; set; }

        public string total_actions { get; set; }
        public string total_action_value { get; set; }
        public string cost_per_total_action { get; set; }

        public List<ParsedAction> cost_per_action_type { get; set; }


        public List<ParsedAction> actions { get; set; }
        public List<ParsedAction> website_purchase_roas { get; set; }
        public List<ParsedAction> action_values { get; set; }
    }
}

