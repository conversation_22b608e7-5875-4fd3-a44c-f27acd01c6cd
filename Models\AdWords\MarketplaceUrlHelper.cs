﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    public class UrlResponse
    {
        public string URL { get; set; }
        public bool IsAffiliateUrl { get; set; }
    }

    public class MarketplaceUrlHelper
    {
        public static UrlResponse GetSiteUrl(string url, bool allowAutoRedirect = false, int? shopID = null)
        {
            return GetSiteUrl(url, out bool isWorkingSite, allowAutoRedirect, shopID);
        }
        public static UrlResponse GetSiteUrl(string url, out bool isWorkingSite, bool allowAutoRedirect = false, int? shopID = null)
        {
            //ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            //ServicePointManager.Expect100Continue = true;
            //ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            isWorkingSite = false;
            bool fixedToHttps = false;

            Uri storeUri = GetHomePage(url);
            string storeUrl = url;

            if (storeUri != null)
                storeUrl = storeUri.AbsoluteUri;


            if (!storeUrl.Contains("https://"))
            {
                if (storeUrl.Contains("http://"))
                {
                    storeUrl = storeUrl.Replace("http://", "https://");
                    fixedToHttps = true;
                }

                else
                {
                    storeUrl = "https://" + storeUrl.TrimStart('/').TrimStart('/');
                    fixedToHttps = true;
                }
            }

            UrlResponse response = new UrlResponse();
            response.IsAffiliateUrl = false;
            response.URL = storeUrl;

            StoreYaWebResponse web_response = HttpRequestResponseHelper.GetStoreYaHttpWebResponse(storeUrl, allowAutoRedirect, shopID);
            if (web_response != null)
            {
                isWorkingSite = true;
                SetURLAdIsAffiliate(storeUrl, web_response, ref response);
                if (response.URL.Contains(".etsy.com") && !response.URL.Contains("https://"))
                {
                    StoreYaWebResponse web_response_3 = HttpRequestResponseHelper.GetStoreYaHttpWebResponse(response.URL, allowAutoRedirect, shopID);
                    if (web_response_3 != null)
                    {
                        SetURLAdIsAffiliate(url, web_response_3, ref response);
                    }
                }

            }
            else if (fixedToHttps) //still not all sites have https working
            {
                storeUrl = storeUrl.Replace("https://", "http://");
                response.URL = storeUrl;
                StoreYaWebResponse web_response_2 = HttpRequestResponseHelper.GetStoreYaHttpWebResponse(storeUrl, allowAutoRedirect, shopID);
                if (web_response_2 != null)
                {
                    isWorkingSite = true;
                    SetURLAdIsAffiliate(storeUrl, web_response_2, ref response);
                    if (response.URL.Contains(".myshopify.com"))
                    {
                        StoreYaWebResponse web_response_3 = HttpRequestResponseHelper.GetStoreYaHttpWebResponse(response.URL, allowAutoRedirect, shopID);
                        if (web_response_3 != null)
                        {
                            SetURLAdIsAffiliate(url, web_response_3, ref response);
                        }
                    }
                }
            }
            else
            {
                throw new Exception(string.Format("Not valid url: {0}", storeUrl));
            }

            return response;
        }


        public static string FormatUrl(string url) // remove https://, http://, last /
        {
            url = url.ToLower().TrimEnd('/');
            if (url.Contains("https://"))
            {
                url = url.Replace("https://", "");
            }
            if (url.Contains("http://"))
            {
                url = url.Replace("http://", "");
            }
            if (url.Contains("www."))
            {
                url = url.Replace("www.", "");
            }

            if (url.Contains("/"))
            {
                string[] splitted = url.Split('/');
                if (splitted != null && splitted.Length > 1)
                {
                    //url = url.Replace(string.Format("/{0}", splitted.Length - 1), "");
                    url = splitted[0];
                }
            }

            return url;
        }

        private static void SetURLAdIsAffiliate(string url, StoreYaWebResponse web_response, ref UrlResponse response)
        {
            if (web_response.StatusCode == HttpStatusCode.Moved
                || web_response.StatusCode == HttpStatusCode.MovedPermanently
                || web_response.StatusCode == HttpStatusCode.TemporaryRedirect
                || (web_response.StatusCode == HttpStatusCode.Found
                && !string.IsNullOrEmpty(web_response.StatusDescription)
                && (web_response.StatusDescription.ToLower() == "moved temporarily"
                || web_response.StatusDescription.ToLower() == "found")))
            {
                string willBeRedirectedTo = web_response.Headers["location"];

                if (willBeRedirectedTo == null || !Uri.IsWellFormedUriString(willBeRedirectedTo, UriKind.Absolute))
                {
                    willBeRedirectedTo = web_response.ResponseUri.Scheme + "://" + web_response.ResponseUri.Authority + "/";
                }

                if (willBeRedirectedTo.Contains(":443"))
                {
                    int index = willBeRedirectedTo.IndexOf(":443");
                    willBeRedirectedTo = willBeRedirectedTo.Remove(index);
                }

                if (!url.ToLower().Contains("myshopify.com") && !url.ToLower().Contains(".etsy.com") && !willBeRedirectedTo.ToLower().Contains("myshopify.com") && FormatUrl(url) != FormatUrl(willBeRedirectedTo)) //&& url.ToLower().TrimEnd('/') != willBeRedirectedTo.ToLower().TrimEnd('/') && (url.ToLower().Replace("https://", "").Replace("www.", "") != willBeRedirectedTo.ToLower().Replace("https://", "").Replace("www.", "")
                {
                    response.IsAffiliateUrl = true;
                }

                response.URL = willBeRedirectedTo;
            }
            else if (!url.ToLower().Contains(".etsy.com"))
            {
                response.URL = web_response.ResponseUri.Scheme + "://" + web_response.ResponseUri.Authority + "/";
            }
        }


        public static bool IsMarketplace(string url)
        {
            List<string> marketplaces = new List<string>
            {
                "etsy.com",
                "wix.com",
                "wixsite.com",
                "myshopify.com",

                "facebook.com",
                "instagram.com",
                "www.shop.com/",
                "//shop.com/",
                "ebay.com",
                ".ebay.",
                "twitter.com",
                "linkedin.com",
                "dawanda.com",
                "amazon.com",
                "amazon.",
                "pinterest.com",
                "aliexpress.com",
                "chloeandisabel.com",
                "zazzle.com",
                "cafepress.com",
                "udemy.com",
                "ecwid.com",
                "teespring.com",
                "sunfrog.com",
                "www.gearbubble.com",
                "www.11street.my",
                "://pixels.com",
                "www.artfinder.com",
                "://reverb.com",
                "indiegogo.com",
                "fiverr.com",
                "redbubble.com",
                "linktr.ee",
                "paypal.com"
            };



            foreach (var item in marketplaces)
            {
                if (url.Contains(item))
                    return true;
            }

            return false;
        }

        public static Uri GetHomePage(string url)
        {
            Uri uri = null;
            if (!Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                return null;
            }

            Uri.TryCreate(url, UriKind.Absolute, out uri);
            return uri;
        }


        public static string GetStoreHomePage(string url)
        {
            Uri uri = null;
            if (!Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                return null;
            }

            Uri.TryCreate(url, UriKind.Absolute, out uri);

            if (IsSubdomain(uri))
            {
                return uri.Host;
            }

            string[] segments = uri.Segments;
            if (segments.Count() > 1)
            {
                return uri.Host + "/" + segments[1].TrimEnd('/');
            }
            return uri.Host;
        }


        public static DsaUrls GetDsaUrls(string url)
        {

            string cleanHost = url;
            if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                Uri uri = new Uri(url);
                cleanHost = uri.Host;
            }
            else if (Uri.IsWellFormedUriString("http://" + url, UriKind.Absolute))
            {
                Uri uri = new Uri("http://" + url);
                cleanHost = uri.Host;
            }

            if (url.Contains("myshopify.com"))
            {
                return new DsaUrls()
                {
                    Display = cleanHost,
                    CriteriaUrl = cleanHost,
                    Website = cleanHost//"myshopify.com"
                };
            }
            else if (url.Contains("wix.com"))
            {
                return new DsaUrls()
                {
                    Display = cleanHost,
                    CriteriaUrl = cleanHost,
                    Website = "wix.com"
                };
            }
            else if (IsSubdomain(cleanHost))
            {
                return new DsaUrls()
                {
                    Display = cleanHost,
                    CriteriaUrl = cleanHost + "/DSA_WILL_NOT_WORK",
                    Website = GetTopDomainOfSubdomain(cleanHost)
                };
            }

            return new DsaUrls()
            {
                Display = cleanHost,
                CriteriaUrl = cleanHost + "/DSA_WILL_NOT_WORK",
                Website = cleanHost
            };
        }

        private static bool IsSubdomain(Uri uri)
        {

            if (uri.HostNameType == UriHostNameType.Dns)
            {
                string host = uri.Host;
                return IsSubdomain(host);
                //var nodes = host.Split('.');

                //if (nodes[0] == "www" || nodes.Count() == 2)
                //{
                //    return false;
                //}
                //else
                //{
                //    return true;
                //}
            }

            return false;
        }

        public static bool IsSubdomain(string host)
        {
            var nodes = host.Split('.');

            if (nodes[0] == "www" || nodes.Count() == 2)
            {
                return false;
            }
            else if (nodes.Count() == 3)
            {
                if (nodes[1].Length <= 3 && nodes[2].Length <= 3 && (nodes[1].Length == 2 || nodes[2].Length == 2))
                {
                    //asdfasdf.com.ua
                    //asdfasdfsd.co.uk
                    return false;
                }
                else
                {
                    //test.wix.com
                    //shop.woef.be
                    return true;
                }
            }
            else
            {
                return true;
            }
        }


        public static string GetTopDomainOfSubdomain(string host)
        {
            var nodes = host.Split('.');
            string domain = null;// nodes[1] + "." + nodes[2];
            for (int i = 1; i < nodes.Count(); i++)
            {
                if (domain != null)
                {
                    domain += ".";
                }
                domain += nodes[i];
            }
            return domain;
        }

        public class DsaUrls
        {
            public string Display { get; set; }
            public string Website { get; set; }
            public string CriteriaUrl { get; set; }
        }
    }
}
