﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Models.AdWords
{
    public class PerformanceDataAtDashboard
    {
        public decimal Revenues { get; set; }
        public long Transactions { get; set; }
        public string Currency { get; set; }


        public static PerformanceDataAtDashboard ParseFromSettings(string revenuesText, long? transacations)
        {
            PerformanceDataAtDashboard data = new PerformanceDataAtDashboard();
            if (!string.IsNullOrEmpty(revenuesText))
            {
                var nospaces = revenuesText.Replace(" ", "");
                //var textPart = RegexHelper.GetSingleValue(@"[^0-9,.]\w*", nospaces);
                //var textPart = RegexHelper.GetSingleValue(@"\p{Sc}", nospaces);
                //var numberPart = revenuesText;
                var numberPart = RegexHelper.GetSingleValue(@"[0-9,.]+", nospaces);
                var textPart = "";
                if (numberPart != nospaces)
                {
                    textPart = nospaces.Replace(numberPart, "");
                }
                data.Revenues = decimal.Parse(numberPart, NumberStyles.Currency);
                if (!string.IsNullOrEmpty(textPart))
                {
                    data.Currency = textPart;
                }
            }
            data.Transactions = transacations ?? 0;

            return data;

        }
    }
}
