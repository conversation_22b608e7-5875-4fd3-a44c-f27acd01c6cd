﻿using Storeya.Core.Helpers;
using Storeya.Core.Models.TrafficBoosterModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Models.AdWords
{


    public class ResponsiveDisplayAdSettings
    {

        public List<string> AssetGroupHeadlines { get; set; }
        public List<string> AssetGroupDescriptions { get; set; }
        public List<string> RSAHeadlines { get; set; }
        public List<string> RSADescriptions { get; set; }
        public string shortHeadline { get; set; }
        public string longHeadline { get; set; }
        public string description { get; set; }
        public string advertiserName { get; set; }
        public string finalUrl { get; set; }
        public string buttonText { get; set; }
        public string mobileUrl { get; set; }
        public string pricePrefix { get; set; }
        public string promoText { get; set; }
        public string imageUrl { get; set; }
        public string squareMarketingImage { get; set; }
        public string logo { get; set; }
        public string landscapeLogo { get; set; }

        public string defaultImageUrl { get; set; }
        public string defaultSquareMarketingImage { get; set; }
        public string defaultLogo { get; set; }
        public string defaultLandscapeLogo { get; set; }
        public string BusinessName { get; set; }
        public bool CustomCopies { get; set; }
        public bool ValidForPmax { get; set; }
        public bool ThereAreCustomAdsButUseDefault { get; set; }

    }

    public class ResponsiveDisplayAdHelper
    {

        public static ResponsiveDisplayAdSettings SetResponsiveDisplayAdSettings(TbCategoriesHelper.Category category, string homepageUrl, string advertiserName, int shopID, TbCampaignTypes tbCampaignType = new TbCampaignTypes())
        {
            string IMAGES_PLACEHOLDER_IMAGES_URL = "https://strys3.s3.amazonaws.com/images";
            ResponsiveDisplayAdSettings adSettings = new ResponsiveDisplayAdSettings();

            adSettings.defaultLogo = IMAGES_PLACEHOLDER_IMAGES_URL + "/130x130.png"; //128 x 128 or greater.
            adSettings.defaultImageUrl = IMAGES_PLACEHOLDER_IMAGES_URL + "/1200x628.png";
            adSettings.defaultSquareMarketingImage = IMAGES_PLACEHOLDER_IMAGES_URL + "/400x400.png"; //your square (1:1) image should be greater than 300 x 300. The file size limit is 5MB.
            adSettings.defaultLandscapeLogo = IMAGES_PLACEHOLDER_IMAGES_URL + "/600x315.png"; //your landscape image should have a ratio of 1.91:1 and be greater than 600 x 314. The file size limit is 5MB.

            //string extractedLogo = null;
            //string validLogoImage = null;
            string homePageHtml = null;
            try
            {
                homePageHtml = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(homepageUrl, true, null, false, false);
            }
            catch
            {
                homePageHtml = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest(homepageUrl, true, null, false, false, useSecurityProtocol: false);
            }

            //string homePageHtml = AdwordsManager.GetHomePageContent(homepageUrl);

            //https://support.google.com/google-ads/answer/9267035?visit_id=637002601098815554-4201338674&rd=1
            string shortHeadline = null; //Maximum display width is 25.
            string longHeadline = "We Have Everything You Need"; // Maximum display width is 90.
            string description = null;  // Maximum display width is 90.
            string domain = UrlPathHelper.GetDomainName(homepageUrl);

            if (!string.IsNullOrEmpty(homePageHtml))
            {
                HtmlParser parser = new HtmlParser(homePageHtml);

                parser.Parse();

                shortHeadline = AdwordsManager.FixHeadline(!string.IsNullOrEmpty(parser.Title)
                    ? parser.Title
                    : (!string.IsNullOrEmpty(parser.MetaOgTitle) ? parser.MetaOgTitle : parser.MetaOgSiteName));

                description = AdwordsManager.FixDescription(!string.IsNullOrEmpty(parser.MetaDescription)
                    ? parser.MetaDescription
                    : parser.MetaOgDescription);

            }

            adSettings.shortHeadline = shortHeadline;
            adSettings.longHeadline = longHeadline;
            adSettings.description = description;

            adSettings.advertiserName = advertiserName; //  business name Maximum display width is 25.
            adSettings.finalUrl = homepageUrl;
            adSettings.mobileUrl = homepageUrl;
            adSettings.buttonText = "Buy Now";
            adSettings.pricePrefix = "as low as";
            adSettings.promoText = "Free shipping!";
            adSettings.BusinessName = advertiserName.Substring(0, Math.Min(advertiserName.Length, 25));
            string categoryName = category?.Name;



            var customCopies = AdCopiesManager.GetAll(shopID);
            adSettings.ThereAreCustomAdsButUseDefault = false;
            if (tbCampaignType == TbCampaignTypes.PreformanceMax || tbCampaignType == TbCampaignTypes.SearchPMax)
            {
                adSettings.CustomCopies = FillAdSettingsFieldsByCustomAdCopies(customCopies, ref adSettings, useFifteenHeaders: true);
            }
            else
            {
                adSettings.CustomCopies = FillAdSettingsFieldsByCustomAdCopies(customCopies, ref adSettings);
            }
            if (tbCampaignType == TbCampaignTypes.PreformanceMax && !adSettings.ValidForPmax)
            {
                if (adSettings.CustomCopies)
                {
                    adSettings.ThereAreCustomAdsButUseDefault = true;
                }
                adSettings.CustomCopies = false; //make default headers and descriptions 
            }
            if (!adSettings.CustomCopies)
            {
                FillDefaultCopiesByCategory(category, adSettings);
                string fullHeadline = $"Shop {adSettings.BusinessName} {categoryName}";
                if (fullHeadline.Length > 29)
                {
                    fullHeadline = AdwordsManager.FixHeadline($"{adSettings.BusinessName} {categoryName}", 30);
                }
                string headline2 = AdwordsManager.FixHeadline($"{adSettings.BusinessName} online", 30);
                if (headline2 == fullHeadline)
                {
                    fullHeadline = fullHeadline.TrimEnd(new char[] { '©' });
                }
                adSettings.AssetGroupHeadlines = new List<string>(){
                    AdwordsManager.FixHeadline(adSettings.shortHeadline,30),
                    headline2,
                    fullHeadline};

                adSettings.AssetGroupDescriptions = new List<string>(){
                    AdwordsManager.FixDescription(adSettings.description),
                    AdwordsManager.FixDescription($"{adSettings.advertiserName} online"),
                };
            }



            return adSettings;
        }
        public static bool FillAdSettingsFieldsByCustomAdCopies(List<AdCopy> customCopies, ref ResponsiveDisplayAdSettings adSettings, bool useFifteenHeaders = false)
        {
            if (customCopies.Count < 5)
            {
                return false;
            }
            var heads = AdCopiesManager.FilterHeadlinesForDisplay(customCopies, useFifteenHeaders);
            if (heads.Count >= 3)
            {
                var headers = heads.Select(c => c.Text).ToList();
                adSettings.AssetGroupHeadlines = headers;//AdwordsManager.FixHeadlines(headers, maxLength: 30);
            }
            else
            {
                return false;
            }
            var descs = AdCopiesManager.FilterDescriptionForDisplay(customCopies);
            if (descs.Count >= 2)
            {
                if (descs.Where(x => x.Text.Length <= 60).Any())
                {
                    adSettings.ValidForPmax = true;
                }
                else
                {
                    adSettings.ValidForPmax = false;
                }
                var descriptions = descs.Select(c => c.Text).ToList();
                adSettings.AssetGroupDescriptions = descriptions;//AdwordsManager.FixDescriptions(descriptions);
                adSettings.longHeadline = adSettings.AssetGroupDescriptions.OrderByDescending(x => x.Length).First().Replace('!', '.');
            }
            else
            {
                return false;
            }
            return true;
        }
        private static void FillDefaultCopiesByCategory(TbCategoriesHelper.Category category, ResponsiveDisplayAdSettings adSettings)
        {
            if (category != null && category.ID == 515) //Cosmetics
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "Cosmetics";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "Shop Our Large Collection Of Top & Exclusive Cosmetic Brands. New Arrivals Just Arrived";
                if (string.IsNullOrEmpty(adSettings.description))
                    adSettings.description = "Explore our unrivaled selection of cosmetics. Shop now for the best offers online";
            }
            else if (category != null && (category.ID == 15 || category.ID == 26 || category.ID == 502)) // 26: Electronics, 15: Computer Repair, 502:Electronics & Computers
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "Computers & Electronics";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "We Sell High Quality Products At Amazing Prices. Check Out Our Online Store Now";
                if (string.IsNullOrEmpty(adSettings.description))
                    adSettings.description = "We have incredibly low prices for the best electronics around. Find an amazing deal today";
            }
            else if (category != null && (category.ID == 29 || category.ID == 503)) // 29:Fashion & Accessories
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "Fashion & Accessories ";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "We Have All the Latest Styles & The Best Fashion Brands Around. Stand Out From The Crowd";
                adSettings.description = "The Most Fashionable Clothing & Accessories Brands. Shop Online For Our Latest Collection";
            }
            else if (category != null && (category.ID == 93 || category.ID == 514)) // General  93:514: Other
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "Limited Time Sale";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "New Arrivals Just Added, Check Out Our Collection";
                if (string.IsNullOrEmpty(adSettings.description))
                    adSettings.description = "Competitive Pricing & The Best Online Deals You Can Find. Shop Now For The Best Offers";
            }
            else if (category != null && (category.ID == 42 || category.ID == 138)) // Health & Beauty
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "The Health & Beauty Store";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "High Quality Beauty & Health Products Browse Our Online Store & Find The Perfect Solution";
                if (string.IsNullOrEmpty(adSettings.description))
                    adSettings.description = "Become a healthier beautiful you today. Find just what you need with us now";
            }
            else if (category != null && (category.ID == 44 || category.ID == 140)) // Home & Garden
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "The Home & Garden Store";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "For Home & Garden Products Shop Now For The Best Selection & Great Discounts";
                adSettings.description = "Create A Beautiful Space At An Affordable Price. Shop Now For The Best Online Deals";
            }
            else if (category != null && category.ID == 508) // Jewelry
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "Jewelry Shop";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "We Sell High Quality Jewelry At Amazing Prices. Check Out Our Online Store Now";
                if (string.IsNullOrEmpty(adSettings.description))
                    adSettings.description = "The best fine jewelry selection availble. Shop Online For Our Latest Collection";
            }
            else if (category != null && (category.ID == 166 || category.ID == 70 || category.ID == 510)) // Pet Shop
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "The Wonderful Pet Shop";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "The Best Online Pet Store. View Our Online Shop Now For Barking Mad Prices Now";
                if (string.IsNullOrEmpty(adSettings.description))
                    adSettings.description = "We have incredibly low prices for the best pet goods around. Find an amazing deal today";
            }
            else if (category != null && (category.ID == 181 || category.ID == 85 || category.ID == 512)) // Sports & Outdoors
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "Sports & Outdoors Store";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "We Have Everything You Need For An Active Lifestyle. Browse For High Quality Brands ";
                if (string.IsNullOrEmpty(adSettings.description))
                    adSettings.description = "Competitive Pricing & The Best Online Deals You Can Find. Shop Now For The Best Offers";
            }
            else if (category != null && category.ID == 513) // Toys, Kids & Babies
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "Toys & Baby Products Shop";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "We Have Everything You Need For Your Young Ones. Browse Our Store To See What's In Stock";
                if (string.IsNullOrEmpty(adSettings.description))
                    adSettings.description = "We have some of the lowest prices you will find online for high quality products";
            }
            else
            {
                if (string.IsNullOrEmpty(adSettings.shortHeadline))
                    adSettings.shortHeadline = "shortHeadline";
                if (string.IsNullOrEmpty(adSettings.longHeadline))
                    adSettings.longHeadline = "longHeadline";
                if (string.IsNullOrEmpty(adSettings.description))
                    adSettings.description = "description";
            }
        }

        private static string GetValidImage(string imageUrl, int height, int width, int shopID, int maxAllowedSize = 100000, int minSize = 300)
        {
            string validImage = null;
            try
            {
                validImage = AdwordsManager.GetValidImage(imageUrl, height, width, shopID, maxAllowedSize, minSize);
            }
            catch (Exception ex)
            {
                Console.Write(ex.ToString());
            }

            return validImage;
        }
    }
}
