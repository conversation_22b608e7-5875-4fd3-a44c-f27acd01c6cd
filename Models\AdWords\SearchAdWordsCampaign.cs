﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{
    public class SearchAdWordsCampaign : BaseAdWordsCampaign
    {
        public enum SearchCampaignType
        {
            None,
            Search,
            Search_B,
            RLSA_B,
            Search_B_Broad
        }
        public readonly SearchCampaignType CampaignType;
        public SearchAdWordsCampaign(SearchCampaignType searchCampaignType)
        {
            this.AdGroups = new List<SearchAdGroup>();
            CampaignType = searchCampaignType;
        }
        //public string BusinessName { get; set; }
        //public string Phone { get; set; }
        //public string PhoneCountryCode { get; set; }

        //public string Category { get; set; }

        //public string CallZipCode { get; set; }
        //public int? Radius { get; set; }
        //public int? RadiusUnits { get; set; }
        public string FinalUrl { get; set; }
        public List<SearchAdGroup> AdGroups { get; set; }

        public RSAAdsData RSAAdCategoryData { get; set; }

        public static RSAAdsData GetRSAdsByCategory(int? categoryId, string domain, string countryCodes = null)
        {
            string language = AdwordsManager.SetLanguage(countryCodes);
            string json = null;
            switch (language)
            {
                case "nl":
                    json = Storeya.Core.Properties.Resources.NLRSAAdsData_json;
                    break;
                case "fr":
                    json = Storeya.Core.Properties.Resources.FRRSAAdsData_json;
                    break;
                case "de":
                    json = Storeya.Core.Properties.Resources.DERSAAdsData_json;
                    break;
                case "es":
                    json = Storeya.Core.Properties.Resources.ESRSAAdsData_json;
                    break;
                case "it":
                    json = Storeya.Core.Properties.Resources.ITRSAAdsData_json;
                    break;
                default:
                    json = Storeya.Core.Properties.Resources.RSAAdsData_json;
                    break;
            }
            domain = AdCopyExtractorViewModel.GetCleanHost(domain);
            string rsaJson = json.Replace("{domain}", domain.Replace(".myshopify", "").SubString2(0, 29));
            RSAAdsJson rSAAdsJson = rsaJson.FromJson<RSAAdsJson>();
            categoryId = categoryId.HasValue ? categoryId : 514;
            RSAAdsData RSAAdCategoryData = rSAAdsJson.RSAAds.SingleOrDefault(a => a.TrafficCategoryID == categoryId);
            if (RSAAdCategoryData == null)
            {
                RSAAdCategoryData = rSAAdsJson.RSAAds.SingleOrDefault(a => a.TrafficCategoryID == 514);
            }

            return RSAAdCategoryData;
        }
    }

    public class RSAAdsJson
    {
        public List<RSAAdsData> RSAAds { get; set; }
    }
    public class RSAAdsData
    {
        public int TrafficCategoryID { get; set; }
        public string TrafficCategoryName { get; set; }
        // public string Headline1 { get; set; }
        private string headline1;
        private string headline2;
        private string headline3;
        private string headline4;
        private string headline5;
        private string headline6;
        private string headline7;
        private string headline8;
        private string headline9;
        private string headline10;
        private string headline11;
        private string headline12;
        private string headline13;
        private string headline14;
        private string headline15;

        private string description1;
        private string description2;
        private string description3;
        private string description4;
        public bool DefaultAds { get; set; }
        public string Headline1
        {
            get { return headline1; }
            set { headline1 = value; }
        }
        public string Headline2
        {
            get { return headline2; }
            set { headline2 = value; }
        }
        public string Headline3
        {
            get { return headline3; }
            set { headline3 = value; }
        }
        public string Headline4
        {
            get { return headline4; }
            set { headline4 = value; }
        }
        public string Headline5
        {
            get { return headline5; }
            set { headline5 = value; }
        }
        public string Headline6
        {
            get { return headline6; }
            set { headline6 = value; }
        }
        public string Headline7
        {
            get { return headline7; }
            set { headline7 = value; }
        }
        public string Headline8
        {
            get { return headline8; }
            set { headline8 = value; }
        }
        public string Headline9
        {
            get { return headline9; }
            set { headline9 = value; }
        }
        public string Headline10
        {
            get { return headline10; }
            set { headline10 = value; }
        }
        public string Headline11
        {
            get { return headline11; }
            set { headline11 = value; }
        }
        public string Headline12
        {
            get { return headline12; }
            set { headline12 = value; }
        }
        public string Headline13
        {
            get { return headline13; }
            set { headline13 = value; }
        }
        public string Headline14
        {
            get { return headline14; }
            set { headline14 = value; }
        }
        public string Headline15
        {
            get { return headline15; }
            set { headline15 = value; }
        }
        public string Description1
        {
            get { return AdwordsManager.FixDescription(description1); }
            set { description1 = value; }
        }
        public string Description2
        {
            get { return AdwordsManager.FixDescription(description2); }
            set { description2 = value; }
        }
        public string Description3
        {
            get { return AdwordsManager.FixDescription(description3); }
            set { description3 = value; }
        }
        public string Description4
        {
            get { return AdwordsManager.FixDescription(description4); }
            set { description4 = value; }
        }

    }

    public class SearchAdGroup
    {
        public string AdGroupName { get; set; }
        public string Headline1 { get; set; }
        public string Headline2 { get; set; }
        public string Description { get; set; }
        public List<string> Keywords { get; set; }
        public string HomepageUrl { get; set; }

        public List<SearchKeywordByPattern> KeywordsByPatterns { get; set; }
        public long BigMicroAmount { get; set; }

        public SearchAdGroup()
        {
            this.Keywords = new List<string>();
        }
    }

    public class SearchKeywordByPattern
    {
        public string Keyword { get; set; }
        public string Url { get; set; }
    }
}