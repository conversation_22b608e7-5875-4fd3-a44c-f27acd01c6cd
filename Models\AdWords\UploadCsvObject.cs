﻿using CsvHelper.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AdWords
{

    //public class TBUploadCsvObject
    //{
    //    public List<UploadCsvObject> CampaignRows { get; set; }
    //}

    //public class UploadCsvObject
    //{
    //    [CsvField(Name = "Campaign")]
    //    public string Campaign { get; set; }
    //    [CsvField(Name = "DSA Website")]
    //    public string DSAWebsite { get; set; }
    //    [CsvField(Name = "Display URL")]
    //    public string DisplayURL { get; set; }
    //    [CsvField(Name = "Dynamic Ad Target Value 1")]
    //    public string DynamicAdTargetValue1 { get; set; }
    //    [CsvField(Name = "Description Line 1")]
    //    public string DescriptionLine1 { get; set; }
    //    [CsvField(Name = "Description Line 2")]
    //    public string DescriptionLine2 { get; set; }
    //    [CsvField(Name = "Max CPC")]
    //    public string MaxCPC { get; set; }
    //    [CsvField(Name = "Campaign Daily Budget")]
    //    public string CampaignDailyBudget { get; set; }
    //    [CsvField(Name = "DSA Language")]
    //    public string DSALanguage { get; set; }
    //    [CsvField(Name = "Languages")]
    //    public string Languages { get; set; }
    //    [CsvField(Name = "Location")]
    //    public string Location { get; set; }
    //    [CsvField(Name = "Campaign Type")]
    //    public string CampaignType { get; set; }
    //    [CsvField(Name = "Networks")]
    //    public string Networks { get; set; }
    //    [CsvField(Name = "Bid Strategy Type")]
    //    public string BidStrategyType { get; set; }
    //    [CsvField(Name = "Enhanced CPC")]
    //    public string EnhancedCPC { get; set; }
    //    [CsvField(Name = "Viewable CPM")]
    //    public string ViewableCPM { get; set; }
    //    [CsvField(Name = "Bid Modifier")]
    //    public string BidModifier { get; set; }
    //    [CsvField(Name = "Ad rotation")]
    //    public string AdRotation { get; set; }
    //    [CsvField(Name = "Delivery method")]
    //    public string DeliveryMethod { get; set; }
    //    [CsvField(Name = "Targeting method")]
    //    public string TargetingMethod { get; set; }
    //    [CsvField(Name = "Exclusion method")]
    //    public string ExclusionMethod { get; set; }
    //    [CsvField(Name = "Campaign Priority")]
    //    public string CampaignPriority { get; set; }
    //    [CsvField(Name = "Local Inventory Ads")]
    //    public string LocalInventoryAds { get; set; }
    //    [CsvField(Name = "Tracking template")]
    //    public string TrackingTemplate { get; set; }
    //    [CsvField(Name = "Ad Group")]
    //    public string AdGroup { get; set; }
    //    [CsvField(Name = "Display Network Max CPC")]
    //    public string DisplayNetworkMaxCPC { get; set; }
    //    [CsvField(Name = "Max CPM")]
    //    public string MaxCPM { get; set; }
    //    [CsvField(Name = "CPA Bid")]
    //    public string CPABid { get; set; }
    //    [CsvField(Name = "Display Network Custom Bid Type")]
    //    public string DisplayNetworkCustomBidType { get; set; }
    //    [CsvField(Name = "Targeting optimization")]
    //    public string TargetingOptimization { get; set; }
    //    [CsvField(Name = "Ad Group Type")]
    //    public string AdGroupType { get; set; }
    //    [CsvField(Name = "Flexible Reach")]
    //    public string FlexibleReach { get; set; }
    //    [CsvField(Name = "Device Preference")]
    //    public string DevicePreference { get; set; }
    //    [CsvField(Name = "Dynamic Ad Target Condition 1")]
    //    public string DynamicAdTargetCondition1 { get; set; }
    //    [CsvField(Name = "Dynamic Ad Target Condition 2")]
    //    public string DynamicAdTargetCondition2 { get; set; }
    //    [CsvField(Name = "Dynamic Ad Target Condition 3")]
    //    public string DynamicAdTargetCondition3 { get; set; }
    //    [CsvField(Name = "Campaign Status")]
    //    public string CampaignStatus { get; set; }
    //    [CsvField(Name = "Ad Group Status")]
    //    public string AdGroupStatus { get; set; }
    //    [CsvField(Name = "Status")]
    //    public string Status { get; set; }

    //}
}
