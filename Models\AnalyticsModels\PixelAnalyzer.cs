﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AnalyticsModels
{
    public class PixelAnalyzer
    {
        public static PixelStats Parse(string file)
        {
            PixelStats stats = new PixelStats();
            string[] lines = File.ReadAllLines(file);
            int pixelColumnIndex = -1;
            foreach (var line in lines)
            {
                if (pixelColumnIndex ==-1 )
                {
                    //LogFilename,LogRow,date,time,c-ip,cs-username,s-sitename,s-computername,s-ip,s-port,cs-method,cs-uri-stem,cs-uri-query,sc-status,sc-substatus,sc-win32-status,sc-bytes,cs-bytes,time-taken,cs-version,cs-host,cs(User-Agent),cs(Cookie),cs(Referer),s-event,s-process-type,s-user-time,s-kernel-time,s-page-faults,s-total-procs,s-active-procs,s-stopped-procs
                    string[] columns = line.Split(',');
                    int i = 0;
                    foreach (var col in columns)
                    {

                        if (col == "cs-uri-query")
                        {
                            pixelColumnIndex = i;
                            continue;
                        }
                        i++;
                    }
                    continue;
                }
                //LogFilename,LogRow,date,time,c-ip,cs-username,s-sitename,s-computername,s-ip,s-port,cs-method,cs-uri-stem,cs-uri-query,sc-status,sc-substatus,sc-win32-status,sc-bytes,cs-bytes,time-taken,cs-version,cs-host,cs(User-Agent),cs(Cookie),cs(Referer),s-event,s-process-type,s-user-time,s-kernel-time,s-page-faults,s-total-procs,s-active-procs,s-stopped-procs
                LogRow row = ParseRow(line, pixelColumnIndex);

                if (row.ShopID == null)
                {
                    if (!stats.Stats.ContainsKey("Errors"))
                    {
                        stats.Stats.Add("Errors", new SingleShopStats());
                    }
                    stats.Stats["Errors"].PageViews++;
                    continue;
                }

                if (row.ShopKey.Contains("cs-uri-query"))
                {
                    continue;
                }

                if (!stats.Stats.ContainsKey(row.ShopKey))
                {
                    stats.Stats.Add(row.ShopKey, new SingleShopStats());
                    if (row.ShopKey == "5126CD08")
                    {
                        Debug.WriteLine("");
                    }
                }

                if (stats.Stats.ContainsKey(row.ShopKey))
                {
                    stats.Stats[row.ShopKey].ShopID = row.ShopID;
                    stats.Stats[row.ShopKey].PageViews++;
                    stats.Stats[row.ShopKey].CartImpressions += (row.IsCart) ? 1 : 0;
                    stats.Stats[row.ShopKey].Sales += (row.IsSale) ? 1 : 0;
                    if (row.IsOur)
                    {
                        stats.Stats[row.ShopKey].OurPageViews++;
                        stats.Stats[row.ShopKey].OurCartImpressions += (row.IsCart) ? 1 : 0;
                        stats.Stats[row.ShopKey].OurSales += (row.IsSale) ? 1 : 0;
                    }
                }
            }
            return stats;
        }

        public static void ParseToTabDelimited(string source, int columnIndex = 1)
        {
            string folder = @"d:\Dev\StoreYa\Performace\data\";
            string f = @"mybogega.csv";
            source = folder + f;

            StringBuilder s = new StringBuilder();

            string[] lines = File.ReadAllLines(source);

            bool isClean = !lines[0].Contains(",");
            foreach (var line in lines)
            {
                LogRow row;
                if (isClean)
                {
                    row = ParsePxlQuery(line);
                    
                }
                else
                {
                    //get only queryString
                    row = ParseRow(line, 0);
                }

                s.AppendLine(row.ShopID + "\t" + row.ShopKey + "\t" + row.Url + "\t" + row.Referer + "\t" + row.TrafficSource);
            }

            File.AppendAllText(folder + "tabdelimited.csv", s.ToString());

        }


        public static LogRow ParseRow(string line, int queryIndex)
        {
            LogRow row = new LogRow();
            string queryString = "";
            if (line.Split(',').Length > 0)
            {
                try
                {
                    string[] fields = line.Split(',');
                    queryString = fields[queryIndex];
                    row = ParsePxlQuery(queryString);
                }
                catch (Exception)
                {
                    row.IsError = true;
                }
            }
            return row;
        }

        private static string GetParam(string query, string nextParam, out string source)
        {
            string[] pars = query.Split(new string[] { "&" + nextParam + "=" }, StringSplitOptions.RemoveEmptyEntries);
            if (pars.Length > 1)
            {
                source = pars[1];
                return pars[0];
            }
            source = pars[0];
            return null;
        }

        public static LogRow ParsePxlQuery(string pxl)
        {
            LogRow row = new LogRow();
            try
            {
                //id=6128E728&u=http://www.mybotega.com/?utm_source=stry&utm_medium=trafb&utm_campaign=cmp3&gclid=CIS2mMiw8sgCFYyRGwodl4AOMQ&r=&auid=&ts=stry&t=0.1500194733161233

                string line = pxl;

                string rest;
                string id = line.Split(new string[] { "&u=" }, StringSplitOptions.RemoveEmptyEntries)[0];
                id = id.Replace("\"id=", "").Replace("id=", "");
                rest = line.Split(new string[] { "&u=" }, StringSplitOptions.RemoveEmptyEntries)[1];

                string url = GetParam(rest, "r", out rest);
                if (url == null)
                {
                    url = rest;
                }

                string reff = GetParam(rest, "auid", out rest);
                string auid = GetParam(rest, "ts", out rest);
                string ts = GetParam(rest, "t", out rest);

                row.Url = url;
                row.TrafficSource = ts;
                row.Referer = reff;
                row.ShopKey = id;
                row.ShopID = SequenceHelper.Decode(id).ToString();

                row.IsCart = url.Contains("/cart") || url.Contains("/commande") || url.Contains("/order");
                row.IsOur = (ts == "stry");
                row.IsSale = url.Contains("thank") || url.Contains("/confirmation-commande")  || url.Contains("/order-confirmation");
            }
            catch (Exception)
            {
                row.IsError = true;
            }

            return row;

        }

    }

    public class LogRow
    {
        public bool IsOur { get; set; }
        public bool IsCart { get; set; }
        public bool IsSale { get; set; }
        public string ShopKey { get; set; }
        public string ShopID { get; set; }
        public bool IsError { get; set; }
        public string Url { get; set; }
        public string Referer { get; set; }
        public string TrafficSource { get; set; }
    }

    public class PixelStats
    {
        public PixelStats()
        {
            this.Stats = new Dictionary<string, SingleShopStats>();
        }
        public Dictionary<string, SingleShopStats> Stats { get; set; }

        public void Print()
        {
            string header = "ShopKey\tShopID\tImpressions\tCart\tSales\tOurPageViews\tOurCart\tOurSales";
            Debug.WriteLine(header);
            foreach (var item in this.Stats)
            {
                SingleShopStats st = item.Value;

                //string result = st.PageViews + "\t" + st.CartImpressions + "\t" + st.Sales + "\t" ;
                string result = string.Format("{0}\t{1}\t{2}\t{3}\t{4}\t{5}\t{6}", st.ShopID, st.PageViews, st.CartImpressions, st.Sales, st.OurPageViews, st.OurCartImpressions, st.OurSales);

                Debug.WriteLine(item.Key + "\t" + result);
            }
        }

        public string GetStatsAsString()
        {
            string header = "ShopKey\tShopID\tImpressions\tCart\tSales\tOurPageViews\tOurCart\tOurSales";
            StringBuilder s = new StringBuilder();
            s.AppendLine(header);
            foreach (var item in this.Stats)
            {
                SingleShopStats st = item.Value;

                //string result = st.PageViews + "\t" + st.CartImpressions + "\t" + st.Sales + "\t" ;
                string result = string.Format("{0}\t{1}\t{2}\t{3}\t{4}\t{5}\t{6}", st.ShopID, st.PageViews, st.CartImpressions, st.Sales, st.OurPageViews, st.OurCartImpressions, st.OurSales);

                s.AppendLine(item.Key + "\t" + result);
            }

            return s.ToString();
        }

    }
    public class SingleShopStats
    {
        public string ShopID { get; set; }
        public int PageViews { get; set; }
        public int OurPageViews { get; set; }
        public int CartImpressions { get; set; }
        public int OurCartImpressions { get; set; }

        public int Sales { get; set; }
        public int OurSales { get; set; }
    }
}
