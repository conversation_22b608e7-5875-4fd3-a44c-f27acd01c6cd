﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AppStore
{
    public class AllApps
    {
        private Dictionary<int, AppEntity> _appsByID { get; set; }
        private Dictionary<string, AppEntity> _appsByUrl { get; set; }
        private List<AppEntity> _apps { get; set; }

        public AllApps()
        {
            if (_apps == null)
            {
                AppEntity a = new AppEntity();
                _apps.Add(new AppEntity() { AppID = 1, AppKey = "fbshop", AppName = "Facebook Shop", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = (int)AppTypes.CouponPop, AppKey = "couponpop", AppName = "Coupon Pop", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = (int)AppTypes.ExitPop, AppKey = "exitpop", AppName = "Exit Pop", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = (int)AppTypes.RFF, AppKey = "refer-a-friend", AppName = "RFF", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = (int)AppTypes.PoweredBanner, AppKey = "powerbanner", AppName = "Powered Banner", EditLink = "", IsExternal = false });

                
                _apps.Add(new AppEntity() { AppID = 10, AppKey = "scratchnwin", AppName = "Facebook Shop", EditLink = "", IsExternal = false });
                //_apps.Add(new AppEntity() { AppID = 11, AppKey = "instagallery", AppName = "Facebook Shop", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = 12, AppKey = "group-deal", AppName = "Facebook Shop", EditLink = "", IsExternal = false });

                _apps.Add(new AppEntity() { AppID = 100, AppKey = "likebox", AppName = "Facebook Shop", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = 101, AppKey = "fangate", AppName = "Facebook Shop", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = 102, AppKey = "twitter-tab", AppName = "Facebook Shop", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = 103, AppKey = "instagram-tab", AppName = "Facebook Shop", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = 104, AppKey = "youtube-tab", AppName = "Facebook Shop", EditLink = "", IsExternal = false });
                _apps.Add(new AppEntity() { AppID = 105, AppKey = "pinterest-tab", AppName = "Facebook Shop", EditLink = "", IsExternal = false });

                _appsByID = null;
                _appsByUrl = null;
                foreach (var app in _apps)
                {
                    _appsByID.Add(app.AppID, app);
                    _appsByUrl.Add(app.AppKey, app);
                }

            }
        }
    }
}
