﻿using Storeya.Core.Helpers;
using Storeya.Core.Models.Payments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Storeya.Core.Models.AppStore
{
    public class AppPaymentManager
    {
        public static void CancelAppPayment(int shopID, int appTypeID)
        {
            try
            {
                AppEntity appEntity = AppStoreManager.GetAppByID(appTypeID);

                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                ShopApp shopApp = db.ShopApps.Where(a => a.ShopID == shopID && a.AppTypeID == appTypeID).SingleOrDefault();
                if (shopApp != null
                    && shopApp.PaymentPlan.HasValue && shopApp.PaymentPlan > 0
                    && shopApp.SubscriptionID.Value > 0
                                            && shopApp.SubscriptionID.Value != (int)SpecificSubscriptionID.Shopify
                                            && shopApp.SubscriptionID.Value != (int)SpecificSubscriptionID.Tictail)
                {
                    List<ShopApp> paidApps = AppStoreManager.GetPaidApps(shopID);

                    if (paidApps.Where(a => a.PaymentPlan < 0).Any())
                    {
                        string message = string.Format("The subscription for {0} can not be cancelled automatically because one of client's shop apps has PaymentPlan < 0.", appEntity.AppName);
                        Log4NetLogger.Info(message, shopID);

                        SendEmail("Shop subscription should be changed manually.", message + Environment.NewLine + string.Format("Shop: {0}", shopID));
                        return;
                    }

                    ShopSubscription shopSubscription = db.ShopSubscriptions.Where(s => s.ID == shopApp.SubscriptionID).SingleOrDefault();

                    List<ShopApp> appsUseSameSubscription = (from sa in paidApps.Where(s => s.ID != shopApp.ID
                                               && s.SubscriptionID == shopApp.SubscriptionID)
                                                             select sa).ToList();
                    if (appsUseSameSubscription == null)
                    {
                        if (shopSubscription != null
                            && shopSubscription.PaymentAdapterType == (int)PaymentAdapterTypes.BlueSnap
                            && (string.IsNullOrEmpty(shopSubscription.PaymentMethod) || shopSubscription.PaymentMethod.ToLower() != "paypal"))
                        {
                            AbstractPaymentAdapter paymentAdapter = PaymentAdapterFactory.GetInstance(PaymentAdapterTypes.BlueSnap);
                            bool isCancelled = paymentAdapter.CancelSubscription(shopSubscription.OriginalSubscriptionID, shopID);

                            if (!isCancelled)
                            {
                                throw new Exception();
                            }
                            else
                            {
                                Log4NetLogger.Info(string.Format("BlueSnap subscription {0} was saccessfully cancelled.", shopSubscription.OriginalSubscriptionID), shopID);
                            }
                        }
                        else
                        {
                            string message = string.Format("The subscription for {0} can not be cancelled automatically because this shop app has not changeable subscription. {1} PaymentAdapterType: {2}{1}, PaymetMethod: {3}", appEntity.AppName,
                             Environment.NewLine,
                            (shopSubscription.PaymentAdapterType.HasValue ? ((PaymentAdapterTypes)shopSubscription.PaymentAdapterType).ToString() : null),
                            shopSubscription.PaymentMethod);

                            Log4NetLogger.Info(message, shopID);

                            SendEmail("Shop subscription should be cancelled manually.", message + Environment.NewLine + string.Format("Shop: {0}", shopID));
                        }
                    }
                    else
                    {
                        if (shopSubscription != null
                            && shopSubscription.PaymentAdapterType == (int)PaymentAdapterTypes.BlueSnap
                            && (string.IsNullOrEmpty(shopSubscription.PaymentMethod) || shopSubscription.PaymentMethod.ToLower() != "paypal"))
                        {
                            SubscriptionChangeManager m = new SubscriptionChangeManager(shopID);
                            RequiredActions change = m.GetRemoveChange(appTypeID);
                            if (change.RequiredSubscriptionToCancel == null)
                            {
                                AbstractPaymentAdapter paymentAdapter = PaymentAdapterFactory.GetInstance(PaymentAdapterTypes.BlueSnap);

                                SubscriptionChangeResult response = SubscriptionManager.UpdateBlueSnap2(change);
                                if (response.IsUpdated)
                                {
                                    int subscriptionID = response.SubscriptionData.SubscriptionID;
                                    //update subscription
                                    if (subscriptionID == 0)
                                    {
                                        //new subscription added
                                        subscriptionID = SubscriptionManager.SaveToDB(change.AppID, change.ShopID, change.PlanID,
                                            response.SubscriptionData.BsSubscriptionID.ToString(),
                                            change.ContractID.ToString(),
                                            response.SubscriptionData.BsShopperID,
                                            response.SubscriptionData.BsCurrency,
                                            response.TotalSubscriptionCharge.ToString());

                                        Log4NetLogger.Info(string.Format("UpdateAppSubscription: new subscription created. BsSubscriptionID - {0}.", response.SubscriptionData.BsSubscriptionID.ToString()), change.ShopID);
                                    }
                                    else
                                    {
                                        //existing subscription
                                        SubscriptionManager.Update(response.SubscriptionData.SubscriptionID, response.TotalSubscriptionCharge);

                                        Log4NetLogger.Info(string.Format("UpdateAppSubscription: subscription was updated. StoreYa subscription ID - {0}.", response.SubscriptionData.SubscriptionID.ToString()), change.ShopID);
                                    }

                                    //update shopapps
                                    if (!change.IsRemoveApp)
                                    {
                                        AppStoreManager.SetAppAsPaid(subscriptionID, change.ShopID, change.AppID, change.PlanID, change.ContractID);
                                        Log4NetLogger.Info(string.Format("UpdateAppSubscription: SetAppAsPaid. StoreYa subscription ID - {0}.", subscriptionID), change.ShopID);
                                    }
                                }
                                else
                                {
                                    string message = string.Format("Failed to update BlueSnapSubscription {0} at BlueSnap API removing app payment for {1} and changing subscription to planID: {2} with new charge: {3}.",
                                   response.SubscriptionData.BsSubscriptionID.ToString(),
                                   appEntity.AppName,
                                   change.PlanID,
                                   response.TotalSubscriptionCharge
                                   );

                                    Log4NetLogger.Info(message, shopID);

                                    SendEmail("Shop subscription should be changed manually.", message + Environment.NewLine + string.Format("Shop: {0}", shopID));

                                }
                            }
                            else
                            {
                                string message = string.Format("The BlueSnap subscription can not be updated automatically removing  a payment for {0}. RequiredSubscriptionToCancel is not null. Please, check this flow.", appEntity.AppName);

                                Log4NetLogger.Info(message, shopID);

                                SendEmail("Shop subscription should be changed manually.", message + Environment.NewLine + string.Format("Shop: {0}", shopID));
                            }
                        }
                        else
                        {
                            string message = string.Format("The subscription can not be updated automatically removing app payment for {0} because this shop app has not changeable subscription. {1} PaymentAdapterType: {2}{1}, PaymetMethod: {3}",
                                appEntity.AppName,
                                Environment.NewLine,
                                (shopSubscription.PaymentAdapterType.HasValue ? ((PaymentAdapterTypes)shopSubscription.PaymentAdapterType).ToString() : null),
                                shopSubscription.PaymentMethod);

                            Log4NetLogger.Info(message, shopID);

                            SendEmail("Shop subscription should be changed manually.", message + Environment.NewLine + string.Format("Shop: {0}", shopID));
                        }
                    }
                }
                else
                {
                    string message = string.Format("The subscription for {0} can not be cancelled automatically because this shop app has PaymentPlan < 0.", appEntity.AppName);
                    Log4NetLogger.Info(message, shopID);

                    SendEmail("Shop subscription should be changed manually.", message + Environment.NewLine + string.Format("Shop: {0}", shopID));
                }
            }
            catch (Exception ex)
            {
                string message = "Failed to cancel/update shop app subsciption.";
                Log4NetLogger.Error(message, ex, shopID);    
                SendEmail("Shop subscription cancellation has failed.", message + Environment.NewLine + string.Format("Shop: {0}", shopID) + Environment.NewLine + ex.ToString());
            }
        }


        public static void SendEmail(string title, string message)
        {
            string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
            EmailHelper.SendEmail(email, title, message, null, null, true, "SYSTEM");
        }
    }
}
