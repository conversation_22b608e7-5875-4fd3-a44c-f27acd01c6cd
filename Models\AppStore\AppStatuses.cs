﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Storeya.Core.Models.AppStore
{
    public enum AppPaymentStatus
    {
        Active = 10,
        Trial = 1,
        Disabled = -1,
        ActiveAsFree = -100
    }

    public enum AppRegStatus
    {
        None = 0,
        Created = 1,
        Installed = 2,
        Old_NotPaid = 3
    }
    public enum SUBSCRIPTION_STATUS
    {
        UNKNOWN = 0,
        ACTIVE = 1,
        CANCEL = -1,
    }
    public enum LastChargeStatus
    {
        ImportFromBS = -100,
        Failed_Rebill = -10,
        Failed_Charge = -2,
        Cancelled = -1,
        None = 0,
        Charged = 1,
        Pending_Rebill = 10,       
        Waiting_Recurring_Webhook = 20,
        Waiting_Upgrade_Webhook = 21,
    }
    public enum CUSTOM_CONTRACT_TYPE
    {
        NONE = 0,
        PAYAT_1AND15 = 15
    }
    public enum ReportSubscriberStatus
    {
        ACTIVE = 10,
        PAUSED = -10,
        UNKNOWN = 0,
    }
}