﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Storeya.Core.Helpers;

namespace Storeya.Core.Models.AppStore
{
    public class AppStoreEmailEntity
    {
        public int ShopID { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string LogBackUsing { get; set; }
        public string AppName { get; set; }
        public string Domain { get; set; }
        public string ChoosePlanLink { get; set; }
        //public string ReadMoreLink { get; set; }
        //public string TrialWillExpireIn_Hours { get; set; }
        public string TrialWillExpireIn { get; set; }
        //public string LinkToNewTicket { get; set; }

        public string ImagesPath
        {
            get
            {
                return HttpHelper.GetCurrentDomain() +"/EmailTemplates/AppStore/images";
                //d:\Dev\StoreYa\trunk\storeya\EmailTemplates\Performance\images\downloads.gif
            }
        }
        public string ImagesPathPerformance
        {
            get
            {
                return HttpHelper.GetCurrentDomain() +"/EmailTemplates/Performance/images";
                //d:\Dev\StoreYa\trunk\storeya\EmailTemplates\Performance\images\downloads.gif
            }
        }
        public string OpenBracket
        {
            get
            {

                return "{";
            }

        }


        public string CloseBracket
        {
            get
            {

                return "}";
            }

        }

        public AppStoreEmailEntity(int shopID, int appTypeID, int trialsDaysLeft)
        {
            this.ShopID = shopID;
            this.AppName = AppStoreManager.GetAppByID(appTypeID).AppName;
            //this.TrialWillExpireIn_Hours = trialsDaysLeft * 24;
            if (trialsDaysLeft > 2)
            {
                this.TrialWillExpireIn = trialsDaysLeft + " days";
            }
            else
            {
                this.TrialWillExpireIn = trialsDaysLeft * 24 + " hours";
            }

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(s => s.ID == shopID).Single();
            User user = db.Users.Where(u => u.ID == shop.UserID).Single();

            this.Name = user.Name;
            this.Email = user.Email;
            this.LogBackUsing = string.Format("Facebook profile <a href=\"https://www.facebook.com/profile.php?id={0}\">{1}</a>", user.FbProfileID, user.Name);
            this.Domain = GetShopAppDomain(shopID, appTypeID);
            this.ChoosePlanLink = string.Format("{0}/billing/chooseplan/{1}?utm_source=upgemail&utm_medium={1}&utm_campaign=days{2}", GetCurrentDomainOrStoreYaForExe(), AppStoreManager.GetAppKeyForView(appTypeID), trialsDaysLeft);
            
        }


        private string GetCurrentDomainOrStoreYaForExe()
        {
            string domain = Storeya.Core.HttpHelper.GetCurrentDomain();
            if (string.IsNullOrEmpty(domain))
            {
                domain = "https://www.storeya.com";
            }
            return domain;
        }

        private string GetShopAppDomain(int shopID, int appID)
        {
            string domain = "your store";

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            switch ((AppTypes)appID)
            {
                case AppTypes.CouponPop:
                    CouponPopInstallationDomain couponPopInstallationDomain = db.CouponPopInstallationDomains.Where(c => c.ShopID == shopID).SingleOrDefault();
                    if (couponPopInstallationDomain != null && !string.IsNullOrEmpty(couponPopInstallationDomain.Domain))
                    {
                        domain = couponPopInstallationDomain.Domain;
                    }
                    break;

                case AppTypes.ExitPop:
                    ExitPopInstallationDomain exitPopInstallationDomain = db.ExitPopInstallationDomains.Where(c => c.ShopID == shopID).SingleOrDefault();
                    if (exitPopInstallationDomain != null && !string.IsNullOrEmpty(exitPopInstallationDomain.Domain))
                    {
                        domain = exitPopInstallationDomain.Domain;
                    }
                    break;

                case AppTypes.RFF:
                    RffOffer offer = db.RffOffers.Where(r => r.ShopID == shopID).FirstOrDefault();
                    if (offer != null && !string.IsNullOrEmpty(offer.Website))
                    {
                        domain = offer.Website;
                    }
                    break;

                default:
                    break;
            }

            return domain;
        }
    }
}
