﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Storeya.Core;
using Storeya.Core.Helpers;
using Storeya.Core.Models;
using Storeya.Core.Models.Payments;
using Storeya.Core.Models.CatalogSync;
using Storeya.Core.Entities;
using Storeya.Core.Models.UserIntelligence;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Api;
using Storeya.Core.Models.AuditTool;
using System.Globalization;

using Storeya.Core.Models.Marketplaces;
using Storeya.Core.Models.Charges;
using Storeya.Core.Models.TrafficBoosterModels.TrafficChannels;
using Storeya.Core.Models.Plimus;
using Storeya.Core.Models.Account;
using Storeya.Core.Models.TbInternalTasks;
using Storeya.Core.Models.ProductDescriber;
using System.Data.Entity;
using System.Security.AccessControl;

namespace Storeya.Core.Models.AppStore
{
    public class AppStoreManager
    {
        StoreYaEntities _db = DataHelper.GetStoreYaEntities();

        public static void CreateAppIfNeeded(Shop shop, AppTypes appType)
        {

            var app = AppStoreManager.GetAppSettings(shop.ID, (int)appType);

            if (app == null)
            {
                ShopApp appWithSuitePlan = AppStoreManager.GetOtherUsersShopPrivateJetApp(shop.ID, (int)appType);

                //ShopSubscription o_d_p_d_sub = null;

                //one dollar per month contract unsupported
                //if (IsPaidApp((int)appType) && appType != AppTypes.TrafficBooster)
                //{
                //    o_d_p_d_sub = GetOneDollarPerDayContractIfExist(shop.ID, (int)appType);
                //}

                //if (appWithSuitePlan != null)// || o_d_p_d_sub != null)
                //{
                //    //if (o_d_p_d_sub != null)
                //    //{
                //    //    Log4NetLogger.Info(string.Format("The {0} app will be registered on OneDollarPerDay contract.", (int)appType), shop.ID);
                //    //}

                //    int planID = ((appWithSuitePlan != null) ? (int)PlanTypes.PrivateJet : (int)PlanTypes.Economy);
                //    //int subscriptionID = ((appWithSuitePlan != null) ? (appWithSuitePlan.SubscriptionID ?? 0) : o_d_p_d_sub.ID);
                //    //string price = ((appWithSuitePlan != null) ? appWithSuitePlan.AppPrice.ToString() : o_d_p_d_sub.ContractPrice);

                //    int subscriptionID = appWithSuitePlan.SubscriptionID.Value;
                //    string price = appWithSuitePlan.AppPrice.ToString();

                //    AppStoreManager.CreatePaidApp(shop.ID, (shop.UserID ?? 0), (int)appType, planID, price, subscriptionID);

                //    if (appType == AppTypes.FacebookShop)
                //    {
                //        StoreYaEntities db = DataHelper.GetStoreYaEntities();
                //        Shop shopFromDb = db.Shops.Where(s => s.ID == shop.ID).Single();
                //        shopFromDb.IsPaid = planID;

                //        db.SaveChanges();
                //    }

                //    return;
                //}

                if (IsShopifyPaid(shop.ID, (int)appType))//(AppStoreManager.IsShopify(shop.ID) && appType == AppTypes.CouponPop)
                {
                    //Shopify pay monthly but year discounted price
                    int contMethod = 1;
                    //double appPrice = BluesnapHelper.GetContract((int)PlanTypes.Economy, contMethod, (int)appType).Price;
                    double appPrice = ConractSettingsHelper.GetContractPrice((int)PlanTypes.Economy, contMethod, (int)appType);
                    appPrice = GetMonthlyPriceForShopify(appPrice);

                    AppStoreManager.CreatePaidApp(shop.ID, shop.UserID ?? 0, (int)appType, (int)PlanTypes.Economy, appPrice.ToString(), (int)SpecificSubscriptionID.Shopify);
                    return;
                }
            }

            AppStoreManager.SetAppAsStarted(shop.ID, (int)appType);

        }

        private static double GetMonthlyPriceForShopify(double yearlyPrice)
        {
            return yearlyPrice / 12;
        }
        public static ShopApp GetFirstSuiteWidget(int currentShopId)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopApp suitApp = db.ShopApps.Where(sa => sa.ShopID == currentShopId
                && (sa.PaymentPlan == (int)PlanTypes.Tuxedo || sa.PaymentPlan == (int)PlanTypes.PrivateJet)).FirstOrDefault();
            return suitApp;
        }

        //public static ShopSubscription GetOneDollarPerDayContractIfExist(int currentShopId, int appType)
        //{
        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
        //    //List<ShopSubscription> oneDollarPerDayContractSubscriptions = db.ShopSubscriptions.Where(s => s.ShopID == currentShopId && s.ContractID.HasValue && (s.ContractID == (int)BlueSnapContractIDs.OneDollarPerDayMonthly || s.ContractID == (int)BlueSnapContractIDs.OneDollarPerDayAnnual)).ToList();

        //    //if(oneDollarPerDayContractSubscriptions!=null && oneDollarPerDayContractSubscriptions.Count > 0)
        //    //{
        //    //    return oneDollarPerDayContractSubscriptions.FirstOrDefault();
        //    //}

        //    List<ShopApp> paidApps = db.ShopApps.Where(sa => sa.ShopID == currentShopId && sa.AppTypeID != appType && sa.PaymentPlan > 0 && sa.SubscriptionID.HasValue && sa.SubscriptionID > 0 && sa.SubscriptionID != (int)SpecificSubscriptionID.Shopify && sa.SubscriptionID != (int)SpecificSubscriptionID.Tictail && sa.SubscriptionID != (int)SpecificSubscriptionID.Wix).ToList();
        //    if (paidApps != null && paidApps.Count > 0)
        //{
        //        foreach (ShopApp shopApp in paidApps)
        //        {
        //            ShopSubscription subscr = db.ShopSubscriptions.Where(ss => ss.ID == shopApp.SubscriptionID).FirstOrDefault();
        //            if (subscr != null && (subscr.ContractID == bluesnapConfiguration.OneDollarPerDayMonthly || subscr.ContractID == bluesnapConfiguration.OneDollarPerDayAnnual))
        //            {
        //                return subscr;
        //}
        //        }
        //    }

        //    return null;
        //}

        public static ShopApp GetOtherUsersShopPrivateJetApp(int currentShopId, int appType)
        {
            if (appType == (int)AppTypes.TrafficBooster)
            {
                //not relevant to TB
                return null;
            }

            //see if we need to mark just created app as paid because PJ was purchased for this app in other shop

            ShopApp appWithSuitePlan = null;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop currentShop = db.Shops.Where(s => s.ID == currentShopId).Single();
            User user = db.Users.Where(u => u.ID == currentShop.UserID).Single();

            if (user.ID == 232458) // is agency, which has stores with different plans
                return null;

            List<ShopApp> usersShopApps = db.ShopApps.Where(sa => sa.UserID == user.ID && sa.AppTypeID == appType && sa.ShopID > 0 && sa.ShopID != currentShopId).ToList();
            if (usersShopApps != null && usersShopApps.Count > 0)
            {
                appWithSuitePlan = usersShopApps.Where(usa => usa.PaymentPlan == (int)PlanTypes.Tuxedo || usa.PaymentPlan == (int)PlanTypes.PrivateJet).FirstOrDefault();
            }
            return appWithSuitePlan;
        }

        public static bool IsShopify(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(s => s.ID == shopID).Single();
            User user = db.Users.Where(u => u.ID == shop.UserID).Single();
            ShopifyConnectedShop connectedShop = db.ShopifyConnectedShops.Where(t => t.ShopID == shopID).FirstOrDefault();
            if (shop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi || user.OriginMarketplace == 3 || connectedShop != null)
            {
                return true;
            }
            return false;
        }

        public static bool IsShopifyApp(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(s => s.ID == shopID).Single();
            User user = db.Users.Where(u => u.ID == shop.UserID).Single();

            int storeYaAppType = (int)StoreyaAppHelper.GetStoreyaAppType(appID);

            ShopifyConnectedShop connectedShop = db.ShopifyConnectedShops.Where(t => t.ShopID == shop.ID && t.StoreyaAppTypeID == storeYaAppType).FirstOrDefault();
            if (connectedShop != null
                && (shop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.ShopifyApi || user.OriginMarketplace == 3))
            {
                return true;
            }
            return false;
        }

        public static bool IsShopifyActiveAsFree(int shopID, int appType)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            int storeYaAppType = StoreyaAppHelper.GetStoreyaAppType(appType);
            ShopifyConnectedShop connectedShop = db.ShopifyConnectedShops.Where(t => t.ShopID == shopID && t.StoreyaAppTypeID == storeYaAppType).FirstOrDefault();
            if (connectedShop != null && connectedShop.PaymentPlan.HasValue && connectedShop.PaymentPlan == (int)AppPaymentStatus.ActiveAsFree)
            {
                return true;
            }
            return false;
        }

        public static bool IsShopifyPaid(int shopID, int appType)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(s => s.ID == shopID).Single();
            User user = db.Users.Where(u => u.ID == shop.UserID).Single();

            int storeYaAppType = (int)StoreyaAppHelper.GetStoreyaAppType(appType);

            ShopifyConnectedShop connectedShop = db.ShopifyConnectedShops.Where(t => t.ShopID == shop.ID && t.StoreyaAppTypeID == storeYaAppType).FirstOrDefault();//(int)StoreyaApp.CouponPop  && (t.PaymentPlan ?? 0) > 0 
            if (((appType == (int)AppTypes.CouponPop && !ConfigHelper.GetBoolValue("CPFreeInstallation")) || appType == (int)AppTypes.ExitPop)
                && user.OriginMarketplace == (int)Storeya.Core.OriginMarketplaces.Shopify
                && connectedShop != null && connectedShop.PaymentPlan.HasValue && connectedShop.PaymentPlan > 0 && connectedShop.PaymentPlan != 1)
            {
                return true;
            }
            return false;
        }

        //not in use?
        //private static bool HasOnlyExternalSubscriptionOfOtherApp(List<ShopApp> paidApps, int appType)
        //{
        //    bool allAppsHaveSameSource = false;

        //    var shopifySubscriptionsOfOtherApp = from sa in paidApps.Where(s => s.SubscriptionID.HasValue
        //                                     && s.SubscriptionID.Value > 0
        //                                     && s.SubscriptionID.Value == (int)SpecificSubscriptionID.Shopify)
        //                                         select sa;

        //    var tictailSubscriptionsOfOtherApp = from sa in paidApps.Where(s => s.SubscriptionID.HasValue
        //                                     && s.SubscriptionID.Value > 0
        //                                     && s.SubscriptionID.Value == (int)SpecificSubscriptionID.Tictail)
        //                                         select sa;

        //    var wixSubscriptionsOfOtherApp = from sa in paidApps.Where(s => s.SubscriptionID.HasValue
        //                                     && s.SubscriptionID.Value > 0
        //                                     && s.SubscriptionID.Value == (int)SpecificSubscriptionID.Wix)
        //                                     select sa;

        //    // check if all shop apps have one source
        //    if (paidApps.Count == shopifySubscriptionsOfOtherApp.ToList().Count
        //        || paidApps.Count == tictailSubscriptionsOfOtherApp.ToList().Count
        //        || paidApps.Count == wixSubscriptionsOfOtherApp.ToList().Count)
        //    {
        //        allAppsHaveSameSource = paidApps.Where(s => s.AppTypeID != appType).Any();
        //    }

        //    return allAppsHaveSameSource;
        //}

        //not in use?
        //private static bool HasSubscriptionOf(User user, PaymentAdapterTypes paymentAdapterType)
        //{
        //    List<ShopApp> paidApps = GetPaidShopApps(user);
        //    if (paidApps != null && paidApps.Count > 0)
        //    {
        //        StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //        var notPayPalPaidApps = from sa in paidApps.Where(s => s.SubscriptionID.HasValue && s.SubscriptionID.Value > 0
        //                                && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Shopify
        //                                && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Tictail
        //                                && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Wix)
        //                                join subs in db.ShopSubscriptions on sa.SubscriptionID equals subs.ID
        //                                where subs.PaymentAdapterType == (int)paymentAdapterType
        //                                select sa;

        //        return notPayPalPaidApps.Any();
        //    }
        //    return false;
        //}

        //not in use?
        //private static bool HasChangableOrPayPalSubscription(int shopID)
        //{
        //    List<ShopApp> paidApps = GetPaidShopApps(shopID);
        //    if (paidApps != null && paidApps.Count > 0)
        //    {
        //        StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //        var changableOrPayPalSubscription = from sa in paidApps.Where(s => s.SubscriptionID.HasValue && s.SubscriptionID.Value > 0
        //                                                                                         && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Shopify
        //                                                                                         && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Tictail
        //                                                                                         && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Wix)
        //                                            join subs in db.ShopSubscriptions on sa.SubscriptionID equals subs.ID
        //                                            select sa;

        //        return changableOrPayPalSubscription.Any();
        //    }
        //    return false;
        //}


        public static bool HasChangebleSubscription(int shopID)
        {
            List<ShopApp> paidApps = GetPaidShopApps(shopID);
            if (paidApps != null && paidApps.Count > 0)
            {
                //StoreYaEntities db = DataHelper.GetStoreYaEntities();
                var notPayPalPaidApps = from sa in paidApps.Where(s => (s.SubscriptionID.HasValue && s.SubscriptionID.Value > 0 || s.HasAgreements > 0)
                                        && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Shopify
                                        && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Tictail
                                        && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Wix)

                                            //join subs in db.ShopSubscriptions on sa.SubscriptionID equals subs.ID
                                            //where (string.IsNullOrEmpty(subs.PaymentMethod) || subs.PaymentMethod.ToLower() != "paypal")
                                        select sa;

                bool ret = notPayPalPaidApps.Any();
                return ret;
            }
            return false;
        }

        public static bool HasAgreement(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var paidAppsWithAgreement = from sa in db.ShopApps.Where(a => a.ShopID == shopID && a.HasAgreements != null && a.PaymentPlan.HasValue && a.PaymentPlan > 0)
                                        select sa;

            return paidAppsWithAgreement.Any();
        }

        public static List<ShopApp> GetNotPaidAppsOtherThanCurrentApp(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var apps = db.ShopApps.Where(a => a.ShopID == shopID && a.AppTypeID != appID && a.AppTypeID != (int)AppTypes.TrafficBooster && appID < 1000);

            List<ShopApp> appsToMark = null;
            if (apps.Any())
            {
                foreach (var item in apps)
                {
                    if (IsPaidApp(item.AppTypeID) && (!item.PaymentPlan.HasValue || item.PaymentPlan.Value < 0))
                    {
                        if (appsToMark == null)
                            appsToMark = new List<ShopApp>();

                        appsToMark.Add(item);
                    }
                }

                return appsToMark;
            }
            return null;
        }

        public static List<ShopApp> GetPaidApps(int shopID)
        {
            var apps = GetShopApps(shopID);
            if (apps != null)
            {
                return apps.Where(s => (s.PaymentPlan ?? 0) > 0).ToList();
            }
            return null;
        }


        public static bool IsHasPaidApp(int shopID, int? otherThenAppID = null)
        {
            var apps = GetShopApps(shopID);
            if (otherThenAppID == null)
            {
                return apps.Where(s => (s.PaymentPlan ?? 0) > 0).Any();
            }
            else
            {
                return apps.Where(s => (s.PaymentPlan ?? 0) > 0 && s.AppTypeID != otherThenAppID).Any();
            }

        }

        public static void SetAppAsStarted(int shopID, int appType)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = AppStoreManager.GetAppSettings(shopID, appType);
            if (app == null)
            {
                AppTypes apptype = (AppTypes)appType;

                switch (apptype)
                {

                    case AppTypes.CouponPop:
                        if (ConfigHelper.GetBoolValue("CPFreeInstallation"))
                        {
                            CreateFreeApp(shopID, (int)AppTypes.CouponPop);
                        }
                        else
                        {
                            CreateCouponPopShopApp(shopID);
                        }
                        break;

                    case AppTypes.ExitPop:
                        CreateExitPopShopApp(shopID);
                        break;

                    case AppTypes.FacebookShop:
                        CreateFacebookShopApp(shopID);
                        break;

                    //case AppTypes.LikeBox:
                    //    CreateFreeApp(shopID, (int)AppTypes.LikeBox);
                    //    break;

                    //case AppTypes.PoweredBanner:
                    //    CreateFreeApp(shopID, (int)AppTypes.PoweredBanner);
                    //    break;

                    //case AppTypes.RFF:
                    //    CreateRFFShopApp(shopID);
                    //    break;

                    case AppTypes.TrafficBooster:
                        CreateFreeApp(shopID, (int)AppTypes.TrafficBooster);
                        break;

                    //case AppTypes.TrafficTracker:
                    //    CreateFreeApp(shopID, (int)AppTypes.TrafficTracker);
                    //    break;

                    case AppTypes.FacebookAdsGrader:
                        CreateFreeApp(shopID, (int)AppTypes.FacebookAdsGrader);
                        break;

                    case AppTypes.BenchmarkHero:
                        CreateFreeApp(shopID, (int)AppTypes.BenchmarkHero);
                        break;
                    case AppTypes.GrowthHero:
                        CreateFreeApp(shopID, (int)AppTypes.GrowthHero);
                        break;
                    case AppTypes.ProductDescriber:
                        CreateFreeApp(shopID, (int)AppTypes.ProductDescriber);
                        break;
                    default:

                        break;
                }

                AppStoreManager.AddEventToIntercom(shopID, "GetApp_" + apptype.ToString());
            }


        }



        //public static void UpdatePaidShopAppAsInstalled(int shopID, int appType, int? appPaymentStatus, int? paymentPlan, string appPrice, int? subscriptionID)
        //{
        //    try
        //    {
        //        int appRegStatus = (int)AppRegStatus.Installed;
        //        StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //        ShopApp appToUpdate = AppStoreManager.GetAppSettings(shopID, appType);
        //        if (appToUpdate != null)
        //        {
        //            appToUpdate.RegStatus = appRegStatus;
        //            appToUpdate.PaymentStatus = appPaymentStatus;
        //            appToUpdate.PaymentPlan = paymentPlan;
        //            appToUpdate.AppPrice = appPrice;
        //            //appToUpdate.PaymentPlan = paymentPlan;
        //            //appToUpdate.PaymentPlanUpdatedAt = DateTime.Now;
        //            if (appPaymentStatus == (int)AppPaymentStatus.Trial)
        //            {
        //                appToUpdate.TrialStartedAt = DateTime.Now;
        //                appToUpdate.TrialEndsAt = DateTime.Now.AddDays(14);
        //            }
        //            appToUpdate.UpdatedAt = DateTime.Now;
        //            appToUpdate.SubscriptionID = subscriptionID;
        //            db.SaveChanges();

        //            string eventType = "TrialStarted";
        //            if (appPaymentStatus == (int)AppPaymentStatus.Active)
        //            {
        //                eventType = "PaidInstallation";
        //            }
        //            var appName = AppStoreManager.GetAppByID(appType).AppName;
        //            Eventer.ReportEvent(shopID, EventCategory.AppStore, BackgroundEvents.app_purchased.ToString(), appName);
        //            //AmplitudeApi.ReportSimpleEvent(shopID, BackgroundEvents.app_purchased, appName);

        //            AddEventToIntercom(shopID, appType, eventType);
        //        }
        //        else
        //        {
        //            Log4NetLogger.Error(string.Format("There is no shop app record to update with apptype: {0}, appRegStatus: {1}, appPaymentStatus: {2}", appType, appRegStatus, appPaymentStatus), shopID);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log4NetLogger.Error("Failed to update a ShopApp record.", ex, shopID);
        //    }
        //}


        public static bool AddEventToIntercom(Shop shop, User user, string eventName)
        {
            //if (!EmailHelper.IsBacklistedForEmails(user))
            //    return false;

            bool posted = PostDataToIntercom(shop, user, eventName);
            return posted;
        }

        public static bool AddEventToIntercom(int shopID, string eventName)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.SingleOrDefault(s => s.ID == shopID);
            User user = db.Users.SingleOrDefault(u => u.ID == shop.UserID);

            return AddEventToIntercom(shop, user, eventName);
        }

        public static bool AddEventToIntercom(int shopID, int? appType, string eventType)
        {
            return AddEventToIntercom(shopID, GetEventName(eventType, appType));
        }

        private static bool PostDataToIntercom(Shop shop, User user, string eventName)
        {
            bool posted = false;

            if (user == null || string.IsNullOrEmpty(user.Email))
            {
                return false;
            }

            try
            {
                string intercomUserID = null;
                intercomUserID = IntercomAPI.GetUserID(user.Email, shop.ID);
                if (!string.IsNullOrEmpty(intercomUserID))
                {
                    IntercomAPI.AddEvent(user.Email, eventName);
                    posted = true;
                }
                else
                {
                    string name = user.Name;

                    //Log4NetLogger.Info("Trying to add user to Intercom. Email: " + user.Email, (int)Log4NetLogger.SpecialShopIDs.CriticalError);
                    string newIntercomUserID = IntercomAPI.AddUser(user.Email, name, shop.ID, user.UserType);
                    if (!string.IsNullOrEmpty(newIntercomUserID))
                    {
                        IntercomAPI.AddEvent(user.Email, eventName);
                        posted = true;
                    }
                    else
                    {
                        Log4NetLogger.Error(string.Format("Failed to add user to Intercom at event addition. Event name: {0}, email: {1}", eventName, user.Email), (int)Log4NetLogger.SpecialShopIDs.CriticalError);
                    }

                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to add event: {0} for email: {1}", eventName, user.Email), ex, (int)Log4NetLogger.SpecialShopIDs.CriticalError);
            }
            return posted;
        }


        public static string GetEventName(string eventType, int? appType)
        {
            string eventName = null;
            if (appType == null)
            {
                eventName = eventType;
            }
            else
            {
                string apptypeInitials = GetApptypeInitials(appType.Value);

                if (eventType == "TrialStarted")
                {
                    eventName = string.Format("Installed {0}", apptypeInitials);
                }
                else if (eventType == "NewAccount" || eventType == "PaidInstallation")
                {
                    eventName = string.Format("Paid {0}", apptypeInitials);
                }
                else
                {
                    eventName = eventType;
                }

            }

            return eventName;
        }

        private static string GetApptypeInitials(int appType)
        {
            if (appType == (int)AppTypes.CouponPop)
            {
                return "CP";
            }
            else if (appType == (int)AppTypes.ExitPop)
            {
                return "EP";
            }
            else if (appType == (int)AppTypes.FacebookShop)
            {
                return "FS";
            }
            else if (appType == (int)AppTypes.RFF)
            {
                return "RFF";
            }
            else if (appType == (int)AppTypes.PoweredBanner)
            {
                return "PB";
            }
            else if (appType == (int)AppTypes.TrafficBooster)
            {
                return "TB";
            }
            else if (appType == (int)AppTypes.TrafficTracker)
            {
                return "TT";
            }
            else if (appType == (int)AppTypes.FacebookAdsGrader)
            {
                return "FAG";
            }
            else if (appType == (int)AppTypes.BenchmarkHero)
            {
                return "BH";
            }
            return "Not Paid App";
        }


        public static void CreatePaidApp(int shopID, int userID, int appType, int paymentPlan, string appPrice, int subscriptionID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            ShopApp shopAppRecordToAdd = new ShopApp();
            shopAppRecordToAdd.ShopID = shopID;
            shopAppRecordToAdd.UserID = userID;
            shopAppRecordToAdd.AppTypeID = appType;
            shopAppRecordToAdd.InsertedAt = DateTime.Now;
            shopAppRecordToAdd.RegStatus = (int)AppRegStatus.Installed;
            shopAppRecordToAdd.PaymentStatus = (int)AppPaymentStatus.Active;
            shopAppRecordToAdd.PaymentPlan = paymentPlan;
            shopAppRecordToAdd.AppPrice = appPrice;
            shopAppRecordToAdd.SubscriptionID = subscriptionID;
            shopAppRecordToAdd.PaymentPlanUpdatedAt = DateTime.Now;
            shopAppRecordToAdd.UpdatedAt = DateTime.Now;
            db.ShopApps.Add(shopAppRecordToAdd);
            db.SaveChanges();

            Log4NetLogger.Info(string.Format("Paid shop app record was created. apptype: {0}, appRegStatus: {1}, appPaymentStatus: {2}, paymentPlan: {3}, appPrice: {4}, subscriptionID: {5}", appType, ((AppRegStatus)shopAppRecordToAdd.RegStatus).ToString(), ((AppPaymentStatus)shopAppRecordToAdd.PaymentStatus).ToString(), shopAppRecordToAdd.PaymentPlan, shopAppRecordToAdd.AppPrice, shopAppRecordToAdd.SubscriptionID), shopID);

            SystemEventHelper.Add(userID, shopID, appType, SystemEventTypes.AppStore, SystemEventActions.AppInstalled, "Paid app installation", null);
        }

        public static void UpdateShopAppAsInstalled(int shopID, int appType, int? appPaymentStatus = null)
        {
            try
            {
                int appRegStatus = (int)AppRegStatus.Installed;
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                ShopApp appToUpdate = AppStoreManager.GetAppSettings(shopID, appType);
                if (appToUpdate != null)
                {
                    appToUpdate.RegStatus = appRegStatus;

                    if ((appToUpdate.PaymentStatus ?? 0) != (int)AppPaymentStatus.Disabled
                        && (appToUpdate.PaymentStatus ?? 0) != (int)AppPaymentStatus.ActiveAsFree
                        && (appToUpdate.PaymentStatus ?? 0) < appPaymentStatus)
                        appToUpdate.PaymentStatus = appPaymentStatus;

                    if (appToUpdate.PaymentStatus == (int)AppPaymentStatus.Trial && appPaymentStatus == (int)AppPaymentStatus.Trial)
                    {
                        appToUpdate.TrialStartedAt = DateTime.Now;
                        appToUpdate.TrialEndsAt = DateTime.Now.AddDays(14);

                        //report to intercom only if trial indeed started (Shopify or example not reported)
                        AddEventToIntercom(shopID, appType, "TrialStarted");
                    }

                    appToUpdate.UpdatedAt = DateTime.Now;
                    db.SaveChanges();

                    var appName = AppStoreManager.GetAppByID(appType).AppName;
                    Eventer.ReportEvent(shopID, EventCategory.AppStore, BackgroundEvents.app_usage_started.ToString(), appName);
                    //AmplitudeApi.ReportSimpleEvent(shopID, BackgroundEvents.app_usage_started, appName);

                }
                else
                {
                    Log4NetLogger.Error(string.Format("There is no shop app record to update with apptype: {0}, appRegStatus: {1}, appPaymentStatus: {2}", appType, appRegStatus, appPaymentStatus), shopID);
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Failed to update a ShopApp record.", ex, shopID);
            }
        }

        private static void CreateFacebookShopApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = AppStoreManager.GetAppSettings(shopID, (int)AppTypes.FacebookShop);
            if (app == null)
            {
                CreateShopApp(shopID, (int)AppTypes.FacebookShop, (int)AppRegStatus.Created);
            }
        }

        private static void CreateCouponPopShopApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = AppStoreManager.GetAppSettings(shopID, (int)AppTypes.CouponPop);
            if (app == null)
            {
                if (!db.CouponPopInstallationDomains.Where(a => a.ShopID == shopID).Any()) //new customer
                {
                    CreateShopApp(shopID, (int)AppTypes.CouponPop, (int)AppRegStatus.Created);
                }
            }
        }

        private static void CreateExitPopShopApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = AppStoreManager.GetAppSettings(shopID, (int)AppTypes.ExitPop);
            //if (!db.ShopApps.Where(a => a.ShopID == shopID && a.AppTypeID == (int)AppTypes.ExitPop).Any())
            if (app == null)
            {
                if (!db.ExitPopInstallationDomains.Where(a => a.ShopID == shopID).Any()) //new customer
                {
                    CreateShopApp(shopID, (int)AppTypes.ExitPop, (int)AppRegStatus.Created);
                }
                //else // old customers
                //{
                //    CreateShopApp(shopID, (int)AppTypes.ExitPop, (int)AppRegStatus.Installed);
                //}
            }
        }

        private static void CreateRFFShopApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = AppStoreManager.GetAppSettings(shopID, (int)AppTypes.RFF);
            if (app == null)
            {
                if (!db.RffEvents.Where(r => r.ShopID == shopID && r.EventType == (int)Storeya.Core.Models.ReferAFriend.RffStatsModel.RffEventTypes.RefererOfferViewed).Any()) //new customer
                {
                    CreateShopApp(shopID, (int)AppTypes.RFF, (int)AppRegStatus.Created);
                }
                //else // old customers
                //{
                //    CreateShopApp(shopID, (int)AppTypes.RFF, (int)AppRegStatus.Installed);
                //}
            }
        }


        public static void UpdateRffShopApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = AppStoreManager.GetAppSettings(shopID, (int)AppTypes.RFF);
            if (app != null)
            {
                if (app.RegStatus == (int)AppRegStatus.Created && app.UserID != 0)
                {
                    //user = 0 means old user that shold use it for free for now
                    UpdateShopAppAsInstalled(shopID, (int)AppTypes.RFF, (int)AppPaymentStatus.Trial);
                }

            }
            ////update created to installed trial for new customer
            //if (db.ShopApps.Where(a => a.ShopID == shopID && a.AppTypeID == (int)AppTypes.RFF && a.RegStatus == (int)AppRegStatus.Created).Any()) //new installation
            //{
            //    UpdateShopAppAsInstalled(shopID, (int)AppTypes.RFF, (int)AppPaymentStatus.Trial);
            //}
        }

        public static void CreateFreeApp(int shopID, int appType)
        {
            //free app will be marked as installed from the begining since they dont' have product pages with GetApp buttons

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopApp app = AppStoreManager.GetAppSettings(shopID, appType);
            if (app == null) //new installation
            {
                CreateShopApp(shopID, appType, (int)AppRegStatus.Installed);
            }
        }

        //private static void CreateShopApp(int shopID, int appType, int appRegStatus, int? appPaymentStatus = null, int? paymentPlan = null)
        public static void CreateShopApp(int shopID, int appType, int appRegStatus)
        {
            try
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                Shop shop = db.Shops.Where(sh => sh.ID == shopID).SingleOrDefault();


                AddShopAppRecord(shop.ID, shop.UserID.Value, appType, appRegStatus, db);

                AppEntity appEntity = GetAppByID(appType);
                var eventName = BackgroundEvents.app_installed.ToString();
                Eventer.ReportEvent(shopID, EventCategory.AppStore, eventName, appEntity.AppName);
                Log4NetLogger.Info(string.Format("{0} App Created", appEntity.AppName), shopID);
                AddEventToIntercom(shopID, eventName + " " + appEntity.AppName);

                SystemEventHelper.Add(shop.UserID.Value, shop.ID, appType, SystemEventTypes.AppStore, SystemEventActions.AppInstalled, null, null);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Failed to create a ShopApp record.", ex, shopID);
            }
        }

        private static void AddShopAppRecord(int shopID, int userID, int appType, int appRegStatus, StoreYaEntities db)
        {
            ShopApp shopAppRecordToAdd = new ShopApp();
            shopAppRecordToAdd.ShopID = shopID;
            shopAppRecordToAdd.UserID = userID;
            shopAppRecordToAdd.AppTypeID = appType;
            shopAppRecordToAdd.InsertedAt = DateTime.Now;
            shopAppRecordToAdd.RegStatus = appRegStatus;

            if (IsShopify(shopID) && IsShopifyActiveAsFree(shopID, appType))
            {
                shopAppRecordToAdd.PaymentStatus = (int)AppPaymentStatus.ActiveAsFree;
            }

            shopAppRecordToAdd.UpdatedAt = DateTime.Now;

            db.ShopApps.Add(shopAppRecordToAdd);
            db.SaveChanges();
        }


        public static Dictionary<int, string> AppStoreIdsForView
        {
            get
            {
                return FillAppStoreIdsDictionaryForView(); ;
            }
        }

        private static Dictionary<int, string> FillAppStoreIdsDictionaryForView()
        {
            Dictionary<int, string> appStoreIds = new Dictionary<int, string>();

            appStoreIds.Add(1, "fbshop");
            appStoreIds.Add((int)AppTypes.CouponPop, "couponpop");
            appStoreIds.Add((int)AppTypes.ExitPop, "exitpop");
            appStoreIds.Add((int)AppTypes.RFF, "refer-a-friend");
            appStoreIds.Add((int)AppTypes.PoweredBanner, "powerbanner");
            appStoreIds.Add((int)AppTypes.TrafficBooster, "trafficbooster");
            appStoreIds.Add((int)AppTypes.TrafficTracker, "traffictracker");
            appStoreIds.Add((int)AppTypes.FacebookAdsGrader, "facebook-ads-grader");
            appStoreIds.Add(12, "group-deal");
            appStoreIds.Add(10, "scratchnwin");
            appStoreIds.Add(100, "likebox");
            appStoreIds.Add(101, "fangate");
            appStoreIds.Add(11, "instagallery");
            appStoreIds.Add(104, "youtube-tab");
            appStoreIds.Add(102, "twitter-tab");
            appStoreIds.Add(105, "pinterest-tab");
            appStoreIds.Add(103, "instagram-tab");
            appStoreIds.Add((int)AppTypes.BenchmarkHero, "benchmark-hero");
            appStoreIds.Add((int)AppTypes.GrowthHero, "growth-hero");
            appStoreIds.Add((int)AppTypes.ProductDescriber, "product-description-wizard");
            return appStoreIds;
        }

        public static string GetAppKeyForView(int appID)
        {
            return AppStoreIdsForView.FirstOrDefault(x => x.Key == appID).Value;
        }
        private static int GetAppIdByName(string appKey)
        {
            return AppStoreIdsForView.FirstOrDefault(x => x.Value == appKey).Key;
        }

        public static string GetAppName(int appTypeID)
        {
            if (appTypeID == (int)AppTypes.CouponPop)
            {
                return "Coupon Pop";
            }
            else if (appTypeID == (int)AppTypes.ExitPop)
            {
                return "Exit Pop";
            }
            else if (appTypeID == (int)AppTypes.FacebookShop)
            {
                return "Facebook Shop";
            }
            else if (appTypeID == (int)AppTypes.FanGate)
            {
                return "Fan Gate";
            }
            else if (appTypeID == (int)AppTypes.GroupDeal)
            {
                return "Group Deal";
            }
            else if (appTypeID == (int)AppTypes.InstaGallery)
            {
                return "Insta Gallery";
            }
            else if (appTypeID == (int)AppTypes.InstaTab)
            {
                return "Instagram Tab";
            }
            else if (appTypeID == (int)AppTypes.LikeBox)
            {
                return "Like Box";
            }
            else if (appTypeID == (int)AppTypes.PintrestTab)
            {
                return "Pintrest Tab";
            }
            else if (appTypeID == (int)AppTypes.RFF)
            {
                return "Refer a friend";
            }
            else if (appTypeID == (int)AppTypes.ScratchAndWinPromotion)
            {
                return "Scratch & Win";
            }
            else if (appTypeID == (int)AppTypes.TwitterTab)
            {
                return "Twitter Tab";
            }
            else if (appTypeID == (int)AppTypes.YoutubeTab)
            {
                return "YouTube Tab";
            }
            else if (appTypeID == (int)AppTypes.PoweredBanner)
            {
                return "Power Banner";
            }
            else if (appTypeID == (int)AppTypes.TrafficBooster)
            {
                return "Traffic Booster";
            }
            else if (appTypeID == (int)AppTypes.TrafficTracker)
            {
                return "Traffic Tracker";
            }
            else if (appTypeID == (int)AppTypes.FacebookAdsGrader)
            {
                return "Facebook Ads Grader";
            }
            else if (appTypeID == (int)AppTypes.BenchmarkHero)
            {
                return "Benchmark Hero";
            }
            else if (appTypeID == (int)AppTypes.GrowthHero)
            {
                return "Growth Hero";
            }
            else if (appTypeID == (int)AppTypes.ProductDescriber)
            {
                return "Product Description Wizard";
            }
            else
                return null;
        }
        private static string GetEditLink(int appTypeID)
        {
            if (appTypeID == (int)AppTypes.CouponPop)
            {
                return "/couponpops";
            }
            else if (appTypeID == (int)AppTypes.ExitPop)
            {
                return "/exitpops";
            }
            else if (appTypeID == (int)AppTypes.FacebookShop)
            {
                return "/home/<USER>";
            }
            else if (appTypeID == (int)AppTypes.FanGate)
            {
                return "/marketing/fangate";
            }
            //else if (appTypeID == (int)AppTypes.GroupDeal)
            //{
            //    return "/groupdeals";
            //}
            //else if (appTypeID == (int)AppTypes.InstaGallery)
            //{
            //    return "/galleryadmin";
            //}
            else if (appTypeID == (int)AppTypes.InstaTab)
            {
                return "/marketing/instagram";
            }
            else if (appTypeID == (int)AppTypes.LikeBox)
            {
                return "/marketing/likebox";
            }
            else if (appTypeID == (int)AppTypes.PintrestTab)
            {
                return "/marketing/pinterest";
            }
            else if (appTypeID == (int)AppTypes.RFF)
            {
                return "/referafriend/offers";
            }
            else if (appTypeID == (int)AppTypes.ScratchAndWinPromotion)
            {
                return "/scratchandwinpromotion";
            }
            else if (appTypeID == (int)AppTypes.TwitterTab)
            {
                return "/marketing/twitter";
            }
            else if (appTypeID == (int)AppTypes.YoutubeTab)
            {
                return "/marketing/youtube";
            }
            else if (appTypeID == (int)AppTypes.PoweredBanner)
            {
                return "/banners";
            }
            else if (appTypeID == (int)AppTypes.TrafficBooster)
            {
                return "/trafficbooster";
            }
            else if (appTypeID == (int)AppTypes.TrafficTracker)
            {
                return "/traffictracker";
            }
            else if (appTypeID == (int)AppTypes.FacebookAdsGrader)
            {
                return "/FacebookAdsGrader";
            }
            else if (appTypeID == (int)AppTypes.BenchmarkHero)
            {
                return "/benchmark";
            }
            else if (appTypeID == (int)AppTypes.GrowthHero)
            {
                return "/growthhero";
            }
            else if (appTypeID == (int)AppTypes.ProductDescriber)
            {
                return "/productdescriptionwizard";
            }
            else
                return null;
        }
        public static AppEntity GetAppByKey(string appKey)
        {
            AppEntity app = new AppEntity();

            if (IsExternalApp(appKey))
            {
                ExternalApp externalApp = AppStoreManager.GetExternalAppByAppUrlKey(appKey);
                app = SetAppEntityByAppUrlKey(externalApp);
                app.IsExternal = true;
            }
            else
            {
                app.AppKey = appKey;
                app.AppID = GetAppIdByName(appKey);
                app.AppName = GetAppName(app.AppID);
                app.EditLink = GetEditLink(app.AppID);
            }

            return app;
        }
        public static AppEntity GetAppByID(int appID)
        {
            AppEntity app = new AppEntity();
            app.AppKey = GetAppKeyForView(appID);
            app.AppID = appID;
            app.AppName = GetAppName(app.AppID);
            app.EditLink = GetEditLink(app.AppID);
            app.ShowChoosePlanNowButton = true;

            //if (IsExternalApp(appID))
            //{
            //    ExternalApp externalApp = GetExternalAppSettings(appID);
            //    app = SetAppEntityByAppUrlKey(externalApp);
            //    app.IsExternal = true;
            //}
            //else
            //{
            //    app.AppKey = GetAppKeyForView(appID);
            //    app.AppID = appID;
            //    app.AppName = GetAppName(app.AppID);
            //    app.EditLink = GetEditLink(app.AppID);
            //    app.ShowChoosePlanNowButton = true;
            //}
            return app;
        }

        public static bool IsFreeApp(ShopApp installedApp)
        {
            ExternalApp bestContactForm = DataHelper.GetExternalAppsFromCache().Where(ea => ea.AppUrlKey == "best-contact-form").FirstOrDefault();
            if (bestContactForm != null && bestContactForm.ID == installedApp.AppTypeID)
            {
                //best form is free app
                return true;
            }

            if (installedApp.AppTypeID == (int)AppTypes.LikeBox
                || installedApp.AppTypeID == (int)AppTypes.FanGate
                || installedApp.AppTypeID == (int)AppTypes.GroupDeal
                || installedApp.AppTypeID == (int)AppTypes.InstaGallery
                || installedApp.AppTypeID == (int)AppTypes.ScratchAndWinPromotion
                || installedApp.AppTypeID == (int)AppTypes.InstaTab
                || installedApp.AppTypeID == (int)AppTypes.PintrestTab
                || installedApp.AppTypeID == (int)AppTypes.TwitterTab
                || installedApp.AppTypeID == (int)AppTypes.YoutubeTab
                || installedApp.AppTypeID == (int)AppTypes.TrafficTracker
                || installedApp.AppTypeID == (int)AppTypes.PoweredBanner
                 || installedApp.AppTypeID == (int)AppTypes.FacebookAdsGrader
                || installedApp.AppTypeID == (int)AppTypes.BenchmarkHero
                || installedApp.AppTypeID == (int)AppTypes.GrowthHero
                || installedApp.AppTypeID == (int)AppTypes.ProductDescriber
                )
            {
                return true;
            }
            return false;
        }

        public static bool IsExternalApp(int appID)
        {
            return (appID >= 1000);

            /// or more then 1000

            //if (appID != (int)AppTypes.CouponPop
            //&& appID != (int)AppTypes.ExitPop
            //&& appID != (int)AppTypes.FacebookShop
            //&& appID != (int)AppTypes.FanGate
            //&& appID != (int)AppTypes.GroupDeal
            //&& appID != (int)AppTypes.InstaGallery
            //&& appID != (int)AppTypes.InstaTab
            //&& appID != (int)AppTypes.PintrestTab
            //&& appID != (int)AppTypes.PoweredBanner
            //&& appID != (int)AppTypes.RFF
            //&& appID != (int)AppTypes.ScratchAndWinPromotion
            //&& appID != (int)AppTypes.TwitterTab
            //&& appID != (int)AppTypes.YoutubeTab)
            //    return true;

            //return false;
        }
        public static bool IsExternalApp(string appKey)
        {
            if (appKey != "fbshop"
                && appKey != "couponpop"
                && appKey != "exitpop"
                && appKey != "refer-a-friend"
                && appKey != "powerbanner"
                && appKey != "group-deal"
                && appKey != "scratchnwin"
                && appKey != "likebox"
                && appKey != "fangate"
                && appKey != "instagallery"
                && appKey != "youtube-tab"
                && appKey != "twitter-tab"
                && appKey != "pinterest-tab"
                && appKey != "instagram-tab"
                && appKey != "trafficbooster"
                && appKey != "traffictracker"
                && appKey != "facebook-ads-grader"
                && appKey != "benchmark-hero"
                && appKey != "growth-hero"
                && appKey != "product-description-wizard"
                )
                return true;

            return false;
        }

        public static AppEntity SetAppEntityByAppUrlKey(ExternalApp externalApp)
        {
            AppEntity app = new AppEntity();

            app.AppKey = externalApp.AppUrlKey;
            app.AppID = externalApp.ID;
            app.EditLink = string.Format("/manage/apps/{0}", externalApp.AppUrlKey);
            app.AppName = externalApp.AppName;
            app.IsExternal = true;
            app.ShowChoosePlanNowButton = (externalApp.ShowChoosePlanNowButton ?? 0) == 1 ? true : false;
            return app;
        }

        public static ExternalApp GetExternalAppByAppUrlKey(string appUrlKey)
        {
            var app = DataHelper.GetExternalAppsFromCache().Where(a => a.AppUrlKey == appUrlKey);
            if (app.Any())
            {
                return app.Single();
            }
            return null;
        }

        public static ExternalApp GetExternalAppSettings(int id)
        {
            var app = DataHelper.GetExternalAppsFromCache().Where(a => a.ID == id);
            if (app.Any())
            {
                return app.Single();
            }
            return null;
        }

        public static ShopApp GetExternalShopAppSettings(int shopID, int externalAppID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = db.ShopApps.Where(a => a.ShopID == shopID && a.AppTypeID == externalAppID);
            if (app.Any())
            {
                return app.Single();
            }
            return null;
        }

        public static List<ShopApp> GetRemovedApps(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            int removedShopID = (shopID * -1);
            List<ShopApp> removed_apps = db.ShopApps.Where(t => t.ShopID == removedShopID).ToList();

            return removed_apps;
        }

        public static List<ShopApp> GetRemovedApps(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var apps = db.ShopApps.Where(a => a.ShopID == shopID && a.AppTypeID == appID).ToList();
            return apps;
        }

        public static ShopApp GetAppSettings(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = db.ShopApps.Where(a => a.ShopID == shopID && a.AppTypeID == appID);
            if (app.Any())
            {
                return app.Single();
            }
            return null;
        }

        public static string GetActiveAppsCommaDelimited(List<ShopApp> apps)
        {
            //StoreYaEntities db = DataHelper.GetStoreYaEntities();

            if (apps != null && apps.Where(s => s.PaymentStatus.HasValue && s.PaymentStatus == (int)AppPaymentStatus.Active).Any())
            {
                List<string> inst_apps = new List<string>();
                foreach (var item in apps)
                {
                    string appName = Enum.GetName(typeof(AppTypes), item.AppTypeID);

                    inst_apps.Add(appName);

                    //if (item.AppTypeID == (int)AppTypes.CouponPop)
                    //{
                    //    var query = db.CouponPops.Where(c => c.ShopID == item.ShopID && (c.Status ?? 0) > 0 && (c.Status ?? 0) != 100).OrderByDescending(c => c.ID);
                    //    if (query != null && query.Count() > 0 && !inst_apps.Where(x => x == appName).Any())
                    //    {
                    //        inst_apps.Add(appName);
                    //    }
                    //}
                    //else if (item.AppTypeID == (int)AppTypes.ExitPop)
                    //{
                    //    var query = db.ExitPops.Where(c => c.ShopID == item.ShopID && (c.Status ?? 0) > 0 && (c.Status ?? 0) != 100).OrderByDescending(c => c.ID);
                    //    if (query != null && query.Count() > 0 && !inst_apps.Where(x => x == appName).Any())
                    //    {
                    //        inst_apps.Add(appName);
                    //    }
                    //}
                    //else if (item.AppTypeID == (int)AppTypes.FacebookShop)
                    //{
                    //    var shop = db.Shops.Where(c => c.ID == item.ShopID).SingleOrDefault();
                    //    if (shop != null && shop.IsPublished.HasValue && shop.IsPublished == 1 && !inst_apps.Where(x => x == appName).Any())
                    //    {
                    //        inst_apps.Add(appName);
                    //    }
                    //}
                    //else
                    //{
                    //    if (!inst_apps.Where(x => x == appName).Any())
                    //        inst_apps.Add(appName);
                    //}
                }
                return string.Join(",", inst_apps.ToArray());
            }
            return null;
        }

        public static List<string> GetInstalledAppsNames(List<ShopApp> apps)
        {
            List<string> inst_apps = new List<string>();

            if (apps != null && apps.Where(s => s.RegStatus.HasValue && s.RegStatus == (int)AppRegStatus.Installed).Any())
            {
                foreach (var item in apps.Where(s => s.RegStatus.HasValue && s.RegStatus == (int)AppRegStatus.Installed))
                {
                    string appName = Enum.GetName(typeof(AppTypes), item.AppTypeID);

                    if (!inst_apps.Where(x => x == appName).Any())
                    {
                        inst_apps.Add(appName);
                    }
                }
            }
            return null;
        }

        public static string GetInstalledAppsCommaDelimited(List<ShopApp> apps)
        {
            List<string> inst_apps = GetInstalledAppsNames(apps);
            if (inst_apps != null && inst_apps.Count > 0)
                return string.Join(",", inst_apps.ToArray());

            return null;
        }

        public static string GetRemovedAppsCommaDelimited(int shopID)
        {
            List<string> removed_apps = new List<string>();
            List<ShopApp> removedApps = AppStoreManager.GetRemovedApps(shopID);
            if (removedApps != null && removedApps.Count > 0)
            {
                foreach (var item in removedApps)
                {
                    string appName = Enum.GetName(typeof(AppTypes), item.AppTypeID);

                    if (!removed_apps.Where(x => x == appName).Any())
                    {
                        removed_apps.Add(appName);
                    }
                }
                return string.Join(",", removed_apps.ToArray());
            }
            return null;
        }

        public static List<ShopApp> GetShopApps(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var apps = db.ShopApps.Where(a => a.ShopID == shopID);
            if (apps.Any())
            {
                return apps.ToList();
            }
            return null;
        }
        public static bool IsAppInstalled(int shopID, AppTypes appType)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            return db.ShopApps.Any(a => a.ShopID == shopID && a.AppTypeID == (int)appType);
        }

        public static bool HasSubscriptionWithManualTreatment(User user, List<ShopApp> userPaidApps)
        {
            if (userPaidApps != null && userPaidApps.Where(a => a.SubscriptionID.HasValue && a.SubscriptionID == (int)SpecificSubscriptionID.SetManuallyByAdmin).Any())
            {
                return true;
            }
            return false;
        }

        public static bool IsLastAppInSubscriptionNew(List<ShopApp> userPaidApps, int shopID, int appID, int subscriptionID)
        {
            if (userPaidApps != null)
            {
                var appToRemove = userPaidApps.Where(a => a.ShopID == shopID && a.AppTypeID == appID).SingleOrDefault();
                if (appToRemove != null)
                {
                    List<ShopApp> otherPaidApps = userPaidApps;
                    otherPaidApps.Remove(appToRemove);

                    if (otherPaidApps != null)
                    {
                        foreach (var app in otherPaidApps)
                        {
                            if (app.SubscriptionID.HasValue && app.SubscriptionID == subscriptionID)
                            {
                                return false;
                            }
                        }
                    }
                }
            }

            return true;
        }

        public static bool HasMoreThenOneAppOnSubscription(List<ShopApp> userPaidApps, int shopID, int appID, int subscriptionID)
        {
            if (userPaidApps != null)
            {
                var shopPaidApps = userPaidApps.Where(s => s.ShopID == shopID).ToList();

                if (shopPaidApps != null)
                {
                    foreach (var app in shopPaidApps)
                    {
                        if (app.SubscriptionID.HasValue && app.SubscriptionID == subscriptionID
                            && app.AppTypeID != appID)
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        public static List<ShopApp> GetPaidShopApps(User user)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            List<ShopApp> userPaidApps = null;

            List<Shop> userActiveShops = db.Shops.Where(s => s.UserID == user.ID && (!s.IsDisabled.HasValue || s.IsDisabled != 1)).ToList();

            if (userActiveShops != null)
            {
                foreach (Shop shop in userActiveShops)
                {
                    List<ShopApp> shopPaidApps = GetPaidShopApps(shop.ID);
                    if (shopPaidApps != null)
                    {
                        if (userPaidApps == null)
                            userPaidApps = new List<ShopApp>();

                        userPaidApps.AddRange(shopPaidApps);
                    }
                }
            }

            return userPaidApps;
        }
        public static List<ShopApp> GetPaidShopApps(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var apps = db.ShopApps.Where(a => a.ShopID == shopID);
            var paidApps = apps.Where(p => p.PaymentPlan.HasValue && p.PaymentPlan > 0);
            if (paidApps.Any())
            {
                return paidApps.ToList();
            }
            return null;
        }

        public static List<ShopApp> GetFreeShopApps(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<ShopApp> freeApps = new List<ShopApp>();
            var apps = db.ShopApps.Where(a => a.ShopID == shopID);
            foreach (var shopApp in apps)
            {
                if (IsFreeApp(shopApp))
                {
                    freeApps.Add(shopApp);
                }
            }
            return null;
        }

        public static bool HasApps(int shopId)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var apps = db.ShopApps.Where(a => a.ShopID == shopId);
            return (apps.ToList().Count > 0);
            //var paidApps = GetPaidShopApps(shopId);
            //var freeApps = GetFreeShopApps(shopId);
            //if (paidApps == null && freeApps == null)
            //{
            //    return false;
            //}
            //if (paidApps != null && paidApps.Count() > 0)
            //{
            //    return true;
            //}
            //if (freeApps != null && freeApps.Count() > 0)
            //{
            //    return true;
            //}
            //return false;
        }
        public static void DowngradeAllSubscriptionApps(int shopID, string subscriptionID)
        {
            //Check if the cancelled subscription is connected to the APP
            List<ShopApp> apps = SubscriptionManager.GetAppsByBsSubscription(shopID, subscriptionID);
            foreach (var app in apps)
            {
                if (app.PaymentStatus != (int)AppPaymentStatus.Disabled)
                {
                    DisableShopApp(shopID, app.AppTypeID);
                }
            }
        }

        public static void DowngradeAllShopApps(int shopID)
        {
            List<ShopApp> apps = AppStoreManager.GetShopApps(shopID);
            if (apps != null)
            {
                foreach (var app in apps)
                {
                    //if (app.AppTypeID == (int)AppTypes.FacebookShop)
                    //{
                    //    if (app.PaymentPlan > 0)
                    //    {
                    //        ShopManager.DowngradeShopImmediately(shopID);
                    //    }
                    //}

                    DisableShopApp(shopID, app.AppTypeID);
                }
            }
            else
            {
                Log4NetLogger.Info("There are no shop apps to downgrade.", shopID);
            }
        }

        public static void DisableShopApp(int shopID, int appID)
        {
            Log4NetLogger.Info(string.Format("Downgarding {0} shop app...", GetAppName(appID)), shopID);

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            User user = db.Users.Where(u => u.ID == shop.UserID).SingleOrDefault();

            switch ((AppTypes)appID)
            {
                case AppTypes.FacebookShop:
                    ShopManager.DowngradeShopImmediately(shopID);
                    break;

                case AppTypes.CouponPop:
                    DisableCpApp(shopID);
                    break;

                case AppTypes.ExitPop:
                    DisableEpApp(shopID);
                    break;

                case AppTypes.RFF:
                    DisableRffApp(shopID);
                    break;

                //case AppTypes.PoweredBanner:
                //    DisableBannerApp(shopID);
                //    break;

                case AppTypes.LikeBox:
                    DisableLikeBoxApp(shopID);
                    break;

                case AppTypes.TrafficBooster:
                    SendEmailBigCancellationToForward(shop, user, db);
                    DisableTrafficBoosterApp(shopID);
                    break;

                case AppTypes.TrafficTracker:
                    DisableTrafficTrackerApp(shopID);
                    break;

                case AppTypes.FacebookAdsGrader:
                    //DisableFacebookAdsGraderApp(shopID); //??
                    break;

                case AppTypes.BenchmarkHero:
                    DisableBenchmarkHeroApp(shopID); //??
                    break;
                case AppTypes.GrowthHero:
                    DisableGrowthHeroApp(shopID); //??
                    break;
                case AppTypes.ProductDescriber:
                    DisableProductDescriberApp(shopID); //??
                    break;
            }

            ShopApp appToDisable = null;
            //if (IsExternalApp(appID))
            //{
            //    DeleteExternalScript(shopID, appID);

            //    appToDisable = GetExternalShopAppSettings(shopID, appID);
            //}
            //else
            //{
            //    appToDisable = GetAppSettings(shopID, appID);
            //}

            appToDisable = GetAppSettings(shopID, appID);

            if (appToDisable != null)
            {
                string messageAboutPaymentPlan = null;
                if (appToDisable.PaymentPlan.HasValue && appToDisable.PaymentPlan > 0)
                {
                    if (appToDisable.SubscriptionID == (int)SpecificSubscriptionID.Wix)
                    {
                        var wixConnectedSite = db.WixConnectedSites.Where(s => s.ShopID == shopID).SingleOrDefault();
                        if (wixConnectedSite != null)
                        {
                            wixConnectedSite.WixPlanID = null;
                        }
                    }

                    appToDisable.PaymentStatus = (int)AppPaymentStatus.Disabled;
                    messageAboutPaymentPlan = string.Format("A payment plan: {0} changed to null", appToDisable.PaymentPlan);
                    appToDisable.PaymentPlan = null;
                    appToDisable.AppPrice = null;
                    appToDisable.SubscriptionID = null;
                    appToDisable.PaymentPlanUpdatedAt = DateTime.Now;
                    appToDisable.UpdatedAt = DateTime.Now;
                    Log4NetLogger.Info(string.Format("{0} shop app was disabled. {1}", GetAppKeyForView(appID), messageAboutPaymentPlan), shopID);
                }
                else
                {
                    appToDisable.UpdatedAt = DateTime.Now;
                    appToDisable.PaymentStatus = (int)AppPaymentStatus.Disabled;
                    Log4NetLogger.Info(string.Format("Not paid {0} shop app was disabled", GetAppKeyForView(appID)), shopID);
                }
                db.SaveChanges();
            }

            if (IsPaidApp(appID))
            {
                //change permissions
                ShopManager.ResetShopPermissionsByInstalledApps(shop.UserID ?? 0, shop.ID, Permissions.PlanFreemium);
                if (user != null && user.AgencyID.HasValue && user.AgencyID > 0)
                {
                    //get other shop admins
                    List<ShopAdmin> admins = ShopManager.GetShopAdmins(shopID);
                    foreach (var admin in admins)
                    {
                        ShopManager.ResetShopPermissionsByInstalledApps(admin.UserID, shopID, Permissions.PlanFreemium);
                    }
                }
            }

            SystemEventHelper.Add(user.ID, shopID, appID, SystemEventTypes.AppStore, SystemEventActions.AppDisabled, null, null);

            var appName = AppStoreManager.GetAppByID(appID).AppName;
            Eventer.ReportEvent(shopID, EventCategory.AppStore, BackgroundEvents.app_disabled.ToString(), appName);
        }

        private static void DisableBenchmarkHeroApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Benchmark benchmark = db.Benchmarks.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (benchmark != null)
            {
                benchmark.Status = (int)BenchmarkStatuses.Canceled;
                db.SaveChanges();
            }
        }

        private static void DisableGrowthHeroApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var hero = db.GrowthHeros.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (hero != null)
            {
                hero.Status = (int)BenchmarkStatuses.Canceled;
                db.SaveChanges();
            }
        }

        private static void DisableProductDescriberApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var productDescribers = db.ProductDescribers.Where(l => l.ShopId == shopID).FirstOrDefault();
            if (productDescribers != null)
            {
                productDescribers.Status = (int)ProductDescriberAppStatus.Canceled;
                db.SaveChanges();
            }
        }
        public static void EnableTrafficBoosterApp(int shopID, bool shopIdisnotNegative = false)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            int tbshopId = -shopID;
            if (shopIdisnotNegative)
            {
                tbshopId = shopID;
            }
            TrafficBooster trafficBooster = db.TrafficBoosters.Where(l => l.ShopID == tbshopId).FirstOrDefault();
            if (trafficBooster != null)
            {
                Log4NetLogger.InfoWithDB("EnableTrafficBoosterApp: TB App status was changed to ACTIVE", null, shopID);
                trafficBooster.AppStatus = TB_APP_STATUS.ACTIVE.GetHashCode();
                trafficBooster.Status = (byte)TB_AW_STATUS.RUNNING;
                trafficBooster.CancelledAt = null;
                trafficBooster.ShopID = shopID;
                db.SaveChanges();
            }
        }

        public static void DisableTrafficBoosterApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //disable LikeBox
            TrafficBooster trafficBooster = db.TrafficBoosters.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (TrafficBoostersDbHelper.UpdateTbAppStatus(shopID, TB_APP_STATUS.CANCELED))
            {
                TrafficBoostersDbHelper.UpdateTbStatus(shopID, TB_AW_STATUS.CANCELED);
                TbChannelManager.DisableChannelsInDb(shopID);
            }
            try
            {
                if (IsShopify(shopID))
                {
                    var api = ShopifyConnector.GetShopifyApiClient(shopID, (int)AppTypes.TrafficBooster);
                    api.UninstallStoryaApp();
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteErrorWithDB("Failed To uninstall Storeya Shopify app", ex, shopID);
            }
        }


        private static void DisableFacebookAdsGraderApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            FbGrader ttApp = db.FbGraders.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (ttApp != null)
            {
                //db.SaveChanges();
            }
        }

        private static void DisableTrafficTrackerApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            TTApp ttApp = db.TTApps.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (ttApp != null)
            {
                ttApp.Status = (byte)TB_AW_STATUS.CANCELED;
                //trafficBooster.CancelledAt = DateTime.Now;
                db.SaveChanges();
            }
        }

        private static DateTime GetNextBillingDate(DateTime paymentDate)
        {
            DateTime today = DateTime.Today;

            DateTime nextBillingDate = (DateTime)paymentDate;
            while (nextBillingDate < today)
            {
                nextBillingDate = nextBillingDate.AddMonths(1);
            }
            return nextBillingDate;
        }

        private static void DisableLikeBoxApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //disable LikeBox
            LikeBox likeBox = db.LikeBoxes.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (likeBox != null)
            {
                likeBox.Status = -1;
                db.SaveChanges();
            }
        }

        private static void DisableBannerApp(int shopID)
        {
            //nothing to do 

            //StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //disable all Rffs
            //foreach (var item in db.Banners.Where(o => o.ShopID == shopID).ToList())
            //{
            //    item.IsPaid = 0;
            //}
            //db.SaveChanges();
        }

        public static bool IsPaidApp(int appType)
        {
            if (appType == (int)AppTypes.CouponPop
                || appType == (int)AppTypes.ExitPop
                || appType == (int)AppTypes.RFF
                || appType == (int)AppTypes.FacebookShop
                //|| appType == (int)AppTypes.PoweredBanner
                || appType == (int)AppTypes.TrafficBooster)
                return true;
            return false;
        }

        private static void DisableRffApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //disable all Rffs
            foreach (var item in db.RffOffers.Where(o => o.ShopID == shopID).ToList())
            {
                item.Status = 0;
            }
            db.SaveChanges();
        }

        private static void DisableEpApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //disable all Rffs
            foreach (var item in db.ExitPops.Where(o => o.ShopID == shopID).ToList())
            {
                item.Status = 0;
            }
            db.SaveChanges();
        }

        private static void DisableCpApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //disable all Rffs
            foreach (var item in db.CouponPops.Where(o => o.ShopID == shopID).ToList())
            {
                item.Status = 0;
            }
            db.SaveChanges();
        }

        public static void SetAppAsPaid(int subscriptionID, int shopID, int appID, int planID, int contractID, int? chargeID = null, double? contractPrice = null, int agreeID = 0, bool updateLastPaymentDate = true, string currency = "USD", bool convertNonUSDPrice = true)
        {
            int contractMethod = BluesnapHelper.ReverseMethodID(contractID, appID);
            ContractSettings contract = BluesnapHelper.GetContract(planID, contractMethod, appID);
            double price = contract.Price;
            if (contractPrice != null)
            {
                price = contractPrice.Value;
            }
            if (currency != "USD")
            {
                if (convertNonUSDPrice)
                {
                    decimal connvertedPrice = CurrencyHelper.GetConvertedValueInUSD(shopID, currency, (decimal)price);
                    Log4NetLogger.Info(string.Format("Price Was Converted From :{0} To :{1} base currency:{2}", price, connvertedPrice, currency), shopID);
                    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "SetAppAsPaid was done in a " + currency + " currency.", EmailHelper.GetBoLinkHref(shopID, "PaymentsHistory") + "<br/>Please review the shop payment since payment was done in a none USD currency.");
                    price = (double)connvertedPrice;
                }
                else
                {
                    Log4NetLogger.Info(string.Format("Price: {0}  Was recived in none USD currency {1} - No Conversion was done", price, currency), shopID);
                    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "SetAppAsPaid was done in a " + currency + " currency.", EmailHelper.GetBoLinkHref(shopID, "PaymentsHistory") + "<br/>Please review the shop payment since payment was done in a none USD currency. and was not converted.");
                }
            }
            //UpdateShopAppdWithSubscriptionData(shopID, appID, subscriptionID, planID, price);

            if (appID == AppTypes.TrafficBooster.GetHashCode() && ConfigHelper.GetBoolValue("UsePaymentsWithoutAgreeLogic") == false)
            {
                //Traffic boster purchased
                UpdateTbWithSubscriptionData(shopID, appID, subscriptionID, planID, price, agreeID, updateLastPaymentDate);
            }
            else
            {
                UpdateShopAppdWithSubscriptionData(shopID, appID, subscriptionID, planID, price);
            }


        }

        //internal static void SetAppAsPaid(int subscriptionID, int shopID, int appID, int planID, int contractID, AbstractPaymentAdapter paymentAdapter, int chargeID)
        //{
        //    int contractMethod = paymentAdapter.ReverseMethodID(contractID, appID);
        //    //ContractSettings contract = paymentAdapter.GetContract(planID, contractMethod, appID);
        //    ContractSettings contract = BluesnapHelper.GetContract(planID, contractMethod, appID);
        //    UpdateShopAppdWithSubscriptionData(shopID, appID, subscriptionID, planID, contract.Price, chargeID);
        //}




        public static void UpdateShopAppdWithSubscriptionData(int shopID, int appID, int subscriptionID, int planID, double appPrice)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopApp appToChange = AppStoreManager.GetAppSettings(shopID, appID);

            //List<ShopApp> cancelledApps = GetRemovedApps(shopID * -1, appID);

            if (appToChange.SubscriptionID != null && appToChange.SubscriptionID != subscriptionID && appToChange.SubscriptionID > 0)
            {
                //changing existing subscription ID - the old one probably should be cancelled.

                //TODO: remove when agree changes goes live
                if (appID == (int)AppTypes.TrafficBooster) // that is an upgrade
                {
                    Shop shop = db.Shops.Where(x => x.ID == shopID).SingleOrDefault();
                    User user = db.Users.Where(x => x.ID == shop.UserID).SingleOrDefault();

                    //report fake reccurring event for ReactivatePaidApps
                    SystemEventHelper.Add(user.ID, shopID, appID, SystemEventTypes.IPNCalls, SystemEventActions.Recurring, string.Format("PlanID_{0}_Fake_Recurring", planID), Convert.ToDecimal(appPrice));

                    if (appToChange.SubscriptionID.Value > 0)
                    {
                        ShopSubscription storeyaSubscriptionToCancel = SubscriptionManager.GetShopSubscription(appToChange.SubscriptionID.Value);

                        if (storeyaSubscriptionToCancel.PaymentMethod.ToLower() == "paypal") //In case this is paypal subscription:
                        {
                            //Cancel old subscriptions 
                            bool isCancelled = SubscriptionManager.UpdateSubscriptionStatus(storeyaSubscriptionToCancel.BlueSnapSubscriptionID.ToString(), BluesnapSubscriptionStatus.Cancelled, shop.ID);
                            if (isCancelled)
                            {
                                string emailHtml = EmailManager.GetEmailAboutPayPalSubscriptionCancellation(user);
                                EmailHelper.SendEmail(user.Email, "Regarding your Traffic Booster plan", emailHtml, null, null, true);

                                Log4NetLogger.Info(string.Format("The paypal subscription {0} was cancelled after a new subscription was registered.", storeyaSubscriptionToCancel.BlueSnapSubscriptionID), shop.ID);
                            }
                        }
                        else //In case of BS subscription:
                        {
                            //Change status to Cancel On Renewal + Set next billing date as far as possible
                            bool isfrozen = SubscriptionManager.FreezeOrRenewSubcription(storeyaSubscriptionToCancel.BlueSnapSubscriptionID.ToString(), false, shop.ID);
                            if (isfrozen)
                            {
                                Log4NetLogger.Info(string.Format("The subscription {0} was frowsen after a new subscription was registered.", storeyaSubscriptionToCancel.BlueSnapSubscriptionID), shop.ID);
                            }
                        }
                    }

                    string body = string.Format("The subscription ID of app {0} is changeing from {1} to {2}. <br/> https://www.storeya.com/admin/shopsubscriptions?shopid={3} ", appID, appToChange.SubscriptionID, subscriptionID, shopID);
                    Log4NetLogger.Info(body, shopID);
                    //EmailHelper.SendEmail("<EMAIL>", "AUDIT REQUIRED: Changing shop subscription ID. Shop ID - " + shopID, body, null, null, true, "SYSTEM");

                }
            }

            appToChange.SubscriptionID = subscriptionID;
            appToChange.PaymentPlan = planID;
            appToChange.PaymentPlanUpdatedAt = DateTime.Now;
            appToChange.AppPrice = appPrice.ToString();
            appToChange.PaymentStatus = (int)AppPaymentStatus.Active;

            if (appToChange.RegStatus == (int)AppRegStatus.Old_NotPaid)
                appToChange.RegStatus = (int)AppRegStatus.Installed;

            appToChange.UpdatedAt = DateTime.Now;
            //appToChange.ActiveChargeID = chargeID;

            db.SaveChanges();
            Log4NetLogger.Info(string.Format("{0} shop app record was updated with: subscriptionID: {1}, planID: {2}", GetAppName(appID), subscriptionID, planID), shopID);

            if (appID == (int)AppTypes.FacebookShop)
            {
                ShopManager.SetShopPaymentPlan(shopID, planID);
                ShopManager.ResetInventoryIfNeeded(shopID);
            }
            else if (appID == (int)AppTypes.CouponPop)
            {
                //enable disabled widget if needed
                EnableDisabledWidgetIfNeeded(shopID, appID);
            }
            else if (appID == (int)AppTypes.TrafficBooster)
            {
                TrafficBoostersDbHelper.SetPurchasedAmount(shopID, planID, appPrice);
                if (TbCampaignsManager.InitCampaigns(shopID))
                {
                    User user = db.Users.Where(x => x.ID == appToChange.UserID).SingleOrDefault();
                    LauncherHelper.InitCommand(shopID, user, "CREATE_ADW_ACCOUNT");
                }

                if (planID != (int)PlanTypes.CustomPlan)
                {
                    AddEventToIntercom(shopID, "tb_purchased_first_time_plan_" + planID);
                }
            }
        }


        private static void EnableDisabledWidgetIfNeeded(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            if (appID == (int)AppTypes.CouponPop)
            {
                List<CouponPop> widgets = db.CouponPops.Where(w => w.ShopID == shopID && (w.Status ?? 0) < 100).ToList();
                if (widgets != null && widgets.Count > 0)
                {
                    if (!widgets.Where(w => (w.Status ?? 0) == 1).Any())
                    {
                        //enable last widget
                        CouponPop widget = widgets.Where(w => (w.Status ?? 0) == 0).OrderByDescending(w => w.InsertedAt).FirstOrDefault();
                        if (widget != null)
                        {
                            widget.Status = 1;
                            db.SaveChanges();
                        }
                    }
                }

                // who disabled after trial ended and they were removed (status was set to -10 ), if they buy they should back to the list
                ReportSubscriber subscriber = db.ReportSubscribers.Where(s => s.ShopID == shopID && s.ReportType == 1 && (s.Status ?? 10) == -10).FirstOrDefault();
                if (subscriber != null)
                {
                    subscriber.Status = 10;
                    db.SaveChanges();
                }
            }
        }

        //public static void UpdateAppSubscription(SubscriptionChange sub)
        //{
        //    bool isUpdated = false;
        //    isUpdated = SubscriptionManager.UpdateBlueSnap(sub);

        //    if (isUpdated)
        //    {
        //        //update subscription         
        //        bool isUpdatedInDB_subscription = SubscriptionManager.UpdateInDB(sub.ShopSubscription.ID, sub.PlanID.Value, sub.TotalSubscriptionCharge.ToString(), sub.ShopID, sub.AppID);
        //        if (isUpdatedInDB_subscription)
        //        {
        //            Log4NetLogger.Info(string.Format("BlueSnapSubscription {0} was successfully updated in DB with overrided contract charge.", sub.ShopSubscription.BlueSnapSubscriptionID), sub.ShopID);
        //        }

        //        //update shopapp that was upgraded
        //        if ((!sub.IsRemovingApp))
        //        {
        //            UpdateShopAppdWithSubscriptionData(sub.ShopID, sub.AppID, sub.ShopSubscription.ID, sub.PlanID.Value, sub.Contract.Price);
        //        }
        //    }
        //    else
        //    {
        //        Log4NetLogger.Info(string.Format("BlueSnapSubscription {0} was NOT updated at Plimus API to planID: {1} and new charge: {2} for his {3} shopApp", sub.ShopSubscription.BlueSnapSubscriptionID.ToString(), sub.PlanID, sub.TotalSubscriptionCharge, AppStoreManager.GetAppByID(sub.AppID).AppName), sub.ShopID);
        //    }

        //    string body = string.Format("View shop logs: {0}/admin/logs/?shopid={1}, View blueSnapSubscription: {0}/admin/SubscriptionDetails?shopID={1}&blueSnapSubscriptionID={2}", HttpHelper.GetCurrentDomain(), sub.ShopID, sub.ShopSubscription.BlueSnapSubscriptionID);
        //    string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
        //    EmailHelper.SendEmail(email, string.Format("Updating BlueSnapSubscription: change type: {0}", ((SubscriptionChangeTypes)sub.ChangeType).ToString()), body);

        //}

        public static string PayPalOneChanrgePreAction(RequiredActions changes)
        {
            var payment = changes.RequiredOneTimePayment;
            BlueSnapApi api = new BlueSnapApi();
            BluesnapShopper bluesnapShopper = api.RetreiveShopperObject(payment.ShopperID.ToString());
            if (bluesnapShopper == null)
            {
                throw new Exception("Cannot Find Bluesnap Shopper With Shopperid:" + payment.ShopperID.ToString());
            }
            // Redirect to payPal to create onetime charge
            string ppReturnUrl = changes.RequiredOneTimePayment.PayPalExtention.ReturnUrl;
            string ppCancelUrl = changes.RequiredOneTimePayment.PayPalExtention.CancelUrl;
            string remoteHost = changes.RequiredOneTimePayment.PayPalExtention.RemoteHost;
            string userAgent = changes.RequiredOneTimePayment.PayPalExtention.UserAgent;

            BlueSnapApiWrapper apiWrapper = new BlueSnapApiWrapper();
            string ip = apiWrapper.GetValidIp(bluesnapShopper.ShopperContactInfo.State, bluesnapShopper.ShopperContactInfo.Country);// api.BluesnapConfiguration.BlueSnapClientIP;
            string fraudSessionId = apiWrapper.CreateFraudID();
            int trialPeriod = 0;
            string paypalUrl = apiWrapper.CreateShoppingContext(changes.ShopID, changes.AppID, changes.PlanID, ip, remoteHost, userAgent, fraudSessionId, bluesnapShopper.ShopperContactInfo.FirstName,
               bluesnapShopper.ShopperContactInfo.LastName, bluesnapShopper.ShopperContactInfo.Email, ppCancelUrl, ppReturnUrl, payment.ContractID.Value.ToString(), payment.Amount.ToString(), payment.Currency, trialPeriod, payment.Amount.ToString(), payment.Amount.ToString(),
               false, 0, changes.AgreeID, bluesnapShopper.ShopperContactInfo.State, bluesnapShopper.ShopperContactInfo.Country, payment.AmountWithTax.ToString()
                );
            return paypalUrl;
        }
        //public static bool UpdateAppSubscription(RequiredActions change, bool isPayPal = false, bool updateLastPaymentDate = true)
        //{
        //    return UpdateAppSubscription(change, out int? onetimePaymentOrderID, isPayPal, updateLastPaymentDate);
        //}
        public static bool UpdateAppSubscription(RequiredActions change, out int? onetimePaymentOrderID, out string error, bool isPayPal = false, bool updateLastPaymentDate = true)
        {
            bool successed = false;
            SubscriptionChangeResult response = SubscriptionManager.UpdateBlueSnap2(change, out error, isPayPal);

            onetimePaymentOrderID = response.OneTimePaymentOrderId;
            if (response.IsUpdated)
            {
                int subscriptionID = response.SubscriptionData.SubscriptionID;
                //update subscription
                if (subscriptionID == 0)
                {
                    BluesnapSubscription sub = SubscriptionManager.GetSubscriptionFromBlueSnap(response.SubscriptionData.BsSubscriptionID, response.ShopID);
                    //new subscription added
                    subscriptionID = SubscriptionManager.SaveToDB(change.AppID, change.ShopID, change.PlanID,
                        response.SubscriptionData.BsSubscriptionID.ToString(),
                        change.ContractID.ToString(),
                        response.SubscriptionData.BsShopperID,
                        response.SubscriptionData.BsCurrency,
                        response.TotalSubscriptionCharge.ToString(),
                        sub == null ? null : sub.PaymentMethod,
                        change.AgreeID == 0 ? null : change.AgreeID.ToString()
                        );
                    Log4NetLogger.Info(string.Format("UpdateAppSubscription: new subscription created. BsSubscriptionID - {0} ,Total - {1}.", response.SubscriptionData.BsSubscriptionID.ToString(), response.TotalSubscriptionCharge), change.ShopID);
                }
                else
                {
                    //existing subscription
                    SubscriptionManager.Update(change.ShopID, change.AppID, change.AgreeID, response.SubscriptionData.SubscriptionID, response.TotalSubscriptionCharge);
                    Log4NetLogger.Info(string.Format("UpdateAppSubscription: subscription was updated. StoreYa subscription ID - {0} ,Total - {1}.", response.SubscriptionData.SubscriptionID.ToString(), response.TotalSubscriptionCharge), change.ShopID);
                }

                //update shopapps
                if (!change.IsRemoveApp)
                {

                    AppStoreManager.SetAppAsPaid(subscriptionID, change.ShopID, change.AppID, change.PlanID, change.ContractID, null, response.TotalSubscriptionCharge, change.AgreeID, updateLastPaymentDate, response.SubscriptionData.BsCurrency, false);
                    Log4NetLogger.Info(string.Format("UpdateAppSubscription: SetAppAsPaid. StoreYa subscription ID - {0}.", subscriptionID), change.ShopID);
                }
                successed = true;
            }
            else
            {
                Log4NetLogger.Info(string.Format("BlueSnapSubscription {0} was NOT updated at Plimus API to planID: {1} and new charge: {2} for his {3} shopApp", response.SubscriptionData?.BsSubscriptionID?.ToString(), change.PlanID, response.TotalSubscriptionCharge, AppStoreManager.GetAppByID(change.AppID).AppName), change.ShopID);
                successed = false;
            }

            //string body = string.Format("View shop logs: {0}/admin/logs/?shopid={1}, View blueSnapSubscription: {0}/admin/SubscriptionDetails?shopID={1}&blueSnapSubscriptionID={2}. Shop App: {3}, ChangeType: {4}, amount of new plan: {5}", HttpHelper.GetCurrentDomain(), change.ShopID, response.SubscriptionData.BsSubscriptionID, GetAppName(change.AppID), response.ChangeType.ToString(), response.TotalSubscriptionCharge);
            //string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
            //EmailHelper.SendEmail(email, "BlueSnapSubscription changed - shop " + change.ShopID, body, null, null, true, "SYSTEM");




            return successed;
        }


        //private static int? GetPlanIdToBeDowngardedWith(int shopID, int appID)
        //{
        //    int? planID = 0;
        //    double max = 0;

        //    List<ShopApp> shopApps = AppStoreManager.GetShopApps(shopID);
        //    var otherPaidApps = shopApps.Where(sa => sa.AppTypeID != appID && sa.PaymentStatus == (int)AppPaymentStatus.Active && !string.IsNullOrEmpty(sa.AppPrice)).ToList();
        //    if (otherPaidApps != null && otherPaidApps.Count > 0)
        //    {
        //        foreach (var shopApp in otherPaidApps)
        //        {
        //            double appPrice;
        //            if (double.TryParse(shopApp.AppPrice, out appPrice))
        //            {
        //                if (appPrice > max)
        //                {
        //                    max = appPrice;
        //                    planID = shopApp.PaymentPlan;
        //                }
        //            }
        //        }
        //    }
        //    return planID;
        //}

        public static double GetAppsPriceForSubscriptionExcludingProvidedAppId(int shopID, int appID)
        {
            double appsPrice = 0;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<ShopApp> shopApps = AppStoreManager.GetShopApps(shopID);
            var otherPaidApps = shopApps.Where(sa => sa.AppTypeID != appID && sa.PaymentStatus == (int)AppPaymentStatus.Active && !string.IsNullOrEmpty(sa.AppPrice)).ToList();
            if (otherPaidApps != null && otherPaidApps.Count > 0)
            {
                foreach (var shopApp in otherPaidApps)
                {
                    double appPrice;
                    if (double.TryParse(shopApp.AppPrice, out appPrice))
                    {
                        appsPrice += appPrice;
                    }
                }
            }

            return appsPrice;
        }

        public static bool IsWebApp(int appID)
        {
            if (appID == (int)AppTypes.CouponPop
                || appID == (int)AppTypes.ExitPop
                || appID == (int)AppTypes.RFF
                //|| appID == (int)AppTypes.PoweredBanner
                || appID == (int)AppTypes.TrafficBooster)
            {
                return true;
            }
            return false;
        }

        public static bool IsFbShopVeteran(int planID)
        {
            return (planID == (int)PlanTypes.EconomyVeteran ||
                    planID == (int)PlanTypes.BusinessVeteran ||
                    planID == (int)PlanTypes.FirstClassVeteran ||
                    planID == (int)PlanTypes.PrivateJetVeteran);
        }

        public static void DeleteExternalScript(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<ExtAppsScript> extAppsScripts = db.ExtAppsScripts.Where(r => r.ShopID == shopID && r.AppID == appID).ToList();
            if (extAppsScripts != null && extAppsScripts.Count > 0)
            {
                foreach (var extAppsScript in extAppsScripts)
                {
                    db.ExtAppsScripts.Remove(extAppsScript);
                    db.SaveChanges();

                    Log4NetLogger.Info(string.Format("An ExtAppsScript for {0} shop app was deleted from DB.", AppStoreManager.GetAppByID(appID).AppName), shopID);
                }
            }
        }

        //public static void RemoveExternalApp(int shopID, int appID)
        //{
        //    ShopApp installation = AppStoreManager.GetExternalShopAppSettings(shopID, appID);
        //    if (installation != null)
        //    {
        //        //if paid - care about subscription
        //        if ((installation.PaymentPlan ?? 0) != 0 && SubscriptionManager.UseBlueSnapPayment(shopID))
        //        {
        //            RemoveAppFromBlueSnapSubscription(shopID, appID);
        //        }

        //        DeleteExternalScript(shopID, appID);

        //        if (installation.ShopID != 0)
        //        {
        //            //remove installation record
        //            StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //            installation.ShopID = -installation.ShopID;
        //            //db.ShopApps.Remove(installation);
        //            db.SaveChanges();
        //            Log4NetLogger.Info(string.Format("{0} shop app was deleted from DB.", AppStoreManager.GetAppByID(appID).AppName), shopID);
        //        }
        //    }
        //}

        public static void CancelApp(Shop shop, User user, int appID)
        {
            List<int?> canceleldSubs = new List<int?>();
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopApp shopApp = db.ShopApps.Where(a => a.ShopID == shop.ID && a.AppTypeID == appID).SingleOrDefault();
            if (shopApp != null)
            {
                if (shopApp.PaymentPlan.HasValue
                        && shopApp.PaymentPlan > 0
                        && shopApp.SubscriptionID.HasValue
                        && (SubscriptionManager.IsBsSubscription(shopApp.SubscriptionID)
                        || shopApp.SubscriptionID.Value == (int)SpecificSubscriptionID.SetManuallyByAdmin))
                {
                    List<ShopApp> userPaidApps = AppStoreManager.GetPaidShopApps(user);
                    if (shopApp.HasAgreements.HasValue)
                    {

                        var agreementSub = SubscriptionManager.GetShopAgreementSubscription(shop.ID);
                        if (agreementSub == null)
                        {
                            EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Cancel account need to check agreement subscription is not active- shop " + shop.ID, "CancelApp Shop " + EmailHelper.AdminLinkHref(shop.ID) + " has agremment but no active subscription while trying to cancel app.", null, null, true, "SYSTEM");
                        }
                        else
                        {
                            AccountManager.CancelPaidAppSubscription(agreementSub, user.ID);
                            canceleldSubs.Add(agreementSub.BlueSnapSubscriptionID);
                        }
                        //string body = string.Format("Cancelled the last paid app {0} - https://www.storeya.com/admin/shopdetailsnew/{1}. By User:{3} ({4}) The subscription {2} was cancelled automatically.", appID, shop.ID, shopApp.SubscriptionID, user.Name, user.ID);
                        //if (shopApp.HasAgreements.HasValue)
                        //{
                        //    body = body + " Notice! this app has an agreements";
                        //}
                        //EmailHelper.SendEmail("<EMAIL>", "Paid Shop App cancelation - shop " + shop.ID, body, null, null, true, "SYSTEM");
                    }

                    if (AppStoreManager.IsLastAppInSubscriptionNew(userPaidApps, shop.ID, appID, shopApp.SubscriptionID.Value))
                    {
                        canceleldSubs.Add(AppStoreManager.CancelPaymentForLastAppInSubscription(shop, user, appID));
                    }
                    else if (AppStoreManager.HasMoreThenOneAppOnSubscription(userPaidApps, shop.ID, appID, shopApp.SubscriptionID.Value))//For non TB apps
                    {
                        canceleldSubs.Add(AppStoreManager.RemoveAppFromBlueSnapSubscription(shop.ID, appID));
                    }
                    //Cancel App leftover Subscriptions
                    var shopSubscriptions = SubscriptionManager.GetActiveShopAppSubscriptions(shop.ID, appID);
                    foreach (var shopSubscription in shopSubscriptions)
                    {
                        if (shopSubscription.BlueSnapSubscriptionID.HasValue)
                        {
                            if (!canceleldSubs.Contains(shopSubscription.BlueSnapSubscriptionID))
                            {
                                AccountManager.CancelPaidAppSubscription(shopSubscription, user.ID);
                            }
                        }
                    }
                    // account that was paying us $500 and yielded all time ROI > 3
                    if (appID == (int)AppTypes.TrafficBooster)
                    {
                        SendEmailBigCancellationToForward(shop, user, db);
                    }
                }
                else if (shopApp.PaymentPlan.HasValue
                        && shopApp.PaymentPlan > 0
                        && !shopApp.SubscriptionID.HasValue)
                {
                    string body = string.Format("Cancelling the paid app {0} - https://www.storeya.com/admin/userdetails/{1}. The subscription can not be cancelled automatically, because there is no a subscription value in shop app record. Client's subscription change/cancellation should be treated manually.", appID, user.ID);
                    string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
                    EmailHelper.SendEmail(email, "ACTION REQUIRED: Veteran subscription should be cancelled - shop " + shop.ID, body, null, null, true, "SYSTEM");
                }
                else if (shopApp.PaymentPlan.HasValue
                        && shopApp.PaymentPlan > 0
                        && shopApp.SubscriptionID.HasValue
                        && shopApp.SubscriptionID == (int)SpecificSubscriptionID.Shopify
                    && (shopApp.AppTypeID == (int)AppTypes.CouponPop || shopApp.AppTypeID == (int)AppTypes.ExitPop || shopApp.AppTypeID == (int)AppTypes.FacebookShop))
                {
                    CancelShopifyChargeIfNeeded(shop, user, appID, db);
                }

                RemoveApp(shopApp);

                SystemEventHelper.Add(user.ID, shop.ID, appID, SystemEventTypes.AppStore, SystemEventActions.AppRemoved, null, null);
            }
        }

        public static void SendEmailBigCancellationToForward(Shop shop, User user, StoreYaEntities db)
        {
            TbBigSpender tbBigSpender = db.TbBigSpenders.Where(x => x.ShopID == shop.ID).SingleOrDefault();
            if (tbBigSpender != null && (tbBigSpender.CurrentPeriodROI > 3 || tbBigSpender.PreviousPeriodROI > 3))
            {
                string revenues = "$0";
                Storeya.Core.TrafficBooster existingAppSettings = TrafficBoostersDbHelper.GetSettings(shop.ID);
                if (existingAppSettings != null)
                {
                    try
                    {
                        MoneyObject revenue = TbSettingsHelper.ParseRevenue(existingAppSettings.TotalRevenues, shop.ID, true);
                        string totalRevenuesString = revenue.Currency + TbSettingsHelper.FormatRevenue(revenue.Amount);
                        revenues = totalRevenuesString;
                    }
                    catch (FormatException)
                    {
                        revenues = existingAppSettings.TotalRevenues;
                    }
                }

                var templateContent = EmailHelper.GetTemplateContentFromResource("big_cancellation");
                string sendTo = ConfigHelper.GetValue("BigCancellation_SendTo", "<EMAIL>");
                string subject = string.Format("BIG CANCELLATION TO FORWARD TO {0} shop - {1}. [Action Required] Your profitable Traffic Booster account was cancelled. ", user.Email, shop.ID);

                EmailHelper.SendEmail(sendTo, subject, templateContent.FormatWith(new { Name = (string.IsNullOrEmpty(user.Name) ? "there" : user.Name), Revenues = revenues }));
                string shopLink = EmailHelper.GetBoLinkHrefAndAMEmail(shop.ID, out string amName, out string amEmail);
                if (amEmail != null)
                {
                    EmailHelper.SendEmail(amEmail, $"Shop {shop.ID} subscription canceled", $"{shopLink}, Click <a href='https://bo.storeya.com/Launcher/Execute?name=DO_NOT_CONTACT&shopId={shop.ID}' >here</a> if you want to prevent us from contact this merchant again.");
                }
            }
        }

        private static void CancelShopifyChargeIfNeeded(Shop shop, User user, int appID, StoreYaEntities db)
        {
            try
            {
                int storeYaAppType = (int)StoreyaAppHelper.GetStoreyaAppType(appID);
                ShopifyConnectedShop connectedShop = db.ShopifyConnectedShops.Where(t => t.ShopID == shop.ID && t.StoreyaAppTypeID == storeYaAppType).FirstOrDefault();
                if (connectedShop != null)
                {
                    ShopifyConnector connector = new ShopifyConnector(new ConnectionData() { StoreName = connectedShop.ShopifyShopName, Token = connectedShop.AppToken });

                    ShopifyAppCharge charge = db.ShopifyAppCharges.Where(x => x.ShopifyShopName == connectedShop.ShopifyShopName && x.StoreyaAppTypeID == x.StoreyaAppTypeID && x.ChargeStatus == "active").FirstOrDefault();
                    if (charge != null)
                    {
                        ShopifyRootRecurringApplicationCharge updatedCharge = null;
                        try
                        {
                            updatedCharge = connector.ReadCharge(charge.ChargeID.Value);
                        }
                        catch (Exception ex)
                        {
                            Log4NetLogger.Error(string.Format("Failed to read Shopify chargeID: {0}", charge.ChargeID.Value), ex, shop.ID);
                        }

                        if (updatedCharge != null
                            && updatedCharge.recurring_application_charge != null
                            && updatedCharge.recurring_application_charge.status == "active")
                        {
                            try
                            {
                                connector.DeleteCharge(charge.ChargeID.Value);
                                Log4NetLogger.Info(string.Format("Shopify charge {0} for {1} app was just cancelled.", charge.ChargeID.Value, StoreyaAppHelper.GetAppName(storeYaAppType)), shop.ID);
                            }
                            catch (Exception ex)
                            {
                                Log4NetLogger.Error("Failed to cancel Shopify charge", ex, shop.ID);

                                string body = string.Format("Failed to cancel automatically the {0} Shopify app charge for https://www.storeya.com/admin/userdetails/{1}. <br/> The manually treatment is required. https://www.bo.storeya.com/ShopifyApps/ConnectedShops?shopID={2}", StoreyaAppHelper.GetAppName(storeYaAppType), user.ID, shop.ID);
                                string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
                                EmailHelper.SendEmail(email, "ACTION REQUIRED: Shopifycharge should be cancelled - shop " + shop.ID, body, null, null, true, "SYSTEM");
                            }

                            ShopifyRootRecurringApplicationCharge canceledCharge = connector.ReadCharge(charge.ChargeID.Value);
                            ShopifyAppCharge mappedCancelledCharge = connector.MapToShopifyAppCharge(connectedShop.ShopifyShopName, connectedShop.StoreyaAppTypeID, canceledCharge);
                            bool isUpdatedInDb = SubscriptionManagerNew.UpdateShopifyChargeInDB(mappedCancelledCharge, shop.ID);
                        }
                        else
                        {
                            Log4NetLogger.Info(string.Format("No need to cancel Shopify charge for {1} app, - the Shopify charge status is {0}.",
                                ((updatedCharge != null && updatedCharge.recurring_application_charge != null) ? updatedCharge.recurring_application_charge.status : ""),
                                StoreyaAppHelper.GetAppName(storeYaAppType)), shop.ID);
                        }
                    }
                    else
                    {
                        Log4NetLogger.Info(string.Format("No Shopify charge record for {0} app was found to be cancelled.", StoreyaAppHelper.GetAppName(storeYaAppType)), shop.ID);
                    }
                }
                else
                {
                    Log4NetLogger.Info(string.Format("No ShopifyConnectedShop record was found to cancel the Shopify charge for {0} app.", StoreyaAppHelper.GetAppName(storeYaAppType)), shop.ID);
                }
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error("Failed on Shopify charge cancellation.", ex, shop.ID);
            }
        }

        public static void RemoveApp(ShopApp shopApp)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopApp shopAppToUpdate = db.ShopApps.Where(a => a.ID == shopApp.ID).Single();

            switch ((AppTypes)shopApp.AppTypeID)
            {
                case AppTypes.FacebookShop:
                    RemoveFBShop(shopApp.ShopID);
                    //remove the paln
                    shopAppToUpdate.PaymentPlan = null;
                    break;
                case AppTypes.CouponPop:
                    RemoveCpApp(shopApp.ShopID, shopApp.AppTypeID);
                    shopAppToUpdate.PaymentPlan = null;
                    break;

                case AppTypes.ExitPop:
                    RemoveEpApp(shopApp.ShopID, shopApp.AppTypeID);
                    shopAppToUpdate.PaymentPlan = null;
                    break;

                case AppTypes.RFF:
                    RemoveRffApp(shopApp.ShopID, shopApp.AppTypeID);
                    break;

                case AppTypes.LikeBox:
                    RemoveLikeBoxApp(shopApp.ShopID, shopApp.AppTypeID);
                    break;

                case AppTypes.TrafficBooster:
                    RemoveTrafficBoosterApp(shopApp.ShopID, shopApp.AppTypeID);
                    break;

                case AppTypes.TrafficTracker:
                    RemoveTrafficTrackerApp(shopApp.ShopID, shopApp.AppTypeID);
                    break;

                case AppTypes.FacebookAdsGrader:
                    RemoveFacebookAdsGraderApp(shopApp.ShopID);
                    break;
                case AppTypes.BenchmarkHero:
                    RemoveBenchmarkHeroApp(shopApp.ShopID, shopApp.AppTypeID);
                    break;
                case AppTypes.GrowthHero:
                    RemoveGrowthHeroApp(shopApp.ShopID);
                    break;
                case AppTypes.ProductDescriber:
                    RemoveProductDescriberApp(shopApp.ShopID);
                    break;
            }

            //if (AppStoreManager.IsExternalApp(shopApp.AppTypeID))
            //{
            //    //WebHookEventsManager.PostWebHookEvent(shopApp.ShopID, (shopApp.UserID ?? 0), shopApp.AppTypeID, WebHookEventType.Uninstalled);
            //    DeleteExternalScript(shopApp.ShopID, shopApp.AppTypeID);
            //}


            shopAppToUpdate.ShopID = -shopApp.ShopID;
            shopAppToUpdate.UpdatedAt = DateTime.Now;
            //db.ShopApps.Remove(installation);
            db.SaveChanges();

            var appName = AppStoreManager.GetAppByID(shopApp.AppTypeID).AppName;
            Eventer.ReportEvent(shopApp.ShopID, EventCategory.AppStore, BackgroundEvents.app_removed.ToString(), appName);
        }

        private static void RemoveProductDescriberApp(int shopAppShopId)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = db.ProductDescribers.Where(l => l.ShopId == shopAppShopId).FirstOrDefault();
            if (app != null)
            {
                app.Status = (int)ProductDescriberAppStatus.Canceled;
                // app.ShopId = app.ShopId * -1;
                //ttApp.CancelledAt = DateTime.Now;
                db.SaveChanges();
            }

            Log4NetLogger.Info(string.Format("A Product Describer App was removed at {0}.", DateTime.Now), shopAppShopId);
        }

        private static void RemoveGrowthHeroApp(int shopAppShopId)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var app = db.GrowthHeros.Where(l => l.ShopID == shopAppShopId).FirstOrDefault();
            if (app != null)
            {
                app.Status = (int)GrowthHeroStatuses.Canceled;
                app.ShopID = app.ShopID * -1;
                //ttApp.CancelledAt = DateTime.Now;
                db.SaveChanges();
            }

            Log4NetLogger.Info(string.Format("A Growth Hero App was removed at {0}.", DateTime.Now), shopAppShopId);

        }

        private static void RemoveBenchmarkHeroApp(int shopID, int p2)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Benchmark ttApp = db.Benchmarks.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (ttApp != null)
            {
                ttApp.Status = (int)BenchmarkStatuses.Canceled;
                ttApp.ShopID = ttApp.ShopID * -1;
                //ttApp.CancelledAt = DateTime.Now;
                db.SaveChanges();
            }

            Log4NetLogger.Info(string.Format("A TrafficTracker App was removed at {0}.", DateTime.Now), shopID);
        }



        private static void RemoveFacebookAdsGraderApp(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            FbGrader ttApp = db.FbGraders.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (ttApp != null)
            {
                //db.SaveChanges();
                //Log4NetLogger.Info(string.Format("A TrafficTracker App was removed at {0}.", DateTime.Now), shopID);
            }

        }

        private static void RemoveTrafficTrackerApp(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            TTApp ttApp = db.TTApps.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (ttApp != null)
            {
                ttApp.Status = -1;
                ttApp.ShopID = ttApp.ShopID * -1;
                //ttApp.CancelledAt = DateTime.Now;
                db.SaveChanges();
            }

            Log4NetLogger.Info(string.Format("A TrafficTracker App was removed at {0}.", DateTime.Now), shopID);
        }

        private static void RemoveTrafficBoosterApp(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            TrafficBooster trafficBooster = db.TrafficBoosters.Where(l => l.ShopID == shopID).FirstOrDefault();
            if (trafficBooster != null)
            {
                Log4NetLogger.InfoWithDB("RemoveTrafficBoosterApp: TB App status was changed to CANCELED", null, shopID);
                trafficBooster.AppStatus = TB_APP_STATUS.CANCELED.GetHashCode();
                trafficBooster.Status = (byte)TB_AW_STATUS.CANCELED;
                trafficBooster.ShopID = trafficBooster.ShopID * -1;
                trafficBooster.CancelledAt = DateTime.Now;
                db.SaveChanges();

                TbChannelManager.DisableChannelsInDb(shopID);
                try
                {
                    if (IsShopify(shopID))
                    {
                        var api = ShopifyConnector.GetShopifyApiClient(shopID, (int)AppTypes.TrafficBooster);
                        api.UninstallStoryaApp();
                    }
                    int storeYaAppType = (int)StoreyaAppHelper.GetStoreyaAppType(appID);
                    ShopifyConnectedShop connectedShop = db.ShopifyConnectedShops.Where(t => t.ShopID == shopID && t.StoreyaAppTypeID == storeYaAppType).FirstOrDefault();
                    if (connectedShop != null)
                    {
                        connectedShop.PermissionsScope = -1;
                        db.SaveChanges();
                    }
                }
                catch (Exception ex)
                {
                    ConsoleAppHelper.WriteErrorWithDB("Failed To uninstall Storeya Shopify app", ex, shopID);
                }
            }

            Log4NetLogger.Info(string.Format("A TrafficBooster App was removed at {0}.", DateTime.Now), shopID);
        }

        private static void RemoveLikeBoxApp(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            LikeBox likeBox = db.LikeBoxes.Where(l => l.ShopID == shopID).SingleOrDefault();
            db.LikeBoxes.Remove(likeBox);
            db.SaveChanges();
            Log4NetLogger.Info(string.Format("A LikeBox App App was removed at {0}.", DateTime.Now), shopID);
        }


        private static void RemoveFBShop(int shopID)
        {
            //StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //Shop shop = db.Shops.Where(s => s.ID == shopID).Single();
            //User user = db.Users.Where(u => u.ID == shop.UserID).Single();

            ShopManager.RemoveFbShopApp(shopID);
            //ShopManager.DowngradeShopImmediately

            //create new shop
            //ShopManager.CreateAdditionalShop(user);
        }

        private static void RemoveRffApp(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            //delete settings
            foreach (var item in db.RffOffers.Where(o => o.ShopID == shopID).ToList())
            {
                item.Status = 100; //deleted
            }

            List<RffEvent> rffViewedEvents = db.RffEvents.Where(r => r.ShopID == shopID && r.EventType == (int)Storeya.Core.Models.ReferAFriend.RffStatsModel.RffEventTypes.RefererOfferViewed).ToList();
            if (rffViewedEvents != null && rffViewedEvents.Count > 0)
            {
                foreach (var rffViewedEvent in rffViewedEvents)
                {
                    db.RffEvents.Remove(rffViewedEvent);
                }
            }

            db.SaveChanges();

            Log4NetLogger.Info(string.Format("A Rff App was removed at {0}. All suitable Rff Viewed Events were removed right after.", DateTime.Now), shopID);
        }

        public static void RemoveCpApp(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            //delete settings
            foreach (var item in db.CouponPops.Where(c => c.ShopID == shopID).ToList())
            {
                item.Status = 100; //deleted
            }

            CouponPopInstallationDomain installationDomainRecord = db.CouponPopInstallationDomains.Where(a => a.ShopID == shopID).SingleOrDefault();
            if (installationDomainRecord != null)
            {
                db.CouponPopInstallationDomains.Remove(installationDomainRecord);
            }

            db.SaveChanges();
            //TODO?: remove script for Shopify
            Log4NetLogger.Info(string.Format("A CouponPop App was removed at {0}. The suitable CouponPopInstallationDomain record was removed right after..", DateTime.Now), shopID);
        }

        public static void RemoveEpApp(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            //delete settings
            foreach (var item in db.ExitPops.Where(c => c.ShopID == shopID).ToList())
            {
                item.Status = 100; //deleted
            }

            ExitPopInstallationDomain installationDomainRecord = db.ExitPopInstallationDomains.Where(a => a.ShopID == shopID).SingleOrDefault();
            if (installationDomainRecord != null)
            {
                db.ExitPopInstallationDomains.Remove(installationDomainRecord);
            }

            db.SaveChanges();

            Log4NetLogger.Info(string.Format("An ExitPop App was removed at {0}. The suitable ExitPopInstallationDomain record was removed right after.", DateTime.Now), shopID);
        }



        public static int? CancelPaymentForLastAppInSubscription(Shop shop, User user, int appID)
        {
            string messageAboutBlueSnapSubscription = "A subscription was NOT cancelled.";
            int? subscriptionID = null;
            bool isCancelled = false;
            //if (shop.IsPaid.HasValue && AppStoreManager.IsFbShopVeteran(shop.IsPaid.Value))
            //{
            //    Log4NetLogger.Info("LastAppInSubscription: Extracting BsSubscriptionId for veteran client to cancel.", shop.ID);
            //    string bsSubscriptionToCancel = SubscriptionManager.GetBlueSnapSubscriptionIDForVeteran(shop.ID);

            //    if (string.IsNullOrEmpty(bsSubscriptionToCancel))
            //    {
            //        messageAboutBlueSnapSubscription = "BsSubscriptionId to cancel was not discovered for veteran client. Please, check all client's subscriptions.";
            //    }
            //    else
            //    {
            //        isCancelled = SubscriptionManager.UpdateSubscriptionStatus(bsSubscriptionToCancel, BluesnapSubscriptionStatus.Cancelled, shop.ID);
            //        if (isCancelled)
            //        {
            //            subscriptionID = int.Parse(bsSubscriptionToCancel);
            //            string message = string.Format("The BsSubscription {0} was canceled in BlueSnap.", bsSubscriptionToCancel);
            //            Log4NetLogger.Info(message, shop.ID);
            //            messageAboutBlueSnapSubscription = message;
            //        }
            //        else
            //        {
            //            string body = string.Format("The {0} app was just removed, but a subscription was not cancelled, as expected. Please, review logs https://www.storeya.com/admin/logs/?shopid={1} and cancel the subscription manually", appID, shop.ID);
            //            EmailHelper.SendEmail("<EMAIL>", "AUDIT REQUIRED: Failed to cancel subscription. Shop ID - " + shop.ID, body, null, null, true, "SYSTEM");
            //        }
            //    }
            //}
            //else
            //{
            ShopApp paidApp = AppStoreManager.GetAppSettings(shop.ID, appID);
            if (paidApp.SubscriptionID != null)
            {
                ShopSubscription shopSubscription = SubscriptionManager.GetShopSubscription(paidApp.SubscriptionID.Value);
                if (shopSubscription != null)
                {
                    if (shopSubscription.PaymentAdapterType == (int)PaymentAdapterTypes.BlueSnap)
                    {
                        Log4NetLogger.Info(string.Format("IsLastAppInSubscription: subscription of {0} will be cancelled in BlueSnap.", AppStoreManager.GetAppByID(appID).AppName), shop.ID);

                        isCancelled = SubscriptionManager.UpdateSubscriptionStatus(shopSubscription.BlueSnapSubscriptionID.ToString(), BluesnapSubscriptionStatus.Cancelled, shop.ID);
                        subscriptionID = shopSubscription.BlueSnapSubscriptionID;
                    }
                    //else if (shopSubscription.PaymentAdapterType == (int)PaymentAdapterTypes.Avangate)
                    //{
                    //    Log4NetLogger.Info(string.Format("IsLastAppInSubscription: subscription of {0} will be cancelled in Avangate.", AppStoreManager.GetAppByID(appID).AppName), shop.ID);

                    //    isCancelled = SubscriptionManager.CancelAvangateSubscription(shopSubscription.OriginalSubscriptionID, shop.ID);
                    //}

                    if (isCancelled)
                    {
                        string message = string.Format("The subscription {0} was successfully cancelled.{1}", shopSubscription.BlueSnapSubscriptionID, Environment.NewLine);
                        Log4NetLogger.Info(message, shop.ID);
                        messageAboutBlueSnapSubscription = message;
                    }
                }
                else
                {
                    string message = string.Format("The StoreYa ShopSubscription {0} was not found in DB when there is a paid shop app record referencing to this ID. SHOULD BE CHECKED.{1}", paidApp.SubscriptionID.ToString(), Environment.NewLine);
                    Log4NetLogger.Info(message, shop.ID);
                    messageAboutBlueSnapSubscription = message;
                }
            }
            else
            {
                string message = string.Format("{0} paid app has no subscription to cancel. This paid app SHOULD BE CHECKED.", AppStoreManager.GetAppByID(paidApp.AppTypeID).AppName);
                Log4NetLogger.Info(message, shop.ID);
                messageAboutBlueSnapSubscription = message;
            }
            //}
            return subscriptionID;
            ////send us email and show alert
            //string body = string.Format("The last app in subscription was just removed - https://www.storeya.com/admin/userdetails/{0}.{1} Shop platform is {2}.{1} OriginMarketplace: {3}.{1}{4}", user.ID, Environment.NewLine, shop.CatalogSourcePlatform.HasValue ? ((CatalogSourcePlatforms)shop.CatalogSourcePlatform).ToString() : "None", user.OriginMarketplace.HasValue ? ((OriginMarketplaces)user.OriginMarketplace).ToString() : "None", messageAboutBlueSnapSubscription);
            //string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
            //EmailHelper.SendEmail(email, "Subscription cancelation - shop " + shop.ID, body, null, null, true, "SYSTEM");
        }

        public static int? RemoveAppFromBlueSnapSubscription(int shopID, int appID)
        {
            SubscriptionChangeManager m = new SubscriptionChangeManager(shopID);
            RequiredActions actions = m.GetRemoveChange(appID);
            //SubscriptionChange subscriptionToDowngrade = new SubscriptionChange(shopID, appID);
            if (actions.RequiredSubscriptionToCancel == null)
            {
                UpdateAppSubscription(actions, out int? onetimePaymentOrderID, out string error);
                if (actions.RequiredSubscriptionToCancel != null)
                {
                    if (int.TryParse(actions.RequiredSubscriptionToCancel.BsSubscriptionID, out int subTocancel))
                    {
                        return subTocancel;
                    }
                }
            }
            else
            {
                //string message = string.Format("Removing not last app in subscription without updating a subscription. ShopID: {1} app: {0}. Please, check this flow.", appID, shopID);
                //Log4NetLogger.Info(message, shopID);
                //EmailHelper.SendEmail(ConfigHelper.GetValue("paymentsEmail", "<EMAIL>"), "Removing app - RequiredSubscriptionToCancel is not null.", message);
            }
            return null;
        }

        public static int? GetSubcriptionToCancel(int appID, int shopID)
        {
            var appToRemove = GetAppSettings(shopID, appID);
            return appToRemove.SubscriptionID;
        }

        public static bool IsLastAppInSubscription(int appID, int shopID)
        {
            var appToRemove = GetAppSettings(shopID, appID);
            if ((appToRemove.PaymentPlan ?? 0) == 0)
            {
                //removing not paid app
                return false;
            }
            var apps = GetPaidShopApps(shopID);
            if (apps != null)
            {
                foreach (var app in apps)
                {
                    if (app.AppTypeID != appToRemove.AppTypeID && appToRemove.SubscriptionID == app.SubscriptionID)
                    {
                        //other app using same subscription
                        return false;
                    }
                }
            }

            ShopApp otherUserShopSuiteWidget = GetOtherUsersShopPrivateJetApp(shopID, appID);
            if (otherUserShopSuiteWidget != null)
                return false;

            return true;
        }


        public static AppsForAppStore GetAppForAppStore(int appTypeID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            AppsForAppStore appsForAppStore = db.AppsForAppStores.Where(a => a.AppTypeID == appTypeID).FirstOrDefault();
            return appsForAppStore;
        }

        public static List<AppsForAppStore> GetAppsForAppStore()
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            //only status = 1 is shown in app store
            List<AppsForAppStore> appsForAppStore = db.AppsForAppStores.Where(a => a.Status == 1).OrderBy(a => a.OrderID).ToList();

            //if (ConfigHelper.GetBoolValue("IsStagingServer"))
            //{
            //    appsForAppStore = db.AppsForAppStores.Where(a => a.Status == 1).OrderBy(a => a.OrderID).ToList();
            //}
            //else
            //{
            //    appsForAppStore =db.AppsForAppStores.OrderBy(a => a.OrderID).ToList();
            //}
            return appsForAppStore;
        }

        public static bool IsWhiteLabelPlan(int planType)
        {
            if (Enum.IsDefined(typeof(PlanTypes), planType))
            {
                return planType == (int)PlanTypes.Tuxedo || planType == (int)PlanTypes.PrivateJet;
            }
            return false;
            //return planType == PlanTypes.Elegant 
            //    || planType == PlanTypes.Tuxedo
            //    || planType == PlanTypes.FirstClass_2
            //    || planType == PlanTypes.PrivateJet;

        }


        private static bool IsSetupDone(int appID, int shopID)
        {
            AppEntity app = AppStoreManager.GetAppByID(appID);
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var appQuery = db.ShopApps.Where(a => a.ShopID == shopID && a.AppTypeID == app.AppID && a.RegStatus >= (int)AppRegStatus.Created);
            return appQuery.Any();
        }

        private static bool IsFbInstalled(Shop shop)
        {
            return shop.RegState > 3;
        }


        public static string GetWidgetSettingsUrl(int shopID, int appTypeID)
        {
            string widgetSettingsUrl = null;

            switch (appTypeID)
            {
                case (int)AppTypes.CouponPop:
                    widgetSettingsUrl = GetActiveCouponPopSettingsUrl(shopID);
                    break;

                case (int)AppTypes.ExitPop:
                    widgetSettingsUrl = GetActiveExitPopSettingsUrl(shopID);
                    break;

                case (int)AppTypes.TrafficBooster:
                    widgetSettingsUrl = "/home/<USER>" + shopID; //GetTrafficBoosterSettingsUrl(shopID);
                    break;

                case (int)AppTypes.BenchmarkHero:
                    widgetSettingsUrl = "/home/<USER>" + shopID; //GetTrafficBoosterSettingsUrl(shopID);
                    break;
                case (int)AppTypes.GrowthHero:
                    widgetSettingsUrl = "https://bo.storeya.com/home/<USER>" + shopID; //GetTrafficBoosterSettingsUrl(shopID);
                    break;
                case (int)AppTypes.ProductDescriber:
                    widgetSettingsUrl = "https://bo.storeya.com/Home/ProductDescribers?shopId=" + shopID; //GetTrafficBoosterSettingsUrl(shopID);
                    break;
                case (int)AppTypes.PintrestTab:
                case (int)AppTypes.InstaTab:
                case (int)AppTypes.TwitterTab:
                case (int)AppTypes.YoutubeTab:
                    widgetSettingsUrl = string.Format("/admin/TabsSettings?shopID={0}", shopID);
                    break;

                default:
                    break;
            }

            return widgetSettingsUrl;
        }

        //private static string GetTrafficBoosterSettingsUrl(int shopID)
        //{
        //    string tb_url = null;
        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    TrafficBooster tb_settings = db.TrafficBoosters.Where(tb => tb.ShopID == shopID).FirstOrDefault();

        //    if (tb_settings != null)
        //        tb_url = string.Format("/admin/TrafficBoosterDetails?id={0}", tb_settings.ID);

        //    return tb_url;
        //}

        private static string GetActiveExitPopSettingsUrl(int shopID)
        {
            string ep_url = null;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Core.ExitPop activeEP = db.ExitPops.Where(c => c.ShopID == shopID && c.Status == 1).FirstOrDefault();

            if (activeEP != null)
                ep_url = string.Format("/Reports/ExitPopDetails?id={0}", activeEP.ID);

            return ep_url;
        }

        public static string GetActiveCouponPopSettingsUrl(int shopID)
        {
            string cp_url = null;
            cp_url = $"/home/<USER>/{shopID}";
            return cp_url;
        }

        public static bool HasChangebleNotOneDollarContractSubscription(int shopID)
        {
            IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
            List<ShopApp> paidApps = GetPaidShopApps(shopID);
            if (paidApps != null && paidApps.Count > 0)
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                var notPayPalPaidApps = from sa in paidApps.Where(s => s.SubscriptionID.HasValue && s.SubscriptionID.Value > 0
                                        && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Shopify
                                        && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Tictail
                                        && s.SubscriptionID.Value != (int)SpecificSubscriptionID.Wix)
                                        join subs in db.ShopSubscriptions on sa.SubscriptionID equals subs.ID
                                        where (string.IsNullOrEmpty(subs.PaymentMethod) || subs.PaymentMethod.ToLower() != "paypal")
                                        && subs.ContractID != bluesnapConfiguration.OneDollarPerDayMonthly
                                        && subs.ContractID != bluesnapConfiguration.OneDollarPerDayAnnual
                                        select sa;

                return notPayPalPaidApps.Any();
            }
            return false;
        }


        private static void UpdateTbWithSubscriptionData(int shopID, int appID, int subscriptionID, int planID, double appPrice, int agreeID, bool updateLastPaymentDate = true)
        {

            ShopApp appToChange = AppStoreManager.GetAppSettings(shopID, appID);
            TbAgreementsManager agreeManager = new TbAgreementsManager(shopID);
            var subscriptionToCancel = agreeManager.GetSubscriptionToCancel(appToChange, shopID, subscriptionID, agreeID);
            if (subscriptionToCancel != null)
            //          if (appToChange.SubscriptionID != null && appToChange.SubscriptionID != subscriptionID && appToChange.SubscriptionID > 0)
            {
                CancelOrFreezePreviousSubscriptions(shopID, subscriptionToCancel.ID, subscriptionID, planID, Convert.ToDecimal(appPrice), agreeID);
            }

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            if (agreeID > 0)//
            {
                TbAgreement agree = db.TbAgreements.Where(a => a.ID == agreeID).Single();
                var dateNow = DateTime.Now;
                if (agree.PurchasedAt == null)
                {
                    agree.PurchasedAt = dateNow;
                    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Plan with agree ID purchased - " + shopID, "Please check " + EmailHelper.GetBoLinkHrefAndAM(shopID)); ;
                }
                TrafficBoostersDbHelper.SetPurchasedAt(shopID, dateNow);
                if (updateLastPaymentDate)
                {
                    agree.LastPaymentDate = dateNow;
                }
                SystemEventHelper.Add(0, shopID, AppTypes.TrafficBooster.GetHashCode(), SystemEventTypes.Payments, SystemEventActions.BudgetValueUpdated, $"Pre Value ${agree.PurchasedBudget}", Convert.ToDecimal(appPrice), agreeID);
                agree.PurchasedBudget = Convert.ToDecimal(appPrice);
                db.SaveChanges();

                //update channel budget
                UpdateAgreementChannelBudget(db, shopID, agreeID, agree.PurchasedBudget, TrafficChannelsStatuses.WaitingForPermissions, true);

                appToChange.HasAgreements = 1;

                if (appToChange.SubscriptionID == null)
                {
                    appToChange.SubscriptionID = SpecificSubscriptionID.SetManuallyByAdmin.GetHashCode();
                }

                //Add to BS as Facebook channel requires setup
                bool newBS = TbBigSpendersManager.Add(shopID, true, null, ReasonsWhyBigSpender.FbChannel);

            }
            else
            {
                appToChange.SubscriptionID = subscriptionID;
                appToChange.PaymentPlanUpdatedAt = DateTime.Now;
                appToChange.AppPrice = appPrice.ToString();

            }

            appToChange.PaymentPlan = planID;
            appToChange.PaymentStatus = (int)AppPaymentStatus.Active;
            appToChange.RegStatus = (int)AppRegStatus.Installed;
            appToChange.UpdatedAt = DateTime.Now;

            db.SaveChanges();
            Log4NetLogger.Info(string.Format("{0} shop app record was updated with: subscriptionID: {1}, planID: {2}", GetAppName(appID), subscriptionID, planID), shopID);

            if (agreeID == 0)//
            {
                TrafficBoostersDbHelper.SetPurchasedAmount(shopID, planID, appPrice, updateLastPaymentDate);
                if (TbCampaignsManager.InitCampaigns(shopID))
                {
                    User user = db.Users.Where(x => x.ID == appToChange.UserID).SingleOrDefault();
                    LauncherHelper.InitCommand(shopID, user, "CREATE_ADW_ACCOUNT");
                }
                if (planID != (int)PlanTypes.CustomPlan)
                {
                    AddEventToIntercom(shopID, "tb_purchased_first_time_plan_" + planID);
                }
                FeedsHelper.ActivateFeeds(shopID);
            }
            else
            {
                FeedsHelper.ActivateFeeds(shopID, true);
                //just set TB as paid
                // TrafficBoostersDbHelper.UpdateTbStatus(shopID, TB_AW_STATUS.PAID);
                TrafficBoostersDbHelper.UpdateTbAppStatus(shopID, TB_APP_STATUS.ACTIVE);
            }
            //appToChange = GetAppSettings(shopID, appID);
            //if (appToChange == null || appToChange.PaymentStatus == (int)AppPaymentStatus.Disabled)
            //{
            //    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "IPN was received and TB app was not enabled or removed shopid-" + shopID, "UpdateTbWithSubscriptionData<br/>" + EmailHelper.AdminBoShopLinkHref(shopID) + string.Format("appID:{0},subscriptionID:{1},planID:{2},appPrice:{3},agreeID:{4}", appID, subscriptionID, planID, appPrice, agreeID));
            //}
        }

        private static void UpdateAgreementChannelBudget(StoreYaEntities db, int shopID, int agreeID, decimal? purchasedBudget, TrafficChannelsStatuses status = TrafficChannelsStatuses.Active, bool updateIfStatusIsNull = false)
        {
            var channel = db.TbAccountTrafficChannels.Where(c => c.ShopID == shopID && c.AgreeID == agreeID).SingleOrDefault();
            if (channel != null)
            {
                Log4NetLogger.Info(string.Format("UpdateChannelBudget - changeing FB channel status agreeID:{2} old:{0} new{1}:", channel.Status, (int)status, agreeID), shopID);
                // channel.BudgetStatus = AdWords.BudjetStatuses.ACTIVE.GetHashCode();
                if (updateIfStatusIsNull && channel.Status == null)
                {
                    channel.Status = status.GetHashCode();
                }
                else if (!updateIfStatusIsNull)
                {
                    channel.Status = status.GetHashCode();
                }
                channel.LastPaidAt = DateTime.Now;
                channel.PurchasedBudget = purchasedBudget;
                channel.UpdatedAt = DateTime.Now;
                db.SaveChanges();
                if (status == TrafficChannelsStatuses.Active || status == TrafficChannelsStatuses.WaitingForPermissions)
                {
                    TrafficBoostersDbHelper.UpdateTbAppStatus(shopID, TB_APP_STATUS.ACTIVE);
                }
            }
            else
            {
                throw new Exception("No channel found for shopID " + shopID + " agree " + agreeID);
            }
        }

        private static List<TbAgreement> GetShopAgreements()
        {
            throw new NotImplementedException();
        }

        private static void CancelOrFreezePreviousSubscriptions(int shopID, int currentSubscriptionID, int newSubscriptionID, int planID, decimal appPrice, int? agreeId = null)
        {

            int appID = AppTypes.TrafficBooster.GetHashCode();

            //changing existing subscription ID - the old one probably should be cancelled.

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(x => x.ID == shopID).SingleOrDefault();
            User user = db.Users.Where(x => x.ID == shop.UserID).SingleOrDefault();

            //report fake reccurring event for ReactivatePaidApps
            SystemEventHelper.Add(user.ID, shopID, appID, SystemEventTypes.IPNCalls, SystemEventActions.Recurring, string.Format("PlanID_{0}_Fake_Recurring", planID), Convert.ToDecimal(appPrice), agreeId);

            ShopSubscription storeyaSubscriptionToCancel = SubscriptionManager.GetShopSubscription(currentSubscriptionID);

            if (storeyaSubscriptionToCancel.PaymentMethod.ToLower() == "paypal") //In case this is paypal subscription:
            {
                //Cancel old subscriptions 
                bool isCancelled = SubscriptionManager.UpdateSubscriptionStatus(storeyaSubscriptionToCancel.BlueSnapSubscriptionID.ToString(), BluesnapSubscriptionStatus.Cancelled, shop.ID);
                if (isCancelled)
                {
                    string emailHtml = EmailManager.GetEmailAboutPayPalSubscriptionCancellation(user);
                    EmailHelper.SendEmail(user.Email, "Regarding your Traffic Booster plan", emailHtml, null, null, true);

                    ConsoleAppHelper.WriteLogWithDB(string.Format("The paypal subscription {0} was cancelled after a new subscription was registered.", storeyaSubscriptionToCancel.BlueSnapSubscriptionID), shop.ID);
                }
            }
            else //In case of BS subscription:
            {
                //Change status to Cancel On Renewal + Set next billing date as far as possible
                bool isfrozen = SubscriptionManager.FreezeOrRenewSubcription(storeyaSubscriptionToCancel.BlueSnapSubscriptionID.ToString(), false, shop.ID);
                if (isfrozen)
                {
                    ConsoleAppHelper.WriteLogWithDB(string.Format("The subscription {0} was frowsen after a new subscription was registered.", storeyaSubscriptionToCancel.BlueSnapSubscriptionID), shop.ID);
                }
            }

            string body = string.Format("The subscription ID of app {0} is changeing from {1} to {2}. <br/> https://www.storeya.com/admin/shopsubscriptions?shopid={3} ", appID, currentSubscriptionID, newSubscriptionID, shopID);
            ConsoleAppHelper.WriteLogWithDB(body, shopID);
            //EmailHelper.SendEmail("<EMAIL>", "AUDIT REQUIRED: Changing shop subscription ID. Shop ID - " + shopID, body, null, null, true, "SYSTEM");
        }

        public static void SubscribToShopifyWebHook(Shopify_StoreyaApp appType, int? shopId = null, int installInLastDays = 30)
        {
            DateTime startDate = DateTime.Now.AddDays(-installInLastDays);

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var connectedShops = db.ShopifyConnectedShops.Where(s =>
                s.PermissionsScope == 1
                && s.StoreyaAppTypeID == (int)appType
                && s.ShopID > 0 && s.InsertedAt > startDate).ToList();

            if (shopId.HasValue)
            {
                connectedShops = connectedShops.Where(s => s.ShopID == shopId.Value).ToList();
            }
            Console.WriteLine($"Total to run on:{connectedShops.Count()} for app {appType} Are you sure? y/n");
            string res = Console.ReadLine();
            if (res.Trim() != "y")
            {
                return;
            }
            foreach (var connectedShop in connectedShops)
            {
                try
                {
                    string subscribe_uninstall_response = SubscribToShopifyWebHook(connectedShop.ShopID, appType);
                    Console.WriteLine($"{connectedShop.ShopID} - {connectedShop.ShopifyShopName} Shopify Subscribed to app/uninstalled responce: {subscribe_uninstall_response}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{connectedShop.ShopID} - {connectedShop.ShopifyShopName} Failed Shopify Subscribed to app/uninstalled responce: {ex.Message}");
                }

            }

        }

        public static string SubscribToShopifyWebHook(int shopId, Shopify_StoreyaApp appType, string domain = "www.storeya.com")
        {
            ShopifyConnector client = new ShopifyConnector(shopId, (int)appType);
            string subscribe_to_app_uninstall_webhook_url = string.Format("https://{0}/shopify/WebHookAppUninstalled?appType={1}", domain, appType);
            string subscribe_uninstall_response = client.SubscribeToWebhook("app/uninstalled", subscribe_to_app_uninstall_webhook_url);
            Log4NetLogger.Info(string.Format("Shopify Subscribed to app/uninstalled responce: {0}", subscribe_uninstall_response), shopId);
            return subscribe_uninstall_response;
        }
        public static string UnSubscribToShopifyWebHook(ShopifyConnector client, Shopify_StoreyaApp appType, string type = "update")
        {
            ShopifyConnector.WebHooksType whType = ShopifyConnector.WebHooksType.Update;
            if (type == "uninstall")
            {
                whType = ShopifyConnector.WebHooksType.Uninstall;
            }
            string res = client.UnSubscribeWebHook(0, appType, whType);
            return res;
        }
        public static string UnSubscribToShopifyWebHook(int shopId, Shopify_StoreyaApp appType, string type = "update")
        {
            ShopifyConnector client = new ShopifyConnector(shopId, (int)appType);
            ShopifyConnector.WebHooksType whType = ShopifyConnector.WebHooksType.Update;
            if (type == "uninstall")
            {
                whType = ShopifyConnector.WebHooksType.Uninstall;
            }
            string res = client.UnSubscribeWebHook(shopId, appType, whType);
            Log4NetLogger.Info(res, shopId);
            return res;
        }
    }
}
