﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Storeya.Core.Models
{
    public enum AppTypes
    {
        None = 0,
        FacebookShop = 1,
        ScratchAndWinPromotion = 10,
        InstaGallery = 11,
        GroupDeal = 12,

        LikeBox = 100,
        FanGate = 101,
        TwitterTab = 102,
        InstaTab = 103,
        YoutubeTab = 104,
        PintrestTab = 105,

        CouponPop = 200,
        ExitPop = 201,
        RFF = 202,
        PoweredBanner = 203,
        TrafficBooster = 204,
        TrafficTracker = 205,
        FacebookAdsGrader = 206,
        BenchmarkHero = 207,
        GrowthHero = 208,
        ProductDescriber = 209,
        TrustBadges = 210,
        AITryOnModel = 211,
        WhatsApp = 212,

        External = 1000,
        AdditionalService = 2000
    }

    public class AppTypesHelper
    {
        public static List<AppType> GetAppTypes()
        {
            List<AppType> list = new List<AppType>();

            list.Add(new AppType() { ID = 0, Name = "- Select App Type-" });

            foreach (AppTypes val in Enum.GetValues(typeof(AppTypes)))
            {
                list.Add(new AppType() { ID = (int)val, Name = Enum.GetName(typeof(AppTypes), val) + "(" + ((int)val).ToString() + ")" });
            }

            return list;
        }
    }

    public class AppType
    {
        public int ID { get; set; }
        public string Name { get; set; }
    }

}