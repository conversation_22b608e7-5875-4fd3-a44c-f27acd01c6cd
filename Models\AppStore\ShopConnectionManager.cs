﻿using Newtonsoft.Json;
using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Text;

namespace Storeya.Core.Models.AppStore
{
    public class ShopConnectionManager
    {
        public static void AddSyncTask(int shopID, int appID, SyncTaskType syncTaskType)
        {
            string encodedShopID = SequenceHelper.Encode(shopID);
            string encodedAppID = SequenceHelper.Encode(appID);

            string apiUrl = ConfigHelper.GetValue("Api_Url").TrimEnd('/');
            string requestUrl = apiUrl + "/shops/" + encodedShopID + "/synctasks";

            string method = "POST";

            SyncTasksModel initProductsTask = new SyncTasksModel() { AppID = encodedAppID, ShopID = encodedShopID, Type = syncTaskType };
            string initProductsResponse = PostData(shopID, method, requestUrl, initProductsTask);
        }

        
        
        public static void InitShopConnection(int shopID, int appID) 
        {
            string encodedShopID = SequenceHelper.Encode(shopID);
            string encodedAppID = SequenceHelper.Encode(appID);

            string apiUrl = ConfigHelper.GetValue("Api_Url").TrimEnd('/');            
            string requestUrl = apiUrl + "/shops/" + encodedShopID + "/synctasks";

            string method = "POST";

            SyncTasksModel initProductsTask = new SyncTasksModel() { AppID = encodedAppID, ShopID = encodedShopID, Type = SyncTaskType.InitProducts};
            string initProductsResponse = PostData(shopID, method, requestUrl, initProductsTask);

            SyncTasksModel initOrdersTask = new SyncTasksModel() { AppID = encodedAppID, ShopID = encodedShopID, Type = SyncTaskType.InitOrders };
            string initOrdersResponse = PostData(shopID,  method, requestUrl, initOrdersTask);

            SyncTasksModel initOrdersArchivesTask = new SyncTasksModel() { AppID = encodedAppID, ShopID = encodedShopID, Type = SyncTaskType.InitOrdersArchive };
            string initOrdersArchivesResponse = PostData(shopID, method, requestUrl, initOrdersArchivesTask);
        }


        private static string PostData(int shopIDToLog, string method = "GET", string url = null, object requestObject = null)
        {
            string content = null;            
            try
            {
                ServicePointManager.SecurityProtocol =  SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                request.Method = method;
                request.AllowAutoRedirect = true;
                request.KeepAlive = true;
                //request.Headers.Add("Cache-Control", "max-age=0");
                //request.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31";
                //request.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
                request.Headers.Add("Accept-Encoding", "gzip,deflate,sdch");
                request.Headers.Add("Accept-Language", "en-US,en;q=0.8");
                request.Headers.Add("Accept-Charset", "ISO-8859-1,utf-8;q=0.7,*;q=0.3");

                string storeyaPrivateKey = ConfigHelper.GetValue("StoreyaPrivateKey");
                request.Headers.Add("Authorization", storeyaPrivateKey);

                request.ContentType = "application/json";
               

                // set header with StoreyaPrivateKey 

                if (method == "PUT" || method == "POST")
                {
                    string requestData = JsonConvert.SerializeObject(requestObject);
                    byte[] data = Encoding.UTF8.GetBytes(requestData);
                    request.ContentLength = data.Length;
                    Stream dataStream = request.GetRequestStream();
                    dataStream.Write(data, 0, data.Length);
                }

                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                if (response != null)
                {
                    Stream responseStream = responseStream = response.GetResponseStream();
                    if (response.ContentEncoding.ToLower().Contains("gzip"))
                        responseStream = new GZipStream(responseStream, CompressionMode.Decompress);
                    StreamReader Reader = new StreamReader(responseStream, Encoding.Default);
                    content = Reader.ReadToEnd();
                }

                //var js = new JavaScriptSerializer();
                //var responseJSON = js.Deserialize<Dictionary<string, dynamic>>(content);

                return content;
            }
            catch (Exception ex)
            {
                throw ex;
                //Log4NetLogger.Error(string.Format("Failed to get authcode for {0}", requestUrl), ex, this.ShopID);
            }
        }
    }

    public class SyncTasksModel
    {
        public string TaskID;
        public string AppID;
        public SyncTaskType Type;
        public SyncTaskStatus Status;
        public DateTime InsertedAt;
        public string Frequency;
        public int Priority;
        public string ShopID;
    }

    public class SyncTasksResponse
    {
        public bool success;
        public string error;
    }

    public enum SyncTaskType
    {
        InitProducts = 1,
        InitOrders = 2,
        InitOrdersArchive = 3,
        SyncOrders = 4,
        SyncProducts = 5,
        AppInstall = 6,
        AppUninstall = 7,
        InitShop = 10
    }

    public enum SyncTaskStatus
    {
        Failed = 0,
        Success = 1,
        Created = 2,
        Pending = 3
    }
}
