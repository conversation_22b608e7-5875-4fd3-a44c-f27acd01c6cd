﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Storeya.Core.Helpers;
using Storeya.Core.Models.Payments;

namespace Storeya.Core.Models.AppStore
{
    public class SubscriptionChange
    {
        public int ShopID { get; set; }
        public int AppID { get; set; }
        public int? PlanID { get; set; }
        public ShopSubscription ShopSubscription { get; set; }
        public int ContractMethod { get; set; }
        public ContractSettings Contract { get; set; }
        public double TotalSubscriptionCharge { get; set; }
        public bool IsRemovingApp { get; set; }
        public SubscriptionChangeTypes ChangeType { get; set; }
        //public SubscriptionChange(int shopID, int appID, int planID = -1) //if not send plan - remove app
        //{
        //    this.ShopID = shopID;
        //    this.AppID = appID; // current app 
        //    this.PlanID = planID;
        //    this.IsRemovingApp = (planID==-1);
        //    //StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    this.ShopSubscription = SubscriptionManager.GetActiveShopSubscription(shopID);
        //    //this.ShopSubscription = SubscriptionManager.GetActiveShopSubscription(shopID, appID);
        //    this.ContractMethod = BluesnapHelper.ReverseMethodID(this.ShopSubscription.ContractID.Value, appID);
        //    this.TotalSubscriptionCharge = AppStoreManager.GetAppsPriceForSubscriptionExcludingProvidedAppId(shopID, appID);
        //    if (planID != -1)
        //    {
        //        this.Contract = BluesnapHelper.GetContract(planID, this.ContractMethod, appID);
        //        this.TotalSubscriptionCharge = this.Contract.Price + this.TotalSubscriptionCharge;
        //    }
            
        //    this.ChangeType = CalculateType(shopID, appID, planID);
        //}

        public SubscriptionChange()
        {
        }



        private static SubscriptionChange GetRemoveChange(int shopID, int appID)
        {
            throw new Exception("should not be in use.");
            //SubscriptionChange change = new SubscriptionChange();
            //change.ShopID = shopID;
            //change.AppID = appID; // current app 
            //change.PlanID = -1;
            //change.IsRemovingApp = true;
            //change.ShopSubscription = SubscriptionManager.GetLastShopSubscription(shopID);
            ////this.ShopSubscription = SubscriptionManager.GetActiveShopSubscription(shopID, appID);
            ////this.ContractMethod = BluesnapHelper.ReverseMethodID(this.ShopSubscription.ContractID.Value, appID);
            //change.TotalSubscriptionCharge = AppStoreManager.GetAppsPriceForSubscriptionExcludingProvidedAppId(shopID, appID);
            //change.ChangeType = SubscriptionChangeTypes.RemoveExistingApp;

            //return change;
        }

        //private SubscriptionChangeTypes CalculateType(int shopID, int appID, int planID)
        //{
        //    if (planID == -1)
        //    {
        //        return SubscriptionChangeTypes.RemoveExistingApp;
        //    }

        //    var app = AppStoreManager.GetAppSettings(shopID, appID);
        //    if (app == null || app.PaymentPlan == null)
        //    {
        //        return SubscriptionChangeTypes.AddNewApp;
        //    }
        //    else if (SubscriptionChangeManager.IsUpgrade(app.PaymentPlan.Value, planID))
        //    {
        //        return SubscriptionChangeTypes.UpgaradeExistingApp;
        //    }
        //    else
        //    {
        //        return SubscriptionChangeTypes.DowngradeExistingApp;
        //    }
        //} 
    }

    //public enum SubscriptionChangeTypes
    //{
    //    AddNewApp = 1,
    //    RemoveExistingApp = 2,
    //    UpgaradeExistingApp = 3,
    //    DowngradeExistingApp = 4
    //}

}
