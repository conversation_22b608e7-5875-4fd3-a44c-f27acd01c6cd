﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using Storeya.Core.Helpers;
using Storeya.Core.Models.Charges;
using Storeya.Core.Models.FastSpring;
using Storeya.Core.Models.Payments;
using Storeya.Core.Models.Plimus;
using Storeya.Core.Models.TrafficBoosterModels;
using static Storeya.Core.Models.FastSpring.FastSpringHelper;

namespace Storeya.Core.Models.AppStore
{
    public class SubscriptionChangeManager
    {
        public int ShopID { get; set; }
        public int AppID { get; set; }
        public int PlanID { get; set; }
        public int ContractMethod { get; set; }

        public WebInfo _webInfo { get; set; }

        private List<PaidApp> _existingPaidApps { get; set; }

        private RequiredActions _requiredActions { get; set; }
        public PaymentAdapterTypes PaymentProvider { get; private set; }

        public SubscriptionChangeManager(int shopID, string blueSnapSubscriptionID = null, PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.BlueSnap)
        {
            PaymentProvider = paymentProvider;
            this.ShopID = shopID;
            LoadExistingApps(shopID, blueSnapSubscriptionID);
            LoadWebInfo(shopID);
        }

        private void LoadWebInfo(int shopID)
        {
            var clientIP = SslHelper.FetchUserIP();
            var remoteHost = HttpContext.Current?.Request?.ServerVariables["REMOTE_HOST"];


            var db = DataHelper.GetStoreYaEntities();
            string country = "";
            var shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            if (shop != null)
            {

                var user = db.Users.Where(u => u.ID == shop.UserID).SingleOrDefault();
                country = UserLocationHelper.GetBillingCountry(user);
                clientIP = BlueSnapApiWrapper.GetValidIpStatic(clientIP, user.SignupState, country);
                if (string.IsNullOrEmpty(clientIP) && user.SignupIP.HasValue)
                {
                    clientIP = HttpHelper.GetIpString(user.SignupIP.Value);
                }
            }
            string userAgent = HttpContext.Current?.Request?.UserAgent;
            if (string.IsNullOrEmpty(userAgent))
            {
                userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36";
            }
            if (string.IsNullOrEmpty(remoteHost))
            {
                remoteHost = clientIP;
            }
            _webInfo = new WebInfo() { IP = clientIP, UserAgent = userAgent, RemoteHost = remoteHost, SignupCountry = country };
        }

        public SubscriptionChangeManager(List<PaidApp> existnigPaidApps)
        {
            _existingPaidApps = existnigPaidApps;
        }


        //public ShopSubscription ShopSubscription { get; set; }

        //public ContractSettings Contract { get; set; }
        //public double TotalSubscriptionCharge { get; set; }
        //public bool IsRemovingApp { get; set; }
        //public SubscriptionChangeTypes ChangeType { get; private set; }
        //public SubscriptionChangeManager(int shopID, int appID, int planID = -1) //if not send plan - remove app
        //{
        //    this.ShopID = shopID;
        //    this.AppID = appID; // current app 
        //    this.PlanID = planID;
        //    this.IsRemovingApp = (planID==-1);
        //    //StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    this.ShopSubscription = SubscriptionManager.GetActiveShopSubscription(shopID);
        //    //this.ShopSubscription = SubscriptionManager.GetActiveShopSubscription(shopID, appID);
        //    this.ContractMethod = BluesnapHelper.ReverseMethodID(this.ShopSubscription.ContractID.Value, appID);
        //    this.TotalSubscriptionCharge = AppStoreManager.GetAppsPriceForSubscriptionExcludingProvidedAppId(shopID, appID);
        //    if (planID != -1)
        //    {
        //        this.Contract = BluesnapHelper.GetContract(planID, this.ContractMethod, appID);
        //        this.TotalSubscriptionCharge = this.Contract.Price + this.TotalSubscriptionCharge;
        //    }

        //    this.ChangeType = CalculateType(shopID, appID, planID);
        //}


        public RequiredActions GetRemoveChange(int appID)
        {

            RequiredActions actions = new RequiredActions();
            actions.ShopID = this.ShopID;
            actions.AppID = appID;
            actions.IsRemoveApp = true;

            var appQuery = this._existingPaidApps.Where(a => a.AppID == appID);
            if (appQuery.Any())
            {
                var app = appQuery.Single();
                ContractSettings contractToRemove = BluesnapHelper.GetContract(app.PlanID, app.ContractMethod, appID);

                var subscriptionToUpdate = GetSubscriptionToShare(contractToRemove, appID);
                if (subscriptionToUpdate != null)
                {
                    //no last app in subscription
                    SubscriptionUpdate update = new SubscriptionUpdate();
                    update.BsSubscriptionID = subscriptionToUpdate.BsSubscriptionID;
                    update.SubscriptionID = subscriptionToUpdate.SubscriptionID;
                    update.NewAmount = GetCurrentSubscriptionPrice(subscriptionToUpdate.SubscriptionID, appID);
                    update.NewNextBillingDate = null;

                    actions.RequiredUpdate = update;
                }
                else
                {
                    //cancel subscription
                    SubscriptionToCancel sub = new SubscriptionToCancel();
                    sub.BsSubscriptionID = app.BsSubscriptionID;
                    sub.SubscriptionID = app.SubscriptionID;

                    actions.RequiredSubscriptionToCancel = sub;
                }
            }
            return actions;
        }

        public RequiredActions GetChange(int appID, int newPlanID, int method, double customPrice = 0, int? oneTimePaymentContractID = null, int? agreeID = null, double customPriceWithTax = 0, double proratedPrice = 0, double proratedPriceWithTax = 0, int? notChangeableContractId = null, string useThisBlueSnapSubscriptionID = null)
        {
            RequiredActions actions = new RequiredActions();
            actions.WebInfo = _webInfo;
            actions.ShopID = this.ShopID;
            actions.AppID = appID;
            actions.PlanID = newPlanID;
            actions.AgreeID = agreeID.HasValue ? agreeID.Value : 0;
            if (customPriceWithTax == 0)
            {
                customPriceWithTax = customPrice;
            }
            FastSpringApiProvider fastSpringApiProvider = null;
            ContractSettings newContract = null;
            if (this.PaymentProvider == PaymentAdapterTypes.BlueSnap)
            {
                newContract = BluesnapHelper.GetContract(newPlanID, method, appID);
                if (notChangeableContractId.HasValue)
                {
                    newContract.ContractID = notChangeableContractId.Value;
                }
            }
            else if (this.PaymentProvider == PaymentAdapterTypes.FastSpring)
            {
                fastSpringApiProvider = new FastSpringApiProvider(this.ShopID);
                newContract = fastSpringApiProvider.GetContract(newPlanID, method, appID);
            }

            if (appID == (int)AppTypes.TrafficBooster)
            {

                actions.ContractID = newContract.ContractID;

                double tbPlanPrice = (customPrice == 0) ? newContract.Price : customPrice;

                var qExistingApp = this._existingPaidApps.Where(a => a.AppID == appID && (a.AgreeID ?? 0) == (agreeID ?? 0));
                if (qExistingApp.Any())
                {
                    //update exising subscription    
                    var app = qExistingApp.Single();
                    if (proratedPrice == 0)
                    {
                        OneTimePayment payment = CreateOneTimePayment(tbPlanPrice, oneTimePaymentContractID, customPriceWithTax,
                        app.BsSubscriptionCurrency, app.BsSubscriptionID, app.BsShopperID, app.FsAccountID);
                        actions.RequiredOneTimePayment = payment;
                    }
                    else
                    {
                        OneTimePayment payment = CreateOneTimePayment(proratedPrice, oneTimePaymentContractID, proratedPriceWithTax,
                        app.BsSubscriptionCurrency, app.BsSubscriptionID, app.BsShopperID, app.FsAccountID);
                        actions.RequiredOneTimePayment = payment;
                    }


                    SubscriptionUpdate update = new SubscriptionUpdate();
                    update.BsSubscriptionID = app.BsSubscriptionID;
                    update.SubscriptionID = app.SubscriptionID;
                    update.NewAmount = tbPlanPrice;
                    if (proratedPrice == 0)
                    {
                        update.NewNextBillingDate = DateTime.Now.AddMonths(1);
                    }
                    else
                    {
                        if (this.PaymentProvider == PaymentAdapterTypes.BlueSnap)
                        {
                            DateTime? nextCharge = new BlueSnapApi().GetSubCurptionNextChargeDate(app.BsSubscriptionID);
                            if (nextCharge.HasValue)
                            {
                                update.NewNextBillingDate = nextCharge.Value;
                            }
                            else
                            {
                                update.NewNextBillingDate = DateTime.Now.AddMonths(1);
                            }
                        }
                    }
                    update.Currency = app.BsSubscriptionCurrency;
                    if (notChangeableContractId.HasValue)
                    {
                        update.NewContractID = notChangeableContractId.Value;
                    }
                    else
                    {
                        switch (PaymentProvider)
                        {
                            case PaymentAdapterTypes.BlueSnap:
                                update.NewContractID = BluesnapHelper.GetContractIDForTrafficBooster(newPlanID);
                                break;
                            case PaymentAdapterTypes.FastSpring:
                                update.NewContractID = fastSpringApiProvider.GetContractIDForTrafficBooster(newPlanID, out string fsContactID, out FastSpringHelper.IntervalUnits intervalUnit);
                                break;

                        }

                    }

                    actions.RequiredUpdate = update;
                }
                else
                {
                    string currency = null;
                    string activeSubscriptionID = null;
                    List<ShopSubscription> existingSub = SubscriptionManager.GetActiveShopSubscriptions(this.ShopID, true, useThisBlueSnapSubscriptionID, hasShooperAndCurrency: true);
                    ShopSubscription subToExtractShopperData = existingSub.FirstOrDefault();
                    SubscriptionToCreate newSubscription = new SubscriptionToCreate();
                    newSubscription.Amount = tbPlanPrice;
                    newSubscription.TrialAmount = newContract.TrialPrice;
                    if (newContract.TrialPrice == 0 && !newContract.HasTrial)
                    {
                        //no trial price and no discount in initial price specified -> set reular price as first payment
                        newSubscription.TrialAmount = newSubscription.Amount;
                    }
                    newSubscription.ContractID = newContract.ContractID;

                    if (subToExtractShopperData != null)
                    {
                        currency = subToExtractShopperData.BlueSnapShopperCurrency;
                        if (subToExtractShopperData.PaymentAdapterType == PaymentAdapterTypes.BlueSnap.GetHashCode())
                        {
                            newSubscription.ShopperID = subToExtractShopperData.BlueSnapShopperID.Value;
                            activeSubscriptionID = subToExtractShopperData.BlueSnapSubscriptionID.ToString();
                        }
                        if (subToExtractShopperData.PaymentAdapterType == PaymentAdapterTypes.FastSpring.GetHashCode())
                        {
                            newSubscription.FsAccountID = subToExtractShopperData.FsAccountID;
                            activeSubscriptionID = subToExtractShopperData.FsSubscriptionID;
                        }

                    }
                    //OneTimePayment payment = CreateOneTimePayment(tbPlanPrice, oneTimePaymentContractID, customPriceWithTax,
                    //currency, activeSubscriptionID, shopperID);
                    //actions.RequiredOneTimePayment = payment;
                    //create subscription                   
                    newSubscription.Currency = currency;
                    actions.RequiredNewSubscription = newSubscription;

                }
            }
            else
            {
                //non TB apps

                var qExistingApp = this._existingPaidApps.Where(a => a.AppID == appID);
                if (qExistingApp.Any())
                {
                    //update exising app    
                    var app = qExistingApp.Single();

                    method = app.ContractMethod;
                    actions.ContractMethod = method;
                    actions.ContractID = newContract.ContractID;

                    if (IsUpgrade(app.PlanID, newPlanID))
                    {
                        var firstPayment = app.FirstPaymentDate ?? DateTime.Now;
                        var daysTillNextPayment = GetDaysTillNextPayment(app.ContractMethod, firstPayment);
                        var dayInBilingPeriod = ConractSettingsHelper.MethodToDays(method);
                        var percentagePeriodLeft = (double)daysTillNextPayment / dayInBilingPeriod;

                        if (percentagePeriodLeft != 0)
                        {
                            //upgrade - charge difference
                            OneTimePayment payment = new OneTimePayment();
                            payment.Amount = percentagePeriodLeft * (newContract.Price - app.AppPrice);
                            payment.Currency = app.BsSubscriptionCurrency;
                            payment.ShopperID = app.BsShopperID;
                            payment.BsSubscriptionID = app.BsSubscriptionID;
                            payment.FsAccountID = app.FsAccountID;
                            actions.RequiredOneTimePayment = payment;
                        }
                    }

                    SubscriptionUpdate update = new SubscriptionUpdate();
                    update.BsSubscriptionID = app.BsSubscriptionID;
                    update.SubscriptionID = app.SubscriptionID;
                    update.NewAmount = GetCurrentSubscriptionPrice(app.SubscriptionID, appID) + newContract.Price;
                    update.NewNextBillingDate = null;
                    update.Currency = app.BsSubscriptionCurrency;
                    actions.RequiredUpdate = update;
                }

                else
                {
                    //no existing subscription to update

                    //  ContractSettings newContract = BluesnapHelper.GetContract(newPlanID, method, appID);
                    actions.ContractMethod = method;
                    actions.ContractID = newContract.ContractID;

                    //add new app
                    var appToShareSubscription = GetSubscriptionToShare(newContract);

                    if (appToShareSubscription != null)
                    {
                        //get first payment for new app
                        OneTimePayment payment = new OneTimePayment();
                        payment.Amount = newContract.Price;
                        payment.Currency = appToShareSubscription.BsSubscriptionCurrency;
                        payment.ShopperID = appToShareSubscription.BsShopperID;
                        payment.FsAccountID = appToShareSubscription.FsAccountID;
                        payment.BsSubscriptionID = appToShareSubscription.BsSubscriptionID;
                        actions.RequiredOneTimePayment = payment;

                        //update an existing subscription
                        SubscriptionUpdate update = new SubscriptionUpdate();
                        update.BsSubscriptionID = appToShareSubscription.BsSubscriptionID;
                        update.SubscriptionID = appToShareSubscription.SubscriptionID;
                        update.Currency = appToShareSubscription.BsSubscriptionCurrency;
                        update.NewAmount = GetCurrentSubscriptionPrice(appToShareSubscription.SubscriptionID) + newContract.Price;
                        update.NewNextBillingDate = DateTime.Now.AddMonths(ConractSettingsHelper.MethodToMonthsPeriod(appToShareSubscription.ContractMethod));
                        actions.RequiredUpdate = update;

                    }
                    else
                    {
                        //create subscription
                        SubscriptionToCreate newSubscription = new SubscriptionToCreate();
                        newSubscription.Amount = newContract.Price;
                        newSubscription.TrialAmount = newContract.TrialPrice;
                        if (newContract.TrialPrice == 0 && !newContract.HasTrial)
                        {
                            //no trial price and no discount in initial price specified -> set reular price as first payment
                            newSubscription.TrialAmount = newSubscription.Amount;
                        }
                        newSubscription.ContractID = newContract.ContractID;
                        newSubscription.BsSubscriptionID = newSubscription.BsSubscriptionID;

                        List<ShopSubscription> existingSub = SubscriptionManager.GetActiveShopSubscriptions(this.ShopID, true, useThisBlueSnapSubscriptionID);
                        ShopSubscription subToExtractShopperData = existingSub.Where(s => s.BlueSnapShopperCurrency != null && s.BlueSnapShopperID != null).FirstOrDefault();
                        if (subToExtractShopperData != null)
                        {
                            newSubscription.Currency = subToExtractShopperData.BlueSnapShopperCurrency;
                            newSubscription.ShopperID = subToExtractShopperData.BlueSnapShopperID.Value;
                        }

                        actions.RequiredNewSubscription = newSubscription;

                    }
                }
            }
            return actions;
        }

        private static OneTimePayment CreateOneTimePayment(double customPrice, int? oneTimePaymentContractID,
            double customPriceWithTax, string subscriptionCurrency, string bsSubscriptionID, int bsShopperID, string FsAccountId = null)
        {
            OneTimePayment payment = new OneTimePayment();
            payment.Amount = customPrice;
            payment.AmountWithTax = customPriceWithTax; // TODO: remove tax from here
            payment.Currency = subscriptionCurrency;
            payment.ShopperID = bsShopperID;
            payment.ContractID = oneTimePaymentContractID;
            payment.BsSubscriptionID = bsSubscriptionID;
            payment.FsAccountID = FsAccountId;
            return payment;
        }

        public RequiredActions GetOneTimeChangeForTB(double customPrice, int oneTimePaymentContractID)
        {
            RequiredActions actions = new RequiredActions();
            actions.ShopID = this.ShopID;
            actions.AppID = (int)AppTypes.AdditionalService;
            actions.PlanID = (int)PlanTypes.CustomPlan;

            var qExistingApp = this._existingPaidApps.Where(a => a.AppID == (int)AppTypes.TrafficBooster);
            if (qExistingApp.Any())
            {
                //update exising app    
                var app = qExistingApp.Single();

                //TB - only upgrade is supported for now
                OneTimePayment payment = new OneTimePayment();
                payment.Amount = customPrice;
                payment.Currency = app.BsSubscriptionCurrency;
                payment.ShopperID = app.BsShopperID;
                payment.ContractID = oneTimePaymentContractID;
                payment.BsSubscriptionID = app.BsSubscriptionID;
                payment.FsAccountID = app.FsAccountID;
                actions.RequiredOneTimePayment = payment;
            }

            return actions;
        }

        private User GetUserDetails(int shopID)
        {
            User user = null;
            var db = DataHelper.GetStoreYaEntities();
            var shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            if (shop != null)
            {
                user = db.Users.Where(u => u.ID == shop.UserID).SingleOrDefault();
            }

            return user;
        }

        public static bool IsUpgrade(int currentPlanID, int newPlanID)
        {
            if (currentPlanID == (int)PlanTypes.CustomPlan)
            {
                //custom plan allows only upgrades for now.
                return true;
            }

            if (currentPlanID > 20) // belongs to widget plan types: Casual , Sporty , Elegant , Tuxedo
            {
                PlanTypes currentPlanType = (PlanTypes)currentPlanID;
                switch (currentPlanType)
                {
                    case PlanTypes.Casual:
                        currentPlanID = (int)PlanTypes.Economy;
                        break;
                    case PlanTypes.Sporty:
                        currentPlanID = (int)PlanTypes.Business;
                        break;
                    case PlanTypes.Elegant:
                        currentPlanID = (int)PlanTypes.FirstClass_2;
                        break;
                    case PlanTypes.Tuxedo:
                        currentPlanID = (int)PlanTypes.PrivateJet;
                        break;

                    default:
                        break;
                }
            }

            if ((currentPlanID == (int)PlanTypes.Economy || currentPlanID == (int)PlanTypes.Business || currentPlanID == (int)PlanTypes.FirstClass_2) && newPlanID == (int)PlanTypes.FirstClass_2)
                return true;

            if (currentPlanID == (int)PlanTypes.PrivateJet && newPlanID == (int)PlanTypes.FirstClass_2)
                return false;

            if (currentPlanID < newPlanID)
                return true;

            return false;
        }


        public static int GetDaysTillNextPayment(int method, DateTime firstPaymentDate)
        {
            int months = ConractSettingsHelper.MethodToMonthsPeriod(method);
            DateTime nextPayment = firstPaymentDate;
            if (nextPayment >= DateTime.Now)
            {
                return 0;
            }

            while (nextPayment < DateTime.Now)
            {
                nextPayment = nextPayment.AddMonths(months);
            }

            return (nextPayment - DateTime.Now).Days;
        }

        private PaidApp GetSubscriptionToShare(ContractSettings desiredContract, int currentAppIDToExclude = -1)
        {
            foreach (var app in _existingPaidApps)
            {
                if (app.ContractID == desiredContract.ContractID && app.AppID != currentAppIDToExclude)
                {
                    return app;
                }
            }
            return null;
        }

        private double GetCurrentSubscriptionPrice(int subscriptionID, int appToExclude = -1)
        {
            double price = 0;
            foreach (var app in _existingPaidApps)
            {
                if (app.SubscriptionID == subscriptionID && app.AppID != appToExclude)
                {
                    price += app.AppPrice;
                }
            }

            return price;
        }

        public static SubscriptionChangeTypes CalculateType(int shopID, int appID, int planID)
        {

            var app = AppStoreManager.GetAppSettings(shopID, appID);
            if (app == null || app.PaymentPlan == null)
            {
                return SubscriptionChangeTypes.AddNewApp;
            }
            else if (SubscriptionChangeManager.IsUpgrade(app.PaymentPlan.Value, planID))
            {
                return SubscriptionChangeTypes.UpgaradeExistingApp;
            }
            else
            {
                return SubscriptionChangeTypes.DowngradeExistingApp;
            }
        }

        private void LoadExistingApps(int shopID, string providerSubscriptionID = null)
        {
            if (this._existingPaidApps != null)
            {
                //allow existing apps to be filled from outsioe
                return;
            }
            List<PaidApp> list = new List<PaidApp>();
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var apps = AppStoreManager.GetShopApps(shopID);
            TbAppManager tbAppManager = new TbAppManager(shopID, false, null, false); // load also not paid channels
            //TODO:Agree  - change with agre functionality
            var subs = SubscriptionManager.GetActiveShopSubscriptions(shopID, providerSubscriptionID: providerSubscriptionID);
            foreach (var app in apps)
            {

                var sub = subs.Where(s => s.ID == app.SubscriptionID).SingleOrDefault();
                if (sub != null)
                {
                    PaidApp paidApp = new PaidApp();
                    paidApp.AppID = app.AppTypeID;
                    paidApp.AppPrice = Convert.ToDouble(app.AppPrice);
                    paidApp.SubscriptionID = app.SubscriptionID ?? 0;
                    paidApp.PlanID = app.PaymentPlan ?? 0;
                    if (sub.PaymentAdapterType == PaymentAdapterTypes.BlueSnap.GetHashCode())
                    {
                        paidApp.BsSubscriptionID = sub.BlueSnapSubscriptionID.ToString();
                        paidApp.ContractID = sub.ContractID ?? 0;
                        paidApp.ContractMethod = BluesnapHelper.ReverseMethodID(sub.ContractID ?? 0, app.AppTypeID);
                        paidApp.BsShopperID = sub.BlueSnapShopperID ?? 0;
                    }
                    if (sub.PaymentAdapterType == PaymentAdapterTypes.FastSpring.GetHashCode())
                    {
                        paidApp.BsSubscriptionID = sub.FsSubscriptionID;
                        paidApp.ContractMethod = FastSpringHelper.ReverseMethodID(shopID, sub.FsContractID, app.AppTypeID);
                        paidApp.FsAccountID = sub.FsAccountID;
                    }

                    paidApp.SubscriptionStartDate = sub.InsertedAt;
                    paidApp.FirstPaymentDate = GetFirstPaymentDate(app.PaymentPlan ?? 0, paidApp.ContractMethod, app.AppTypeID, paidApp.SubscriptionStartDate ?? DateTime.Now);
                    paidApp.BsSubscriptionCurrency = sub.BlueSnapShopperCurrency;

                    list.Add(paidApp);
                }

                //For TB app load also agreement subscription 
                if (app.AppTypeID == AppTypes.TrafficBooster.GetHashCode() && tbAppManager.HasAgreement)
                {
                    //if (tbAppManager.ChannelsStruct == TbAppManager.CHANNELS_STURCT.WITH_AGREEMENT)
                    //{
                    var agreeID = tbAppManager.GetAgreementChannel().AgreeID;
                    sub = subs.Where(s => s.AgreeID == agreeID).FirstOrDefault();
                    if (sub != null)
                    {
                        PaidApp paidApp = new PaidApp();
                        paidApp.AppID = app.AppTypeID;
                        paidApp.AppPrice = Convert.ToDouble(app.AppPrice);
                        paidApp.SubscriptionID = sub.ID;
                        paidApp.PlanID = app.PaymentPlan ?? 0;
                        if (sub.PaymentAdapterType == PaymentAdapterTypes.BlueSnap.GetHashCode())
                        {
                            paidApp.BsSubscriptionID = sub.BlueSnapSubscriptionID.ToString();
                            paidApp.ContractID = sub.ContractID ?? 0;
                            paidApp.ContractMethod = BluesnapHelper.ReverseMethodID(sub.ContractID ?? 0, app.AppTypeID);
                            paidApp.BsShopperID = sub.BlueSnapShopperID ?? 0;
                        }
                        if (sub.PaymentAdapterType == PaymentAdapterTypes.FastSpring.GetHashCode())
                        {
                            paidApp.BsSubscriptionID = sub.FsSubscriptionID;
                            paidApp.ContractMethod = FastSpringHelper.ReverseMethodID(shopID, sub.FsContractID, app.AppTypeID);
                            paidApp.FsAccountID = sub.FsAccountID;
                        }
                        paidApp.SubscriptionStartDate = sub.InsertedAt;
                        paidApp.FirstPaymentDate = GetFirstPaymentDate(app.PaymentPlan ?? 0, paidApp.ContractMethod, app.AppTypeID, paidApp.SubscriptionStartDate ?? DateTime.Now);
                        paidApp.BsSubscriptionCurrency = sub.BlueSnapShopperCurrency;

                        paidApp.AgreeID = agreeID;
                        list.Add(paidApp);
                    }
                    //}
                }
            }
            this._existingPaidApps = list;

        }

        private static DateTime GetFirstPaymentDate(int planID, int contMethod, int appID, DateTime subscriptionStart)
        {
            if (ConractSettingsHelper.HasTrial(planID, contMethod, appID))
            {
                return subscriptionStart.AddDays(14);
            }
            return subscriptionStart;
        }

    }


    public class PaidApp
    {
        public int AppID { get; set; }
        public double AppPrice { get; set; }
        public int PlanID { get; set; }
        public int SubscriptionID { get; set; }
        public string BsSubscriptionID { get; set; }
        public string BsSubscriptionCurrency { get; set; }
        public string FsAccountID { get; set; }
        public int BsShopperID { get; set; }
        public int ContractID { get; set; }
        public int ContractMethod { get; set; }
        public DateTime? FirstPaymentDate { get; set; }
        public DateTime? SubscriptionStartDate { get; set; }

        public int? AgreeID { get; set; }

        //public double AppPrice
        //{
        //    get
        //    {
        //        ContractSettings contract = BluesnapHelper.GetContract(this.PlanID, this.ContractMethod, this.AppID);
        //        return contract.Price;
        //    }
        //}
    }

    public class RequiredActions
    {
        public int ShopID { get; set; }
        public int AppID { get; set; }
        public int PlanID { get; set; }
        public int ContractMethod { get; set; }
        public int ContractID { get; set; }
        public bool IsRemoveApp { get; set; }
        public int AgreeID { get; set; }

        //public int SubscriptionID { get; set; }
        public OneTimePayment RequiredOneTimePayment { get; set; }
        public SubscriptionUpdate RequiredUpdate { get; set; }
        public SubscriptionToCreate RequiredNewSubscription { get; set; }
        public SubscriptionToCancel RequiredSubscriptionToCancel { get; set; }

        public WebInfo WebInfo { get; set; }

    }

    public class OneTimePayment
    {
        public string FsAccountID { get; set; }
        public int ShopperID { get; set; }
        public string Currency { get; set; }
        public double Amount { get; set; }
        public double AmountWithTax { get; set; }
        public int? ContractID { get; set; }
        public string BsSubscriptionID { get; set; }
        public PayPalExtention PayPalExtention { get; set; }
    }

    public class SubscriptionUpdate
    {
        public int SubscriptionID { get; set; }
        public string BsSubscriptionID { get; set; }
        public double NewAmount { get; set; }
        public DateTime? NewNextBillingDate { get; set; }
        public int? NewContractID { get; set; }
        public string Currency { get; set; }
    }

    public class SubscriptionToCancel
    {
        public int SubscriptionID { get; set; }
        public string BsSubscriptionID { get; set; }
    }

    public class SubscriptionToCreate
    {
        public string FsAccountID { get; set; }
        public int ShopperID { get; set; }
        public string Currency { get; set; }
        public double Amount { get; set; }
        public int ContractID { get; set; }
        public double TrialAmount { get; set; }
        public string BsSubscriptionID { get; set; }

        //public int? AgreeID { get; set; }
    }
    public class PayPalExtention
    {
        public string RemoteHost { get; set; }
        public string UserAgent { get; set; }
        public string CancelUrl { get; set; }
        public string ReturnUrl { get; set; }
    }
}
