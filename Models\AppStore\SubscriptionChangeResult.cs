﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;

namespace Storeya.Core.Models.AppStore
{
    public class SubscriptionChangeResult
    {
        public SubscriptionChangeTypes ChangeType { get; set; }

        public int ShopID { get; set; }
        //public int AppID { get; set; }
        //public int PlanID { get; set; }
        //public double AppPrice { get; set; }

        public double TotalSubscriptionCharge { get; set; }

        public bool IsUpdated { get; set; }
        public int? OneTimePaymentOrderId { get; set; }
        public Subscription SubscriptionData { get; set; }

        public class Subscription
        {
            public int SubscriptionID { get; set; }
            public string BsSubscriptionID { get; set; }
            public string  BsShopperID { get; set; }
            public string BsCurrency { get; set; }
            //public string BsContractID { get; set; }
        }

    }

    public enum SubscriptionChangeTypes
    {
        AddNewApp = 1,
        RemoveExistingApp = 2,
        UpgaradeExistingApp = 3,
        DowngradeExistingApp = 4
    }

}
