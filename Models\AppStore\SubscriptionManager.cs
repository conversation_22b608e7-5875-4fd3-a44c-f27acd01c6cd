﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;
using System.Text.RegularExpressions;
using System.Net;
using System.IO;

using Storeya.Core.Models.Payments;
using Storeya.Core.Models.Plimus;
using Storeya.Core.Models.Charges;
using Storeya.Core.Models.TrafficBoosterModels;
using Storeya.Core.Models.FastSpring;
using static Storeya.Core.Models.FastSpring.Models.FSSubscriptionsModel;
using static Storeya.Core.Models.Charges.BluesnapSubscriptions;
using static Storeya.Core.Models.AppStore.SubscriptionChangeResult;
using System.Globalization;
using static Storeya.Core.Models.ShopAttributes.Attributes.Apps;
using System.Threading;

namespace Storeya.Core.Models.AppStore
{
    public class SubscriptionManager
    {
        public static bool SetSubscriptionAsActiveInBudget(int subDbId, out string message)
        {
            message = string.Empty;
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                var sub = db.ShopSubscriptions.SingleOrDefault(b => b.ID == subDbId);
                if (sub == null)
                {
                    message = $"Cannot find subscription with db id:{subDbId}";
                    return false;
                }
                int appId = Convert.ToInt32(sub.AppIDs);
                ResetShopActiveInBudget(sub.ShopID, appId, sub.AgreeID);
                sub.ActiveInBudget = 1;
                sub.UpdatedAt = DateTime.Now;
                ShopApp appToChange = AppStoreManager.GetAppSettings(sub.ShopID, appId);
                appToChange.SubscriptionID = sub.ID;
                db.SaveChanges();
                message = $"Subscription db id:{subDbId} provider id:{sub.OriginalSubscriptionID} - is set as active";
                return true;
            }
            catch (Exception ex)
            {
                message = $"Subscription db id:{subDbId} failed to set as actibe:{ex}";
            }
            return false;
        }

        public static bool UseBlueSnapPayment(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(s => s.ID == shopID).Single();
            User user = db.Users.Where(u => u.ID == shop.UserID).Single();
            if (user.OriginMarketplace == (int)Storeya.Core.OriginMarketplaces.Shopify
                || shop.CatalogSourcePlatform == (int)CatalogSourcePlatforms.Tictail)
            {
                return false;
            }
            return true;
        }


        public static bool HasOneDollarContract(int shopID)
        {
            IBluesnapConfiguration bluesnapConfiguration = new BlueSnapApi().BluesnapConfiguration;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<ShopSubscription> subscriptions = db.ShopSubscriptions.Where(s => s.ShopID == shopID).ToList();
            if (subscriptions != null && subscriptions.Count > 0 && subscriptions.Where(x => x.ContractID == bluesnapConfiguration.OneDollarPerDayMonthly || x.ContractID == (int)bluesnapConfiguration.OneDollarPerDayAnnual).Any())
            {
                return true;
            }
            return false;
        }
        public static ShopSubscription GetShopSubscriptionById(string providerSubscriptionID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var sub = db.ShopSubscriptions.Where(s => s.FsSubscriptionID == providerSubscriptionID).SingleOrDefault();
            if (sub == null)
            {
                if (int.TryParse(providerSubscriptionID, out int bsSubscriptionID))
                {
                    sub = db.ShopSubscriptions.Where(s => s.BlueSnapSubscriptionID == bsSubscriptionID).SingleOrDefault();
                }
            }
            return sub;
        }
        public static ShopSubscription GetShopSubscriptionByBSId(int? blueSnapSubscriptionID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            return db.ShopSubscriptions.Where(s => s.BlueSnapSubscriptionID == blueSnapSubscriptionID).SingleOrDefault();
        }
        public static ShopSubscription GetShopSubscription(int subscriptionID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            return db.ShopSubscriptions.Where(s => s.ID == subscriptionID).SingleOrDefault();
        }

        public static ShopSubscription GetShopAgreementSubscription(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            return db.ShopSubscriptions.Where(s => s.ShopID == shopID && (s.AgreeID.HasValue && s.AgreeID.Value > 0) && s.Status == 1).SingleOrDefault();
        }
        public static ShopSubscription UpdateShopSubscriptionStatus(string blueSnapSubscriptionID, SUBSCRIPTION_STATUS status)
        {
            return UpdateShopSubscriptionStatus(blueSnapSubscriptionID, status, out string appIds);
        }
        public static ShopSubscription UpdateShopSubscriptionStatus(string blueSnapSubscriptionID, SUBSCRIPTION_STATUS status, out string appIds)
        {
            appIds = null;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            if (!int.TryParse(blueSnapSubscriptionID, out int subscriptionId))
            {
                return null;
            }
            var shopSubscription = db.ShopSubscriptions.Where(s => s.BlueSnapSubscriptionID == subscriptionId).SingleOrDefault();
            if (shopSubscription != null)
            {
                appIds = shopSubscription.AppIDs;
                shopSubscription.Status = status.GetHashCode();
                shopSubscription.UpdatedAt = DateTime.Now;
                db.SaveChanges();
            }
            return shopSubscription;
        }
        public static ShopSubscription UpdateCustomContractType(string subscriptionId, CUSTOM_CONTRACT_TYPE customContractType)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();

            var shopSubscription = db.ShopSubscriptions.Where(s => s.OriginalSubscriptionID == subscriptionId).SingleOrDefault();
            if (shopSubscription != null)
            {
                if (customContractType == CUSTOM_CONTRACT_TYPE.NONE)
                {
                    shopSubscription.CustomContractType = null;
                }
                else
                {
                    shopSubscription.CustomContractType = customContractType.GetHashCode();
                }
                shopSubscription.UpdatedAt = DateTime.Now;
                db.SaveChanges();
            }
            return shopSubscription;
        }
        public static bool IsShopLastAgreementSubscription(int shopID, string blueSnapSubscriptionID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var subs = db.ShopSubscriptions.Where(s => s.ShopID == shopID && (s.AgreeID.HasValue && s.AgreeID.Value > 0)).OrderByDescending(s => s.InsertedAt);
            if (subs.Any())
            {
                if (subs.First().OriginalSubscriptionID == blueSnapSubscriptionID)
                {
                    return true;
                }
            }
            return false;
        }

        public static ShopSubscription GetShopSubscription(int shopID, string appID, int? agreeId = null)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            if (agreeId.HasValue)
            {
                var q = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AgreeID == agreeId && s.AppIDs == appID).OrderByDescending(s => s.ActiveInBudget ?? 0).OrderByDescending(s => s.InsertedAt);
                if (q.Any(s => s.Status == 1))
                {
                    return q.FirstOrDefault(s => s.Status == 1);
                }
                else
                {
                    return q.FirstOrDefault();
                }
            }
            else
            {
                var q = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == appID && (!s.AgreeID.HasValue || s.AgreeID.Value == 0)).OrderByDescending(s => s.ActiveInBudget ?? 0).ThenByDescending(s => s.InsertedAt);


                //var ll = q.ToList();
                if (q.Any(s => s.Status == 1))
                {
                    return q.FirstOrDefault(s => s.Status == 1);
                }
                else
                {
                    return q.FirstOrDefault();
                }
            }

            //return null;
        }

        public static List<ShopSubscription> GetActiveShopSubscriptions(int shopID, bool noneCancelState = false, string providerSubscriptionID = null, bool hasShooperAndCurrency = false)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            if (!string.IsNullOrEmpty(providerSubscriptionID))
            {
                List<ShopSubscription> l = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.OriginalSubscriptionID == providerSubscriptionID).OrderByDescending(s => s.InsertedAt).ToList();
                if (l.Count == 0)
                {
                    l = db.ShopSubscriptions.Where(s => s.ShopID == shopID &&
                            s.BlueSnapSubscriptionID.HasValue ? s.BlueSnapSubscriptionID.ToString() == providerSubscriptionID : s.FsSubscriptionID == providerSubscriptionID).OrderByDescending(s => s.InsertedAt).ToList();
                }
                if (hasShooperAndCurrency)
                {
                    l = l.Where(s => s.BlueSnapShopperCurrency != null && (s.BlueSnapShopperID != null || s.FsAccountID != null)).ToList();
                }
                return l;
            }
            if (noneCancelState)
            {
                return db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.Status != (int)SUBSCRIPTION_STATUS.CANCEL).OrderByDescending(s => s.InsertedAt).ToList();
            }
            return db.ShopSubscriptions.Where(s => s.ShopID == shopID).OrderByDescending(s => s.InsertedAt).ToList();
        }


        public static List<ShopSubscription> GetActiveShopAppSubscriptions(int shopID, int appID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            return db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == appID.ToString()).OrderByDescending(s => s.InsertedAt).ToList();
        }

        public static ShopSubscription GetActiveShopTBLastSubscription_Anyprovider(int shopID, int? agreeId, AppTypes appType = AppTypes.TrafficBooster, bool getCancelled = false)
        {

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            int tbAppId = appType.GetHashCode();
            if (agreeId.HasValue && agreeId > 0)
            {
                var aSub = db.ShopSubscriptions.SingleOrDefault(s => s.ShopID == shopID && s.AppIDs == tbAppId.ToString() && s.AgreeID == agreeId.Value && s.Status == 1 && s.ActiveInBudget == 1);
                if (aSub != null)
                {
                    return aSub;
                }
                var subs = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == tbAppId.ToString() && s.AgreeID == agreeId.Value && s.Status == 1).ToList();
                if (subs != null && subs.Count() > 0)
                {
                    return subs.OrderByDescending(s => s.InsertedAt).First();
                }
                if (getCancelled)
                {
                    return db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == tbAppId.ToString() && s.AgreeID == agreeId.Value && s.Status == -1).OrderByDescending(s => s.InsertedAt).First();
                }
                return null;

            }
            var sub = db.ShopSubscriptions.SingleOrDefault(s => s.ShopID == shopID && s.AppIDs == tbAppId.ToString() && (s.AgreeID == null || s.AgreeID == 0) && s.Status == 1 && s.ActiveInBudget == 1);
            if (sub != null)
            {
                return sub;
            }

            var app = db.ShopApps.SingleOrDefault(a => a.ShopID == shopID && a.AppTypeID == tbAppId);
            ShopSubscription res = null;
            if (app != null)
            {
                res = db.ShopSubscriptions.SingleOrDefault(s => s.ID == app.SubscriptionID);
            }
            if (res == null && getCancelled)
            {
                var susCancelled = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == tbAppId.ToString() && (s.AgreeID == null || s.AgreeID == 0) && s.Status == -1);
                if (susCancelled != null)
                {
                    return susCancelled.OrderByDescending(s => s.InsertedAt).SingleOrDefault();
                }
            }
            return res;
        }

        public static void ResetShopActiveInBudget(int shopID, int appId, int? agreeId)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            IEnumerable<ShopSubscription> shopSubscriptions = null;
            if (agreeId.HasValue && agreeId > 0)
            {
                shopSubscriptions = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == appId.ToString() && s.AgreeID == agreeId.Value);
            }
            else
            {
                shopSubscriptions = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == appId.ToString() && (!s.AgreeID.HasValue || s.AgreeID == 0));
            }

            if (shopSubscriptions != null)
            {
                foreach (var sub in shopSubscriptions)
                {
                    if (sub.ActiveInBudget == 1)
                    {
                        sub.ActiveInBudget = 0;
                    }
                    else
                    {
                        sub.ActiveInBudget = null;
                    }
                }
                db.SaveChanges();
                return;
            }
        }
        //TODO:use GetTBLastShopSubscription for TB
        public static ShopSubscription GetLastShopSubscription(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var subs = db.ShopSubscriptions.Where(s => s.ShopID == shopID).OrderByDescending(s => s.InsertedAt);
            if (subs.Any())
            {
                return subs.First();
            }
            return null;

        }
        public static ShopSubscription GetTBLastShopSubscription(int shopID, int? agreeId, string blueSnapSubscriptionID = null, PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.BlueSnap)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            if (!string.IsNullOrEmpty(blueSnapSubscriptionID))
            {
                switch (paymentProvider)
                {
                    case PaymentAdapterTypes.FastSpring:
                        return db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == "204"
                        && s.FsSubscriptionID == blueSnapSubscriptionID)
                        .OrderByDescending(s => s.InsertedAt).FirstOrDefault();
                    case PaymentAdapterTypes.BlueSnap:
                    default:
                        return db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == "204"
                         && s.BlueSnapSubscriptionID.HasValue && s.BlueSnapSubscriptionID.ToString() == blueSnapSubscriptionID)
                        .OrderByDescending(s => s.InsertedAt).FirstOrDefault();
                }

            }
            if (agreeId.HasValue && agreeId > 0)
            {
                var sub = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == "204" && s.AgreeID == agreeId).OrderByDescending(s => s.InsertedAt);
                if (sub.Any())
                {
                    return sub.First();
                }
            }
            var q = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == "204" && (s.AgreeID == null || s.AgreeID == 0)).OrderByDescending(s => s.InsertedAt);
            if (q.Any())
            {
                return q.First();
            }
            return null;
        }

        public static ShopSubscription GetLastFsShopSubscription(int shopID, int? agreeId, string originSubscriptionID = null)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            if (!string.IsNullOrEmpty(originSubscriptionID))
            {
                return db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == "204" && s.OriginalSubscriptionID == originSubscriptionID)
                    .OrderByDescending(s => s.InsertedAt).FirstOrDefault();
            }

            if (agreeId > 0)
            {
                var sub = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == "204" && s.AgreeID == agreeId)
                    .OrderByDescending(s => s.InsertedAt);
                if (sub.Any())
                {
                    return sub.First();
                }
            }

            var q = db.ShopSubscriptions.Where(s => s.ShopID == shopID && s.AppIDs == "204" && (s.AgreeID == null || s.AgreeID == 0))
                .OrderByDescending(s => s.InsertedAt);
            return q.FirstOrDefault();
        }

        public static int SaveToDB(int appID, int shopID, int planID, string bsSubscriptinID, string bsContractID, string bsShopperID, string bsCurrency,
            string price, string paymentMethod, string agreeID = null,
            SUBSCRIPTION_STATUS status = SUBSCRIPTION_STATUS.ACTIVE, DateTime? nextPaymenDate = null, decimal? nextPaymentAmount = null, string chargeFrequency = null, string bsStatus = null, PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.BlueSnap)
        {
            ResetShopActiveInBudget(shopID, appID, Helper.GetIntOrNull(agreeID));
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopSubscription subscription = new ShopSubscription()
            {
                AppIDs = appID.ToString(),
                OriginalSubscriptionID = bsSubscriptinID,
                BlueSnapShopperCurrency = bsCurrency,
                InsertedAt = DateTime.Now,
                PlanIDs = planID.ToString(),
                ShopID = shopID,
                Status = status.GetHashCode(),
                ContractPrice = price,
                PaymentMethod = paymentMethod,
                PaymentAdapterType = paymentProvider.GetHashCode(),
                AgreeID = Helper.GetIntOrNull(agreeID),
                ActiveInBudget = 1,
                NextPaymenDate = nextPaymenDate,
                NextPaymentAmount = nextPaymentAmount,
                ChargeFrequency = chargeFrequency

            };



            switch (paymentProvider)
            {
                case PaymentAdapterTypes.BlueSnap:
                    subscription.ContractID = Helper.GetIntOrNull(bsContractID);
                    subscription.BlueSnapShopperID = Helper.GetIntOrNull(bsShopperID);
                    subscription.BlueSnapSubscriptionID = Helper.GetIntOrNull(bsSubscriptinID);
                    break;
                case PaymentAdapterTypes.FastSpring:
                    subscription.ContractID = FastSpringHelper.GetContacrtID(bsContractID);
                    subscription.FsAccountID = bsShopperID;
                    subscription.FsSubscriptionID = bsSubscriptinID;
                    subscription.BsStatus = FastSpringHelper.GetSubscriptionBsStatus(bsStatus);
                    subscription.Autorenew = 0;
                    switch (bsStatus)
                    {
                        case "C":
                            subscription.Status = SUBSCRIPTION_STATUS.CANCEL.GetHashCode();
                            break;
                        case "A":
                            subscription.Status = SUBSCRIPTION_STATUS.ACTIVE.GetHashCode();
                            break;
                        default:
                            subscription.Status = SUBSCRIPTION_STATUS.UNKNOWN.GetHashCode();
                            break;
                    }
                    break;
            }
            db.ShopSubscriptions.Add(subscription);
            db.SaveChanges();

            return subscription.ID;
        }

        public static bool UpdateShopSubscriptionStatus(int shopId, string originSubscriptionId, double? newPrice, SUBSCRIPTION_STATUS status, out DateTime? nextChargeDate, bool updateNextCharge = false, LastChargeStatus? lastChargeStatus = null)
        {
            nextChargeDate = null;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopSubscription existingSubscription = db.ShopSubscriptions.Where(sub => sub.OriginalSubscriptionID == originSubscriptionId && sub.ShopID == shopId).FirstOrDefault();
            if (existingSubscription != null)
            {
                bool update = false;
                if (updateNextCharge)
                {
                    nextChargeDate = FastSpringHelper.GetNextChargeDate(existingSubscription.ChargeFrequency);
                    existingSubscription.NextPaymenDate = nextChargeDate.Value;
                    update = true;
                }
                if (existingSubscription.Status != status.GetHashCode())
                {
                    existingSubscription.Status = status.GetHashCode();
                    update = true;
                }
                if (lastChargeStatus.HasValue)
                {
                    existingSubscription.LastChargeStatus = lastChargeStatus.Value.GetHashCode();
                    if (lastChargeStatus.Value == LastChargeStatus.Charged)
                    {
                        existingSubscription.LastChargeTriedAt = DateTime.Now;
                    }
                    update = true;
                }
                if (newPrice.HasValue)
                {
                    update = true;
                    existingSubscription.OverridedContractPrice = newPrice.ToString();
                }
                if (update)
                {
                    existingSubscription.UpdatedAt = DateTime.Now;
                    db.SaveChanges();
                    Log4NetLogger.InfoWithDB($"Update ShopSubscription Status:{status} SubscriptionId:{originSubscriptionId} newPrice:{newPrice} LastChargeStatus:{lastChargeStatus}", null, shopId);
                    return true;
                }
            }
            return false;
        }
        public static bool FreezeOrRenewSubcription(string subscriptionID, bool atoRenew, int shopID, PaymentAdapterTypes? paymentAdapterTypes = null)
        {
            bool isUpdated = false;
            if (paymentAdapterTypes == null)
            {
                paymentAdapterTypes = FastSpringHelper.GetPaymentAdapterType(shopID, subscriptionID);
            }
            try
            {
                switch (paymentAdapterTypes)
                {
                    case PaymentAdapterTypes.FastSpring:
                        FastSpringApiProvider fastSpringApiProvider = new FastSpringApiProvider(shopID);
                        fastSpringApiProvider.CancelSubscription(subscriptionID);
                        break;
                    case PaymentAdapterTypes.None:
                    case PaymentAdapterTypes.BlueSnap:
                    default:
                        //Log4NetLogger.Info(string.Format("The BsSubscription {0} will be freezed in BlueSnap.", subscriptionID), shopID);
                        BlueSnapApi bluesnapApi = new BlueSnapApi();
                        isUpdated = bluesnapApi.FreezeOrRenewSubcription(subscriptionID, atoRenew, shopID);
                        break;
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteErrorWithDB(string.Format("Failed to update Subscription {2} ID: {0} with atoRenew: {1}", subscriptionID, atoRenew, paymentAdapterTypes.ToString()), ex, shopID);
                string message = "";
                if (atoRenew == false)
                {
                    message = string.Format("Failed to freeze {2} subscription <a href=\"https://bo.storeya.com/Payments/SubscriptionDetails?shopID={1}&blueSnapSubscriptionID={0}\">{0}</a> automatically. <br/> Please, review shop <a href=\"https://bo.storeya.com/Shop/Logs/{1}\">logs</a> and <b>set AtoRenew to FALSE on {2} side manually</b> to avoid the subsequent RECURRING payment.<br/><a href=\"https://bo.storeya.com/shop/subscriptions/{1}\">View All Shop Subcriptions</a>", subscriptionID, shopID, paymentAdapterTypes.ToString());
                }
                else
                {
                    message = string.Format("Failed to enable AutoRenew for {2} subscription <a href=\"https://bo.storeya.com/Payments/SubscriptionDetails?shopID={1}&blueSnapSubscriptionID={0}\">{0}</a> automatically.<br/> Please, review shop <a href=\"https://bo.storeya.com/Shop/Logs/{1}\">logs</a> and <b>set AtoRenew to TRUE on {2} side manually</b> to enable the subsequent RECURRING payment.<br/><a href=\"https://bo.storeya.com/shop/subscriptions/{1}\">View All Shop Subcriptions</a>", subscriptionID, shopID, paymentAdapterTypes.ToString());
                }
                EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, string.Format("AUDIT REQUIRED: Changing BlueSnapSubscriptionID: {0} ShopID: {1} - {2}", subscriptionID, shopID, DateTime.Now.ToShortDateString()), message, null, null, true, "SYSTEM");
            }

            return isUpdated;
        }


        //public static string GetBlueSnapSubscriptionIDForVeteran(int shopID)
        //{
        //    string bsSubscriptionToFreeze = null;

        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //    List<PlimusIpnCall> ipnRecords = db.PlimusIpnCalls.Where(i => i.ShopID == shopID && !string.IsNullOrEmpty(i.subscriptionId) &&
        //        (i.TransactionType == "AUTH_ONLY" || i.TransactionType == "CHARGE" || i.TransactionType == "RECURRING" || i.TransactionType == "UNDER_REVIEW")).ToList();
        //    if (ipnRecords != null && ipnRecords.Count > 0)
        //    {
        //        bsSubscriptionToFreeze = ipnRecords.OrderByDescending(i => i.TransactionDate).First().subscriptionId;
        //    }
        //    else
        //    {
        //        Log4NetLogger.Info("BsSubscriptionId to freeze was not discovered for veteran client.", shopID);
        //    }

        //    return bsSubscriptionToFreeze;
        //}

        public static PlimusIpnCall GetBlueSnapSubscriptionData(int shopID, int? agreeId = null, string subscriptionId = null)
        {
            PlimusIpnCall bsSubscription = null;

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            List<PlimusIpnCall> ipnRecords = null;
            List<string> TransactionsType = new List<string>() {
                "SUBSCRIPTION_CHARGE_FAILURE",
                "AUTH_ONLY",
                "CHARGE",
                "RECURRING",
                "UNDER_REVIEW"
            };
            if (string.IsNullOrEmpty(subscriptionId))
            {
                if (agreeId.HasValue && agreeId.Value > 0)
                {
                    ipnRecords = db.PlimusIpnCalls.Where(i => i.ShopID == shopID && i.AgreeID == agreeId && TransactionsType.Contains(i.TransactionType)).ToList();
                }
                else
                {
                    ipnRecords = db.PlimusIpnCalls.Where(i => i.ShopID == shopID && (i.AgreeID == null || i.AgreeID == 0) && TransactionsType.Contains(i.TransactionType)).ToList();
                }
            }
            else
            {

                if (agreeId.HasValue && agreeId.Value > 0)
                {
                    ipnRecords = db.PlimusIpnCalls.Where(i => i.ShopID == shopID && i.AgreeID == agreeId && !string.IsNullOrEmpty(i.subscriptionId) &&
                    i.subscriptionId == subscriptionId && TransactionsType.Contains(i.TransactionType)).ToList();
                }
                else
                {
                    ipnRecords = db.PlimusIpnCalls.Where(i => i.ShopID == shopID && (i.AgreeID == null || i.AgreeID == 0) && !string.IsNullOrEmpty(i.subscriptionId) &&
                    i.subscriptionId == subscriptionId && TransactionsType.Contains(i.TransactionType)).ToList();
                }
            }
            if (ipnRecords == null || ipnRecords.Count == 0)
            {
                throw new Exception(string.Format("Cannot find BsSubscriptionId:{1} for shopId:{0}", shopID, subscriptionId));
            }
            bsSubscription = ipnRecords.OrderByDescending(i => i.TransactionDate).First();
            return bsSubscription;
        }

        public static bool UpdateSubscriptionStatus(string subscriptionID, BluesnapSubscriptionStatus status, int shopID, PaymentAdapterTypes? paymentAdapterTypes = null)
        {
            bool isUpdated = false;
            try
            {
                if (paymentAdapterTypes == null)
                {
                    paymentAdapterTypes = FastSpringHelper.GetPaymentAdapterType(shopID, subscriptionID);
                }
                switch (paymentAdapterTypes)
                {
                    case PaymentAdapterTypes.FastSpring:
                        FastSpringApiProvider fastSpringApiProvider = new FastSpringApiProvider(shopID);
                        if (status == BluesnapSubscriptionStatus.Cancelled)
                        {
                            var res = fastSpringApiProvider.CancelSubscription(subscriptionID, true);
                            if (res != null && res.subscriptions != null && res.subscriptions.FirstOrDefault() != null)
                            {
                                if (res.subscriptions.FirstOrDefault().result == "success")
                                {
                                    isUpdated = true;
                                }
                            }
                        }
                        break;
                    case PaymentAdapterTypes.None:
                    case PaymentAdapterTypes.BlueSnap:
                    default:
                        BlueSnapApi bluesnapApi = new BlueSnapApi();
                        isUpdated = bluesnapApi.UpdateSubscriptionStatus(subscriptionID, status, shopID);
                        break;
                }

            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to update SubscriptionID: {0} with status: {1}", subscriptionID, status), ex, shopID);
            }
            return isUpdated;
        }

        //public static bool CancelAvangateSubscription(string subscriptionID, int shopID)
        //{
        //    bool isCancelled = false;
        //    try
        //    {
        //        //AvangatePaymentAdapter paymentAdapter = new AvangatePaymentAdapter();
        //        //isCancelled = paymentAdapter.CancelSubscription(subscriptionID);
        //    }
        //    catch (Exception ex)
        //    {
        //        Log4NetLogger.Error(string.Format("Failed to cancel Avangate subscription. subscriptionID: {0}", subscriptionID), ex, shopID);
        //    }

        //    return isCancelled;
        //}
        public static BluesnapSubscription GetSubscriptionWithCC(int shopId, string tryFirstBsSubscriptionId)
        {
            BluesnapSubscription sub = GetSubscriptionFromBlueSnap(tryFirstBsSubscriptionId, shopId);
            if (sub != null && string.IsNullOrEmpty(sub.CardType) && string.IsNullOrEmpty(sub.CardLastFourDigits))
            {
                List<ShopSubscription> existingSubs = GetActiveShopSubscriptions(shopId, true);
                foreach (var existingSub in existingSubs)
                {
                    sub = GetSubscriptionFromBlueSnap(existingSub.BlueSnapSubscriptionID.ToString(), shopId);
                    if (sub != null && !string.IsNullOrEmpty(sub.CardType) && !string.IsNullOrEmpty(sub.CardLastFourDigits))
                    {
                        EmailHelper.SendEmail("<EMAIL>", string.Format("Subscription was change since no CC details : ShopID: {0}, original subscriptionID: {1}- Used subscriptionID {2}", shopId, tryFirstBsSubscriptionId, sub.SubscriptionID), sub.ToHtmlTable("Used Subscrition For CC details"), null, null, true, "SYSTEM");
                        return sub;
                    }
                }
            }
            return sub;
        }

        public static SubscriptionChangeResult UpdateFastSpring(RequiredActions changes, out string error, bool isPayPal = false)
        {
            error = "";
            if (ConfigHelper.GetBoolValue("FastSpring_SkipRealPayment"))
            {
                return GetFakeResultToContinueFlow(changes);
            }

            SubscriptionChangeResult result = new SubscriptionChangeResult();
            result.SubscriptionData = new SubscriptionChangeResult.Subscription();
            result.IsUpdated = true;
            bool chargeNow = false;
            try
            {
                FastSpringApiProvider api = new FastSpringApiProvider(changes.ShopID);
                //not required for PayPal -- start
                bool checkIfProrated = false;
                if (changes.RequiredOneTimePayment != null)
                {
                    checkIfProrated = true;
                    chargeNow = true;
                }
                if (result.IsUpdated == true && changes.RequiredUpdate != null)
                {
                    var subUpdate = changes.RequiredUpdate;

                    //TODO: add "if" for TB app and check change contract only in case this is first upgrade

                    //TbAppManager.CHARGE_FREQUENCY chargeFrequency = TbAppManager.GetShopChargeFrequency(changes.ShopID, subUpdate.BsSubscriptionID);
                    //string billingDate = null;
                    //if (chargeFrequency == TbAppManager.CHARGE_FREQUENCY.WEEKLY)
                    //{
                    //    //  BluesnapSubscription sub = GetSubscriptionFromBlueSnap(subUpdate.BsSubscriptionID, changes.ShopID);
                    //    FSSubscription sub = api.GetSubscription(subUpdate.BsSubscriptionID);
                    //    if (sub.product != FastSpringHelper.GetContacrtName(subUpdate.NewContractID))
                    //    {
                    //        //subUpdate.NewContractID = sub.SkuID;
                    //        //billingDate = sub.NextChargeDate;

                    //        //don't change
                    //        subUpdate.NewContractID = null;
                    //        //a week from now
                    //        billingDate = DateTime.Now.Date.AddDays(7).ToString("dd-MMM-yy");
                    //    }
                    //    Log4NetLogger.Info(string.Format("UpdateFastSpring: UpdateRecuringSubscription - {0} Weekly charge frequency Contract:{1} will not be changed!", changes.ShopID, subUpdate.NewContractID), changes.ShopID);
                    //    //string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
                    //    //EmailHelper.SendEmail(email, string.Format("Upgrade Subscription: Changing Weekly shop BlueSnapSubscriptionID: {0} ShopID: {1} - {2}", subUpdate.BsSubscriptionID, changes.ShopID, DateTime.Now.ToShortDateString()), string.Format("UpdateBlueSnap2: UpdateRecuringSubscription - Shop:{0} Weekly charge, Contract:{1} will not be changed!", changes.ShopID, subUpdate.NewContractID), null, null, true, "SYSTEM");
                    //}
                    //else
                    //{
                    //    if (subUpdate.NewNextBillingDate.HasValue)
                    //    {
                    //        billingDate = (subUpdate.NewNextBillingDate ?? DateTime.Now).ToString("dd-MMM-yy");
                    //    }
                    //}

                    //fix contract id
                    //if (subUpdate.NewContractID != null)
                    //{
                    //    subUpdate.NewContractID = BluesnapHelper.GetFixedContract(subUpdate.NewContractID.ToString(), changes.WebInfo.SignupCountry);
                    //}

                    Log4NetLogger.Info("UpdateFastSpring: UpdateSubscription - newAmount:" + subUpdate.NewAmount + " nextbilling: " + subUpdate.NewNextBillingDate + " NewContractID: " + subUpdate.NewContractID, changes.ShopID);
                    bool prorated = false;
                    if (checkIfProrated)
                    {
                        if (changes.RequiredOneTimePayment.Amount != subUpdate.NewAmount)
                        {
                            prorated = true;
                        }
                    }
                    result.IsUpdated = api.UpdateAndChargeSubscription(subUpdate.BsSubscriptionID, subUpdate.NewContractID, subUpdate.NewAmount, subUpdate.NewNextBillingDate, prorated, chargeNow, changes.RequiredOneTimePayment.Amount);
                    result.SubscriptionData.BsCurrency = subUpdate.Currency;
                    result.TotalSubscriptionCharge = subUpdate.NewAmount;
                    result.SubscriptionData.BsSubscriptionID = subUpdate.BsSubscriptionID;
                    result.SubscriptionData.SubscriptionID = subUpdate.SubscriptionID;
                }

                if (result.IsUpdated == true && changes.RequiredNewSubscription != null)
                {
                    //create subscription
                    var newSub = changes.RequiredNewSubscription;


                    // BluesnapSubscription sub = GetSubscriptionFromBlueSnap(newSub.BsSubscriptionID, changes.ShopID);
                    string message = string.Format("shopperID: {0}, shopperCurrency: {1}, amountInUSD: {2}, trialAmount: {5}, sku_id: {3}, hasTrial: {4}", newSub.ShopperID, newSub.Currency, newSub.Amount, newSub.ContractID, "?", newSub.TrialAmount);
                    Log4NetLogger.Info(string.Format("UpdateFastSpring: CreateRecuringSubscription - {0}", message), changes.ShopID);
                    PlaceOrderResponse response = null;
                    //if (sub != null)
                    //{
                    //    response = api.CreateRecuringSubscriptionWithCCData(newSub.ShopperID.ToString(), newSub.Currency, newSub.Amount, (long)newSub.ContractID, newSub.TrialAmount, changes.ShopID, changes.AppID, changes.PlanID, sub.CardLastFourDigits, sub.CardType, changes.AgreeID, 0, changes.WebInfo);
                    //}
                    //else
                    //{

                    //TODO:FASTSPRING NEED TO IMPLEMENT 
                    response = api.CreateRecuringSubscription(newSub.FsAccountID, newSub.Currency, newSub.Amount, newSub.ContractID, newSub.TrialAmount, changes.AppID, changes.PlanID, changes.AgreeID, 0, changes.WebInfo);
                    //}

                    result.IsUpdated = true;
                    result.SubscriptionData.BsSubscriptionID = (response.SubscriptionID ?? -1).ToString();
                    //result.SubscriptionData.BsContractID = newSub.ContractID.ToString();
                    result.SubscriptionData.BsCurrency = newSub.Currency;
                    result.SubscriptionData.BsShopperID = newSub.ShopperID.ToString();
                    result.TotalSubscriptionCharge = newSub.Amount;

                }
            }
            catch (WebException ex)
            {
                string metaData = "";
                try
                {
                    metaData = changes.ToJson();
                }
                catch
                {

                }
                string errorMessage = "";
                WebResponse response = ex.Response;
                using (StreamReader reader =
                            new StreamReader(response.GetResponseStream()))
                {
                    errorMessage = reader.ReadToEnd();
                }

                result.IsUpdated = false;
                // error = errorMessage.Replace("<?xml version=\"1.0\" encoding=\"UTF-8\" sta ndalone=\"yes\"?><messages xmlns=\"http://ws.plimus.com\">", "");
                ConsoleAppHelper.WriteErrorWithDB($"Failed on updating FastSpring Subscription. {errorMessage} , {metaData}", ex, changes.ShopID);
            }
            catch (Exception ex)
            {
                string metaData = "";
                try
                {
                    metaData = changes.ToJson();
                }
                catch
                {

                }
                result.IsUpdated = false;
                ConsoleAppHelper.WriteErrorWithDB($"Failed on updating FastSpring Subscription. {metaData}", ex, changes.ShopID);
            }

            return result;
        }
        public static SubscriptionChangeResult UpdateBlueSnap2(RequiredActions changes, out string error, bool isPayPal = false)
        {
            error = "";
            if (ConfigHelper.GetBoolValue("BlueSnap_SkipRealPayment"))
            {
                return GetFakeResultToContinueFlow(changes);
            }

            SubscriptionChangeResult result = new SubscriptionChangeResult();
            result.SubscriptionData = new SubscriptionChangeResult.Subscription();
            result.IsUpdated = true;
            try
            {
                BlueSnapApi api = new BlueSnapApi();
                //not required for PayPal -- start
                if (changes.RequiredOneTimePayment != null && !isPayPal)
                {
                    var payment = changes.RequiredOneTimePayment;
                    BluesnapSubscription sub = GetSubscriptionWithCC(changes.ShopID, payment.BsSubscriptionID);

                    Log4NetLogger.Info(string.Format("UpdateBlueSnap2: One time order for {0}. ShopperID: {1}, Currency: {2},AmountWithTax:{3},ContractID (before fix):{4}", payment.Amount, payment.ShopperID, payment.Currency, payment.AmountWithTax, payment.ContractID), changes.ShopID);
                    int? orderID = null;
                    if (sub != null && !string.IsNullOrEmpty(sub.CardType) && !string.IsNullOrEmpty(sub.CardLastFourDigits))
                    {
                        orderID = api.PlaceOrderWithCCData(payment.ShopperID.ToString(), payment.Currency, payment.Amount, changes.ShopID, changes.AppID, changes.PlanID, sub.CardLastFourDigits, sub.CardType, payment.ContractID, changes.AgreeID, payment.AmountWithTax, changes.WebInfo);
                    }
                    else
                    {
                        //TODO: check logs - may be this is not working code
                        Log4NetLogger.Error("Using api.PlaceOrder", (int)Log4NetLogger.SpecialShopIDs.AppStorePayments);
                        orderID = api.PlaceOrder(payment.ShopperID.ToString(), payment.Currency, payment.Amount, changes.ShopID, changes.AppID, changes.PlanID, payment.ContractID, changes.AgreeID, payment.AmountWithTax, changes.WebInfo);
                    }
                    result.OneTimePaymentOrderId = orderID;
                }
                //not required for PayPal -- end
                if (result.IsUpdated == true && changes.RequiredUpdate != null)
                {
                    var subUpdate = changes.RequiredUpdate;

                    //TODO: add "if" for TB app and check change contract only in case this is first upgrade

                    TbAppManager.CHARGE_FREQUENCY chargeFrequency = TbAppManager.GetShopChargeFrequency(changes.ShopID, subUpdate.BsSubscriptionID);
                    string billingDate = null;
                    if (chargeFrequency == TbAppManager.CHARGE_FREQUENCY.WEEKLY)
                    {
                        BluesnapSubscription sub = GetSubscriptionFromBlueSnap(subUpdate.BsSubscriptionID, changes.ShopID);
                        if (sub.SkuID != subUpdate.NewContractID)
                        {
                            //subUpdate.NewContractID = sub.SkuID;
                            //billingDate = sub.NextChargeDate;

                            //don't change
                            subUpdate.NewContractID = null;


                        }
                        //a week from now
                        billingDate = DateTime.Now.Date.AddDays(7).ToString("dd-MMM-yy");
                        Log4NetLogger.Info(string.Format("UpdateBlueSnap2: UpdateRecuringSubscription - {0} Weekly charge frequency Contract:{1} will not be changed!", changes.ShopID, subUpdate.NewContractID), changes.ShopID);
                        //string email = ConfigHelper.GetValue("paymentsEmail", "<EMAIL>");
                        //EmailHelper.SendEmail(email, string.Format("Upgrade Subscription: Changing Weekly shop BlueSnapSubscriptionID: {0} ShopID: {1} - {2}", subUpdate.BsSubscriptionID, changes.ShopID, DateTime.Now.ToShortDateString()), string.Format("UpdateBlueSnap2: UpdateRecuringSubscription - Shop:{0} Weekly charge, Contract:{1} will not be changed!", changes.ShopID, subUpdate.NewContractID), null, null, true, "SYSTEM");
                    }
                    else
                    {
                        if (subUpdate.NewNextBillingDate.HasValue)
                        {
                            billingDate = (subUpdate.NewNextBillingDate ?? DateTime.Now).ToString("dd-MMM-yy");
                        }
                    }

                    //fix contract id
                    if (subUpdate.NewContractID != null)
                    {
                        subUpdate.NewContractID = BluesnapHelper.GetFixedContract(subUpdate.NewContractID.ToString(), changes.WebInfo.SignupCountry);
                    }

                    Log4NetLogger.Info("UpdateBlueSnap2: UpdateSubscription - newAmount:" + subUpdate.NewAmount + " nextbilling: " + billingDate + " NewContractID: " + subUpdate.NewContractID, changes.ShopID);
                    result.IsUpdated = api.UpdateSubscription(subUpdate.BsSubscriptionID.ToString(), subUpdate.NewAmount, billingDate, changes.ShopID, subUpdate.NewContractID, subUpdate.Currency);
                    result.SubscriptionData.BsCurrency = subUpdate.Currency;
                    result.TotalSubscriptionCharge = subUpdate.NewAmount;
                    result.SubscriptionData.BsSubscriptionID = subUpdate.BsSubscriptionID;
                    result.SubscriptionData.SubscriptionID = subUpdate.SubscriptionID;
                }

                if (result.IsUpdated == true && changes.RequiredNewSubscription != null)
                {
                    //create subscription
                    var newSub = changes.RequiredNewSubscription;
                    //bool hasTrial = BluesnapHelper.HasTrial(newSub.ContractID);
                    //if (!hasTrial && newSub.TrialAmount == 0)
                    //{
                    //    newSub.TrialAmount = newSub.Amount;
                    //}

                    BluesnapSubscription sub = GetSubscriptionFromBlueSnap(newSub.BsSubscriptionID, changes.ShopID);
                    string message = string.Format("shopperID: {0}, shopperCurrency: {1}, amountInUSD: {2}, trialAmount: {5}, sku_id: {3}, hasTrial: {4}", newSub.ShopperID, newSub.Currency, newSub.Amount, newSub.ContractID, "?", newSub.TrialAmount);
                    Log4NetLogger.Info(string.Format("UpdateBlueSnap2: CreateRecuringSubscription - {0}", message), changes.ShopID);
                    PlaceOrderResponse response = null;
                    if (sub != null)
                    {
                        response = api.CreateRecuringSubscriptionWithCCData(newSub.ShopperID.ToString(), newSub.Currency, newSub.Amount, (long)newSub.ContractID, newSub.TrialAmount, changes.ShopID, changes.AppID, changes.PlanID, sub.CardLastFourDigits, sub.CardType, changes.AgreeID, 0, changes.WebInfo);
                    }
                    else
                    {
                        response = api.CreateRecuringSubscription(newSub.ShopperID.ToString(), newSub.Currency, newSub.Amount, (long)newSub.ContractID, newSub.TrialAmount, changes.ShopID, changes.AppID, changes.PlanID, changes.AgreeID, 0, changes.WebInfo);
                    }

                    result.IsUpdated = true;
                    result.SubscriptionData.BsSubscriptionID = (response.SubscriptionID ?? -1).ToString();
                    //result.SubscriptionData.BsContractID = newSub.ContractID.ToString();
                    result.SubscriptionData.BsCurrency = newSub.Currency;
                    result.SubscriptionData.BsShopperID = newSub.ShopperID.ToString();
                    result.TotalSubscriptionCharge = newSub.Amount;

                }
            }
            catch (WebException ex)
            {
                string metaData = "";
                try
                {
                    metaData = changes.ToJson();
                }
                catch
                {

                }
                string errorMessage = "";
                WebResponse response = ex.Response;
                using (StreamReader reader =
                            new StreamReader(response.GetResponseStream()))
                {
                    errorMessage = reader.ReadToEnd();
                }

                result.IsUpdated = false;
                error = errorMessage.Replace("<?xml version=\"1.0\" encoding=\"UTF-8\" sta ndalone=\"yes\"?><messages xmlns=\"http://ws.plimus.com\">", "");
                ConsoleAppHelper.WriteErrorWithDB($"Failed on updating BlueSnapSubscription. {errorMessage} , {metaData}", ex, changes.ShopID);
            }
            catch (Exception ex)
            {
                string metaData = "";
                try
                {
                    metaData = changes.ToJson();
                }
                catch
                {

                }
                result.IsUpdated = false;
                ConsoleAppHelper.WriteErrorWithDB($"Failed on updating BlueSnapSubscription. {metaData}", ex, changes.ShopID);
            }

            return result;
        }
        public static FSSubscription GetSubscriptionFromFastSpring(string fsSubscriptionID, int shopID)
        {
            FSSubscription sub = null;
            FastSpringApiProvider api = new FastSpringApiProvider(shopID);
            try
            {
                sub = api.GetSubscription(fsSubscriptionID);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                Log4NetLogger.Error(string.Format("Failed to get FastSpring subscription of subscriptionID: {0}", fsSubscriptionID), ex, shopID);
            }
            return sub;
        }
        public static BluesnapSubscription GetSubscriptionFromBlueSnap(string bsSubscriptionID, int shopID)
        {
            BluesnapSubscription sub = null;
            BlueSnapApi api = new BlueSnapApi();
            try
            {
                sub = api.RetreiveSubscriptionObject(bsSubscriptionID);
            }
            catch (Exception ex)
            {
                Log4NetLogger.Error(string.Format("Failed to get subscriptionObject of subscriptionID: {0}", bsSubscriptionID), ex, shopID);
            }
            return sub;
        }

        private static SubscriptionChangeResult GetFakeResultToContinueFlow(RequiredActions changes)
        {
            SubscriptionChangeResult result = new SubscriptionChangeResult();
            result.IsUpdated = true;
            result.TotalSubscriptionCharge = 100;

            result.SubscriptionData = new SubscriptionChangeResult.Subscription();
            result.SubscriptionData.BsCurrency = "USD";
            result.SubscriptionData.BsShopperID = "1000";
            result.SubscriptionData.BsSubscriptionID = "2000";
            result.SubscriptionData.SubscriptionID = 0; // new one will be added

            return result;
        }

        private static double CalculateAmountToGetForUpgrade(SubscriptionChange subscription, ShopApp app)
        {
            double paidPrice = Convert.ToDouble(app.AppPrice ?? "0");
            double newPrice = subscription.Contract.Price;

            int bilingPeriod = ConractSettingsHelper.MethodToMonthsPeriod(subscription.ContractMethod);
            DateTime existingStarted = app.PaymentPlanUpdatedAt ?? app.TrialEndsAt ?? DateTime.Now.AddMonths(-bilingPeriod);
            DateTime existingEnds = existingStarted.AddMonths(bilingPeriod);

            if (app.AppTypeID == (int)AppTypes.FacebookShop && subscription.ContractMethod == (int)ContractMethods.Yearly)
            {
                if ((DateTime.Now - existingStarted).Days < 14)
                {
                    return 0; //trial is not ended yet
                }
            }

            double periodLeft = (double)((existingEnds - DateTime.Now).Days) / (existingEnds - existingStarted).Days;

            double amountToGetNow = (newPrice - paidPrice) * periodLeft;//calculate realtive number
            return amountToGetNow;
        }

        //public static bool ApproveFbShop(SubscriptionChange subscription)
        //{
        //    bool isUpdated = false;
        //    try
        //    {
        //        isUpdated = AddNewApp(subscription);

        //    }
        //    catch (Exception ex)
        //    {
        //        Log4NetLogger.Error("Failed on place an order for Facebook Shop app and update an existing subscription.", ex, subscription.ShopID);
        //    }
        //    return isUpdated;
        //}

        //private static bool AddNewApp(SubscriptionChange subscription)
        //{
        //    bool isUpdated = false;
        //    Log4NetLogger.Info(string.Format("Placing order for {0} app and updating an existing subscription.", AppStoreManager.GetAppByID(subscription.AppID).AppName), subscription.ShopID);
        //    BlueSnapApi api = new BlueSnapApi();
        //    //place order for new added app
        //    double amountToGetNow = subscription.Contract.Price;
        //    string shopperID = subscription.ShopSubscription.BlueSnapShopperID.ToString();
        //    string bsSubscriptionID = subscription.ShopSubscription.BlueSnapSubscriptionID.ToString();

        //    BluesnapSubscription sub = GetSubscriptionFromBlueSnap(bsSubscriptionID, subscription.ShopID);
        //    int? orderID = null;
        //    if (sub != null)
        //    {
        //        orderID = api.PlaceOrderWithCCData(shopperID, subscription.ShopSubscription.BlueSnapShopperCurrency, amountToGetNow, subscription.ShopID, subscription.AppID, (subscription.PlanID ?? 0), sub.CardLastFourDigits, sub.CardType);
        //    }
        //    else
        //    {
        //        orderID = api.PlaceOrder(shopperID, subscription.ShopSubscription.BlueSnapShopperCurrency, amountToGetNow, subscription.ShopID, subscription.AppID, (subscription.PlanID ?? 0));
        //    }

        //    Log4NetLogger.Info(string.Format("An orderid: {0} was placed in BlueSnap with amount: {1} for upgrading {2} shop app.", orderID, amountToGetNow, AppStoreManager.GetAppByID(subscription.AppID).AppName), subscription.ShopID);

        //    //change subscription amount and next date
        //    string subscription_next_charge_date = GetSubscriptionCurrentNextChargeDate(subscription.ContractMethod);

        //    string logSecondCall = string.Format("Running {0}. UpdateSubscription for amount = {1} and changeing next billing for {2}", subscription.ChangeType.ToString(), subscription.TotalSubscriptionCharge, subscription_next_charge_date);
        //    Log4NetLogger.Info(logSecondCall, subscription.ShopID);
        //    isUpdated = api.UpdateSubscription(bsSubscriptionID, subscription.TotalSubscriptionCharge, subscription_next_charge_date, subscription.ShopID);
        //    return isUpdated;
        //}


        public static string GetSubscriptionCurrentNextChargeDate(int contractMethod)
        {
            string subscription_next_charge_date = null;
            DateTime nextChargeDate = DateTime.Now.AddMonths(ConractSettingsHelper.MethodToMonthsPeriod(contractMethod)); ;

            subscription_next_charge_date = nextChargeDate.ToString("dd-MMM-yy");

            return subscription_next_charge_date;
        }

        private static string GetMatchValue(string content, string tagName)
        {
            Match match = Regex.Match(content, @"<" + tagName + ">.*?</" + tagName + ">", RegexOptions.Singleline);
            if (match.Success)
            {
                string tagValue = SetMatchValue(match, "<" + tagName + ">", "</" + tagName + ">");
                return tagValue;
            }
            return null;
        }
        private static string SetMatchValue(Match match, string openingTag, string closingTag)
        {
            string value = match.Value.Remove(0, openingTag.Length);
            int indexOfClosingTag = value.IndexOf(closingTag);
            value = value.Remove(indexOfClosingTag, closingTag.Length);
            return value;
        }

        public static void Update(int shopId, int appId, int? agreeId, int subscriptionID, double newPrice, decimal? nextPaymentAmount = null, bool updateNextPaymenDate = false, LastChargeStatus? lastChargeStatus = null)
        {
            ResetShopActiveInBudget(shopId, appId, agreeId);
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopSubscription subscription = db.ShopSubscriptions.Where(s => s.ID == subscriptionID).Single();
            subscription.UpdatedAt = DateTime.Now;
            subscription.OverridedContractPrice = newPrice.ToString();
            subscription.ActiveInBudget = 1;
            if (nextPaymentAmount.HasValue)
            {
                subscription.NextPaymentAmount = nextPaymentAmount.Value;
            }
            if (lastChargeStatus.HasValue)
            {
                subscription.LastChargeStatus = (int)lastChargeStatus.Value;
            }
            if (updateNextPaymenDate)
            {

                subscription.NextPaymenDate = FastSpringHelper.GetNextChargeDate(subscription.ChargeFrequency);
            }
            subscription.UpdatedAt = DateTime.Now;
            db.SaveChanges();
        }

        public static void UpdateNextPaymentInAllSubscriptions(int shopId)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            foreach (var sub in db.ShopSubscriptions.Where(s => s.ShopID == shopId).ToList())
            {
                switch (sub.PaymentAdapterType)
                {
                    case (int)PaymentAdapterTypes.FastSpring:
                        if (!string.IsNullOrEmpty(sub.FsSubscriptionID))
                        {
                            UpdateNextPayment(shopId, sub.FsSubscriptionID, PaymentAdapterTypes.FastSpring);
                        }
                        break;
                    case (int)PaymentAdapterTypes.BlueSnap:
                    default:
                        if (sub.BlueSnapSubscriptionID.HasValue)
                        {
                            UpdateNextPayment(shopId, sub.BlueSnapSubscriptionID.Value.ToString());
                        }
                        break;
                }

            }
        }

        public static ShopSubscription UpdateNextPayment(int shopId, string bsSubscriptionId, PaymentAdapterTypes? paymentProvider = null)
        {
            if (string.IsNullOrEmpty(bsSubscriptionId))
            {
                return null;
            }
            DateTime? nextChargeDate = null;
            string nextCharge = "0";
            string chargefrequency = null;
            bool autorenew = false;
            string status = null;
            bool adhoc = false;
            if (paymentProvider == null)
            {
                paymentProvider = FastSpringHelper.GetPaymentAdapterType(shopId, bsSubscriptionId);
            }
            if (paymentProvider == PaymentAdapterTypes.BlueSnap)
            {
                BlueSnapApi bs = new BlueSnapApi();
                var bsSubscription = bs.RetrieveSubscriptionObject(bsSubscriptionId);
                if (bsSubscription == null)
                {
                    return null;
                }

                if (!string.IsNullOrEmpty(bsSubscription.nextchargedate))
                {
                    if (DateTime.TryParse(bsSubscription.nextchargedate, out DateTime oNextChargeDate))
                    {
                        nextChargeDate = oNextChargeDate;
                    }
                }

                if (bsSubscription.overriderecurringcharge == null)
                {
                    if (bsSubscription.catalogrecurringcharge != null)
                    {
                        nextCharge = bsSubscription.catalogrecurringcharge.amount.ToString();
                    }
                }
                else
                {
                    nextCharge = bsSubscription.overriderecurringcharge.amount.ToString();
                }
                chargefrequency = bsSubscription.chargefrequency;
                autorenew = bsSubscription.autorenew;
                status = bsSubscription.status;
            }
            if (paymentProvider == PaymentAdapterTypes.FastSpring)
            {
                FastSpringApiProvider fs = new FastSpringApiProvider(shopId);
                var sub = fs.GetSubscription(bsSubscriptionId);

                //if (!string.IsNullOrEmpty(sub.nextChargeDateDisplay))
                //{
                //    if (DateTime.TryParse(sub.nextChargeDateDisplay, out DateTime oNextChargeDate))
                //    {
                //        nextChargeDate = oNextChargeDate;
                //    }

                //}
                if (sub.adhoc)
                {
                    adhoc = true;
                    //nextChargeDate = DateTime.Now;
                    //if (sub.tags.NextCharge != null)
                    //{
                    //    nextChargeDate = DateTime.ParseExact(sub.tags.NextCharge, FastSpringHelper.DATE_FORMAT_DISPLAY, System.Globalization.CultureInfo.InvariantCulture);
                    //}
                    //chargefrequency = FastSpringHelper.GetChargeFrequency(sub.tags.ChargeFrequency);
                }
                else
                {
                    nextChargeDate = sub.next.Value.FromUnixDateTime();
                    chargefrequency = FastSpringHelper.GetChargeFrequency(sub.intervalUnit);
                }
                nextCharge = sub.nextChargeTotal.ToString();
                autorenew = sub.autoRenew;
                status = FastSpringHelper.GetSubscriptionBsStatus(sub.state); //fs state is not state - it's status 
            }
            if (adhoc)
            {
                return FastSpringHelper.GetShopSubscription(shopId, bsSubscriptionId);
            }
            else
            {
                return UpdateNextPayment(shopId, bsSubscriptionId, nextChargeDate, nextCharge, chargefrequency, autorenew, status);

            }

        }

        private static ShopSubscription UpdateNextPayment(int shopId, string bsSubscriptionId, DateTime? nextPaymenDate,
            string nextPaymentAmount, string chargeFrequency, bool autorenew, string bsStatus)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopSubscription subscription = null;

            if (int.TryParse(bsSubscriptionId, out int subscriptionId))
            {
                subscription = db.ShopSubscriptions.Where(s => s.ShopID == shopId && s.BlueSnapSubscriptionID == subscriptionId).SingleOrDefault();
            }
            else
            {
                subscription = db.ShopSubscriptions.Where(s => s.ShopID == shopId && s.FsSubscriptionID == bsSubscriptionId).SingleOrDefault();
            }


            if (subscription == null)
            {
                return null;
            }
            subscription.UpdatedAt = DateTime.Now;
            subscription.ChargeFrequency = chargeFrequency;
            subscription.NextPaymenDate = nextPaymenDate;
            decimal dNextPaymentAmount = 0;
            decimal.TryParse(nextPaymentAmount, out dNextPaymentAmount);
            subscription.NextPaymentAmount = dNextPaymentAmount;
            subscription.Autorenew = autorenew ? 1 : 0;
            subscription.BsStatus = bsStatus;
            switch (bsStatus)
            {
                case "C":
                    subscription.Status = SUBSCRIPTION_STATUS.CANCEL.GetHashCode();
                    break;
                case "A":
                    subscription.Status = SUBSCRIPTION_STATUS.ACTIVE.GetHashCode();
                    break;
                default:
                    subscription.Status = SUBSCRIPTION_STATUS.UNKNOWN.GetHashCode();
                    break;
            }
            db.SaveChanges();
            return subscription;
        }
        ////Looks like not in use
        //private static bool UpdateInDB(int subscriptionID, int planID, string price, int shopID, int appID)
        //{
        //    bool isUpdated = false;

        //    StoreYaEntities db = DataHelper.GetStoreYaEntities();

        //    //ShopSubscription subscription = db.ShopSubscriptions.Where(ss => ss.ID == subscriptionID).SingleOrDefault();
        //    ShopSubscription subscription = SubscriptionManager.GetLastShopSubscription(shopID);
        //    if (subscription != null)
        //    {
        //        try
        //        {
        //            if (!subscription.AppIDs.Split(',').Contains(appID.ToString()))
        //            {
        //                subscription.AppIDs = subscription.AppIDs.TrimEnd(',') + "," + appID.ToString();
        //            }
        //            if (!subscription.PlanIDs.Split(',').Contains(planID.ToString()))
        //            {
        //                subscription.PlanIDs = subscription.PlanIDs.TrimEnd(',') + "," + planID.ToString();
        //            }
        //            subscription.OverridedContractPrice = price;
        //            subscription.UpdatedAt = DateTime.Now;
        //            db.SaveChanges();
        //            isUpdated = true;
        //        }
        //        catch (Exception ex)
        //        {
        //            Log4NetLogger.Error(string.Format("Failed to update a bluesnap subscription ID: {0} for {1} shop app with price: {2}", subscriptionID, AppStoreManager.GetAppByID(appID).AppName, price), ex, shopID);
        //        }
        //    }
        //    return isUpdated;
        //}

        internal static List<ShopApp> GetAppsByBsSubscription(int shopID, string subscriptionID)
        {
            List<ShopApp> paidApps = new List<ShopApp>();

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            //if (int.TryParse(subscriptionID, out int subId))
            //{
            var sub = db.ShopSubscriptions.Where(s => s.OriginalSubscriptionID == subscriptionID).FirstOrDefault();
            if (sub != null)
            {
                List<ShopApp> apps = AppStoreManager.GetShopApps(shopID);
                if (apps != null)
                {
                    foreach (var app in apps)
                    {
                        if (app.SubscriptionID == sub.ID)
                        {
                            paidApps.Add(app);
                        }
                    }
                }
            }

            return paidApps;
            //}
            //throw new Exception("GetAppsByBsSubscription Cannot Parse to int subscriptionID:" + subscriptionID);
        }


        public static void ReplaceInDB(int subscriptionID, string newBsSubscriptionID, string oldBsSubscriptionID, string newBsContractID, string price)
        {

            Log4NetLogger.Info(string.Format("Replace BS subscription {0} with {1}", oldBsSubscriptionID, newBsSubscriptionID));

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var subscription = db.ShopSubscriptions.Where(s => s.ID == subscriptionID).Single();
            subscription.UpdatedAt = DateTime.Now;
            subscription.BlueSnapSubscriptionID = Helper.GetIntOrNull(newBsSubscriptionID);
            subscription.ContractID = Helper.GetIntOrNull(newBsContractID);
            subscription.ContractPrice = price;

            db.SaveChanges();

        }


        public static bool IsPaymentGateWaySubscription(int? subscriptionID)
        {
            if (subscriptionID.HasValue && subscriptionID.Value > 0
                        && subscriptionID.Value != (int)SpecificSubscriptionID.Shopify
                        && subscriptionID.Value != (int)SpecificSubscriptionID.Tictail
                        && subscriptionID.Value != (int)SpecificSubscriptionID.Wix)
            {
                return true;
            }

            return false;
        }

        public static TrafficBoosterModels.TrafficChannels.AccountTbBudget GetBudgetByBlueSnapSubscription(string blueSnapSubscriptionId, out string results)
        {
            results = string.Empty;
            if (!int.TryParse(blueSnapSubscriptionId, out int subscriptionId))
            {
                results = string.Format("blueSnapSubscriptionId is not in the correct format:{0}", blueSnapSubscriptionId);
                return null;
            }
            var subscription = GetShopSubscriptionByBSId(subscriptionId);
            if (subscription == null)
            {
                results = string.Format("cannot find ShopSubscriptions related to the blueSnapSubscriptionId:{0}", blueSnapSubscriptionId);
                return null;
            }

            TbAppManager tbAppManager = new TbAppManager(subscription.ShopID, onlyIfActiveAndPaid: false);
            TrafficBoosterModels.TrafficChannels.AccountTbBudget budget = null;
            if (subscription.AgreeID.HasValue && subscription.AgreeID.Value > 0)
            {
                budget = tbAppManager.GetAgreementChannel();
            }
            else
            {
                budget = tbAppManager.GetDefaultChannel();
            }
            if (budget == null)
            {
                results = string.Format("cannot find budget related to the ShopID,:{0}", subscription.ShopID);
                return null;
            }

            return budget;

        }

        public static bool SetSubscriptionsToOneDollar(string blueSnapSubscriptionId, out string results)
        {
            try
            {
                var budget = GetBudgetByBlueSnapSubscription(blueSnapSubscriptionId, out results);
                DateTime sixMonthAgo = DateTime.Now.AddMonths(-6);
                DateTime threeMonthAgo = DateTime.Now.AddMonths(-3);
                if (budget == null)
                {
                    return false;
                }
                if (budget.LastPaymentDate < threeMonthAgo && budget.LastPaymentDate > sixMonthAgo)
                {
                    BlueSnapApi bluesnapApi = new BlueSnapApi();
                    string chargeDate = DateTime.Now.AddYears(2).ToString("dd-MMM-yy");
                    if (bluesnapApi.UpdateSubscription(blueSnapSubscriptionId.ToString(), 1, chargeDate, budget.ShopID))
                    {
                        results = string.Format("blueSnapSubscriptionId: {1} ,LastPaymentDate: {2} was set to 1$ and next charge date:{3} ,ShopID,:{0}", budget.ShopID, blueSnapSubscriptionId, budget.LastPaymentDate, chargeDate);
                        return true;
                    }
                }
                results = string.Format("Last Payment Date is not in the threshold ShopID,:{0} , LastPaymentDate:{1} , threshold min:{2}, max:{3}", budget.ShopID, budget.LastPaymentDate, threeMonthAgo, sixMonthAgo);
                return false;
            }
            catch (Exception ex)
            {
                results = string.Format("failed to proccess blueSnapSubscriptionId:{1} exception:{0}", ex.ToString(), blueSnapSubscriptionId);
                return false;
            }
        }
        public static void CancelSixMonthsAgoActiveBlueSnapSubscription(int? filterShopId = null, bool onlyPreview = false)
        {
            StringBuilder sb = new StringBuilder();
            DateTime sixMonthAgo = DateTime.Now.AddMonths(-6);
            string prev = onlyPreview ? " - Its only Preview" : "";
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                var reportingTbs = (from t in db.TrafficBoosters.Where(t =>
                            t.Status != (int)TB_AW_STATUS.CANCELED && t.Status > (int)TB_AW_STATUS.WAITING_TO_RUN_FIRST_TIME
                            && t.AppStatus == (int)TB_APP_STATUS.ACTIVE
                            && t.LastPaymentDate.HasValue && t.LastPaymentDate.Value < sixMonthAgo
                            )
                                    select new { ShopID = t.ShopID, tbId = t.ID, t.LastPaymentDate }).ToList();

                if (filterShopId.HasValue)
                {
                    reportingTbs = reportingTbs.Where(t => t.ShopID == filterShopId).ToList();
                }
                Console.WriteLine($"Checking Total of:{reportingTbs.Count} Shops.");
                BlueSnapApi blueSnapApi = new BlueSnapApi();
                bool notifyAbout = false;

                foreach (var tb in reportingTbs)
                {

                    UpdateNextPaymentInAllSubscriptions(tb.ShopID.Value);
                    var subs = db.ShopSubscriptions.Where(s => s.ShopID == tb.ShopID && (s.AgreeID == null || s.AgreeID == 0) && s.AppIDs == "204" && s.BsStatus == "A").ToList();
                    if (subs != null && subs.Count > 0)
                    {
                        Console.WriteLine(tb.ToJson());
                        foreach (var sub in subs)
                        {
                            sb.Append(string.Format("Subscription {1} was cancel - ShopID,:{0} ,LastPaymentDate: {2}<br/>", EmailHelper.GetBoLinkHref(sub.ShopID, "subscriptions"), sub.BlueSnapSubscriptionID, tb.LastPaymentDate));
                            if (!onlyPreview)
                            {
                                try
                                {
                                    ConsoleAppHelper.WriteLogWithDB($"Canceling subscription older then 6 months:{sub.BlueSnapSubscriptionID}", sub.ShopID);
                                    blueSnapApi.UpdateSubscriptionStatus(sub.BlueSnapSubscriptionID.Value.ToString(), BluesnapSubscriptionStatus.Cancelled, sub.ShopID);
                                    UpdateShopSubscriptionStatus(sub.BlueSnapSubscriptionID.Value.ToString(), SUBSCRIPTION_STATUS.CANCEL);
                                }
                                catch (Exception ex)
                                {
                                    sb.Append(string.Format("Subscription Failed to cancel - ShopID,:{0} - blueSnapSubscriptionId: {1} ,LastPaymentDate: {2} ex:{3}<br/>", EmailHelper.GetBoLinkHref(sub.ShopID, "subscriptions"), sub.BlueSnapSubscriptionID, tb.LastPaymentDate, ex));
                                }
                            }
                        }

                        if (subs.Count > 5)
                        {
                            //too many cancelations
                            notifyAbout = true;
                        }
                    }
                }
                if (notifyAbout && !string.IsNullOrEmpty(sb.ToString()))
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Cancel none active blueSnap subscription older then: {sixMonthAgo.ToShortDateString()}{prev}", sb.ToString());
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Failed to Cancel none active blueSnap subscription older then: {sixMonthAgo.ToShortDateString()}{prev}", ex + "<br/>" + sb.ToString());
            }
        }

        public static bool CancelNotActiveBlueSnapSubscription(string blueSnapSubscriptionId, out string results)
        {
            try
            {
                var budget = GetBudgetByBlueSnapSubscription(blueSnapSubscriptionId, out results);
                DateTime sixMonthAgo = DateTime.Now.AddMonths(-6);
                if (budget == null)
                {
                    return false;
                }
                if (budget.LastPaymentDate < sixMonthAgo)
                {
                    if (UpdateSubscriptionStatus(blueSnapSubscriptionId.ToString(), BluesnapSubscriptionStatus.Cancelled, budget.ShopID))
                    {
                        UpdateShopSubscriptionStatus(blueSnapSubscriptionId, SUBSCRIPTION_STATUS.CANCEL);
                        results = string.Format("Done - blueSnapSubscriptionId: {1} ,LastPaymentDate: {2} was canclled for  ShopID,:{0}", budget.ShopID, blueSnapSubscriptionId, budget.LastPaymentDate);
                        return true;
                    }
                }
                results = string.Format("No Need - Last Payment Date is larger then the threshold ShopID,:{0} , LastPaymentDate:{1} , threshold:{2}", budget.ShopID, budget.LastPaymentDate, sixMonthAgo);
                return false;
            }
            catch (Exception ex)
            {
                results = string.Format("failed to proccess blueSnapSubscriptionId:{1} exception:{0}", ex.ToString(), blueSnapSubscriptionId);
                return false;
            }
        }
        //Ronny Not In use yet 15-01-2024
        public static void CanclNoneTBctiveBlueSnapSubscription(int? filterShopId = null, bool onlyPreview = false)
        {
            StringBuilder sb = new StringBuilder();
            DateTime sixMonthAgo = DateTime.Now.AddMonths(-6);
            string prev = onlyPreview ? " - Its only Preview" : "";
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                var reportingTbs = (from t in db.TrafficBoosters.Where(t =>
                            t.Status != (int)TB_AW_STATUS.CANCELED && t.Status > (int)TB_AW_STATUS.WAITING_TO_RUN_FIRST_TIME
                            && t.AppStatus == (int)TB_APP_STATUS.ACTIVE
                            && t.LastPaymentDate.HasValue && t.LastPaymentDate.Value < sixMonthAgo
                            )
                                    select new { ShopID = t.ShopID, tbId = t.ID, t.LastPaymentDate }).ToList();

                if (filterShopId.HasValue)
                {
                    reportingTbs = reportingTbs.Where(t => t.ShopID == filterShopId).ToList();
                }
                Console.WriteLine($"Checking Total of:{reportingTbs.Count} Shops.");
                BlueSnapApi blueSnapApi = new BlueSnapApi();
                foreach (var tb in reportingTbs)
                {
                    UpdateNextPaymentInAllSubscriptions(tb.ShopID.Value);
                    var subs = db.ShopSubscriptions.Where(s => s.ShopID == tb.ShopID && (s.AgreeID == null || s.AgreeID == 0) && s.AppIDs == "204" && s.BsStatus == "A").ToList();
                    if (subs != null && subs.Count > 0)
                    {
                        Console.WriteLine(tb.ToJson());
                        foreach (var sub in subs)
                        {
                            sb.Append(string.Format("Subscription {1} was cancel - ShopID,:{0} ,LastPaymentDate: {2}<br/>", EmailHelper.GetBoLinkHref(sub.ShopID, "subscriptions"), sub.BlueSnapSubscriptionID, tb.LastPaymentDate));
                            if (!onlyPreview)
                            {
                                try
                                {
                                    ConsoleAppHelper.WriteLogWithDB($"Canceling subscription older then 6 months:{sub.BlueSnapSubscriptionID}", sub.ShopID);
                                    blueSnapApi.UpdateSubscriptionStatus(sub.BlueSnapSubscriptionID.Value.ToString(), BluesnapSubscriptionStatus.Cancelled, sub.ShopID);
                                    UpdateShopSubscriptionStatus(sub.BlueSnapSubscriptionID.Value.ToString(), SUBSCRIPTION_STATUS.CANCEL);
                                }
                                catch (Exception ex)
                                {
                                    sb.Append(string.Format("Subscription Failed to cancel - ShopID,:{0} - blueSnapSubscriptionId: {1} ,LastPaymentDate: {2} ex:{3}<br/>", EmailHelper.GetBoLinkHref(sub.ShopID, "subscriptions"), sub.BlueSnapSubscriptionID, tb.LastPaymentDate, ex));
                                }
                            }
                        }
                    }
                }
                if (!string.IsNullOrEmpty(sb.ToString()))
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Cancel none active blueSnap subscription older then: {sixMonthAgo.ToShortDateString()}{prev}", sb.ToString());
                }
            }
            catch (Exception ex)
            {
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Failed to Cancel none active blueSnap subscription older then: {sixMonthAgo.ToShortDateString()}{prev}", ex + "<br/>" + sb.ToString());
            }
        }

        //public static void ActiveSubscriptionMonitor()
        //{
        //    try
        //    {


        //        Console.WriteLine("Start Active Subscription Monitor.");
        //        StoreYaEntities db = DataHelper.GetStoreYaEntities();
        //        Dictionary<string, int> checkDups = new Dictionary<string, int>();
        //        BlueSnapApi api = new BlueSnapApi();
        //        //   StringBuilder sb = new StringBuilder();
        //        List<SubscriptionReport> subscriptionsReport = new List<SubscriptionReport>();
        //        dynamic subs = api.GetAllActiveSubscriptions();
        //        Console.WriteLine("Active Subscription Monitor Total Active Subs." + subs.data.Count);
        //        int count = 0;
        //        foreach (var sub in subs.data)
        //        {

        //            SubscriptionReport subscriptionReport = new SubscriptionReport();
        //            int? subscriptionId = null;
        //            try
        //            {
        //                subscriptionId = int.Parse(sub["Subscription ID"].ToString());
        //                string nextChargeDate = sub["Next Charge Date"].ToString();
        //                string lastChargeDate = sub["Last Charge Date"].ToString();
        //                string price = sub["Price (Auth. Currency)"].ToString();
        //                string currency = sub["Auth. Currency"].ToString();
        //                string product = sub["Product Name"].ToString();
        //                subscriptionReport.NextChargeAmount = price;
        //                subscriptionReport.NextChargeDate = nextChargeDate;
        //                subscriptionReport.LastChargeDate = lastChargeDate;
        //                //subscriptionReport.MoreInfo = string.Format(
        //                //    "Next Charge Date:<b>{0}</b><br/>Price:<b>{1} {2}</b><br/>Last Charge Date:<b>{3}</b><br/>Product:<b>{4}</b>", 
        //                //    nextChargeDate, price, currency, lastChargeDate, product);
        //                Console.WriteLine("Checking Subscription Id -" + subscriptionId);
        //                ShopSubscription shopSubscriptions = db.ShopSubscriptions.SingleOrDefault(s => s.BlueSnapSubscriptionID == subscriptionId);
        //                if (shopSubscriptions == null)
        //                {
        //                    subscriptionReport.SubscriptionId = subscriptionId;
        //                    subscriptionReport.Message = "Cannot find ShopSubscription recored in the DB";
        //                }
        //                else
        //                {
        //                    subscriptionReport.AppIds = shopSubscriptions.AppIDs;
        //                    subscriptionReport.ShopId = EmailHelper.AdminBoShopLinkHref(shopSubscriptions.ShopID, "subscriptions");
        //                    subscriptionReport.SubscriptionId = subscriptionId;
        //                    var shop = db.Shops.SingleOrDefault(s => s.ID == shopSubscriptions.ShopID);
        //                    if (shop == null)
        //                    {
        //                        subscriptionReport.Message = "Cannot find Shop recored in the DB. ShopSubscriptions.ShopID";
        //                    }
        //                    else
        //                    {
        //                        string key = string.Format("{0}_{1}", shop.ID, shopSubscriptions.AppIDs);
        //                        if (checkDups.ContainsKey(key))
        //                        {
        //                            subscriptionReport.Message = "Duplicate Active subscription:" + checkDups[key];
        //                            //sb.Append(string.Format("Subscription: {0} ShopID: {1} AppIDs:{2} Duplicate Active subscription : {3}<br/>", subscriptionId, shopSubscriptions.ShopID, shopSubscriptions.AppIDs, checkDups[key]));
        //                        }
        //                        else
        //                        {
        //                            checkDups.Add(key, subscriptionId.Value);
        //                        }

        //                        if (shopSubscriptions.AppIDs != null && shopSubscriptions.AppIDs.Contains(AppTypes.TrafficBooster.GetHashCode().ToString()))
        //                        {
        //                            var tb = db.TrafficBoosters.SingleOrDefault(s => s.ShopID == shopSubscriptions.ShopID);
        //                            if (tb == null)
        //                            {
        //                                subscriptionReport.Message = subscriptionReport.Message + " Cannot find Traffic Bostter in the DB!";
        //                                // sb.Append(string.Format("Subscription: {0} ShopID: {1} AppIDs:{2} ,Cannot find Traffic Bostter in the DB!<br/>", subscriptionId, shopSubscriptions.ShopID, shopSubscriptions.AppIDs));
        //                            }
        //                            else
        //                            {
        //                                if (tb.Status.HasValue && tb.Status.Value == TrafficBoosterStatuses.Canceled.GetHashCode())
        //                                {
        //                                    subscriptionReport.Message = subscriptionReport.Message + " Traffic Bostter is Canceled!";
        //                                    //   sb.Append(string.Format("Subscription: {0} ShopID: {1} Traffic Bostter is Canceled!<br/>", subscriptionId, shopSubscriptions.ShopID));
        //                                }
        //                            }
        //                        }
        //                    }
        //                }
        //            }
        //            catch (Exception ex)
        //            {
        //                subscriptionReport.SubscriptionId = subscriptionId;
        //                subscriptionReport.Message = subscriptionReport.Message + " Failed with exception" + sub.ToString() + " Exception:" + ex.ToString();
        //            }
        //            if (!string.IsNullOrEmpty(subscriptionReport.Message))
        //            {
        //                subscriptionReport.ID = count++;
        //                subscriptionReport.Message = subscriptionReport.Message.Trim();
        //                subscriptionsReport.Add(subscriptionReport);
        //            }

        //        }
        //        if (subscriptionsReport.Count() > 0)
        //        {
        //            EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Active Subscription Monitor Total- " + subs.data.Count, subscriptionsReport.ListToHtmlTable());
        //        }
        //        Console.WriteLine("End Active Subscription Monitor.");
        //    }
        //    catch (Exception ex)
        //    {
        //        Console.WriteLine("Failed to run Active subscription monitor ." + ex.ToString());
        //    }
        //}


        public static void ActiveSubscriptionMonitor2(bool onlyDuplicateSubscriptions = false, bool saveBSReporttoDisk = false)
        {
            try
            {
                Console.WriteLine("Start Active Subscription Monitor.");
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                Dictionary<string, int> checkDups = new Dictionary<string, int>();

                Dictionary<int, List<SubscriptionReport>> shops = new Dictionary<int, List<SubscriptionReport>>();
                BlueSnapApi api = new BlueSnapApi();
                List<SubscriptionReport> subscriptionsReport = new List<SubscriptionReport>();
                dynamic subs = api.GetAllActiveSubscriptions(saveToDisk: saveBSReporttoDisk);
                StringBuilder sb = new StringBuilder();
                Console.WriteLine("Active Subscription Monitor Total Active Subs." + subs.data.Count);
                foreach (var sub in subs.data)
                {

                    SubscriptionReport subscriptionReport = new SubscriptionReport();
                    int? subscriptionId = null;
                    try
                    {
                        subscriptionId = int.Parse(sub["Subscription ID"].ToString());
                        string nextChargeDate = sub["Next Charge Date"].ToString();
                        string lastChargeDate = sub["Last Charge Date"].ToString();
                        string price = sub["Price (Auth. Currency)"].ToString();
                        string currency = sub["Auth. Currency"].ToString();
                        string product = sub["Product Name"].ToString();
                        subscriptionReport.NextChargeAmount = price;
                        subscriptionReport.NextChargeDate = nextChargeDate;
                        subscriptionReport.LastChargeDate = lastChargeDate;
                        subscriptionReport.SubscriptionId = subscriptionId.HasValue ? subscriptionId.ToString() : "";
                        Console.WriteLine("Add Subscription Id -" + subscriptionId);

                        ShopSubscription shopSubscriptions = db.ShopSubscriptions.SingleOrDefault(s => s.BlueSnapSubscriptionID == subscriptionId);
                        if (shopSubscriptions == null)
                        {
                            subscriptionReport.ShopID = 0;
                            subscriptionReport.Message = "Cannot find ShopSubscription recored in the DB";
                        }
                        else
                        {
                            subscriptionReport.ShopID = shopSubscriptions.ShopID;
                            subscriptionReport.AgreeID = shopSubscriptions.AgreeID;
                            subscriptionReport.InsertedAt = shopSubscriptions.InsertedAt;
                            subscriptionReport.AppIds = shopSubscriptions.AppIDs;
                            subscriptionReport.ShopLink = EmailHelper.GetBoLinkHref(shopSubscriptions.ShopID, "subscriptions");

                            subscriptionReport.NextChargeAmountInDB = shopSubscriptions.NextPaymentAmount?.ToString();
                            subscriptionReport.NextChargeDateInDB = shopSubscriptions.NextPaymenDate?.ToString();
                            subscriptionReport.AutorenewInDb = shopSubscriptions.Autorenew;
                        }
                    }
                    catch (Exception ex)
                    {
                        subscriptionReport.ShopID = -1;
                        subscriptionReport.Message = subscriptionReport.Message + " Failed with exception" + sub.ToString() + " Exception:" + ex.ToString();
                    }

                    if (subscriptionReport.AppIds == "204" && (subscriptionReport.AutorenewInDb ?? 1) == 1)
                    {
                        AddToList(shops, subscriptionReport);
                    }
                    string a = AnalyzeSubscription(subscriptionReport);
                    if (!string.IsNullOrEmpty(a))
                    {
                        Console.WriteLine(a);
                        sb.Append($"{EmailHelper.GetBoLinkHref(subscriptionReport.ShopID, "subscriptions", subscriptionReport.SubscriptionId.ToString())} - {a}<br/>");
                    }
                }

                Console.WriteLine("Shops - " + shops.Keys.Count);
                if (saveBSReporttoDisk)
                {
                    File.WriteAllText(@"c:\temp\ActiveSubscriptionMonitor2Shops.json", shops.ToFormattedJson());
                }
                List<SubscriptionReport> results = AnalyzeSubscriptions(shops, onlyDuplicateSubscriptions, saveBSReporttoDisk);
                if (results.Count() > 0)
                {
                    string subject = $"Active Subscription Monitor {results.Count} out of {subs.data.Count}";
                    if (onlyDuplicateSubscriptions)
                    {
                        subject = $"Duplicate Subscription Monitor Total- {results.Count} out of {subs.data.Count}";
                    }
                    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, subject, results.ListToHtmlTable());
                }
                else
                {
                    Console.WriteLine("Nothing to report");
                }
                if (!string.IsNullOrEmpty(sb.ToString()))
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, "1$ Subscriptions Report.", sb.ToString());

                }
                Console.WriteLine("End Active Subscription Monitor.");
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed to run Active subscription monitor ." + ex.ToString());
            }
        }

        public static string AnalyzeSubscription(SubscriptionReport subscriptionReport)
        {

            DateTime nextChargeDate = Convert.ToDateTime(subscriptionReport.NextChargeDate);
            if (DateTime.Now > nextChargeDate.AddDays(-15))
            {
                decimal overriderecurringcharge = 0;
                bool autoRenew = true;
                try
                {
                    BlueSnapApi api = new BlueSnapApi();
                    var sub = api.RetrieveSubscriptionObject(subscriptionReport.SubscriptionId);
                    if (sub != null)
                    {
                        overriderecurringcharge = sub.overriderecurringcharge.amount;
                        autoRenew = sub.autorenew;
                    }
                }
                catch
                {

                }
                if (decimal.TryParse(subscriptionReport.NextChargeAmount, out decimal nextChargeAmount))
                {
                    if (nextChargeAmount == 1 && overriderecurringcharge > nextChargeAmount)
                    {
                        return null;
                    }
                }
                if (autoRenew && (subscriptionReport.NextChargeAmount == "1.00" || overriderecurringcharge == 1))
                {

                    return $"{subscriptionReport.ShopID} - 1$ charge is due in: {subscriptionReport.NextChargeDate} - override recurring charge:{overriderecurringcharge} less the 14 days!";
                }
            }
            return null;
        }


        public static List<SubscriptionReport> DuplicateSubscriptions(int daysBack = 45)
        {
            DateTime paymentDays = DateTime.Now.AddDays(-daysBack);
            DateTime nextPaymentDays = DateTime.Now.AddDays(30);
            var db = DataHelper.GetStoreYaEntities();
            var result = (from s in db.ShopSubscriptions
                          join t in db.TrafficBoosters on s.ShopID equals t.ShopID
                          where s.Status == 1
                                && t.AppStatus == (byte)TB_APP_STATUS.ACTIVE
                                && t.LastPaymentDate > paymentDays
                                && s.NextPaymenDate < nextPaymentDays
                          group s by new { s.ShopID, s.AgreeID } into grouped
                          where grouped.Count() > 1
                          select new
                          {
                              grouped.Key.ShopID,
                              grouped.Key.AgreeID
                          }).ToList();
            Console.WriteLine($"DuplicateSubscriptions run over active tbs:{result.Count}");
            List<SubscriptionReport> subscriptionReports = new List<SubscriptionReport>();
            foreach (var s in result)
            {
                var duplicates = db.ShopSubscriptions.Where(sub => sub.ShopID == s.ShopID && sub.AgreeID == s.AgreeID
                && sub.BsStatus == "A" && (sub.PaymentAdapterType == 3 || sub.Autorenew == 1)
                ).GroupBy(sub => new { sub.ShopID, sub.AgreeID })
                 .Where(g =>
                     g.Count() > 1
                 )
             .Select(g => new
             {
                 ShopID = g.Key.ShopID,
                 AgreeID = g.Key.AgreeID,
                 Count = g.Count(),
                 Subscriptions = g.ToList()
             })
             .ToList();

                Console.WriteLine($"ShopId:{s.ShopID} - total subscriptions:{duplicates.Count}");
                foreach (var dup in duplicates)
                {
                    foreach (var sb in dup.Subscriptions)
                    {
                        SubscriptionReport subscriptionReport = new SubscriptionReport()
                        {
                            ShopID = s.ShopID,
                            AgreeID = sb.AgreeID,
                            ChargeFrequency = sb.ChargeFrequency,
                            AppIds = sb.AppIDs,
                            InsertedAt = sb.InsertedAt,
                            Message = $"ActiveInBudget:{sb.ActiveInBudget} ,BsStatus:{sb.BsStatus}",
                            NextChargeAmount = sb.OverridedContractPrice,
                            NextChargeAmountInDB = sb.ContractPrice,
                            NextChargeDate = sb.NextPaymenDate.ToString(),
                            ShopLink = EmailHelper.GetBoLinkHrefAndAM(s.ShopID),
                            SubscriptionId = sb.OriginalSubscriptionID,
                        };
                        subscriptionReports.Add(subscriptionReport);
                    }

                }
            }
            if (subscriptionReports.Count > 0)
            {

                string subject = $"All Payments Gateways Duplicate Subscriptions Total- {subscriptionReports.Count} ";
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, subject, subscriptionReports.ListToHtmlTable());
            }
            return subscriptionReports;
        }

        public static List<SubscriptionReport> AnalyzeSubscriptions(Dictionary<int, List<SubscriptionReport>> shops, bool onlyDuplicateSubscriptions = false, bool savetoDisk = false, PaymentAdapterTypes paymentProvider = PaymentAdapterTypes.BlueSnap)
        {
            var db = DataHelper.GetStoreYaEntities();
            List<SubscriptionReport> findings = new List<SubscriptionReport>();
            DateTime sixMonthAgo = DateTime.Now.AddMonths(-6);
            //Active that didnt paied for 6 monthes 
            var reportingTbs = (from t in db.TrafficBoosters.Where(t =>
                     t.Status != (int)TB_AW_STATUS.CANCELED && t.Status > (int)TB_AW_STATUS.WAITING_TO_RUN_FIRST_TIME
                     && t.AppStatus == (int)TB_APP_STATUS.ACTIVE
                     )
                                select new { ShopID = t.ShopID, tbId = t.ID, t.LastPaymentDate }
                     ).ToList();

            if (!onlyDuplicateSubscriptions)
            {

                reportingTbs = reportingTbs.Where(t => t.LastPaymentDate.HasValue && t.LastPaymentDate.Value < sixMonthAgo).ToList();
            }
            if (savetoDisk)
            {
                File.WriteAllText($@"c:\temp\AnalyzeSubscriptionsReportingTbs_{paymentProvider}.json", reportingTbs.ToFormattedJson());
            }
            foreach (var shop in shops)
            {
                int shopID = shop.Key;
                Console.WriteLine("Analyze shop " + shopID);
                var subscriptions = shop.Value;//.Where(s=> s.a;
                if (shopID == 0)
                {
                    findings.AddRange(subscriptions);
                }
                else
                {

                    var tb = db.TrafficBoosters.SingleOrDefault(s => s.ShopID == shopID);
                    if (paymentProvider == PaymentAdapterTypes.FastSpring)
                    {
                        foreach (var item in subscriptions)
                        {
                            bool same = CompareSubscriptionApiVsDB(item);
                            if (!same)
                            {
                                var ret = FastSpringPaymentService.SyncSubscriptionTagsFromShop(shopID, item.SubscriptionId, out string error);
                                if (ret != null && error == null)
                                {
                                    Thread.Sleep(2000);
                                    var retV = FastSpringPaymentService.ValidateSubscriptionData(item.SubscriptionId);
                                    if (retV == null)
                                    {
                                        if (retV.FirstOrDefault() != null && !string.IsNullOrEmpty(retV.FirstOrDefault().Message))
                                        {
                                            findings.Add(item);
                                        }
                                    }
                                }
                                else
                                {
                                    findings.Add(item);
                                }
                            }
                        }
                    }
                    if (TbSettingsHelper.IsDefaultBudgetCanceled(tb) && subscriptions.Any(s => (s.AgreeID ?? 0) == 0))
                    {
                        if (!onlyDuplicateSubscriptions)
                        {

                            foreach (var item in subscriptions)
                            {
                                bool reportSubs = true;
                                try
                                {
                                    DateTime nextChargeDate = Convert.ToDateTime(item.NextChargeDate);
                                    item.Message += $"Active {paymentProvider} subscriptions without paid TB. ";


                                    // if (Convert.ToDateTime(item.LastChargeDate) < sixMonthAgo && DateTime.Now.AddMonths(4) > nextChargeDate)
                                    if (nextChargeDate > DateTime.Now.AddMonths(6) && (item.NextChargeAmount == "1.00" || (item.NextChargeAmount == "1")))
                                    {
                                        Console.WriteLine($"{shopID} {nextChargeDate} > {DateTime.Now.AddMonths(6)} and {item.NextChargeAmount}");
                                        reportSubs = false;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    item.Message += $"Failed to analyze active {paymentProvider} subscriptions without paid TB:ex: {ex.Message} - {item.ToJson()}. ";

                                }

                                if (findings.Any(s => s.SubscriptionId == item.SubscriptionId))
                                {
                                    if (!reportSubs)
                                    {
                                        findings.Remove(item);
                                    }
                                }
                                else
                                {
                                    if (reportSubs)
                                    {
                                        findings.Add(item);
                                    }
                                }
                                //{

                                //}
                            }
                        }
                    }
                    else if (tb.AppStatus != (byte)TB_APP_STATUS.ACTIVE && subscriptions.Any(s => s.AgreeID.HasValue && s.AgreeID > 0))
                    {
                        if (!onlyDuplicateSubscriptions)
                        {
                            //List<SubscriptionReport> results = SetMessage(subscriptions, "Active subscriptions without paid TB");
                            foreach (var item in subscriptions)
                            {
                                var agreeement = db.TbAgreements.SingleOrDefault(s => s.ShopID == shopID && s.ID == item.AgreeID);
                                if (agreeement != null)
                                {
                                    item.Message += $"Active {paymentProvider} subscriptions without paid TB Agreement. ";
                                    DateTime nextChargeDate = Convert.ToDateTime(item.NextChargeDate);
                                    Console.WriteLine($"{Convert.ToDateTime(item.LastChargeDate)}< {sixMonthAgo} and {DateTime.Now.AddMonths(4)} > {nextChargeDate}");
                                    if (agreeement.LastPaymentDate < DateTime.Now.AddDays(-45))
                                    {
                                        if (!findings.Any(s => s.SubscriptionId == item.SubscriptionId))
                                        {
                                            findings.Add(item);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else if (subscriptions.Count > 1)
                    {
                        List<SubscriptionReport> duplicateInactiveSubscriptions = FindProblematicDuplicates(shopID, subscriptions);
                        findings.AddRange(duplicateInactiveSubscriptions);
                    }
                    if (!reportingTbs.Any(s => s.ShopID == shopID) && subscriptions.Any(s => s.NextChargeAmount == "1.00"))
                    {
                        var listOneDollar = subscriptions.Where(s => s.NextChargeAmount == "1.00");
                        foreach (var item in listOneDollar)
                        {
                            DateTime nextChargeDate = Convert.ToDateTime(item.NextChargeDate);
                            Console.WriteLine($"One Dollar Payment: {DateTime.Now.AddMonths(3)} > {nextChargeDate}");
                            if (DateTime.Now.AddMonths(3) > nextChargeDate)
                            {
                                item.Message = $"1$ Payment will be charged on the {nextChargeDate}. ";
                                if (!findings.Any(s => s.SubscriptionId == item.SubscriptionId))
                                {
                                    findings.Add(item);
                                }
                            }
                        }

                    }
                    if (findings.Any(c => c.ShopID == shopID))
                    {
                        Console.WriteLine($"Analyze shop {shopID} findings:{findings.Count}");
                    }
                }
            }
            return findings;
        }


        private static bool CompareSubscriptionApiVsDB(SubscriptionReport item)
        {
            bool same = true;
            try
            {
                if (string.IsNullOrEmpty(item.NextChargeAmount) || !item.NextChargeAmount.CompareAsDecimal(item.NextChargeAmountInDB))
                {
                    item.Message += $"Api NextChargeAmount {item.NextChargeAmount} not the same as DB:{item.NextChargeAmountInDB}. ";
                    same = false;
                }

                if ((string.IsNullOrEmpty(item.LastChargeDate) != string.IsNullOrEmpty(item.LastChargeDateInDB)) ||
                    (!string.IsNullOrEmpty(item.LastChargeDate) && !string.IsNullOrEmpty(item.LastChargeDateInDB) &&
                    !item.LastChargeDate.CompareAsDateTime(item.LastChargeDateInDB, 86400)))
                {
                    item.Message += $"Api LastChargeDate {item.LastChargeDate} not the same as DB:{item.LastChargeDateInDB}. ";
                    same = false;
                }
                if (string.IsNullOrEmpty(item.ChargeFrequency) || item.ChargeFrequency.ToLower() != item.ChargeFrequencyInDB.ToLower())
                {
                    item.Message += $"Api ChargeFrequency {item.ChargeFrequency} not the same as DB:{item.ChargeFrequencyInDB}. ";
                    same = false;
                }
                if ((string.IsNullOrEmpty(item.NextChargeDate) != string.IsNullOrEmpty(item.NextChargeDateInDB)) ||
                  (!string.IsNullOrEmpty(item.NextChargeDate) && !string.IsNullOrEmpty(item.NextChargeDateInDB) &&
                  !item.NextChargeDate.CompareAsDateTime(item.NextChargeDateInDB, 1800)))
                {
                    item.Message += $"Api NextChargeDate {item.NextChargeDate} not the same as DB:{item.NextChargeDateInDB}. ";
                    same = false;
                }
            }
            catch
            {
                same = false;
            }
            return same;
        }

        public static List<SubscriptionReport> FindProblematicDuplicates(int shopID, List<SubscriptionReport> subs)
        {


            DateTime threeM = DateTime.Now.AddMonths(3);

            List<SubscriptionReport> results = new List<SubscriptionReport>();
            var db = DataHelper.GetStoreYaEntities();
            var shopApps = db.ShopApps.SingleOrDefault(s => s.ShopID == shopID && s.AppTypeID == 204);
            int? activeSubscription = shopApps?.SubscriptionID;

            subs = subs.OrderBy(s => s.AgreeID ?? 0).ThenByDescending(s => s.InsertedAt).ToList();

            DateTime threeMonthsFromNow = DateTime.UtcNow.AddMonths(4);

            // Exclude subscriptions that have NextChargeDate > 3 months from now AND NextChargeAmount != 1.00
            List<SubscriptionReport> subs2 = new List<SubscriptionReport>();
            foreach (var s in subs)
            {
                DateTime nextChargeDate;
                bool isValidDate = DateTime.TryParseExact(s.NextChargeDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out nextChargeDate);
                if (!isValidDate)
                {
                    isValidDate = DateTime.TryParseExact(s.NextChargeDate, "MM/dd/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out nextChargeDate);

                }
                if ((isValidDate && nextChargeDate <= threeMonthsFromNow) || s.NextChargeAmount == "1.00")
                {
                    subs2.Add(s);
                }
            }
            subs = subs2.OrderBy(s => s.AgreeID ?? 0)
                .ThenByDescending(s => s.InsertedAt)
                .ToList();
            //subs = subs
            //    .Where(s =>
            //    {
            //        DateTime nextChargeDate;
            //        bool isValidDate = DateTime.TryParseExact(s.NextChargeDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out nextChargeDate);

            //        // Keep subscriptions if they are within 3 months OR have a charge amount of $1.00
            //        return (isValidDate && nextChargeDate <= threeMonthsFromNow) || s.NextChargeAmount == "1.00";
            //    })
            //    .OrderBy(s => s.AgreeID ?? 0)
            //    .ThenByDescending(s => s.InsertedAt)
            //    .ToList();

            int? previousAgreeID = -1;
            string firstSubscription = "";

            foreach (var item in subs)
            {
                int currentAgrement = item.AgreeID ?? 0;
                if (previousAgreeID == currentAgrement)
                {
                    //SubscriptionReport duplicate = item;
                    item.Message = "Duplicate subscriptions found. Agreement - " + currentAgrement + " Active is " + firstSubscription;
                    results.Add(item);
                }
                else
                {
                    firstSubscription = (item.SubscriptionId).ToString();
                }
                previousAgreeID = currentAgrement;
            }
            return results;

        }

        private static List<SubscriptionReport> SetMessage(List<SubscriptionReport> subs, string message)
        {
            foreach (var item in subs)
            {
                item.Message += message;
            }
            return subs;
        }

        internal static void AddToList(Dictionary<int, List<SubscriptionReport>> shops, SubscriptionReport subscriptionReport)
        {
            if (shops.ContainsKey(subscriptionReport.ShopID))
            {
                var list = shops[subscriptionReport.ShopID];
                list.Add(subscriptionReport);
                shops[subscriptionReport.ShopID] = list;
            }
            else
            {
                List<SubscriptionReport> newList = new List<SubscriptionReport>();
                newList.Add(subscriptionReport);
                shops.Add(subscriptionReport.ShopID, newList);
            }
        }

        public class SubscriptionReport
        {
            public string Message { get; set; }
            public string SubscriptionId { get; set; }
            public int ShopID { get; set; }
            public int? AgreeID { get; set; }
            public DateTime? InsertedAt { get; set; }
            public string ShopLink { get; set; }
            public string AppIds { get; set; }
            public string LastChargeDate { get; set; }
            public string LastChargeDateInDB { get; set; }
            public string NextChargeDate { get; set; }
            public string NextChargeDateInDB { get; set; }
            public string NextChargeAmount { get; set; }
            public string NextChargeAmountInDB { get; set; }
            public string ChargeFrequency { get; set; }
            public string ChargeFrequencyInDB { get; set; }
            public int? AutorenewInDb { get; set; }
        }

    }

}
