﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Storeya.Core.Helpers;
using System.Text.RegularExpressions;

namespace Storeya.Core.Models.AppStore
{
    public class SubscriptionManager
    {
        public static int Save(int appID, int shopID, int planID, string bsSubscriptinID, string bsContractID, string price)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopSubscription subscription = new ShopSubscription()
                {
                    AppIDs = appID.ToString(),
                    BlueSnapSubscriptionID = Convert.ToInt32(bsSubscriptinID),
                    ContractID = Convert.ToInt32(bsContractID),
                    InsertedAt = DateTime.Now,
                    PlanIDs = planID.ToString(),
                    ShopID = shopID,
                    Status = 1,
                    ContractPrice = price
                };
            db.AddToShopSubscriptions(subscription);
            db.SaveChanges();

            return subscription.ID;
        }

        public static bool CreateSubscriptionChargeAndUpdateSubscriptionChargeViaBlueSnapAPI(SubscriptionToUpgrade subscription)
        {
            string subscription_next_charge_date = GetSubscriptionCurrentNextChargeDate(subscription.ContractMethod);

            if (!string.IsNullOrEmpty(subscription_next_charge_date))
            {
                BluespapApi target = new BluespapApi();

                //Create subscription charge
                //TODO:

                //Update subscription
                bool isUpdated = target.UpdateSubscriptionChangingRecurringChargeAmount(subscription.ShopSubscription.BlueSnapSubscriptionID.ToString(), subscription.TotalSubscriptionCharge, subscription_next_charge_date, subscription.ShopID);
                if (isUpdated)
                {
                    return true;
                }

            }
            return false;
        }


        public static string GetSubscriptionCurrentNextChargeDate(int contractMethod)
        {
            string subscription_next_charge_date = null;
            DateTime nextChargeDate = DateTime.Now;

            if (contractMethod == 0) //monthly
            {
                nextChargeDate = nextChargeDate.AddMonths(1);
            }
            else if (contractMethod == 1) // Annual
            {
                nextChargeDate = nextChargeDate.AddMonths(12);
            }
            else if (contractMethod == 2)//quartally - etsy
            {
                nextChargeDate = nextChargeDate.AddMonths(3);
            }
            else
            {
                return null;
            }

            subscription_next_charge_date = nextChargeDate.ToString("dd-MMM-yy");

            return subscription_next_charge_date;
        }

        private static string GetMatchValue(string content, string tagName)
        {
            Match match = Regex.Match(content, @"<" + tagName + ">.*?</" + tagName + ">", RegexOptions.Singleline);
            if (match.Success)
            {
                string tagValue = SetMatchValue(match, "<" + tagName + ">", "</" + tagName + ">");
                return tagValue;
            }
            return null;
        }
        private static string SetMatchValue(Match match, string openingTag, string closingTag)
        {
            string value = match.Value.Remove(0, openingTag.Length);
            int indexOfClosingTag = value.IndexOf(closingTag);
            value = value.Remove(indexOfClosingTag, closingTag.Length);
            return value;
        }


        public static bool UpdateInDB(int subscriptionID, int planID, string price, int shopID, int appID)
        {
            bool isUpdated = false;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            ShopSubscription subscription = db.ShopSubscriptions.Where(ss => ss.ID == subscriptionID).SingleOrDefault();
            if (subscription != null)
            {
                try
                {
                    if (!subscription.AppIDs.Split(',').Contains(appID.ToString()))
                    {
                        subscription.AppIDs = subscription.AppIDs.TrimEnd(',') + "," + appID.ToString();
                    }
                    if (!subscription.PlanIDs.Split(',').Contains(planID.ToString()))
                    {
                        subscription.PlanIDs = subscription.PlanIDs.TrimEnd(',') + "," + planID.ToString();
                    }
                    subscription.OverridedContractPrice = price;
                    subscription.UpdatedAt = DateTime.Now;
                    db.SaveChanges();
                    isUpdated = true;
                }
                catch (Exception ex)
                {
                    Log4NetLogger.Error(string.Format("Failed to update a bluesnap subscription ID: {0} for {1} shop app with price: {2}", subscriptionID, AppStoreManager.GetAppByID(appID).AppName, price), ex, shopID);
                }
            }
            return isUpdated;
        }

        //public static string GetPlanByContract(string contractID)
        //{
        //    string planToContract = ConfigHelper.GetValue("BlueSnap_PlanToContact");
        //    string[] values = planToContract.Split(',');
        //    for (int i = 0; i < values.Length; i = i + 2)
        //    {
        //        if (values[i + 1] == contractID)
        //        {
        //            return values[i];
        //        }
        //    }
        //    throw new Exception("No contract definition found for plan ID = " + contractID.ToString());
        //}

        internal static bool UpdateSubscriptionInBlueSnap(SubscriptionChange sub)
        {
            throw new NotImplementedException();
        }
    }


    public class SubscriptionToUpgrade
    {
        public int ShopID { get; set; }
        public int AppID { get; set; }
        public int PlanID { get; set; }
        public ShopSubscription ShopSubscription { get; set; }
        public int ContractMethod { get; set; }
        public ContractSettings Contract { get; set; }
        public double TotalSubscriptionCharge { get; set; } 
        
        public SubscriptionToUpgrade(int shopID, int appID, int planID)
        {
            this.ShopID = shopID;
            this.AppID = appID;
            this.PlanID = planID;

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            this.ShopSubscription = db.ShopSubscriptions.Where(sub => sub.ShopID == shopID).FirstOrDefault();
            this.ContractMethod = BluesnapHelper.ReverseMethodID(this.ShopSubscription.ContractID.Value, appID);
            this.Contract = BluesnapHelper.GetContract(planID, this.ContractMethod, appID);

            this.TotalSubscriptionCharge = this.Contract.Price + AppStoreManager.GetAppsPriceForSubscriptionExcludingProvidedAppId(shopID, appID);
        } 
    }
}
