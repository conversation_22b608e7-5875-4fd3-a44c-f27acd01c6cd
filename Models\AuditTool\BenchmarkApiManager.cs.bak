﻿using Storeya.Core.Entities;
using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Data.Entity.Validation;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Models.AuditTool
{

    public class BenchmarkApiManager
    {
        public static T PreserveAdminValue<T>(string[] preserveAdminValues, string propertyName, T value, T currentValue)
        {
            if (preserveAdminValues == null)
            {
                return value;
            }
            if (preserveAdminValues.Contains(propertyName.ToLower()))
            {
                return currentValue;
            }
            return value;
        }

        public static void Update(string url, AuditTool collected, int? status)
        {
            //var db = DataHelper.GetStoreYaEntities();
            //Benchmark b = BenchmarksManager.GetSettings(shopID);

            using (var db = new StoreYaEntities())
            {
                BenchmarkApi b = null;
                string hostName = GetHostName(url);
                int shopid = GetShopId(url);
                if (!string.IsNullOrEmpty(hostName))
                {
                    b = db.BenchmarkApis.Where(t => t.HostName == hostName).SingleOrDefault();
                }
                if (b != null)
                {
                    string[] preserveAdminValues = null;
                    if (!string.IsNullOrEmpty(b.PreserveAdminValues))
                    {
                        preserveAdminValues = b.PreserveAdminValues.ToLower().Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    }
                    if (collected.Data != null)
                    {

                        b.CanonicalUrl = GetTruncatedUrl(collected.Data.CanonicalUrl);
                        b.DataRangeInMonths = collected.Data.DataRangeInMonths; //(collectedData.Results != null ? collectedData.Results.MonthsAmount : 0),
                        b.HasFbPixelOnProductPage = PreserveAdminValue<int?>(preserveAdminValues, "HasFbPixelOnProductPage", (collected.Data.HasFbPixelOnProductPage ? 1 : 0), b.HasFbPixelOnProductPage);
                        b.HasGaOnHomePage = PreserveAdminValue<int?>(preserveAdminValues, "HasGaOnHomePage", (collected.Data.HasGaOnHomePage ? 1 : 0), b.HasGaOnHomePage);
                        b.HasGoogleMerchantCenterOnHomepage = PreserveAdminValue<int?>(preserveAdminValues, "HasGoogleMerchantCenterOnHomepage", (collected.Data.HasGoogleMerchantCenterOnHomepage ? 1 : 0), b.HasGoogleMerchantCenterOnHomepage);
                        b.HasGooglePixelOnThankYouPage = (collected.Data.HasGooglePixelOnThankYouPage ? 1 : 0);
                        b.HasGoogleRemarketingCodeOnProductPage = PreserveAdminValue<int?>(preserveAdminValues, "HasGoogleRemarketingCodeOnProductPage", (collected.Data.HasGoogleRemarketingCodeOnProductPage ? 1 : 0), b.HasGoogleRemarketingCodeOnProductPage);
                        b.HomePageUrl = GetTruncatedUrl(collected.Data.HomePageUrl);

                        if (collected.Data.Orders != null)
                        {
                            b.OrdersAmount = collected.Data.Orders.Count; //(collectedData.Results != null ? collectedData.Results.OrdersAmount : 0); //
                        }

                        b.OnSitePromotions = (collected.Data.OnSitePromotions ? 1 : 0);
                        b.OrderConfirmationPageUrl = GetTruncatedUrl(collected.Data.OrderConfirmationPageUrl);
                        b.ProductPageUrl = GetTruncatedUrl(collected.Data.ProductPageUrl);

                        if (collected.Data.Products != null)
                        {
                            b.ProductsAmount = collected.Data.Products.Count; //(collectedData.Results != null ? collectedData.Results.OrdersAmount : 0); //
                        }

                        b.RedirectsFromHttpToHttps = (collected.Data.RedirectsFromHttpToHttps ? 1 : 0);
                        b.RedirectsToCanonical = (collected.Data.RedirectsToCanonical ? 1 : 0);
                        b.ShopifyShopName = collected.Data.ShopifyShopName;

                        b.H1 = TextManipulationHelper.Truncate(collected.Data.H1, 485);
                        //b.H2 = collected.Data.H2;
                        b.MetaTitle = collected.Data.MetaTitle;
                        b.MetaDescription = collected.Data.MetaDescription;
                        //b.Images = (collected.Data.Images != null ? string.Join(",", collected.Data.Images) : null);

                        b.ProductImagesCountFoundOnHP = (collected.Data.Images != null ? collected.Data.Images.Count : 0);

                        var imagesWithMissingAtr = (collected.Data.ImageTagsWithEmptyOrMissingAltAttribute != null ? string.Join(",", collected.Data.ImageTagsWithEmptyOrMissingAltAttribute) : null);
                        b.ImageTagsWithEmptyOrMissingAltAttribute = TextManipulationHelper.Truncate(imagesWithMissingAtr, 3990);
                        b.DeprecatedHtmlTags = (collected.Data.DeprecatedHtmlTags != null ? string.Join(",", collected.Data.DeprecatedHtmlTags) : null);
                        b.SiteMapUrl = collected.Data.SiteMapUrl;
                        b.RobotsTxtUrl = collected.Data.RobotsTxtUrl;
                        b.FaviconIcoUrl = collected.Data.FaviconIcoUrl;
                    }

                    if (collected.Results != null)
                    {
                        b.AverageOrderAmount = collected.Results.AverageOrderAmount;
                        b.ContactUsUrl = GetTruncatedUrl(collected.Results.ContactUsUrl);
                        b.CustomerLifetimeValue = collected.Results.CustomerLifetimeValue;
                        b.DescriptionLenght = collected.Results.DescriptionLenght;

                        if (collected.Results.DesktopPerformance != null)
                        {
                            b.DesktopPerformanceScore = collected.Results.DesktopPerformance.Score;
                            if (collected.Results.DesktopPerformance.OptimizedImages != null)
                            {
                                b.DesktopPerformanceImagesAmount = collected.Results.DesktopPerformance.OptimizedImages.Count;
                            }
                            b.DesktopPerformanceImagesReduction = collected.Results.DesktopPerformance.ImagesPossibleReduction;
                        }

                        b.HasChatOrMessenger = (collected.Results.HasChatOrMessenger ? 1 : 0);
                        b.HasSocialLinks = PreserveAdminValue<int?>(preserveAdminValues, "HasSocialLinks", (collected.Results.HasSocialLinks ? 1 : 0), b.HasSocialLinks);

                        // b.HomePageHasReviews = (collected.Results.HomePageHasReviews ? 1 : 0);

                        if (collected.Results.MobilePerformance != null)
                        {
                            b.MobilePerformanceScore = collected.Results.MobilePerformance.Score;
                            if (collected.Results.MobilePerformance.OptimizedImages != null)
                            {
                                b.MobilePerformanceImagesAmount = collected.Results.MobilePerformance.OptimizedImages.Count;
                            }
                            b.MobilePerformanceImagesReduction = collected.Results.MobilePerformance.ImagesPossibleReduction;
                        }

                        b.PercentageOfDiscountedItems = collected.Results.PercentageOfDiscountedItems;
                        b.ProductPageHasReviews = PreserveAdminValue<int?>(preserveAdminValues, "ProductPageHasReviews", (collected.Results.ProductPageHasReviews ? 1 : 0), b.ProductPageHasReviews);
                        b.HomePageHasReviews = PreserveAdminValue<int?>(preserveAdminValues, "HomePageHasReviews", (collected.Results.HomePageHasReviews ? 1 : 0), b.HomePageHasReviews);
                        b.ReturningCustomers = collected.Results.ReturningCustomers;
                        b.Revenues = collected.Results.Revenues;
                        b.ReturnPolicyUrl = PreserveAdminValue<string>(preserveAdminValues, "ReturnPolicyUrl", GetTruncatedUrl(collected.Results.ReturnPolicyUrl), b.ReturnPolicyUrl);
                        b.ShippingInfoUrl = PreserveAdminValue<string>(preserveAdminValues, "ShippingInfoUrl", GetTruncatedUrl(collected.Results.ShippingInfoUrl), b.ShippingInfoUrl);
                        b.TrustBadges = PreserveAdminValue<int?>(preserveAdminValues, "TrustBadges", (collected.Results.TrustBadges ? 1 : 0), b.TrustBadges);

                        b.GaOverallConversionRate = collected.Results.OverallConversionRate;
                        b.GaGoogleOrganicConversionRate = collected.Results.GoogleOrganicConversionRate;
                        b.GaMobileConversionRate = collected.Results.MobileConversionRate;
                        b.GaDesktopConversionRate = collected.Results.DesktopConversionRate;

                    }

                    if (collected.GaData != null)
                    {
                        b.ConversionByChannelData = collected.GaData.ConversionByChannelData;
                        b.OrdersAmount = collected.GaData.Transactions;
                        b.Sessions = collected.GaData.Sessions;
                        b.Revenues = collected.GaData.Revenue;
                        b.AdCost = collected.GaData.AdCost;
                        b.AdRevenue = collected.GaData.AdRevenue;
                        b.LandingPagesCount = collected.GaData.LandingPagesCount;
                    }

                    if (status.HasValue)
                        b.Status = status;
                    b.ShopID = shopid;
                    b.UpdatedAt = DateTime.Now;
                    b.RunAt = DateTime.Now;

                    db.SaveChanges();

                    //try
                    //{
                    //    db.SaveChanges();
                    //}
                    ////catch (SqlException sql_ex)
                    ////catch (SqlException sql_ex)
                    ////{
                    ////    //Somehow this object is getting to be attached (probably because it's connected to the _currentShop)
                    ////    //db.Detach(b);
                    ////}
                    //catch (Exception ex)
                    //{
                    //    throw new Exception(ex.ToString());
                    //} 
                }
            }
        }


        public static void Update(string url, Benchmark data)
        {

            var db = DataHelper.GetStoreYaEntities();
            BenchmarkApi b = GetSettings(url);
            if (b != null)
            {
                b.AverageOrderAmount = data.AverageOrderAmount;
                b.CanonicalUrl = GetTruncatedUrl(data.CanonicalUrl);
                b.ContactUsUrl = GetTruncatedUrl(data.ContactUsUrl);
                b.CustomerLifetimeValue = data.CustomerLifetimeValue;
                b.DataRangeInMonths = data.DataRangeInMonths;
                b.DescriptionLenght = data.DescriptionLenght;
                //b.DesktopPerformanceImagesAmount = data.DesktopPerformanceImagesAmount;
                b.DesktopPerformanceScore = data.DesktopPerformanceScore;
                b.DesktopPerformanceImagesReduction = data.DesktopPerformanceImagesReduction;
                //b.DesktopPerformanceStrategy = data.DesktopPerformanceStrategy;
                b.HasFbPixelOnProductPage = data.HasFbPixelOnProductPage;
                b.HasChatOrMessenger = data.HasChatOrMessenger;
                b.HasGaOnHomePage = data.HasGaOnHomePage;
                b.HasGoogleMerchantCenterOnHomepage = data.HasGoogleMerchantCenterOnHomepage;
                b.HasGooglePixelOnThankYouPage = data.HasGooglePixelOnThankYouPage;
                b.HasGoogleRemarketingCodeOnProductPage = data.HasGoogleRemarketingCodeOnProductPage;
                b.HasSocialLinks = data.HasSocialLinks;
                b.HomePageHasReviews = data.HomePageHasReviews;
                b.HomePageUrl = GetTruncatedUrl(data.HomePageUrl);
                //b.MobilePerformanceImagesAmount = data.MobilePerformanceImagesAmount;
                b.MobilePerformanceScore = data.MobilePerformanceScore;
                b.MobilePerformanceImagesReduction = data.MobilePerformanceImagesReduction;
                b.OrderConfirmationPageUrl = GetTruncatedUrl(data.OrderConfirmationPageUrl);
                b.OrdersAmount = data.OrdersAmount;
                b.PercentageOfDiscountedItems = data.PercentageOfDiscountedItems;
                b.ProductPageHasReviews = data.ProductPageHasReviews;
                b.ProductPageUrl = GetTruncatedUrl(data.ProductPageUrl);
                b.ProductsAmount = data.ProductsAmount;
                b.RedirectsFromHttpToHttps = data.RedirectsFromHttpToHttps;
                b.RedirectsToCanonical = data.RedirectsToCanonical;
                b.ReturningCustomers = data.ReturningCustomers;
                b.ReturnPolicyUrl = GetTruncatedUrl(data.ReturnPolicyUrl);
                b.Revenues = data.Revenues;
                b.ShippingInfoUrl = GetTruncatedUrl(data.ShippingInfoUrl);
                b.ShopifyShopName = data.ShopifyShopName;
                b.TrustBadges = data.TrustBadges;
                b.OnSitePromotions = data.OnSitePromotions;
                b.UpdatedAt = DateTime.Now;
                b.RunAt = DateTime.Now;
                b.Status = (int)BenchmarkStatuses.Ready;
                db.SaveChanges();
            }
        }


        public static string GetTruncatedUrl(string url, int lenght = 250)
        {
            if (!string.IsNullOrEmpty(url))
            {
                if (url.Length <= lenght)
                {
                    return url;
                }
                else
                {
                    return url.Substring(0, lenght);
                }
            }
            return null;
        }

        public static BenchmarkApi GetSettings(string url)
        {
            string hostName = GetHostName(url);
            if (!string.IsNullOrEmpty(hostName))
            {
                StoreYaEntities db = DataHelper.GetStoreYaEntities();
                BenchmarkApi benchmarks_data = db.BenchmarkApis.Where(t => t.HostName == hostName).OrderByDescending(c => c.InsertedAt).FirstOrDefault();
                return benchmarks_data;
            }
            return null;
        }
        public static List<BenchmarkApi> GetBenchmarkApisToRun(string hostName = null)
        {
            //StoreYaEntities db = DataHelper.GetStoreYaEntities();
            using (var db = new StoreYaEntities())
            {
                if (string.IsNullOrEmpty(hostName))
                {
                    var apis = db.BenchmarkApis.Where(b => !b.RunAt.HasValue && b.HostName != null && b.Status != (int)BenchmarkStatuses.Canceled).ToList();
                    return apis;
                }
                else if (hostName.ToLower().StartsWith("storeyabh.bad"))
                {
                    return new List<BenchmarkApi>() { SetShowAllMessagesSettings("https://" + hostName) };
                }
                else if (hostName.ToLower().StartsWith("storeyabh.ok"))
                {
                    return new List<BenchmarkApi>() { SetShowAllOKMessagesSettings("https://" + hostName) };
                }
                else
                {
                    hostName = hostName.ToLower();
                    var apis = db.BenchmarkApis.Where(b => !b.RunAt.HasValue && b.HostName == hostName && b.Status != (int)BenchmarkStatuses.Canceled).ToList();
                    return apis;
                }

            }
        }
        public static string GetHostName(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return null;
            }
            url = UrlPathHelper.BuildValidUri(url);
            Uri uri = new Uri(url);
            string u = uri.Host.ToLower();
            if (uri.Host.StartsWith("www."))
            {
                u = uri.Host.Replace("www.", "");
            }
            return u;
        }
        public static BenchmarkApi AddBenchmarkApi(string url, string callerUrl, string clientIp)
        {
            if (string.IsNullOrEmpty(url))
            {
                return null;
            }

            url = UrlPathHelper.BuildValidUri(url);
            string hostName = GetHostName(url);

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var bm = BenchmarkApiManager.GetSettings(hostName);
            if (bm == null)
            {
                int data_source = (int)BenchmarkDataSources.HomePage;
                BenchmarkApi benchmark = new BenchmarkApi();
                benchmark.HostName = hostName;
                benchmark.Revenues = 0;
                benchmark.OrdersAmount = 0;
                benchmark.ReturningCustomers = 0;
                benchmark.HomePageUrl = url;
                // benchmark.ShopID = shopID;
                benchmark.CallerUrl = callerUrl;
                benchmark.ClientIp = clientIp;
                benchmark.InsertedAt = DateTime.Now;
                benchmark.BenchmarkDataSource = data_source;

                db.BenchmarkApis.Add(benchmark);
                db.SaveChanges();

                return benchmark;
            }
            else
            {
                bm.UpdatedAt = DateTime.Now;
                bm.CallerUrl = callerUrl;
                bm.ClientIp = clientIp;
                db.SaveChanges();
            }
            return bm;
        }
        public static void RemoveResults(string url)
        {
            string hostName = GetHostName(url); ;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var bm = db.BenchmarkApis.Where(t => t.HostName == hostName).OrderByDescending(c => c.InsertedAt).FirstOrDefault();
            if (bm != null)
            {
                db.BenchmarkApis.Remove(bm);
                db.SaveChanges();
            }

        }

        public static void ResetResults(string url, string callerUrl, string clientIp)
        {
            string hostName = GetHostName(url); ;
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var bm = db.BenchmarkApis.Where(t => t.HostName == hostName).OrderByDescending(c => c.InsertedAt).FirstOrDefault(); 
            if (bm != null)
            {
                bm.UpdatedAt = DateTime.Now;
                bm.RunAt = null;
                bm.Status = (int)BenchmarkStatuses.Waiting;
                bm.CallerUrl = callerUrl;
                bm.ClientIp = clientIp;
                db.SaveChanges();
            }
            else
            {
                AddBenchmarkApi(url, callerUrl, clientIp);
            }

        }

        public static BenchmarkStatuses HasNewResults(BenchmarkApi benchmarkApi)
        {
            if (benchmarkApi.RunAt != null)
            {
                return BenchmarkStatuses.Ready;
            }
            else
            {
                return BenchmarkStatuses.Waiting;
            }
        }

        public static void SetStatus(string url, BenchmarkStatuses newStatus)
        {
            string hostName = GetHostName(url);
            if (string.IsNullOrEmpty(hostName))
            {
                return;
            }
            using (var db = new StoreYaEntities())
            {
                var b = db.BenchmarkApis.Where(t => t.HostName == hostName).OrderByDescending(c=> c.InsertedAt).FirstOrDefault();
                if (b != null)
                {
                    b.Status = (int)newStatus;
                    b.UpdatedAt = DateTime.Now;
                    b.RunAt = DateTime.Now;
                }
                db.SaveChanges();
            }
        }
        public static BenchmarkApi SetShowAllMessagesSettings(string url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                BenchmarkApi b = AddBenchmarkApi(url, "tester.com", "0.0.0.0");
                if (b.JsonResults != null)
                {
                    return b;
                }
                using (var db = new StoreYaEntities())
                {
                    b.AverageOrderAmount = 0;
                    b.CanonicalUrl = null;
                    b.ContactUsUrl = null;
                    b.CustomerLifetimeValue = 0;
                    b.DataRangeInMonths = 0;
                    b.DescriptionLenght = 0; // 500 characters
                    b.DesktopPerformanceImagesAmount = 0;
                    b.DesktopPerformanceImagesReduction = 0;
                    b.DesktopPerformanceScore = 0;
                    b.HasChatOrMessenger = null;
                    b.HasFbPixelOnProductPage = 0;
                    b.HasGaOnHomePage = 0;
                    b.HasGoogleMerchantCenterOnHomepage = 0;
                    b.HasGooglePixelOnThankYouPage = 0;
                    b.HasGoogleRemarketingCodeOnProductPage = 0;
                    b.HasSocialLinks = 0;
                    b.HomePageHasReviews = 0;
                    b.HostName = GetHostName(url);
                    b.HomePageUrl = url;
                    b.MobilePerformanceImagesAmount = 0;
                    b.MobilePerformanceImagesReduction = 0;
                    b.MobilePerformanceScore = 0;
                    b.OnSitePromotions = 1;
                    b.OrderConfirmationPageUrl = null;
                    b.OrdersAmount = 0;
                    b.PercentageOfDiscountedItems = 0;
                    b.ProductPageHasReviews = 0;
                    b.ProductPageUrl = null;
                    b.ProductsAmount = 200;
                    b.RedirectsFromHttpToHttps = 0;
                    b.RedirectsToCanonical = 0;
                    b.ReturningCustomers = 0;
                    b.ReturnPolicyUrl = null;
                    b.Revenues = 0;
                    b.H1 = null;
                    b.ShippingInfoUrl = null;
                    b.ShopID = 23;
                    b.ShopifyShopName = "";
                    b.Status = (int)BenchmarkStatuses.Ready;
                    b.AdCost = 0;
                    b.FaviconIcoUrl = null;
                    b.MetaTitle = null;
                    b.SiteMapUrl = null;
                    b.AdRevenue = 0;
                    b.BenchmarkDataSource = (int)BenchmarkDataSources.HomePage;
                    b.RobotsTxtUrl = null;
                    b.DeprecatedHtmlTags = "DeprecatedHtmlTags;DeprecatedHtmlTags";
                    b.ImageTagsWithEmptyOrMissingAltAttribute = "imag1,image2,image 3";
                    b.TrustBadges = 0;
                    b.MetaDescription = "";
                    b.RunAt = DateTime.Now;
                    b.InsertedAt = DateTime.Now;
                    b.UpdatedAt = DateTime.Now;

                    db.SaveChanges();
                }
                return b;
            }
            return null;

        }

        public static BenchmarkApi SetShowAllOKMessagesSettings(string url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                BenchmarkApi b = AddBenchmarkApi(url, "tester.com", "0.0.0.0");
                if (b.JsonResults != null)
                {
                    return b;
                }
                using (var db = new StoreYaEntities())
                {
                    b.AverageOrderAmount = 10;
                    b.CanonicalUrl = url + "/CanonicalUrl"; ;
                    b.ContactUsUrl = url + "/ContactUsUrl";
                    b.CustomerLifetimeValue = 10;
                    b.DataRangeInMonths = 10;
                    b.DescriptionLenght = 500;// characters
                    b.DesktopPerformanceImagesAmount = 10;
                    b.DesktopPerformanceImagesReduction = 10;
                    b.DesktopPerformanceScore = 100;
                    b.HasChatOrMessenger = 1;
                    b.HasFbPixelOnProductPage = 1;
                    b.HasGaOnHomePage = 1;
                    b.HasGoogleMerchantCenterOnHomepage = 1;
                    b.HasGooglePixelOnThankYouPage = 1;
                    b.HasGoogleRemarketingCodeOnProductPage = 1;
                    b.HasSocialLinks = 1;
                    b.HomePageHasReviews = 1;
                    b.HostName = GetHostName(url);
                    b.HomePageUrl = url;
                    b.MobilePerformanceImagesAmount = 10;
                    b.MobilePerformanceImagesReduction = 10;
                    b.MobilePerformanceScore = 100;
                    b.OnSitePromotions = 1;
                    b.OrderConfirmationPageUrl = url;
                    b.OrdersAmount = 100;
                    b.PercentageOfDiscountedItems = 20;
                    b.ProductPageHasReviews = 1;
                    b.ProductPageUrl = url;
                    b.ProductsAmount = 200;
                    b.RedirectsFromHttpToHttps = 1;
                    b.RedirectsToCanonical = 1;
                    b.ReturningCustomers = 100;
                    b.ReturnPolicyUrl = url;
                    b.Revenues = 10;
                    b.H1 = "H1";
                    b.ShippingInfoUrl = url + "/ShippingInfoUrl";
                    b.ShopID = 666;
                    b.ShopifyShopName = "ShopifyShopName";
                    b.Status = (int)BenchmarkStatuses.Ready;
                    b.AdCost = 100;
                    b.FaviconIcoUrl = url + "/Favicon.ico";
                    b.MetaTitle = "The recommended number of meta title characters by search engines";
                    b.SiteMapUrl = url + "/Sitemap.xml";
                    b.AdRevenue = 10;
                    b.BenchmarkDataSource = (int)BenchmarkDataSources.HomePage;
                    b.RobotsTxtUrl = url + "/Robots.Txt"; ;
                    b.DeprecatedHtmlTags = null;
                    b.ImageTagsWithEmptyOrMissingAltAttribute = null;
                    b.TrustBadges = 1;
                    b.MetaDescription = "The recommended number of meta title characters by search engines";
                    b.RunAt = DateTime.Now;
                    b.InsertedAt = DateTime.Now;
                    b.UpdatedAt = DateTime.Now;

                    db.SaveChanges();
                }
                return b;
            }
            return null;

        }
        public static BenchmarkApi SetDummySettings(string url)
        {
            BenchmarkApi b = new BenchmarkApi();

            if (!string.IsNullOrEmpty(url))
            {
                b.AverageOrderAmount = 10;
                b.CanonicalUrl = "CanonicalUrl";
                b.ContactUsUrl = "ContactUsUrl";
                b.CustomerLifetimeValue = 111;
                b.DataRangeInMonths = 3;
                b.DescriptionLenght = 500; // 500 characters
                b.DesktopPerformanceImagesAmount = 12;
                b.DesktopPerformanceImagesReduction = 13;
                b.DesktopPerformanceScore = 75;
                b.HasChatOrMessenger = null;
                b.HasFbPixelOnProductPage = 1;
                b.HasGaOnHomePage = 1;
                b.HasGoogleMerchantCenterOnHomepage = 1;
                b.HasGooglePixelOnThankYouPage = 1;
                b.HasGoogleRemarketingCodeOnProductPage = 1;
                b.HasSocialLinks = 1;
                b.HomePageHasReviews = 1;
                b.HostName = GetHostName(url);
                b.HomePageUrl = url;
                b.InsertedAt = DateTime.Now;
                b.MobilePerformanceImagesAmount = 15;
                b.MobilePerformanceImagesReduction = 16;
                b.MobilePerformanceScore = 80;
                b.OnSitePromotions = 1;
                b.OrderConfirmationPageUrl = "OrderConfirmationPageUrl";
                b.OrdersAmount = 18;
                b.PercentageOfDiscountedItems = 30;
                b.ProductPageHasReviews = 1;
                b.ProductPageUrl = "ProductPageUrl";
                b.ProductsAmount = 200;
                b.RedirectsFromHttpToHttps = 1;
                b.RedirectsToCanonical = 1;
                b.ReturningCustomers = 3;
                b.ReturnPolicyUrl = "";
                b.Revenues = 22;
                b.RunAt = DateTime.Now;
                b.ShippingInfoUrl = "ShippingInfoUrl";
                b.ShopID = 23;
                b.ShopifyShopName = "ShopifyShopName";
                b.Status = (int)BenchmarkStatuses.Ready;
                b.TrustBadges = 0;
                b.UpdatedAt = DateTime.Now;
            }

            return b;
        }

        private static int GetShopId(string url)
        {
            var db = DataHelper.GetStoreYaEntities();
            var shop = db.Shops.Where(s => s.BaseUrl.ToLower().EndsWith(url.ToLower()) || s.ShopUrl.ToLower().EndsWith(url.ToLower())).OrderByDescending(i => i.InsertedAt).ToList();
            if (shop != null && shop.Count() > 0)
            {
                return shop.First().ID;

            }
            return 0;
        }

        public static bool SaveJsonResults(int id, string error = null, bool onlyMarketing = false)
        {
            using (var db = new StoreYaEntities())
            {
                var b = db.BenchmarkApis.Where(t => t.ID == id).SingleOrDefault();
                if (b != null)
                {
                    BenchmarkApiJsonResults results = new BenchmarkApiJsonResults();
                    b.Status = (int)BenchmarkStatuses.Ready;
                    b.UpdatedAt = DateTime.Now;
                    b.RunAt = DateTime.Now;
                    if (error == null)
                    {
                        results.Load(b.HomePageUrl);
                        if (onlyMarketing)
                        {
                            b.JsonResults = results.ConvertToBenchmarkApiResultOnlyMarketing().ToJson();
                        }
                        else
                        {
                            b.JsonResults = results.ConvertToBenchmarkApiResult().ToJson();
                        }                        
                    }
                    else
                    {
                        b.Status = (int)BenchmarkStatuses.Error;
                        b.JsonResults = error;
                    }
                }
                db.SaveChanges();
                return true;
            }
        }
        public static BHResult RunAnnalyzation(BenchmarkApi benchmarkApi)
        {
            BHResult result = null;

            try
            {
                int statusToUpdate = (int)BenchmarkStatuses.Ready;
                ConsoleAppHelper.WriteLog("Working with HostName " + benchmarkApi.HostName + " " + DateTime.Now);
                AuditTool t = new AuditTool();
                Console.WriteLine("Getting data. " + DateTime.Now.ToString());
                try
                {
                    t.Data = AuditToolData.GetDataByUrl(benchmarkApi.HomePageUrl);
                }
                catch (System.Net.WebException wEx)
                {
                    var webResponse = wEx.Response as System.Net.HttpWebResponse;
                    if (webResponse != null)
                    {
                        SetStatus(benchmarkApi.HomePageUrl, BenchmarkStatuses.Error);
                        return result = new BHResult()
                        {
                            ReportIsCreated = false,
                            Error = "Failed on AuditToolData.GetData for " + benchmarkApi.HostName + " exception" + wEx.ToString()
                        };
                    }
                }
                catch (Exception ex)
                {
                    SetStatus(benchmarkApi.HomePageUrl, BenchmarkStatuses.Error);
                    string dataError = ex.ToString();
                    if (t.Data != null && !string.IsNullOrEmpty(t.Data.Error))
                    {
                        dataError += " " + t.Data.Error;
                    }
                    return result = new BHResult()
                    {
                        ReportIsCreated = false,
                        Error = "Failed on AuditToolData.GetData for " + benchmarkApi.HostName + " " + dataError
                    };
                }

                t.SetMetaDataShopify();

                Console.WriteLine("Data received - calculating grades. " + DateTime.Now.ToString());
                t.CalcOverallValues();
                //t.CalcTechnicalPerformance();
                t.CalcPurchaseExperience();
                t.CalcMarketingScore();
                t.CalcTrustworthyStore();
                t.CheckRobotsAndSitemapsUrls();


                //if (t.Data.HasGooglePixelOnThankYouPage == false)
                //{
                //    TrafficBooster existingTbSettings = TrafficBoostersDbHelper.GetSettings(shopID);
                //    if (existingTbSettings != null && TbSettingsHelper.IsDefaultBudgetActiveAndPaid(existingTbSettings))
                //    {
                //        t.Data.HasGooglePixelOnThankYouPage = true;
                //    }
                //}


                if (t.WebsiteIsPasswordProtected())
                {
                    statusToUpdate = (int)BenchmarkStatuses.WebsiteIsPasswordProtected;
                }

                Update(benchmarkApi.HomePageUrl, t, statusToUpdate);

                ConsoleAppHelper.WriteLog(" HostName " + benchmarkApi.HostName + ". Done at " + DateTime.Now);

                result = new BHResult() { ReportIsCreated = true };

                if (!string.IsNullOrEmpty(t.Data.Error))
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_EMAIL, "Failed on AuditToolData.GetData for " + benchmarkApi.HostName + " " + DateTime.Now.ToShortDateString(), t.Data.Error);
                }

            }
            catch (Exception ex)
            {
                var exString = ex.ToString();
                if (ex is DbEntityValidationException)
                {
                    var newException = new FormattedDbEntityValidationException((DbEntityValidationException)ex);
                    exString = newException.ToString();
                }

                SetStatus(benchmarkApi.HomePageUrl, BenchmarkStatuses.Error);
                ConsoleAppHelper.WriteLog("Failed RunBenchmarkAnnalyzation. for Hostname:" + benchmarkApi.HostName + " exception:" + exString, 0);

                //Console.WriteLine(string.Format("ShopID: {0} Ex: {1}", shopID, ex.Message));
                //Log4NetLogger.Error("Failed on getting data for Benchmarks Hero tool.", ex, shopID);

                result = new BHResult() { ReportIsCreated = false, Error = exString };
            }
            return result;
        }
    }

    public class BHResult
    {
        public bool ReportIsCreated { get; set; }
        public string Error { get; set; }
    }
    public class BenchmarkApiResults
    {
        public string url { get; set; }
        public string status { get; set; }
        public string totalGrade { get; set; }
        public string error { get; set; }
        public List<BenchmarkApiResult> data { get; set; }
    }

    public class BenchmarkApiResult
    {
        public string category { get; set; }
        public int grade { get; set; }
        public List<Row> rows { get; set; }
    }

    public class Row
    {
        public string title { get; set; }
        public string value { get; set; }
        public string comment { get; set; }
    }

}

