﻿using GoogleChartSharp;
using PostmarkDotNet.Model;
using Storeya.Core.Entities;
using Storeya.Core.Helpers;
using Storeya.Core.Models.CRM;
using Storeya.Core.Models.DataProviders;
using Storeya.Core.Models.ImageOptimizer;
using Storeya.Core.Models.Marketplaces;
using Storeya.Core.Models.ShopAttributes;
using Storeya.Core.Models.ShoppingFeed;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;


namespace Storeya.Core.Models.AuditTool
{
    public class BenchmarkImagesManager
    {
        public enum STEP
        {
            NEW = 1,
            DOWNLOADING = 3,
            FILTER_IMAGES = 5,
            OPTIMIZING = 7,
            UPLOAD = 9,
            RESTORE = 10,
            DOWNLOADING_ALL = 13,
            FILTER_IMAGES_ALL = 15,
            OPT_FROM_EMAIL = 16, //Incase uses from email 
            OPTIMIZING_ALL = 17,
            UPLOAD_ALL = 19,
            REPORT = 21,
            RESTORE_ALL = 23,
            CLEAN = 25,
            FINIAL = 30,
        }
        public enum STATUS
        {
            FAILED = -1,
            INIT = 0,
            STARTED = 2,
            DONE = 1,
            NA = -2
        }
        public const int MAX_RUNS_PER_DAY = 5;
        public const int MAX_PRODUCTS_TO_REVIEW = 20;
        public const int MAX_IMAGES_TO_DOWNLOAD = 1000;
        public const long MAX_IMAGES_SIZE_TO_DOWNLOAD = 500000000;
        public const long MIN_IMAGE_SIZE_TO_OPTIMZE = 100000;

        public const decimal PRECENTAGE_IMAGES_TO_OPTIMIZE = 0.3M;
        public const decimal TIME_SAVED_PER_IMAGE_MIN = 4.8M;
        public static void RunTestFlow(int shopId, int quality, bool upload = false)
        {
            Console.WriteLine($"RunTestFlow ShopId:{shopId}, quality:{quality}");
            Console.WriteLine($"{DateTime.Now}- ImageOptimizerTask START ShopId:{shopId}");
            UpdateStep(shopId, STEP.NEW, STATUS.INIT, "NEW Test flow");
            ImageOptimizerTask(shopId, 20, 50); //1.
            Console.WriteLine($"{DateTime.Now}- ImageOptimizerTask END ShopId:{shopId}");
            UpdateStep(shopId, STEP.DOWNLOADING_ALL, STATUS.INIT, "DOWNLOADING_ALL Test flow");
            Console.WriteLine($"{DateTime.Now}- InitAllImagesOptimization START ShopId:{shopId}");
            InitAllImagesOptimization(shopId); //2.
            Console.WriteLine($"{DateTime.Now}- InitAllImagesOptimization END ShopId:{shopId}");
            UpdateStep(shopId, STEP.OPTIMIZING_ALL, STATUS.INIT, "OPTIMIZING_ALL Test flow");
            Console.WriteLine($"{DateTime.Now}- ExecuteAllImagesOptimization START ShopId:{shopId}");
            ExecuteAllImagesOptimization(shopId, quality, upload); //3
            Console.WriteLine($"{DateTime.Now}- ExecuteAllImagesOptimization END ShopId:{shopId}");
        }
        public static IEnumerable<BenchmarkImagesOptimizationHistory> GetBenchmarkImagesOptimizationHistory(int? status = null, int? step = null, DateTime? fromDate = null, DateTime? toDate = null, int? shopId = null)
        {
            var db = DataHelper.GetStoreYaEntities();

            if (toDate.HasValue)
            {
                toDate = toDate.Value.AddDays(1);
            }
            else
            {
                toDate = DateTime.Now.AddDays(1);
            }
            var p = from c in db.BenchmarkImagesOptimizationHistories
                    where (status == null ? 1 == 1 : c.Status == status)
                    && (step == null ? 1 == 1 : c.Step == step)
                    && (fromDate == null ? 1 == 1 : c.InsertedAt > fromDate && c.InsertedAt < toDate)
                    && (shopId == null ? 1 == 1 : c.ShopId == shopId)
                    orderby c.Id descending
                    select c;
            return p;
        }
        public static IEnumerable<BenchmarkImagesOptimization> GetBenchmarkImagesOptimizations(int? status = null, int? step = null, DateTime? fromDate = null, DateTime? toDate = null, int? shopId = null)
        {
            var db = DataHelper.GetStoreYaEntities();

            if (toDate.HasValue)
            {
                toDate = toDate.Value.AddDays(1);
            }
            else
            {
                toDate = DateTime.Now.AddDays(1);
            }
            var p = from c in db.BenchmarkImagesOptimizations
                    where (status == null ? 1 == 1 : c.Status == status)
                    && (step == null ? 1 == 1 : c.Step == step)
                    && (fromDate == null ? 1 == 1 : c.InsertedAt > fromDate && c.InsertedAt < toDate)
                    && (shopId == null ? 1 == 1 : c.ShopId == shopId)
                    orderby c.Id descending
                    select c;
            return p;
        }
        public static bool HandleRestorePoints(int shopId, string shopPath, int? runCount)
        {
            string shopOriginPath = shopPath + "\\Origin";
            bool RunMoreThenOnce = false;
            if (Directory.Exists(shopPath))
            {
                if (FileDeleterHelper.IsSubFolderExists(shopPath, "origin"))
                {
                    string shopOriginVerPath = $"{shopOriginPath}.{runCount}";
                    if (!Directory.Exists(shopOriginVerPath))
                    {
                        if (!Directory.Exists(shopOriginPath))
                        {
                            Directory.CreateDirectory(shopOriginPath);
                        }
                        Directory.Move(shopOriginPath, shopOriginVerPath);
                        ConsoleAppHelper.WriteLogWithDB($"HandleRestorePoints:Origin folder was created :{shopOriginVerPath}", shopId);
                    }
                    else
                    {
                        ConsoleAppHelper.WriteErrorWithDB($"HandleRestorePoints:Origin folder was not created already exists:{shopOriginVerPath}", null, shopId);
                    }
                    RunMoreThenOnce = true;
                }
                //Array.ForEach(Directory.GetFiles(shopPath), delegate (string path) { File.Delete(path); });
                FileDeleterHelper.DeleteFiles(shopId, shopPath, new List<string>() { "origin" }, new List<string> { ".json" });
            }
            return RunMoreThenOnce;
        }
        public static void ImageOptimizerTask(int shopId, int? maxProductToPreview = null, int? maxImagesToDownload = MAX_IMAGES_TO_DOWNLOAD, long? maxTotalImageSize = MAX_IMAGES_SIZE_TO_DOWNLOAD, bool useCreatedAt = false, List<DataProviders.ShopifyEntities.ProductEntity> shopifyProducts = null)
        {
            string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
            bool stepDone = false;
            string shopPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}";
            int totalProducts = 0;
            int totalImage = 0;
            List<ImageObject> allImages;

            STEP step = STEP.DOWNLOADING_ALL;
            bool RunMoreThenOnce = false;
            bool allowUnlimitedImages = ShopAttributesManager.GetInstance(shopId).IsExists(Attributes.Apps.BenchmarkHero.AllowUnlimitedImages);
            if (!CheckIfAllowedToRun(shopId, out DateTime? lastRunEndedAt))
            {
                return;
            }
            var bio = GetBenchmarkImagesOptimizations(shopId: shopId).FirstOrDefault();
            if (bio != null && bio.ExecuteCounter.HasValue && bio.ExecuteCounter > 0)
            {
                //Reset the status step and folders and json file for new run   
                HandleRestorePoints(shopId, shopPath, bio.ExecuteCounter);
                RunMoreThenOnce = true;
                List<ImageObject> preImageObjects = LoadPreUploadImagesObjects(shopPath);
                if (preImageObjects != null && preImageObjects.Count() > 0)
                {
                    preImageObjects = preImageObjects.Where(x => (x.Status == IMAGE_STATUS.UPLOADED.ToString())).ToList();
                    File.WriteAllText($"{shopPath}\\PreUploadImagesOptResults.json", preImageObjects.ToJson());
                }
                SaveImagesObject(shopId, new List<ImageObject>());
                UpdateStep(shopId, STEP.NEW, STATUS.INIT);
            }

            if (maxProductToPreview.HasValue)
            {
                UpdateStep(shopId, STEP.NEW, STATUS.INIT);
                step = STEP.DOWNLOADING;
            }
            else
            {
                if (allowUnlimitedImages)
                {
                    maxImagesToDownload = null;
                    maxTotalImageSize = null;
                }
            }
            try
            {
                UpdateStep(shopId, step, STATUS.STARTED);
                List<DataProviders.ShopifyEntities.ProductEntity> productsForImageOptimisation = GetProductsForImageOptimisation(shopId, shopifyProducts, lastRunEndedAt, useCreatedAt, out string clientDomain);
                allImages = DownloadImages(productsForImageOptimisation, clientDomain, shopId, shopPath, out totalProducts, out totalImage, maxProductToPreview, maxImagesToDownload, maxTotalImageSize);
                UpdateStep(shopId, step, STATUS.DONE);
                stepDone = true;
            }
            catch (Exception ex)
            {
                UpdateStep(shopId, step, STATUS.FAILED, "ImageOptimizerTask Failed To Download images", ex);
                return;
            }
            step = STEP.FILTER_IMAGES_ALL;
            if (maxProductToPreview.HasValue)
            {
                step = STEP.FILTER_IMAGES;
            }
            if (stepDone)
            {
                stepDone = false;
                try
                {
                    UpdateStep(shopId, step, STATUS.STARTED);
                    if (allImages == null || allImages.Count() == 0)
                    {
                        if (RunMoreThenOnce)
                        {
                            //TODO:Check If need to do somthing here
                        }
                        UpdateStep(shopId, step, STATUS.FAILED, "ImageOptimizerTask Failed To Download images");
                        return;

                    }
                    List<ImageObject> filteredImages = ImageOptimizer.ImageOptimizer.FilterBiggestImages(allImages, MIN_IMAGE_SIZE_TO_OPTIMZE, PRECENTAGE_IMAGES_TO_OPTIMIZE);
                    //Upload To S3 Json
                    string url = SaveImagesObject(shopId, filteredImages);
                    //string url = FeedsManager.SaveFile(shopId, filteredImages.ToJson(), "json", $"ImagesOptResults", rootFolderPath, productImagesPath, subFolder: null);
                    if (step == STEP.FILTER_IMAGES_ALL)
                    {
                        CleanUpImageFolder(shopId, false);
                    }
                    if (maxProductToPreview.HasValue)
                    {
                        UpdateMeta(shopId, totalProducts: totalProducts, totalImages: totalImage, previewTotalImages: allImages.Count(), previewTotalImagesToOptimize: filteredImages.Count(), metaFileUrl: url);
                    }
                    else
                    {
                        int? previewTotalImagesToOptimize = null;
                        int? previewTotalImages = null;
                        if (RunMoreThenOnce)
                        {
                            previewTotalImagesToOptimize = filteredImages.Count();
                            previewTotalImages = totalImage;
                        }
                        UpdateMeta(shopId, totalProducts: totalProducts, previewTotalImages: previewTotalImages, previewTotalImagesToOptimize: previewTotalImagesToOptimize, totalImages: totalImage, totalImagesToOptimize: filteredImages.Count(), metaFileUrl: url);
                    }
                    if (useCreatedAt && step == STEP.FILTER_IMAGES_ALL)
                    {
                        step = STEP.OPT_FROM_EMAIL;
                    }
                    AddHistory(UpdateStep(shopId, step, STATUS.DONE));
                    stepDone = true;
                }
                catch (Exception ex)
                {
                    UpdateStep(shopId, step, STATUS.FAILED, "ImageOptimizerTask Failed To Filter images", ex);
                }
            }
            else
            {
                UpdateStep(shopId, step, STATUS.FAILED, "ImageOptimizerTask");
            }
        }

        private static List<DataProviders.ShopifyEntities.ProductEntity> GetProductsForImageOptimisation(int shopId, List<DataProviders.ShopifyEntities.ProductEntity> shopifyProducts, DateTime? lastRunEndedAt, bool useCreatedAt, out string clientDomain)
        {
            lastRunEndedAt = DateTime.Now.AddDays(-1);
            ShopifyApiProvider api = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.BenchmarkHero);
            clientDomain = api.ClientDomain;
            if (shopifyProducts != null)
            {
                if (lastRunEndedAt != null)
                {
                    return shopifyProducts.Where(x => x.updated_at >= lastRunEndedAt.Value).ToList();
                }
                else
                {
                    return shopifyProducts;
                }
            }
            else
            {
                return api.GetProductsGraphQL(null, maxItems: 5000, dateMin: lastRunEndedAt, useCreatedAt: useCreatedAt);
            }
        }

        public static List<ImageObject> LoadImagesObject(int shopId)
        {
            try
            {
                string s = HttpRequestResponseHelper.GetHttpWebResponseFromFullRequest($"https://strys3.s3.amazonaws.com/{ConfigHelper.GetValue("BenchmarkImages.Path", "BenchmarkUploads")}/{shopId}/ImagesOptResults.json");
                return s.FromJson<List<ImageObject>>();
            }
            catch //(Exception ex)
            {


            }
            return null;
        }

        public static string SaveImagesObject(int shopId, List<ImageObject> imagesObjects)
        {
            try
            {
                string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
                string productImagesPath = ConfigHelper.GetValue("BenchmarkImages.Path");
                string aWSStrys3Domain = ConfigHelper.GetValue("AWSStrys3Domain");
                string url = FeedsManager.SaveFile(shopId, imagesObjects.ToJson(), "json", $"ImagesOptResults", rootFolderPath, productImagesPath, "BenchmarkImages.Path", subFolder: null);
                url = $"{aWSStrys3Domain}{url}";
                //ConsoleAppHelper.WriteLogWithDB($"SaveImagesObject to {url}", shopId);
                return url;
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteErrorWithDB("Failed To SaveImagesObject", ex, shopId);
            }
            return null;
        }
        public static ImageObject GetImageObject(int shopId, int id)
        {
            List<ImageObject> imagesObjects = LoadImagesObject(shopId);
            ImageObject pImageObject = imagesObjects.SingleOrDefault(i => i.Id == id);
            return pImageObject;
        }
        public static List<ImageObject> UpdateImageObject(int shopId, int id, ImageObject imageopbject)
        {
            List<ImageObject> imagesObjects = LoadImagesObject(shopId);
            ImageObject pImageObject = imagesObjects.SingleOrDefault(i => i.Id == id);
            pImageObject.MergeChanges(imageopbject);
            SaveImagesObject(shopId, imagesObjects);
            return imagesObjects;
        }
        public static int GetProductChangedFromLastRun(int shopId, BenchmarkImagesOptimization benchmarkImagesOptimization)
        {
            try
            {
                if (benchmarkImagesOptimization.LastRunEndedAt.HasValue)
                {
                    ShopifyApiProvider api = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.BenchmarkHero);
                    int productsCount = 0;
                    productsCount = api.GetProductsCount(benchmarkImagesOptimization.LastRunEndedAt, false);//discuss
                    //if (useCount)
                    //{
                    //    productsCount = api.GetProductsCount(benchmarkImagesOptimization.LastRunEndedAt, false);
                    //}
                    //else
                    //{
                    //    productsCount = api.GetNewProductCount(false, benchmarkImagesOptimization.LastRunEndedAt);
                    //}

                    return productsCount;
                }
            }
            catch //(Exception ex)
            {
                return 0;
            }
            return -1;
        }

        public static List<ImageObject> DownloadImages(List<DataProviders.ShopifyEntities.ProductEntity> products, string clientDomain, int shopId, string shopPath, out int totalProducts, out int totalImage, int? maxProductCount = null, int? maxImagesToDownload = MAX_IMAGES_TO_DOWNLOAD, long? maxTotalImageSize = MAX_IMAGES_SIZE_TO_DOWNLOAD)
        {
            string excludedExtentions = ConfigHelper.GetValue("MediaOptimizationOptExcludeImageExtentions", "gif,heic,webp");
            List<ImageObject> imageObjects = new List<ImageObject>();
            //ShopifyApiProvider api = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.BenchmarkHero);
            //string clientDomain = api.ClientDomain;
            // var products = api.GetProductsGraphQL(null, maxItems: 5000, dateMin: getProductUpdateAfter, useCreatedAt: useCreatedAt); //GetProducts(max_items: 5000, true, dateMin: getProductUpdateAfter, useCreatedAt: useCreatedAt);
            totalImage = 0;
            totalProducts = products.Count();
            if (totalProducts > 0)
            {
                products = products.Where(p => p.images != null && p.images.Length > 0).ToList();
                totalImage = products.Sum(x => x.images.Count());
            }
            List<string> excludeImageExtentions = excludedExtentions.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).ToList();
            if (maxProductCount.HasValue)
            {
                products = products.OrderByDescending(c => c.published_at).Take(maxProductCount.Value).ToList();
                excludeImageExtentions = excludedExtentions.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).ToList();
            }
            List<ImageObject> preUploadImagesObjects = new List<ImageObject>();
            if (File.Exists($"{shopPath}\\PreUploadImagesOptResults.json"))
            {
                preUploadImagesObjects = File.ReadAllText($"{shopPath}\\PreUploadImagesOptResults.json").FromJson<List<ImageObject>>();
            }

            UpdateProgress(shopId, 1, totalProducts);
            int count = 0;
            int productCount = 0;
            long totalImageSize = 0;
            int failedCount = 20;
            int failedProductsCount = 20;
            int excludedExtCount = 0;
            foreach (var item in products)
            {
                if (item.images.Length > 0)
                {
                    try
                    {
                        foreach (var img in item.images.OrderBy(i => i.position))
                        {
                            string imageUrl = img.src;
                            /////DownLoad
                            var imageFile = ImageNamer.GetFileName(imageUrl, out string extention, out string orignalImageName, img.position.ToString());
                            if (excludeImageExtentions.Contains(extention))
                            {
                                excludedExtCount++;
                                continue;
                            }
                            if (ImageWasOptimized(shopId, item.id, imageUrl, preUploadImagesObjects))
                            {
                                continue;
                            }
                            if (imageObjects.Any(i => i.FileName.ToLower() == imageFile.ToLower()))
                            {
                                imageFile = $"{DateTime.Now.GetRandomFromTicks(5)}_{imageFile}";
                            }
                            string imagePath = $@"{shopPath}\{imageFile}";
                            try
                            {
                                ImageOptimizer.ImageOptimizer.DownloadImage(imageUrl, imagePath, shopId);
                            }
                            catch (Exception ex)
                            {
                                ConsoleAppHelper.WriteError($"Failed To download image: {imageUrl}", ex, shopId);
                                failedCount--;
                                if (failedCount == 0)
                                {
                                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BM -Image Optimize {failedCount} failure to download images:{shopId}", EmailHelper.GetBoLinkHref(shopId, "logs") + $"<br/>Id:{item.id},imageUrl:{imageUrl}<br/> {ex}");
                                    throw new Exception($"Failed To download 20 images aborting last one: {imageUrl}", ex);
                                }
                                continue;
                            }
                            ////Get Image Info
                            FileInfo fi = new FileInfo(imagePath);
                            Image image = Image.FromFile(imagePath);

                            ImageObject imageObject = new ImageObject()
                            {
                                Id = count,
                                ImageId = img.id.HasValue ? img.id.Value : 0,
                                ProductId = item.id,
                                ProductTitle = item.title,
                                ProductUrl = $"{clientDomain}/products/{item.handle}",
                                Position = img.position,
                                ProductPublishedAt = item.published_at,
                                FileName = imageFile,
                                OriginaImageName = orignalImageName,
                                Url = imageUrl,
                                SizeInBytes = fi.Length,
                                Width = image.Width,
                                Height = image.Height,
                                Status = IMAGE_STATUS.CREATED.ToString(),
                                AltTag = img.alt,
                                Variants = new List<string>(),
                            };
                            totalImageSize += fi.Length;
                            if (item.variants.Length > 0)
                            {
                                foreach (var variant in item.variants)
                                {
                                    if (variant.image_id == imageObject.ImageId)
                                    {
                                        imageObject.Variants.Add(variant.id);
                                    }
                                }
                            }
                            image.Dispose();
                            imageObjects.Add(imageObject);
                            count++;

                            if (maxImagesToDownload.HasValue && count > maxImagesToDownload.Value)
                            {
                                return imageObjects;
                            }
                            //Console.WriteLine($"{productCount} - {totalImageSize}");
                            if (maxTotalImageSize.HasValue && totalImageSize > maxTotalImageSize)
                            {
                                return imageObjects;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ConsoleAppHelper.WriteError($"Failed To download product images: {item.ToJson()}", ex, shopId);
                        failedProductsCount--;
                        if (failedCount == 0)
                        {
                            EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BM -Image Optimize {failedProductsCount} failure to download products images:{shopId}", EmailHelper.GetBoLinkHref(shopId, "logs") + $"<br/> {item.ToJson()}<br/> {ex}");
                            throw new Exception($"Failed To download 20  products aborting last one: {item.id}", ex);
                        }
                        continue;
                        throw ex;
                    }
                }
                productCount++;
                UpdateProgress(shopId, productCount, totalProducts);
            }
            if (excludedExtCount > 20)
            {
                string preview = maxProductCount.HasValue ? "Preview" : "";
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BM -Image Optimize {preview} {excludedExtCount} images excluded:{shopId}", $"{EmailHelper.GetBoLinkHref(shopId, "logs")}<br/> Notice {excludedExtCount} images were excluded from total off :{totalImage} with extentions:{excludedExtentions}< br/> total images to optimize:{imageObjects.Count()} ");
            }
            totalImage = count;
            return imageObjects;
        }

        private static bool ImageWasOptimized(int shopId, string productId, string imageUrl, List<ImageObject> preUploadImagesObjects)
        {
            var ims = preUploadImagesObjects.Where(p => p.ProductId == productId);
            if (ims != null)
            {
                foreach (var im in ims)
                {
                    try
                    {
                        int lastDotIndex = im.FileName.LastIndexOf('.');
                        if (lastDotIndex != -1)
                        {
                            string urlUntilPoint = im.FileName.Substring(0, lastDotIndex);
                            if (imageUrl.Contains(urlUntilPoint))
                            {
                                ConsoleAppHelper.WriteLog($"Image was already optimized:{productId} - position:{im.Position},url: {imageUrl}", shopId);
                                return true;
                            }
                        }
                    }
                    catch
                    {

                    }
                }
            }
            return false;
        }
        private static List<ImageObject> LoadPreUploadImagesObjects(string rootPath)
        {
            List<ImageObject> preUploadImagesObjects = new List<ImageObject>();
            if (Directory.Exists(rootPath))
            {
                // SearchOption.AllDirectories to include subdirectories in the search
                string[] jsonFiles = Directory.GetFiles(rootPath, "*.json", SearchOption.AllDirectories);
                foreach (string file in jsonFiles)
                {
                    Console.WriteLine(file);
                    preUploadImagesObjects.AddRange(File.ReadAllText(file).FromJson<List<ImageObject>>());
                }
            }
            return preUploadImagesObjects;
        }
        public static BenchmarkImagesOptimization GetImagesOptimization(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(s => s.ShopId == shopId);
            return bio;
        }
        public static BenchmarkImagesOptimization InitImagesOptimization(int shopId, STEP step = STEP.NEW, STATUS status = STATUS.STARTED)
        {
            var db = DataHelper.GetStoreYaEntities();
            var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(s => s.ShopId == shopId);
            if (bio == null)
            {
                BenchmarkImagesOptimization bioNew = new BenchmarkImagesOptimization()
                {
                    InsertedAt = DateTime.Now,
                    ShopId = shopId,
                    Step = (int)step,
                    Status = (int)status,
                    ExecuteCounter = 0,
                    ExecuteCounterMax = 0,
                };
                db.BenchmarkImagesOptimizations.Add(bioNew);
            }
            else
            {

                //if (bio.Step == (int)STEP.RESTORE_ALL && bio.Status == (int)STATUS.DONE)
                //{
                //    resetExecuteCounter = true;
                //}
                ConsoleAppHelper.WriteLogDebugWithDB($"InitImagesOptimization,Status update:{(STATUS)bio.Status} -> {status}, Step:{(STEP)bio.Step} -> {step}", shopId);
                bio.UpdatedAt = DateTime.Now;
                bio.Status = status.GetHashCode();
                bio.Step = step.GetHashCode();
                //if (step == STEP.NEW && status == STATUS.STARTED)
                //{

                //    bio.StepStartedAt = null;
                //    bio.StepEndedAt = null;
                //    bio.SetQualityTo = null;
                //    bio.TotalImages = null;
                //    bio.TotalImagesToOptimize = null;
                //    bio.ImagesToResize = null;
                //    bio.ImagesMissingAltTag = null;
                //    bio.TotalProducts = null;
                //    bio.PreviewTotalImages = null;
                //    bio.PreviewTotalImagesToOptimize = null;
                //    bio.TotalUploadedImages = null;
                //    bio.AvgImagesSizeAfterOptimize = null;
                //    bio.TotalImagesSizeAfterOptimize = null;
                //    bio.PercentageSaved = null;
                //    bio.TimeSaved = null;
                //    bio.Results = null;
                //}
                //if (resetExecuteCounter)
                //{
                //    bio.ExecuteCounter = 0;
                //}
            }
            db.SaveChanges();
            bio = db.BenchmarkImagesOptimizations.SingleOrDefault(s => s.ShopId == shopId);
            return bio;
        }
        public static STATUS GetStepStatus(int shopId, STEP step, out BenchmarkImagesOptimization bio)
        {
            var db = DataHelper.GetStoreYaEntities();
            bio = db.BenchmarkImagesOptimizations.SingleOrDefault(s => s.ShopId == shopId);
            if (bio == null)
            {
                return STATUS.NA;
            }
            if (bio.Step == (int)step)
            {
                return (STATUS)bio.Status;
            }
            if (bio.Status == (int)STATUS.FAILED)
            {
                return STATUS.FAILED;
            }
            return STATUS.STARTED;
        }
        public static void UpdateProgress(int shopId, int value, int total)
        {

            var db = DataHelper.GetStoreYaEntities();
            var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(s => s.ShopId == shopId);
            if (bio != null)
            {
                bio.Progress = value;
                bio.ImagesToCompress = total;
                db.SaveChanges();
            }
        }
        public static List<BenchmarkImagesOptimization> GetBIOs(int daysBack, int? limitDays = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            DateTime utilUpdatedAt = DateTime.Now.AddDays(-daysBack);
            if (limitDays.HasValue && limitDays.Value > 0)
            {
                DateTime fromUpdatedAt = utilUpdatedAt.AddDays(-limitDays.Value);
                return db.BenchmarkImagesOptimizations.Where(b => b.UpdatedAt < utilUpdatedAt && b.UpdatedAt > fromUpdatedAt).ToList();
            }
            return db.BenchmarkImagesOptimizations.Where(b => b.UpdatedAt < utilUpdatedAt).ToList();
        }

        public static bool CheckIfAllowedToRun(int shopId, out DateTime? lastRunEndedAt, int limitRunsPerDays = MAX_RUNS_PER_DAY)
        {
            lastRunEndedAt = null;
            var db = DataHelper.GetStoreYaEntities();
            var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(s => s.ShopId == shopId);
            if (bio == null)
            {
                return true;
            }
            lastRunEndedAt = bio.LastRunEndedAt;
            if (bio.LastRunEndedAt.HasValue && bio.Step == STEP.FINIAL.GetHashCode())
            {
                if (!bio.ExecuteCounterMax.HasValue)
                {
                    bio.ExecuteCounterMax = 0;
                    db.SaveChanges();
                }
                if (DateTime.Now.Date == bio.LastRunEndedAt.Value.Date && bio.ExecuteCounterMax.Value > limitRunsPerDays - 1)
                {
                    return false;
                }
                return true;
            }
            return true;
        }

        public static BenchmarkImagesOptimization UpdateStep(int shopId, STEP step = STEP.NEW, STATUS status = STATUS.STARTED, string results = null, Exception ex = null)
        {
            var db = DataHelper.GetStoreYaEntities();

            var bio = InitImagesOptimization(shopId, step, status);
            if (status == STATUS.STARTED)
            {
                bio.StepStartedAt = DateTime.Now;
                bio.StepEndedAt = null;
            }

            if (step == STEP.NEW && status == STATUS.INIT)
            {
                ConsoleAppHelper.WriteLogDebugWithDB($"InitImagesOptimization,Date pre reset:{bio.ToJson()}", shopId);
                if (bio.StepEndedAt.HasValue && bio.StepEndedAt.Value.Date != DateTime.Now.Date)
                {
                    bio.ExecuteCounterMax = 1;
                }
                bio.StepStartedAt = null;
                bio.StepEndedAt = null;
                bio.SetQualityTo = null;
                bio.TotalImages = null;
                bio.TotalImagesToOptimize = null;
                bio.ImagesToResize = null;
                bio.ImagesMissingAltTag = null;
                bio.TotalProducts = null;
                bio.PreviewTotalImages = null;
                bio.PreviewTotalImagesToOptimize = null;
                bio.TotalUploadedImages = null;
                bio.AvgImagesSizeAfterOptimize = null;
                bio.TotalImagesSizeAfterOptimize = null;
                bio.PercentageSaved = null;
                //bio.TimeSaved = null;
                bio.Results = null;
            }

            if (status == STATUS.INIT)
            {
                bio.ImagesToCompress = null;
                bio.Progress = null;
            }
            if (step == STEP.FINIAL)
            {
                if (bio.LastRunEndedAt.HasValue && bio.LastRunEndedAt.Value.Date == DateTime.Now.Date)
                {
                    if (bio.ExecuteCounterMax.HasValue && bio.ExecuteCounterMax.Value > 0)
                    {
                        bio.ExecuteCounterMax++;
                    }
                    else
                    {
                        bio.ExecuteCounterMax = 1;
                    }
                }
                if (bio.ExecuteCounter.HasValue && bio.ExecuteCounter.Value > 0)
                {
                    bio.ExecuteCounter++;
                }
                else
                {
                    bio.ExecuteCounter = 1;
                }
                if (status == STATUS.DONE)
                {
                    bio.LastRunEndedAt = DateTime.Now;
                }
            }
            if (!string.IsNullOrEmpty(results))
            {
                string exMessage = ex == null ? "" : ",ex:" + ex.Message;
                if (string.IsNullOrEmpty(bio.Results))
                {
                    bio.Results = $"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")} {results} {exMessage}";
                }
                else
                {
                    bio.Results = $"{bio.Results},{Environment.NewLine}{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")} {results} {exMessage}";
                }
            }

            if (status == STATUS.DONE)
            {
                bio.StepEndedAt = DateTime.Now;
                ConsoleAppHelper.WriteLogDebugWithDB($"BM ImagesOptimization Step:{step} ,Status:{status} ,StepStartedAt:{bio.StepStartedAt} TotalSeconds:{(bio.StepEndedAt.Value - (bio.StepStartedAt.HasValue ? bio.StepStartedAt.Value : DateTime.Now)).TotalSeconds},Counter:{bio.ExecuteCounter} - {results}", shopId);
            }
            else if (status == STATUS.FAILED)
            {
                bio.StepEndedAt = DateTime.Now;
                ConsoleAppHelper.WriteErrorWithDB($"BM ImagesOptimization Step:{step} ,Status: {status} ,StepStartedAt: {bio.StepStartedAt} TotalSeconds: {(bio.StepEndedAt.Value - (bio.StepStartedAt.HasValue ? bio.StepStartedAt.Value : DateTime.Now)).TotalSeconds},Counter:{bio.ExecuteCounter}   - {results}", ex, shopId);
            }

            db.SaveChanges();
            return bio;
        }

        public static BenchmarkImagesOptimization UpdateMeta(int shopId, STEP step = STEP.NEW, STATUS status = STATUS.STARTED,
            string metaFileUrl = null, string setQualityTo = null, int? totalProducts = null, int? totalImages = null, int? totalImagesToOptimize = null,
            int? imagesToResize = null, int? imagesMissingAltTag = null,
            int? previewTotalImages = null, int? previewTotalImagesToOptimize = null)
        {
            var db = DataHelper.GetStoreYaEntities();
            var bio = InitImagesOptimization(shopId, step, status);
            if (status == STATUS.STARTED)
            {
                bio.StepStartedAt = DateTime.Now;
                bio.StepEndedAt = null;
            }
            else if (status == STATUS.DONE || status == STATUS.FAILED)
            {
                bio.StepEndedAt = DateTime.Now;
            }
            if (metaFileUrl != null)
            {
                bio.MetaFileUrl = metaFileUrl;
            }

            bio.SetQualityTo = setQualityTo;
            bio.TotalImages = totalImages;
            bio.TotalImagesToOptimize = totalImagesToOptimize;
            bio.ImagesToResize = imagesToResize;
            //bio.ImagesToCompress = imagesToCompress;
            bio.ImagesMissingAltTag = imagesMissingAltTag;
            bio.TotalProducts = totalProducts;
            if (previewTotalImages.HasValue)
            {
                bio.PreviewTotalImagesToOptimize = previewTotalImagesToOptimize;
                bio.PreviewTotalImages = previewTotalImages;
            }
            db.SaveChanges();
            return bio;
        }

        public static void ExecuteAllImagesOptimization(int shopId, int quality, bool upload = false)
        {
            STEP currentStep = STEP.OPTIMIZING_ALL;
            List<ImageObject> imagesObjects = LoadImagesObject(shopId);

            try
            {
                var db = DataHelper.GetStoreYaEntities();
                //var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId && i.Step == (int)STEP.OPTIMIZING_ALL && i.Status == (int)STATUS.INIT);
                var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId);
                if (bio == null)
                {
                    ConsoleAppHelper.WriteLog($"ExecuteAllImagesOptimization cannot find DB recored", shopId);
                    return;
                }
                else
                {
                    if (bio.Step == (int)STEP.DOWNLOADING_ALL && bio.Status == (int)STATUS.INIT)
                    {
                        UpdateStep(shopId, currentStep, STATUS.STARTED);
                    }
                    else if (bio.Step == (int)STEP.OPTIMIZING_ALL)
                    {

                    }
                    else
                    {
                        ConsoleAppHelper.WriteLog($"ExecuteAllImagesOptimization need to run when step is OPTIMIZING_ALL and status INIT", shopId);
                        return;
                    }
                }
                if (imagesObjects == null)
                {
                    ConsoleAppHelper.WriteLog($"Cannot Find the Images Data file: {bio.MetaFileUrl} needs to run the proccess from the start.", shopId);
                    UpdateStep(shopId, currentStep, STATUS.FAILED, $"Cannot Find the Images Data file: {bio.MetaFileUrl} needs to run the proccess from the start.");
                    return;
                }
                UpdateStep(shopId, currentStep, STATUS.STARTED);
                ConsoleAppHelper.WriteLog($"Start ExecuteAllImagesOptimization on total Images: {bio.TotalImages}", shopId);
                string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
                string productImagesPath = ConfigHelper.GetValue("BenchmarkImages.Path");
                string aWSStrys3Domain = ConfigHelper.GetValue("AWSStrys3Domain");

                string shopPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}";
                string uploadPath = ImageUtilities.OptimizeFolder(shopPath, quality, out List<string> failedImages);
                int totalImages = imagesObjects.Count();
                int interval = Math.DivRem(totalImages, 10, out int remainder);
                int count = 1;
                int errorCheckingCounter = 0;
                foreach (var imageFile in Directory.GetFiles(uploadPath))
                {
                    string imageFileName = Path.GetFileName(imageFile);

                    var imageObject = imagesObjects.SingleOrDefault(i => i.FileName == imageFileName);
                    if (imageObject != null)
                    {
                        UpdateProgress(shopId, count, imagesObjects.Count);
                        try
                        {
                            imageObject.Status = IMAGE_STATUS.OPTIMIZED.ToString();
                            FileInfo fi = new FileInfo(imageFile);
                            Image image = Image.FromFile(imageFile);
                            imageObject.OptimizeSizeInBytes = fi.Length;
                            imageObject.OptimizeWidth = image.Width;
                            imageObject.OptimizeHeight = image.Height;
                            image.Dispose();
                            if (imageObject.OptimizeSizeInBytes > imageObject.SizeInBytes)
                            {
                                imageObject.Status = IMAGE_STATUS.LARGER.ToString();
                            }
                            else
                            {
                                if (100 - Math.Round((decimal)imageObject.OptimizeSizeInBytes / (decimal)imageObject.SizeInBytes * 100, 2) < 5.1M)
                                {
                                    imageObject.Status = IMAGE_STATUS.NO_MAGER_CHANGE.ToString();
                                }
                                else
                                {
                                    if (count == 1 || interval == 0 || (interval > 0 && (count % interval) == 0))
                                    {
                                        imageObject.ReviewOptimizeUrl = aWSStrys3Domain + FeedsManager.SaveImageFile(shopId, imageFile, rootFolderPath, productImagesPath, "BenchmarkImages.Path", subFolder: null);
                                        imageObject.ReviewOriginUrl = aWSStrys3Domain + FeedsManager.SaveImageFile(shopId, imageFile.Replace("Upload\\", ""), rootFolderPath, productImagesPath, "BenchmarkImages.Path", subFolder: "origin");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            errorCheckingCounter++;
                            if (errorCheckingCounter > 6)
                            {
                                ConsoleAppHelper.WriteError($"Failed TO check image : {imageFile} ", ex, shopId);
                                if (errorCheckingCounter > 20)
                                {
                                    throw new Exception($"Failed To check 20 images aborting last one: {imageFile}", ex);
                                }
                            }
                            else
                            {
                                ConsoleAppHelper.WriteErrorWithDB($"Failed TO check image : {imageFile} ", ex, shopId);
                            }
                        }
                        count++;
                    }
                }
                if (errorCheckingCounter > 6)
                {
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BM -Image Optimize {errorCheckingCounter} failures to get image date:{shopId}", EmailHelper.GetBoLinkHref(shopId, "logs") + "<br/> notice only first 6 will show on the shop logs the rest will be in the server log");
                }
                if (failedImages != null)
                {
                    foreach (var imageFile in failedImages)
                    {
                        string imageFileName = Path.GetFileName(imageFile);
                        var imageObject = imagesObjects.SingleOrDefault(i => i.FileName == imageFileName);
                        if (imageObject != null)
                        {
                            imageObject.Status = IMAGE_STATUS.FAILED.ToString();
                        }
                    }
                }
                UpdateStep(shopId, currentStep, STATUS.DONE);
                SaveImagesObject(shopId, imagesObjects);


                if (upload)
                {
                    ConsoleAppHelper.WriteLog($"UploadImagesToShopify START ShopId:{shopId}, Step:{currentStep}", shopId);
                    currentStep = UploadImagesToShopify(shopId, currentStep);
                    ConsoleAppHelper.WriteLog($"UploadImagesToShopify END ShopId:{shopId}, Step:{currentStep}", shopId);
                    GenerateFinalReport(shopId, quality.ToString(), currentStep);
                }
                else
                {
                    GenerateFinalReport(shopId, quality.ToString(), currentStep);
                    CleanUpImageFolder(shopId, removeUploadFolder: true);
                }
                ConsoleAppHelper.WriteLog($"Done ExecuteAllImagesOptimization on total Images: {bio.TotalImages}", shopId);
                currentStep = STEP.FINIAL;
                AddHistory(UpdateStep(shopId, currentStep, STATUS.DONE));
                SystemEventHelper.Add(shopId, AppTypes.BenchmarkHero.GetHashCode(), SystemEventTypes.BHReport, SystemEventActions.BenchmarkImagesOptimizeAll);


            }
            catch (Exception ex)
            {
                UpdateStep(shopId, currentStep, STATUS.FAILED, "Failed to run ExecuteAllImagesOptimization", ex);

            }
        }

        public static STEP UploadImagesToShopify(int shopId, STEP startFromStep = STEP.REPORT)
        {
            try
            {

                var db = DataHelper.GetStoreYaEntities();
                var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId && i.Step == (int)startFromStep);
                if (bio == null)
                {
                    ConsoleAppHelper.WriteLog($"UploadImagesToShopify will not run step should be Report!,  total Images: {bio.TotalImages}", shopId);
                    throw new Exception($"UploadImagesToShopify will not run step should be Report!,  total Images: {bio.TotalImages}");
                }
                else
                {
                    string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
                    //string productImagesPath = ConfigHelper.GetValue("ProductImages.Path");
                    string shopPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}\Upload";
                    UpdateStep(shopId, STEP.UPLOAD_ALL, STATUS.STARTED);
                    ShopifyApiProvider shopifyApiProvider = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.BenchmarkHero);
                    List<ImageObject> imagesObjects = LoadImagesObject(shopId);
                    int countUpload = 0;
                    int failedToDeleteCount = 0;
                    int totalimagesToUpload = imagesObjects.Count(i => i.Status == IMAGE_STATUS.OPTIMIZED.ToString());
                    foreach (var imageObject in imagesObjects.Where(i => i.Status == IMAGE_STATUS.OPTIMIZED.ToString()))
                    {
                        try
                        {
                            UpdateProgress(shopId, countUpload, totalimagesToUpload);
                            string altTag = string.IsNullOrEmpty(imageObject.AltTag) ? imageObject.ProductTitle : imageObject.AltTag;
                            string optimizedImagePath = $"{shopPath}\\{imageObject.FileName}";
                            dynamic results = shopifyApiProvider.UploadProductImage(long.Parse(imageObject.ProductId), optimizedImagePath, altTag, imageObject.Position, imageObject.OriginaImageName);
                            if (results.image != null)
                            {
                                imageObject.OptimizeImageId = results.image.id;
                                if (imageObject.Variants != null)
                                {
                                    foreach (string variantId in imageObject.Variants)
                                    {
                                        shopifyApiProvider.UpdateVariantImage(variantId, imageObject.OptimizeImageId.ToString());
                                    }
                                }
                                imageObject.Status = IMAGE_STATUS.UPLOADED.ToString();
                                countUpload++;
                                try
                                {
                                    dynamic delResults = shopifyApiProvider.DeleteProductImage(long.Parse(imageObject.ProductId), imageObject.ImageId);

                                }
                                catch (Exception ex)
                                {
                                    dynamic delResults = shopifyApiProvider.DeleteProductImage(long.Parse(imageObject.ProductId), imageObject.OptimizeImageId);
                                    failedToDeleteCount++;
                                    countUpload--;
                                    imageObject.Status = IMAGE_STATUS.UPLOADED_FAILED.ToString();
                                    ConsoleAppHelper.WriteError($"Failed to delete image: {imageObject.ImageId} from product {imageObject.ProductId} reverting upload!", ex, shopId);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            imageObject.Status = $"{IMAGE_STATUS.FAILED.ToString()} - {ex.Message}";
                        }
                    }

                    bio.TotalUploadedImages = countUpload;
                    db.SaveChanges();
                    string objectUrl = SaveImagesObject(shopId, imagesObjects);
                    BackupFirstUploaddedImages(shopId);
                    //string url = FeedsManager.SaveFile(shopId, imagesObjects.ToJson(), "json", $"ImagesOptResults", rootFolderPath, productImagesPath, "BenchmarkImages.Path", subFolder: null);
                    UpdateStep(shopId, STEP.UPLOAD_ALL, STATUS.DONE);
                    CleanUpImageFolder(shopId, removeUploadFolder: true);
                    if (failedToDeleteCount > 9)
                    {
                        EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BH Image optimize has failed To upload images - {shopId}", $"{EmailHelper.AdminLinkHref(shopId)} <br/>Failed to delete:{failedToDeleteCount} uploaded:{countUpload}<br/><a href='{objectUrl}' >Images object Url</a>");
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStep(shopId, STEP.UPLOAD_ALL, STATUS.FAILED, "Failed to UploadImagesToShopify", ex);
            }
            return STEP.CLEAN;
        }
        private static bool BackupFirstUploaddedImages(int shopId)
        {
            string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
            string shopPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}";
            string shopOriginPath = shopPath + "\\Origin";
            try
            {
                if (Directory.Exists(shopOriginPath))
                {
                    return false;
                }
                Directory.CreateDirectory(shopOriginPath);
                List<ImageObject> imagesObjects = LoadImagesObject(shopId);
                foreach (var imageObject in imagesObjects.Where(i => i.Status == IMAGE_STATUS.UPLOADED.ToString()))
                {
                    string originalImagePath = $"{shopPath}\\{imageObject.FileName}";
                    string backupImagePath = $"{shopOriginPath}\\{imageObject.FileName}";
                    if (File.Exists(originalImagePath) && !File.Exists(backupImagePath))
                    {
                        File.Copy(originalImagePath, backupImagePath);
                    }
                }
                File.WriteAllText($"{shopOriginPath}\\ImagesOptResults.json", imagesObjects.ToJson());
                return true;
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteErrorWithDB($"BackupFirstUploaddedImages failed to execute on folder: {shopOriginPath}", ex, shopId);
                return false;
            }
        }

        public static void RestoreAllToOriginal(int shopId)
        {
            var bio = UpdateStep(shopId, STEP.RESTORE_ALL, STATUS.INIT);
            for (int i = 0; i < bio.ExecuteCounter - 1; i++)
            {
                RestoreImagesToShopify(shopId, true, i);
            }
            RestoreImagesToShopify(shopId, true);
        }


        public static void RestoreOriginalImages(int shopId, int? restorePoint = null, bool dontDeleteIfFailed = false, bool testMode = false)
        {
            UpdateStep(shopId, STEP.RESTORE_ALL, STATUS.INIT);
            RestoreImagesToShopify(shopId, true, restorePoint, dontDeleteIfFailed);
        }
        private static string DownloadImageForTester(int shopId, string shopPath, string imageUrl, int position)
        {
            string imageFile = ImageNamer.GetFileName(imageUrl, out string extention, out string orignalImageName, position.ToString());
            string imagePath = $@"{shopPath}\{imageFile}";
            try
            {
                ImageOptimizer.ImageOptimizer.DownloadImage(imageUrl, imagePath, shopId);

            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteError($"Failed To download image: {imageUrl}", ex, shopId);
            }
            return imagePath;
        }
        public static void RestoreImagesToShopifyTester(int shopId, bool restoreFromOrigin = false, int? restorePoint = null, bool downloadCurrentImage = false)
        {
            try
            {
                int failedToRestoreCount = 0;
                int countRestore = 1;
                var db = DataHelper.GetStoreYaEntities();
                var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId);

                if (bio == null)
                {
                    ConsoleAppHelper.WriteLogWithDB($"RestoreImagesToShopifyTester will not run Cannot find BenchmarkImagesOptimizations !", shopId);
                    return;
                }
                else
                {
                    string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
                    //string productImagesPath = ConfigHelper.GetValue("ProductImages.Path");
                    string shopBakupPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}";
                    //string resultMessage = null;

                    ShopifyApiProvider shopifyApiProvider = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.BenchmarkHero);
                    List<ImageObject> imagesObjects = LoadImagesObject(shopId);
                    if (restoreFromOrigin)
                    {
                        shopBakupPath = $"{shopBakupPath}\\Origin";
                        if (restorePoint.HasValue && restorePoint.Value > 0)
                        {
                            shopBakupPath = $"{shopBakupPath}.{restorePoint}";
                        }
                        if (Directory.Exists(shopBakupPath))
                        {

                            imagesObjects = File.ReadAllText($"{shopBakupPath}\\ImagesOptResults.json").FromJson<List<ImageObject>>();

                            ConsoleAppHelper.WriteLog($"RestoreImagesToShopifyTester from origin folder.", shopId);
                        }
                        else
                        {
                            ConsoleAppHelper.WriteLog($"RestoreImagesToShopify {shopBakupPath} not exists.", shopId);
                            return;
                        }
                    }
                    int totalimagesToRestore = imagesObjects.Count(i => i.Status == IMAGE_STATUS.UPLOADED.ToString());
                    if (downloadCurrentImage)
                    {
                        if (!Directory.Exists($"{shopBakupPath}\\Download"))
                        {
                            Directory.CreateDirectory($"{shopBakupPath}\\Download");
                        }
                    }

                    foreach (var imageObject in imagesObjects.Where(i => i.Status == IMAGE_STATUS.UPLOADED.ToString()))
                    {
                        try
                        {
                            //UpdateProgress(shopId, countRestore, totalimagesToRestore);
                            ConsoleAppHelper.WriteLog($"Testing restore: {countRestore} of {totalimagesToRestore} - {imageObject.FileName}");
                            string altTag = string.IsNullOrEmpty(imageObject.AltTag) ? imageObject.ProductTitle : imageObject.AltTag;
                            string optimizedImagePath = $"{shopBakupPath}\\{imageObject.FileName}";

                            dynamic allResults = shopifyApiProvider.GetProductImage(long.Parse(imageObject.ProductId), optimizedImagePath, altTag, imageObject.Position, imageObject.OriginaImageName);

                            dynamic results = null;
                            if (allResults.images != null)
                            {
                                foreach (var item in allResults.images)
                                {
                                    if (item.position == imageObject.Position)
                                    {
                                        results = item;
                                    }
                                    if (downloadCurrentImage)
                                    {
                                        string imagePath = DownloadImageForTester(shopId, $"{shopBakupPath}\\Download", (string)item.src, (int)item.position);
                                    }
                                }
                            }

                            Console.WriteLine($"{imageObject.ProductId} - {imageObject.Position}:Checking Restored {countRestore} of {totalimagesToRestore} - {optimizedImagePath}");

                            if (!File.Exists(optimizedImagePath))
                            {
                                ConsoleAppHelper.WriteError($"{imageObject.ProductId} - {imageObject.Position}: Failed to find Image file locally:{optimizedImagePath}", null, shopId);
                            }
                            if (results != null)
                            {
                                if (results.id == imageObject.OptimizeImageId)
                                {
                                    imageObject.ImageId = results.id;
                                    imageObject.Status = "CAN BE RESTORED";
                                    Console.WriteLine($"Restored {countRestore} of {totalimagesToRestore} - {imageObject.ImageId}");
                                    if (results.Variants != null)
                                    {
                                        foreach (var variantId in results.variant_ids)
                                        {
                                            if (!imageObject.Variants.Contains(variantId.ToString()))
                                            {
                                                imageObject.Status = $"{IMAGE_STATUS.FAILED.ToString()} - VARIANT FAILED";
                                                Console.WriteLine($"{imageObject.ProductId} - {imageObject.Position}:Test Restored  UpdateVariantImage- variantId:{variantId}, {imageObject.ImageId.ToString()} was not found in JSON file");
                                            }
                                            // shopifyApiProvider.UpdateVariantImage(variantId, imageObject.ImageId.ToString());

                                        }
                                    }
                                }
                                else
                                {
                                    ConsoleAppHelper.WriteError($"{imageObject.ProductId} - {imageObject.Position}:Test Failed to find image Id was changed:api:{results.id}, OptimizeImageId {imageObject.OptimizeImageId} , {optimizedImagePath} - reverting upload!", null, shopId);
                                    imageObject.Status = $"{IMAGE_STATUS.FAILED.ToString()} - NOT SAME ID";
                                    failedToRestoreCount++;
                                    // Console.WriteLine($"Test Restored Failed to Delete - {optimizedImagePath} ");
                                }
                            }
                            else
                            {
                                failedToRestoreCount++;
                                imageObject.Status = $"{IMAGE_STATUS.FAILED.ToString()} - FAILED TO GET IMAGE";
                                ConsoleAppHelper.WriteError($"{imageObject.ProductId} - {imageObject.Position}:Test Restored Failed to get Image ImagePath:{optimizedImagePath} ", null, shopId);
                            }
                        }
                        catch (Exception ex)
                        {
                            failedToRestoreCount++;
                            imageObject.Status = $"{IMAGE_STATUS.FAILED.ToString()} - {ex.Message}";

                        }
                        countRestore++;
                    }
                    File.WriteAllText($"{shopBakupPath}\\ImagesOptResultsRestoreTester.json", imagesObjects.ToJson());
                    if (failedToRestoreCount > 0)
                    {
                        if (failedToRestoreCount > 9)
                        {
                            Stream stream = new MemoryStream(File.ReadAllBytes($"{shopBakupPath}\\ImagesOptResultsRestoreTester.json"));
                            System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(stream, "ImagesOptResultsRestoreTester.json");
                            List<System.Net.Mail.Attachment> attachments = new List<System.Net.Mail.Attachment>() {
                            attachment
                            };
                            EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BH Image optimize test restore results - {shopId}", $"{EmailHelper.AdminLinkHref(shopId)} <br/>Restore tester results Failed :{failedToRestoreCount} restored:{countRestore - failedToRestoreCount} total:{countRestore}<br/>", attachments: attachments);
                        }
                        ConsoleAppHelper.WriteError($"BH Image optimize test restore results - {shopId} Failed to restore:{failedToRestoreCount} can be restored:{countRestore - failedToRestoreCount} total:{countRestore} json:{$"{shopBakupPath}\\ImagesOptResultsRestoreTester.json"}", null, shopId);
                    }
                    else
                    {
                        ConsoleAppHelper.WriteLog($"BH Image optimize test restore PASS results - {shopId} can be restored:{countRestore} total:{countRestore} json:{$"{shopBakupPath}\\ImagesOptResultsRestoreTester.json"}", shopId);
                    }
                }
            }
            catch (Exception ex)
            {

                ConsoleAppHelper.WriteError($"BH Image optimize test restore results - {shopId} ,Failed to restore", ex, shopId);
            }
        }

        public static void RestoreMissingImagesByNameToShopify(int shopId, bool restoreFromOrigin = false, int? restorePoint = null, bool downloadCurrentImage = false, bool testMode = false)
        {
            try
            {
                int failedToRestoreCount = 0;
                int countRestore = 1;
                var db = DataHelper.GetStoreYaEntities();
                var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId);

                if (bio == null)
                {
                    ConsoleAppHelper.WriteLogWithDB($"RestoreImagesToShopifyTester will not run Cannot find BenchmarkImagesOptimizations !", shopId);
                    return;
                }
                else
                {
                    string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
                    //string productImagesPath = ConfigHelper.GetValue("ProductImages.Path");
                    string shopBakupPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}";
                    ShopifyApiProvider shopifyApiProvider = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.BenchmarkHero);
                    List<ImageObject> imagesObjects = LoadImagesObject(shopId);
                    if (restoreFromOrigin)
                    {
                        shopBakupPath = $"{shopBakupPath}\\Origin";
                        if (restorePoint.HasValue && restorePoint.Value > 0)
                        {
                            shopBakupPath = $"{shopBakupPath}.{restorePoint}";
                        }
                        if (Directory.Exists(shopBakupPath))
                        {

                            imagesObjects = File.ReadAllText($"{shopBakupPath}\\ImagesOptResults.json").FromJson<List<ImageObject>>();
                            ConsoleAppHelper.WriteLog($"RestoreImagesToShopifyTester from origin folder.", shopId);
                        }
                        else
                        {
                            ConsoleAppHelper.WriteLog($"RestoreImagesToShopify {shopBakupPath} not exists.", shopId);
                            return;
                        }
                    }
                    int totalimagesToRestore = imagesObjects.Count(i => i.Status == IMAGE_STATUS.UPLOADED.ToString());
                    if (downloadCurrentImage)
                    {
                        if (!Directory.Exists($"{shopBakupPath}\\Download"))
                        {
                            Directory.CreateDirectory($"{shopBakupPath}\\Download");
                        }
                    }

                    foreach (var imageObject in imagesObjects.Where(i => i.Status == IMAGE_STATUS.UPLOADED.ToString()))
                    {
                        try
                        {
                            //UpdateProgress(shopId, countRestore, totalimagesToRestore);
                            ConsoleAppHelper.WriteLog($"Testing restore: {countRestore} of {totalimagesToRestore} - {imageObject.FileName}");
                            string altTag = string.IsNullOrEmpty(imageObject.AltTag) ? imageObject.ProductTitle : imageObject.AltTag;
                            string optimizedImagePath = $"{shopBakupPath}\\{imageObject.FileName}";

                            dynamic allResults = shopifyApiProvider.GetProductImage(long.Parse(imageObject.ProductId), optimizedImagePath, altTag, imageObject.Position, imageObject.OriginaImageName);
                            bool toUpload = true;
                            dynamic results = null;
                            if (allResults.images != null)
                            {
                                foreach (var item in allResults.images)
                                {
                                    if (item.position == imageObject.Position)
                                    {
                                        results = item;
                                    }
                                    string imageName = ImageNamer.GetFileName((string)item.src, out string extention, out string orignalImageName, (string)item.position);
                                    if (orignalImageName == imageObject.OriginaImageName)
                                    {
                                        toUpload = false;
                                        break;
                                    }
                                    if (downloadCurrentImage)
                                    {
                                        string imagePath = DownloadImageForTester(shopId, $"{shopBakupPath}\\Download", (string)item.src, (int)item.position);
                                    }
                                }
                            }

                            // Console.WriteLine($"{imageObject.ProductId} - {imageObject.Position}:Checking Restored {countRestore} of {totalimagesToRestore} - {optimizedImagePath}");
                            if (toUpload)
                            {
                                countRestore++;
                                if (testMode)
                                {
                                    Console.WriteLine($"Restored {countRestore} of {totalimagesToRestore} - {optimizedImagePath} - {imageObject.OriginaImageName}, {imageObject.Position})");
                                    continue;
                                }
                                try
                                {
                                    dynamic resultsUpload = shopifyApiProvider.UploadProductImage(long.Parse(imageObject.ProductId), optimizedImagePath, altTag, imageObject.Position, imageObject.OriginaImageName);
                                    Console.WriteLine($"Restored {countRestore} of {totalimagesToRestore} - {optimizedImagePath}");
                                    if (resultsUpload.image != null)
                                    {
                                        imageObject.ImageId = resultsUpload.image.id;
                                        imageObject.Status = IMAGE_STATUS.RESTORED.ToString();
                                        Console.WriteLine($"Restored {countRestore} of {totalimagesToRestore} - {imageObject.ImageId}");
                                        if (imageObject.Variants != null)
                                        {
                                            foreach (string variantId in imageObject.Variants)
                                            {
                                                shopifyApiProvider.UpdateVariantImage(variantId, imageObject.ImageId.ToString());
                                                Console.WriteLine($"Restored  UpdateVariantImage- variantId:{variantId}, {imageObject.ImageId.ToString()}");
                                            }
                                        }


                                    }

                                }
                                catch (Exception ex)
                                {
                                    imageObject.Status = $"{IMAGE_STATUS.FAILED.ToString()} - {ex.Message}";
                                }

                            }
                        }
                        catch (Exception ex)
                        {
                            failedToRestoreCount++;
                            imageObject.Status = $"{IMAGE_STATUS.FAILED.ToString()} - {ex.Message}";

                        }
                        countRestore++;
                    }
                    string jsonPath = $"{shopBakupPath}\\ImagesOptResultsRestoreMissing.json";
                    File.WriteAllText(jsonPath, imagesObjects.ToJson());
                    if (failedToRestoreCount > 0)
                    {
                        if (failedToRestoreCount > 9)
                        {
                            Stream stream = new MemoryStream(File.ReadAllBytes(jsonPath));
                            System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(stream, "ImagesOptResultsRestoreMissing.json");
                            List<System.Net.Mail.Attachment> attachments = new List<System.Net.Mail.Attachment>() {
                            attachment
                            };
                            EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BH Image optimize missing image restore results - {shopId}", $"{EmailHelper.AdminLinkHref(shopId)} <br/>Restore tester results Failed :{failedToRestoreCount} restored:{countRestore - failedToRestoreCount} total:{countRestore}<br/>", attachments: attachments);
                        }
                        ConsoleAppHelper.WriteError($"BH Image optimize missing image restore results - {shopId} Failed to restore:{failedToRestoreCount} restored:{countRestore - failedToRestoreCount} total:{countRestore} json:{$"{shopBakupPath}\\ImagesOptResultsRestoreMissing.json"}", null, shopId);
                    }
                    else
                    {
                        ConsoleAppHelper.WriteLog($"BH Image optimize missing image restore results- {shopId} restored:{countRestore} total:{countRestore} json:{$"{shopBakupPath}\\ImagesOptResultsRestoreTester.json"}", shopId);
                    }
                }
            }
            catch (Exception ex)
            {

                ConsoleAppHelper.WriteError($"BH Image optimize test restore results - {shopId} ,Failed to restore", ex, shopId);
            }
        }
        public static void RestoreImagesToShopify(int shopId, bool restoreFromOrigin = false, int? restorePoint = null, bool dontDeleteIfFailed = false)
        {
            try
            {
                int failedToRestoreCount = 0;
                int countRestore = 1;
                var db = DataHelper.GetStoreYaEntities();
                var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId && i.Step == (int)STEP.RESTORE_ALL && i.Status == (int)STATUS.INIT);
                if (bio == null)
                {
                    bio = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId && i.Step == (int)STEP.FINIAL && i.Status == (int)STATUS.DONE);
                }
                if (bio == null)
                {
                    ConsoleAppHelper.WriteLogWithDB($"RestoreImagesToShopify will not run step should be FINIAL!", shopId);
                    UpdateStep(shopId, STEP.RESTORE_ALL, STATUS.FAILED, $"RestoreImagesToShopify will not run step should be UPLOAD_ALL!");
                    return;
                }
                else
                {
                    string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
                    //string productImagesPath = ConfigHelper.GetValue("ProductImages.Path");
                    string shopBakupPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}";
                    string resultMessage = null;
                    UpdateStep(shopId, STEP.RESTORE_ALL, STATUS.STARTED);
                    ShopifyApiProvider shopifyApiProvider = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.BenchmarkHero);
                    List<ImageObject> imagesObjects = LoadImagesObject(shopId);
                    if (restoreFromOrigin)
                    {
                        shopBakupPath = $"{shopBakupPath}\\Origin";
                        if (restorePoint.HasValue && restorePoint.Value > 0)
                        {
                            shopBakupPath = $"{shopBakupPath}.{restorePoint}";
                        }
                        if (Directory.Exists(shopBakupPath))
                        {

                            imagesObjects = File.ReadAllText($"{shopBakupPath}\\ImagesOptResults.json").FromJson<List<ImageObject>>();
                            resultMessage = "Restore from origin";
                            ConsoleAppHelper.WriteLogWithDB($"RestoreImagesToShopify from origin folder.", shopId);
                        }
                        else
                        {
                            ConsoleAppHelper.WriteLogWithDB($"RestoreImagesToShopify {shopBakupPath} not exists.", shopId);
                            return;
                        }
                    }

                    int totalimagesToRestore = imagesObjects.Count(i => i.Status == IMAGE_STATUS.UPLOADED.ToString());

                    foreach (var imageObject in imagesObjects.Where(i => i.Status == IMAGE_STATUS.UPLOADED.ToString()))
                    {
                        try
                        {
                            UpdateProgress(shopId, countRestore, totalimagesToRestore);
                            string altTag = string.IsNullOrEmpty(imageObject.AltTag) ? imageObject.ProductTitle : imageObject.AltTag;
                            string optimizedImagePath = $"{shopBakupPath}\\{imageObject.FileName}";
                            dynamic results = shopifyApiProvider.UploadProductImage(long.Parse(imageObject.ProductId), optimizedImagePath, altTag, imageObject.Position, imageObject.OriginaImageName);
                            Console.WriteLine($"Restored {countRestore} of {totalimagesToRestore} - {optimizedImagePath}");
                            if (results.image != null)
                            {
                                imageObject.ImageId = results.image.id;
                                imageObject.Status = IMAGE_STATUS.RESTORED.ToString();
                                Console.WriteLine($"Restored {countRestore} of {totalimagesToRestore} - {imageObject.ImageId}");
                                if (imageObject.Variants != null)
                                {
                                    foreach (string variantId in imageObject.Variants)
                                    {
                                        shopifyApiProvider.UpdateVariantImage(variantId, imageObject.ImageId.ToString());
                                        Console.WriteLine($"Restored  UpdateVariantImage- variantId:{variantId}, {imageObject.ImageId.ToString()}");
                                    }
                                }
                                try
                                {
                                    dynamic delResults = shopifyApiProvider.DeleteProductImage(long.Parse(imageObject.ProductId), imageObject.OptimizeImageId);
                                }
                                catch (Exception ex)
                                {
                                    if (dontDeleteIfFailed)
                                    {
                                        imageObject.Status = "RESTORED_DELETE_FAILED";
                                    }
                                    else
                                    {
                                        dynamic delResults = shopifyApiProvider.DeleteProductImage(long.Parse(imageObject.ProductId), imageObject.ImageId);
                                        ConsoleAppHelper.WriteError($"Failed to delete image: {imageObject.OptimizeImageId} from product {imageObject.ProductId} reverting upload!", ex, shopId);
                                    }
                                    failedToRestoreCount++;
                                    Console.WriteLine($"Restored Failed to Delete - {optimizedImagePath} dontDeleteIfFailed:{dontDeleteIfFailed}");

                                }

                            }

                        }
                        catch (Exception ex)
                        {
                            imageObject.Status = $"{IMAGE_STATUS.FAILED.ToString()} - {ex.Message}";
                        }
                        countRestore++;
                    }
                    if (restoreFromOrigin)
                    {
                        File.WriteAllText($"{shopBakupPath}\\ImagesOptResults.json", imagesObjects.ToJson());
                        if (dontDeleteIfFailed)
                        {
                            imagesObjects.ToCSV($"{shopBakupPath}\\ImagesOptResults.csv");
                        }
                    }
                    else
                    {
                        SaveImagesObject(shopId, imagesObjects);
                    }
                    if (failedToRestoreCount > 9)
                    {
                        List<System.Net.Mail.Attachment> attachments = new List<System.Net.Mail.Attachment>();
                        if (restoreFromOrigin)
                        {
                            Stream stream = new MemoryStream(File.ReadAllBytes($"{shopBakupPath}\\ImagesOptResults.json"));
                            System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(stream, "ImagesOptResults.json");
                            attachments.Add(attachment);
                        }
                        else
                        {
                            Stream stream = new MemoryStream(Encoding.UTF8.GetBytes(imagesObjects.ToJson() ?? ""));
                            System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(stream, "ImagesOptResults.json");
                            attachments.Add(attachment);
                        }
                        EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BH Image optimize has failed To restore images - {shopId}", $"{EmailHelper.AdminLinkHref(shopId)} <br/>Failed to restore:{failedToRestoreCount} restored:{countRestore - failedToRestoreCount} total:{countRestore}<br/>", attachments: attachments);
                    }
                    STATUS endStatus = STATUS.DONE;
                    if (countRestore == failedToRestoreCount)
                    {
                        endStatus = STATUS.FAILED;
                    }
                    AddHistory(UpdateStep(shopId, STEP.RESTORE_ALL, endStatus, resultMessage));
                    //string url = FeedsManager.SaveFile(shopId, imagesObjects.ToJson(), "json", $"ImagesOptResults", rootFolderPath, productImagesPath, subFolder: null);

                    if (restoreFromOrigin)
                    {
                        SystemEventHelper.Add(shopId, AppTypes.BenchmarkHero.GetHashCode(), SystemEventTypes.BHReport, SystemEventActions.BenchmarkImagesOptRestoreAllOrigin);
                    }
                    else
                    {
                        SystemEventHelper.Add(shopId, AppTypes.BenchmarkHero.GetHashCode(), SystemEventTypes.BHReport, SystemEventActions.BenchmarkImagesOptRestoreAll);
                    }
                }
            }
            catch (Exception ex)
            {
                AddHistory(UpdateStep(shopId, STEP.RESTORE_ALL, STATUS.FAILED, "Failed to run RestoreImagesToShopify", ex));
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BH Image optimize has failed To restore images - {shopId}", $"{EmailHelper.AdminLinkHref(shopId)} <br/>Failed to restore:{ex}");
            }
        }
        public static string CleanUpAllAccounts(string folder, int daysBackToClean, int? limitDays = null, bool excludeOrigin = true, int? shopId = null)
        {
            try
            {
                string errors = "";
                double totalCleand = 0;
                int totalShops = 0;

                string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
                string uploadFolder = ConfigHelper.GetValue("BenchmarkImages.Path", "BenchmarkUploads");
                var db = DataHelper.GetStoreYaEntities();
                DateTime utilUpdatedAt = DateTime.Now.AddDays(-daysBackToClean);
                DateTime? fromUpdatedAt = null;
                if (limitDays.HasValue && limitDays.Value > 0)
                {
                    fromUpdatedAt = utilUpdatedAt.AddDays(-limitDays.Value);
                }
                ConsoleAppHelper.WriteLog($"CleanUpAllAccounts daysBackToClean:{daysBackToClean},limitDays:{limitDays}, utilUpdatedAt:{utilUpdatedAt}, fromUpdatedAt:{fromUpdatedAt},Remove Origin:{!excludeOrigin}");
                List<BenchmarkImagesOptimization> bios = null;
                if (shopId.HasValue)
                {
                    bios = GetBenchmarkImagesOptimizations(shopId: shopId.Value).ToList();
                }
                else
                {
                    bios = GetBIOs(daysBackToClean, limitDays);
                }
                ConsoleAppHelper.WriteLog($"CleanUpAllAccounts Total Shops: {bios.Count}");
                foreach (var bio in bios)
                {
                    Console.WriteLine($"Cleaning shopid: {bio.ShopId}");
                    string shopPath = string.Empty;
                    try
                    {
                        var shopBio = db.BenchmarkImagesOptimizations.SingleOrDefault(b => b.Id == bio.Id);
                        shopBio.StepStartedAt = DateTime.Now;
                        bool excludeShopOrigin = excludeOrigin;
                        if (bio.Step < STEP.RESTORE_ALL.GetHashCode())
                        {
                            excludeShopOrigin = false;
                        }
                        shopPath = CleanShopFolder(rootFolderPath, uploadFolder, bio.ShopId, out double sizePreClean, out double sizePostClean, excludeShopOrigin, utilUpdatedAt);
                        double totalShopCleanup = sizePreClean - sizePostClean;
                        totalCleand += totalShopCleanup;
                        if (totalShopCleanup > 0)
                        {
                            if (bio.Step < STEP.RESTORE_ALL.GetHashCode())
                            {
                                ConsoleAppHelper.WriteLogWithDB($"Cleanup image folder:{folder}, reset step from:{(STEP)bio.Step} status: {(STATUS)bio.Status} to FILTER_IMAGES, DONE.", bio.ShopId);
                                shopBio.Status = (int)STATUS.INIT;
                                shopBio.Step = (int)STEP.NEW;
                                shopBio.StepEndedAt = DateTime.Now;
                                shopBio.Results = $"{bio.Results},{Environment.NewLine}{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")} Cleanup done step was reset saved {totalShopCleanup:N2} MB";
                                db.SaveChanges();
                            }
                            ConsoleAppHelper.WriteLogWithDB($"Cleanup image folder:{folder}, last updated at:{bio.UpdatedAt} MB cleaned {totalShopCleanup:N2} MB", bio.ShopId);
                        }
                    }
                    catch (Exception ex)
                    {
                        ConsoleAppHelper.WriteErrorWithDB($"Failed to Clean {shopPath} ", ex, bio.ShopId);
                        errors = $"{errors}{bio.ShopId}:Failed to Clean {shopPath} -{ex}<br/>";
                    }
                    totalShops++;
                }
                if (totalCleand < 200 || !string.IsNullOrEmpty(errors))
                {
                    return $"Folder: {folder}, from:{fromUpdatedAt} to:{utilUpdatedAt} - total shops cleaned:{bios.Count}, total size:{totalCleand:N2} MB<br/>{errors}";
                }
                return "";
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
        }

        public static string CleanShopFolder(string rootFolderPath, string uploadFolder, int shopId, out double sizePreClean, out double sizePostClean, bool excludeOrigin = true, DateTime? excludeIfAfter = null)
        {
            string shopPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}";
            string shopOriginPath = shopPath + "\\Origin";
            string uploadS3Folder = $@"{rootFolderPath}\{uploadFolder}\{shopId}";
            sizePreClean = GetDirectorySizeMB(shopPath);
            //DeleteFolder(shopId, uploadS3Folder, true);
            FileDeleterHelper.DeleteFiles(shopId, uploadS3Folder);
            List<string> excludedFiles = null;
            string exFiles = string.Empty;
            if (Directory.Exists(shopPath))
            {
                if (excludeOrigin)
                {
                    excludedFiles = FileDeleterHelper.DeleteFiles(shopId, shopPath, new List<string>() { "origin" }, new List<string> { ".json" }, excludeIfAfter);
                    Console.WriteLine($"CleanUpAllAccounts - shopId:{shopId}, excludedFiles:{excludedFiles.Count}");
                    foreach (var item in excludedFiles)
                    {
                        Console.WriteLine(item);
                        exFiles = $"{exFiles}{item}<br/>";
                    }
                }
                else
                {
                    excludedFiles = FileDeleterHelper.DeleteFiles(shopId, shopPath, null, new List<string> { ".json" }, excludeIfAfter);
                    Console.WriteLine($"CleanUpAllAccounts - shopId:{shopId}, excludedFiles:{excludedFiles.Count}");
                    foreach (var item in excludedFiles)
                    {
                        Console.WriteLine(item);
                        exFiles = $"{exFiles}{item}<br/>";
                    }
                }
            }
            sizePostClean = GetDirectorySizeMB(shopPath);
            if (excludedFiles != null && excludedFiles.Count > 0)
            {
                ConsoleAppHelper.WriteLog($"CleanShopFolder - shopId:{shopId}, excludedFiles:{excludedFiles.Count}");
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BH Image optimize cleanup has excludes images - {shopId}", $"{EmailHelper.AdminLinkHref(shopId)} <br/>Total Excluded:{excludedFiles.Count}, Date after :{excludeIfAfter}<br/>ExcludeOrigin:{excludeOrigin}<br/>{exFiles}");
            }
            return shopPath;
        }

        //private static void DeleteFolder(int shopid, string path, bool recursive = false, List<string> excludeFolders = null)
        //{
        //    if (Directory.Exists(path))
        //    {
        //        if (excludeFolders == null)
        //        {   
        //            try
        //            {
        //                Directory.Delete(path, recursive);
        //                Console.WriteLine($"Delete Directory (no exclude):{path}");
        //            }
        //            catch (Exception ex)
        //            {

        //                ConsoleAppHelper.WriteError($"Failed to delete {path} ", ex, shopid);
        //            }
        //        }
        //        else
        //        {
        //            List<string> files = new List<string>(System.IO.Directory.GetFiles(path));
        //            files.ForEach(x => { try { System.IO.File.Delete(x); } catch { } });
        //            foreach (var dir in Directory.GetDirectories(path))
        //            {
        //                if (!excludeFolders.Contains(Path.GetFileName(dir.ToLower())))
        //                {
        //                    files = new List<string>(System.IO.Directory.GetFiles(dir));
        //                    files.ForEach(x => { try { System.IO.File.Delete(x); } catch { } });
        //                    Directory.Delete(dir);
        //                    Console.WriteLine($"Delete Directory:{dir}");
        //                }
        //            }
        //        }
        //    }
        //}
        private static double GetDirectorySizeMB(string folderPath)
        {
            if (Directory.Exists(folderPath))
            {
                DirectoryInfo di = new DirectoryInfo(folderPath);
                double kb = di.EnumerateFiles("*", SearchOption.AllDirectories).Sum(fi => fi.Length);
                return Math.Round(((double)kb) / (1024 * 1024), 1);
            }
            return 0;
        }
        public static void CleanUpImageFolder(int shopId, bool deleteIfOnMetaDataFile = true, bool removeUploadFolder = false)
        {
            UpdateStep(shopId, STEP.CLEAN, STATUS.STARTED);
            string rootFolderPath = ConfigHelper.GetValue("ProductImages.RootPath");
            string shopPath = $@"{rootFolderPath}{ProductImageHelper.GetImagesFolderName(shopId)}";
            List<ImageObject> imagesObjects = LoadImagesObject(shopId);
            foreach (var imageFile in Directory.GetFiles(shopPath))
            {
                string imageFileName = Path.GetFileName(imageFile);
                var imageObject = imagesObjects.SingleOrDefault(i => i.FileName == imageFileName);
                if (imageObject == null)
                {
                    try
                    {
                        File.Delete(imageFile);
                    }
                    catch
                    {
                    }
                }
                else
                {
                    if (deleteIfOnMetaDataFile && imageObject.Status != IMAGE_STATUS.OPTIMIZED.ToString() && imageObject.Status != IMAGE_STATUS.UPLOADED.ToString())
                    {
                        try
                        {
                            File.Delete(imageFile);
                            imageObject.Status = $"{imageObject.Status} ({IMAGE_STATUS.DELETE})";
                        }
                        catch
                        {
                        }
                    }
                }
            }
            try
            {
                string uploadFolder = $"{shopPath}\\Upload";
                if (removeUploadFolder && Directory.Exists(uploadFolder))
                {
                    Directory.Delete(uploadFolder, true);
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteErrorWithDB($"CleanUpImageFolder failed to delete upload folder: {shopPath}\\Upload", ex, shopId);
            }
            SaveImagesObject(shopId, imagesObjects);
            UpdateStep(shopId, STEP.CLEAN, STATUS.DONE);
        }
        private static void GenerateFinalReport(int shopId, string quality, STEP lastStep = STEP.OPTIMIZING_ALL)
        {
            var db = DataHelper.GetStoreYaEntities();
            List<ImageObject> imagesObjects = LoadImagesObject(shopId);
            var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId && i.Step == (int)lastStep && i.Status == (int)STATUS.DONE);
            if (bio != null)
            {
                UpdateStep(shopId, STEP.REPORT, STATUS.STARTED);
                long sumSizeInBytes = imagesObjects.Sum(s => s.SizeInBytes);
                long sumOptimizeSizeInBytes = imagesObjects.Where(i => i.Status == IMAGE_STATUS.UPLOADED.ToString()).Sum(s => s.OptimizeSizeInBytes);
                bio.SetQualityTo = quality;
                decimal size = sumSizeInBytes == 0 ? 0 : sumSizeInBytes / 1024;
                bio.TotalImagesSize = (int)Math.Round(size, 0);
                decimal sizeOpt = sumOptimizeSizeInBytes == 0 ? 0 : sumOptimizeSizeInBytes / 1024;
                bio.TotalImagesSizeAfterOptimize = (int)Math.Round(sizeOpt, 0);
                int countOpt = imagesObjects.Where(i => i.Status == IMAGE_STATUS.UPLOADED.ToString()).Count();
                bio.TotalImagesToOptimize = countOpt;
                bio.PercentageSaved = 0;
                bio.AvgImagesSizeAfterOptimize = 0;
                if (size > 0 && sizeOpt > 0 && countOpt > 0)
                {
                    decimal avg = size / countOpt;
                    decimal avgAfterOpt = sizeOpt / countOpt;
                    decimal prec = ((avg - avgAfterOpt) / avg) * 100;
                    bio.PercentageSaved = (int)Math.Round(prec, 0);
                    bio.AvgImagesSizeAfterOptimize = (int)Math.Round(avgAfterOpt, 0);
                }
                if (countOpt == 0)
                {
                    bio.TimeSaved = 0;
                }
                else
                {
                    decimal timeSaved = TIME_SAVED_PER_IMAGE_MIN * countOpt / 60;//5 min saved per image 
                    bio.TimeSaved = bio.TimeSaved.HasValue ? bio.TimeSaved.Value + (int)Math.Round(timeSaved, 0) : (int)Math.Round(timeSaved, 0);
                }

                db.SaveChanges();

                ConsoleAppHelper.WriteLogWithDB("Generate Final Report", shopId);
                ConsoleAppHelper.WriteLogDebugWithDB("GenerateFinalReport:" + bio.ToJson(), shopId);
            }
            UpdateStep(shopId, STEP.REPORT, STATUS.DONE);

        }
        public static void InitAllImagesOptimization(int shopId)
        {
            var db = DataHelper.GetStoreYaEntities();
            var bio = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId);
            if (bio == null)
            {
                throw new Exception($"Cannot find any BenchmarkImagesOptimizations for shopid:{shopId} ");
            }
            try
            {
                //if (bio.ExecuteCounter.HasValue && bio.ExecuteCounter > 0)
                //{
                //    Console.WriteLine($"InitAllImagesOptimization:Already was executed:{bio.ToJson()}");
                //    return;
                //}
                //var runOnce = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.ShopId == shopId && (i.Step == (int)STEP.FINIAL || i.Step == (int)STEP.RESTORE_ALL)
                //&& i.Status == (int)STATUS.DONE && i.ExecuteCounter.HasValue && i.ExecuteCounter > 0);
                //if (runOnce != null)
                //{
                //    return;
                //}
                var waitingToDownloadList = db.BenchmarkImagesOptimizations.SingleOrDefault(i => i.Step == (int)STEP.DOWNLOADING_ALL && i.Status == (int)STATUS.INIT && i.ShopId == shopId);
                if (waitingToDownloadList == null)
                {
                    ConsoleAppHelper.WriteLogWithDB($"InitAllImagesOptimization failed wrong step/status should be DOWNLOADING_ALL/INIT {bio.ToJson()}", shopId);
                    EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Init All Images Optimization:{(STEP)bio.Step},{(STATUS)bio.Status} Failed: {shopId}", $"{EmailHelper.GetBoLinkHref(shopId, "logs")} ,InitAllImagesOptimization<br/> failed wrong step/status should be DOWNLOADING_ALL/INIT<br/><pre>{bio.ToJson()}</pre>");
                    return;
                }
                else
                {

                    ConsoleAppHelper.WriteLog($"Start InitAllImagesOptimization on total Images: {waitingToDownloadList.TotalImages}", shopId);
                    ImageOptimizerTask(shopId);
                    SystemEventHelper.Add(shopId, AppTypes.BenchmarkHero.GetHashCode(), SystemEventTypes.BHReport, SystemEventActions.BenchmarkImagesOptDownloadAll);
                    ConsoleAppHelper.WriteLog($"Done InitAllImagesOptimization on total Images: {waitingToDownloadList.TotalImages}", shopId);
                }
            }
            catch (Exception ex)
            {
                ConsoleAppHelper.WriteErrorWithDB($"Failed to run InitAllImagesOptimization {bio?.ToJson()}", ex, shopId);
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"Init All Images Optimization:{(STEP)bio?.Step},{(STATUS)bio?.Status} Failed: {shopId}", $"{EmailHelper.GetBoLinkHref(shopId, "logs")} ,InitAllImagesOptimization<br/> failed wrong step/status should be DOWNLOADING_ALL/INIT<br/><pre>{bio?.ToJson()}</pre><br/><pre>{ex}</pre>");
            }
        }

        public static void StorageReport()
        {
            string directory = ConfigHelper.GetValue("ProductImages.RootPath") + "prod\\";
            string FileName = directory + "StorageReport_" + DateTime.Now.ToString("yyyy-MM-dd") + ".csv";
            StringBuilder StorageReport = new StringBuilder();
            StorageReport.AppendLine("ShopID;InsertedAt;UpdatedAt;Execute Count;Status;Step;Total folder size;Origin folder size;Origin Folder Count;Size difference;Path");
            string ShopID;
            long? totalFolderSize = null;
            long? originFolderSize = null;
            string[] folders = Directory.GetDirectories(directory);
            string[] folders1;
            string[] foldersShops;
            string[] shop;
            string[] foldersInShop;
            string[] nameOFfolder;
            long? size_difference = null;
            var db = DataHelper.GetStoreYaEntities();
            DirectoryInfo di;

            foreach (string foldersMultiplesOfAMillion in folders)
            {
                folders1 = Directory.GetDirectories(foldersMultiplesOfAMillion);

                foreach (string foldersMultiplesOfAThousand in folders1)
                {
                    foldersShops = Directory.GetDirectories(foldersMultiplesOfAThousand);
                    foreach (string folderShop in foldersShops)
                    {
                        di = new DirectoryInfo(folderShop);
                        totalFolderSize = di.EnumerateFiles("*", SearchOption.AllDirectories).Sum(fi => fi.Length);
                        shop = folderShop.Split('\\');
                        ShopID = shop[shop.Length - 1];

                        int shopIDint = Int32.Parse(ShopID);
                        BenchmarkImagesOptimization model = db.BenchmarkImagesOptimizations.FirstOrDefault(b => b.ShopId == shopIDint);

                        foldersInShop = Directory.GetDirectories(folderShop);
                        int originFolderCount = 0;
                        foreach (string folder in foldersInShop)
                        {
                            nameOFfolder = folder.Split('\\');
                            if (nameOFfolder[nameOFfolder.Length - 1].StartsWith("Origin"))
                            {
                                di = new DirectoryInfo(folder);
                                originFolderSize = di.EnumerateFiles("*", SearchOption.AllDirectories).Sum(fi => fi.Length);
                                originFolderCount++;
                            }
                        }
                        size_difference = totalFolderSize - originFolderSize;
                        StorageReport.AppendLine(ShopID + ";" + model.InsertedAt + ";" + model.UpdatedAt + ";" + model.ExecuteCounter + ";" + model.Status + ";" + model.Step + ";" + totalFolderSize + ";" + originFolderSize + ";" + originFolderCount + ";" + size_difference + ";" + folderShop);
                        totalFolderSize = null;
                        originFolderSize = null;
                    }
                }
            }
            File.WriteAllText(FileName, StorageReport.ToString());
            Console.WriteLine("file has been created: " + FileName);
        }
        public static void DataDogReport()
        {
            DateTime fromDate = DateTime.Now.AddDays(-1).StartOfDay();
            DateTime toDate = DateTime.Now.AddDays(-1).EndOfDay();
            Console.WriteLine($"BM DataDogReport:{fromDate}:{toDate}");
            try
            {
                var db = DataHelper.GetStoreYaEntities();
                var totalNew = db.BenchmarkImagesOptimizations.Count(item => item.InsertedAt >= fromDate && item.InsertedAt <= toDate);

                var rows = (from item in db.BenchmarkImagesOptimizations
                            where item.UpdatedAt >= fromDate && item.UpdatedAt <= toDate
                            group item by DbFunctions.TruncateTime(item.UpdatedAt) into g
                            orderby g.Key descending
                            select new
                            {
                                Date = g.Key.Value,
                                TotalUpdated = g.Count(),
                                NotFirstRun = g.Count(i => i.ExecuteCounter > 1),
                                Finial = g.Count(i => i.Step == 30 && i.Status == 1),
                                RestoreAll = g.Count(i => i.Step == 23 && i.Status == 1),
                                FirstStep = g.Count(i => i.Step == 15 && i.Status == 1),
                                FirstStepFailed = g.Count(i => i.Step == 15 && i.Status == -1),
                                Failed = g.Count(i => i.Status == -1)
                            }).ToList();

                int totalUpdated = rows.Select(r => r.TotalUpdated).First();
                int notFirstRun = rows.Select(r => r.NotFirstRun).First();
                int firstStep = rows.Select(r => r.FirstStep).First();
                int finial = rows.Select(r => r.Finial).First();
                int restoreAll = rows.Select(r => r.RestoreAll).First();
                int failed = rows.Select(r => r.Failed).First();
                int firstStepFailed = rows.Select(r => r.FirstStepFailed).First();

                Console.WriteLine($"totalNew:{totalNew}");
                Console.WriteLine($"totalUpdated:{totalUpdated}");
                Console.WriteLine($"firstStep:{firstStep}");
                Console.WriteLine($"finial:{finial}");
                Console.WriteLine($"restoreAll:{restoreAll}");
                Console.WriteLine($"failed:{failed}");
                Console.WriteLine($"firstStepFailed:{firstStepFailed}");
                using (DataDogApiClient dataDogApiClient = new DataDogApiClient())
                {
                    dataDogApiClient.ReportCountValue("benchmark.image.optimize.totalnew", totalNew);
                    dataDogApiClient.ReportCountValue("benchmark.image.optimize.totalupdated", totalUpdated);
                    dataDogApiClient.ReportCountValue("benchmark.image.optimize.notfirstrun", notFirstRun);
                    dataDogApiClient.ReportCountValue("benchmark.image.optimize.firststep", firstStep);
                    dataDogApiClient.ReportCountValue("benchmark.image.optimize.finial", finial);
                    dataDogApiClient.ReportCountValue("benchmark.image.optimize.restoreall", restoreAll);
                    dataDogApiClient.ReportCountValue("benchmark.image.optimize.failed", failed);
                    dataDogApiClient.ReportCountValue("benchmark.image.optimize.firststepfailed", firstStepFailed);
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex}");
                EmailHelper.SendEmail(EmailHelper.DEV_FB_EMAIL, $"BM image optimize daily report {fromDate.Date}:{toDate.Date}", $"{ex}");
            }
        }

        public static void AddHistory(BenchmarkImagesOptimization bio)
        {
            if (bio == null) return;
            var db = DataHelper.GetStoreYaEntities();
            BenchmarkImagesOptimizationHistory benchmarkImagesOptimizationHistory = new BenchmarkImagesOptimizationHistory()
            {
                InsertedAt = bio.InsertedAt,
                UpdatedAt = bio.UpdatedAt,
                Status = bio.Status,
                Step = bio.Step,
                StepEndedAt = bio.StepEndedAt,
                StepStartedAt = bio.StepStartedAt,
                AvgImagesSizeAfterOptimize = bio.AvgImagesSizeAfterOptimize,
                ExecuteCounter = bio.ExecuteCounter,
                ExecuteCounterMax = bio.ExecuteCounterMax,
                ImagesMissingAltTag = bio.ImagesMissingAltTag,
                ImagesToCompress = bio.ImagesToCompress,
                ImagesToResize = bio.ImagesToResize,
                LastRunEndedAt = bio.LastRunEndedAt,
                MetaFileUrl = bio.MetaFileUrl,
                PercentageSaved = bio.PercentageSaved,
                PreviewTotalImages = bio.PreviewTotalImages,
                PreviewTotalImagesToOptimize = bio.PreviewTotalImagesToOptimize,
                Progress = bio.Progress,
                Results = bio.Results,
                SetQualityTo = bio.SetQualityTo,
                ShopId = bio.ShopId,
                TimeSaved = bio.TimeSaved,
                TotalImages = bio.TotalImages,
                TotalImagesSize = bio.TotalImagesSize,
                TotalImagesSizeAfterOptimize = bio.TotalImagesSizeAfterOptimize,
                TotalImagesToOptimize = bio.TotalImagesToOptimize,
                TotalProducts = bio.TotalProducts,
                TotalUploadedImages = bio.TotalUploadedImages,
            };
            db.BenchmarkImagesOptimizationHistories.Add(benchmarkImagesOptimizationHistory);
            db.SaveChanges();
        }

        public static void UploadOneImage(int shopId, string path, long productId, string altTag, int position, string variantId = null)
        {
            ShopifyApiProvider shopifyApiProvider = ShopifyConnector.GetShopifyApiClient(shopId, (int)AppTypes.BenchmarkHero);
            //List<ImageObject> imagesObjects = LoadImagesObject(shopId);
            string tokecn = shopifyApiProvider.GetStoreFrontAccessToken("");

            //dynamic results = shopifyApiProvider.UploadProductImage(productId, path, altTag, position);


            //if (variantId != null)
            //{
            //    shopifyApiProvider.UpdateVariantImage(variantId, results.image.id.ToString());
            //}
            //Console.WriteLine($"upload  {path} to {productId} at  {position} variantId:{variantId}");
        }
    }
}
