﻿using Storeya.Core.Entities;
using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace Storeya.Core.Models.AuditTool
{
    public enum LlmStatus
    {
        None = 0,
        InProgress = 2,
        Completed = 1,
        Error = -1,
    }
    public enum BenchmarkDataSources
    {
        HomePage = 0,
        Shopify = 1,
        GA = 2
    }

    public enum BenchmarkStatuses
    {
        Canceled = -100,
        WebsiteIsPasswordProtected = -2,
        Waiting = 0,
        Error = -1,
        Ready = 1,
        ShopifyUnauthorized = 401,
    }

    public static class BenchmarksManager
    {
        public static string FormatRelatedAmount(decimal amount, string currency, string shopCurrencyPattern = null)
        {
            string priceHtmlPattern;

            string currencySymbol = CurrencyHelper.GetSymbolByCode(currency);


            if (!string.IsNullOrEmpty(shopCurrencyPattern))
            {
                priceHtmlPattern = shopCurrencyPattern;
            }
            else if (currencySymbol.Length <= 2)
            {
                priceHtmlPattern = "{1}{0:#,0}"; //"{1}{0:#,0.00}";
            }
            else
            {
                priceHtmlPattern = "{0:#,0} {1}"; //"{0:#,0.00} {1}"; // revenues.ToString("#,#", System.Globalization.CultureInfo.InvariantCulture)
            }

            string output = string.Format(priceHtmlPattern, amount, currencySymbol);

            output = TextManipulationHelper.StripTagsCharArray(output);

            return output;
        }

        public static T PreserveAdminValue<T>(string[] preserveAdminValues, string propertyName, T value, T currentValue)
        {
            if (preserveAdminValues == null)
            {
                return value;
            }
            if (preserveAdminValues.Contains(propertyName.ToLower()))
            {
                return currentValue;
            }
            return value;
        }

        public static void Update(int shopID, AuditTool collected, int? status, bool isPublicStore = true)
        {
            //var db = DataHelper.GetStoreYaEntities();
            //Benchmark b = BenchmarksManager.GetSettings(shopID);

            using (var db = new StoreYaEntities())
            {
                Benchmark b = db.Benchmarks.Where(t => t.ShopID == shopID).SingleOrDefault();

                if (b != null)
                {
                    string[] preserveAdminValues = null;
                    if (!string.IsNullOrEmpty(b.PreserveAdminValues))
                    {
                        preserveAdminValues = b.PreserveAdminValues.ToLower().Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    }
                    if (collected.Data != null)
                    {

                        b.CanonicalUrl = GetTruncatedUrl(collected.Data.CanonicalUrl);
                        b.DataRangeInMonths = collected.Data.DataRangeInMonths; //(collectedData.Results != null ? collectedData.Results.MonthsAmount : 0),
                        b.HasFbPixelOnProductPage = PreserveAdminValue<int?>(preserveAdminValues, "HasFbPixelOnProductPage", (collected.Data.HasFbPixelOnProductPage ? 1 : 0), b.HasFbPixelOnProductPage);
                        b.HasBingPixelOnProductPage = PreserveAdminValue<int?>(preserveAdminValues, "HasBingPixelOnProductPage", (collected.Data.HasBingPixelOnProductPage ? 1 : 0), b.HasBingPixelOnProductPage);
                        b.HasGaOnHomePage = PreserveAdminValue<int?>(preserveAdminValues, "HasGaOnHomePage", (collected.Data.HasGaOnHomePage ? 1 : 0), b.HasGaOnHomePage);
                        b.HasGoogleMerchantCenterOnHomepage = PreserveAdminValue<int?>(preserveAdminValues, "HasGoogleMerchantCenterOnHomepage", (collected.Data.HasGoogleMerchantCenterOnHomepage ? 1 : 0), b.HasGoogleMerchantCenterOnHomepage);
                        b.HasGooglePixelOnThankYouPage = (collected.Data.HasGooglePixelOnThankYouPage ? 1 : 0);
                        b.HasGoogleRemarketingCodeOnProductPage = PreserveAdminValue<int?>(preserveAdminValues, "HasGoogleRemarketingCodeOnProductPage", (collected.Data.HasGoogleRemarketingCodeOnProductPage ? 1 : 0), b.HasGoogleRemarketingCodeOnProductPage);
                        b.HomePageUrl = GetTruncatedUrl(collected.Data.HomePageUrl);

                        if (collected.Data.Orders != null)
                        {
                            b.OrdersAmount = collected.Data.Orders.Count; //(collectedData.Results != null ? collectedData.Results.OrdersAmount : 0); //
                        }

                        b.OnSitePromotions = (collected.Data.OnSitePromotions ? 1 : 0);
                        b.OrderConfirmationPageUrl = GetTruncatedUrl(collected.Data.OrderConfirmationPageUrl);
                        b.ProductPageUrl = GetTruncatedUrl(collected.Data.ProductPageUrl);

                        if (collected.Data.Products != null)
                        {
                            b.ProductsAmount = collected.Data.Products.Count; //(collectedData.Results != null ? collectedData.Results.OrdersAmount : 0); //
                        }

                        b.RedirectsFromHttpToHttps = (collected.Data.RedirectsFromHttpToHttps ? 1 : 0);
                        b.RedirectsToCanonical = (collected.Data.RedirectsToCanonical ? 1 : 0);
                        b.ShopifyShopName = collected.Data.ShopifyShopName;

                        b.H1 = TextManipulationHelper.Truncate(collected.Data.H1, 485);
                        //b.H2 = collected.Data.H2;
                        b.MetaTitle = collected.Data.MetaTitle;
                        b.MetaDescription = collected.Data.MetaDescription;
                        //b.Images = (collected.Data.Images != null ? string.Join(",", collected.Data.Images) : null);

                        b.ProductImagesCountFoundOnHP = (collected.Data.Images != null ? collected.Data.Images.Count : 0);

                        var imagesWithMissingAtr = (collected.Data.ImageTagsWithEmptyOrMissingAltAttribute != null ? string.Join(",", collected.Data.ImageTagsWithEmptyOrMissingAltAttribute) : null);
                        b.ImageTagsWithEmptyOrMissingAltAttribute = TextManipulationHelper.Truncate(imagesWithMissingAtr, 3990);
                        b.DeprecatedHtmlTags = (collected.Data.DeprecatedHtmlTags != null ? string.Join(",", collected.Data.DeprecatedHtmlTags) : null);
                        b.SiteMapUrl = collected.Data.SiteMapUrl;
                        b.RobotsTxtUrl = collected.Data.RobotsTxtUrl;
                        b.FaviconIcoUrl = collected.Data.FaviconIcoUrl;
                        b.LlmUrl = collected.Data.LLMUrl;
                    }

                    if (collected.Results != null)
                    {
                        b.AverageOrderAmount = collected.Results.AverageOrderAmount;
                        b.ContactUsUrl = GetTruncatedUrl(collected.Results.ContactUsUrl);
                        b.CustomerLifetimeValue = collected.Results.CustomerLifetimeValue;
                        b.DescriptionLenght = collected.Results.DescriptionLenght;

                        if (collected.Results.DesktopPerformance != null)
                        {
                            b.DesktopPerformanceScore = collected.Results.DesktopPerformance.Score;
                            if (collected.Results.DesktopPerformance.OptimizedImages != null)
                            {
                                b.DesktopPerformanceImagesAmount = collected.Results.DesktopPerformance.OptimizedImages.Count;
                            }
                            b.DesktopPerformanceImagesReduction = collected.Results.DesktopPerformance.ImagesPossibleReduction;
                        }

                        b.HasChatOrMessenger = (collected.Results.HasChatOrMessenger ? 1 : 0);
                        b.HasSocialLinks = PreserveAdminValue<int?>(preserveAdminValues, "HasSocialLinks", (collected.Results.HasSocialLinks ? 1 : 0), b.HasSocialLinks);

                        // b.HomePageHasReviews = (collected.Results.HomePageHasReviews ? 1 : 0);

                        if (collected.Results.MobilePerformance != null)
                        {
                            b.MobilePerformanceScore = collected.Results.MobilePerformance.Score;
                            if (collected.Results.MobilePerformance.OptimizedImages != null)
                            {
                                b.MobilePerformanceImagesAmount = collected.Results.MobilePerformance.OptimizedImages.Count;
                            }
                            b.MobilePerformanceImagesReduction = collected.Results.MobilePerformance.ImagesPossibleReduction;
                        }

                        b.PercentageOfDiscountedItems = collected.Results.PercentageOfDiscountedItems;
                        b.ProductPageHasReviews = PreserveAdminValue<int?>(preserveAdminValues, "ProductPageHasReviews", (collected.Results.ProductPageHasReviews ? 1 : 0), b.ProductPageHasReviews);
                        b.HomePageHasReviews = PreserveAdminValue<int?>(preserveAdminValues, "HomePageHasReviews", (collected.Results.HomePageHasReviews ? 1 : 0), b.HomePageHasReviews);
                        b.ReturningCustomers = collected.Results.ReturningCustomers;
                        b.Revenues = collected.Results.Revenues;
                        b.ReturnPolicyUrl = PreserveAdminValue<string>(preserveAdminValues, "ReturnPolicyUrl", GetTruncatedUrl(collected.Results.ReturnPolicyUrl), b.ReturnPolicyUrl);
                        b.ShippingInfoUrl = PreserveAdminValue<string>(preserveAdminValues, "ShippingInfoUrl", GetTruncatedUrl(collected.Results.ShippingInfoUrl), b.ShippingInfoUrl);
                        b.TrustBadges = PreserveAdminValue<int?>(preserveAdminValues, "TrustBadges", (collected.Results.TrustBadges ? 1 : 0), b.TrustBadges);

                        b.GaOverallConversionRate = collected.Results.OverallConversionRate;
                        b.GaGoogleOrganicConversionRate = collected.Results.GoogleOrganicConversionRate;
                        b.GaMobileConversionRate = collected.Results.MobileConversionRate;
                        b.GaDesktopConversionRate = collected.Results.DesktopConversionRate;

                    }

                    if (collected.GaData != null)
                    {
                        b.ConversionByChannelData = collected.GaData.ConversionByChannelData;
                        b.OrdersAmount = collected.GaData.Transactions;
                        b.Sessions = collected.GaData.Sessions;
                        b.Revenues = collected.GaData.Revenue;
                        b.AdCost = collected.GaData.AdCost;
                        b.AdRevenue = collected.GaData.AdRevenue;
                        b.LandingPagesCount = collected.GaData.LandingPagesCount;
                    }

                    if (status.HasValue)
                        b.Status = status;

                    b.UpdatedAt = DateTime.Now;
                    b.RunAt = DateTime.Now;

                    db.SaveChanges();

                    //try
                    //{
                    //    db.SaveChanges();
                    //}
                    ////catch (SqlException sql_ex)
                    ////catch (SqlException sql_ex)
                    ////{
                    ////    //Somehow this object is getting to be attached (probably because it's connected to the _currentShop)
                    ////    //db.Detach(b);
                    ////}
                    //catch (Exception ex)
                    //{
                    //    throw new Exception(ex.ToString());
                    //} 
                }
                if (b.BenchmarkDataSource == (int)BenchmarkDataSources.Shopify)
                {
                    if (isPublicStore)
                    {
                        if (ConfigHelper.GetBoolValue("IgnoreLlm") == false)
                        {
                            LlmPage llmPage = GetLLmPageTable(shopID);
                            if (llmPage == null && b.LlmUrl != null)
                            {
                                CreateOrUpdateLLmPageTable(shopID, LlmStatus.Completed, createdByClient: 1);
                            }
                            else if (b.LlmUrl == null)
                            {
                                CreateOrUpdateLLmPageTable(shopID, LlmStatus.None, createdByClient: 0, changeUpdateDate: false);
                            }
                        }
                    }
                }
            }
        }


        public static void Update(int shopID, Benchmark data)
        {
            var db = DataHelper.GetStoreYaEntities();
            Benchmark b = BenchmarksManager.GetSettings(shopID);
            if (b != null)
            {
                b.AverageOrderAmount = data.AverageOrderAmount;
                b.CanonicalUrl = GetTruncatedUrl(data.CanonicalUrl);
                b.ContactUsUrl = GetTruncatedUrl(data.ContactUsUrl);
                b.CustomerLifetimeValue = data.CustomerLifetimeValue;
                b.DataRangeInMonths = data.DataRangeInMonths;
                b.DescriptionLenght = data.DescriptionLenght;
                //b.DesktopPerformanceImagesAmount = data.DesktopPerformanceImagesAmount;
                b.DesktopPerformanceScore = data.DesktopPerformanceScore;
                b.DesktopPerformanceImagesReduction = data.DesktopPerformanceImagesReduction;
                //b.DesktopPerformanceStrategy = data.DesktopPerformanceStrategy;
                b.HasFbPixelOnProductPage = data.HasFbPixelOnProductPage;
                b.HasChatOrMessenger = data.HasChatOrMessenger;
                b.HasGaOnHomePage = data.HasGaOnHomePage;
                b.HasGoogleMerchantCenterOnHomepage = data.HasGoogleMerchantCenterOnHomepage;
                b.HasGooglePixelOnThankYouPage = data.HasGooglePixelOnThankYouPage;
                b.HasGoogleRemarketingCodeOnProductPage = data.HasGoogleRemarketingCodeOnProductPage;
                b.HasSocialLinks = data.HasSocialLinks;
                b.HomePageHasReviews = data.HomePageHasReviews;
                b.HomePageUrl = GetTruncatedUrl(data.HomePageUrl);
                //b.MobilePerformanceImagesAmount = data.MobilePerformanceImagesAmount;
                b.MobilePerformanceScore = data.MobilePerformanceScore;
                b.MobilePerformanceImagesReduction = data.MobilePerformanceImagesReduction;
                b.OrderConfirmationPageUrl = GetTruncatedUrl(data.OrderConfirmationPageUrl);
                b.OrdersAmount = data.OrdersAmount;
                b.PercentageOfDiscountedItems = data.PercentageOfDiscountedItems;
                b.ProductPageHasReviews = data.ProductPageHasReviews;
                b.ProductPageUrl = GetTruncatedUrl(data.ProductPageUrl);
                b.ProductsAmount = data.ProductsAmount;
                b.RedirectsFromHttpToHttps = data.RedirectsFromHttpToHttps;
                b.RedirectsToCanonical = data.RedirectsToCanonical;
                b.ReturningCustomers = data.ReturningCustomers;
                b.ReturnPolicyUrl = GetTruncatedUrl(data.ReturnPolicyUrl);
                b.Revenues = data.Revenues;
                b.ShippingInfoUrl = GetTruncatedUrl(data.ShippingInfoUrl);
                b.ShopifyShopName = data.ShopifyShopName;
                b.TrustBadges = data.TrustBadges;
                b.OnSitePromotions = data.OnSitePromotions;
                b.UpdatedAt = DateTime.Now;
                b.RunAt = DateTime.Now;
                b.Status = (int)BenchmarkStatuses.Ready;
                db.SaveChanges();
            }
        }


        public static string GetTruncatedUrl(string url, int lenght = 250)
        {
            if (!string.IsNullOrEmpty(url))
            {
                if (url.Length <= lenght)
                {
                    return url;
                }
                else
                {
                    return url.Substring(0, lenght);
                }
            }
            return null;
        }

        public static Benchmark GetSettings(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Benchmark benchmarks_data = db.Benchmarks.Where(t => t.ShopID == shopID).SingleOrDefault();
            return benchmarks_data;
        }
        public static List<Benchmark> GetBenchmarksToRun()
        {
            //StoreYaEntities db = DataHelper.GetStoreYaEntities();
            using (var db = new StoreYaEntities())
            {
                var shops = db.Benchmarks.Where(b => !b.RunAt.HasValue && b.ShopID.HasValue && b.ShopID > 0).ToList();
                return shops;
            }
        }

        public static int? AddBenchmark(int shopID)
        {
            int? benchmarkID = null;

            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            Shop shop = db.Shops.Where(s => s.ID == shopID).SingleOrDefault();
            if (shop != null)
            {
                var bm = BenchmarksManager.GetSettings(shopID);
                if (bm == null)
                {
                    int data_source = (int)BenchmarkDataSources.GA;

                    ShopifyConnectedShop connectedShop = db.ShopifyConnectedShops.Where(cs => cs.ShopID == shopID && cs.StoreyaAppTypeID == (int)Shopify_StoreyaApp.BenchmarkHero).SingleOrDefault();
                    if (connectedShop != null)
                        data_source = (int)BenchmarkDataSources.Shopify;

                    Benchmark benchmark = new Benchmark();

                    benchmark.Revenues = 0;
                    benchmark.OrdersAmount = 0;
                    benchmark.ReturningCustomers = 0;
                    benchmark.HomePageUrl = UrlPathHelper.BuildValidUri(shop.ShopUrl);
                    benchmark.ShopID = shopID;
                    benchmark.InsertedAt = DateTime.Now;
                    benchmark.BenchmarkDataSource = data_source;

                    db.Benchmarks.Add(benchmark);
                    db.SaveChanges();

                    benchmarkID = benchmark.ID;
                }
            }

            return benchmarkID;
        }

        public static void ResetResults(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            var bm = db.Benchmarks.Where(t => t.ShopID == shopID).SingleOrDefault();
            if (bm != null)
            {
                bm.RunAt = null;
                bm.Status = (int)BenchmarkStatuses.Waiting;
                db.SaveChanges();
            }

        }

        public static BenchmarkStatuses HasNewResults(Benchmark benchmark)
        {
            if (benchmark.RunAt != null)
            {
                return BenchmarkStatuses.Ready;
            }
            else
            {
                return BenchmarkStatuses.Waiting;
            }
        }

        public static void SetStatus(int shopID, BenchmarkStatuses newStatus)
        {
            using (var db = new StoreYaEntities())
            {
                var b = db.Benchmarks.Where(t => t.ShopID == shopID).SingleOrDefault();
                if (b != null)
                {
                    b.Status = (int)newStatus;
                    b.UpdatedAt = DateTime.Now;
                    b.RunAt = DateTime.Now;
                }
                db.SaveChanges();
            }
        }
        public static void SetHomePageUrl(int shopID, string homePageUrl)
        {
            var db = DataHelper.GetStoreYaEntities();
            var b = db.Benchmarks.Where(t => t.ShopID == shopID).SingleOrDefault();
            if (b != null && string.IsNullOrEmpty(b.HomePageUrl))
            {
                b.UpdatedAt = DateTime.Now;
                b.HomePageUrl = homePageUrl;
                db.SaveChanges();
            }
        }


        public static Benchmark SetDummySettings(int test)
        {
            Benchmark b = new Benchmark();
            if (test == 1)
            {
                b.AverageOrderAmount = 10;
                b.CanonicalUrl = "CanonicalUrl";
                b.ContactUsUrl = "ContactUsUrl";
                b.CustomerLifetimeValue = 111;
                b.DataRangeInMonths = 3;
                b.DescriptionLenght = 245; // 500 characters
                b.DesktopPerformanceImagesAmount = 12;
                b.DesktopPerformanceImagesReduction = 13;
                b.DesktopPerformanceScore = 75;
                b.HasChatOrMessenger = null;
                b.HasFbPixelOnProductPage = 1;
                b.HasGaOnHomePage = 1;
                b.HasGoogleMerchantCenterOnHomepage = 1;
                b.HasGooglePixelOnThankYouPage = 1;
                b.HasGoogleRemarketingCodeOnProductPage = null;
                b.HasSocialLinks = 1;
                b.HomePageHasReviews = 1;
                b.HomePageUrl = "http://www.yourdomain.com";
                b.InsertedAt = DateTime.Now;
                b.MobilePerformanceImagesAmount = 18;
                b.MobilePerformanceImagesReduction = 16;
                b.MobilePerformanceScore = 80;
                b.OnSitePromotions = 1;
                b.OrderConfirmationPageUrl = "OrderConfirmationPageUrl";
                b.OrdersAmount = 116075;
                b.PercentageOfDiscountedItems = 50;
                b.ProductPageHasReviews = 1;
                b.ProductPageUrl = "ProductPageUrl";
                b.ProductsAmount = 14993;
                b.RedirectsFromHttpToHttps = 1;
                b.RedirectsToCanonical = 1;
                b.ReturningCustomers = 6607;
                b.ReturnPolicyUrl = "ReturnPolicyUrl";
                b.Revenues = 770;
                b.RunAt = DateTime.Now;
                b.ShippingInfoUrl = "ShippingInfoUrl";
                b.ShopID = 23;
                b.ShopifyShopName = "ShopifyShopName";
                b.Status = (int)BenchmarkStatuses.Ready;
                b.TrustBadges = 1;
                b.SiteMapUrl = "SiteMapUrl";
                b.UpdatedAt = DateTime.Now;
                b.LlmUrl = "https://example.com/llm-result.json";
            }
            else if (test == 2)
            {
                //add nothing
            }
            else if (test == 3)
            {
                b.ContactUsUrl = "ContactUsUrl";
                b.DataRangeInMonths = 3;
                b.DescriptionLenght = 500;
                b.DesktopPerformanceImagesAmount = 100;
                b.DesktopPerformanceImagesReduction = 105;
                b.HasChatOrMessenger = 1;
                b.HasFbPixelOnProductPage = 1;
                b.HasGaOnHomePage = 1;
                b.HasGoogleMerchantCenterOnHomepage = 1;
                b.HasGooglePixelOnThankYouPage = 1;
                b.HasGoogleRemarketingCodeOnProductPage = 1;
                b.HasSocialLinks = 1;
                b.HomePageHasReviews = 1;
                b.HomePageUrl = "http://www.yourdomain.com";
                b.InsertedAt = DateTime.Now;
                b.MobilePerformanceImagesAmount = 110;
                b.MobilePerformanceImagesReduction = 160;
                b.MobilePerformanceScore = 100;
                b.OnSitePromotions = 1;
                b.OrderConfirmationPageUrl = "OrderConfirmationPageUrl";
                b.OrdersAmount = 116075;
                b.PercentageOfDiscountedItems = 100;
                b.ProductPageHasReviews = 1;
                b.ProductPageUrl = "ProductPageUrl";
                b.ProductsAmount = 14993;
                b.RedirectsFromHttpToHttps = 1;
                b.RedirectsToCanonical = 1;
                b.ReturningCustomers = 6607;
                b.ReturnPolicyUrl = "ReturnPolicyUrl";
                b.Revenues = 770;
                b.RunAt = DateTime.Now;
                b.ShippingInfoUrl = "ShippingInfoUrl";
                b.ShopID = 23;
                b.ShopifyShopName = "ShopifyShopName";
                b.Status = (int)BenchmarkStatuses.Ready;
                b.TrustBadges = 1;
                b.UpdatedAt = DateTime.Now;
                b.MetaTitle = "MetaTitle MetaTitle MetaTitle MetaTitle MetaTitle MetaTitle MetaTitle MetaTitle";
                b.MetaDescription = "MetaDescription MetaDescription MetaDescription MetaDescription MetaDescription MetaDescription";
                b.H1 = "H1";
                b.H2 = "H2";
                b.FaviconIcoUrl = "FaviconIcoUrl";
                b.RobotsTxtUrl = "RobotsTxtUrl";
                b.SiteMapUrl = "SiteMapUrl";
                b.LlmUrl = "https://example.com/llm-result.json";
            }
            return b;
        }

        public static BenchmarkImagesOptimization SetDummySettingsImageOpt(int test)
        {
            BenchmarkImagesOptimization io = null;
            if (test == 1)
            {
                io = new BenchmarkImagesOptimization()
                {
                    TotalImages = 38465,
                    TotalImagesToOptimize = 1732,
                    ImagesToCompress = 14993,
                    TotalProducts = 14993,
                    PreviewTotalImages = 51,
                    PreviewTotalImagesToOptimize = 24
                };
            }
            else if (test == 2)
            {
                io = new BenchmarkImagesOptimization()
                {
                    TotalImages = 111,
                    TotalImagesToOptimize = 0,
                    ImagesToCompress = 111,
                    TotalProducts = 222,
                    PreviewTotalImages = 50,
                    PreviewTotalImagesToOptimize = 50
                };
            }
            else if (test == 3)
            {
                io = new BenchmarkImagesOptimization()
                {
                    TotalImages = 38465,
                    TotalImagesToOptimize = 0,
                    ImagesToCompress = 0,
                    TotalProducts = 14993,
                    PreviewTotalImages = 51,
                    PreviewTotalImagesToOptimize = 0
                };
            }



            return io;
        }

        public static LlmPage GetLLmPageTable(int shopId)
        {
            if (ConfigHelper.GetBoolValue("IgnoreLlm"))
            {
                return null;
            }
            var db = new StoreYaEntities();
            LlmPage llmPage = db.LlmPages.Where(l => l.ShopID == shopId).SingleOrDefault();
            return llmPage;
        }
        public static LlmPage CreateOrUpdateLLmPageTable(int shopId, LlmStatus llmStatus, int createdByClient = 0, bool changeUpdateDate = true, string error = null)
        {
            var db = new StoreYaEntities();
            LlmPage llmPage = db.LlmPages.Where(l => l.ShopID == shopId).SingleOrDefault();
            if (llmPage == null)
            {
                llmPage = new LlmPage();
                llmPage.ShopID = shopId;
                llmPage.Status = (int)llmStatus;
                llmPage.InstertedAt = DateTime.Now;
                llmPage.CreatedByClient = createdByClient;
                llmPage.Error = error;
                db.LlmPages.Add(llmPage);
            }
            else
            {
                if (changeUpdateDate)
                {
                    llmPage.UpdatedAt = DateTime.Now;
                }
                llmPage.Status = (int)llmStatus;
                llmPage.Error = error;
            }
            db.SaveChanges();
            return llmPage;
        }

        public static void UpdateLlmUrlInBH(int shopID, string fileUrl)
        {
            var db = DataHelper.GetStoreYaEntities();
            var b = db.Benchmarks.Where(t => t.ShopID == shopID).SingleOrDefault();
            b.UpdatedAt = DateTime.Now;
            b.LlmUrl = fileUrl;
            db.SaveChanges();

        }
    }
}
