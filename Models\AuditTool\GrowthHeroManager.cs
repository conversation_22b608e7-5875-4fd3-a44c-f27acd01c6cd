﻿using System;
using System.Linq;

using Storeya.Core.Helpers;
using Storeya.Core.Models.FbAds;
using Storeya.Core.Models.GrowthHeroModels;

namespace Storeya.Core.Models.AuditTool
{
    public enum GrowthHeroStatuses
    {
        Canceled = -100,
        //WebsiteIsPasswordProtected = -2,
        Waiting = 0,
        Error = -1,
        Ready = 1,
        WaitingForAccountToBeSelected  = 5
    }

    public class GrowthHeroManager
    {

        public static GrowthHero GetSettings(int shopID)
        {
            StoreYaEntities db = DataHelper.GetStoreYaEntities();
            GrowthHero herosData = db.GrowthHeros.SingleOrDefault(t => t.ShopID == shopID);
            return herosData;
        }

        public static string HasNewResults(GrowthHero growth)
        {
            if (growth != null && growth.Status != null && growth.Status!= (int)GrowthHeroStatuses.Waiting&& growth.Status!= (int)GrowthHeroStatuses.WaitingForAccountToBeSelected)
            {
                return "1";
            }
            else
            {
                return "0";
            }
        }

        public static GrowthHeroMissingOpportunities CalculateMissingApportunities(
            FbAdsSDK.CustomAudiencesResponse.Response elements)
        {
            if (elements == null)
                return new GrowthHeroMissingOpportunities();
            
            GrowthHeroMissingOpportunities result = new GrowthHeroMissingOpportunities();
            bool lookALikePresent = false;
            if(elements.data !=null)
            foreach (var element in elements.data)
            {
                if (element.lookalike_audience_ids != null && element.lookalike_audience_ids.Length > 0)
                    lookALikePresent = true;
                else
                {
                    lookALikePresent = false;
                }

                if (element.Rules != null && element.Rules.inclusions != null) // retargeting
                {
                    foreach (FbAdsSDK.CustomAudiencesResponse.Rule rule in element.Rules.inclusions.rules)
                    {
                        //Console.WriteLine("retention_seconds - " + rule.retention_seconds);
                        var range = result.RetargetingRanges.FirstOrDefault(p => p.IsBetween(rule.retention_seconds));
                        if(rule.filter.filters !=null)
                        foreach (FbAdsSDK.CustomAudiencesResponse.Filter1 filter in rule.filter.filters)
                        {
                            if (filter.field == "event" && filter.value == "AddToCart")
                            {
                                if (range != null)
                                {
                                    switch (range.Name)
                                    {
                                        case "AddToCart30":
                                            result.RetargetingMissingOpportunities.AddToCard30Days = true;
                                            result.LookALikeMissingOpportunities.AddToCard = lookALikePresent;
                                            Console.WriteLine("Retargeting on AddToCart30 true");
                                            break;
                                        case "any":
                                            result.RetargetingMissingOpportunities.Visitors30Secs = true;
                                            result.LookALikeMissingOpportunities.PastVisitor = lookALikePresent;
                                            Console.WriteLine("Retargeting on any true");
                                            break;
                                    }
                                }
                            }

                            if (filter.field == "event" && filter.value == "Purchase")
                            {
                                if (range != null)
                                {
                                    switch (range.Name)
                                    {
                                        case "Purchased90":
                                            result.RetargetingMissingOpportunities.Purchased90Days = true;
                                            result.LookALikeMissingOpportunities.Purchased = lookALikePresent;
                                            Console.WriteLine("Purchase on 90 true");
                                            break;
                                        case "AddToCart30":
                                            result.RetargetingMissingOpportunities.AddToCard30Days = true;
                                            result.LookALikeMissingOpportunities.AddToCard = true;
                                            Console.WriteLine("Purchase AddToCart30 true");
                                            break;
                                    }
                                }
                            }
                        }
                    }
                }

                if (element.DPARules != null && element.DPARules.Inclusions != null) // DPA
                {

                    foreach (var dpaRootObject in element.DPARules.Inclusions)
                    {
                        int secs = dpaRootObject.retention_seconds;
                        var range = result.DpaRanges.FirstOrDefault(p => p.IsBetween(secs));
                        if (range != null)
                        {

                            foreach (var ruleItem in element.DPARules.Inclusions)
                            {
                               
                                    if (<EMAIL> == "ViewContent")
                                        result.DpaMissingOpportunities.ViewContent = true;
                                    switch (<EMAIL>)
                                    {
                                        case "AddToCart":
                                            switch (range.Name)
                                            {
                                                case "DPALast30":
                                                    result.DpaMissingOpportunities.AddCartLast15Days = true;
                                                    Console.WriteLine("AddToCart DPALast30 true");
                                                    break;
                                                case "DPALast7":
                                                    result.DpaMissingOpportunities.AddCartLast4Days = true;
                                                    Console.WriteLine("AddToCart DPALast7 true");

                                                    break;
                                                case "DPALast15":
                                                    result.DpaMissingOpportunities.AddCartLast15Days = true;
                                                    Console.WriteLine("AddToCart DPALast15 true");
                                                    break;
                                            }

                                            break;
                                    }
                            }
                        }
                    }
                  
                }
            }

            return result;
        }
    }
}
