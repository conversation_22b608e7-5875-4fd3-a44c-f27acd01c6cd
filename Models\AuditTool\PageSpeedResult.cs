﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Storeya.Core.Models.AuditTool
{
    public class PageSpeedResult
    {
        public string Strategy { get; set; } //"desktop" or "mobile"
        public int Score { get; set; }
        public List<PageSpeedImage> OptimizedImages { get; set; }
        public int ImagesPossibleReduction { get; set; }
        public string OptimizationImagesSuggestion { get; set; }

        public int AmountOfRequestsOnHP { get; set; }
        public int SizeOfAllRequestsInBytes { get; set; }
        public string SizeOfAllRequests { get; set; }
        public int AmountOfImagesRequestsOnHP { get; set; }
        public int SizeOfAllImagesRequestsInBytes { get; set; }

        public List<PageSpeedImage> HpImages { get; set; }  // list of images above 70K so merchant can try to optimize

        public List<PageSpeedImage> OffScreenImages { get; set; }
        public string OffscreenImagesSuggestion { get; set; }

        public List<PageSpeedImage> UsesResponsiveImages { get; set; }
        public string UsesResponsiveImagesSuggestion { get; set; }

        public List<PageSpeedImage> UsesWebpImages{ get; set; }
        public string UsesWebpImagesSuggestion { get; set; }
}

    public class PageSpeedImage
    {
        public string Url { get; set; }
        public double Percentage { get; set; }
        public string SizeInBytesString { get; set; }
        public int SizeInBytes { get; set; }
        public string OptimizationSuggestions { get; set; }
    }
}
