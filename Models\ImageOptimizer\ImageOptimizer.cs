﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static Storeya.Core.Helpers.ImageUtilities;

namespace Storeya.Core.Models.ImageOptimizer
{
    public class ImageOptimizer
    {
        public static string DownloadAndOptimizeImage(string sourceUrl, int shopID, int quality, int? resizeInPercentage = null, ImageUtilities.IMAGE_TYPE imageType = ImageUtilities.IMAGE_TYPE.JPEG, bool overwriteBackup = true, int position = 0)
        {
            // handling https requests
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            //bypass (ignore) SSL certificate
            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
            WebClient client = SetWebClient();
            try
            {
                if (imageType == ImageUtilities.IMAGE_TYPE.WEBP)
                {
                    client = AddHeadersForWebP(client);
                }
                string fileName = ImageNamer.GetFileName(sourceUrl, out string extention, out string orignalImageName, position.ToString()); ;
                string tempFileName = ProductImageHelper.GetTempShopFileFolder(shopID) + "\\" + fileName;
                if (!Directory.Exists(Path.GetDirectoryName(tempFileName)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(tempFileName));
                }

                try
                {
                    client.DownloadFile(sourceUrl, tempFileName);
                }
                catch (Exception ex)
                {
                    if (sourceUrl.ToLower().StartsWith("https://"))
                    {
                        sourceUrl = sourceUrl.Replace("https://", "http://");
                        client.DownloadFile(sourceUrl, tempFileName);
                    }
                    else
                    {
                        throw ex;
                    }
                }

                string localPath = ImageUtilities.Optimize(tempFileName, quality, resizeInPercentage, imageType, overwriteBackup);
                //RescaleImage(tempFileName, localPath, null, requiredHeight, requiredWidth);
                return localPath;
                //MoveToImagesStorage(localPath);

                //System.IO.File.Delete(localPath);

                //if (!string.IsNullOrEmpty(ConfigHelper.GetValue("StaticImagesLocation")))
                //{
                //MoveToImagesStorage(localPath);
                //}
            }
            catch (Exception ex)
            {
                //Log4NetLogger.Error(Log4NetLogger.BuildMessage("Failed to download image " + product.ImageUrl, product.Shop.ID));
                throw new Exception("Failed to create image from " + sourceUrl, ex);
            }
            finally
            {
                client.Dispose();
            }
        }

        public static WebClient AddHeadersForWebP(WebClient client)
        {
            client.Headers.Add(HttpRequestHeader.UserAgent,
"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 " +
"(KHTML, like Gecko) Chrome/127.0.0.1 Safari/537.36");
            client.Headers.Add(HttpRequestHeader.Accept,
                "image/avif,image/webp,image/*,*/*;q=0.8");
            return client;
        }

        public static void DownloadImage(string sourceUrl, string localPath, int shopID)
        {
            // handling https requests
            //ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            WebClient client = SetWebClient();
            try
            {
                if (sourceUrl.ToLower().Contains(".webp"))
                {
                    client = AddHeadersForWebP(client);
                }

                if (!Directory.Exists(Path.GetDirectoryName(localPath)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(localPath));
                }

                client.DownloadFile(sourceUrl, localPath);
            }
            catch (Exception ex)
            {
                //Log4NetLogger.Error(Log4NetLogger.BuildMessage("Failed to download image " + product.ImageUrl, product.Shop.ID));
                throw new Exception("Failed to create image from " + sourceUrl, ex);
            }
            finally
            {
                client.Dispose();
            }
        }
        private static WebClient SetWebClient(string referer = null)
        {
            WebClient client = new WebClient();
            client.Headers.Add("user-agent", "Rigor API Tester");
            client.Headers.Add("Accept", "*/*");

            if (!string.IsNullOrEmpty(referer))
            {
                client.Headers.Add("Referer", referer);
            }

            return client;
        }

        public static List<ImageObject> FilterBiggestImages(List<ImageObject> images, long minSizeToOptimze, decimal precentageToSelect = 0.3M, int? maxImagesToOptimize = null)
        {
            decimal totalImages = images.Count;

            long[] sizes = new long[] { 2000000, 1000000, 500000, 400000, 300000, 200000 };
            foreach (var size in sizes)
            {
                decimal overWeightedCount = images.Where(i => i.SizeInBytes > size).Count();
                Console.WriteLine($"overWeightedCount{overWeightedCount} total {totalImages} Size:{size}");
                if (overWeightedCount / totalImages > precentageToSelect)
                {
                    minSizeToOptimze = size;
                }
            }
            List<ImageObject> filtered = images.Where(i => i.SizeInBytes > minSizeToOptimze).ToList();
            if (maxImagesToOptimize.HasValue)
            {
                filtered = filtered.Take(maxImagesToOptimize.Value).ToList();
            }
            Console.WriteLine($"Filterd by Size:{minSizeToOptimze} total: {filtered.Count()} ");
            return filtered;

        }

        public static List<ImageObject> FilterFolderBiggestImages(string folder, long minSizeToOptimze = 100000)
        {
            List<ImageObject> images = new List<ImageObject>();
            var files = Directory.GetFiles(folder);
            foreach (var file in files)
            {
                ImageObject img = new ImageObject();
                FileInfo fi = new FileInfo(file);

                img.SizeInBytes = fi.Length;
                img.FileName = fi.FullName;
                img.Status = "CREATED";


                //var imageHeight = img.Height;
                //var imageWidth = img.Width;

                //if (fi.Length > 0)
                //{
                //    using (Image image = new Bitmap(file))
                //    {
                //        img.Width = image.Height;
                //        img.Height = image.Width;
                //    }
                //}

                images.Add(img);
            }

            List<ImageObject> filtered = FilterBiggestImages(images, minSizeToOptimze);
            //var totalImages = images.Count;
            //long minSizeToOptimze = -1;
            //long[] sizes = new long[] { 2000000, 1000000, 500000, 300000, 200000, 100000 };
            //foreach (var size in sizes)
            //{
            //    var overWeightedCount = images.Where(i => i.SizeInBytes > size).Count();
            //    Console.WriteLine("overWeightedCount" + overWeightedCount + " total " + totalImages);
            //    if ((decimal)overWeightedCount / totalImages > 0.1M)
            //    {
            //        minSizeToOptimze = size;
            //        break;
            //    }
            //}
            //List<ImageObject> filtered = images.Where(i => i.SizeInBytes > minSizeToOptimze).ToList();
            return filtered;
        }

        public static List<ImageObject> OptimizeImages(List<ImageObject> images, string targetPath, int quality)
        {
            List<ImageObject> optimized = new List<ImageObject>();

            foreach (var img in images)
            {
                //string output = null;
                try
                {
                    string localPath = OptimizeImage(img.FileName, targetPath + @"\quality" + quality, quality);
                    if (localPath != null)
                    {

                        Console.WriteLine("Image generated - " + localPath);
                        FileInfo fi = new FileInfo(localPath);
                        ImageObject i = new ImageObject();
                        i.FileName = localPath;
                        i.SizeInBytes = fi.Length;

                        if (img.SizeInBytes > fi.Length * 1.2)
                        {
                            //add images with significant improvments
                            optimized.Add(i);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(img.FileName + " - Error: " + ex.Message);
                    //output = $"{img.FileName},{img.SizeInBytes},{ex.Message}";
                }
            }

            return optimized;
        }

        public static string OptimizeImage(string sourceFile, string targetFolder, int quality, string namePrefix = "")
        {
            int? resizeInPercentage = null;
            string fileName = Path.GetFileName(sourceFile);
            string targetFilePath = Path.Combine(targetFolder, $"{namePrefix}{fileName}");

            string imageType = GetImageType(sourceFile);
            switch (imageType)
            {
                case ".jpeg":
                    SaveJpeg(sourceFile, targetFilePath, quality, resizeInPercentage);
                    break;
                case ".jpg":
                    SaveJpeg(sourceFile, targetFilePath, quality, resizeInPercentage);
                    break;
                case ".png":
                    SavePNG(sourceFile, targetFilePath, quality, resizeInPercentage);
                    break;
                default:
                    targetFilePath = null;
                    break;
            }
            if (!string.IsNullOrEmpty(namePrefix))
            {
                try
                {
                    File.Delete(sourceFile);
                }
                catch { }
            }
            return targetFilePath;
        }

        private static string GetImageType(string sourceFile)
        {
            return Path.GetExtension(sourceFile);
        }
    }
}
