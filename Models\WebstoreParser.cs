﻿using Storeya.Core.Helpers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Text;
using System.Linq;
using Newtonsoft.Json.Linq;
using Storeya.Core.Models.BI;
//using System.Web.UI.WebControls;
//using Storeya.Core.Models.DataProviders.ShopifyEntities;

namespace Storeya.Core.Models
{
    public class ImageFile
    {
        public string ImageUrl { get; set; }
        public string FileName { get; set; }
        public string FileDimensions { get; set; }

        public int? Hight
        {
            get
            {
                if (!string.IsNullOrEmpty(FileDimensions))
                {
                    if (this.FileDimensions.Contains("x"))
                    {
                        return Convert.ToInt32(FileDimensions.Split('x')[1]);
                    }
                }
                return null;
            }
        }
        public int? Width
        {
            get
            {
                if (!string.IsNullOrEmpty(FileDimensions))
                {
                    if (this.FileDimensions.Contains("x"))
                    {
                        return Convert.ToInt32(FileDimensions.Split('x')[0]);
                    }
                }
                return null;
            }
        }
    }

    public class ProductPageData
    {
        public string Link { get; set; }
        public string Title { get; set; }
        public string MainImage { get; set; }
        public string Price { get; set; }
        public string Sku { get; set; }
        public string ID { get; set; }
        public string Brand { get; set; }
        public string Gtin { get; set; }
    }

    public class WebstoreParser
    {
        public List<string> Images { get; set; }
        public string Url { get; set; }

        public static List<string> GetImagesFromUrl(string url)
        {
            string htmlContent = ShopifyCrawler.GetContent(url);
            return GetImagesFromHtml(htmlContent);
        }

        public static List<string> GetImagesFromHtml(string htmlContent)
        {
            var imageUrlList = new List<string>();
            try
            {
                var regex = new Regex("<img[^>]+src=\"(.*?)\"", RegexOptions.IgnoreCase);

                foreach (Match match in regex.Matches(htmlContent))
                {
                    string srcValue = match.Groups[1].Value;
                    srcValue = srcValue.Replace("{width}", "200");
                    imageUrlList.Add(srcValue);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching data: {ex.Message}");
            }
            return imageUrlList;
        }

        public static List<ImageFile> GetImageSizes(string folderPath)
        {
            var imageFiles = new List<ImageFile>();

            // Check if the directory exists
            if (!Directory.Exists(folderPath))
            {
                throw new DirectoryNotFoundException($"The directory {folderPath} was not found.");
            }

            // Get all files in the directory
            string[] files = Directory.GetFiles(folderPath);
            foreach (string file in files)
            {
                try
                {
                    // Check if the file is an image
                    ImageFile i = GetImageSize(file);
                    if (i != null)
                    {
                        imageFiles.Add(i);
                    }
                }
                catch (Exception ex)
                {
                    // Handle exceptions if necessary
                    Console.WriteLine($"An error occurred processing {file}: {ex.Message}");
                }
            }

            return imageFiles;
        }

        private static ImageFile GetImageSize(string file)
        {
            ImageFile imageFile = null;
            if (IsImage(file, includeWebImage: true))
            {
                int width = 0, height = 0;
                string fileName = null;
                if (file.ToLower().Contains(".webp"))
                {
                    ImageUtilities.GetImageParametrsBySkiImage(file, out width, out height, out float hResolution, out float vResolution);
                    fileName = Path.GetFileName(file);
                }
                else
                {
                    using (Image img = Image.FromFile(file))//ok
                    {
                        fileName = Path.GetFileName(file);
                        width = img.Width;
                        height = img.Height;
                    }
                }
                imageFile = (new ImageFile
                {
                    FileName = fileName,
                    FileDimensions = $"{width}x{height}"
                });
            }
            return imageFile;
        }
        public static string GetImageDimentions(string file)
        {
            if (file != null)
            {

                if (file.ToLower().Contains(".webp"))
                {
                    ImageUtilities.GetImageParametrsBySkiImage(file, out int width, out int height, out float hResolution, out float vResolution);
                    return $"{width}x{height}";
                }
                else
                {
                    using (Image img = Image.FromFile(file))//ok
                    {
                        return $"{img.Width}x{img.Height}";
                    }
                }
            }
            return null;
        }


        //private static bool IsImage(string file)
        //{
        //    try
        //    {
        //        // Attempt to load the file as an image
        //        using (Image img = Image.FromFile(file))
        //        {
        //            return true;
        //        }
        //    }
        //    catch
        //    {
        //        // If an exception occurs, the file is not an image
        //        return false;
        //    }
        //}


        public static void UpdateImagesHtml(string folderName, string dimentionToMark)
        {
            string text = File.ReadAllText(Path.Combine(folderName, "index.html"));
            text = text.Replace($">{dimentionToMark}<", $"><b>{dimentionToMark}</b><");
            File.WriteAllText(Path.Combine(folderName, "index.html"), text);
        }
        public static void CreateImagesHtml(string folderName, string dimentionToLimit = null)
        {
            var files = Directory.GetFiles(folderName, "*.*").Where(f => IsImage(f));

            StringBuilder htmlBuilder = new StringBuilder();
            htmlBuilder.AppendLine("<html><body>");
            htmlBuilder.AppendLine("<table>");

            int counter = 0;
            foreach (string file in files)
            {
                if (counter % 4 == 0) // Start a new row for every 4 images
                {
                    if (counter > 0) htmlBuilder.AppendLine("</tr>"); // Close the previous row if not the first time
                    htmlBuilder.AppendLine("<tr>"); // Start a new row
                }

                string fileName = Path.GetFileName(file);
                string imageDim = null;
                try
                {
                    imageDim = GetImageSize(file).FileDimensions;
                }
                catch// (Exception ex)
                {
                    Console.WriteLine($"Can't get dimension for {file}");
                }


                if (dimentionToLimit != null && dimentionToLimit != imageDim && !fileName.Contains("PmaxImage"))
                {
                    continue;
                }

                htmlBuilder.AppendLine($"<td><img src=\"{fileName}\" style=\"width:200px\"><br>{fileName}<br/>{imageDim}</td>");

                counter++;
            }

            if (counter > 0) htmlBuilder.AppendLine("</tr>"); // Close the last row
            htmlBuilder.AppendLine("</table>");
            htmlBuilder.AppendLine("</body></html>");
            File.WriteAllText(Path.Combine(folderName, $"index{dimentionToLimit}.html"), htmlBuilder.ToString());
        }

        public static List<string> CropImages(string folderName, string dimentionToLimit = null, string logoImageFolder = null)
        {
            List<string> croppedImages = new List<string>();
            bool logoImage = false;
            if (logoImageFolder != null)
            {
                logoImage = true;
            }
            var files = Directory.GetFiles(folderName, "*.*").Where(f => IsImage(f, includeWebImage: true));
            int counter = 0;
            var fixedImages = files.Where(x => x.Contains("PmaxImage")).ToList();
            if (logoImage)
            {
                files = files.Where(x => x == logoImageFolder);
            }
            else
            {
                if (fixedImages != null && fixedImages.Count > 0)
                {
                    foreach (var fixedImage in fixedImages)
                    {
                        if (counter == 6)
                        {
                            break;
                        }
                        string fileName = Path.GetFileName(fixedImage);
                        if (!fileName.Contains("PmaxImage"))
                        {
                            continue;
                        }
                        bool hasIssue = false;
                        var imagesAfterCpopping = ImaggeHelper.CropImage(fixedImage, counter, folderName, out hasIssue, logoImage);
                        if (imagesAfterCpopping != null)
                        {
                            croppedImages.AddRange(imagesAfterCpopping);
                        }
                        else if (hasIssue)
                        {
                            return null;
                        }
                        counter++;
                    }
                }
            }

            foreach (string file in files)
            {
                if (counter == 6 || (logoImage && counter > 0))
                {
                    break;
                }


                string imageDim = GetImageSize(file).FileDimensions;

                if (dimentionToLimit != null && dimentionToLimit != imageDim)
                {
                    continue;
                }
                bool hasIssue = false;
                var imagesAfterCpopping = ImaggeHelper.CropImage(file, counter, folderName, out hasIssue, logoImage);
                if (imagesAfterCpopping != null)
                {
                    croppedImages.AddRange(imagesAfterCpopping);
                }
                else if (hasIssue)
                {
                    return null;
                }
                counter++;
            }
            return croppedImages;
            //File.WriteAllText(Path.Combine(folderName, $"index{dimentionToLimit}.html"), htmlBuilder.ToString());
        }
        private static bool IsImage(string file, bool includeWebImage = false)
        {
            string[] extensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
            if (includeWebImage)
            {
                extensions = extensions.Concat(new[] { ".webp" }).ToArray();
            }
            return extensions.Any(x => file.EndsWith(x, StringComparison.OrdinalIgnoreCase));
        }


        public static ProductPageData GetProductData(string htmlContent)
        {
            ProductPageData product = new ProductPageData();
            try
            {
                //<meta property="og:title" content="Mojave Perfume">
                product.Title = ReadMetaProperty("og:title", htmlContent);
                product.Link = ReadMetaProperty("og:url", htmlContent);
                product.MainImage = ReadMetaProperty("og:image", htmlContent);
                product.Price = ReadMetaProperty("og:price:amount", htmlContent) + " " + ReadMetaProperty("og:price:currency", htmlContent);
                if (product.Price == " ")
                {
                    product.Price = ReadMetaProperty("product:price:amount", htmlContent) + " " + ReadMetaProperty("product:price:currency", htmlContent);
                }

            }
            catch (Exception ex)
            {
                product.Title = "PARSING ERROR";
                Console.WriteLine($"Error fetching data: {ex.Message}");
            }
            return product;
        }
        public static string GetLogoFromLdJson(string htmlContent)
        {
            try
            {
                var jsons = RegexHelper.GetAllGroupValues($"application\\/ld\\+json\">(?<content>.*?)<", htmlContent);
                var regex = new Regex($"application\\/ld\\+json\">(?<content>.*?)<", RegexOptions.CultureInvariant | RegexOptions.Singleline | RegexOptions.IgnoreCase);

                foreach (Match match in regex.Matches(htmlContent))
                {
                    string json = match.Groups[1].Value;

                    var j = json.ToString().Replace("application/ld+json\">", "");
                    if (j.Contains("Organization"))
                    {
                        dynamic ldJson = JObject.Parse(j);

                        if (ldJson["@type"] == "Organization")
                        {
                            return ldJson.logo;
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching data: {ex.Message}");
            }
            return null;
        }

        public static ProductPageData GetProductDataFromLdJson(string htmlContent)
        {
            ProductPageData product = new ProductPageData();
            try
            {
                //var json = RegexHelper.GetSingleValue($"application\\/ld\\+json\">(?<content>.*?)<", htmlContent);
                var jsons = RegexHelper.GetAllGroupValues($"application\\/ld\\+json\">(?<content>.*?)<", htmlContent);
                var regex = new Regex($"application\\/ld\\+json\">(?<content>.*?)<", RegexOptions.CultureInvariant | RegexOptions.Singleline | RegexOptions.IgnoreCase);
                foreach (Match match in regex.Matches(htmlContent))
                {
                    string json = match.Groups[1].Value;

                    var j = json.ToString().Replace("application/ld+json\">", "");
                    dynamic ldJson = JObject.Parse(j);

                    dynamic pr = GetProductJson(ldJson);
                    if (pr["@type"] == "Product")
                    {
                        product.Link = pr.url;
                        product.Title = pr.name;
                        if (pr.image != null)
                        {
                            if (pr.image.GetType() == typeof(JArray))
                            {
                                product.MainImage = pr.image[0];
                            }
                            else if (pr.image.GetType() == typeof(JObject))
                            {
                                product.MainImage = pr.image.image;
                            }
                            else
                            {
                                product.MainImage = pr.image;
                            }
                        }
                        product.Price = ParsePrice(pr);
                        product.Sku = pr.sku;
                        product.Brand = pr.brand?.name;

                        break;
                    }
                    else
                    {
                        product.Title = null;
                    }
                }
                //if (jsons != null)
                //{
                //    foreach (var json in jsons)
                //    {
                //        var j = json.ToString().Replace("application/ld+json\">", "");
                //        dynamic pr = JObject.Parse(j);
                //        if (pr["@type"] == "Product")
                //        {
                //            product.Link = pr.url;
                //            product.Title = pr.name;
                //            product.MainImage = pr.image;
                //            product.Sku = pr.sku;
                //            break;
                //        }
                //        else
                //        {
                //            product.Title = null;
                //        }
                //    }
                //}
            }
            catch (Exception ex)
            {
                product.Title = "PARSING ERROR";
                Console.WriteLine($"Error fetching data: {ex.Message}");
            }
            return product;
        }

        private static dynamic GetProductJson(dynamic ldJson)
        {
            var graphNode = ldJson["@graph"];
            if (graphNode != null && graphNode.GetType() == typeof(JArray))
            {
                foreach (var item in graphNode)
                {
                    if (item["@type"] == "Product")
                    {
                        return item;
                    }
                }
            }
            else
            {
                if (ldJson["@type"] == "Product")
                {
                    return ldJson;
                }
            }
            return null;
        }

        private static string ParsePrice(dynamic pr)
        {
            if (pr.offers.GetType() == typeof(JArray))
            {
                var firstOffer = pr.offers[0];
                return firstOffer.price + " " + firstOffer.priceCurrency;
            }
            else if (pr.offers.GetType() != typeof(JObject))
            {
                return pr.offers.price + " " + pr.offers.priceCurrency;
            }
            return null;
        }

        private static string ReadMetaProperty(string propertyName, string html)
        {
            return RegexHelper.GetSingleValue($"<meta property=\"{propertyName}\".*?content=\"(?<content>.*?)\".*?>", html);
        }
    }
}