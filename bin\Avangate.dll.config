<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Avangate.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <applicationSettings>
        <Avangate.Properties.Settings>
            <setting name="Avangate_AvangateSubscriptionAPI_subscriptionService" serializeAs="String">
                <value>https://api.avangate.com/subscription/2.2/soap/</value>
            </setting>
        </Avangate.Properties.Settings>
    </applicationSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/></startup></configuration>
