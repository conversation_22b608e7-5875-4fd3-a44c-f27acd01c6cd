<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CTCT</name>
    </assembly>
    <members>
        <member name="T:CTCT.Authentication.WebPage.AuthenticationWebPage">
            <summary>
            Class used to authenticate from web application
            </summary>
        </member>
        <member name="F:CTCT.Authentication.WebPage.AuthenticationWebPage.AccessToken">
            <summary>
            Access token field
            </summary>
        </member>
        <member name="F:CTCT.Authentication.WebPage.AuthenticationWebPage.State">
            <summary>
            State field
            </summary>
        </member>
        <member name="F:CTCT.Authentication.WebPage.AuthenticationWebPage.HttpContext">
            <summary>
            Httpcontext field
            </summary>
        </member>
        <member name="M:CTCT.Authentication.WebPage.AuthenticationWebPage.#ctor(System.Web.HttpContext,System.String)">
            <summary>
            Initialize new instance of AuthenticationWebForm class
            </summary>
            <param name="httpContext">current application context</param>
            <param name="state">state query parameter</param>
        </member>
        <member name="M:CTCT.Authentication.WebPage.AuthenticationWebPage.GetAuthorizationCode">
            <summary>
            Gets authorization code
            </summary>
        </member>
        <member name="M:CTCT.Authentication.WebPage.AuthenticationWebPage.GetAccessTokenByCode(System.String)">
            <summary>
            Gets access token by code
            </summary>
            <param name="code">authorization code</param>
            <returns>access token</returns>
        </member>
        <member name="T:CTCT.AuthenticationForm">
            <summary>
            Form used for windows authentication
            </summary>
        </member>
        <member name="F:CTCT.AuthenticationForm.AccessToken">
            <summary>
            Access token field
            </summary>
        </member>
        <member name="F:CTCT.AuthenticationForm.State">
            <summary>
            State field
            </summary>
        </member>
        <member name="M:CTCT.AuthenticationForm.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="F:CTCT.AuthenticationForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CTCT.AuthenticationForm.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CTCT.AuthenticationForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:CTCT.Components.AccountService.OrganizationAddresses">
            <summary>
            Organization addresses
            </summary>
        </member>
        <member name="T:CTCT.Components.Component">
            <summary>
            Base class for components.
            </summary>
        </member>
        <member name="M:CTCT.Components.Component.FromJSON``1(System.String)">
            <summary>
            Get the object from JSON.
            </summary>
            <typeparam name="T">The class type to be deserialized.</typeparam>
            <param name="json">The serialization string.</param>
            <returns>Returns the object deserialized from the JSON string.</returns>
        </member>
        <member name="M:CTCT.Components.Component.ToJSON">
            <summary>
            Serialize an object to JSON.
            </summary>
            <returns>Returns a string representing the serialized object.</returns>
        </member>
        <member name="P:CTCT.Components.AccountService.OrganizationAddresses.City">
            <summary>
            REQUIRED if including organization_addresses; The city the organization is located in 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.OrganizationAddresses.CountryCode">
            <summary>
            REQUIRED if including organization_addresses; Standard 2 letter ISO 3166-1 code for the organization_addresses 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.OrganizationAddresses.Line1">
            <summary>
            REQUIRED if including organization_addresses; Line 1 of the organization's street address 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.OrganizationAddresses.Line2">
            <summary>
            Line 2 of the organization's street address
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.OrganizationAddresses.Line3">
            <summary>
            Line 3 of the organization's street address 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.OrganizationAddresses.PostalCode">
            <summary>
            Postal (zip) code of the organization's street address 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.OrganizationAddresses.State">
            <summary>
            Name of the state or province for the organization_addresses; 
            For country = CA or US, this field is overwritten by the state or province name derived from the state_code, if entered. 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.OrganizationAddresses.StateCode">
            <summary>
            Use ONLY for the standard 2 letter abbreviation for the US state or Canadian province for organization_addresses;
            NOTE: A data validation error occurs if state_code is populated and country_code does not = US or CA. 
            </summary>
        </member>
        <member name="T:CTCT.Components.AccountService.AccountSummaryInformation">
            <summary>
            Summary account-related information 
            </summary>
        </member>
        <member name="M:CTCT.Components.AccountService.AccountSummaryInformation.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.CountryCode">
            <summary>
            Standard 2 letter ISO 3166-1 code of the country associated with the account owner 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.Email">
            <summary>
            Email address associated with the account owner 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.FirstName">
            <summary>
            The account owner's first name 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.LastName">
            <summary>
            The account owner's last name 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.OrganizationName">
            <summary>
            Name of the organization associated with the account 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.Phone">
            <summary>
            Phone number associated with the account owner 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.StateCode">
            <summary>
            2 letter code for USA state or Canadian province ONLY, available only if country_code = US or CA associated with the account owner 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.TimeZone">
            <summary>
            The time zone associated with the account 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.Website">
            <summary>
            The URL of the Web site associated with the account 
            </summary>
        </member>
        <member name="P:CTCT.Components.AccountService.AccountSummaryInformation.OrganizationAddresses">
            <summary>
            An array of organization street addresses; currently, only a single address is supported. 
            This is not a required attribute, but if you include organization_addresses in a put, it must include the country_code, city, and line1 fields at minimum. 
            </summary>
        </member>
        <member name="T:CTCT.Components.Activities.Activity">
            <summary>
            Activity class.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.Id">
            <summary>
            Activity id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.Type">
            <summary>
            Gets or sets the type.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.Status">
            <summary>
            Gets or sets the status.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.StartDateString">
            <summary>
            Represetns the start date string.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.StartDate">
            <summary>
            Gets or sets the start date.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.FinishDateString">
            <summary>
            String representation of finish date.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.FinishDate">
            <summary>
            Gets or sets the finish date.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.FileName">
            <summary>
            Gets or sets the filename.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.CreatedDateString">
            <summary>
            String representation of created date.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.CreatedDate">
            <summary>
            Gets or sets the created date.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.ErrorCount">
            <summary>
            Gets or sets the error count.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.ContactCount">
            <summary>
            Gets or sets the contact count.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.Errors">
            <summary>
            Gets or sets the error list.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.Activity.Warnings">
            <summary>
            Gets or sets the warning list.
            </summary>
        </member>
        <member name="T:CTCT.Components.Activities.ActivityError">
            <summary>
            Activity error class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Activities.ActivityError.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ActivityError.Message">
            <summary>
            Gets or sets the message.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ActivityError.LineNumber">
            <summary>
            Gets or sets the line number.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ActivityError.EmailAddress">
            <summary>
            Gets or sets the email address.
            </summary>
        </member>
        <member name="T:CTCT.Components.Activities.AddContacts">
            <summary>
            Represents an AddContact activity class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Activities.AddContacts.#ctor(System.Collections.Generic.IList{CTCT.Components.Activities.AddContactsImportData},System.Collections.Generic.IList{System.String},System.Collections.Generic.IList{System.String})">
            <summary>
            Class constructor.
            </summary>
            <param name="contacts">List of contacts.</param>
            <param name="lists">List of id's.</param>
            <param name="columnNames">Column names to import.</param>
        </member>
        <member name="P:CTCT.Components.Activities.AddContacts.Id">
            <summary>
            Activity id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContacts.ImportData">
            <summary>
            Gets or sets the list of imported data.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContacts.Lists">
            <summary>
            Gets or sets the list of id's to add.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContacts.ColumnNames">
            <summary>
            Gets or sets the list of column names.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContacts.ContactCount">
            <summary>
            Gets or sets the contact count that were processed by this activity.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContacts.ErrorCount">
            <summary>
            Gets or sets the activity process error count.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContacts.Type">
            <summary>
            Activity type.
            </summary>
        </member>
        <member name="T:CTCT.Components.Activities.AddContactsImportData">
            <summary>
            Add contacts import data class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Activities.AddContactsImportData.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.FirstName">
            <summary>
            Gets or sets the first name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.MiddleName">
            <summary>
            Gets or sets the middle name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.LastName">
            <summary>
            Gets or sets the last name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.JobTitle">
            <summary>
            Gets or sets the job title.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.CompanyName">
            <summary>
            Gets or sets the company name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.WorkPhone">
            <summary>
            Gets or sets the work phone.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.HomePhone">
            <summary>
            Gets or sets the home phone.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.EmailAddresses">
            <summary>
            Gets or sets the email addresses list.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.Addresses">
            <summary>
            Gets or sets addresses list.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.AddContactsImportData.CustomFields">
            <summary>
            Gets or sets the custom fields list.
            </summary>
        </member>
        <member name="T:CTCT.Components.Activities.ClearContactList">
            <summary>
            Represents an ClearContactList activity class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Activities.ClearContactList.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ClearContactList.Lists">
            <summary>
            Gets or sets the list of id's to add.
            </summary>
        </member>
        <member name="T:CTCT.Components.Activities.ExportContacts">
            <summary>
            Export contacts class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Activities.ExportContacts.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ExportContacts.FileType">
            <summary>
            Gets or sets the field type.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ExportContacts.SortBy">
            <summary>
            Gets or sets sort by.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ExportContacts.ExportDateAdded">
            <summary>
            Gets or sets the flag for export date added.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ExportContacts.ExportAddedBy">
            <summary>
            Gets or sets the flag for export added by.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ExportContacts.Lists">
            <summary>
            Gets or sets list of id's to export.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ExportContacts.ColumnNames">
            <summary>
            Gets or sets the column names.
            </summary>
        </member>
        <member name="T:CTCT.Components.Activities.RemoveContact">
            <summary>
            Represents an AddContact activity class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Activities.RemoveContact.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.RemoveContact.ImportData">
            <summary>
            Gets or sets the list of imported data.
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.RemoveContact.Lists">
            <summary>
            Gets or sets the list of id's to add.
            </summary>
        </member>
        <member name="T:CTCT.Components.Activities.ImportEmailAddress">
            <summary>
            Represents an ImportEmailAddress class
            </summary>
        </member>
        <member name="M:CTCT.Components.Activities.ImportEmailAddress.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.Activities.ImportEmailAddress.EmailAddresses">
            <summary>
            Gets or sets the list of email addresses
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.SentContactList">
            <summary>
            Represents a single List in Constant Contact.
            </summary>
        </member>
        <member name="M:CTCT.Components.Contacts.SentContactList.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.SentContactList.Id">
            <summary>
            Gets or sets the id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.SentContactList.Status">
            <summary>
            Gets or sets the status.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.VerifiedEmailAddress">
            <summary>
            VerifiedEmailAddress class
            </summary>
        </member>
        <member name="M:CTCT.Components.Contacts.VerifiedEmailAddress.#ctor">
            <summary>
            Class constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.VerifiedEmailAddress.EmailAddr">
            <summary>
            Gets or sets the email address.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.VerifiedEmailAddress.Status">
            <summary>
            Gets or sets the status.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.ClickThroughDetails">
            <summary>
            Represents a click through detail class.
            </summary>
        </member>
        <member name="M:CTCT.Components.EmailCampaigns.ClickThroughDetails.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.ClickThroughDetails.Url">
            <summary>
            Gets or sets the actual url that was clicked on.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.ClickThroughDetails.UrlUid">
            <summary>
            Gets or sets the url unique identifier.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.ClickThroughDetails.ClickCount">
            <summary>
            Gets or sets the number of times the url was clicked on.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.EmailCampaign">
            <summary>
            Represents a single Campaign in Constant Contact.
            </summary>
        </member>
        <member name="M:CTCT.Components.EmailCampaigns.EmailCampaign.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.Id">
            <summary>
            Gets or sets the id.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.Name">
            <summary>
            Gets or sets the name.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.Subject">
            <summary>
            Gets or sets the subject.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.FromName">
            <summary>
            Gets or sets the name from.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.FromEmail">
            <summary>
            Gets or sets the email from.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.ReplyToEmail">
            <summary>
            Gets or sets the reply email address.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.TemplateTypeString">
            <summary>
            Campaign type, string representation.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.TemplateType">
            <summary>
            Gets or sets the campaign type.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.CreatedDateString">
            <summary>
            Created date string representation.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.CreatedDate">
            <summary>
            Gets or sets the creation date.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.ModifiedDateString">
            <summary>
            String representation of modification date.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.ModifiedDate">
            <summary>
            Gets or sets the modification date.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.LastRunDateString">
            <summary>
            String representation of last run date.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.LastRunDate">
            <summary>
            Gets or sets the last run date.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.NextRunDateString">
            <summary>
            String representation of next run date.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.NextRunDateDate">
            <summary>
            Gets or sets the next run date.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.StatusString">
            <summary>
            Campaign status, string representation.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.Status">
            <summary>
            Gets or sets the campaign status.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.IsViewAsWebPageEnabled">
            <summary>
            Gets or sets the permission for web page.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.PermissionReminderText">
            <summary>
            Gets or sets the permission reminder text.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.ViewAsWebPageText">
            <summary>
            Gets or sets the view page text.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.ViewAsWebPageLinkText">
            <summary>
            Gets or sets the view page link.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.GreetingSalutations">
            <summary>
            Gets or sets the greeting.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.GreetingNameString">
            <summary>
            Greeting name, string representation.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.GreetingName">
            <summary>
            Gets or sets the greeting name.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.PermanentLink">
            <summary>
            Gets or sets o non-expiring link to use for sharing a sent email campaign using social channels
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.GreetingString">
            <summary>
            Gets or sets the greeting string.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.EmailContent">
            <summary>
            Gets or sets the email content.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.EmailContentFormatString">
            <summary>
            String representation of email content format.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.EmailContentFormat">
            <summary>
            Gets or sets the email content format.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.TextContent">
            <summary>
            Gets or sets the text content.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.StyleSheet">
            <summary>
            Gets or sets the syle sheet.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.TrackingSummary">
            <summary>
            Gets or sets the tracking summary.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.ClickThroughDetails">
            <summary>
            Click through details for each link in this email campaign.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.MessageFooter">
            <summary>
            Gets or sets the message footer.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.Lists">
            <summary>
            Gets or sets the lists where the campaign is registered.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.IsVisibleInUI">
            <summary>
            Gets or sets the flag for UI visibility.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.ArchiveStatus">
            <summary>
            Gets or sets the archive status.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.EmailCampaign.ArchiveURL">
            <summary>
            Gets or sets the archive URL.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.GreetingName">
            <summary>
            Greeting name.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.GreetingName.NONE">
            <summary>
            None.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.GreetingName.FIRST_NAME">
            <summary>
            First name.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.GreetingName.LAST_NAME">
            <summary>
            Last name.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.GreetingName.FIRST_AND_LAST_NAME">
            <summary>
            First and last name.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.TemplateType">
            <summary>
            Campaign type.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.TemplateType.CUSTOM">
            <summary>
            Custom type.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.TemplateType.STOCK">
            <summary>
            Stock type.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.CampaignStatus">
            <summary>
            Campaign status.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.CampaignStatus.DRAFT">
            <summary>
            Draft.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.CampaignStatus.RUNNING">
            <summary>
            Running.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.CampaignStatus.SENT">
            <summary>
            Sent.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.CampaignStatus.SCHEDULED">
            <summary>
            Scheduled.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.CampaignEmailFormat">
            <summary>
            Campaign email format.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.CampaignEmailFormat.HTML">
            <summary>
            HTML.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.CampaignEmailFormat.XHTML">
            <summary>
            XHTML.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.Address">
            <summary>
            Represents a single Address of a Contact.
            </summary>
        </member>
        <member name="M:CTCT.Components.Contacts.Address.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.Id">
            <summary>
            Gets or sets the id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.Line1">
            <summary>
            Gets or sets the first address line.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.Line2">
            <summary>
            Gets or sets the second address line.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.Line3">
            <summary>
            Gets or sets the third address line.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.City">
            <summary>
            Gets or sets the city.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.AddressType">
            <summary>
            Gets or sets the address type.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.StateCode">
            <summary>
            Gets or sets the state code.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.StateName">
            <summary>
            Gets or sets the state name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.CountryCode">
            <summary>
            Gets or sets the contry code.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.PostalCode">
            <summary>
            Gets or sets the postal code.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Address.SubPostalCode">
            <summary>
            Gets or sets the subpostal code.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.AddressType">
            <summary>
            Address type structure.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.AddressType.Personal">
            <summary>
            Personal.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.AddressType.Business">
            <summary>
            Business.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.AddressType.Unknown">
            <summary>
            Unknown.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.Contact">
            <summary>
            Represents a single Contact in Constant Contact.
            </summary>
        </member>
        <member name="M:CTCT.Components.Contacts.Contact.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.Id">
            <summary>
            Gets or sets the id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.Status">
            <summary>
            Gets or sets the status.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.FirstName">
            <summary>
            Gets or sets the first name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.MiddleName">
            <summary>
            Gets or sets the middle name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.LastName">
            <summary>
            Gets or sets the last name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.Confirmed">
            <summary>
            Gets or sets the confirmation flag.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.Source">
            <summary>
            Gets or sets the source.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.EmailAddresses">
            <summary>
            Gets or sets the email addresses.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.PrefixName">
            <summary>
            Gets or sets the prefix name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.JobTitle">
            <summary>
            Gets or sets job title.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.Addresses">
            <summary>
            Gets or sets the addresses.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.Notes">
            <summary>
            Gets or sets the notes.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.CompanyName">
            <summary>
            Gets or sets the company name.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.HomePhone">
            <summary>
            Gets or sets the home phone.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.WorkPhone">
            <summary>
            Gets or sets the work phone.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.CellPhone">
            <summary>
            Gets or sets the cell phone.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.Fax">
            <summary>
            Gets or sets the fax number.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.DateCreated">
            <summary>
            Gets or sets the date and time the contact was added
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.DateModified">
            <summary>
            Gets or sets the date and time contact's information was last modified
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.CustomFields">
            <summary>
            Gets or sets the list of custom fields.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.Lists">
            <summary>
            Gets or sets the lists.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Contact.SourceDetails">
            <summary>
            Gets or sets the source details.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.ContactStatus">
            <summary>
            Contact status enumeration
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.ContactStatus.ACTIVE">
            <summary>
            Active.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.ContactStatus.UNCONFIRMED">
            <summary>
            Unconfirmed.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.ContactStatus.OPTOUT">
            <summary>
            OptOut.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.ContactStatus.REMOVED">
            <summary>
            Removed.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.ActionBy">
            <summary>
            ActionBy structure.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.ActionBy.ActionByVisitor">
            <summary>
            ActionByVisitor.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.ActionBy.ActionByOwner">
            <summary>
            ActionByOwner.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.Status">
            <summary>
            Status structure.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.Status.Active">
            <summary>
            Active.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.Status.Unconfirmed">
            <summary>
            Unconfirmed.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.Status.OptOut">
            <summary>
            OptOut.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.Status.Removed">
            <summary>
            Removed.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.Status.NonSubscriber">
            <summary>
            NonSubscriber.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.Status.Visitor">
            <summary>
            Visitor.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.ContactList">
            <summary>
            Represents a single List in Constant Contact.
            </summary>
        </member>
        <member name="M:CTCT.Components.Contacts.ContactList.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.ContactList.Id">
            <summary>
            Gets or sets the id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.ContactList.Status">
            <summary>
            Gets or sets the status.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.ContactList.ContactCount">
            <summary>
            Gets or sets the number of contacts in the list
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.ContactList.Name">
            <summary>
            Gets or sets the contact list name
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.ContactList.DateCreated">
            <summary>
            Gets or sets the date and time the contact list was added
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.ContactList.DateModified">
            <summary>
            Gets or sets the date and time list's information was last modified
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.CustomField">
            <summary>
            Custom field class.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.CustomField.Name">
            <summary>
            Name of the custom field. Only accepted names.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.CustomField.Value">
            <summary>
            Value of the custom field.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.CustomFieldName">
            <summary>
            Custom fields names structure.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField1">
            <summary>
            Custom field 1.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField2">
            <summary>
            Custom field 2.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField3">
            <summary>
            Custom field 3.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField4">
            <summary>
            Custom field 4.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField5">
            <summary>
            Custom field 5.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField6">
            <summary>
            Custom field 6.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField7">
            <summary>
            Custom field 7.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField8">
            <summary>
            Custom field 8.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField9">
            <summary>
            Custom field 9.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField10">
            <summary>
            Custom field 10.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField11">
            <summary>
            Custom field 11.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField12">
            <summary>
            Custom field 12.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField13">
            <summary>
            Custom field 13.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField14">
            <summary>
            Custom field 14.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.CustomFieldName.CustomField15">
            <summary>
            Custom field 15.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.EmailAddress">
            <summary>
            Represents a single EmailAddress of a Contact.
            </summary>
        </member>
        <member name="M:CTCT.Components.Contacts.EmailAddress.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="M:CTCT.Components.Contacts.EmailAddress.#ctor(System.String)">
            <summary>
            Class constructor.
            </summary>
            <param name="emailAddress">Email address.</param>
        </member>
        <member name="P:CTCT.Components.Contacts.EmailAddress.Id">
            <summary>
            Email address id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.EmailAddress.OptOutDate">
            <summary>
            Gets or sets the OPT out date.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.EmailAddress.Status">
            <summary>
            Gets or sets the status.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.EmailAddress.OptInSource">
            <summary>
            Gets or sets the OPT source.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.EmailAddress.OptInDate">
            <summary>
            Gets or sets the OPT date.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.EmailAddress.OptOutSource">
            <summary>
            Gets or sets the OPT source.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.EmailAddress.ConfirmStatus">
            <summary>
            Gets or sets the confirmation status.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.EmailAddress.EmailAddr">
            <summary>
            Gets or sets the email address.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.ConfirmStatus">
            <summary>
            Confirmation status structure.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.ConfirmStatus.Confirmed">
            <summary>
            Confirmed.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.ConfirmStatus.NoConfirmationRequired">
            <summary>
            NoConfirmationRequired.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.ConfirmStatus.Unconfirmed">
            <summary>
            Unconfirmed.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.OptInSource">
            <summary>
            Option source structure.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.OptInSource.ActionByVisitor">
            <summary>
            ActionByVisitor.
            </summary>
        </member>
        <member name="F:CTCT.Components.Contacts.OptInSource.ActionByOwner">
            <summary>
            ActionByOwner.
            </summary>
        </member>
        <member name="T:CTCT.Components.Contacts.Note">
            <summary>
            Note class.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Note.Id">
            <summary>
            Gets or sets the id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Note.Content">
            <summary>
            Gets or sets the note content.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Note.CreatedDate">
            <summary>
            Gets or sets the datetime when note was created.
            </summary>
        </member>
        <member name="P:CTCT.Components.Contacts.Note.ModifiedDate">
            <summary>
            Gets or sets the datetime when note was modified.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.MessageFooter">
            <summary>
            Represents a click through detail class.
            </summary>
        </member>
        <member name="M:CTCT.Components.EmailCampaigns.MessageFooter.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.City">
            <summary>
            Gets or sets the city.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.State">
            <summary>
            Gets or sets the state.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.Country">
            <summary>
            Gets or sets the country.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.OrganizationName">
            <summary>
            Gets or sets the organization name.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.AddressLine1">
            <summary>
            Gets or sets the addrese line 1.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.AddressLine2">
            <summary>
            Gets or sets the addrese line 2.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.AddressLine3">
            <summary>
            Gets or sets the addrese line 3.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.InternationalState">
            <summary>
            Gets or sets the international state.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.PostalCode">
            <summary>
            Gets or sets the postal code.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.IncludeForwardEmail">
            <summary>
            Gets or sets the include forward email flag.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.ForwardEmailLinkText">
            <summary>
            Gets or sets the forward email link text.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.IncludeSubscribeLink">
            <summary>
            Gets or sets the include subscribe link flag.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.MessageFooter.SubscribeLinkText">
            <summary>
            Gets or sets the subscribe link text.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.Schedule">
            <summary>
            Represents a campaign Schedule in Constant Contact class.
            </summary>
        </member>
        <member name="M:CTCT.Components.EmailCampaigns.Schedule.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.Schedule.Id">
            <summary>
            Unique id of the schedule.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.Schedule.ScheduledDateString">
            <summary>
            The scheduled start date/time in ISO 8601 format.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.Schedule.ScheduledDate">
            <summary>
            Gets or sets the scheduled date.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.TestSend">
            <summary>
            Represents a campaign Test Send in Constant Contact class.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.TestSend.Format">
            <summary>
            Format of the email to send (HTML, TEXT, HTML_AND_TEXT).
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.TestSend.PersonalMessage">
            <summary>
            Personal message to send along with the test send.
            </summary>
        </member>
        <member name="P:CTCT.Components.EmailCampaigns.TestSend.EmailAddresses">
            <summary>
            Array of email addresses to send the test send to.
            </summary>
        </member>
        <member name="T:CTCT.Components.EmailCampaigns.EmailFormat">
            <summary>
            Email format.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.EmailFormat.HTML">
            <summary>
            Html format.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.EmailFormat.TEXT">
            <summary>
            Text format.
            </summary>
        </member>
        <member name="F:CTCT.Components.EmailCampaigns.EmailFormat.HTML_AND_TEXT">
            <summary>
            Html and text format.
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.EventSpotAddress">
            <summary>
            EventSpotAddress class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.StateCode">
            <summary>
            Standard 2 letter abbreviation for the state or Canadian province of the event location; if state_code is entered, the system overwrites the state property with the resolved state or province name
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.PostalCode">
            <summary>
            Postal ZIP code for the event 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.State">
            <summary>
            State or Canadian province name of the event location 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.Longitude">
            <summary>
            Longitude coordinates of the event location, , not used to determine event Location at this time on map if is_map_displayed set to true 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.Latitude">
            <summary>
            Latitude coordinates of the event location, not used to determine event Location on map if is_map_displayed set to true 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.Line1">
            <summary>
            Event address line 1 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.Line2">
            <summary>
            Event address line 2
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.Line3">
            <summary>
            Event address line 3
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.CountryCode">
            <summary>
            Standard 2 letter ISO 3166-1 code of the country associated with the event address 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.Country">
            <summary>
            Country of the event location 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotAddress.City">
            <summary>
            City of the event location 
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.Attribute">
            <summary>
            EventSpot Item Attribute
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Attribute.Id">
            <summary>
            The attribute's unique ID 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Attribute.Name">
            <summary>
            Name of attribute being sold 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Attribute.QuantityAvailable">
            <summary>
            Number of item attributes that are still available for sale 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Attribute.QuantityTotal">
            <summary>
            Number of attributes offered for sale 
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.EventSpotContact">
            <summary>
            EventSpotContact class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotContact.EmailAddress">
            <summary>
            Event contact's email-address
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotContact.Name">
            <summary>
            Name of the person conducting or managing the event 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotContact.OrganizationName">
            <summary>
            Event contact's organization name 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventSpotContact.PhoneNumber">
            <summary>
            Event contact's phone number 
            </summary>
        </member>
        <member name="T:CTCT.Services.IEventSpotService">
            <summary>
            Interface for EventSpot class
            </summary>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetAllEventSpots(System.String,System.String,System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            View all existing events
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 50</param>
            <param name="pag">Pagination object</param>
            <returns>ResultSet containing a results array of IndividualEvents</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetEventSpot(System.String,System.String,System.String)">
            <summary>
            Retrieve an event specified by the event_id
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>The event</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PostEventSpot(System.String,System.String,CTCT.Components.EventSpot.IndividualEvent)">
            <summary>
            Publish an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventSpot">The event to publish</param>
            <returns>The published event</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PutEventSpot(System.String,System.String,System.String,CTCT.Components.EventSpot.IndividualEvent)">
            <summary>
            Update an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id to be updated</param>
            <param name="eventSpot">The new values for event</param>
            <returns>The updated event</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PatchEventSpotStatus(System.String,System.String,System.String,CTCT.Components.EventSpot.EventStatus)">
            <summary>
            Publish or cancel an event by changing the status of the event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="eventStatus">New status of the event. ACTIVE" and "CANCELLED are allowed</param>
            <returns>The updated event</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetAllEventFees(System.String,System.String,System.String)">
            <summary>
            Retrieve all existing fees for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>A list of event fees for the specified event</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetEventFee(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve an individual event fee
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="feeId">EventFee id</param>
            <returns>An EventFee object</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PutEventFee(System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.EventFee)">
            <summary>
            Update an individual event fee
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="feeId">EventFee id</param>
            <param name="eventFee">The new values of EventFee</param>
            <returns>The updated EventFee</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.DeleteEventFee(System.String,System.String,System.String,System.String)">
            <summary>
             Delete an individual event fee
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="feeId">EventFee id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PostEventFee(System.String,System.String,System.String,CTCT.Components.EventSpot.EventFee)">
            <summary>
            Create an individual event fee
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="eventFee">EventFee object</param>
            <returns>The newly created EventFee</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetAllPromocodes(System.String,System.String,System.String)">
            <summary>
            Retrieve all existing promo codes for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>A list of Promocode</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetPromocode(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve an existing promo codes for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="promocodeId">Promocode id</param>
            <returns>The Promocode object</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PostPromocode(System.String,System.String,System.String,CTCT.Components.EventSpot.Promocode)">
            <summary>
            Create a new promo code for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="promocode">Promocode object to be created</param>
            <returns>The newly created Promocode</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PutPromocode(System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.Promocode)">
            <summary>
            Update a promo code for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="promocodeId">Promocode id</param>
            <param name="promocode">The new Promocode values</param>
            <returns>The newly updated Promocode</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.DeletePromocode(System.String,System.String,System.String,System.String)">
            <summary>
            Delete a promo code for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="promocodeId">Promocode id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetRegistrant(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve detailed information for a specific event registrant
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="registrantId">Redistrant id</param>
            <returns>Registrant details</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetAllRegistrants(System.String,System.String,System.String)">
            <summary>
            Retrieve a list of registrants for the specified event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>ResultSet containing a results array of Registrant</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetAllEventItems(System.String,System.String,System.String)">
            <summary>
            Retrieve all existing items associated with an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>A list of EventItem</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetEventItem(System.String,System.String,System.String,System.String)">
            <summary>
             Retrieve specific event item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">Eventitem id</param>
            <returns>EventItem object</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PutEventItem(System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.EventItem)">
            <summary>
             Update a specific event item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="eventItem">The newly values for EventItem</param>
            <returns>The updated EventItem</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PostEventItem(System.String,System.String,System.String,CTCT.Components.EventSpot.EventItem)">
            <summary>
             Create a specific event item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="eventItem">EventItem id</param>
            <returns>The newly created EventItem</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.DeleteEventItem(System.String,System.String,System.String,System.String)">
            <summary>
            Delete a specific event item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PostEventItemAttribute(System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.Attribute)">
            <summary>
            Create an attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attribute">The Attribute object</param>
            <returns>The newly created attribure</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.PutEventItemAttribute(System.String,System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.Attribute)">
            <summary>
            Updates an existing attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attributeId">Attribute id</param>
            <param name="attribute">Attribute new values</param>
            <returns>The newly updated attribute</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetEventItemAttribute(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve an existing attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attributeId">Attribute id</param>
            <returns>Attribute object</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.GetAllEventItemAttributes(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve all existing attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <returns>A list of Attributes</returns>
        </member>
        <member name="M:CTCT.Services.IEventSpotService.DeleteEventItemAttribute(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Delete an existing attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attributeId">Attribute id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="T:CTCT.Util.PatchRequest">
            <summary>
            Used PATCH method
            </summary>
        </member>
        <member name="M:CTCT.Util.PatchRequest.#ctor(System.String,System.String,System.Object)">
            <summary>
            Constructor
            </summary>
            <param name="op">The operation to perform </param>
            <param name="path">Where in the object to perform it</param>
            <param name="value">The new value to write</param>
        </member>
        <member name="P:CTCT.Util.PatchRequest.Op">
            <summary>
            The operation to perform 
            </summary>
        </member>
        <member name="P:CTCT.Util.PatchRequest.Path">
            <summary>
            Where in the object to perform it
            </summary>
        </member>
        <member name="P:CTCT.Util.PatchRequest.Value">
            <summary>
            The new value to write
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.EventFee">
            <summary>
            EventFee class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventFee.EarlyFee">
            <summary>
            Fee for registrations that occur prior to the event's early_fee_date 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventFee.Fee">
            <summary>
            The fee amount 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventFee.FeeScopeString">
            <summary>
            String representation Specifies who the fee applies to
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventFee.FeeScope">
            <summary>
            Specifies who the fee applies to
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventFee.Id">
            <summary>
            Unique ID for that fee 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventFee.Label">
            <summary>
            Fee description displayed to event registrants 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventFee.LateFee">
            <summary>
            Fee for registrations that occur after the event's late_fee_date 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventFee.HasRestrictedAccess">
            <summary>
            Has restricted access
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.FeeScope">
            <summary>
            Specifies who the fee applies to
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.FeeScope.BOTH">
            <summary>
            Fee applies to Registrants and Guests
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.FeeScope.REGISTRANTS">
            <summary>
            Fee applies to registrants only
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.FeeScope.GUESTS">
            <summary>
            Fee applies to guests only
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.EventItem">
            <summary>
            EventItem class
            </summary>
        </member>
        <member name="M:CTCT.Components.EventSpot.EventItem.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventItem.Attributes">
            <summary>
            An array of item attributes and options 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventItem.DefaultQuantityAvailable">
            <summary>
            Number of items available for sale, displayed on the registration page if show_quantity_available = true. 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventItem.DefaultQuantityTotal">
            <summary>
            The total quantity of items offered for sale, minimum = 0, cannot leave blank. 
            If the item has attributes, the summation of the quantity_total for all attributes automatically overwrites the value you enter here. 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventItem.Description">
            <summary>
            The item description that is shown on the registration page
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventItem.Id">
            <summary>
            The items unique ID 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventItem.Name">
            <summary>
            Item name that is shown on the registration page 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventItem.PerRegistrantLimit">
            <summary>
            The maximum quantity of this item that a registrant, as well as any of their guests, can purchase, minimum = 0, cannot leave blank; value cannot be greater than the value of default_quantity_available 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventItem.Price">
            <summary>
            The item cost, minimum = 0.00 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.EventItem.ShowQuantityAvailable">
            <summary>
            If true, displays the remaining quantity of this item for purchase on the registration page 
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.Fee">
            <summary>
            Fee class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Fee.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Fee.Name">
            <summary>
            Name
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Fee.Type">
            <summary>
            Type
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Fee.Quantity">
            <summary>
            Quantity
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Fee.Amount">
            <summary>
            Amount
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Fee.FeePeriodType">
            <summary>
            Fee period type
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Fee.PromoType">
            <summary>
            Amount paid
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.Field">
            <summary>
            Field class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Field.TypeString">
            <summary>
            String representation of the type value
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Field.FieldType">
            <summary>
            Type of the value
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Field.Name">
            <summary>
            Name of the field 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Field.Label">
            <summary>
            Field label displayed to viewers 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Field.Value">
            <summary>
            The value
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.Guest">
            <summary>
            Guest class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Guest.GuestId">
            <summary>
            Unique ID assigned to a guest 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Guest.GuestsInfo">
            <summary>
            Field sections displayed 
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.Order">
            <summary>
            Order class
            </summary>
        </member>
        <member name="M:CTCT.Components.EventSpot.Order.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Order.Total">
            <summary>
            Total
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Order.Fees">
            <summary>
            Fees list
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Order.OrderId">
            <summary>
            Order id
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Order.CurrencyType">
            <summary>
            Currency type
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Order.OrderDateString">
            <summary>
            String representation Order date
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Order.OrderDate">
            <summary>
            Order date
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.PaymentPromoCode">
            <summary>
            PaymentPromoCode class
            </summary>
        </member>
        <member name="M:CTCT.Components.EventSpot.PaymentPromoCode.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PaymentPromoCode.TotalDiscount">
            <summary>
            Total discount
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PaymentPromoCode.PromoCodeInfo">
            <summary>
            Promo code info
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.PaymentSummary">
            <summary>
            Payment summary
            </summary>
        </member>
        <member name="M:CTCT.Components.EventSpot.PaymentSummary.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PaymentSummary.Order">
            <summary>
            Order
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PaymentSummary.PaymentStatus">
            <summary>
            Payment status
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PaymentSummary.PaymentType">
            <summary>
            Payment type
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PaymentSummary.PromoCode">
            <summary>
            Promo code
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.PromoCodeInfo">
            <summary>
            PromoCodeInfo class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PromoCodeInfo.CodeName">
            <summary>
            Code name
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PromoCodeInfo.CodeTypeString">
            <summary>
            String representation Code type
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PromoCodeInfo.CodeType">
            <summary>
            Code type
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PromoCodeInfo.RedemptionCount">
            <summary>
            Redemption count
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PromoCodeInfo.DiscountPercent">
            <summary>
            Discount percent
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PromoCodeInfo.DiscountAmount">
            <summary>
            Discount amount 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PromoCodeInfo.DiscountTypeString">
            <summary>
            Discount types
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PromoCodeInfo.DiscountType">
            <summary>
            Discount types
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.PromoCodeInfo.DiscountScope">
            <summary>
            Discount scope
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.Registrant">
            <summary>
            Registrant class
            </summary>
        </member>
        <member name="M:CTCT.Components.EventSpot.Registrant.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.Sections">
            <summary>
            Sections
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.TicketId">
            <summary>
            Ticket id
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.RegistrationDateString">
            <summary>
            String representation of date the registrant registered for the event
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.RegistrationDate">
            <summary>
            Date the registrant registered for the event
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.PaymentSummary">
            <summary>
            Payment summary
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.Email">
            <summary>
            Email
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.FirstName">
            <summary>
            First name
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.LastName">
            <summary>
            Last name
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.GuestCount">
            <summary>
            Guest count
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.PaymentStatus">
            <summary>
            Payment status
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.RegistrationStatus">
            <summary>
            Registration status
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.UpdatedDateString">
            <summary>
            String representation of update date
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.UpdatedDate">
            <summary>
            Update date
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Registrant.GuestSections">
            <summary>
            An array of guest properties 
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.IndividualEvent">
            <summary>
            IndividualEvent class
            </summary>
        </member>
        <member name="M:CTCT.Components.EventSpot.IndividualEvent.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.ActiveDateString">
            <summary>
            String representation of date event was published or announced, in ISO-8601 format
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.ActiveDate">
            <summary>
            Date event was published or announced
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.AreRegistrantsPublic">
            <summary>
            Set to true allows registrants to view others who have registered for the event, Default is false 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.CancelledDateString">
            <summary>
            Date the event was cancelled in ISO-8601 format
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.CancelledDate">
            <summary>
            Date the event was cancelled
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.CreatedDateString">
            <summary>
            String representation Date the event was created in ISO-8601 format
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.CreatedDate">
            <summary>
            Date the event was created
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.CurrencyType">
            <summary>
            Currency that the account will be paid in; although this is not a required field, it has a default value of USD.
            Valid values are: USD, CAD, AUD, CHF, CZK, DKK, EUR, GBP, HKD, HUF, ILS, JPY, MXN, NOK, NZD, PHP, PLN, SEK, SGD, THB, TWD 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.DeletedDateString">
            <summary>
            String representation Date the event was deleted in ISO-8601 format 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.DeletedDate">
            <summary>
            Date the event was deleted
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.Description">
            <summary>
            Provide a brief description of the event that will be visible on the event registration form and landing page 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.EndDateString">
            <summary>
            String representation of the event end date, in ISO-8601 format
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.EndDate">
            <summary>
            The event end date
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.GoogleAnalyticsKey">
            <summary>
            Enter the Google analytics key if being used to track the event registration homepage 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.GoogleMerchantId">
            <summary>
            Google merchant id to which payments are made; Google Checkout is not supported for new events, only valid on events created prior to October 2013
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.Id">
            <summary>
            Unique ID of the event 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.IsCalendarDisplayed">
            <summary>
            Set to true to display the event on the account's calendar; Default = true 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.IsCheckinAvailable">
            <summary>
            Set to true to enable registrant check-in, and indicate that the registrant attended the event; Default is false 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.IsHomePageDisplayed">
            <summary>
            Indicates if the event home/landing page is displayed for the event; set to true only if a landing page has been created for the event; Default is false 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.IsListedInExternalDirectory">
            <summary>
            Set to true to publish the event in external event directories such as SocialVents and EventsInAmerica; Default is false 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.IsMapDisplayed">
            <summary>
            For future usage, Default = true 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.IsVirtualEvent">
            <summary>
            Set to true if this is an online event; Default is false 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.Location">
            <summary>
            Name of the venue or Location at which the event is being held 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.MetaDataTags">
            <summary>
            Specify keywords to improve search engine optimization (SEO) for the event; use commas to separate multiple keywords 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.Name">
            <summary>
            The event filename - not visible to registrants 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.PayableTo">
            <summary>
            Name to which registrants paying by check must make checks payable to; REQUIRED if 'CHECK' is selected as a payment option 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.PaypalAccountEmail">
            <summary>
            Email address linked to PayPal account to which payments will be made. REQUIRED if 'PAYPAL' is selected as a payment option 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.RegistrationUrl">
            <summary>
            The URL for the event registration form 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.StartDateString">
            <summary>
            String representation of the event start date, in ISO-8601 format
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.StartDate">
            <summary>
            The event start date
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.StatusString">
            <summary>
            String representation of the event status
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.Status">
            <summary>
            The event status
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.ThemeName">
            <summary>
            The background and color theme for the event invitation, home page, and Registration form; default is Default 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.TimeZoneDescription">
            <summary>
            Specify additional text to help describe the event time zone
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.TimeZoneId">
            <summary>
            Time zone in which the event occurs
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.Title">
            <summary>
            The event title, visible to registrants 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.TotalRegisteredCount">
            <summary>
            Number of event registrants 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.TwitterHashTag">
            <summary>
            The event's Twitter hashtag 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.TypeString">
            <summary>
            String representation of the event type
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.Type">
            <summary>
            The event type
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.UpdatedDateString">
            <summary>
            String representation of the date the event was updated in ISO-8601 format
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.UpdatedDate">
            <summary>
            Date the event was updated
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.EventDetailUrl">
            <summary>
            URI that points to the detailed description of that event
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.Address">
            <summary>
            Address specifying the event location, used to determine event location on map if is_map_displayed set to true
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.Contact">
            <summary>
            The event host's contact information 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.NotificationOptions">
            <summary>
            Define whether or not event notifications are sent to the contact email_address, and which notifications. 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.OnlineMeeting">
            <summary>
            Online meeting details, REQUIRED if is_virtual_event is set to true 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.PaymentAddress">
            <summary>
            Address to which checks will be sent. REQUIRED if CHECK is selected as a payment option 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.TrackInformation">
            <summary>
            Use these settings to define the information displayed on the Event registration page 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.PaymentOptionsArray">
            <summary>
            Specifies the payment options available to registrants 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.IndividualEvent.PaymentOptions">
            <summary>
            Specifies the payment options available to registrants 
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.EventStatus">
            <summary>
            Event status
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.EventStatus.DRAFT">
            <summary>
            Draft.
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.EventStatus.ACTIVE">
            <summary>
            Active.
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.EventStatus.COMPLETE">
            <summary>
            Complete.
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.EventStatus.CANCELLED">
            <summary>
            Cancelled.
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.EventStatus.DELETED">
            <summary>
            Deleted
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.EventType">
            <summary>
            Event type
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.PaymentTypes">
            <summary>
            Payment Types
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.NotificationOptions">
            <summary>
            NotificationOptions class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.NotificationOptions.IsOptedIn">
            <summary>
            Set to true to send event notifications to the contact email_address, false for no notifications; Default is false 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.NotificationOptions.NotificationTypeString">
            <summary>
            String representation of the type of notifications sent to the contact email_address, valid values: SO_REGISTRATION_NOTIFICATION - send notice for each registration (Default) 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.NotificationOptions.NotificationType">
            <summary>
            Specifies the type of notifications sent to the contact email_address
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.NotificationType">
            <summary>
            Specifies the type of notifications sent to the contact email_address
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.OnlineMeeting">
            <summary>
            OnlineMeeting class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.OnlineMeeting.Instructions">
            <summary>
            Online meeting instructions, such as dial in number, password, etc 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.OnlineMeeting.ProviderMeetingId">
            <summary>
            Meeting ID, if any, for the meeting 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.OnlineMeeting.ProviderType">
            <summary>
            Specify the online meeting provider, such as WebEx 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.OnlineMeeting.Url">
            <summary>
            URL for online meeting. REQUIRED if is_virtual_event is set to true
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.Promocode">
            <summary>
            Promocode class
            </summary>
        </member>
        <member name="M:CTCT.Components.EventSpot.Promocode.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.CodeName">
            <summary>
            Name of the promotional code visible to registrants, between 4 - 12 characters, cannot contain spaces or special character (_ is OK); each code_name must be unique 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.CodeTypeString">
            <summary>
             Type of promocode:
             ACCESS - applies to a specific fee with has_restricted_access = true, fee_list must include only a single fee_id. See Event Fees
             DISCOUNT - when set to DISCOUNT, you must specify either a discount_percent or a discount_amount
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.CodeType">
            <summary>
             Type of promocode:
             ACCESS - applies to a specific fee with has_restricted_access = true, fee_list must include only a single fee_id. See Event Fees
             DISCOUNT - when set to DISCOUNT, you must specify either a discount_percent or a discount_amount
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.DiscountAmount">
            <summary>
            Specifies a fixed discount amount, minimum of 0.01, is required when code_type = DISCOUNT, but not using discount_percent
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.DiscountPercent">
            <summary>
            Specifies a discount percentage, from 1% - 100%, is required when code_type = DISCOUNT, but not using discount_amount
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.DiscountScopeString">
            <summary>
             Required when code_type = DISCOUNT;
             FEE_LIST - discount is applied only to those fees listed in the fee_ids array ORDER_TOTAL - discount is applied to the order total
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.DiscountScope">
            <summary>
             Required when code_type = DISCOUNT;
             FEE_LIST - discount is applied only to those fees listed in the fee_ids array ORDER_TOTAL - discount is applied to the order total
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.DiscountTypeString">
            <summary>
            Discount types
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.DiscountType">
            <summary>
            Discount types
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.FeeIds">
            <summary>
            Identifies the fees to which the promocode applies; 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.Id">
            <summary>
            Unique ID for the event promotional code 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.IsPaused">
            <summary>
            When set to true, promocode cannot be redeemed; when false, promocode can be redeemed; default = false
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.QuantityAvailable">
            <summary>
            Number of promocodes available for redemption; -1 = unlimited 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.QuantityTotal">
            <summary>
            Total number of promocodes available for redemption; -1 = unlimited
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.QuantityUsed">
            <summary>
            Number of promocodes that have been redeemed; starts at 0
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.StatusString">
            <summary>
            Status of the promocode
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Promocode.Status">
            <summary>
            Status of the promocode
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.CodeType">
            <summary>
            Type of promocode
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.CodeType.ACCESS">
            <summary>
            applies to a specific fee with has_restricted_access = true, fee_list must include only a single fee_id. See Event Fees
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.CodeType.DISCOUNT">
            <summary>
            when set to DISCOUNT, you must specify either a discount_percent or a discount_amount
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.DiscountScope">
            <summary>
            Discount Scope
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.DiscountScope.FEE_LIST">
            <summary>
             discount is applied only to those fees listed in the fee_ids array
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.DiscountScope.ORDER_TOTAL">
            <summary>
            discount is applied to the order total
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.DiscountType">
            <summary>
            Discount Type
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.DiscountType.PERCENT">
            <summary>
            discount is a percentage specified by discount_percent
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.DiscountType.AMOUNT">
            <summary>
            discount is a fixed amount, specified by discount_amount
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.PromocodeStatus">
            <summary>
            Promocode Status
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.PromocodeStatus.LIVE">
            <summary>
            promocode is available to be redeemed
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.PromocodeStatus.PAUSED">
            <summary>
            promocode is not available for redemption
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.PromocodeStatus.DEPLETED">
            <summary>
            no more promocodes remain, 
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.SaleItem">
            <summary>
            SaleItem class
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.SaleItem.Amount">
            <summary>
            Amount paid
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.SaleItem.Id">
            <summary>
            Fee ID 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.SaleItem.PromoType">
            <summary>
            Amount paid
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.SaleItem.Name">
            <summary>
            Name of registrant or guest 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.SaleItem.Quantity">
            <summary>
            Number of amount required
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.SaleItem.Type">
            <summary>
            Type of fees 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.SaleItem.FeePeriodType">
            <summary>
            Fee period type
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.Section">
            <summary>
            Section class
            </summary>
        </member>
        <member name="M:CTCT.Components.EventSpot.Section.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Section.Label">
            <summary>
            Field label displayed to viewers 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Section.Fields">
            <summary>
             An array of the fields displayed in a section: field_type, name, label, value, values
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Section.FieldTypeString">
            <summary>
            String representation of field type
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.Section.FieldType">
            <summary>
            Type of the value
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.FieldType">
            <summary>
            Field type
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.FieldType.single_value">
            <summary>
            single value
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.FieldType.multiple_values">
            <summary>
            multiple value
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.TrackInformation">
            <summary>
            TrackInformation class
            </summary>
        </member>
        <member name="M:CTCT.Components.EventSpot.TrackInformation.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.EarlyFeeDateString">
            <summary>
            Date on which early fees end, in ISO-8601 format 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.EarlyFeeDate">
            <summary>
            Date on which early fees end
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.GuestDisplayLabel">
            <summary>
            Default = Guest(s); How guests are referred to on the registration form; use your own, or one of the following suggestions are 
            Associate(s), Camper(s), Child(ren), Colleague(s), Employee(s), Friend(s), Guest(s), Member(s), Participant(s), Partner(s), Player(s), Spouse(s), Student(s), Teammate(s), Volunteer(s) 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.GuestLimit">
            <summary>
            Number of guests each registrant can bring, 0 - 100, default = 0 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.IsGuestAnonymousEnabled">
            <summary>
            Default = false; Set to true to display the guest count field on the registration form; if true, is_guest_name_required must be set to false (default)
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.IsGuestNameRequired">
            <summary>
            Default = false. Set to display guest name fields on registration form; if true, then is_guest_anonymous_enabled must be set false (default)
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.IsRegistrationClosedManually">
            <summary>
            Default = false; Manually closes the event registration when set to true, takes precedence over registration_limit_date and registration_limit_count settings 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.IsTicketingLinkDisplayed">
            <summary>
            Default = false; Set to true provide a link for registrants to retrieve an event ticket after they register
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.LateFeeDateString">
            <summary>
            String representation of the date after which late fees apply, in ISO-8601 format 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.LateFeeDate">
            <summary>
            Date after which late fees apply
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.RegistrationLimitCount">
            <summary>
            Specifies the maximum number of registrants for the event 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.RegistrationLimitDateString">
            <summary>
            String representation of the date when event registrations close, in ISO-8601 format 
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.RegistrationLimitDate">
            <summary>
            Date when event registrations close
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.InformationSectionsArray">
            <summary>
             Determines if the Who (CONTACT), When (TIME), or Where (LOCATION) information is shown on the Event page. Default settings are CONTACT, TIME, and LOCATION 
             valid values are: CONTACT - displays the event contact informationTIME - displays the event date and time
             LOCATION - displays the event location
            </summary>
        </member>
        <member name="P:CTCT.Components.EventSpot.TrackInformation.InformationSections">
            <summary>
             Determines if the Who (CONTACT), When (TIME), or Where (LOCATION) information is shown on the Event page. Default settings are CONTACT, TIME, and LOCATION 
             valid values are: CONTACT - displays the event contact informationTIME - displays the event date and time
             LOCATION - displays the event location
            </summary>
        </member>
        <member name="T:CTCT.Components.EventSpot.InformationSections">
            <summary>
            InformationSection of TrackTnformation
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.InformationSections.CONTACT">
            <summary>
            displays the event contact information
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.InformationSections.TIME">
            <summary>
            displays the event date and time
            </summary>
        </member>
        <member name="F:CTCT.Components.EventSpot.InformationSections.LOCATION">
            <summary>
            displays the event location
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.BaseLibrary">
            <summary>
            Base class for MyLibrary
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.BaseLibrary.Id">
            <summary>
            Gets or sets the id
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.BaseLibrary.CreatedDate">
            <summary>
            Gets or sets the created date
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.BaseLibrary.ModifiedDate">
            <summary>
            Gets or sets the modified date
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.BaseLibrary.Name">
            <summary>
            Gets or sets the name of the library item
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.FileMoveResult">
            <summary>
            Represents a single File Move Result
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.FileMoveResult.Id">
            <summary>
            Gets or sets the id
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.FileMoveResult.Uri">
            <summary>
            Gets or sets the uri
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.FileUploadStatus">
            <summary>
            Represents a single Upload Status 
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.FileUploadStatus.FileId">
            <summary>
            Gets or setd the file id
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.FileUploadStatus.Description">
            <summary>
            Gets or sets the description
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.FileUploadStatus.StatusString">
            <summary>
            Status, string representation.
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.FileUploadStatus.Status">
            <summary>
            Gets or sets the file status
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.MyLibraryFile">
            <summary>
            Represents a single MyLibrary File
            </summary>
        </member>
        <member name="M:CTCT.Components.MyLibrary.MyLibraryFile.#ctor">
            <summary>
            Class contructor
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.Description">
            <summary>
            Gets or sets the description of the file
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.FileTypeString">
            <summary>
            File type, string representation.
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.FileType">
            <summary>
            Gets or sets the source of the original file
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.Folder">
            <summary>
            Gets or sets the name of the folder the file is in
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.FolderId">
            <summary>
            Gets or sets the id of the folder the file is in
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.Height">
            <summary>
            Gets or sets the height in pixels of the image 
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.IsImage">
            <summary>
            Gets or sets whether the file is an image (true) or not (false)
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.Size">
            <summary>
            Gets or sets  the size of the file (in bytes) 
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.SourceString">
            <summary>
            Source, string representation.
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.Source">
            <summary>
            Gets or sets the source of the original file
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.StatusString">
            <summary>
            Status, string representation.
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.Status">
            <summary>
            Gets or sets the file status
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.Url">
            <summary>
            Gets or sets the url of the file
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.Width">
            <summary>
            Gets or sets the width (in pixels) of the image
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFile.Thumbnail">
            <summary>
            Gets or sets the thumbnail image of the file
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.FileType">
            <summary>
            File type enum
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileType.JPEG">
            <summary>
            JPEG file type
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileType.JPG">
            <summary>
            JPG file type
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileType.GIF">
            <summary>
            GIF file type
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileType.PDF">
            <summary>
            PDF file type
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileType.PNG">
            <summary>
            PNG file type
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.FileSource">
            <summary>
            File source enum
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileSource.MyComputer">
            <summary>
            Computer source
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileSource.StockImage">
            <summary>
            StockImage source
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileSource.Facebook">
            <summary>
            Facebook source - MyLibrary Plus customers only
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileSource.Instagram">
            <summary>
            Istagram source - MyLibrary Plus customers only
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileSource.Shutterstock">
            <summary>
            Shutterstock source
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileSource.Mobile">
            <summary>
            Mobile source
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.FileStatus">
            <summary>
            File status enum
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileStatus.Active">
            <summary>
            File is available for use
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileStatus.Processing">
            <summary>
            File is in the process of being uploaded to account
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileStatus.Uploaded">
            <summary>
            File has been uploaded, but not yet available for use
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileStatus.VirusFound">
            <summary>
            Virus scan during upload detected a virus, upload cancelled
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileStatus.Failed">
            <summary>
            File upload failed
            </summary>
        </member>
        <member name="F:CTCT.Components.MyLibrary.FileStatus.Deleted">
            <summary>
            File was deleted from MyLibrary
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.MyLibraryFolder">
            <summary>
            Represents a single MyLibrary Folder
            </summary>
        </member>
        <member name="M:CTCT.Components.MyLibrary.MyLibraryFolder.#ctor">
            <summary>
            Class constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFolder.ItemCount">
            <summary>
            Gets or sets the number of files in the library folder
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFolder.Level">
            <summary>
            Gets or sets the level
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFolder.ParentId">
            <summary>
            Gets or sets the parent folder id
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryFolder.Children">
            <summary>
            Gets or sets the list of child or grandchild folders
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.MyLibraryInfo">
            <summary>
            Represents a MyLibrary Info data class
            </summary>
        </member>
        <member name="M:CTCT.Components.MyLibrary.MyLibraryInfo.#ctor">
            <summary>
            Class constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryInfo.ImageRoot">
            <summary>
            Gets or sets the image root
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryInfo.MaxFreeFileNum">
            <summary>
            Gets or sets the maximum number of free MyLibrary files
            If value = 0, refer to max_premium_space_limit for capacity
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryInfo.MaxPremiumSpaceLimit">
            <summary>
            Gets or sets the total amount of MyLibrary Plus storage space (in bytes)
            If value = 0, refer to max_free_file_num for capacity
            If value = -1, the account has unlimited storage
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryInfo.MaxUploadSizeLimit">
            <summary>
            Gets or sets the maximum file size (in bytes) that can be uploaded
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.MyLibraryInfo.UsageSummary">
            <summary>
            Gets or sets the usage data
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.Thumbnail">
            <summary>
            Represents a single Thumbnail in MyLibrary File class
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.Thumbnail.Url">
            <summary>
            Gets or sets the URL referencing the thumbnail of the file
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.Thumbnail.Height">
            <summary>
            Gets or sets the height in pixels
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.Thumbnail.Width">
            <summary>
            Gets or sets the width in pixels
            </summary>
        </member>
        <member name="T:CTCT.Components.MyLibrary.UsageSummary">
            <summary>
            Represents a Usage Summary in MyLibrary Info class
            </summary>
        </member>
        <member name="M:CTCT.Components.MyLibrary.UsageSummary.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.UsageSummary.DocumentBytesUsed">
            <summary>
            Gets or sets the total amount of storage space currently being consumed by document files (in bytes)
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.UsageSummary.DocumentCount">
            <summary>
            Gets or sets the total number of documents stored
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.UsageSummary.FileCount">
            <summary>
            Gets or sets the total number of files stored
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.UsageSummary.FolderCount">
            <summary>
            Gets or sets the total number of folders
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.UsageSummary.FreeFilesRemaining">
            <summary>
            Gets or sets the total number of free files
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.UsageSummary.ImageBytesUsed">
            <summary>
            Gets or sets the total amount of storage space being consumed by image files (in bytes)
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.UsageSummary.ImageCount">
            <summary>
            Gets or sets the total number of images stored
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.UsageSummary.TotalBytesRemaining">
            <summary>
            Gets or sets the total amount of storage space available (in bytes)
            </summary>
        </member>
        <member name="P:CTCT.Components.MyLibrary.UsageSummary.TotalBytesUsed">
            <summary>
            Gets or sets the total amount of storage space being used (in bytes)
            </summary>
        </member>
        <member name="T:CTCT.Components.ResultSet`1">
            <summary>
            Container for a get on a collection, such as Contacts, Campaigns, or TrackingData.
            </summary>
            <typeparam name="T">An object derived from Component class.</typeparam>
        </member>
        <member name="M:CTCT.Components.ResultSet`1.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.ResultSet`1.Results">
            <summary>
            Gets or sets the array of result objects returned.
            </summary>
        </member>
        <member name="P:CTCT.Components.ResultSet`1.Meta">
            <summary>
            Gets or sets the next link.
            </summary>
        </member>
        <member name="T:CTCT.Components.Meta">
            <summary>
            Meta class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Meta.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Meta.Pagination">
            <summary>
            Gets or sets the pagination link.
            </summary>
        </member>
        <member name="T:CTCT.Components.Pagination">
            <summary>
            Pagination class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Pagination.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="M:CTCT.Components.Pagination.GetNextUrl">
            <summary>
            Format the URL for the next page call.
            </summary>
            <returns>Returns the URL for the next page call.</returns>
        </member>
        <member name="P:CTCT.Components.Pagination.Next">
            <summary>
            Gets or sets the next link.
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.BaseActivity">
            <summary>
            Base class for activities.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.BaseActivity.ActivityType">
            <summary>
            Gets or sets the activity type.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.BaseActivity.CampaignId">
            <summary>
            Gets or sets the campaign id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.BaseActivity.ContactId">
            <summary>
            Gets or sets the contact id.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.BaseActivity.EmailAddress">
            <summary>
            Gets or sets the email address.
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.ActivityType">
            <summary>
            Activity type enumeration.
            </summary>
        </member>
        <member name="F:CTCT.Components.Tracking.ActivityType.Bounce">
            <summary>
            Bounce activity.
            </summary>
        </member>
        <member name="F:CTCT.Components.Tracking.ActivityType.Click">
            <summary>
            Click activity.
            </summary>
        </member>
        <member name="F:CTCT.Components.Tracking.ActivityType.Forward">
            <summary>
            Forward activity.
            </summary>
        </member>
        <member name="F:CTCT.Components.Tracking.ActivityType.Open">
            <summary>
            Open activity.
            </summary>
        </member>
        <member name="F:CTCT.Components.Tracking.ActivityType.OptOut">
            <summary>
            OptOut activity.
            </summary>
        </member>
        <member name="F:CTCT.Components.Tracking.ActivityType.Send">
            <summary>
            Send activity.
            </summary>
        </member>
        <member name="F:CTCT.Components.Tracking.ActivityType.EMAIL_OPEN">
            <summary>
            Email open
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.BounceActivity">
            <summary>
            Represents a single Bounce Activity class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Tracking.BounceActivity.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.BounceActivity.BounceCode">
            <summary>
            Gets or sets the bounce code.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.BounceActivity.BounceDescription">
            <summary>
            Gets or sets the bounce description.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.BounceActivity.BounceMessage">
            <summary>
            Gets or sets the bounce message.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.BounceActivity.BounceDate">
            <summary>
            Gets or sets the bounce date.
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.ClickActivity">
            <summary>
            Represents a Click Activity class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Tracking.ClickActivity.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ClickActivity.LinkId">
            <summary>
            Gets or sets the link identification.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ClickActivity.ClickDate">
            <summary>
            Gets or sets the click date.
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.ContactActivity">
            <summary>
            Represents a Contact Activity class.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.OpenDate">
            <summary>
            Gets or sets the open date.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.UnsubscribeDate">
            <summary>
            Gets or sets the unsubscribe date
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.SendDate">
            <summary>
            Gets or sets the send date
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.ForwardDate">
            <summary>
            Gets or sets the forward date
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.Opens">
            <summary>
            Gets or sets the opens
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.LinkUri">
            <summary>
            Gets or sets the link uri
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.LinkId">
            <summary>
            Gets or sets the link id
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.Bounces">
            <summary>
            Gets or sets the bounces
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.UnsubscribeReason">
            <summary>
            Gets or sets the unsubscribe reason
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.Forwards">
            <summary>
            Gets or sets the forwards
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.BounceDescription">
            <summary>
            Gets or sets the bounce description
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.UnsubscribeSource">
            <summary>
            Gets or sets the unsubscribe source
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.BounceMessage">
            <summary>
            Gets or sets the bounce message
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.BounceCode">
            <summary>
            Gets or sets the bounce code
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.Clicks">
            <summary>
            Gets or sets the clicks
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.BounceDate">
            <summary>
            Gets or sets the bounce date
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ContactActivity.ClickDate">
            <summary>
            Gets or sets the click date
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.ForwardActivity">
            <summary>
            Represents a Forward Activity class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Tracking.ForwardActivity.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.ForwardActivity.ForwardDate">
            <summary>
            Gets or sets the forward date.
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.OpenActivity">
            <summary>
            Represents an Open Activity class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Tracking.OpenActivity.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.OpenActivity.OpenDate">
            <summary>
            Gets or sets the open date.
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.OptOutActivity">
            <summary>
            Represents a single OptOut Activity class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Tracking.OptOutActivity.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.OptOutActivity.UnsubscribeDate">
            <summary>
            Gets or sets the unsubscribe date.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.OptOutActivity.UnsubscribeSource">
            <summary>
            Gets or sets the unsubscribe source.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.OptOutActivity.UnsubscribeReason">
            <summary>
            Gets or sets the unsubscribe reason.
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.SendActivity">
            <summary>
            Represents a Sent Activity class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Tracking.SendActivity.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.SendActivity.SendDate">
            <summary>
            Gets or sets the send date.
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.TrackingActivity">
            <summary>
            Class to wrap a result set of individual activities (ie: OpensActivity, SendActivity).
            </summary>
        </member>
        <member name="M:CTCT.Components.Tracking.TrackingActivity.#ctor">
            <summary>
            Class constructor
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.TrackingActivity.Results">
            <summary>
            Gets or sets the list of activities.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.TrackingActivity.Next">
            <summary>
            Gets or sets the pagination array returned from a tracking endpoint.
            </summary>
        </member>
        <member name="T:CTCT.Components.Tracking.TrackingSummary">
            <summary>
            Represents a Tracking Summary class.
            </summary>
        </member>
        <member name="M:CTCT.Components.Tracking.TrackingSummary.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.TrackingSummary.Sends">
            <summary>
            Gets or sets the number of sends.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.TrackingSummary.Opens">
            <summary>
            Gets or sets the number of opens.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.TrackingSummary.Clicks">
            <summary>
            Gets or sets the number of clicks.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.TrackingSummary.Forwards">
            <summary>
            Gets or sets the number of forwards.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.TrackingSummary.Unsubscribes">
            <summary>
            Gets or sets the number of unsubscribes.
            </summary>
        </member>
        <member name="P:CTCT.Components.Tracking.TrackingSummary.Bounces">
            <summary>
            Gets or sets the number of bounces.
            </summary>
        </member>
        <member name="T:CTCT.ConstantContact">
             <summary>
             Main class meant to be used by users to access Constant Contact API functionality.
             <example>
             ASPX page:
             <code>
             <![CDATA[
              <%@Page Title="Home Page" Language="C#" MasterPageFile="~/Site.master" AutoEventWireup="true"
                     CodeBehind="Default.aspx.cs" Inherits="WebApplication1._Default"%>
              ...
                 <asp:TextBox ID="tbxEmail" runat="server"></asp:TextBox>
                 <asp:Button ID="btnJoin" runat="server" Text="Join" onclick="btnJoin_Click" />
                 <asp:Label ID="lblMessage" runat="server" Text=""></asp:Label>
              ...
             ]]>
             </code>
             </example>
             <example>
             Code behind:
             <code>
             <![CDATA[
             public partial class _Default : System.Web.UI.Page
             {
                protected void Page_Load(object sender, EventArgs e)
                {
                     ...
                }
            
                protected void btnJoin_Click(object sender, EventArgs e)
                {
                    try
                    {
                        Contact contact = new Contact();
                        // Don't care about the id value
                        contact.Id = 1;
                        contact.EmailAddresses.Add(new EmailAddress() {
                             EmailAddr = tbxEmail.Text,
                             ConfirmStatus = ConfirmStatus.NoConfirmationRequired,
                             Status = Status.Active });
                        contact.Lists.Add(new ContactList() {
                             Id = 1,
                             Status = Status.Active });
            
                        ConstantContact cc = new ConstantContact();
                        cc.AddContact(contact);
                        lblMessage.Text = "You have been added to my mailing list!";
                    }
                    catch (Exception ex) { lblMessage.Text = ex.ToString(); }
                }
             }
             ]]>
             </code>
             </example>
             <example>
             Web.config entries:
             <code>
             <![CDATA[
             ...
             <appSettings>
                 <add key="APIKey" value="APIKey"/>
                 <add key="Password" value="password"/>
                 <add key="Username" value="username"/>
                 <add key="RedirectURL" value="http://somedomain"/>
             </appSettings>
             ...
             ]]>
             </code>
             </example>
             </summary>
        </member>
        <member name="M:CTCT.ConstantContact.#ctor(System.String,System.String)">
            <summary>
            Creates a new ConstantContact object using provided apiKey and accessToken parameters
            </summary>
            <param name="apiKey">APIKey</param>
            <param name="accessToken">access token</param>
        </member>
        <member name="M:CTCT.ConstantContact.GetContacts(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},System.Nullable{CTCT.Components.Contacts.ContactStatus})">
            <summary>
            Get a set of contacts.
            </summary>
            <param name="email">Match the exact email address.</param>
            <param name="limit">Limit the number of returned values, default 500.</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <param name="status">Match the exact contact status</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContacts(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get an array of contacts.
            </summary>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContacts(System.Nullable{System.DateTime})">
            <summary>
            Get a set of contacts.
            </summary>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContact(System.String)">
            <summary>
            Get an individual contact.
            </summary>
            <param name="contactId">Id of the contact to retrieve</param>
            <returns>Returns a contact.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.AddContact(CTCT.Components.Contacts.Contact,System.Boolean)">
            <summary>
            Add a new contact to an account.
            </summary>
            <param name="contact">Contact to add.</param>
            <param name="actionByVisitor">Set to true if action by visitor.</param>
            <returns>Returns the newly created contact.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteContact(CTCT.Components.Contacts.Contact)">
            <summary>
            Sets an individual contact to 'Unsubscribed' status.
            </summary>
            <param name="contact">Contact object.</param>
            <returns>Returns true if operation succeeded.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteContact(System.String)">
            <summary>
            Sets an individual contact to 'Unsubscribed' status.
            </summary>
            <param name="contactId">Contact id.</param>
            <returns>Returns true if operation succeeded.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteContactFromLists(CTCT.Components.Contacts.Contact)">
            <summary>
            Delete a contact from all contact lists.
            </summary>
            <param name="contact">Contact object.</param>
            <returns>Returns true if operation succeeded.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteContactFromLists(System.String)">
            <summary>
            Delete a contact from all contact lists. Sets them to 'Removed' status.
            </summary>
            <param name="contactId">Contact id.</param>
            <returns>Returns true if operation succeeded.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteContactFromList(CTCT.Components.Contacts.Contact,CTCT.Components.Contacts.ContactList)">
            <summary>
            Delete a contact from all contact lists.
            </summary>
            <param name="contact">Contact object.</param>
            <param name="list">List object.</param>
            <returns>Returns true if operation succeeded.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteContactFromList(System.String,System.String)">
            <summary>
            Delete a contact from all contact lists.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="listId">List id.</param>
            <returns>Returns true if operation succeeded.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.UpdateContact(CTCT.Components.Contacts.Contact,System.Boolean)">
            <summary>
            Update an individual contact.
            </summary>
            <param name="contact">Contact to update.</param>
            <param name="actionByVisitor">Set to true if action by visitor.</param>
            <returns>Returns the updated contact.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetLists(System.Nullable{System.DateTime})">
            <summary>
            Get lists.
            </summary>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <returns>Returns the list of lists where contact belong to.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetList(System.String)">
            <summary>
            Get an individual list.
            </summary>
            <param name="listId">Id of the list to retrieve</param>
            <returns>Returns contact list.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.UpdateList(CTCT.Components.Contacts.ContactList)">
            <summary>
            Update a Contact List.
            </summary>
            <param name="list">ContactList to be updated</param>
            <returns>Contact list</returns>
        </member>
        <member name="M:CTCT.ConstantContact.AddList(CTCT.Components.Contacts.ContactList)">
            <summary>
            Add a new list to an account.
            </summary>
            <param name="list">List to add.</param>
            <returns>Returns the newly created list.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteList(System.String)">
            <summary>
            Delete a Contact List.
            </summary>
            <param name="listId">List id.</param>
            <returns>return true if list was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactsFromList(CTCT.Components.Contacts.ContactList,System.Nullable{System.DateTime})">
            <summary>
            Get contact that belong to a specific list.
            </summary>
            <param name="list">Contact list object.</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <returns>Returns the list of contacts.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactsFromList(CTCT.Components.Contacts.ContactList,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get contact that belong to a specific list.
            </summary>
            <param name="list">Contact list object.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <returns>Returns the list of contacts.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactsFromList(System.String,System.Nullable{System.DateTime})">
            <summary>
            Get contact that belong to a specific list.
            </summary>
            <param name="listId">Contact list id.</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <returns>Returns a list of contacts.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactsFromList(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get contact that belong to a specific list.
            </summary>
            <param name="listId">Contact list id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <returns>Returns a list of contacts.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactsFromList(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get contact that belong to a specific list.
            </summary>
            <param name="listId">Contact list id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            /// <param name="pag">Pagination object.</param>
            <returns>Returns a list of contacts.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetActivities">
            <summary>
            Get a list of activities.
            </summary>
            <returns>Returns the list of activities.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetActivity(System.String)">
            <summary>
            Get an activity.
            </summary>
            <param name="activityId">The activity identification.</param>
            <returns>Returns the activity identified by its id.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.CreateAddContactsActivity(CTCT.Components.Activities.AddContacts)">
            <summary>
            Create an Add Contacts Activity.
            </summary>
            <param name="addContacts">AddContacts object.</param>
            <returns>Returns an Activity object.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.AddClearListsActivity(System.Collections.Generic.IList{System.String})">
            <summary>
            Create a Clear Lists Activity.
            </summary>
            <param name="lists">Array of list id's to be cleared.</param>
            <returns>Returns an Activity object.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.AddExportContactsActivity(CTCT.Components.Activities.ExportContacts)">
            <summary>
            Create an Export Contacts Activity.
            </summary>
            <param name="exportContacts">Export contacts object.</param>
            <returns>Returns an Activity object.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.AddRemoveContactsFromListsActivity(System.Collections.Generic.IList{System.String},System.Collections.Generic.IList{System.String})">
            <summary>
            Create a Remove Contacts From Lists Activity.
            </summary>
            <param name="emailAddresses">List of email addresses.</param>
            <param name="lists">List of id's.</param>
            <returns>Returns an Activity object.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.AddContactsMultipartActivity(System.String,System.Byte[],System.Collections.Generic.IList{System.String})">
            <summary>
            Create an Add Contacts Multipart Activity
            </summary>
            <param name="fileName">The name of the file</param>
            <param name="fileContent">The contents of the file</param>
            <param name="lists">List of contact list Ids to add the contacts to</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.RemoveContactsMultipartActivity(System.String,System.Byte[],System.Collections.Generic.IList{System.String})">
            <summary>
            Create a Remove Contacts Multipart Activity
            </summary>
            <param name="fileName">The name of the file</param>
            <param name="fileContent">The contents of the file</param>
            <param name="lists">List of contact list Ids to add to remove the contacts to</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.AddSchedule(System.String,CTCT.Components.EmailCampaigns.Schedule)">
            <summary>
            Create a new schedule for a campaign.
            </summary>
            <param name="campaignId">Campaign id to be scheduled.</param>
            <param name="schedule">Schedule to be created.</param>
            <returns>Returns the scheduled object.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetSchedules(System.String)">
            <summary>
            Get a list of schedules for a campaign.
            </summary>
            <param name="campaignId">Campaign id to be scheduled.</param>
            <returns>Returns the list of schedules for specified campaign.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetSchedule(System.String,System.String)">
            <summary>
            Get a specific schedule for a campaign.
            </summary>
            <param name="campaignId">Campaign id to be get a schedule for.</param>
            <param name="scheduleId">Schedule id to retrieve.</param>
            <returns>Returns the schedule object for the requested campaign.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.UpdateSchedule(System.String,CTCT.Components.EmailCampaigns.Schedule)">
            <summary>
            Update a specific schedule for a campaign.
            </summary>
            <param name="campaignId">Campaign id to be scheduled.</param>
            <param name="schedule">Schedule to retrieve.</param>
            <returns>Returns the updated schedule object.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteSchedule(System.String,System.String)">
            <summary>
            Get a specific schedule for a campaign.
            </summary>
            <param name="campaignId">Campaign id.</param>
            <param name="scheduleId">Schedule id to delete.</param>
            <returns>Returns true if schedule was successfully deleted.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.SendTest(System.String,CTCT.Components.EmailCampaigns.TestSend)">
            <summary>
            Send a test send of a campaign.
            </summary>
            <param name="campaignId">Id of campaign to send test of.</param>
            <param name="testSend">Test send details.</param>
            <returns>Returns the sent object.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingBounces(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get a result set of bounces for a given campaign.
            </summary>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link BounceActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingBounces(CTCT.Components.Pagination)">
            <summary>
            Get a result set of bounces for a given campaign.
            </summary>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link BounceActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingClicks(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a given campaign.
            </summary>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingClicks(System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a specific link in a campaign.
            </summary>
            <param name="campaignId">Campaign id.</param>
            <param name="linkId">Specifies the link in the email campaign to retrieve click data for.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingClicks(CTCT.Components.Pagination)">
            <summary>
            Get clicks for a specific link in a campaign.
            </summary>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetClicks(CTCT.Components.Pagination)">
            <summary>
            Get clicks for a given campaign.
            </summary>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingForwards(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get forwards for a given campaign.
            </summary>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingForwards(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get forwards for a given campaign.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingOpens(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opens for a given campaign.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingOpens(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opens for a given campaign.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingSends(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get sends for a given campaign.
            </summary>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link SendActivity</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingSends(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get sends for a given campaign.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link SendActivity</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingOptOuts(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opt outs for a given campaign.
            </summary>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingOptOuts(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opt outs for a given campaign.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaignTrackingSummary(System.String)">
            <summary>
            Get a summary of reporting data for a given campaign.
            </summary>
            <param name="campaignId">Campaign id.</param>
            <returns>Tracking summary.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingActivities(System.String)">
            <summary>
            Get all activities for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <returns>ResultSet containing a results array of @link ContactActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingActivities(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get all activities for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ContactActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingActivities(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get all activities for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for activities created since the supplied date in the collection</param>	 
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ContactActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingEmailCampaignActivities(System.String)">
            <summary>
            Get activities by email campaign for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <returns>ResultSet containing a results array of @link TrackingSummary.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingBounces(System.String)">
            <summary>
            Get bounces for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <returns>ResultSet containing a results array of @link BounceActivity</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingBounces(System.String,System.Nullable{System.Int32})">
            <summary>
            Get bounces for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <returns>ResultSet containing a results array of @link BounceActivity</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingBounces(CTCT.Components.Pagination)">
            <summary>
            Get bounces for a given contact.
            </summary>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link BounceActivity</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingClicks(System.String,System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingClicks(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingClicks(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get clicks for a given contact.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ClickActivity</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingForwards(System.String,System.Nullable{System.DateTime})">
            <summary>
            Get forwards for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingForwards(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get forwards for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingForwards(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get forwards for a given contact.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingOpens(System.String,System.Nullable{System.DateTime})">
            <summary>
            Get opens for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingOpens(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opens for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingOpens(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opens for a given contact.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingSends(System.String,System.Nullable{System.DateTime})">
            <summary>
            Get sends for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link SendActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingSends(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get sends for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link SendActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingSends(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get sends for a given contact.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link SendActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingOptOuts(System.String,System.Nullable{System.DateTime})">
            <summary>
            Get opt outs for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingOptOuts(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opt outs for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingOptOuts(System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opt outs for a given contact.
            </summary>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetContactTrackingSummary(System.String)">
            <summary>
            Get a summary of reporting data for a given contact.
            </summary>
            <param name="contactId">Contact id.</param>
            <returns>Tracking summary.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaigns(CTCT.Components.Pagination)">
            <summary>
            Get a set of campaigns.
            </summary>
            <param name="pagination">Select the next page of campaigns from a pagination</param>
            <returns>Returns a list of campaigns.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaigns(System.Nullable{System.DateTime})">
            <summary>
            Get a set of campaigns.
            </summary>
            <param name="modifiedSince">limit campaigns to campaigns modified since the supplied date</param>
            <returns>Returns a list of campaigns.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaigns(CTCT.Components.EmailCampaigns.CampaignStatus,System.Nullable{System.DateTime})">
            <summary>
            Get a set of campaigns.
            </summary>
            <param name="status">Returns list of email campaigns with specified status.</param>
            <param name="modifiedSince">limit campaigns to campaigns modified since the supplied date</param>
            <returns>Returns a list of campaigns.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaigns(System.Nullable{CTCT.Components.EmailCampaigns.CampaignStatus},System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get a set of campaigns.
            </summary>
            <param name="status">Returns list of email campaigns with specified status.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit campaigns to campaigns modified since the supplied date</param>
            <param name="pagination">Pagination object supplied by a previous call to GetCampaigns when another page is present</param>
            <returns>Returns a list of campaigns.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetCampaign(System.String)">
            <summary>
            Get campaign details for a specific campaign.
            </summary>
            <param name="campaignId">Campaign id.</param>
            <returns>Returns a campaign.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.AddCampaign(CTCT.Components.EmailCampaigns.EmailCampaign)">
            <summary>
            Create a new campaign.
            </summary>
            <param name="campaign">Campign to be created</param>
            <returns>Returns a campaign.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteCampaign(System.String)">
            <summary>
            Delete an email campaign.
            </summary>
            <param name="campaignId">Valid campaign id.</param>
            <returns>Returns true if successful.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.UpdateCampaign(CTCT.Components.EmailCampaigns.EmailCampaign)">
            <summary>
            Update a specific email campaign.
            </summary>
            <param name="campaign">Campaign to be updated.</param>
            <returns>Returns a campaign.</returns>
            <exception cref="T:CTCT.Exceptions.IllegalArgumentException">IllegalArgumentException</exception>
        </member>
        <member name="M:CTCT.ConstantContact.GetVerifiedEmailAddress">
            <summary>
            Retrieve a list of all the account owner's email addresses
            </summary>
            <returns>list of all verified account owner's email addresses</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetAccountSummaryInformation">
            <summary>
            Get account summary information
            </summary>
            <returns>An AccountSummaryInformation object</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PutAccountSummaryInformation(CTCT.Components.AccountService.AccountSummaryInformation)">
            <summary>
            Updates account summary information
            </summary>
            <param name="accountSumaryInfo">An AccountSummaryInformation object</param>
            <returns>An AccountSummaryInformation object</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryInfo">
            <summary>
            Get MyLibrary usage information
            </summary>
            <returns>Returns a MyLibraryInfo object</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFolders">
            <summary>
            Get all existing MyLibrary folders
            </summary>
            <returns>Returns a collection of MyLibraryFolder objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFolders(System.Nullable{CTCT.Services.FoldersSortBy})">
            <summary>
            Get all existing MyLibrary folders
            </summary>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <returns>Returns a collection of MyLibraryFolder objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFolders(System.Nullable{CTCT.Services.FoldersSortBy},System.Nullable{System.Int32})">
            <summary>
            Get all existing MyLibrary folders
            </summary>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <returns>Returns a collection of MyLibraryFolder objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFolders(System.Nullable{CTCT.Services.FoldersSortBy},System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get all existing MyLibrary folders
            </summary>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFolder objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.AddLibraryFolder(CTCT.Components.MyLibrary.MyLibraryFolder)">
            <summary>
            Add new folder to MyLibrary
            </summary>
            <param name="folder">Folder to be added (with name and parent id)</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFolder(System.String)">
            <summary>
            Get a folder by Id
            </summary>
            <param name="folderId">The id of the folder</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.UpdateLibraryFolder(CTCT.Components.MyLibrary.MyLibraryFolder)">
            <summary>
            Update name and parent_id for a specific folder
            </summary>
            <param name="folder">Folder to be added (with name and parent id)</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.UpdateLibraryFolder(CTCT.Components.MyLibrary.MyLibraryFolder,System.Nullable{System.Boolean})">
            <summary>
            Update name and parent_id for a specific folder
            </summary>
            <param name="folder">Folder to be added (with name and parent id)</param>
            <param name="includePayload">Determines if update's folder JSON payload is returned</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteLibraryFolder(CTCT.Components.MyLibrary.MyLibraryFolder)">
            <summary>
            Delete a specific folder
            </summary>
            <param name="folder">The folder to be deleted</param>
            <returns>Returns true if folder was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteLibraryFolder(System.String)">
            <summary>
            Delete a specific folder
            </summary>
            <param name="folderId">The id of the folder</param>
            <returns>Returns true if folder was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryTrashFiles">
            <summary>
            Get files from Trash folder
            </summary>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryTrashFiles(System.Nullable{CTCT.Services.FileTypes})">
            <summary>
            Get files from Trash folder
            </summary>
            <param name="type">The type of the files to retrieve</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryTrashFiles(System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.TrashSortBy})">
            <summary>
            Get files from Trash folder
            </summary>
            <param name="type">The type of the files to retrieve</param>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryTrashFiles(System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.TrashSortBy},System.Nullable{System.Int32})">
            <summary>
            Get files from Trash folder
            </summary>
            <param name="type">The type of the files to retrieve</param>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryTrashFiles(System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.TrashSortBy},System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get files from Trash folder
            </summary>
            <param name="type">The type of the files to retrieve</param>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteLibraryTrashFiles">
            <summary>
            Delete files in Trash folder
            </summary>
            <returns>Returns true if files were deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFiles">
            <summary>
            Get files
            </summary>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFiles(System.Nullable{CTCT.Services.FileTypes})">
            <summary>
            Get files
            </summary>
            <param name="type">The type of the files to retrieve</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFiles(System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.FilesSources})">
            <summary>
            Get files
            </summary>
            <param name="type">The type of the files to retrieve</param>
            <param name="source">Specifies to retrieve files from a particular source</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFiles(System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.FilesSources},System.Nullable{System.Int32})">
            <summary>
            Get files
            </summary>
            <param name="type">The type of the files to retrieve</param>
            <param name="source">Specifies to retrieve files from a particular source</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFiles(System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.FilesSources},System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get files
            </summary>
            <param name="type">The type of the files to retrieve</param>
            <param name="source">Specifies to retrieve files from a particular source</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFilesByFolder(System.String)">
            <summary>
            Get files from a specific folder
            </summary>
            <param name="folderId">The id of the folder from which to retrieve files</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFilesByFolder(System.String,System.Nullable{System.Int32})">
            <summary>
            Get files from a specific folder
            </summary>
            <param name="folderId">The id of the folder from which to retrieve files</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFilesByFolder(System.String,System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get files from a specific folder
            </summary>
            <param name="folderId">The id of the folder from which to retrieve files</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFile(System.String)">
            <summary>
            Get file after id
            </summary>
            <param name="fileId">The id of the file</param>
            <returns>Returns a MyLibraryFile object.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.UpdateLibraryFile(CTCT.Components.MyLibrary.MyLibraryFile)">
            <summary>
            Update a specific file
            </summary>
            <param name="file">File to be updated</param>
            <returns>Returns a MyLibraryFile object.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.UpdateLibraryFile(CTCT.Components.MyLibrary.MyLibraryFile,System.Nullable{System.Boolean})">
            <summary>
            Update a specific file
            </summary>
            <param name="file">File to be updated</param>
            <param name="includePayload">Determines if update's folder JSON payload is returned</param>
            <returns>Returns a MyLibraryFile object.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteLibraryFile(CTCT.Components.MyLibrary.MyLibraryFile)">
            <summary>
            Delete a specific file
            </summary>
            <param name="file">The file to be deleted</param>
            <returns>Returns true if folder was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteLibraryFile(System.String)">
            <summary>
            Delete a specific file
            </summary>
            <param name="fileId">The id of the file</param>
            <returns>Returns true if folder was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetLibraryFileUploadStatus(System.String)">
            <summary>
            Get status for an upload file
            </summary>
            <param name="fileId">The id of the file</param>
            <returns>Returns a list of FileUploadStatus objects</returns>
        </member>
        <member name="M:CTCT.ConstantContact.MoveLibraryFile(System.String,System.Collections.Generic.IList{System.String})">
            <summary>
            Move files to a different folder
            </summary>
            <param name="folderId">The id of the folder</param>
            <param name="fileIds">List of comma separated file ids</param>
            <returns>Returns a list of FileMoveResult objects.</returns>
        </member>
        <member name="M:CTCT.ConstantContact.AddLibraryFilesMultipart(System.String,CTCT.Components.MyLibrary.FileType,System.String,System.String,CTCT.Components.MyLibrary.FileSource,System.Byte[])">
            <summary>
            Add files using the multipart content-type
            </summary>
            <param name="fileName">The file name and extension</param>
            <param name="fileType">The file type</param>
            <param name="folderId">The id of the folder</param>
            <param name="description">The description of the file</param>
            <param name="source">The source of the original file</param>
            <param name="data">The data contained in the file being uploaded</param>
            <returns>Returns the file Id associated with the uploaded file</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetAllEventSpots(System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            View all existing events
            </summary>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 50</param>
            <param name="pag">Pagination object</param>
            <returns>ResultSet containing a results array of IndividualEvents</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetEventSpot(System.String)">
            <summary>
            Retrieve an event specified by the event_id
            </summary>
            <param name="eventId">Event id</param>
            <returns>The event</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PostEventSpot(CTCT.Components.EventSpot.IndividualEvent)">
            <summary>
            Publish an event
            </summary>
            <param name="eventSpot">The event to publish</param>
            <returns>The published event</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PutEventSpot(System.String,CTCT.Components.EventSpot.IndividualEvent)">
            <summary>
            Update an event
            </summary>
            <param name="eventId">Event id to be updated</param>
            <param name="eventSpot">The new values for event</param>
            <returns>The updated event</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PatchEventSpotStatus(System.String,CTCT.Components.EventSpot.EventStatus)">
            <summary>
            Publish or cancel an event by changing the status of the event
            </summary>
            <param name="eventId">Event id</param>
            <param name="eventStatus">New status of the event. ACTIVE" and "CANCELLED are allowed</param>
            <returns>The updated event</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetAllEventFees(System.String)">
            <summary>
            Retrieve all existing fees for an event
            </summary>
            <param name="eventId">Event id</param>
            <returns>A list of event fees for the specified event</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetEventFee(System.String,System.String)">
            <summary>
            Retrieve an individual event fee
            </summary>
            <param name="eventId">Event id</param>
            <param name="feeId">EventFee id</param>
            <returns>An EventFee object</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PutEventFee(System.String,System.String,CTCT.Components.EventSpot.EventFee)">
            <summary>
            Update an individual event fee
            </summary>
            <param name="eventId">Event id</param>
            <param name="feeId">EventFee id</param>
            <param name="eventFee">The new values of EventFee</param>
            <returns>The updated EventFee</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteEventFee(System.String,System.String)">
            <summary>
             Delete an individual event fee
            </summary>
            <param name="eventId">Event id</param>
            <param name="feeId">EventFee id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PostEventFee(System.String,CTCT.Components.EventSpot.EventFee)">
            <summary>
            Create an individual event fee
            </summary>
            <param name="eventId">Event id</param>
            <param name="eventFee">EventFee object</param>
            <returns>The newly created EventFee</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetAllPromocodes(System.String)">
            <summary>
            Retrieve all existing promo codes for an event
            </summary>
            <param name="eventId">Event id</param>
            <returns>A list of Promocode</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetPromocode(System.String,System.String)">
            <summary>
            Retrieve an existing promo codes for an event
            </summary>
            <param name="eventId">Event id</param>
            <param name="promocodeId">Promocode id</param>
            <returns>The Promocode object</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PostPromocode(System.String,CTCT.Components.EventSpot.Promocode)">
            <summary>
            Create a new promo code for an event
            </summary>
            <param name="eventId">Event id</param>
            <param name="promocode">Promocode object to be created</param>
            <returns>The newly created Promocode</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PutPromocode(System.String,System.String,CTCT.Components.EventSpot.Promocode)">
            <summary>
            Update a promo code for an event
            </summary>
            <param name="eventId">Event id</param>
            <param name="promocodeId">Promocode id</param>
            <param name="promocode">The new Promocode values</param>
            <returns>The newly updated Promocode</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeletePromocode(System.String,System.String)">
            <summary>
            Delete a promo code for an event
            </summary>
            <param name="eventId">Event id</param>
            <param name="promocodeId">Promocode id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetRegistrant(System.String,System.String)">
            <summary>
            Retrieve detailed information for a specific event registrant
            </summary>
            <param name="eventId">Event id</param>
            <param name="registrantId">Redistrant id</param>
            <returns>Registrant details</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetAllRegistrants(System.String)">
            <summary>
            Retrieve a list of registrants for the specified event
            </summary>
            <param name="eventId">Event id</param>
            <returns>ResultSet containing a results array of Registrant</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetAllEventItems(System.String)">
            <summary>
            Retrieve all existing items associated with an event
            </summary>
            <param name="eventId">Event id</param>
            <returns>A list of EventItem</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetEventItem(System.String,System.String)">
            <summary>
             Retrieve specific event item
            </summary>
            <param name="eventId">Event id</param>
            <param name="itemId">Eventitem id</param>
            <returns>EventItem object</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PutEventItem(System.String,System.String,CTCT.Components.EventSpot.EventItem)">
            <summary>
             Update a specific event item
            </summary>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="eventItem">The newly values for EventItem</param>
            <returns>The updated EventItem</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PostEventItem(System.String,CTCT.Components.EventSpot.EventItem)">
            <summary>
             Create a specific event item
            </summary>
            <param name="eventId">Event id</param>
            <param name="eventItem">EventItem id</param>
            <returns>The newly created EventItem</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteEventItem(System.String,System.String)">
            <summary>
            Delete a specific event item
            </summary>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PostEventItemAttribute(System.String,System.String,CTCT.Components.EventSpot.Attribute)">
            <summary>
            Create an attributes for an item
            </summary>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attribute">The Attribute object</param>
            <returns>The newly created attribure</returns>
        </member>
        <member name="M:CTCT.ConstantContact.PutEventItemAttribute(System.String,System.String,System.String,CTCT.Components.EventSpot.Attribute)">
            <summary>
            Updates an existing attributes for an item
            </summary>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attributeId">Attribute id</param>
            <param name="attribute">Attribute new values</param>
            <returns>The newly updated attribute</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetEventItemAttribute(System.String,System.String,System.String)">
            <summary>
            Retrieve an existing attributes for an item
            </summary>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attributeId">Attribute id</param>
            <returns>Attribute object</returns>
        </member>
        <member name="M:CTCT.ConstantContact.GetAllEventItemAttributes(System.String,System.String)">
            <summary>
            Retrieve all existing attributes for an item
            </summary>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <returns>A list of Attributes</returns>
        </member>
        <member name="M:CTCT.ConstantContact.DeleteEventItemAttribute(System.String,System.String,System.String)">
            <summary>
            Delete an existing attributes for an item
            </summary>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attributeId">Attribute id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="P:CTCT.ConstantContact.AccessToken">
            <summary>
            Gets or sets the AccessToken
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.APIKey">
            <summary>
            Gets or sets the api_key
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.ContactService">
            <summary>
            Gets or sets the Contact service.
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.ListService">
            <summary>
            Gets or sets the List service.
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.ActivityService">
            <summary>
            Gets or sets the Activity service.
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.CampaignScheduleService">
            <summary>
            Gets or sets the Campaign Schedule service.
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.CampaignTrackingService">
            <summary>
            Gets or sets the Campaign Tracking service.
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.ContactTrackingService">
            <summary>
            Gets or sets the Contact Tracking service.
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.EmailCampaignService">
            <summary>
            Gets or sets the Email Campaign service.
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.AccountService">
            <summary>
            Gets or sets the Account service
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.MyLibraryService">
            <summary>
            Gets or sets the MyLibrary service
            </summary>
        </member>
        <member name="P:CTCT.ConstantContact.EventSpotService">
            <summary>
            Gets of sets the EventSpot service
            </summary>
        </member>
        <member name="T:CTCT.Exceptions.CtctException">
            <summary>
            General exception.
            </summary>
        </member>
        <member name="M:CTCT.Exceptions.CtctException.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="M:CTCT.Exceptions.CtctException.#ctor(System.String)">
            <summary>
            Class constructor.
            </summary>
            <param name="message">Error message.</param>
        </member>
        <member name="T:CTCT.Exceptions.IllegalArgumentException">
            <summary>
            Exception thrown if there is an illegal argument passed to a particular method.
            </summary>
        </member>
        <member name="M:CTCT.Exceptions.IllegalArgumentException.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="M:CTCT.Exceptions.IllegalArgumentException.#ctor(System.String)">
            <summary>
            Class constructor.
            </summary>
            <param name="message">Error message.</param>
        </member>
        <member name="T:CTCT.Exceptions.OAuth2Exception">
            <summary>
            Exception thrown if an error occured during OAuth2 authentication process.
            </summary>
        </member>
        <member name="M:CTCT.Exceptions.OAuth2Exception.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="M:CTCT.Exceptions.OAuth2Exception.#ctor(System.String)">
            <summary>
            Class constructor.
            </summary>
            <param name="message">Error message.</param>
        </member>
        <member name="T:CTCT.OAuth">
            <summary>
            Main class meant to be used by users to obtain access token by authenticating their app
            </summary>
        </member>
        <member name="M:CTCT.OAuth.AuthenticateFromWinProgram(System.String@)">
            <summary>
            Returns access token obtained after authenticating client app
            </summary>
            <param name="state">state query parameter</param>
            <returns>string representing access token if authentication succeded, null otherwise</returns>
        </member>
        <member name="M:CTCT.OAuth.AuthorizeFromWebApplication(System.Web.HttpContext,System.String)">
            <summary>
            Sends an authorization request to Constant Contact API
            (if access to application is granted, a code is send to Redirect URL field)
            (Redirect URL is one of web application url pages/methods)
            </summary>
            <param name="httpContext">current application context</param>
            <param name="state">state query parameter</param>
        </member>
        <member name="M:CTCT.OAuth.GetAccessTokenByCode(System.Web.HttpContext,System.String)">
            <summary>
            Request access token for an app key, client secret and authorization code
            </summary>
            <param name="httpContext">current application context</param>
            <param name="code">authorization code</param>
            <returns>access token</returns>
        </member>
        <member name="T:CTCT.AuthenticationRequest">
            <summary>
            AuthenticationRequest class.
            </summary>
        </member>
        <member name="P:CTCT.AuthenticationRequest.AccessToken">
            <summary>
            Activity id.
            </summary>
        </member>
        <member name="P:CTCT.AuthenticationRequest.ExpiresIn">
            <summary>
            Activity id.
            </summary>
        </member>
        <member name="P:CTCT.AuthenticationRequest.TokenType">
            <summary>
            Activity id.
            </summary>
        </member>
        <member name="T:CTCT.Services.AccountService">
            <summary>
            Performs all actions pertaining to getting list of verified email addresses
            </summary>
        </member>
        <member name="T:CTCT.Services.BaseService">
            <summary>
            Super class for all services.
            </summary>
        </member>
        <member name="T:CTCT.Services.IBaseService">
            <summary>
            Interface for BaseService class.
            </summary>
        </member>
        <member name="P:CTCT.Services.IBaseService.RestClient">
            <summary>
            Returns the REST client object.
            </summary>
        </member>
        <member name="M:CTCT.Services.BaseService.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="M:CTCT.Services.BaseService.#ctor(CTCT.Util.RestClient)">
            <summary>
            Constructor with the option to to supply an alternative rest client to be used.
            </summary>
            <param name="restClient">RestClientInterface implementation to be used in the service.</param>
        </member>
        <member name="M:CTCT.Services.BaseService.GetQueryParameters(System.Object[])">
            <summary>
            Constructs the query with specified parameters.
            </summary>
            <param name="prms">An array of parameter name and value combinations.</param>
            <returns>Returns the query part of the URL.</returns>
        </member>
        <member name="P:CTCT.Services.BaseService.RestClient">
            <summary>
            Get the rest client being used by the service.
            </summary>
        </member>
        <member name="T:CTCT.Services.IAccountService">
            <summary>
            Interface for account service
            </summary>
        </member>
        <member name="M:CTCT.Services.IAccountService.GetVerifiedEmailAddress(System.String,System.String)">
            <summary>
            Retrieve a list of all the account owner's email addresses
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <returns>list of all verified account owner's email addresses</returns>
        </member>
        <member name="M:CTCT.Services.IAccountService.GetAccountSummaryInformation(System.String,System.String)">
            <summary>
            Get account summary information
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <returns>An AccountSummaryInformation object</returns>
        </member>
        <member name="M:CTCT.Services.IAccountService.PutAccountSummaryInformation(System.String,System.String,CTCT.Components.AccountService.AccountSummaryInformation)">
            <summary>
            Updates account summary information
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="accountSumaryInfo">An AccountSummaryInformation object</param>
            <returns>An AccountSummaryInformation object</returns>
        </member>
        <member name="M:CTCT.Services.AccountService.GetVerifiedEmailAddress(System.String,System.String)">
            <summary>
            Retrieve a list of all the account owner's email addresses
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <returns>list of all verified account owner's email addresses</returns>
        </member>
        <member name="M:CTCT.Services.AccountService.GetAccountSummaryInformation(System.String,System.String)">
            <summary>
            Get account summary information
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <returns>An AccountSummaryInformation object</returns>
        </member>
        <member name="M:CTCT.Services.AccountService.PutAccountSummaryInformation(System.String,System.String,CTCT.Components.AccountService.AccountSummaryInformation)">
            <summary>
            Updates account summary information
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="accountSumaryInfo">An AccountSummaryInformation object</param>
            <returns>An AccountSummaryInformation object</returns>
        </member>
        <member name="T:CTCT.Services.ActivityService">
            <summary> 
            Performs all actions pertaining to scheduling Constant Contact Activities.
            </summary>
        </member>
        <member name="T:CTCT.Services.IActivityService">
            <summary>
            Interface for ActivityService class.
            </summary>
        </member>
        <member name="M:CTCT.Services.IActivityService.GetActivities(System.String,System.String)">
            <summary>
            Get a list of activities.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <returns>Returns the list of activities.</returns>
        </member>
        <member name="M:CTCT.Services.IActivityService.GetActivity(System.String,System.String,System.String)">
            <summary>
            Get an activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="activityId">The activity identification.</param>
            <returns>Returns the activity identified by its id.</returns>
        </member>
        <member name="M:CTCT.Services.IActivityService.CreateAddContactsActivity(System.String,System.String,CTCT.Components.Activities.AddContacts)">
            <summary>
            Create an Add Contacts Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="addContacts">AddContacts object.</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.IActivityService.AddClearListsActivity(System.String,System.String,System.Collections.Generic.IList{System.String})">
            <summary>
            Create a Clear Lists Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="lists">Array of list id's to be cleared.</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.IActivityService.AddExportContactsActivity(System.String,System.String,CTCT.Components.Activities.ExportContacts)">
            <summary>
            Create an Export Contacts Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="exportContacts">Export contacts object.</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.IActivityService.AddRemoveContactsFromListsActivity(System.String,System.String,System.Collections.Generic.IList{System.String},System.Collections.Generic.IList{System.String})">
            <summary>
            Create a Remove Contacts From Lists Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="emailAddresses">List of email addresses.</param>
            <param name="lists">List of id's.</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.IActivityService.AddContactstMultipartActivity(System.String,System.String,System.String,System.Byte[],System.Collections.Generic.IList{System.String})">
            <summary>
            Create an Add Contacts Multipart Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileName">The file name to be imported</param>
            <param name="fileContent">The file content to be imported</param>
            <param name="lists">Array of list's id</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.IActivityService.RemoveContactsMultipartActivity(System.String,System.String,System.String,System.Byte[],System.Collections.Generic.IList{System.String})">
            <summary>
            Create a Remove Contacts Multipart Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileName">The file name to be imported</param>
            <param name="fileContent">The file content to be imported</param>
            <param name="lists">Array of list's id</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.ActivityService.GetActivities(System.String,System.String)">
            <summary>
            Get a list of activities.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <returns>Returns the list of activities.</returns>
        </member>
        <member name="M:CTCT.Services.ActivityService.GetActivity(System.String,System.String,System.String)">
            <summary>
            Get an activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="activityId">The activity identification.</param>
            <returns>Returns the activity identified by its id.</returns>
        </member>
        <member name="M:CTCT.Services.ActivityService.CreateAddContactsActivity(System.String,System.String,CTCT.Components.Activities.AddContacts)">
            <summary>
            Create an Add Contacts Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="addContacts">AddContacts object.</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.ActivityService.AddClearListsActivity(System.String,System.String,System.Collections.Generic.IList{System.String})">
            <summary>
            Create a Clear Lists Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="lists">Array of list id's to be cleared.</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.ActivityService.AddExportContactsActivity(System.String,System.String,CTCT.Components.Activities.ExportContacts)">
            <summary>
            Create an Export Contacts Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="exportContacts">Export contacts object.</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.ActivityService.AddRemoveContactsFromListsActivity(System.String,System.String,System.Collections.Generic.IList{System.String},System.Collections.Generic.IList{System.String})">
            <summary>
            Create a Remove Contacts From Lists Activity.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="emailAddresses">List of email addresses.</param>
            <param name="lists">List of id's.</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.ActivityService.AddContactstMultipartActivity(System.String,System.String,System.String,System.Byte[],System.Collections.Generic.IList{System.String})">
            <summary>
             Create an Add Contacts Multipart Activity
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>>
            <param name="fileName">The file name to be imported</param>
            <param name="fileContent">The file content to be imported</param>
            <param name="lists">Array of list's id</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="M:CTCT.Services.ActivityService.RemoveContactsMultipartActivity(System.String,System.String,System.String,System.Byte[],System.Collections.Generic.IList{System.String})">
            <summary>
             Create a Remove Contacts Multipart Activity
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>>
            <param name="fileName">The file name to be imported</param>
            <param name="fileContent">The file content to be imported</param>
            <param name="lists">Array of list's id</param>
            <returns>Returns an Activity object.</returns>
        </member>
        <member name="T:CTCT.Services.CampaignScheduleService">
            <summary>
            Performs all actions pertaining to scheduling Constant Contact Campaigns.
            </summary>
        </member>
        <member name="T:CTCT.Services.ICampaignScheduleService">
            <summary>
            Interface for CampaignScheduleService class.
            </summary>
        </member>
        <member name="M:CTCT.Services.ICampaignScheduleService.AddSchedule(System.String,System.String,System.String,CTCT.Components.EmailCampaigns.Schedule)">
            <summary>
            Create a new schedule for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id to be scheduled.</param>
            <param name="schedule">Schedule to be created.</param>
            <returns>Returns the schedule added.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignScheduleService.GetSchedules(System.String,System.String,System.String)">
            <summary>
            Get a list of schedules for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id to be scheduled.</param>
            <returns>Returns the list of schedules for the specified campaign.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignScheduleService.GetSchedule(System.String,System.String,System.String,System.String)">
            <summary>
            Get a specific schedule for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id to be get a schedule for.</param>
            <param name="scheduleId">Schedule id to retrieve.</param>
            <returns>Returns the requested schedule object.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignScheduleService.UpdateSchedule(System.String,System.String,System.String,CTCT.Components.EmailCampaigns.Schedule)">
            <summary>
            Update a specific schedule for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id to be scheduled.</param>
            <param name="schedule">Schedule to retrieve.</param>
            <returns>Returns the updated schedule object.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignScheduleService.DeleteSchedule(System.String,System.String,System.String,System.String)">
            <summary>
            Get a specific schedule for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="scheduleId">Schedule id to delete.</param>
            <returns>Returns true if shcedule was successfully deleted.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignScheduleService.SendTest(System.String,System.String,System.String,CTCT.Components.EmailCampaigns.TestSend)">
            <summary>
            Send a test send of a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Id of campaign to send test of.</param>
            <param name="testSend">Test send details.</param>
            <returns>Returns the sent test object.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignScheduleService.AddSchedule(System.String,System.String,System.String,CTCT.Components.EmailCampaigns.Schedule)">
            <summary>
            Create a new schedule for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id to be scheduled.</param>
            <param name="schedule">Schedule to be created.</param>
            <returns>Returns the schedule added.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignScheduleService.GetSchedules(System.String,System.String,System.String)">
            <summary>
            Get a list of schedules for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id to be scheduled.</param>
            <returns>Returns the list of schedules for the specified campaign.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignScheduleService.GetSchedule(System.String,System.String,System.String,System.String)">
            <summary>
            Get a specific schedule for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id to be get a schedule for.</param>
            <param name="scheduleId">Schedule id to retrieve.</param>
            <returns>Returns the requested schedule object.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignScheduleService.UpdateSchedule(System.String,System.String,System.String,CTCT.Components.EmailCampaigns.Schedule)">
            <summary>
            Update a specific schedule for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id to be scheduled.</param>
            <param name="schedule">Schedule to retrieve.</param>
            <returns>Returns the updated schedule object.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignScheduleService.DeleteSchedule(System.String,System.String,System.String,System.String)">
            <summary>
            Get a specific schedule for a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="scheduleId">Schedule id to delete.</param>
            <returns>Returns true if shcedule was successfully deleted.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignScheduleService.SendTest(System.String,System.String,System.String,CTCT.Components.EmailCampaigns.TestSend)">
            <summary>
            Send a test send of a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Id of campaign to send test of.</param>
            <param name="testSend">Test send details.</param>
            <returns>Returns the sent test object.</returns>
        </member>
        <member name="T:CTCT.Services.CampaignTrackingService">
            <summary>
            Performs all actions pertaining to Constant Contact Campaign Tracking.
            </summary>
        </member>
        <member name="T:CTCT.Services.ICampaignTrackingService">
            <summary>
            Interface for CampaignTrackingService class.
            </summary>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetBounces(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get a result set of bounces for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for bounces created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link BounceActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetBounces(System.String,System.String,CTCT.Components.Pagination)">
            <summary>
            Get a result set of bounces for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link BounceActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetClicks(System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="linkId">Specifies the link in the email campaign to retrieve click data for.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetClicks(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetClicks(System.String,System.String,CTCT.Components.Pagination)">
            <summary>
            Get clicks for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetForwards(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get forwards for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetForwards(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get forwards for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetOpens(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opens for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetOpens(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opens for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetSends(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get sends for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link SendActivity</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetSends(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get sends for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link SendActivity</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetOptOuts(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opt outs for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetOptOuts(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opt outs for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ICampaignTrackingService.GetSummary(System.String,System.String,System.String)">
            <summary>
            Get a summary of reporting data for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <returns>Tracking summary.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetBounces(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get a result set of bounces for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for bounces created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link BounceActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetBounces(System.String,System.String,CTCT.Components.Pagination)">
            <summary>
            Get a result set of bounces for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link BounceActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetBounces(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get a result set of bounces for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for bounces created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link BounceActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetClicks(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetClicks(System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a specific link in a campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="linkId">Specifies the link in the email campaign to retrieve click data for.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetClicks(System.String,System.String,CTCT.Components.Pagination)">
            <summary>
            Get clicks for the provided Pagination page.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetClicks(System.String,System.String,System.String)">
            <summary>
            Get ClickActivity at the given url.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="url">The url from which to retrieve an array of @link ClickActivity.</param>
            <returns>ResultSet containing a results array of @link ClickActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetForwards(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get forwards for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetForwards(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get forwards for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetForwards(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get forwards for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetOpens(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opens for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetOpens(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opens for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetOpens(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opens for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetSends(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get sends for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link SendActivity</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetSends(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get sends for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link SendActivity</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetSends(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get sends for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link SendActivity</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetOptOuts(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opt outs for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetOptOuts(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opt outs for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetOptOuts(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opt outs for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.CampaignTrackingService.GetSummary(System.String,System.String,System.String)">
            <summary>
            Get a summary of reporting data for a given campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <returns>Tracking summary.</returns>
        </member>
        <member name="T:CTCT.Services.ContactTrackingService">
            <summary>
            Performs all actions pertaining to Contact Tracking.
            </summary>
        </member>
        <member name="T:CTCT.Services.IContactTrackingService">
            <summary>
            Interface for ContactTrackingService class.
            </summary>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetActivities(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get all activities for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ContactActivity</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetActivities(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get all activities for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for activities created since the supplied date in the collection</param>	 
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ContactActivity</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetEmailCampaignActivities(System.String,System.String,System.String)">
            <summary>
            Get activities by email campaign for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <returns>ResultSet containing a results array of @link TrackingSummary</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetBounces(System.String,System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            Get bounces for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <returns>ResultSet containing a results array of @link BounceActivity</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetBounces(System.String,System.String,CTCT.Components.Pagination)">
            <summary>
            Get bounces for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link BounceActivity</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetClicks(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetClicks(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get clicks for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ClickActivity</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetForwards(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get forwards for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetForwards(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get forwards for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetOpens(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opens for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetOpens(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opens for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetSends(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get sends for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link SendActivity.</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetSends(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get sends for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link SendActivity.</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetOptOuts(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opt outs for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetOptOuts(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opt outs for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.IContactTrackingService.GetSummary(System.String,System.String,System.String)">
            <summary>
            Get a summary of reporting data for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <returns>Tracking summary.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetActivities(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get all activities for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ContactActivity</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetActivities(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get all activities for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">Filter for activities created since the supplied date in the collection</param>	 
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ContactActivity</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetEmailCampaignActivities(System.String,System.String,System.String)">
            <summary>
            Get activities by email campaign for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <returns>ResultSet containing a results array of @link TrackingSummary</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetBounces(System.String,System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            Get bounces for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <returns>ResultSet containing a results array of @link BounceActivity</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetBounces(System.String,System.String,CTCT.Components.Pagination)">
            <summary>
            Get bounces for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link BounceActivity</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetBounces(System.String,System.String,System.String,System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get bounces for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link BounceActivity</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetClicks(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get clicks for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ClickActivity</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetClicks(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get clicks for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ClickActivity</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetClicks(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get clicks for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ClickActivity</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetForwards(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get forwards for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetForwards(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get forwards for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetForwards(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get forwards for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link ForwardActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetOpens(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opens for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetOpens(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opens for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetOpens(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opens for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OpenActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetSends(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get sends for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link SendActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetSends(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get sends for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link SendActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetSends(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get sends for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link SendActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetOptOuts(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get opt outs for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetOptOuts(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opt outs for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetOptOuts(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get opt outs for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="createdSince">filter for activities created since the supplied date in the collection</param>
            <param name="pag">Pagination object.</param>
            <returns>ResultSet containing a results array of @link OptOutActivity.</returns>
        </member>
        <member name="M:CTCT.Services.ContactTrackingService.GetSummary(System.String,System.String,System.String)">
            <summary>
            Get a summary of reporting data for a given contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id.</param>
            <returns>Tracking summary.</returns>
        </member>
        <member name="T:CTCT.Services.EmailCampaignService">
            <summary>
            Performs all actions pertaining to the Contacts Collection.
            </summary>
        </member>
        <member name="T:CTCT.Services.IEmailCampaignService">
            <summary>
            Interface for CampaignService class.
            </summary>
        </member>
        <member name="M:CTCT.Services.IEmailCampaignService.GetCampaigns(System.String,System.String,System.Nullable{CTCT.Components.EmailCampaigns.CampaignStatus},System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get a set of campaigns.
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="status">Returns list of email campaigns with specified status.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit campaigns to campaigns modified since the supplied date</param>
            <returns>Returns a list of campaigns.</returns>
        </member>
        <member name="M:CTCT.Services.IEmailCampaignService.GetCampaigns(System.String,System.String,System.Nullable{CTCT.Components.EmailCampaigns.CampaignStatus},System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get a set of campaigns.
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="status">Returns list of email campaigns with specified status.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit campaigns to campaigns modified since the supplied date</param>
            <param name="pag">Pagination object returned by a previous call to GetCampaigns</param>
            <returns>Returns a list of campaigns.</returns>
        </member>
        <member name="M:CTCT.Services.IEmailCampaignService.GetCampaign(System.String,System.String,System.String)">
            <summary>
            Get campaign details for a specific campaign.
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <returns>Returns a campaign.</returns>
        </member>
        <member name="M:CTCT.Services.IEmailCampaignService.AddCampaign(System.String,System.String,CTCT.Components.EmailCampaigns.EmailCampaign)">
            <summary>
            Create a new campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaign">Campign to be created</param>
            <returns>Returns a campaign.</returns>
        </member>
        <member name="M:CTCT.Services.IEmailCampaignService.DeleteCampaign(System.String,System.String,System.String)">
            <summary>
            Delete an email campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Valid campaign id.</param>
            <returns>Returns true if successful.</returns>
        </member>
        <member name="M:CTCT.Services.IEmailCampaignService.UpdateCampaign(System.String,System.String,CTCT.Components.EmailCampaigns.EmailCampaign)">
            <summary>
            Update a specific email campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaign">Campaign to be updated.</param>
            <returns>Returns a campaign.</returns>
        </member>
        <member name="M:CTCT.Services.EmailCampaignService.GetCampaigns(System.String,System.String,System.Nullable{CTCT.Components.EmailCampaigns.CampaignStatus},System.Nullable{System.Int32},System.Nullable{System.DateTime})">
            <summary>
            Get a set of campaigns.
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="status">Returns list of email campaigns with specified status.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit campaigns to campaigns modified since the supplied date</param>
            <returns>Returns a ResultSet of campaigns.</returns>
        </member>
        <member name="M:CTCT.Services.EmailCampaignService.GetCampaigns(System.String,System.String,System.Nullable{CTCT.Components.EmailCampaigns.CampaignStatus},System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get a set of campaigns.
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="status">Returns list of email campaigns with specified status.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit campaigns to campaigns modified since the supplied date</param>
            <param name="pag">Pagination object returned by a previous call to GetCampaigns</param>
            <returns>Returns a ResultSet of campaigns.</returns>
        </member>
        <member name="M:CTCT.Services.EmailCampaignService.GetCampaign(System.String,System.String,System.String)">
            <summary>
            Get campaign details for a specific campaign.
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Campaign id.</param>
            <returns>Returns a campaign.</returns>
        </member>
        <member name="M:CTCT.Services.EmailCampaignService.AddCampaign(System.String,System.String,CTCT.Components.EmailCampaigns.EmailCampaign)">
            <summary>
            Create a new campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaign">Campign to be created</param>
            <returns>Returns a campaign.</returns>
        </member>
        <member name="M:CTCT.Services.EmailCampaignService.DeleteCampaign(System.String,System.String,System.String)">
            <summary>
            Delete an email campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaignId">Valid campaign id.</param>
            <returns>Returns true if successful.</returns>
        </member>
        <member name="M:CTCT.Services.EmailCampaignService.UpdateCampaign(System.String,System.String,CTCT.Components.EmailCampaigns.EmailCampaign)">
            <summary>
            Update a specific email campaign.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="campaign">Campaign to be updated.</param>
            <returns>Returns a campaign.</returns>
        </member>
        <member name="T:CTCT.Services.ContactService">
            <summary>
            Performs all actions pertaining to the Contacts Collection.
            </summary>
        </member>
        <member name="T:CTCT.Services.IContactService">
            <summary>
            Interface for ContactService class.
            </summary>
        </member>
        <member name="M:CTCT.Services.IContactService.GetContacts(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},System.Nullable{CTCT.Components.Contacts.ContactStatus})">
            <summary>
            Get an array of contacts.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="email">Match the exact email address.</param>
            <param name="limit">Limit the number of returned values.</param>
            <param name="modifiedSince">limit contact to contacts modified since the supplied date</param>
            <param name="status">Returns list of contacts with specified status.</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="M:CTCT.Services.IContactService.GetContacts(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get an array of contacts.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="modifiedSince">limit contact to contacts modified since the supplied date</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="M:CTCT.Services.IContactService.GetContact(System.String,System.String,System.String)">
            <summary>
            Get contact details for a specific contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Unique contact id.</param>
            <returns>Returns a contact.</returns>
        </member>
        <member name="M:CTCT.Services.IContactService.AddContact(System.String,System.String,CTCT.Components.Contacts.Contact,System.Boolean)">
            <summary>
            Add a new contact to the Constant Contact account
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contact">Contact to add.</param>
            <param name="actionByVisitor">Set to true if action by visitor.</param>
            <returns>Returns the newly created contact.</returns>
        </member>
        <member name="M:CTCT.Services.IContactService.DeleteContact(System.String,System.String,System.String)">
            <summary>
            Delete contact details for a specific contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Unique contact id.</param>
            <returns>Returns true if operation succeeded.</returns>
        </member>
        <member name="M:CTCT.Services.IContactService.DeleteContactFromLists(System.String,System.String,System.String)">
            <summary>
            Delete a contact from all contact lists.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id to be removed from lists.</param>
            <returns>Returns true if operation succeeded.</returns>
        </member>
        <member name="M:CTCT.Services.IContactService.DeleteContactFromList(System.String,System.String,System.String,System.String)">
            <summary>
            Delete a contact from a specific contact list.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id to be removed</param>
            <param name="listId">ContactList to remove the contact from</param>
            <returns>Returns true if operation succeeded.</returns>
        </member>
        <member name="M:CTCT.Services.IContactService.UpdateContact(System.String,System.String,CTCT.Components.Contacts.Contact,System.Boolean)">
            <summary>
            Update contact details for a specific contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contact">Contact to be updated.</param>
            <param name="actionByVisitor">Set to true if action by visitor.</param>
            <returns>Returns the updated contact.</returns>
        </member>
        <member name="M:CTCT.Services.ContactService.GetContacts(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},System.Nullable{CTCT.Components.Contacts.ContactStatus})">
            <summary>
            Get an array of contacts.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="email">Match the exact email address.</param>
            <param name="limit">Limit the number of returned values.</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <param name="status">Filter results by contact status</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="M:CTCT.Services.ContactService.GetContacts(System.String,System.String,System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get an array of contacts.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="modifiedSince">limit contact to contacts modified since the supplied date</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="M:CTCT.Services.ContactService.GetContacts(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},System.Nullable{CTCT.Components.Contacts.ContactStatus},CTCT.Components.Pagination)">
            <summary>
            Get an array of contacts.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="email">Match the exact email address.</param>
            <param name="limit">Limit the number of returned values.</param>
            <param name="modifiedSince">limit contact to contacts modified since the supplied date</param>
            <param name="status">Match the exact contact status.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="M:CTCT.Services.ContactService.GetContact(System.String,System.String,System.String)">
            <summary>
            Get contact details for a specific contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Unique contact id.</param>
            <returns>Returns a contact.</returns>
        </member>
        <member name="M:CTCT.Services.ContactService.AddContact(System.String,System.String,CTCT.Components.Contacts.Contact,System.Boolean)">
            <summary>
            Add a new contact to the Constant Contact account
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contact">Contact to add.</param>
            <param name="actionByVisitor">Set to true if action by visitor.</param>
            <returns>Returns the newly created contact.</returns>
        </member>
        <member name="M:CTCT.Services.ContactService.DeleteContact(System.String,System.String,System.String)">
            <summary>
            Unsubscribe a specific contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Unique contact id.</param>
            <returns>Returns true if operation succeeded.</returns>
        </member>
        <member name="M:CTCT.Services.ContactService.DeleteContactFromLists(System.String,System.String,System.String)">
            <summary>
            Delete a contact from all contact lists.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id to be removed from lists.</param>
            <returns>Returns true if operation succeeded.</returns>
        </member>
        <member name="M:CTCT.Services.ContactService.DeleteContactFromList(System.String,System.String,System.String,System.String)">
            <summary>
            Delete a contact from a specific contact list.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contactId">Contact id to be removed</param>
            <param name="listId">ContactList to remove the contact from</param>
            <returns>Returns true if operation succeeded.</returns>
        </member>
        <member name="M:CTCT.Services.ContactService.UpdateContact(System.String,System.String,CTCT.Components.Contacts.Contact,System.Boolean)">
            <summary>
            Update contact details for a specific contact.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="contact">Contact to be updated.</param>
            <param name="actionByVisitor">Set to true if action by visitor.</param>
            <returns>Returns the updated contact.</returns>
        </member>
        <member name="T:CTCT.Services.EventSpotService">
            <summary>
            Performs all actions for EventSpot
            </summary>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetAllEventSpots(System.String,System.String,System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            View all existing events
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 50</param>
            <param name="pag">Pagination object</param>
            <returns>ResultSet containing a results array of IndividualEvents</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetEventSpot(System.String,System.String,System.String)">
            <summary>
            Retrieve an event specified by the event_id
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>The event</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PostEventSpot(System.String,System.String,CTCT.Components.EventSpot.IndividualEvent)">
            <summary>
            Publish an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventSpot">The event to publish</param>
            <returns>The published event</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PutEventSpot(System.String,System.String,System.String,CTCT.Components.EventSpot.IndividualEvent)">
            <summary>
            Update an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id to be updated</param>
            <param name="eventSpot">The new values for event</param>
            <returns>The updated event</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PatchEventSpotStatus(System.String,System.String,System.String,CTCT.Components.EventSpot.EventStatus)">
            <summary>
            Publish or cancel an event by changing the status of the event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="eventStatus">New status of the event. ACTIVE" and "CANCELLED are allowed</param>
            <returns>The updated event</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetAllEventFees(System.String,System.String,System.String)">
            <summary>
            Retrieve all existing fees for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>A list of event fees for the specified event</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetEventFee(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve an individual event fee
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="feeId">EventFee id</param>
            <returns>An EventFee object</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PutEventFee(System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.EventFee)">
            <summary>
            Update an individual event fee
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="feeId">EventFee id</param>
            <param name="eventFee">The new values of EventFee</param>
            <returns>The updated EventFee</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.DeleteEventFee(System.String,System.String,System.String,System.String)">
            <summary>
             Delete an individual event fee
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="feeId">EventFee id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PostEventFee(System.String,System.String,System.String,CTCT.Components.EventSpot.EventFee)">
            <summary>
            Create an individual event fee
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="eventFee">EventFee object</param>
            <returns>The newly created EventFee</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetAllPromocodes(System.String,System.String,System.String)">
            <summary>
            Retrieve all existing promo codes for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>A list of Promocode</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetPromocode(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve an existing promo codes for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="promocodeId">Promocode id</param>
            <returns>The Promocode object</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PostPromocode(System.String,System.String,System.String,CTCT.Components.EventSpot.Promocode)">
            <summary>
            Create a new promo code for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="promocode">Promocode object to be created</param>
            <returns>The newly created Promocode</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PutPromocode(System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.Promocode)">
            <summary>
            Update a promo code for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="promocodeId">Promocode id</param>
            <param name="promocode">The new Promocode values</param>
            <returns>The newly updated Promocode</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.DeletePromocode(System.String,System.String,System.String,System.String)">
            <summary>
            Delete a promo code for an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="promocodeId">Promocode id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetRegistrant(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve detailed information for a specific event registrant
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="registrantId">Redistrant id</param>
            <returns>Registrant details</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetAllRegistrants(System.String,System.String,System.String)">
            <summary>
            Retrieve a list of registrants for the specified event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>ResultSet containing a results array of Registrant</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetAllEventItems(System.String,System.String,System.String)">
            <summary>
            Retrieve all existing items associated with an event
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <returns>A list of EventItem</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetEventItem(System.String,System.String,System.String,System.String)">
            <summary>
             Retrieve specific event item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">Eventitem id</param>
            <returns>EventItem object</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PutEventItem(System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.EventItem)">
            <summary>
             Update a specific event item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="eventItem">The newly values for EventItem</param>
            <returns>The updated EventItem</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PostEventItem(System.String,System.String,System.String,CTCT.Components.EventSpot.EventItem)">
            <summary>
             Create a specific event item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="eventItem">EventItem id</param>
            <returns>The newly created EventItem</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.DeleteEventItem(System.String,System.String,System.String,System.String)">
            <summary>
            Delete a specific event item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PostEventItemAttribute(System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.Attribute)">
            <summary>
            Create an attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attribute">The Attribute object</param>
            <returns>The newly created attribure</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.PutEventItemAttribute(System.String,System.String,System.String,System.String,System.String,CTCT.Components.EventSpot.Attribute)">
            <summary>
            Updates an existing attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attributeId">Attribute id</param>
            <param name="attribute">Attribute new values</param>
            <returns>The newly updated attribute</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetEventItemAttribute(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve an existing attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attributeId">Attribute id</param>
            <returns>Attribute object</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.GetAllEventItemAttributes(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieve all existing attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <returns>A list of Attributes</returns>
        </member>
        <member name="M:CTCT.Services.EventSpotService.DeleteEventItemAttribute(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Delete an existing attributes for an item
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="eventId">Event id</param>
            <param name="itemId">EventItem id</param>
            <param name="attributeId">Attribute id</param>
            <returns>True if successfuly deleted</returns>
        </member>
        <member name="T:CTCT.Services.IListService">
            <summary>
            Interface for ListService class.
            </summary>
        </member>
        <member name="M:CTCT.Services.IListService.GetLists(System.String,System.String,System.Nullable{System.DateTime})">
            <summary>
            Get lists within an account.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <returns>Returns a list of contact lists.</returns>
        </member>
        <member name="M:CTCT.Services.IListService.AddList(System.String,System.String,CTCT.Components.Contacts.ContactList)">
            <summary>
            Add a new list.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="list">Contact list.</param>
            <returns>Returns the newly created list.</returns>
        </member>
        <member name="M:CTCT.Services.IListService.GetList(System.String,System.String,System.String)">
            <summary>
            Get an individual contact list.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="listId">List id.</param>
            <returns>Returns a contact list.</returns>
        </member>
        <member name="M:CTCT.Services.IListService.UpdateList(System.String,System.String,CTCT.Components.Contacts.ContactList)">
            <summary>
            Update a Contact List.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="list">ContactList to be updated</param>
            <returns>Contact list</returns>
        </member>
        <member name="M:CTCT.Services.IListService.DeleteList(System.String,System.String,System.String)">
            <summary>
            Delete a Contact List.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="listId">List id.</param>
            <returns>return true if list was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.Services.IListService.GetContactsFromList(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get all contacts from an individual list.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="listId">List id to retrieve contacts for.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="T:CTCT.Services.IMyLibraryService">
            <summary>
            Interface for MyLibraryService class
            </summary>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.GetLibraryInfo(System.String,System.String)">
            <summary>
            Get MyLibrary usage information
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <returns>Returns a MyLibraryInfo object</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.GetLibraryFolders(System.String,System.String,System.Nullable{CTCT.Services.FoldersSortBy},System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get all existing MyLibrary folders
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFolder objects.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.AddLibraryFolder(System.String,System.String,CTCT.Components.MyLibrary.MyLibraryFolder)">
            <summary>
            Add new folder to MyLibrary
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folder">Folder to be added (with name and parent id)</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.GetLibraryFolder(System.String,System.String,System.String)">
            <summary>
            Get a specific folder by Id
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folderId">The id of the folder</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.UpdateLibraryFolder(System.String,System.String,CTCT.Components.MyLibrary.MyLibraryFolder,System.Nullable{System.Boolean})">
            <summary>
            Update name and parent_id for a specific folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folder">Folder to be updated (with name and parent id)</param>
            <param name="includePayload">Determines if update's folder JSON payload is returned</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.DeleteLibraryFolder(System.String,System.String,System.String)">
            <summary>
            Delete a specific folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folderId">The id of the folder</param>
            <returns>Returns true if folder was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.GetLibraryTrashFiles(System.String,System.String,System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.TrashSortBy},System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get files from Trash folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="type">The type of the files to retrieve</param>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.DeleteLibraryTrashFiles(System.String,System.String)">
            <summary>
            Delete files in Trash folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <returns>Returns true if files were deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.GetLibraryFiles(System.String,System.String,System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.FilesSources},System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get files
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="type">The type of the files to retrieve</param>
            <param name="source">Specifies to retrieve files from a particular source</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.GetLibraryFilesByFolder(System.String,System.String,System.String,System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get files from a specific folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folderId">The id of the folder from which to retrieve files</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.GetLibraryFile(System.String,System.String,System.String)">
            <summary>
            Get file after id
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileId">The id of the file</param>
            <returns>Returns a MyLibraryFile object.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.UpdateLibraryFile(System.String,System.String,CTCT.Components.MyLibrary.MyLibraryFile,System.Nullable{System.Boolean})">
            <summary>
            Update a specific file
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="file">File to be updated</param>
            <param name="includePayload">Determines if update's folder JSON payload is returned</param>
            <returns>Returns a MyLibraryFile object.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.DeleteLibraryFile(System.String,System.String,System.String)">
            <summary>
            Delete a specific file
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileId">The id of the file</param>
            <returns>Returns true if folder was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.GetLibraryFileUploadStatus(System.String,System.String,System.String)">
            <summary>
            Get status for an upload file
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileId">The id of the file</param>
            <returns>Returns a list of FileUploadStatus objects</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.MoveLibraryFile(System.String,System.String,System.String,System.Collections.Generic.IList{System.String})">
            <summary>
            Move files to a different folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folderId">The id of the folder</param>
            <param name="fileIds">List of comma separated file ids</param>
            <returns>Returns a list of FileMoveResult objects.</returns>
        </member>
        <member name="M:CTCT.Services.IMyLibraryService.AddLibraryFilesMultipart(System.String,System.String,System.String,CTCT.Components.MyLibrary.FileType,System.String,System.String,CTCT.Components.MyLibrary.FileSource,System.Byte[])">
            <summary>
            Add files using the multipart content-type
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileName">The file name and extension</param>
            <param name="fileType">The file type</param>
            <param name="folderId">The id of the folder</param>
            <param name="description">The description of the file</param>
            <param name="source">The source of the original file</param>
            <param name="data">The data contained in the file being uploaded</param>
            <returns>Returns the file Id associated with the uploaded file</returns>
        </member>
        <member name="T:CTCT.Services.ListService">
            <summary>
            Performs all actions pertaining to the Lists Collection
            </summary>
        </member>
        <member name="M:CTCT.Services.ListService.GetLists(System.String,System.String,System.Nullable{System.DateTime})">
            <summary>
            Get lists within an account.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <returns>Returns a list of contact lists.</returns>
        </member>
        <member name="M:CTCT.Services.ListService.AddList(System.String,System.String,CTCT.Components.Contacts.ContactList)">
            <summary>
            Add a new list.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="list">Contact list.</param>
            <returns>Returns the newly created list.</returns>
        </member>
        <member name="M:CTCT.Services.ListService.GetList(System.String,System.String,System.String)">
            <summary>
            Get an individual contact list.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="listId">List id.</param>
            <returns>Returns a contact list.</returns>
        </member>
        <member name="M:CTCT.Services.ListService.UpdateList(System.String,System.String,CTCT.Components.Contacts.ContactList)">
            <summary>
            Update a Contact List.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="list">ContactList to be updated</param>
            <returns>Contact list</returns>
        </member>
        <member name="M:CTCT.Services.ListService.DeleteList(System.String,System.String,System.String)">
            <summary>
            Delete a Contact List.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="listId">List id.</param>
            <returns>return true if list was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.Services.ListService.GetContactsFromList(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},CTCT.Components.Pagination)">
            <summary>
            Get all contacts from an individual list.
            </summary>
            <param name="accessToken">Constant Contact OAuth2 access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="listId">List id to retrieve contacts for.</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 500, default = 500.</param>
            <param name="modifiedSince">limit contacts retrieved to contacts modified since the supplied date</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a list of contacts.</returns>
        </member>
        <member name="T:CTCT.Services.MyLibraryService">
            <summary>
            Performs all actions pertaining to the MyLibrary Collection
            </summary>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.GetLibraryInfo(System.String,System.String)">
            <summary>
            Get MyLibrary usage information
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <returns>Returns a MyLibraryInfo object</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.GetLibraryFolders(System.String,System.String,System.Nullable{CTCT.Services.FoldersSortBy},System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get all existing MyLibrary folders
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFolder objects.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.AddLibraryFolder(System.String,System.String,CTCT.Components.MyLibrary.MyLibraryFolder)">
            <summary>
            Add new folder to MyLibrary
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folder">Folder to be added (with name and parent id)</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.GetLibraryFolder(System.String,System.String,System.String)">
            <summary>
            Get a folder by Id
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folderId">The id of the folder</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.UpdateLibraryFolder(System.String,System.String,CTCT.Components.MyLibrary.MyLibraryFolder,System.Nullable{System.Boolean})">
            <summary>
            Update name and parent_id for a specific folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folder">Folder to be added (with name and parent id)</param>
            <param name="includePayload">Determines if update's folder JSON payload is returned</param>
            <returns>Returns a MyLibraryFolder object.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.DeleteLibraryFolder(System.String,System.String,System.String)">
            <summary>
            Delete a specific folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folderId">The id of the folder</param>
            <returns>Returns true if folder was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.GetLibraryTrashFiles(System.String,System.String,System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.TrashSortBy},System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get files from Trash folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="type">The type of the files to retrieve</param>
            <param name="sortBy">Specifies how the list of folders is sorted</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.DeleteLibraryTrashFiles(System.String,System.String)">
            <summary>
            Delete files in Trash folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <returns>Returns true if files were deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.GetLibraryFiles(System.String,System.String,System.Nullable{CTCT.Services.FileTypes},System.Nullable{CTCT.Services.FilesSources},System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get files
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="type">The type of the files to retrieve</param>
            <param name="source">Specifies to retrieve files from a particular source</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.GetLibraryFilesByFolder(System.String,System.String,System.String,System.Nullable{System.Int32},CTCT.Components.Pagination)">
            <summary>
            Get files from a specific folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folderId">The id of the folder from which to retrieve files</param>
            <param name="limit">Specifies the number of results per page in the output, from 1 - 50, default = 50.</param>
            <param name="pag">Pagination object.</param>
            <returns>Returns a collection of MyLibraryFile objects.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.GetLibraryFile(System.String,System.String,System.String)">
            <summary>
            Get file after id
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileId">The id of the file</param>
            <returns>Returns a MyLibraryFile object.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.UpdateLibraryFile(System.String,System.String,CTCT.Components.MyLibrary.MyLibraryFile,System.Nullable{System.Boolean})">
            <summary>
            Update a specific file
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="file">File to be updated</param>
            <param name="includePayload">Determines if update's folder JSON payload is returned</param>
            <returns>Returns a MyLibraryFile object.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.DeleteLibraryFile(System.String,System.String,System.String)">
            <summary>
            Delete a specific file
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileId">The id of the file</param>
            <returns>Returns true if folder was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.GetLibraryFileUploadStatus(System.String,System.String,System.String)">
            <summary>
            Get status for an upload file
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileId">The id of the file</param>
            <returns>Returns a list of FileUploadStatus objects</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.MoveLibraryFile(System.String,System.String,System.String,System.Collections.Generic.IList{System.String})">
            <summary>
            Move files to a different folder
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="folderId">The id of the folder</param>
            <param name="fileIds">List of file ids</param>
            <returns>Returns a list of FileMoveResult objects.</returns>
        </member>
        <member name="M:CTCT.Services.MyLibraryService.AddLibraryFilesMultipart(System.String,System.String,System.String,CTCT.Components.MyLibrary.FileType,System.String,System.String,CTCT.Components.MyLibrary.FileSource,System.Byte[])">
            <summary>
            Add files using the multipart content-type
            </summary>
            <param name="accessToken">Access token.</param>
            <param name="apiKey">The API key for the application</param>
            <param name="fileName">The file name and extension</param>
            <param name="fileType">The file type</param>
            <param name="folderId">The id of the folder</param>
            <param name="description">The description of the file</param>
            <param name="source">The source of the original file</param>
            <param name="data">The data contained in the file being uploaded</param>
            <returns>Returns the file Id associated with the uploaded file</returns>
        </member>
        <member name="T:CTCT.Services.FoldersSortBy">
            <summary>
            Folders sort by enum
            </summary>
        </member>
        <member name="F:CTCT.Services.FoldersSortBy.CREATED_DATE">
            <summary>
            Sort by date folder was created, ascending
            </summary>
        </member>
        <member name="F:CTCT.Services.FoldersSortBy.CREATED_DATE_DESC">
            <summary>
            Sort by date folder was last modified, descending
            </summary>
        </member>
        <member name="F:CTCT.Services.FoldersSortBy.MODIFIED_DATE">
            <summary>
            Sort by date folder was last modified, ascending
            </summary>
        </member>
        <member name="F:CTCT.Services.FoldersSortBy.MODIFIED_DATE_DESC">
            <summary>
            Sort by date folder was last modified, descending
            </summary>
        </member>
        <member name="F:CTCT.Services.FoldersSortBy.NAME">
            <summary>
            Sort by name (A to Z)
            </summary>
        </member>
        <member name="F:CTCT.Services.FoldersSortBy.NAME_DESC">
            <summary>
            Sort by name (Z to A)
            </summary>
        </member>
        <member name="T:CTCT.Services.FileTypes">
            <summary>
            File types enum
            </summary>
        </member>
        <member name="F:CTCT.Services.FileTypes.ALL">
            <summary>
            All files
            </summary>
        </member>
        <member name="F:CTCT.Services.FileTypes.IMAGES">
            <summary>
            Image files
            </summary>
        </member>
        <member name="F:CTCT.Services.FileTypes.DOCUMENTS">
            <summary>
            Document files
            </summary>
        </member>
        <member name="T:CTCT.Services.TrashSortBy">
            <summary>
            Trash folders sort by enum
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.CREATED_DATE">
            <summary>
            Sort by date folder was created, ascending
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.CREATED_DATE_DESC">
            <summary>
            Sort by date folder was last modified, descending
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.MODIFIED_DATE">
            <summary>
            Sort by date folder was last modified, ascending
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.MODIFIED_DATE_DESC">
            <summary>
            Sort by date folder was last modified, descending
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.NAME">
            <summary>
            Sort by name (A to Z)
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.NAME_DESC">
            <summary>
            Sort by name (Z to A)
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.SIZE">
            <summary>
            Sort by file size, smallest to largest
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.SIZE_DESC">
            <summary>
            Sort by file size, largest to smallest
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.DIMENSION">
            <summary>
            Sort by file domensions, smallest to largest
            </summary>
        </member>
        <member name="F:CTCT.Services.TrashSortBy.DIMENSION_DESC">
            <summary>
            Sort by file dimenstiona, largest to smallest
            </summary>
        </member>
        <member name="T:CTCT.Services.FilesSources">
            <summary>
            File source enum
            </summary>
        </member>
        <member name="F:CTCT.Services.FilesSources.ALL">
            <summary>
            Files from all sources
            </summary>
        </member>
        <member name="F:CTCT.Services.FilesSources.MyComputer">
            <summary>
            Computer source
            </summary>
        </member>
        <member name="F:CTCT.Services.FilesSources.StockImage">
            <summary>
            StockImage source
            </summary>
        </member>
        <member name="F:CTCT.Services.FilesSources.Facebook">
            <summary>
            Facebook source - MyLibrary Plus customers only
            </summary>
        </member>
        <member name="F:CTCT.Services.FilesSources.Instagram">
            <summary>
            Istagram source - MyLibrary Plus customers only
            </summary>
        </member>
        <member name="F:CTCT.Services.FilesSources.Shutterstock">
            <summary>
            Shutterstock source
            </summary>
        </member>
        <member name="F:CTCT.Services.FilesSources.Mobile">
            <summary>
            Mobile source
            </summary>
        </member>
        <member name="T:CTCT.Util.Config">
            <summary>
            Configuration structure.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.HeaderAccept">
            <summary>
            Accept header value.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.HeaderContentType">
            <summary>
            ContentType header value.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.HeaderUserAgent">
            <summary>
            UserAgent header value.
            </summary>
        </member>
        <member name="M:CTCT.Util.Config.ConstructUrl(System.String,System.Object[],System.Object[])">
            <summary>
            Creates the URL for API access.
            </summary>
            <param name="urlPart">URL part.</param>
            <param name="prms">Additional parameters for URL formatting.</param>
            <param name="queryList">Query parameters to add to the URL.</param>
            <returns>Returns the URL with all specified query parameters.</returns>
        </member>
        <member name="T:CTCT.Util.Config.Endpoints">
            <summary>
            REST endpoints.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.BaseUrl">
            <summary>
            API access URL.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.Activity">
            <summary>
            Access an activity.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.Activities">
            <summary>
            List activities.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ExportContactsActivity">
            <summary>
            Export contacts linked to an activity.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ClearListsActivity">
            <summary>
            Clear the list of activities.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.RemoveFromListsActivity">
            <summary>
            Remove from list.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.AddContactsActivity">
            <summary>
            Add contacts to activities.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.Contact">
            <summary>
            Access a contact.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.Contacts">
            <summary>
            Get all contacts.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.Lists">
            <summary>
            Get all lists.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.List">
            <summary>
            Access a specified list.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ListContacts">
            <summary>
            Get the list of contacts from a list.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactLists">
            <summary>
            Get contact lists.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactList">
            <summary>
            Get a list from contact lists.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.Campaigns">
            <summary>
            Get campaigns.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.Campaign">
            <summary>
            Access a campaign
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignSchedules">
            <summary>
            Campaign schedules.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignSchedule">
            <summary>
            Campaign schedule.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTestSends">
            <summary>
            Campaign test sends.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTrackingSummary">
            <summary>
            Campaign tracking summary.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTrackingBounces">
            <summary>
            Campaign tracking bounces.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTrackingClicks">
            <summary>
            Campaign tracking clicks.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTrackingClicksForLink">
            <summary>
            Campaign tracking clicks for a specific link.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTrackingForwards">
            <summary>
            Campaign tracking forwards.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTrackingOpens">
            <summary>
            Campaign tracking opens.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTrackingSends">
            <summary>
            Campaign tracking sends.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTrackingUnsubscribes">
            <summary>
            Campaign tracking unsubscribes.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.CampaignTrackingLink">
            <summary>
            Campaign tracking link.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingActivities">
            <summary>
            Contact tracking activities.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingEmailCampaignActivities">
            <summary>
            Contact tracking activities by email campaign.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingSummary">
            <summary>
            Contact tracking summary.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingBounces">
            <summary>
            Contact tracking bounces.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingClicks">
            <summary>
            Contact tracking clicks.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingForwards">
            <summary>
            Contact tracking forwards.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingOpens">
            <summary>
            Contact tracking opens.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingSends">
            <summary>
            Contact tracking sends.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingUnsubscribes">
            <summary>
            Contact tracking unsubscribes.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ContactTrackingLink">
            <summary>
            Contact tracking link.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.AccountVerifiedEmailAddressess">
            <summary>
            Account verified email addresses link
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.AccountSummaryInformation">
            <summary>
            Account Summary Information
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.MyLibraryInfo">
            <summary>
            MyLibrary information
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.MyLibraryFolders">
            <summary>
            MyLibrary folders
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.MyLibraryFolder">
            <summary>
             Access a specified folder
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.MyLibraryTrash">
            <summary>
            Files in Trash folder
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.MyLibraryFiles">
            <summary>
            MyLibrray files
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.MyLibraryFile">
            <summary>
            MyLibrary file
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.MyLibraryFolderFiles">
            <summary>
            MyLibrary files for a specific folder
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.MyLibraryFileUploadStatus">
            <summary>
            MyLibrary file upload status
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.EventSpots">
            <summary>
            EventSpot Events Collection Endpoint
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.EventFees">
            <summary>
            Individual Event Fees Endpoint
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.EventPromocode">
            <summary>
            Individual Promocode Endpoint
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.EventRegistrant">
            <summary>
            Individual Event Registrant Endpoint
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.EventItem">
            <summary>
            Event Item Endpoint
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Endpoints.ItemAttribute">
            <summary>
            Item Attribute Endpoint
            </summary>
        </member>
        <member name="T:CTCT.Util.Config.ActivitiesColumns">
            <summary>
            Column names used with bulk activities.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.Email">
            <summary>
            Email address.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.FirstName">
            <summary>
            First name.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.MiddleName">
            <summary>
            Middle name.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.LastName">
            <summary>
            Last name.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.JobTitle">
            <summary>
            Job title.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CompanyName">
            <summary>
            Company name.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.WorkPhone">
            <summary>
            Work phone.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.HomePhone">
            <summary>
            Home phone.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.Address1">
            <summary>
            Address line 1.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.Address2">
            <summary>
            Address line 2.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.Address3">
            <summary>
            Address line 3.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.City">
            <summary>
            City.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.State">
            <summary>
            State.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.StateProvince">
            <summary>
            US state or province.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.Country">
            <summary>
            Country.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.PostalCode">
            <summary>
            Postal code.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.SubPostalCode">
            <summary>
            Sub-postal code.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField1">
            <summary>
            Custom field 1.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField2">
            <summary>
            Custom field 2.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField3">
            <summary>
            Custom field 3.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField4">
            <summary>
            Custom field 4.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField5">
            <summary>
            Custom field 5.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField6">
            <summary>
            Custom field 6.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField7">
            <summary>
            Custom field 7.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField8">
            <summary>
            Custom field 8.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField9">
            <summary>
            Custom field 9.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField10">
            <summary>
            Custom field 10.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField11">
            <summary>
            Custom field 11.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField12">
            <summary>
            Custom field 12.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField13">
            <summary>
            Custom field 13.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField14">
            <summary>
            Custom field 14.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.ActivitiesColumns.CustomField15">
            <summary>
            Custom field 15.
            </summary>
        </member>
        <member name="T:CTCT.Util.Config.Login">
            <summary>
            Login related configuration options.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Login.BaseUrl">
            <summary>
            Login base URL.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Login.LoginEndpoint">
            <summary>
            Login endpoint.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Login.Host">
            <summary>
            Request host.
            </summary>
        </member>
        <member name="T:CTCT.Util.Config.Errors">
            <summary>
            Errors to be returned for various exceptions.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.ContactOrId">
            <summary>
            Contact or id error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.ListOrId">
            <summary>
            List or id error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.ActivityOrId">
            <summary>
            Activity or id error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.ScheduleOrId">
            <summary>
            Schedule or id error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.CampaignTrackingOrId">
            <summary>
            CampaignTracking or id error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.ContactTrackingOrId">
            <summary>
            ContactTracking or id error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.EmailCampaignOrId">
            <summary>
            EmailCampaign or id error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.UpdateId">
            <summary>
            Update contact without ID error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.FileNameNull">
            <summary>
            FileName null error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.FileNull">
            <summary>
            File null error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.FileTypeInvalid">
            <summary>
            File type invalid error.
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.MyLibraryOrId">
            <summary>
            MyLibrary item or id error
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.FileIdNull">
            <summary>
            File Ids null error
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.FieldNull">
            <summary>
            Field null error
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.InvalidId">
            <summary>
            EventSpot id error
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.ObjectNull">
            <summary>
            Objects null
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.InvalidWebhook">
            <summary>
            Invalid Webhook error
            </summary>
        </member>
        <member name="F:CTCT.Util.Config.Errors.NoClientSecret">
            <summary>
            Client Secret null error
            </summary>
        </member>
        <member name="T:CTCT.Util.CUrlRequestError">
            <summary>
            Class for holding the URL request error.
            </summary>
        </member>
        <member name="M:CTCT.Util.CUrlRequestError.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="P:CTCT.Util.CUrlRequestError.Key">
            <summary>
            Gets or sets the error key.
            </summary>
        </member>
        <member name="P:CTCT.Util.CUrlRequestError.Message">
            <summary>
            Gets or sets the error message.
            </summary>
        </member>
        <member name="T:CTCT.Util.CUrlResponse">
            <summary>
            URL response class.
            </summary>
        </member>
        <member name="M:CTCT.Util.CUrlResponse.#ctor">
            <summary>
            Class constructor.
            </summary>
        </member>
        <member name="M:CTCT.Util.CUrlResponse.GetErrorMessage">
            <summary>
            Returns the list of errors.
            </summary>
            <returns>Returns formatted error message.</returns>
        </member>
        <member name="M:CTCT.Util.CUrlResponse.Get``1">
            <summary>
            Returns the object represented by the JSON string.
            </summary>
            <typeparam name="T">Object type to return.</typeparam>
            <returns>Returns the object from its JSON representation.</returns>
        </member>
        <member name="P:CTCT.Util.CUrlResponse.Body">
            <summary>
            Requests body.
            </summary>
        </member>
        <member name="P:CTCT.Util.CUrlResponse.IsError">
            <summary>
            Returns true if error occur.
            </summary>
        </member>
        <member name="P:CTCT.Util.CUrlResponse.Info">
            <summary>
            List of errors.
            </summary>
        </member>
        <member name="P:CTCT.Util.CUrlResponse.HasData">
            <summary>
            Returns true if valid data exists.
            </summary>
        </member>
        <member name="P:CTCT.Util.CUrlResponse.StatusCode">
            <summary>
            Response status code.
            </summary>
        </member>
        <member name="P:CTCT.Util.CUrlResponse.Headers">
            <summary>
            Headers dictionary.
            </summary>
        </member>
        <member name="T:CTCT.Util.Extensions">
            <summary>
            Extansions class.
            </summary>
        </member>
        <member name="F:CTCT.Util.Extensions.ISO8601">
            <summary>
            ISO-8601 date time format string.
            </summary>
        </member>
        <member name="M:CTCT.Util.Extensions.ToISO8601String(System.Nullable{System.DateTime})">
            <summary>
            Converts a DateTime object to an ISO8601 representation.
            </summary>
            <param name="dateTime">DateTime.</param>
            <returns>Returns the ISO8601 string representation for the provided datetime.</returns>
        </member>
        <member name="M:CTCT.Util.Extensions.FromISO8601String(System.String)">
            <summary>
            Gets the DateTime from an ISO8601 string.
            </summary>
            <param name="str">String.</param>
            <returns>Returns a datetime object.</returns>
        </member>
        <member name="M:CTCT.Util.Extensions.ToJSON(System.Collections.Generic.IList{System.String})">
            <summary>
            Converts a list of strings to JSON representation.
            </summary>
            <param name="list">The string list.</param>
            <returns>Returns the JSON representation of the list.</returns>
        </member>
        <member name="M:CTCT.Util.Extensions.ToJSON``1(System.Collections.Generic.IList{``0})">
            <summary>
            Converts a list T of type Component to JSON representation.
            </summary>
            <typeparam name="T">Type of Component</typeparam>
            <param name="list">The string list.</param>
            <returns>Returns the JSON representation of the list.</returns>
        </member>
        <member name="M:CTCT.Util.Extensions.ToEnum``1(System.String)">
            <summary>
            Converts a string to its enum representation.
            </summary>
            <typeparam name="T">Enum type.</typeparam>
            <param name="s">String to convert.</param>
            <returns>Returns the enum value.</returns>
        </member>
        <member name="T:CTCT.Util.IRestClient">
            <summary>
            Interface for issuing HTTP requests.
            </summary>
        </member>
        <member name="M:CTCT.Util.IRestClient.Get(System.String,System.String,System.String)">
            <summary>
            Make an Http GET request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.IRestClient.Post(System.String,System.String,System.String,System.String)">
            <summary>
            Make an Http POST request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="data">Data to send with request.</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.IRestClient.Patch(System.String,System.String,System.String,System.String)">
            <summary>
            Make an Http PATCH request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="data">Data to send with request.</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.IRestClient.PostMultipart(System.String,System.String,System.String,System.Byte[])">
            <summary>
            Make an Http POST Multipart request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="data">Data to send with request.</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.IRestClient.Put(System.String,System.String,System.String,System.String)">
            <summary>
            Make an Http PUT request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="data">Data to send with request.</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.IRestClient.Delete(System.String,System.String,System.String)">
            <summary>
            Make an Http DELETE request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="T:CTCT.Util.MultipartBuilder">
            <summary>
            Multipart Builder class implementation
            </summary>
        </member>
        <member name="F:CTCT.Util.MultipartBuilder.MULTIPART_BOUNDARY">
            <summary>
            Boundary used for Multipart activities
            </summary>
        </member>
        <member name="M:CTCT.Util.MultipartBuilder.CreateMultipartContent(System.String,System.Byte[],System.Collections.Generic.IList{System.String},System.String,System.String,System.String,System.String)">
            <summary>
            Create multipart content in binary format
            </summary>
            <param name="fileName">The name of the file</param>
            <param name="fileContent">The content of the file</param>
            <param name="lists">List of contact list Ids to add/remove contacts to</param>
            <param name="fileType">The type of the file</param>
            <param name="folderId">The id of the folder</param>
            <param name="description">The file description</param>
            <param name="source">The file source</param>
            <returns>Returns a byte array used for request</returns>
        </member>
        <member name="T:CTCT.Util.RestClient">
            <summary>
            Class implementation of REST client.
            </summary>
        </member>
        <member name="M:CTCT.Util.RestClient.Get(System.String,System.String,System.String)">
            <summary>
            Make an Http GET request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.RestClient.Post(System.String,System.String,System.String,System.String)">
            <summary>
            Make an Http POST request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="data">Data to send with request.</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.RestClient.Patch(System.String,System.String,System.String,System.String)">
            <summary>
            Make an Http PATCH request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="data">Data to send with request.</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.RestClient.PostMultipart(System.String,System.String,System.String,System.Byte[])">
            <summary>
            Make an HTTP Post Multipart request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="data">Data to send with request.</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.RestClient.Put(System.String,System.String,System.String,System.String)">
            <summary>
            Make an Http PUT request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <param name="data">Data to send with request.</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="M:CTCT.Util.RestClient.Delete(System.String,System.String,System.String)">
            <summary>
            Make an Http DELETE request.
            </summary>
            <param name="url">Request URL.</param>
            <param name="accessToken">Constant Contact OAuth2 access token</param>
            <param name="apiKey">The API key for the application</param>
            <returns>The response body, http info, and error (if one exists).</returns>
        </member>
        <member name="T:CTCT.Webhooks.CTCTWebhookUtil">
            <summary>
            Main Webhook Utility class 
            This is meant to be used by users to validate and parse Webhooks received from ConstantContact
            </summary>
        </member>
        <member name="F:CTCT.Webhooks.CTCTWebhookUtil.HmacHeaderName">
            <summary>
            Header name of the HmacSha256 hash
            </summary>
        </member>
        <member name="M:CTCT.Webhooks.CTCTWebhookUtil.#ctor(System.String)">
            <summary>
            Class constructor
            </summary>
            <param name="clientSecret">Client secret key</param>
        </member>
        <member name="M:CTCT.Webhooks.CTCTWebhookUtil.GetBillingChangeNotification(System.String,System.String)">
            <summary>
            Validates and parses the bodyMessage into BillingChangeNotification
            </summary>
            <param name="xCtctHmacSHA256">The value in the x-ctct-hmac-sha256 header</param>
            <param name="bodyMessage">The body message from the POST received from ConstantContact in Webhook callback</param>
            <returns>BillingChangeNotification object corresponding to bodyMessage in case of success; an exception is thrown otherwise.</returns>
        </member>
        <member name="M:CTCT.Webhooks.CTCTWebhookUtil.IsValidWebhook(System.String,System.String)">
            <summary>
            Validates a Webhook message
            </summary>
            <param name="xCtctHmacSHA256">The value in the x-ctct-hmac-sha256 header</param>
            <param name="bodyMessage">The body message from the POST received from ConstantContact in Webhook callback</param>
            <returns>True if in case of success; False if the Webhook is invalid</returns>
        </member>
        <member name="P:CTCT.Webhooks.CTCTWebhookUtil.ClientSecret">
            <summary>
            The client secret associated with the api key
            </summary>
        </member>
        <member name="T:CTCT.Util.StringEnum">
            <summary>
            Helper class for working with 'extended' enums using <see cref="T:CTCT.Util.StringValueAttribute"/> attributes.
            </summary>
        </member>
        <member name="M:CTCT.Util.StringEnum.#ctor(System.Type)">
            <summary>
            Creates a new <see cref="T:CTCT.Util.StringEnum"/> instance.
            </summary>
            <param name="enumType">Enum type.</param>
        </member>
        <member name="M:CTCT.Util.StringEnum.GetStringValue(System.String)">
            <summary>
            Gets the string value associated with the given enum value.
            </summary>
            <param name="valueName">Name of the enum value.</param>
            <returns>String Value</returns>
        </member>
        <member name="M:CTCT.Util.StringEnum.GetStringValues">
            <summary>
            Gets the string values associated with the enum.
            </summary>
            <returns>String value array</returns>
        </member>
        <member name="M:CTCT.Util.StringEnum.GetListValues">
            <summary>
            Gets the values as a 'bindable' list datasource.
            </summary>
            <returns>IList for data binding</returns>
        </member>
        <member name="M:CTCT.Util.StringEnum.IsStringDefined(System.String)">
            <summary>
            Return the existence of the given string value within the enum.
            </summary>
            <param name="stringValue">String value.</param>
            <returns>Existence of the string value</returns>
        </member>
        <member name="M:CTCT.Util.StringEnum.IsStringDefined(System.String,System.Boolean)">
            <summary>
            Return the existence of the given string value within the enum.
            </summary>
            <param name="stringValue">String value.</param>
            <param name="ignoreCase">Denotes whether to conduct a case-insensitive match on the supplied string value</param>
            <returns>Existence of the string value</returns>
        </member>
        <member name="M:CTCT.Util.StringEnum.GetStringValue(System.Enum)">
            <summary>
            Gets a string value for a particular enum value.
            </summary>
            <param name="value">Value.</param>
            <returns>String Value associated via a <see cref="T:CTCT.Util.StringValueAttribute"/> attribute, or null if not found.</returns>
        </member>
        <member name="M:CTCT.Util.StringEnum.Parse(System.Type,System.String)">
            <summary>
            Parses the supplied enum and string value to find an associated enum value (case sensitive).
            </summary>
            <param name="type">Type.</param>
            <param name="stringValue">String value.</param>
            <returns>Enum value associated with the string value, or null if not found.</returns>
        </member>
        <member name="M:CTCT.Util.StringEnum.Parse(System.Type,System.String,System.Boolean)">
            <summary>
            Parses the supplied enum and string value to find an associated enum value.
            </summary>
            <param name="type">Type.</param>
            <param name="stringValue">String value.</param>
            <param name="ignoreCase">Denotes whether to conduct a case-insensitive match on the supplied string value</param>
            <returns>Enum value associated with the string value, or null if not found.</returns>
        </member>
        <member name="M:CTCT.Util.StringEnum.IsStringDefined(System.Type,System.String)">
            <summary>
            Return the existence of the given string value within the enum.
            </summary>
            <param name="stringValue">String value.</param>
            <param name="enumType">Type of enum</param>
            <returns>Existence of the string value</returns>
        </member>
        <member name="M:CTCT.Util.StringEnum.IsStringDefined(System.Type,System.String,System.Boolean)">
            <summary>
            Return the existence of the given string value within the enum.
            </summary>
            <param name="stringValue">String value.</param>
            <param name="enumType">Type of enum</param>
            <param name="ignoreCase">Denotes whether to conduct a case-insensitive match on the supplied string value</param>
            <returns>Existence of the string value</returns>
        </member>
        <member name="P:CTCT.Util.StringEnum.EnumType">
            <summary>
            Gets the underlying enum type for this instance.
            </summary>
            <value></value>
        </member>
        <member name="T:CTCT.Util.StringValueAttribute">
            <summary>
            Simple attribute class for storing String Values
            </summary>
        </member>
        <member name="M:CTCT.Util.StringValueAttribute.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:CTCT.Util.StringValueAttribute"/> instance.
            </summary>
            <param name="value">Value.</param>
        </member>
        <member name="P:CTCT.Util.StringValueAttribute.Value">
            <summary>
            Gets the value.
            </summary>
            <value></value>
        </member>
        <member name="T:CTCT.Webhooks.Helper.WebHookValidator">
            <summary>
            Represents a helper class that validates a Webhook
            </summary>
        </member>
        <member name="M:CTCT.Webhooks.Helper.WebHookValidator.#ctor(System.String,System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="xCtctHmacSHA256">Header hash represented as base64 string</param>
            <param name="body">Body message</param>
            <param name="sharedSecret">Client secret</param>
        </member>
        <member name="M:CTCT.Webhooks.Helper.WebHookValidator.IsValid">
            <summary>
            To verify that the request came from Constant Contact, compute the HMAC digest and compare it to the value in the x-ctct-hmac-sha256 header.
            If they match, you can be sure that the webhook was sent by Constant Contact and the message has not been compromised.
            </summary>
            <returns>True if webhook is valid; False otherwise</returns>
        </member>
        <member name="M:CTCT.Webhooks.Helper.WebHookValidator.HashHMAC(System.String,System.String)">
            <summary>
            Compute HMACSHA256 hash based on message and secret
            </summary>
            <param name="message">Message</param>
            <param name="secret">Secret key</param>
            <returns>Base64 hash string</returns>
        </member>
        <member name="P:CTCT.Webhooks.Helper.WebHookValidator.CtctHttpHeader">
            <summary>
            HTTP header hash
            </summary>
        </member>
        <member name="P:CTCT.Webhooks.Helper.WebHookValidator.Body">
            <summary>
            HTTP body
            </summary>
        </member>
        <member name="P:CTCT.Webhooks.Helper.WebHookValidator.SharedSecret">
            <summary>
            Client secret key
            </summary>
        </member>
        <member name="T:CTCT.Webhooks.Model.BillingChangeNotification">
            <summary>
            Represents a notification object sent to client through Webhooks Notifications.
            </summary>
        </member>
        <member name="M:CTCT.Webhooks.Model.BillingChangeNotification.ToString">
            <summary>
            ToString override
            </summary>
            <returns>A string with details about the notification</returns>
        </member>
        <member name="P:CTCT.Webhooks.Model.BillingChangeNotification.Url">
            <summary>
            Notification url
            </summary>
        </member>
        <member name="P:CTCT.Webhooks.Model.BillingChangeNotification.EventTypeString">
            <summary>
            Event type string
            </summary>
        </member>
        <member name="P:CTCT.Webhooks.Model.BillingChangeNotification.EventType">
            <summary>
            Event type
            </summary>
        </member>
        <member name="T:CTCT.Webhooks.Model.BillingChangeNotificationType">
            <summary>
            Event types for billing change notification.
            </summary>
        </member>
    </members>
</doc>
