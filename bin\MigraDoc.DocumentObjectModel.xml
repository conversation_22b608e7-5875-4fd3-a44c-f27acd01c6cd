<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MigraDoc.DocumentObjectModel</name>
    </assembly>
    <members>
        <member name="T:MigraDoc.DocumentObjectModel.Shading">
            <summary>
            Shading represents the background color of a document object.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.DocumentObject">
            <summary>
            Base class of all objects of the MigraDoc Document Object Model.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.#ctor">
            <summary>
            Initializes a new instance of the DocumentObject class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the DocumentObject class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.Clone">
            <summary>
            Creates a deep copy of the DocumentObject. The parent of the new object is null.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.CreateValue(System.String)">
            <summary>
            Creates an object using the default constructor.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts DocumentObject into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.GetValue(System.String)">
            <summary>
            Returns the value with the specified name.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.GetValue(System.String,MigraDoc.DocumentObjectModel.Internals.GV)">
            <summary>
            Returns the value with the specified name and value flags.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.SetValue(System.String,System.Object)">
            <summary>
            Sets the given value and sets its parent afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.HasValue(System.String)">
            <summary>
            Determines whether this instance has a value of the given name.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.IsNull(System.String)">
            <summary>
            Determines whether the value of the given name is null.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.SetNull(System.String)">
            <summary>
            Resets the value of the given name, i.e. IsNull(name) will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.SetNull">
            <summary>
            Resets this instance, i.e. IsNull() will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.SetParent(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Sets the parent of the specified value.
            If a parent is already set, an ArgumentException will be thrown.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObject.ResetCachedValues">
            <summary>
            When overridden in a derived class resets cached values
            (like column index).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObject.Parent">
            <summary>
            Gets the parent object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObject.Document">
            <summary>
            Gets the document of the object, or null, if the object is not associated with a document.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObject.Section">
            <summary>
            Gets the section of the object, or null, if the object is not associated with a section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObject.Tag">
            <summary>
            Gets or sets a value that contains arbitrary information about this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObject.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shading.#ctor">
            <summary>
            Initializes a new instance of the Shading class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shading.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Shading class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shading.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shading.Clear">
            <summary>
            Clears the Shading object. Additionally 'Shading = null'
            is written to the DDL stream when serialized.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shading.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Shading into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shading.Visible">
            <summary>
            Gets or sets a value indicating whether the shading is visible.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shading.Color">
            <summary>
            Gets or sets the shading color.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shading.IsCleared">
            <summary>
            Gets the information if the shading is marked as cleared. Additionally 'Shading = null'
            is written to the DDL stream when serialized.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shading.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Footnote">
            <summary>
            Represents a footnote in a paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.IVisitable.AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.#ctor">
            <summary>
            Initializes a new instance of the Footnote class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Footnote class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.#ctor(System.String)">
            <summary>
            Initializes a new instance of the Footnote class with a text the Footnote shall content.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.AddParagraph">
            <summary>
            Adds a new paragraph to the footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.AddParagraph(System.String)">
            <summary>
            Adds a new paragraph with the specified text to the footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.AddTable">
            <summary>
            Adds a new table to the footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.AddImage(System.String)">
            <summary>
            Adds a new image to the footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.Add(MigraDoc.DocumentObjectModel.Paragraph)">
            <summary>
            Adds a new paragraph to the footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.Add(MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Adds a new table to the footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.Add(MigraDoc.DocumentObjectModel.Shapes.Image)">
            <summary>
            Adds a new image to the footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Footnote into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Footnote.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Footnote.Elements">
            <summary>
            Gets the collection of paragraph elements that defines the footnote.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Footnote.Reference">
            <summary>
            Gets or sets the character to be used to mark the footnote.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Footnote.Style">
            <summary>
            Gets or sets the style name of the footnote.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Footnote.Format">
            <summary>
            Gets the format of the footnote.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Footnote.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Visitors.MergedCellList">
            <summary>
            Represents a merged list of cells of a table.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.#ctor(MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Initializes a new instance of the MergedCellList class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.Init(MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Initializes this instance from a table.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.IsAlreadyCovered(MigraDoc.DocumentObjectModel.Tables.Cell)">
            <summary>
            Returns whether the given cell is already covered by a preceding cell in this instance.
            </summary>
            <remarks>
            Help function for Init().
            </remarks>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.GetEnumerator">
            <summary>
            Gets the Enumerator for this list.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.GetEffectiveBorders(MigraDoc.DocumentObjectModel.Tables.Cell)">
            <summary>
            Gets a borders object that should be used for rendering.
            </summary>
            <exception cref="T:System.ArgumentException">
              Thrown when the cell is not in this list.
              This situation occurs if the given cell is merged "away" by a previous one.
            </exception>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.GetCoveringCell(MigraDoc.DocumentObjectModel.Tables.Cell)">
            <summary>
            Gets the cell that covers the given cell by merging. Usually the cell itself if not merged.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.GetBorderFromBorders(MigraDoc.DocumentObjectModel.Borders,MigraDoc.DocumentObjectModel.BorderType)">
            <summary>
            Returns the border of the given borders-object of the specified type (top, bottom, ...).
            If that border doesn't exist, it returns a new border object that inherits all properties from the given borders object
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.GetEffectiveBorderWidth(MigraDoc.DocumentObjectModel.Borders,MigraDoc.DocumentObjectModel.BorderType)">
            <summary>
            Returns the width of the border at the specified position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.GetNeighbor(System.Int32,MigraDoc.DocumentObjectModel.Visitors.MergedCellList.NeighborPosition)">
            <summary>
            Gets the specified cell's uppermost neighbor at the specified position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.IsNeighbor(MigraDoc.DocumentObjectModel.Tables.Cell,MigraDoc.DocumentObjectModel.Tables.Cell,MigraDoc.DocumentObjectModel.Visitors.MergedCellList.NeighborPosition)">
            <summary>
            Returns whether cell2 is a neighbor of cell1 at the specified position.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.Item(System.Int32)">
            <summary>
            Gets the cell at the specified position.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.Enumerator">
            <summary>
            Enumerator that can iterate through the MergedCellList.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Visitors.MergedCellList.NeighborPosition">
            <summary>
            Enumeration of neighbor positions of cells in a table.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.TextFrame">
            <summary>
            Represents a text frame that can be freely placed.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Shape">
            <summary>
            Base Class for all positionable Classes.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Shape.#ctor">
            <summary>
            Initializes a new instance of the Shape class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Shape.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Shape class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Shape.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Shape.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Shape.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Shape into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.WrapFormat">
            <summary>
            Gets or sets the wrapping format of the shape.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.RelativeVertical">
            <summary>
            Gets or sets the reference point of the Top property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.RelativeHorizontal">
            <summary>
            Gets or sets the reference point of the Left property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.Top">
            <summary>
            Gets or sets the position of the top side of the shape.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.Left">
            <summary>
            Gets or sets the position of the left side of the shape.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.LineFormat">
            <summary>
            Gets the line format of the shape's border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.FillFormat">
            <summary>
            Gets the background filling format of the shape.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.Height">
            <summary>
            Gets or sets the height of the shape.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.Width">
            <summary>
            Gets or sets the width of the shape.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Shape.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.#ctor">
            <summary>
            Initializes a new instance of the TextFrame class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the TextFrame class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.AddParagraph">
            <summary>
            Adds a new paragraph to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.AddParagraph(System.String)">
            <summary>
            Adds a new paragraph with the specified text to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.AddChart(MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType)">
            <summary>
            Adds a new chart with the specified type to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.AddChart">
            <summary>
            Adds a new chart to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.AddTable">
            <summary>
            Adds a new table to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.AddImage(System.String)">
            <summary>
            Adds a new Image to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.Add(MigraDoc.DocumentObjectModel.Paragraph)">
            <summary>
            Adds a new paragraph to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.Add(MigraDoc.DocumentObjectModel.Shapes.Charts.Chart)">
            <summary>
            Adds a new chart to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.Add(MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Adds a new table to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.Add(MigraDoc.DocumentObjectModel.Shapes.Image)">
            <summary>
            Adds a new image to the text frame.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TextFrame.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts TextFrame into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TextFrame.MarginLeft">
            <summary>
            Gets or sets the Margin between the textframes content and its left edge.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TextFrame.MarginRight">
            <summary>
            Gets or sets the Margin between the textframes content and its right edge.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TextFrame.MarginTop">
            <summary>
            Gets or sets the Margin between the textframes content and its top edge.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TextFrame.MarginBottom">
            <summary>
            Gets or sets the Margin between the textframes content and its bottom edge.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TextFrame.Orientation">
            <summary>
            Gets or sets the text orientation for the texframe content.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TextFrame.Elements">
            <summary>
            The document elements that build the textframe's content.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TextFrame.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.PictureFormat">
            <summary>
            A PictureFormat object.
            Used to set more detailed image attributes
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.PictureFormat.#ctor">
            <summary>
            Initializes a new instance of the PictureFormat class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.PictureFormat.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the PictureFormat class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.PictureFormat.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.PictureFormat.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts PictureFormat into DDL
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.PictureFormat.CropLeft">
            <summary>
            Gets or sets the part cropped from the left of the image.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.PictureFormat.CropRight">
            <summary>
            Gets or sets the part cropped from the right of the image.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.PictureFormat.CropTop">
            <summary>
            Gets or sets the part cropped from the top of the image.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.PictureFormat.CropBottom">
            <summary>
            Gets or sets the part cropped from the bottom of the image.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.PictureFormat.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.PageRefField">
            <summary>
            PageRefField is used to reference the page number of a bookmark in the document.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.NumericFieldBase">
            <summary>
            NumericFieldBase serves as a base for Numeric fields, which are: 
            NumPagesField, PageField, PageRefField, SectionField, SectionPagesField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumericFieldBase.#ctor">
            <summary>
            Initializes a new instance of the NumericFieldBase class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumericFieldBase.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the NumericFieldBase class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumericFieldBase.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumericFieldBase.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumericFieldBase.IsValidFormat(System.String)">
            <summary>
            Determines whether the format is valid for numeric fields.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumericFieldBase.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.NumericFieldBase.Format">
            <summary>
            Gets or sets the format of the number.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.PageRefField.#ctor">
            <summary>
            Initializes a new instance of the PageRefField class.
            </summary>    
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.PageRefField.#ctor(System.String)">
            <summary>
            Initializes a new instance of the PageRefField class with the necessary bookmark name.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.PageRefField.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the PageRefField class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.PageRefField.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.PageRefField.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts PageRefField into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.PageRefField.Name">
            <summary>
            Gets or sets the bookmark name whose page is to be shown.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.PageRefField.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.NumPagesField">
            <summary>
            NumPagesField is used to reference the number of all pages in the document.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumPagesField.#ctor">
            <summary>
            Initializes a new instance of the NumPagesField class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumPagesField.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the NumPagesField class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumPagesField.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.NumPagesField.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts NumPagesField into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.NumPagesField.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.DateField">
            <summary>
            DateField is used to reference the date and time the printing starts.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.DateField.#ctor">
            <summary>
            Initializes a new instance of the DateField class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.DateField.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the DateField class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.DateField.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.DateField.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts DateField into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.DateField.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.DateField.Format">
            <summary>
            Gets or sets the format of the date.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.DateField.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.TextMeasurement">
            <summary>
            Provides functionality to measure the width of text during document design time.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TextMeasurement.#ctor(MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Initializes a new instance of the TextMeasurement class with the specified font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TextMeasurement.MeasureString(System.String,MigraDoc.DocumentObjectModel.UnitType)">
            <summary>
            Returns the size of the bounding box of the specified text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TextMeasurement.MeasureString(System.String)">
            <summary>
            Returns the size of the bounding box of the specified text in point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TextMeasurement.Realize">
            <summary>
            Initializes appropriate GDI+ objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.TextMeasurement.Font">
            <summary>
            Gets or sets the font used for measurement.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.TabAlignment">
            <summary>
            Determines the alignment of the tab.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabAlignment.Left">
            <summary>
            Tab will be left aligned.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabAlignment.Center">
            <summary>
            Tab will be centered.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabAlignment.Right">
            <summary>
            Tab will be right aligned.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabAlignment.Decimal">
            <summary>
            Positioned at the last dot or comma.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Orientation">
            <summary>
            Specifies the page orientation.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Orientation.Portrait">
            <summary>
            Page height is bigger than page width.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Orientation.Landscape">
            <summary>
            Page width is bigger than page height.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.TextOrientation">
            <summary>
            Specifies the orientation of the text in the TextFrame.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.TextOrientation.Horizontal">
            <summary>
            Horizontal orientation.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.TextOrientation.HorizontalRotatedFarEast">
            <summary>
            Horizontal orientation.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.TextOrientation.Upward">
            <summary>
            Vertical orientation (upward).
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.TextOrientation.Vertical">
            <summary>
            Vertical orientation (downward).
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.TextOrientation.VerticalFarEast">
            <summary>
            Vertical orientation (downward).
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.TextOrientation.Downward">
            <summary>
            Vertical orientation (downward).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.TokenType">
            <summary>
            The tokens used by DdlScanner/DdlParser.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.TokenType.None">
            <summary>
            White space or comment.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.TokenType.Identifier">
            <summary>
            Same as identifiers in C#, but not case sensitive.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.TokenType.KeyWord">
            <summary>
            Both «true» and «\bold» are keywords, case sensitive.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.TokenType.IntegerLiteral">
            <summary>
            Sample: «42»
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.TokenType.RealLiteral">
            <summary>
            Samples: «42.0», «42.», «.42»,...
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.TokenType.CharacterLiteral">
            <summary>
            Not used.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.TokenType.StringLiteral">
            <summary>
            Both «"text"» and «@"text with ""quotes"""».
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.TokenType.OperatorOrPunctuator">
            <summary>
            Samples: «.», «{», «+=»,...
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.TokenType.Text">
            <summary>
            Plain text. Possible after ReadText.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.Meta">
            <summary>
            Meta class for document objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the DomMeta class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.GetMeta(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Gets the meta object of the specified document object.
            </summary>
            <param name="documentObject">The document object the meta is returned for.</param>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.GetValue(MigraDoc.DocumentObjectModel.DocumentObject,System.String,MigraDoc.DocumentObjectModel.Internals.GV)">
            <summary>
            Gets the object specified by name from dom.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.SetValue(MigraDoc.DocumentObjectModel.DocumentObject,System.String,System.Object)">
            <summary>
            Sets the member of dom specified by name to val.
            If a member with the specified name does not exist an ArgumentException will be thrown.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.HasValue(System.String)">
            <summary>
            Determines whether this meta contains a value with the specified name.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.SetNull(MigraDoc.DocumentObjectModel.DocumentObject,System.String)">
            <summary>
            Sets the member of dom specified by name to null.
            If a member with the specified name does not exist an ArgumentException will be thrown.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.IsNull(MigraDoc.DocumentObjectModel.DocumentObject,System.String)">
            <summary>
            Determines whether the member of dom specified by name is null.
            If a member with the specified name does not exist an ArgumentException will be thrown.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.SetNull(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Sets all members of the specified dom to null.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.IsNull(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Determines whether all members of the specified dom are null. If dom contains no members IsNull
            returns true.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.Meta.AddValueDescriptors(MigraDoc.DocumentObjectModel.Internals.Meta,System.Type)">
            <summary>
            Adds a value descriptor for each field and property found in type to meta.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.Meta.Item(System.String)">
            <summary>
            Gets the DomValueDescriptor of the member specified by name from the DocumentObject.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.Meta.ValueDescriptors">
            <summary>
            Gets the DomValueDescriptorCollection of the DocumentObject.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.InfoField">
            <summary>
            InfoField is used to reference one of the DocumentInfo fields in the document.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.InfoField.#ctor">
            <summary>
            Initializes a new instance of the InfoField class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.InfoField.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the InfoField class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.InfoField.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.InfoField.IsValidName(System.String)">
            <summary>
            Determines whether the name is a valid InfoFieldType.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.InfoField.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.InfoField.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts InfoField into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.InfoField.Name">
            <summary>
            Gets or sets the name of the information to be shown in the field.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.InfoField.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Text">
            <summary>
            Represents text in a paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Text.#ctor">
            <summary>
            Initializes a new instance of the Text class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Text.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Text class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Text.#ctor(System.String)">
            <summary>
            Initializes a new instance of the Text class with a string as paragraph content.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Text.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Text.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Text into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Text.Content">
            <summary>
            Gets or sets the text content.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Text.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.ParagraphFormat">
            <summary>
            A ParagraphFormat represents the formatting of a paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.#ctor">
            <summary>
            Initializes a new instance of the ParagraphFormat class that can be used as a template.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the ParagraphFormat class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.AddTabStop(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Adds a TabStop object to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.AddTabStop(MigraDoc.DocumentObjectModel.Unit,MigraDoc.DocumentObjectModel.TabAlignment,MigraDoc.DocumentObjectModel.TabLeader)">
            <summary>
            Adds a TabStop object to the collection and sets its alignment and leader.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.AddTabStop(MigraDoc.DocumentObjectModel.Unit,MigraDoc.DocumentObjectModel.TabLeader)">
            <summary>
            Adds a TabStop object to the collection and sets its leader.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.AddTabStop(MigraDoc.DocumentObjectModel.Unit,MigraDoc.DocumentObjectModel.TabAlignment)">
            <summary>
            Adds a TabStop object to the collection and sets its alignment.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.RemoveTabStop(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Adds a TabStop object to the collection marked to remove the tab stop at
            the given position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.Add(MigraDoc.DocumentObjectModel.TabStop)">
            <summary>
            Adds a TabStop object to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.ClearAll">
            <summary>
            Clears all TapStop objects from the collection. Additionally 'TabStops = null'
            is written to the DDL stream when serialized.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts ParagraphFormat into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphFormat.Serialize(MigraDoc.DocumentObjectModel.Serializer,System.String,MigraDoc.DocumentObjectModel.ParagraphFormat)">
            <summary>
            Converts ParagraphFormat into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.Alignment">
            <summary>
            Gets or sets the Alignment of the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.Borders">
            <summary>
            Gets the Borders object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.FirstLineIndent">
            <summary>
            Gets or sets the indent of the first line in the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.Font">
            <summary>
            Gets or sets the Font object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.KeepTogether">
            <summary>
            Gets or sets a value indicating whether to keep all the paragraph's lines on the same page.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.KeepWithNext">
            <summary>
            Gets or sets a value indicating whether this and the next paragraph stay on the same page.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.LeftIndent">
            <summary>
            Gets or sets the left indent of the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.LineSpacing">
            <summary>
            Gets or sets the space between lines on the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.LineSpacingRule">
            <summary>
            Gets or sets the rule which is used to define the line spacing.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.ListInfo">
            <summary>
            Gets or sets the ListInfo object of the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.OutlineLevel">
            <summary>
            Gets or sets the out line level of the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.PageBreakBefore">
            <summary>
            Gets or sets a value indicating whether a page break is inserted before the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.RightIndent">
            <summary>
            Gets or sets the right indent of the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.Shading">
            <summary>
            Gets the shading object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.SpaceAfter">
            <summary>
            Gets or sets the space that's inserted after the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.SpaceBefore">
            <summary>
            Gets or sets the space that's inserted before the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.HasTabStops">
            <summary>
            Indicates whether the ParagraphFormat has a TabStops collection.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.TabStops">
            <summary>
            Get the TabStops collection.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.WidowControl">
            <summary>
            Gets or sets a value indicating whether a line from the paragraph stays alone in a page.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphFormat.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.HeadersFooters">
            <summary>
            Represents the collection of HeaderFooter objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeadersFooters.#ctor">
            <summary>
            Initializes a new instance of the HeadersFooters class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeadersFooters.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the HeadersFooters class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeadersFooters.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeadersFooters.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeadersFooters.HasHeaderFooter(MigraDoc.DocumentObjectModel.HeaderFooterIndex)">
            <summary>
            Determines whether a particular header or footer exists.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeadersFooters.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts HeadersFooters into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeadersFooters.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeadersFooters.IsHeader">
            <summary>
            Returns true if this collection contains headers, false otherwise.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeadersFooters.IsFooter">
            <summary>
            Returns true if this collection contains footers, false otherwise.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeadersFooters.EvenPage">
            <summary>
            Gets or sets the even page HeaderFooter of the HeadersFooters object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeadersFooters.FirstPage">
            <summary>
            Gets or sets the first page HeaderFooter of the HeadersFooters object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeadersFooters.Primary">
            <summary>
            Gets or sets the primary HeaderFooter of the HeadersFooters object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeadersFooters.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.UnitType">
            <summary>
            Specifies the measure of an Unit object.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.UnitType.Point">
            <summary>
            Measure is in points. A point represents 1/72 of an inch. 
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.UnitType.Centimeter">
            <summary>
            Measure is in centimeter. 
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.UnitType.Inch">
            <summary>
            Measure is in inch. 
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.UnitType.Millimeter">
            <summary>
            Measure is in millimeter. 
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.UnitType.Pica">
            <summary>
            Measure is in picas. A pica represents 12 points, i.e. 6 pica are one inch.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.ColorName">
            <summary>
            Internal color names.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.BreakType">
            <summary>
            Specifies the page break in a new section.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BreakType.BreakNextPage">
            <summary>
            Breaks at the next page.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BreakType.BreakEvenPage">
            <summary>
            Breaks at the next even page.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BreakType.BreakOddPage">
            <summary>
            Breaks at the next odd page.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.DdlScanner">
            <summary>
            DdlScanner
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.#ctor(System.String,System.String,MigraDoc.DocumentObjectModel.IO.DdlReaderErrors)">
            <summary>
            Initializes a new instance of the DdlScanner class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.#ctor(System.String,MigraDoc.DocumentObjectModel.IO.DdlReaderErrors)">
            <summary>
            Initializes a new instance of the DdlScanner class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.Init(System.String,System.String)">
            <summary>
            Initializes all members and prepares the scanner.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ReadCode">
            <summary>
            Reads to the next DDL token. Comments are ignored.
            </summary>
            <returns>
            Returns the current symbol.
            It is Symbol.Eof if the end of the DDL string is reached.
            </returns>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.PeekKeyword">
            <summary>
            Gets the next keyword at the current position without touching the DDL cursor.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.PeekKeyword(System.Int32)">
            <summary>
            Gets the next keyword without touching the DDL cursor.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.PeekPunctuator(System.Int32)">
            <summary>
            Gets the next punctuator terminal symbol without touching the DDL cursor.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.PeekSymbol">
            <summary>
            Gets the next symbol without touching the DDL cursor.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ReadText(System.Boolean)">
            <summary>
            Reads either text or \keyword from current position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IgnoreLineBreak">
            <summary>
            Returns whether the linebreak should be ignored, because the previous symbol is already a whitespace.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ReadPlainText(System.Boolean)">
            <summary>
            Read text from current position until block ends or \keyword occurs.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.MoveToCode">
            <summary>
            Moves to the next DDL token if Symbol is not set to a valid position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.MoveToParagraphContent">
            <summary>
            Moves to the first character the content of a paragraph starts with. Empty lines
            and comments are skipped. Returns true if such a character exists, and false if the
            paragraph ends without content.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.MoveToNextParagraphContentLine(System.Boolean)">
            <summary>
            Moves to the first character of the content of a paragraph beyond an EOL. 
            Returns true if such a character exists and belongs to the current paragraph.
            Returns false if a new line (at root level) or '}' occurs. If a new line caused
            the end of the paragraph, the DDL cursor is moved to the next valid content
            character or '}' respectively.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.MoveToNonWhiteSpaceOrEol">
            <summary>
            If the current character is not a white space, the function immediately returns it.
            Otherwise the DDL cursor is moved forward to the first non-white space or EOF.
            White spaces are SPACE, HT, VT, CR, and LF.???
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.MoveToNonWhiteSpace">
            <summary>
            If the current character is not a white space, the function immediately returns it.
            Otherwise the DDL cursor is moved forward to the first non-white space or EOF.
            White spaces are SPACE, HT, VT, CR, and LF.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.MoveBeyondEol">
            <summary>
            Moves to the first character beyond the next EOL. 
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ScanSingleLineComment">
            <summary>
            Reads a single line comment.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.GetTokenValueAsInt">
            <summary>
            Interpret current token as integer literal.
            </summary>
            <returns></returns>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.GetTokenValueAsUInt">
            <summary>
            Interpret current token as unsigned integer literal.
            </summary>
            <returns></returns>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.GetTokenValueAsReal">
            <summary>
            Interpret current token as real literal.
            </summary>
            <returns></returns>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ScanNextChar">
            <summary>
            Move DDL cursor one character further.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ScanToEol">
            <summary>
            Move DDL cursor to the next EOL (or EOF).
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.AppendAndScanNextChar">
            <summary>
            Appends current character to the token and reads next character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.AppendAndScanToEol">
            <summary>
            Appends all next characters to current token until end of line or end of file is reached.
            CR/LF or EOF is not part of the token.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsDigit(System.Char)">
            <summary>
            Is character in '0' ... '9'.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsHexDigit(System.Char)">
            <summary>
            Is character a hexadecimal digit.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsOctDigit(System.Char)">
            <summary>
            Is character an octal digit.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsLetter(System.Char)">
            <summary>
            Is character an alphabetic letter.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsWhiteSpace(System.Char)">
            <summary>
            Is character a white space.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsIdentifierChar(System.Char,System.Boolean)">
            <summary>
            Is character an identifier character. First character can be letter or underscore, following
            letters, digits or underscores.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsEof(System.Char)">
            <summary>
            Is character the end of file character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsDocumentElement(MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            Determines whether the given symbol is a valid keyword for a document element.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsSectionElement(MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            Determines whether the given symbol is a valid keyword for a section element.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsParagraphElement(MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            Determines whether the given symbol is a valid keyword for a paragraph element.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsHeaderFooterElement(MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            Determines whether the given symbol is a valid keyword for a header or footer element.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.IsFootnoteElement(MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            Determines whether the given symbol is a valid keyword for a footnote element.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ScanIdentifier">
            <summary>
            Scans an identifier.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ScanNumber(System.Boolean)">
            <summary>
            Scans an integer or real literal.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ReadHexNumber">
            <summary>
            Scans an hexadecimal literal.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ScanKeyword">
            <summary>
            Scans a DDL keyword that starts with a backslash.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ScanPunctuator">
            <summary>
            Scans punctuator terminal symbols.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ScanVerbatimStringLiteral">
            <summary>
            Scans verbatim strings like «@"String with ""quoted"" text"».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.ScanStringLiteral">
            <summary>
            Scans regular string literals like «"String with \"escaped\" text"».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlScanner.SaveCurDocumentPos">
            <summary>
            Save the current scanner location in the document for error handling.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlScanner.Symbol">
            <summary>
            Gets the current symbol.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlScanner.TokenType">
            <summary>
            Gets the current token type.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlScanner.Token">
            <summary>
            Gets the current token.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlScanner.Char">
            <summary>
            Gets the current character or EOF.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlScanner.NextChar">
            <summary>
            Gets the character after the current character or EOF.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlScanner.DocumentFileName">
            <summary>
            Gets the current filename of the document.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlScanner.DocumentPath">
            <summary>
            Gets the current path of the document.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlScanner.CurrentLine">
            <summary>
            Gets the current scanner line in the document.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlScanner.CurrentLinePos">
            <summary>
            Gets the current scanner column in the document.
            </summary>
        </member>
        <member name="T:Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="T:MigraDoc.NamespaceDoc">
            <summary>
            Base namespace of MigraDoc. Classes are implemented in nested namespaces like e. g. MigraDoc.DocumentObjectModel.
            </summary>
            <seealso cref="N:MigraDoc.DocumentObjectModel"></seealso>
        </member>
        <member name="T:MigraDoc.ProductVersionInfo">
            <summary>
            Version info base for all MigraDoc related assemblies.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Title">
            <summary>
            The title of the product.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Description">
            <summary>
            A characteristic description of the product.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Creator">
            <summary>
            The PDF producer information string.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Version">
            <summary>
            The full version number.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Url">
            <summary>
            The home page of this product.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Configuration">
            <summary>
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Company">
            <summary>
            The company that created/owned the product.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Product">
            <summary>
            The name of the product.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Copyright">
            <summary>
            The copyright information. Also used as NuGet Copyright.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Trademark">
            <summary>
            The trademark the product.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.Culture">
            <summary>
            Unused - must be empty string.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.VersionReferenceDate">
            <summary>
            E.g. "1/1/2005", for use in NuGet Script.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetID">
            <summary>
            Use _ instead of blanks and special characters. Can be complemented with a suffix in the NuGet Script.
            Nuspec Doc: The unique identifier for the package. This is the package name that is shown when packages
            are listed using the Package Manager Console. These are also used when installing a package using the
            Install-Package command within the Package Manager Console. Package IDs may not contain any spaces
            or characters that are invalid in an URL. In general, they follow the same rules as .NET namespaces do.
            So Foo.Bar is a valid ID, Foo! and Foo Bar are not. 
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetTitle">
            <summary>
            Nuspec Doc: The human-friendly title of the package displayed in the Manage NuGet Packages dialog.
            If none is specified, the ID is used instead. 
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetAuthors">
            <summary>
            Nuspec Doc: A comma-separated list of authors of the package code.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetOwners">
            <summary>
            Nuspec Doc: A comma-separated list of the package creators. This is often the same list as in authors.
            This is ignored when uploading the package to the NuGet.org Gallery. 
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetDescription">
            <summary>
            Nuspec Doc: A long description of the package. This shows up in the right pane of the Add Package Dialog
            as well as in the Package Manager Console when listing packages using the Get-Package command. 
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetReleaseNotes">
            <summary>
            Nuspec Doc: A description of the changes made in each release of the package. This field only shows up
            when the _Updates_ tab is selected and the package is an update to a previously installed package.
            It is displayed where the Description would normally be displayed. 
            </summary>                  
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetSummary">
            <summary>
            Nuspec Doc: A short description of the package. If specified, this shows up in the middle pane of the
            Add Package Dialog. If not specified, a truncated version of the description is used instead.
            </summary>                  
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetLanguage">
            <summary>
            Nuspec Doc: The locale ID for the package, such as en-us.
            </summary>                  
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetProjectUrl">
            <summary>
            Nuspec Doc: A URL for the home page of the package.
            </summary>
            <remarks>
            http://www.pdfsharp.net/NuGetPackage_PDFsharp-MigraDoc-GDI.ashx
            http://www.pdfsharp.net/NuGetPackage_PDFsharp-MigraDoc-WPF.ashx
            </remarks>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetIconUrl">
            <summary>
            Nuspec Doc: A URL for the image to use as the icon for the package in the Manage NuGet Packages
            dialog box. This should be a 32x32-pixel .png file that has a transparent background.
            </summary>
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetLicenseUrl">
            <summary>
            Nuspec Doc: A link to the license that the package is under.
            </summary>                  
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetRequireLicenseAcceptance">
            <summary>
            Nuspec Doc: A Boolean value that specifies whether the client needs to ensure that the package license (described by licenseUrl) is accepted before the package is installed.
            </summary>                  
        </member>
        <member name="F:MigraDoc.ProductVersionInfo.NuGetTags">
            <summary>
            Nuspec Doc: A space-delimited list of tags and keywords that describe the package. This information is used to help make sure users can find the package using
            searches in the Add Package Reference dialog box or filtering in the Package Manager Console window.
            </summary>                  
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Serializer">
            <summary>
            Object to be passed to the Serialize function of a DocumentObject to convert
            it into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.#ctor(System.IO.TextWriter,System.Int32,System.Int32)">
            <summary>
            A Serializer object for converting MDDOM into DDL.
            </summary>
            <param name="textWriter">A TextWriter to write DDL in.</param>
            <param name="indent">Indent of a new block. Default is 2.</param>
            <param name="initialIndent">Initial indent to start with.</param>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.#ctor(System.IO.TextWriter)">
            <summary>
            Initializes a new instance of the Serializer class with the specified TextWriter.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.#ctor(System.IO.TextWriter,System.Int32)">
            <summary>
            Initializes a new instance of the Serializer class with the specified TextWriter and indentation.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.IncreaseIndent">
            <summary>
            Increases indent of DDL code.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.DecreaseIndent">
            <summary>
            Decreases indent of DDL code.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteStamp">
            <summary>
            Writes the header for a DDL file containing copyright and creation time information.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.Write(System.String)">
            <summary>
            Appends a string indented without line feed.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteLine(System.String)">
            <summary>
            Writes a string indented with line feed.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.DoWordWrap(System.String)">
            <summary>
            Returns the part of the string str that fits into the line (up to 80 chars).
            If Wordwrap is impossible it returns the input-string str itself.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteLine">
            <summary>
            Writes an empty line.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteLineNoCommit(System.String)">
            <summary>
            Write a line without committing (without marking the text as serialized).
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteLineNoCommit">
            <summary>
            Write a line without committing (without marking the text as serialized).
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteComment(System.String)">
            <summary>
            Writes a text as comment and automatically word-wraps it.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.CloseUpLine">
            <summary>
            Writes a line break if the current position is not at the beginning
            of a new line.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteToStream(System.String,System.Boolean,System.Boolean)">
            <summary>
            Effectively writes text to the stream. The text is automatically indented and
            word-wrapped. A given text gets never word-wrapped to keep comments or string
            literals unbroken.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteToStream(System.String)">
            <summary>
            Write the text into the stream without breaking it and adds an indentation to it.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteLineToStream(System.String)">
            <summary>
            Write a line to the stream.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.IsBlankRequired(System.Char,System.Char)">
            <summary>
            Mighty function to figure out if a blank is required as separator.
            // Does not work without context...
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.BeginAttributes">
            <summary>
            Start attribute part.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.BeginAttributes(System.String)">
            <summary>
            Start attribute part.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.EndAttributes">
            <summary>
            End attribute part.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.EndAttributes(System.Int32)">
            <summary>
            End attribute part.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.WriteSimpleAttribute(System.String,System.Object)">
            <summary>
            Write attribute of type Unit, Color, int, float, double, bool, string or enum.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.BeginContent">
            <summary>
            Start content part.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.BeginContent(System.String)">
            <summary>
            Start content part.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.EndContent">
            <summary>
            End content part.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.EndContent(System.Int32)">
            <summary>
            End content part.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.BeginBlock">
            <summary>
            Starts a new nesting block.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.EndBlock">
            <summary>
            Ends a nesting block.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.EndBlock(System.Int32)">
            <summary>
            Ends a nesting block.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.Flush">
            <summary>
            Flushes the buffers of the underlying text writer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.Ind(System.Int32)">
            <summary>
            Returns an indent string of blanks.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Serializer.CommitText">
            <summary>
            Marks the current block as 'committed'. That means the block contains
            serialized data.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Serializer.Indent">
            <summary>
            Gets or sets the indentation for a new indentation level.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Serializer.InitialIndent">
            <summary>
            Gets or sets the initial indentation which precede every line.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Serializer.Position">
            <summary>
            Gets or sets the position within the underlying stream.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Serializer.Indentation">
            <summary>
            Gets an indent string of current indent.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.XValues">
            <summary>
            Represents the collection of values on the X-Axis.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.DocumentObjectCollection">
            <summary>
            Base class of all collections of the MigraDoc Document Object Model.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.#ctor">
            <summary>
            Initializes a new instance of the DocumentObjectCollection class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the DocumentObjectCollection class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the entire collection to a compatible one-dimensional System.Array,
            starting at the specified index of the target array.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.Clear">
            <summary>
            Removes all elements from the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.InsertObject(System.Int32,MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Inserts an object at the specified index.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.IndexOf(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Determines the index of a specific item in the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.RemoveObjectAt(System.Int32)">
            <summary>
            Removes the element at the specified index.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.Add(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Inserts the object into the collection and sets it's parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.IsNull">
            <summary>
            Determines whether this instance is null.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.GetEnumerator">
            <summary>
            Returns an enumerator that can iterate through this collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#RemoveAt(System.Int32)">
            <summary>
            Removes the item at the specified index from the Collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#Insert(System.Int32,System.Object)">
            <summary>
            Inserts an object at the specified index.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#Remove(System.Object)">
            <summary>
            Removes the first occurrence of the specific object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#Contains(System.Object)">
            <summary>
            Determines whether an element exists.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#IndexOf(System.Object)">
            <summary>
            Determines the index of a specific item in the Collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#Add(System.Object)">
            <summary>
            Adds an item to the Collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#Clear">
            <summary>
            Removes all items from the Collection.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObjectCollection.First">
            <summary>
            Gets the first value in the Collection, if there is any, otherwise null.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#IsReadOnly">
            <summary>
            Gets a value indicating whether the Collection is read-only.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#IsFixedSize">
            <summary>
            Gets a value indicating whether the Collection has a fixed size.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#ICollection#IsSynchronized">
            <summary>
            Gets a value indicating whether access to the Collection is synchronized.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#ICollection#SyncRoot">
            <summary>
            Gets an object that can be used to synchronize access to the collection.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObjectCollection.Count">
            <summary>
            Gets the number of elements actually contained in the collection.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObjectCollection.Item(System.Int32)">
            <summary>
            Gets or sets the element at the specified index.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObjectCollection.LastObject">
            <summary>
            Gets the last element or null, if no such element exists.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentObjectCollection.System#Collections#IList#Item(System.Int32)">
            <summary>
            Gets or sets the element at the specified index. 
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XValues.#ctor">
            <summary>
            Initializes a new instance of the XValues class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XValues.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the XValues class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XValues.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XValues.AddXSeries">
            <summary>
            Adds a new XSeries to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XValues.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts XValues into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.XValues.Item(System.Int32)">
            <summary>
            Gets an XSeries by its index.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.XValues.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries">
            <summary>
            Represents a series of data on the X-Axis.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartObject">
            <summary>
            Base class for all chart classes.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartObject.#ctor">
            <summary>
            Initializes a new instance of the ChartObject class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartObject.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the ChartObject class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartObject.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts ChartObject into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartObject.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries.#ctor">
            <summary>
            Initializes a new instance of the XSeries class.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries.xSeriesElements">
            <summary>
            The actual value container of the XSeries.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries.AddBlank">
            <summary>
            Adds a blank to the XSeries.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries.Add(System.String)">
            <summary>
            Adds a value to the XSeries.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries.Add(System.String[])">
            <summary>
            Adds an array of values to the XSeries.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts XSeries into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesElements">
            <summary>
            Represents the collection of the values in a data series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesElements.#ctor">
            <summary>
            Initializes a new instance of the SeriesElements class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesElements.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the SeriesElements class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesElements.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesElements.AddBlank">
            <summary>
            Adds a blank to the series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesElements.Add(System.Double)">
            <summary>
            Adds a new point with a real value to the series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesElements.Add(System.Double[])">
            <summary>
            Adds an array of new points with real values to the series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesElements.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts SeriesElements into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesElements.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.MarkerStyle">
            <summary>
            Symbols of a data point in a line chart.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType">
            <summary>
            Specifies with type of chart will be drawn.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType.Line">
            <summary>
            A line chart.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType.Column2D">
            <summary>
            A clustered 2d column chart.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType.ColumnStacked2D">
            <summary>
            A stacked 2d column chart.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType.Area2D">
            <summary>
            A 2d area chart.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType.Bar2D">
            <summary>
            A clustered 2d bar chart.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType.BarStacked2D">
            <summary>
            A stacked 2d bar chart.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType.Pie2D">
            <summary>
            A 2d pie chart.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType.PieExploded2D">
            <summary>
            An exploded 2d pie chart.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.BlankType">
            <summary>
            Determines how null values will be handled in a chart.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.BlankType.NotPlotted">
            <summary>
            Null value is not plotted.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.BlankType.Interpolated">
            <summary>
            Null value will be interpolated.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.BlankType.Zero">
            <summary>
            Null value will be handled as zero.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel">
            <summary>
            Represents a DataLabel of a Series
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.#ctor">
            <summary>
            Initializes a new instance of the DataLabel class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the DataLabel class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts DataLabel into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.Format">
            <summary>
            Gets or sets a numeric format string for the DataLabel.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.Font">
            <summary>
            Gets the Font for the DataLabel.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.Style">
            <summary>
            Gets or sets the Style for the DataLabel.
            Only the Font-associated part of the Style's ParagraphFormat is used.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.Position">
            <summary>
            Gets or sets the position of the DataLabel.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.Type">
            <summary>
            Gets or sets the type of the DataLabel.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabel.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.ValueDescriptorCollection">
            <summary>
            A collection that manages ValueDescriptors.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.ValueDescriptorCollection.Add(MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Adds the specified ValueDescriptor.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.ValueDescriptorCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.ValueDescriptorCollection.Count">
            <summary>
            Gets the count of ValueDescriptors.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.ValueDescriptorCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:MigraDoc.DocumentObjectModel.Internals.ValueDescriptor"/> at the specified index.
            </summary>
            <value></value>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.ValueDescriptorCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:MigraDoc.DocumentObjectModel.Internals.ValueDescriptor"/> with the specified name.
            </summary>
            <value></value>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.BorderType">
            <summary>
            Specifies the type of the Border object and therefore its position.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Colors">
            <summary>
            Represents 141 predefined colors.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.Column">
            <summary>
            Represents a column of a table.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Column.#ctor">
            <summary>
            Initializes a new instance of the Column class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Column.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Column class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Column.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Column.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Column.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Column into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Table">
            <summary>
            Gets the table the Column belongs to.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Index">
            <summary>
            Gets the index of the column. First column has index 0.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Item(System.Int32)">
            <summary>
            Gets a cell by its row index. The first cell has index 0.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Style">
            <summary>
            Sets or gets the default style name for all cells of the column.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Format">
            <summary>
            Gets the default ParagraphFormat for all cells of the column.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Width">
            <summary>
            Gets or sets the width of a column.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.LeftPadding">
            <summary>
            Gets or sets the default left padding for all cells of the column.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.RightPadding">
            <summary>
            Gets or sets the default right padding for all cells of the column.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Borders">
            <summary>
            Gets the default Borders object for all cells of the column.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.KeepWith">
            <summary>
            Gets or sets the number of columns that should be kept together with
            current column in case of a page break.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.HeadingFormat">
            <summary>
            Gets or sets a value which define whether the column is a header.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Shading">
            <summary>
            Gets the default Shading object for all cells of the column.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Column.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.WrapFormat">
            <summary>
            Define how the shape should be wrapped between the texts.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.#ctor">
            <summary>
            Initializes a new instance of the WrapFormat class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the WrapFormat class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts WrapFormat into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.Style">
            <summary>
            Gets or sets the wrapping style.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.DistanceTop">
            <summary>
            Gets or sets the distance between the top side of the shape with the adjacent text.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.DistanceBottom">
            <summary>
            Gets or sets the distance between the bottom side of the shape with the adjacent text.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.DistanceLeft">
            <summary>
            Gets or sets the distance between the left side of the shape with the adjacent text.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.DistanceRight">
            <summary>
            Gets or sets the distance between the right side of the shape with the adjacent text.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.WrapFormat.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesCollection">
            <summary>
            The collection of data series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesCollection.#ctor">
            <summary>
            Initializes a new instance of the SeriesCollection class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesCollection.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the SeriesCollection class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesCollection.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesCollection.AddSeries">
            <summary>
            Adds a new series to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesCollection.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts SeriesCollection into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesCollection.Item(System.Int32)">
            <summary>
            Gets a series by it's index.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.SeriesCollection.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.DdlErrorLevel">
            <summary>
            Specifies the severity of a DDL reader diagnostic.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlErrorLevel.None">
            <summary>
            An unknown severity.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlErrorLevel.Info">
            <summary>
            An information diagnostic.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlErrorLevel.Warning">
            <summary>
            A warning or suggestive diagnostic.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlErrorLevel.Error">
            <summary>
            An error diagnostic.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.NEnum">
            <summary>
            Represents a nullable Enum value.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.INullableValue">
            <summary>
            Interface for simple nullable values like NInt, NString etc.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NEnum.Equals(System.Object)">
            <summary>
            Returns a value indicating whether this instance is equal to the specified object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.NEnum.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.DVAttribute">
            <summary>
            Indicates that this field can be accessed via SetValue and GetValue.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.DVAttribute.#ctor">
            <summary>
            Initializes a new instance of the DVAttribute class.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.DVAttribute.RefOnly">
            <summary>
            Determines whether the field is RefOnly and should be excluded from recursive operations.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.DVAttribute.ItemType">
            <summary>
            Describes the type of the elements a collection contains.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.DVAttribute.Type">
            <summary>
            Gets or sets the type of the reflected value. Must be specified by NEnum.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.ValueDescriptor">
            <summary>
            Base class of all value descriptor classes.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.ValueDescriptor.ValueName">
            <summary>
            Name of the value.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.ValueDescriptor.ValueType">
            <summary>
            Type of the described value, e.g. typeof(Int32) for an NInt.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.ValueDescriptor.MemberType">
            <summary>
            Type of the described field or property, e.g. typeof(NInt) for an NInt.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.ValueDescriptor.memberInfo">
            <summary>
            FieldInfo of the described field.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.ValueDescriptor.flags">
            <summary>
            Flags of the described field, e.g. RefOnly.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.NullableDescriptor">
            <summary>
            Value descriptor of all nullable types.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NullableDescriptor.IsNull(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Determines whether the given DocumentObject is null (not set).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.ValueTypeDescriptor">
            <summary>
            Value descriptor of value types.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.ValueTypeDescriptor.IsNull(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Determines whether the given DocumentObject is null (not set).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.DocumentObjectDescriptor">
            <summary>
            Value descriptor of DocumentObject.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.DocumentObjectDescriptor.IsNull(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Determines whether the given DocumentObject is null (not set).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.DocumentObjectCollectionDescriptor">
            <summary>
            Value descriptor of DocumentObjectCollection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.DocumentObjectCollectionDescriptor.IsNull(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Determines whether the given DocumentObject is null (not set).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.StyleNames">
            <summary>
            Enumerates the predefined style names.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.PageBreak">
            <summary>
            A PageBreak is used to put following elements on a new page.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageBreak.#ctor">
            <summary>
            Initializes a new instance of the PageBreak class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageBreak.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the PageBreak class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageBreak.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageBreak.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts PageBreak into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageBreak.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.ImageHelper">
            <summary>
            Deals with image file names, searches along the image path, checks if images exist etc.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ImageHelper.GetImageName(System.String,System.String,System.String)">
            <summary>
            Gets the first existing image from the subfolders.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ImageHelper.InSubfolder(System.String,System.String,System.String,System.String)">
            <summary>
            Gets a value indicating whether the filename given in the referenceFilename exists in the subfolders.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ImageHelper.ExtractPageNumber(System.String,System.Int32@)">
            <summary>
            Extracts the page number if the path has the form 'MyFile.pdf#123' and returns
            the actual path without the number sign and the following digits.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.DomMsgID">
            <summary>
            Represents ids for error and diagnostic messages generated by the MigraDoc DOM.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Chars">
            <summary>
            Character table by name.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Visitors.RtfFlattenVisitor">
            <summary>
            Represents the visitor for flattening the DocumentObject to be used in the RtfRenderer.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Visitors.VisitorBase">
            <summary>
            Summary description for VisitorBase.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor">
            <summary>
            Represents the base visitor for the DocumentObject.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.VisitorBase.ParagraphFormatFromStyle(MigraDoc.DocumentObjectModel.Style)">
            <summary>
            Returns a paragraph format object initialized by the given style.
            It differs from style.ParagraphFormat if style is a character style.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.Row">
            <summary>
            Represents a row of a table.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Row.#ctor">
            <summary>
            Initializes a new instance of the Row class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Row.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Row class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Row.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Row.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Row.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Row into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Row.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Table">
            <summary>
            Gets the table the row belongs to.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Index">
            <summary>
            Gets the index of the row. First row has index 0.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Item(System.Int32)">
            <summary>
            Gets a cell by its column index. The first cell has index 0.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Style">
            <summary>
            Gets or sets the default style name for all cells of the row.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Format">
            <summary>
            Gets the default ParagraphFormat for all cells of the row.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.VerticalAlignment">
            <summary>
            Gets or sets the default vertical alignment for all cells of the row.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Height">
            <summary>
            Gets or sets the height of the row.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.HeightRule">
            <summary>
            Gets or sets the rule which is used to determine the height of the row.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.TopPadding">
            <summary>
            Gets or sets the default value for all cells of the row.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.BottomPadding">
            <summary>
            Gets or sets the default value for all cells of the row.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.HeadingFormat">
            <summary>
            Gets or sets a value which define whether the row is a header.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Borders">
            <summary>
            Gets the default Borders object for all cells of the row.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Shading">
            <summary>
            Gets the default Shading object for all cells of the row.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.KeepWith">
            <summary>
            Gets or sets the number of rows that should be
            kept together with the current row in case of a page break.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Cells">
            <summary>
            Gets the Cells collection of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Row.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.FillFormat">
            <summary>
            Defines the background filling of the shape.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.FillFormat.#ctor">
            <summary>
            Initializes a new instance of the FillFormat class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.FillFormat.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the FillFormat class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.FillFormat.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.FillFormat.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts FillFormat into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.FillFormat.Color">
            <summary>
            Gets or sets the color of the filling.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.FillFormat.Visible">
            <summary>
            Gets or sets a value indicating whether the background color should be visible.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.FillFormat.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.RelativeVertical">
            <summary>
            Reference point of the Top attribute.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.RelativeVertical.Line">
            <summary>
            Alignment relative to the bottom side of the previous element.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.RelativeVertical.Margin">
            <summary>
            Alignment relative to page margin.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.RelativeVertical.Page">
            <summary>
            Alignment relative to page edge.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.RelativeVertical.Paragraph">
            <summary>
            Alignment relative to the bottom line of the previous element.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.HeaderFooter">
            <summary>
            Represents a header or footer object in a section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.#ctor">
            <summary>
            Initializes a new instance of the HeaderFooter class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the HeaderFooter class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.AddParagraph">
            <summary>
            Adds a new paragraph to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.AddParagraph(System.String)">
            <summary>
            Adds a new paragraph with the specified text to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.AddChart(MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType)">
            <summary>
            Adds a new chart with the specified type to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.AddChart">
            <summary>
            Adds a new chart to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.AddTable">
            <summary>
            Adds a new table to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.AddImage(System.String)">
            <summary>
            Adds a new Image to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.AddTextFrame">
            <summary>
            Adds a new textframe to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.Add(MigraDoc.DocumentObjectModel.Paragraph)">
            <summary>
            Adds a new paragraph to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.Add(MigraDoc.DocumentObjectModel.Shapes.Charts.Chart)">
            <summary>
            Adds a new chart to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.Add(MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Adds a new table to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.Add(MigraDoc.DocumentObjectModel.Shapes.Image)">
            <summary>
            Adds a new image to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.Add(MigraDoc.DocumentObjectModel.Shapes.TextFrame)">
            <summary>
            Adds a new text frame to the header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts HeaderFooter into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.Serialize(MigraDoc.DocumentObjectModel.Serializer,System.String)">
            <summary>
            Converts HeaderFooter into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.HeaderFooter.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.IsHeader">
            <summary>
            Returns true if this is a headers, false otherwise.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.IsFooter">
            <summary>
            Returns true if this is a footer, false otherwise.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.IsFirstPage">
            <summary>
            Returns true if this is a first page header or footer, false otherwise.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.IsEvenPage">
            <summary>
            Returns true if this is an even page header or footer, false otherwise.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.IsPrimary">
            <summary>
            Returns true if this is a primary header or footer, false otherwise.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.Style">
            <summary>
            Gets or sets the style name.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.Format">
            <summary>
            Gets or sets the paragraph format.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.Elements">
            <summary>
            Gets the collection of document objects that defines the header or footer.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.HeaderFooter.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Font">
            <summary>
            Font represents the formatting of characters in a paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.#ctor">
            <summary>
            Initializes a new instance of the Font class that can be used as a template.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Font class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.#ctor(System.String,MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Initializes a new instance of the Font class with the specified name and size.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.#ctor(System.String)">
            <summary>
            Initializes a new instance of the Font class with the specified name.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.Clone">
            <summary>
            Creates a copy of the Font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.ApplyFont(MigraDoc.DocumentObjectModel.Font,MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Applies all non-null properties of a font to this font if the given font's property is different from the given refFont's property.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.ApplyFont(MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Applies all non-null properties of a font to this font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.Exists(System.String)">
            <summary>
            Gets a value indicating whether the specified font exists.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.CheckWhatIsNotNull">
            <summary>
            Get a bitmask of all non-null properties.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Font into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Font.Serialize(MigraDoc.DocumentObjectModel.Serializer,MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Converts Font into DDL. Properties with the same value as in an optionally given
            font are not serialized.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Font.Name">
            <summary>
            Gets or sets the name of the font.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Font.Size">
            <summary>
            Gets or sets the size of the font.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Font.Bold">
            <summary>
            Gets or sets the bold property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Font.Italic">
            <summary>
            Gets or sets the italic property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Font.Underline">
            <summary>
            Gets or sets the underline property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Font.Color">
            <summary>
            Gets or sets the color property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Font.Superscript">
            <summary>
            Gets or sets the superscript property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Font.Subscript">
            <summary>
            Gets or sets the subscript property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Font.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.OutlineLevel">
            <summary>
            Specifies the level of a paragraph.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.FootnoteNumberStyle">
            <summary>
            Determines the format of the footnote number.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteNumberStyle.Arabic">
            <summary>
            Numbering like: 1, 2, 3, 4.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteNumberStyle.LowercaseLetter">
            <summary>
            Lower case letters like: a, b, c, d.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteNumberStyle.UppercaseLetter">
            <summary>
            Upper case letters like: A, B, C, D.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteNumberStyle.LowercaseRoman">
            <summary>
            Lower case roman numbers: i, ii, iii, iv.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteNumberStyle.UppercaseRoman">
            <summary>
            Upper case roman numbers: I, II, III, IV.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.DomSR">
            <summary>
            String resources of MigraDoc.DocumentObjectModel. Provides all localized text strings
            for this assembly.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DomSR.FormatMessage(MigraDoc.DocumentObjectModel.DomMsgID,System.Object[])">
            <summary>
            Loads the message from the resource associated with the enum type and formats it
            using 'String.Format'. Because this function is intended to be used during error
            handling it never raises an exception.
            </summary>
            <param name="id">The type of the parameter identifies the resource
            and the name of the enum identifies the message in the resource.</param>
            <param name="args">Parameters passed through 'String.Format'.</param>
            <returns>The formatted message.</returns>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DomSR.GetString(MigraDoc.DocumentObjectModel.DomMsgID)">
            <summary>
            Gets the localized message identified by the specified DomMsgID.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DomSR.TestResourceMessages">
            <summary>
            Writes all messages defined by DomMsgID.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.DocumentElements">
            <summary>
            Represents a collection of document elements.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.#ctor">
            <summary>
            Initializes a new instance of the DocumentElements class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the DocumentElements class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddParagraph">
            <summary>
            Adds a new paragraph to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddParagraph(System.String)">
            <summary>
            Adds a new paragraph with the specified text to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddParagraph(System.String,System.String)">
            <summary>
            Adds a new paragraph with the specified text and style to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddTable">
            <summary>
            Adds a new table to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddLegend">
            <summary>
            Adds a new legend to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddPageBreak">
            <summary>
            Add a manual page break.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddBarcode">
            <summary>
            Adds a new barcode to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddChart(MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType)">
            <summary>
            Adds a new chart with the specified type to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddChart">
            <summary>
            Adds a new chart with the specified type to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddImage(System.String)">
            <summary>
            Adds a new image to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.AddTextFrame">
            <summary>
            Adds a new text frame to the collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts DocumentElements into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentElements.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentElements.Item(System.Int32)">
            <summary>
            Gets a document object by its index.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentElements.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.DdlEncoder">
            <summary>
            Provides functions for encoding and decoding of DDL text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DdlEncoder.#ctor">
            <summary>
            Initializes a new instance of the DdlEncoder class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DdlEncoder.StringToText(System.String)">
            <summary>
            Converts a string into a text phrase.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DdlEncoder.StringToLiteral(System.String)">
            <summary>
            Converts a string into a string literal (a quoted string).
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DdlEncoder.IsDdeIdentifier(System.String)">
            <summary>
            Scans the given string for characters which are invalid for identifiers.
            Strings are limited to 64 characters.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DdlEncoder.QuoteIfNameContainsBlanks(System.String)">
            <summary>
            Quotes the given name, if it contains characters which are invalid for identifiers.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.LineStyle">
            <summary>
            Specifies the line style of the LineFormat object.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.LineStyle.Single">
            <summary>
            A solid line.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels">
            <summary>
            Represents the format of the label of each value on the axis.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels.#ctor">
            <summary>
            Initializes a new instance of the TickLabels class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the TickLabels class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts TickLabels into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels.Style">
            <summary>
            Gets or sets the style name of the label.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels.Format">
            <summary>
            Gets or sets the label's number format.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels.Font">
            <summary>
            Gets the font of the label.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TickLabels.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.DdlReaderError">
            <summary>
            Represents an error or diagnostic message reported by the DDL reader.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReaderError.#ctor(MigraDoc.DocumentObjectModel.IO.DdlErrorLevel,System.String,System.Int32,System.String,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the DdlReaderError class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReaderError.ToString">
            <summary>
            Returns a String that represents the current DdlReaderError.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlReaderError.ErrorLevel">
            <summary>
            Specifies the severity of this diagnostic.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlReaderError.ErrorMessage">
            <summary>
            Specifies the diagnostic message text.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlReaderError.ErrorNumber">
            <summary>
            Specifies the diagnostic number.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlReaderError.SourceFile">
            <summary>
            Specifies the filename of the DDL text that caused the diagnostic,
            or an empty string ("").
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlReaderError.SourceLine">
            <summary>
            Specifies the line of the DDL text that caused the diagnostic (1 based),
            or 0 if there is no line information. 
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.IO.DdlReaderError.SourceColumn">
            <summary>
            Specifies the column of the source text that caused the diagnostic (1 based),
            or 0 if there is no column information.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Paragraph">
            <summary>
            Represents a paragraph which is used to build up a document with text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.#ctor">
            <summary>
            Initializes a new instance of the Paragraph class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Paragraph class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddText(System.String)">
            <summary>
            Adds a text phrase to the paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddChar(System.Char,System.Int32)">
            <summary>
            Adds a single character repeated the specified number of times to the paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddChar(System.Char)">
            <summary>
            Adds a single character to the paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddCharacter(MigraDoc.DocumentObjectModel.SymbolName,System.Int32)">
            <summary>
            Adds one or more Symbol objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddCharacter(MigraDoc.DocumentObjectModel.SymbolName)">
            <summary>
            Adds a Symbol object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddCharacter(System.Char,System.Int32)">
            <summary>
            Adds one or more Symbol objects defined by a character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddCharacter(System.Char)">
            <summary>
            Adds a Symbol object defined by a character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddSpace(System.Int32)">
            <summary>
            Adds a space character as many as count.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddTab">
            <summary>
            Adds a horizontal tab.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddLineBreak">
            <summary>
            Adds a line break.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddFormattedText">
            <summary>
            Adds a new FormattedText.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddFormattedText(MigraDoc.DocumentObjectModel.TextFormat)">
            <summary>
            Adds a new FormattedText object with the given format.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddFormattedText(MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Adds a new FormattedText with the given Font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddFormattedText(System.String)">
            <summary>
            Adds a new FormattedText with the given text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddFormattedText(System.String,MigraDoc.DocumentObjectModel.TextFormat)">
            <summary>
            Adds a new FormattedText object with the given text and format.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddFormattedText(System.String,MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Adds a new FormattedText object with the given text and font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddFormattedText(System.String,System.String)">
            <summary>
            Adds a new FormattedText object with the given text and style.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddHyperlink(System.String)">
            <summary>
            Adds a new Hyperlink of Type "Local", 
            i.e. the Target is a Bookmark within the Document
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddHyperlink(System.String,MigraDoc.DocumentObjectModel.HyperlinkType)">
            <summary>
            Adds a new Hyperlink
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddBookmark(System.String)">
            <summary>
            Adds a new Bookmark.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddPageField">
            <summary>
            Adds a new PageField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddPageRefField(System.String)">
            <summary>
            Adds a new PageRefField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddNumPagesField">
            <summary>
            Adds a new NumPagesField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddSectionField">
            <summary>
            Adds a new SectionField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddSectionPagesField">
            <summary>
            Adds a new SectionPagesField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddDateField">
            <summary>
            Adds a new DateField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddDateField(System.String)">
            <summary>
            Adds a new DateField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddInfoField(MigraDoc.DocumentObjectModel.Fields.InfoFieldType)">
            <summary>
            Adds a new InfoField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddFootnote(System.String)">
            <summary>
            Adds a new Footnote with the specified text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddFootnote">
            <summary>
            Adds a new Footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.AddImage(System.String)">
            <summary>
            Adds a new Image object
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Fields.BookmarkField)">
            <summary>
            Adds a new Bookmark
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Fields.PageField)">
            <summary>
            Adds a new PageField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Fields.PageRefField)">
            <summary>
            Adds a new PageRefField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Fields.NumPagesField)">
            <summary>
            Adds a new NumPagesField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Fields.SectionField)">
            <summary>
            Adds a new SectionField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Fields.SectionPagesField)">
            <summary>
            Adds a new SectionPagesField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Fields.DateField)">
            <summary>
            Adds a new DateField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Fields.InfoField)">
            <summary>
            Adds a new InfoField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Footnote)">
            <summary>
            Adds a new Footnote
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Text)">
            <summary>
            Adds a new Text
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.FormattedText)">
            <summary>
            Adds a new FormattedText
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Hyperlink)">
            <summary>
            Adds a new Hyperlink
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Shapes.Image)">
            <summary>
            Adds a new Image
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Add(MigraDoc.DocumentObjectModel.Character)">
            <summary>
            Adds a new Character
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Paragraph into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.SplitOnParaBreak">
            <summary>
            Returns an array of Paragraphs that are separated by parabreaks. Null if no parabreak is found.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Paragraph.SubsetElements(System.Int32,System.Int32)">
            <summary>
            Gets a subset of the paragraphs elements.
            </summary>
            <param name="startIdx">Start index of the required subset.</param>
            <param name="endIdx">End index of the required subset.</param>
            <returns>A ParagraphElements object with cloned elements.</returns>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Paragraph.Style">
            <summary>
            Gets or sets the style name.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Paragraph.Format">
            <summary>
            Gets or sets the ParagraphFormat object of the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Paragraph.Elements">
            <summary>
            Gets the collection of document objects that defines the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Paragraph.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Paragraph.SerializeContentOnly">
            <summary>
            For internal use only.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Paragraph.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.PageFormat">
            <summary>
            Standard page sizes.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.DocumentInfo">
            <summary>
            Contains information about document content, author etc.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentInfo.#ctor">
            <summary>
            Initializes a new instance of the DocumentInfo class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentInfo.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the DocumentInfo class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentInfo.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentInfo.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts DocumentInfo into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentInfo.Title">
            <summary>
            Gets or sets the document title.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentInfo.Author">
            <summary>
            Gets or sets the document author.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentInfo.Keywords">
            <summary>
            Gets or sets keywords related to the document.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentInfo.Subject">
            <summary>
            Gets or sets the subject of the document.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentInfo.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DocumentInfo.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Borders">
            <summary>
            A Borders collection represents the eight border objects used for paragraphs, tables etc.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.#ctor">
            <summary>
            Initializes a new instance of the Borders class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Borders class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.HasBorder(MigraDoc.DocumentObjectModel.BorderType)">
            <summary>
            Determines whether a particular border exists.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Gets an enumerator for the borders object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.ClearAll">
            <summary>
            Clears all Border objects from the collection. Additionally 'Borders = null'
            is written to the DDL stream when serialized.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Borders into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.Serialize(MigraDoc.DocumentObjectModel.Serializer,MigraDoc.DocumentObjectModel.Borders)">
            <summary>
            Converts Borders into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.GetMyName(MigraDoc.DocumentObjectModel.Border)">
            <summary>
            Gets a name of a border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Top">
            <summary>
            Gets or sets the top border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Left">
            <summary>
            Gets or sets the left border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Bottom">
            <summary>
            Gets or sets the bottom border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Right">
            <summary>
            Gets or sets the right border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.DiagonalUp">
            <summary>
            Gets or sets the diagonalup border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.DiagonalDown">
            <summary>
            Gets or sets the diagonaldown border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Visible">
            <summary>
            Gets or sets a value indicating whether the borders are visible.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Style">
            <summary>
            Gets or sets the line style of the borders.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Width">
            <summary>
            Gets or sets the standard width of the borders.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Color">
            <summary>
            Gets or sets the color of the borders.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.DistanceFromTop">
            <summary>
            Gets or sets the distance between text and the top border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.DistanceFromBottom">
            <summary>
            Gets or sets the distance between text and the bottom border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.DistanceFromLeft">
            <summary>
            Gets or sets the distance between text and the left border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.DistanceFromRight">
            <summary>
            Gets or sets the distance between text and the right border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Distance">
            <summary>
            Sets the distance to all four borders to the specified value.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.BordersCleared">
            <summary>
            Gets the information if the collection is marked as cleared. Additionally 'Borders = null'
            is written to the DDL stream when serialized.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Borders.BorderEnumerator">
            <summary>
            Returns an enumerator that can iterate through the Borders.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.BorderEnumerator.#ctor(System.Collections.Hashtable)">
            <summary>
            Creates a new BorderEnumerator.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.BorderEnumerator.Reset">
            <summary>
            Sets the enumerator to its initial position, which is before the first element in the border collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Borders.BorderEnumerator.MoveNext">
            <summary>
            Advances the enumerator to the next element of the border collection.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.BorderEnumerator.Current">
            <summary>
            Gets the current element in the border collection.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Borders.BorderEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Gets the current element in the border collection.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.Cells">
            <summary>
            Represents the collection of all cells of a row.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cells.#ctor">
            <summary>
            Initializes a new instance of the Cells class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cells.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Cells class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cells.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cells.Resize(System.Int32)">
            <summary>
            Resizes this cells' list if necessary.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cells.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Cells into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cells.Table">
            <summary>
            Gets the table the cells collection belongs to.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cells.Row">
            <summary>
            Gets the row the cells collection belongs to.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cells.Item(System.Int32)">
            <summary>
            Gets a cell by its index. The first cell has the index 0.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cells.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.Cell">
            <summary>
            Represents a cell of a table.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.#ctor">
            <summary>
            Initializes a new instance of the Cell class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Cell class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.ResetCachedValues">
            <summary>
            Resets the cached values.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.AddParagraph">
            <summary>
            Adds a new paragraph to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.AddParagraph(System.String)">
            <summary>
            Adds a new paragraph with the specified text to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.AddChart(MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType)">
            <summary>
            Adds a new chart with the specified type to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.AddChart">
            <summary>
            Adds a new chart to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.AddImage(System.String)">
            <summary>
            Adds a new Image to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.AddTextFrame">
            <summary>
            Adds a new textframe to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.Add(MigraDoc.DocumentObjectModel.Paragraph)">
            <summary>
            Adds a new paragraph to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.Add(MigraDoc.DocumentObjectModel.Shapes.Charts.Chart)">
            <summary>
            Adds a new chart to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.Add(MigraDoc.DocumentObjectModel.Shapes.Image)">
            <summary>
            Adds a new image to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.Add(MigraDoc.DocumentObjectModel.Shapes.TextFrame)">
            <summary>
            Adds a new text frame to the cell.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Cell into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Cell.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Table">
            <summary>
            Gets the table the cell belongs to.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Column">
            <summary>
            Gets the column the cell belongs to.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Row">
            <summary>
            Gets the row the cell belongs to.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Style">
            <summary>
            Sets or gets the style name.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Format">
            <summary>
            Gets the ParagraphFormat object of the paragraph.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.VerticalAlignment">
            <summary>
            Gets or sets the vertical alignment of the cell.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Borders">
            <summary>
            Gets the Borders object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Shading">
            <summary>
            Gets the shading object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.MergeRight">
            <summary>
            Gets or sets the number of cells to be merged right.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.MergeDown">
            <summary>
            Gets or sets the number of cells to be merged down.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Elements">
            <summary>
            Gets the collection of document objects that defines the cell.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Cell.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.NInt">
            <summary>
            Represents a nullable integer value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NInt.MigraDoc#DocumentObjectModel#Internals#INullableValue#GetValue">
            <summary>
            Gets the value of the instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NInt.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetValue(System.Object)">
            <summary>
            Sets the value of the instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NInt.SetNull">
            <summary>
            Resets this instance,
            i.e. IsNull() will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NInt.Equals(System.Object)">
            <summary>
            Returns a value indicating whether this instance is equal to the specified object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.NInt.Value">
            <summary>
            Gets or sets the value of the instance.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.NInt.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.ParagraphElements">
            <summary>
            A ParagraphElements collection contains the individual objects of a paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.#ctor">
            <summary>
            Initializes a new instance of the ParagraphElements class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the ParagraphElements class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddText(System.String)">
            <summary>
            Adds a Text object.
            </summary>
            <param name="text">Content of the new Text object.</param>
            <returns>Returns a new Text object.</returns>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddChar(System.Char,System.Int32)">
            <summary>
            Adds a single character repeated the specified number of times to the paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddChar(System.Char)">
            <summary>
            Adds a single character to the paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddCharacter(MigraDoc.DocumentObjectModel.SymbolName)">
            <summary>
            Adds a Character object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddCharacter(MigraDoc.DocumentObjectModel.SymbolName,System.Int32)">
            <summary>
            Adds one or more Character objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddCharacter(System.Char)">
            <summary>
            Adds a Character object defined by a character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddCharacter(System.Char,System.Int32)">
            <summary>
            Adds one or more Character objects defined by a character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddSpace(System.Int32)">
            <summary>
            Adds a space character as many as count.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddTab">
            <summary>
            Adds a horizontal tab.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddLineBreak">
            <summary>
            Adds a line break.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddFormattedText">
            <summary>
            Adds a new FormattedText.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddFormattedText(MigraDoc.DocumentObjectModel.TextFormat)">
            <summary>
            Adds a new FormattedText object with the given format.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddFormattedText(MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Adds a new FormattedText with the given Font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddFormattedText(System.String)">
            <summary>
            Adds a new FormattedText with the given text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddFormattedText(System.String,MigraDoc.DocumentObjectModel.TextFormat)">
            <summary>
            Adds a new FormattedText object with the given text and format.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddFormattedText(System.String,MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Adds a new FormattedText object with the given text and font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddFormattedText(System.String,System.String)">
            <summary>
            Adds a new FormattedText object with the given text and style.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddHyperlink(System.String)">
            <summary>
            Adds a new Hyperlink of Type "Local", i.e. the Target is a Bookmark within the Document
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddHyperlink(System.String,MigraDoc.DocumentObjectModel.HyperlinkType)">
            <summary>
            Adds a new Hyperlink
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddBookmark(System.String)">
            <summary>
            Adds a new Bookmark.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddPageField">
            <summary>
            Adds a new PageField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddPageRefField(System.String)">
            <summary>
            Adds a new RefFieldPage.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddNumPagesField">
            <summary>
            Adds a new NumPagesField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddSectionField">
            <summary>
            Adds a new SectionField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddSectionPagesField">
            <summary>
            Adds a new SectionPagesField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddDateField">
            <summary>
            Adds a new DateField.
            </summary>
            
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddDateField(System.String)">
            <summary>
            Adds a new DateField with the given format.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddInfoField(MigraDoc.DocumentObjectModel.Fields.InfoFieldType)">
            <summary>
            Adds a new InfoField with the given type.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddFootnote(System.String)">
            <summary>
            Adds a new Footnote with the specified Text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddFootnote">
            <summary>
            Adds a new Footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.AddImage(System.String)">
            <summary>
            Adds a new Image.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.Add(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ParagraphElements.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts ParagraphElements into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphElements.Item(System.Int32)">
            <summary>
            Gets a ParagraphElement by its index.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ParagraphElements.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend">
            <summary>
            Represents a legend of a chart.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.#ctor">
            <summary>
            Initializes a new instance of the Legend class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Legend class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Legend into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.Style">
            <summary>
            Gets or sets the style name of the legend's text.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.Format">
            <summary>
            Gets the paragraph format of the legend's text.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.LineFormat">
            <summary>
            Gets the line format of the legend's border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Legend.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.BookmarkField">
            <summary>
            BookmarkField is used as target for Hyperlinks or PageRefs.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.BookmarkField.#ctor">
            <summary>
            Initializes a new instance of the BookmarkField class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.BookmarkField.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the BookmarkField class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.BookmarkField.#ctor(System.String)">
            <summary>
            Initializes a new instance of the BookmarkField class with the necessary bookmark name.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.BookmarkField.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.BookmarkField.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts BookmarkField into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.BookmarkField.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.BookmarkField.Name">
            <summary>
            Gets or sets the name of the bookmark.
            Used to reference the bookmark from a Hyperlink or PageRef.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.BookmarkField.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.TabStop">
            <summary>
            Represents a tab inside a paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStop.#ctor">
            <summary>
            Initializes a new instance of the TabStop class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStop.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the TabStop class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStop.#ctor(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Initializes a new instance of the TabStop class with the specified position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStop.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabStop.AddTab">
            <summary>
            Generates a '+=' in DDL if it is true, otherwise '-='.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStop.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts TabStop into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.TabStop.Position">
            <summary>
            Gets the tab stop position.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.TabStop.Alignment">
            <summary>
            Gets or sets the alignment of the tabstop.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.TabStop.Leader">
            <summary>
            Gets or sets the character which is used as a leader for the tabstop.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.TabStop.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.TabLeader">
            <summary>
            Used to determine the leader for the tab.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabLeader.Spaces">
            <summary>
            Blanks are used as leader.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabLeader.Dots">
            <summary>
            Dots at the baseline.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabLeader.Dashes">
            <summary>
            Dashes are used as leader.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabLeader.Lines">
            <summary>
            Same as Heavy.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabLeader.Heavy">
            <summary>
            Leader will be underlined.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabLeader.MiddleDot">
            <summary>
            Dots in the middle (vertical) of the line.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.ListType">
            <summary>
            Specifies the symbol or kind of numbering of the list.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.FootnoteNumberingRule">
            <summary>
            Determines the behavior of the footnote numbering.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteNumberingRule.RestartPage">
            <summary>
            Numbering of the footnote restarts on each page.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteNumberingRule.RestartContinuous">
            <summary>
            Numbering does not restart, each new footnote number will be incremented by 1.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteNumberingRule.RestartSection">
            <summary>
            Numbering of the footnote restarts on each section.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.FootnoteLocation">
            <summary>
            Determines the position of the footnote on the page.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteLocation.BottomOfPage">
            <summary>
            Footnote will be rendered on the bottom of the page.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.FootnoteLocation.BeneathText">
            <summary>
            Footnote will be rendered immediately after the text.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.WrapStyle">
            <summary>
            Specifies how the shape object should be placed between the other elements.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.WrapStyle.TopBottom">
            <summary>
            The object will be placed between its predecessor and its successor.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.WrapStyle.None">
            <summary>
            The object will be ignored when the other elements are placed.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.WrapStyle.Through">
            <summary>
            The object will be ignored when the other elements are placed.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.DashStyle">
            <summary>
            Specifies the dash style of the LineFormat object.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.DashStyle.Solid">
            <summary>
            A solid line.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.DashStyle.Dash">
            <summary>
            A dashed line.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.DashStyle.DashDot">
            <summary>
            Alternating dashes and dots.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.DashStyle.DashDotDot">
            <summary>
            A dash followed by two dots.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.DashStyle.SquareDot">
            <summary>
            Square dots.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis">
            <summary>
            This class represents an axis in a chart.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.#ctor">
            <summary>
            Initializes a new instance of the Axis class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Axis class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.CheckGridlines(MigraDoc.DocumentObjectModel.Shapes.Charts.Gridlines)">
            <summary>
            Determines whether the specified gridlines object is a MajorGridlines or an MinorGridlines.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Axis into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.Title">
            <summary>
            Gets the title of the axis.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.MinimumScale">
            <summary>
            Gets or sets the minimum value of the axis.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.MaximumScale">
            <summary>
            Gets or sets the maximum value of the axis.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.MajorTick">
            <summary>
            Gets or sets the interval of the primary tick.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.MinorTick">
            <summary>
            Gets or sets the interval of the secondary tick.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.MajorTickMark">
            <summary>
            Gets or sets the type of the primary tick mark.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.MinorTickMark">
            <summary>
            Gets or sets the type of the secondary tick mark.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.TickLabels">
            <summary>
            Gets the label of the primary tick.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.LineFormat">
            <summary>
            Gets the format of the axis line.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.MajorGridlines">
            <summary>
            Gets the primary gridline object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.MinorGridlines">
            <summary>
            Gets the secondary gridline object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.HasMajorGridlines">
            <summary>
            Gets or sets, whether the axis has a primary gridline object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.HasMinorGridlines">
            <summary>
            Gets or sets, whether the axis has a secondary gridline object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Axis.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.DdlReaderErrors">
            <summary>
            Used to collect errors reported by the DDL parser.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReaderErrors.AddError(MigraDoc.DocumentObjectModel.IO.DdlReaderError)">
            <summary>
            Adds the specified DdlReaderError at the end of the error list.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlReaderErrors.Item(System.Int32)">
            <summary>
            Gets the DdlReaderError at the specified position.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlReaderErrors.ErrorCount">
            <summary>
            Gets the number of messages that are errors.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Style">
            <summary>
            Represents style templates for paragraph or character formatting
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Style.DefaultParagraphFontName">
            <summary>
            Name of the default character style.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Style.DefaultParagraphName">
            <summary>
            Name of the default paragraph style.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.#ctor">
            <summary>
            Initializes a new instance of the Style class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Style class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the Style class with name and base style name.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.GetValue(System.String,MigraDoc.DocumentObjectModel.Internals.GV)">
            <summary>
            Returns the value with the specified name and value access.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.GetBaseStyle">
            <summary>
            Get the BaseStyle of the current style.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Style into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.Optimize">
            <summary>
            Sets all properties to Null that have the same value as the base style.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Style.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.IsReadOnly">
            <summary>
            Indicates whether the style is read-only. 
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.Font">
            <summary>
            Gets the font of ParagraphFormat. 
            Calling style.Font is just a shortcut to style.ParagraphFormat.Font.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.Name">
            <summary>
            Gets the name of the style.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.ParagraphFormat">
            <summary>
            Gets the ParagraphFormat. To prevent read-only styles from being modified, a copy of its ParagraphFormat
            is returned in this case.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.BaseStyle">
            <summary>
            Gets or sets the name of the base style.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.Type">
            <summary>
            Gets the StyleType of the style.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.IsRootStyle">
            <summary>
            Determines whether the style is the style Normal or DefaultParagraphFont.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.BuildIn">
            <summary>
            Indicates whether the style is a predefined (build in) style.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Style.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Section">
            <summary>
            A Section is a collection of document objects sharing the same header, footer, 
            and page setup.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.#ctor">
            <summary>
            Initializes a new instance of the Section class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Section class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.PreviousSection">
            <summary>
            Gets the previous section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.AddParagraph">
            <summary>
            Adds a new paragraph to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.AddParagraph(System.String)">
            <summary>
            Adds a new paragraph with the specified text to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.AddParagraph(System.String,System.String)">
            <summary>
            Adds a new paragraph with the specified text and style to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.AddChart(MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType)">
            <summary>
            Adds a new chart with the specified type to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.AddChart">
            <summary>
            Adds a new chart to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.AddTable">
            <summary>
            Adds a new table to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.AddPageBreak">
            <summary>
            Adds a manual page break.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.AddImage(System.String)">
            <summary>
            Adds a new Image to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.AddTextFrame">
            <summary>
            Adds a new textframe to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.Add(MigraDoc.DocumentObjectModel.Paragraph)">
            <summary>
            Adds a new paragraph to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.Add(MigraDoc.DocumentObjectModel.Shapes.Charts.Chart)">
            <summary>
            Adds a new chart to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.Add(MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Adds a new table to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.Add(MigraDoc.DocumentObjectModel.Shapes.Image)">
            <summary>
            Adds a new image to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.Add(MigraDoc.DocumentObjectModel.Shapes.TextFrame)">
            <summary>
            Adds a new text frame to the section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Section into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Section.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Section.PageSetup">
            <summary>
            Gets the PageSetup object
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Section.Headers">
            <summary>
            Gets the HeadersFooters collection containing the headers.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Section.Footers">
            <summary>
            Gets the HeadersFooters collection containing the footers.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Section.Elements">
            <summary>
            Gets the document elements that build the section's content.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Section.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Section.LastParagraph">
            <summary>
            Gets the last paragraph of this section, or null, if no paragraph exists is this section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Section.LastTable">
            <summary>
            Gets the last table of this section, or null, if no table exists is this section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Section.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.FontProperties">
            <summary>
            Specifies the properties for the font.
            FOR INTERNAL USE ONLY.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.DocumentRelations">
            <summary>
            Provides relational information between document objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentRelations.HasParentOfType(MigraDoc.DocumentObjectModel.DocumentObject,System.Type)">
            <summary>
            Determines whether the specified documentObject has a
            parent of the given type somewhere within the document hierarchy.
            </summary>
            <param name="documentObject">The document object to check.</param>
            <param name="type">The parent type to search for.</param>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentRelations.GetParent(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Gets the direct parent of the given document object.
            </summary>
            <param name="documentObject">The document object the parent is searched for.</param>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DocumentRelations.GetParentOfType(MigraDoc.DocumentObjectModel.DocumentObject,System.Type)">
            <summary>
            Gets a parent of the document object with the given type somewhere within the document hierarchy.
            Returns null if none exists.
            </summary>
            <param name="documentObject">The document object the parent is searched for.</param>
            <param name="type">The parent type to search for.</param>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Visitors.CellComparer">
            <summary>
            Comparer for the cell positions within a table.
            It compares the cell positions from top to bottom and left to right.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.LineFormat">
            <summary>
            Defines the format of a line in a shape object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LineFormat.#ctor">
            <summary>
            Initializes a new instance of the LineFormat class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LineFormat.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Lineformat class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LineFormat.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LineFormat.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts LineFormat into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.LineFormat.Visible">
            <summary>
            Gets or sets a value indicating whether the line should be visible.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.LineFormat.Width">
            <summary>
            Gets or sets the width of the line in Unit.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.LineFormat.Color">
            <summary>
            Gets or sets the color of the line.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.LineFormat.DashStyle">
            <summary>
            Gets or sets the dash style of the line.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.LineFormat.Style">
            <summary>
            Gets or sets the style of the line.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.LineFormat.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeriesElements">
            <summary>
            Represents the collection of the value in an XSeries.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeriesElements.#ctor">
            <summary>
            Initializes a new instance of the XSeriesElements class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeriesElements.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeriesElements.AddBlank">
            <summary>
            Adds a blank to the XSeries.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeriesElements.Add(System.String)">
            <summary>
            Adds a value to the XSeries.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeriesElements.Add(System.String[])">
            <summary>
            Adds an array of values to the XSeries.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeriesElements.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts XSeriesElements into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.XSeriesElements.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea">
            <summary>
            An area object in the chart which contain text or legend.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.#ctor">
            <summary>
            Initializes a new instance of the TextArea class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the TextArea class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.AddParagraph">
            <summary>
            Adds a new paragraph to the text area.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.AddParagraph(System.String)">
            <summary>
            Adds a new paragraph with the specified text to the text area.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.AddTable">
            <summary>
            Adds a new table to the text area.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.AddImage(System.String)">
            <summary>
            Adds a new Image to the text area.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.AddLegend">
            <summary>
            Adds a new legend to the text area.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Add(MigraDoc.DocumentObjectModel.Paragraph)">
            <summary>
            Adds a new paragraph to the text area.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Add(MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Adds a new table to the text area.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Add(MigraDoc.DocumentObjectModel.Shapes.Image)">
            <summary>
            Adds a new image to the text area.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Add(MigraDoc.DocumentObjectModel.Shapes.Charts.Legend)">
            <summary>
            Adds a new legend to the text area.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts TextArea into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Height">
            <summary>
            Gets or sets the height of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Width">
            <summary>
            Gets or sets the width of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Style">
            <summary>
            Gets or sets the default style name of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Format">
            <summary>
            Gets or sets the default paragraph format of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.LineFormat">
            <summary>
            Gets the line format of the area's border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.FillFormat">
            <summary>
            Gets the background filling of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.LeftPadding">
            <summary>
            Gets or sets the left padding of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.RightPadding">
            <summary>
            Gets or sets the right padding of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.TopPadding">
            <summary>
            Gets or sets the top padding of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.BottomPadding">
            <summary>
            Gets or sets the bottom padding of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.VerticalAlignment">
            <summary>
            Gets or sets the Vertical alignment of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Elements">
            <summary>
            Gets the document objects that creates the text area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.TickMarkType">
            <summary>
            Determines the position where the Tickmarks will be rendered.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.TickMarkType.None">
            <summary>
            Tickmarks are not rendered.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.TickMarkType.Inside">
            <summary>
            Tickmarks are rendered inside the plot area.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.TickMarkType.Outside">
            <summary>
            Tickmarks are rendered outside the plot area.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.TickMarkType.Cross">
            <summary>
            Tickmarks are rendered inside and outside the plot area.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabelPosition">
            <summary>
            Determines where the data label will be positioned.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabelPosition.Center">
            <summary>
            DataLabel will be centered inside the bar or pie.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabelPosition.InsideBase">
            <summary>
            Inside the bar or pie at the origin.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabelPosition.InsideEnd">
            <summary>
            Inside the bar or pie at the edge.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabelPosition.OutsideEnd">
            <summary>
            Outside the bar or pie.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart">
            <summary>
            Represents charts with different types.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.#ctor">
            <summary>
            Initializes a new instance of the Chart class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Chart class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.#ctor(MigraDoc.DocumentObjectModel.Shapes.Charts.ChartType)">
            <summary>
            Initializes a new instance of the Chart class with the specified chart type.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.CheckAxis(MigraDoc.DocumentObjectModel.Shapes.Charts.Axis)">
            <summary>
            Determines the type of the given axis.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.CheckTextArea(MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea)">
            <summary>
            Determines the type of the given textarea.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Chart into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.Type">
            <summary>
            Gets or sets the base type of the chart.
            ChartType of the series can be overwritten.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.Style">
            <summary>
            Gets or sets the default style name of the whole chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.Format">
            <summary>
            Gets the default paragraph format of the whole chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.XAxis">
            <summary>
            Gets the X-Axis of the Chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.YAxis">
            <summary>
            Gets the Y-Axis of the Chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.ZAxis">
            <summary>
            Gets the Z-Axis of the Chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.SeriesCollection">
            <summary>
            Gets the collection of the data series.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.XValues">
            <summary>
            Gets the collection of the values written on the X-Axis.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.HeaderArea">
            <summary>
            Gets the header area of the chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.BottomArea">
            <summary>
            Gets the bottom area of the chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.TopArea">
            <summary>
            Gets the top area of the chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.FooterArea">
            <summary>
            Gets the footer area of the chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.LeftArea">
            <summary>
            Gets the left area of the chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.RightArea">
            <summary>
            Gets the right area of the chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.PlotArea">
            <summary>
            Gets the plot (drawing) area of the chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.DisplayBlanksAs">
            <summary>
            Gets or sets a value defining how blanks in the data series should be shown.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.PivotChart">
            <summary>
            Gets or sets whether XAxis Labels should be merged.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.DataLabel">
            <summary>
            Gets the DataLabel of the chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.HasDataLabel">
            <summary>
            Gets or sets whether the chart has a DataLabel.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Chart.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.NDouble">
            <summary>
            Represents a nullable double value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NDouble.MigraDoc#DocumentObjectModel#Internals#INullableValue#GetValue">
            <summary>
            Gets the value of the instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NDouble.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetValue(System.Object)">
            <summary>
            Sets the value of the instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NDouble.SetNull">
            <summary>
            Resets this instance,
            i.e. IsNull() will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NDouble.Equals(System.Object)">
            <summary>
            Returns a value indicating whether this instance is equal to the specified object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.NDouble.Value">
            <summary>
            Gets or sets the value of the instance.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.NDouble.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.SectionPagesField">
            <summary>
            SectionPagesField is used to reference the number of all pages of the current section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.SectionPagesField.#ctor">
            <summary>
            Initializes a new instance of the SectionPagesField class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.SectionPagesField.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the SectionPagesField class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.SectionPagesField.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.SectionPagesField.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts SectionPagesField into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.SectionPagesField.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.PageField">
            <summary>
            PageField is used to reference the number of the current page.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.PageField.#ctor">
            <summary>
            Initializes a new instance of the PageField class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.PageField.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the PageField class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.PageField.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.PageField.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts PageField into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.PageField.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.FormattedText">
            <summary>
            Represents the format of a text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.#ctor">
            <summary>
            Initializes a new instance of the FormattedText class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the FormattedText class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddBookmark(System.String)">
            <summary>
            Adds a new Bookmark.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddChar(System.Char,System.Int32)">
            <summary>
            Adds a single character repeated the specified number of times to the formatted text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddChar(System.Char)">
            <summary>
            Adds a single character to the formatted text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddPageField">
            <summary>
            Adds a new PageField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddPageRefField(System.String)">
            <summary>
            Adds a new PageRefField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddNumPagesField">
            <summary>
            Adds a new NumPagesField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddSectionField">
            <summary>
            Adds a new SectionField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddSectionPagesField">
            <summary>
            Adds a new SectionPagesField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddDateField">
            <summary>
            Adds a new DateField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddDateField(System.String)">
            <summary>
            Adds a new DateField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddInfoField(MigraDoc.DocumentObjectModel.Fields.InfoFieldType)">
            <summary>
            Adds a new InfoField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddFootnote(System.String)">
            <summary>
            Adds a new Footnote with the specified text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddFootnote">
            <summary>
            Adds a new Footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddText(System.String)">
            <summary>
            Adds a text phrase to the formatted text.
            </summary>
            <param name="text">Content of the new text object.</param>
            <returns>Returns a new Text object.</returns>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddFormattedText">
            <summary>
            Adds a new FormattedText.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddFormattedText(MigraDoc.DocumentObjectModel.TextFormat)">
            <summary>
            Adds a new FormattedText object with the given format.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddFormattedText(MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Adds a new FormattedText with the given Font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddFormattedText(System.String)">
            <summary>
            Adds a new FormattedText with the given text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddFormattedText(System.String,MigraDoc.DocumentObjectModel.TextFormat)">
            <summary>
            Adds a new FormattedText object with the given text and format.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddFormattedText(System.String,MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Adds a new FormattedText object with the given text and font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddFormattedText(System.String,System.String)">
            <summary>
            Adds a new FormattedText object with the given text and style.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddHyperlink(System.String)">
            <summary>
            Adds a new Hyperlink of Type "Local", 
            i.e. the target is a Bookmark within the Document
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddHyperlink(System.String,MigraDoc.DocumentObjectModel.HyperlinkType)">
            <summary>
            Adds a new Hyperlink
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddImage(System.String)">
            <summary>
            Adds a new Image object
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddCharacter(MigraDoc.DocumentObjectModel.SymbolName)">
            <summary>
            Adds a Symbol object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddCharacter(MigraDoc.DocumentObjectModel.SymbolName,System.Int32)">
            <summary>
            Adds one or more Symbol objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddCharacter(System.Char)">
            <summary>
            Adds a Symbol object defined by a character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddCharacter(System.Char,System.Int32)">
            <summary>
            Adds one or more Symbol objects defined by a character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddSpace(System.Int32)">
            <summary>
            Adds one or more Symbol objects defined by a character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddTab">
            <summary>
            Adds a horizontal tab.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.AddLineBreak">
            <summary>
            Adds a line break.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Fields.BookmarkField)">
            <summary>
            Adds a new Bookmark
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Fields.PageField)">
            <summary>
            Adds a new PageField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Fields.PageRefField)">
            <summary>
            Adds a new PageRefField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Fields.NumPagesField)">
            <summary>
            Adds a new NumPagesField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Fields.SectionField)">
            <summary>
            Adds a new SectionField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Fields.SectionPagesField)">
            <summary>
            Adds a new SectionPagesField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Fields.DateField)">
            <summary>
            Adds a new DateField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Fields.InfoField)">
            <summary>
            Adds a new InfoField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Footnote)">
            <summary>
            Adds a new Footnote
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Text)">
            <summary>
            Adds a new Text
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.FormattedText)">
            <summary>
            Adds a new FormattedText
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Hyperlink)">
            <summary>
            Adds a new Hyperlink
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Shapes.Image)">
            <summary>
            Adds a new Image
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Add(MigraDoc.DocumentObjectModel.Character)">
            <summary>
            Adds a new Character
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts FormattedText into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.FormattedText.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Font">
            <summary>
            Gets or sets the font object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Style">
            <summary>
            Gets or sets the style name.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.FontName">
            <summary>
            Gets or sets the name of the font.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Name">
            <summary>
            Gets or sets the name of the font.
            For internal use only.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Size">
            <summary>
            Gets or sets the size in point.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Bold">
            <summary>
            Gets or sets the bold property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Italic">
            <summary>
            Gets or sets the italic property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Underline">
            <summary>
            Gets or sets the underline property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Color">
            <summary>
            Gets or sets the color property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Superscript">
            <summary>
            Gets or sets the superscript property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Subscript">
            <summary>
            Gets or sets the subscript property.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Elements">
            <summary>
            Gets the collection of paragraph elements that defines the FormattedText.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.FormattedText.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.SymbolName">
            <summary>
            Represents the type of the special character.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.ParagraphAlignment">
            <summary>
            Specifies the alignment of a paragraph.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Border">
            <summary>
            Represents one border in a borders collection. The type determines its position in a cell,
            paragraph etc.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Border.#ctor">
            <summary>
            Initializes a new instance of the Border class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Border.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Border class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Border.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Border.Clear">
            <summary>
            Clears the Border object. Additionally 'Border = null'
            is written to the DDL stream when serialized.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Border.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Border into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Border.Serialize(MigraDoc.DocumentObjectModel.Serializer,System.String,MigraDoc.DocumentObjectModel.Border)">
            <summary>
            Converts Border into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Border.Visible">
            <summary>
            Gets or sets a value indicating whether the border visible is.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Border.Style">
            <summary>
            Gets or sets the line style of the border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Border.Width">
            <summary>
            Gets or sets the line width of the border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Border.Color">
            <summary>
            Gets or sets the color of the border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Border.Name">
            <summary>
            Gets the name of this border ("top", "bottom"....).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Border.BorderCleared">
            <summary>
            Gets the information if the border is marked as cleared. Additionally 'xxx = null'
            is written to the DDL stream when serialized.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Border.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Visitors.PdfFlattenVisitor">
            <summary>
            Flattens a document for PDF rendering.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Visitors.PdfFlattenVisitor.#ctor">
            <summary>
            Initializes a new instance of the PdfFlattenVisitor class.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.VerticalAlignment">
            <summary>
            Specifies the vertical alignment of the cell's content.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.TopPosition">
            <summary>
            Represents the top position in a shape.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.#ctor(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Initializes a new instance of TopPosition from Unit.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.#ctor(MigraDoc.DocumentObjectModel.Shapes.ShapePosition)">
            <summary>
            Initializes a new instance of TopPosition from ShapePosition.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.IsValid(MigraDoc.DocumentObjectModel.Shapes.ShapePosition)">
            <summary>
            Indicates the given shapePosition is valid for TopPosition.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.op_Implicit(MigraDoc.DocumentObjectModel.Shapes.ShapePosition)~MigraDoc.DocumentObjectModel.Shapes.TopPosition">
            <summary>
            Converts a ShapePosition to a TopPosition.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.op_Implicit(MigraDoc.DocumentObjectModel.Unit)~MigraDoc.DocumentObjectModel.Shapes.TopPosition">
            <summary>
            Converts a Unit to a TopPosition.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.op_Implicit(System.String)~MigraDoc.DocumentObjectModel.Shapes.TopPosition">
            <summary>
            Converts a string to a TopPosition.
            The string is interpreted as a Unit.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.op_Implicit(System.Double)~MigraDoc.DocumentObjectModel.Shapes.TopPosition">
            <summary>
            Converts a double to a TopPosition.
            The double is interpreted as a Unit in Point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.op_Implicit(System.Int32)~MigraDoc.DocumentObjectModel.Shapes.TopPosition">
            <summary>
            Converts an integer to a TopPosition. 
            The integer is interpreted as a Unit in Point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.SetFromEnum(MigraDoc.DocumentObjectModel.Shapes.ShapePosition)">
            <summary>
            Sets shapeposition enum and resets position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.SetFromUnit(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Sets the Position from a Unit.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetValue(System.Object)">
            <summary>
            Sets the Position from an object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.MigraDoc#DocumentObjectModel#Internals#INullableValue#GetValue">
            <summary>
            Gets the Position as Unit or ShapePosition.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetNull">
            <summary>
            Resets this instance, i.e. IsNull() will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.Parse(System.String)">
            <summary>
            Parses the specified value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.TopPosition.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts TopPosition into DDL.
            </summary>  
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.TopPosition.NullValue">
            <summary>
            Represents the unitialized TopPosition object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TopPosition.MigraDoc#DocumentObjectModel#Internals#INullableValue#IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TopPosition.Position">
            <summary>
            Gets the value of the position in unit.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.TopPosition.ShapePosition">
            <summary>
            Gets the value of the position.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.LeftPosition">
            <summary>
            Represents the left position in a shape.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.#ctor(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Initializes a new instance of the LeftPosition class from Unit.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.#ctor(MigraDoc.DocumentObjectModel.Shapes.ShapePosition)">
            <summary>
            Initializes a new instance of the LeftPosition class from ShapePosition.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.SetFromEnum(MigraDoc.DocumentObjectModel.Shapes.ShapePosition)">
            <summary>
            Sets shapeposition enum and resets position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.SetFromUnit(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Sets the Position from a Unit.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetValue(System.Object)">
            <summary>
            Sets the Position from an object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.MigraDoc#DocumentObjectModel#Internals#INullableValue#GetValue">
            <summary>
            Gets the value of the position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetNull">
            <summary>
            Resets this instance, i.e. IsNull() will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.IsValid(MigraDoc.DocumentObjectModel.Shapes.ShapePosition)">
            <summary>
            Indicates the given shapePosition is valid for LeftPosition.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.op_Implicit(MigraDoc.DocumentObjectModel.Shapes.ShapePosition)~MigraDoc.DocumentObjectModel.Shapes.LeftPosition">
            <summary>
            Converts a ShapePosition to a LeftPosition.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.op_Implicit(MigraDoc.DocumentObjectModel.Unit)~MigraDoc.DocumentObjectModel.Shapes.LeftPosition">
            <summary>
            Converts a Unit to a LeftPosition.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.op_Implicit(System.String)~MigraDoc.DocumentObjectModel.Shapes.LeftPosition">
            <summary>
            Converts a string to a LeftPosition.
            The string is interpreted as a Unit.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.op_Implicit(System.Double)~MigraDoc.DocumentObjectModel.Shapes.LeftPosition">
            <summary>
            Converts a double to a LeftPosition.
            The double is interpreted as a Unit in Point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.op_Implicit(System.Int32)~MigraDoc.DocumentObjectModel.Shapes.LeftPosition">
            <summary>
            Converts an integer to a LeftPosition. 
            The integer is interpreted as a Unit in Point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.Parse(System.String)">
            <summary>
            Parses the specified value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts LeftPosition into DDL.
            </summary>  
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.NullValue">
            <summary>
            Returns the unitialized LeftPosition object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.MigraDoc#DocumentObjectModel#Internals#INullableValue#IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.Position">
            <summary>
            Gets the value of the position in unit.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.LeftPosition.ShapePosition">
            <summary>
            Gets the value of the position.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Barcode">
            <summary>
            Represents a barcode in the document or paragraph. !!!Still under Construction!!!
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Barcode.#ctor">
            <summary>
            Initializes a new instance of the Barcode class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Barcode.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Barcode class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Barcode.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Barcode.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Barcode into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Barcode.Orientation">
            <summary>
            Gets or sets the text orientation for the barcode content.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Barcode.Type">
            <summary>
            Gets or sets the type of the barcode.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Barcode.BearerBars">
            <summary>
            Gets or sets a value indicating whether bars shall appear beside the barcode
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Barcode.Text">
            <summary>
            Gets or sets the a value indicating whether the barcode's code is rendered.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Barcode.Code">
            <summary>
            Gets or sets code the barcode represents.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Barcode.LineRatio">
            <summary>
            ???
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Barcode.LineHeight">
            <summary>
            ???
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Barcode.NarrowLineWidth">
            <summary>
            ???
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Barcode.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.HorizontalAlignment">
            <summary>
            Used to determine the horizontal alignment of the axis title.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.HorizontalAlignment.Left">
            <summary>
            Axis title will be left aligned.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.HorizontalAlignment.Right">
            <summary>
            Axis title will be right aligned.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.HorizontalAlignment.Center">
            <summary>
            Axis title will be centered.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.DdlParserException">
            <summary>
            Represents an exception used by the DDL parser. This exception will always be caught inside
            the DDL parser.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParserException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the DdlParserException class with the specified message.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParserException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the DdlParserException class with the specified message and the
            inner exception.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParserException.#ctor(MigraDoc.DocumentObjectModel.IO.DdlErrorLevel,System.String,MigraDoc.DocumentObjectModel.DomMsgID)">
            <summary>
            Initializes a new instance of the DdlParserException class with the specified error level, name,
            error code and message.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParserException.#ctor(MigraDoc.DocumentObjectModel.IO.DdlReaderError)">
            <summary>
            Initializes a new instance of the DdlParserException class with the DdlReaderError.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlParserException.Error">
            <summary>
            Gets the DdlReaderError.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.DdlParser">
            <summary>
            A simple hand-coded parser for MigraDoc DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.#ctor(System.String,MigraDoc.DocumentObjectModel.IO.DdlReaderErrors)">
            <summary>
            Initializes a new instance of the DdlParser class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.#ctor(System.String,System.String,MigraDoc.DocumentObjectModel.IO.DdlReaderErrors)">
            <summary>
            Initializes a new instance of the DdlParser class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseDocument(MigraDoc.DocumentObjectModel.Document)">
            <summary>
            Parses the keyword «\document».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseDocumentObject">
            <summary>
            Parses one of the keywords «\document», «\styles», «\section», «\table», «\textframe», «\chart»
            and «\paragraph» and returns the corresponding DocumentObject or DocumentObjectCollection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseStyles(MigraDoc.DocumentObjectModel.Styles)">
            <summary>
            Parses the keyword «\styles».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseStyleDefinition(MigraDoc.DocumentObjectModel.Styles)">
            <summary>
            Parses a style definition block within the keyword «\styles».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.IsHeaderFooter">
            <summary>
            Determines if the current symbol is a header or footer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseSection(MigraDoc.DocumentObjectModel.Sections)">
            <summary>
            Parses the keyword «\section».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseHeaderFooter(MigraDoc.DocumentObjectModel.Section)">
            <summary>
            Parses the keywords «\header».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.IsParagraphContent">
            <summary>
            Determines whether the next text is paragraph content or document element.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseDocumentElements(MigraDoc.DocumentObjectModel.DocumentElements,MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            Parses the document elements of a «\paragraph», «\cell» or comparable.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseParagraph(MigraDoc.DocumentObjectModel.DocumentElements)">
            <summary>
            Parses the keyword «\paragraph».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseParagraphContent(MigraDoc.DocumentObjectModel.DocumentElements,MigraDoc.DocumentObjectModel.Paragraph)">
            <summary>
            Parses the inner text of a paragraph, i.e. stops on BraceRight and treats empty
            line as paragraph separator.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.RemoveTrailingBlank(MigraDoc.DocumentObjectModel.ParagraphElements)">
            <summary>
            Removes the last blank from the text. Used before a tab, a linebreak or a space will be
            added to the text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseFormattedText(MigraDoc.DocumentObjectModel.ParagraphElements,System.Int32)">
            <summary>
            Parses the inner text of a paragraph. Parsing ends if '}' is reached or an empty
            line occurs on nesting level 0.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseBoldItalicEtc(MigraDoc.DocumentObjectModel.FormattedText,System.Int32)">
            <summary>
            Parses the keywords «\bold», «\italic», and «\underline».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseFont(MigraDoc.DocumentObjectModel.FormattedText,System.Int32)">
            <summary>
            Parses the keyword «\font».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseElementName">
            <summary>
            Parses code like «("name")».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseFontSize(MigraDoc.DocumentObjectModel.FormattedText,System.Int32)">
            <summary>
            Parses the keyword «\fontsize».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseFontColor(MigraDoc.DocumentObjectModel.FormattedText,System.Int32)">
            <summary>
            Parses the keyword «\fontcolor».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseSymbol(MigraDoc.DocumentObjectModel.ParagraphElements)">
            <summary>
            Parses the keyword «\symbol» resp. «\(».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseChr(MigraDoc.DocumentObjectModel.ParagraphElements)">
            <summary>
            Parses the keyword «\chr».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseField(MigraDoc.DocumentObjectModel.ParagraphElements,System.Int32)">
            <summary>
            Parses the keyword «\field».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseFootnote(MigraDoc.DocumentObjectModel.ParagraphElements,System.Int32)">
            <summary>
            Parses the keyword «\footnote».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseHyperlink(MigraDoc.DocumentObjectModel.ParagraphElements,System.Int32)">
            <summary>
            Parses the keyword «\hyperlink».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseSpace(MigraDoc.DocumentObjectModel.ParagraphElements,System.Int32)">
            <summary>
            Parses the keyword «\space».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParsePageBreak(MigraDoc.DocumentObjectModel.DocumentElements)">
            <summary>
            Parses a page break in a document elements container.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseTable(MigraDoc.DocumentObjectModel.DocumentElements,MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Parses the keyword «\table».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseColumns(MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Parses the keyword «\columns».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseColumn(MigraDoc.DocumentObjectModel.Tables.Column)">
            <summary>
            Parses the keyword «\column».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseRows(MigraDoc.DocumentObjectModel.Tables.Table)">
            <summary>
            Parses the keyword «\rows».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseRow(MigraDoc.DocumentObjectModel.Tables.Row)">
            <summary>
            Parses the keyword «\row».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseCell(MigraDoc.DocumentObjectModel.Tables.Cell)">
            <summary>
            Parses the keyword «\cell».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseImage(MigraDoc.DocumentObjectModel.Shapes.Image,System.Boolean)">
            <summary>
            Parses the keyword «\image».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseTextFrame(MigraDoc.DocumentObjectModel.DocumentElements)">
            <summary>
            Parses the keyword «\textframe».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseChart(MigraDoc.DocumentObjectModel.DocumentElements)">
            <summary>
            Parses the keyword «\chart».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseArea(MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea)">
            <summary>
            Parses the keyword «\plotarea» inside a chart.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseArea(MigraDoc.DocumentObjectModel.Shapes.Charts.TextArea)">
            <summary>
            Parses the keywords «\headerarea», «\toparea», «\bottomarea», «\footerarea»,
            «\leftarea» or «\rightarea» inside a chart.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseAxes(MigraDoc.DocumentObjectModel.Shapes.Charts.Axis,MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            Parses the keywords «\xaxis», «\yaxis» or «\zaxis» inside a chart.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseSeries(MigraDoc.DocumentObjectModel.Shapes.Charts.Series)">
            <summary>
            Parses the keyword «\series» inside a chart.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseSeries(MigraDoc.DocumentObjectModel.Shapes.Charts.XSeries)">
            <summary>
            Parses the keyword «\xvalues» inside a chart.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParsePoint(MigraDoc.DocumentObjectModel.Shapes.Charts.Point)">
            <summary>
            Parses the keyword «\point» inside a series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseLegend(MigraDoc.DocumentObjectModel.Shapes.Charts.Legend)">
            <summary>
            Parses the keyword «\legend» inside a textarea.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseAttributes(MigraDoc.DocumentObjectModel.DocumentObject,System.Boolean)">
            <summary>
            Parses an attribute declaration block enclosed in brackets «[…]». If readNextSymbol is
            set to true, the closing bracket will be read.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseAttributes(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Parses an attribute declaration block enclosed in brackets «[…]».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseAttributeStatement(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Parses a single statement in an attribute declaration block.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseAttributeBlock(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Parses an attribute declaration block enclosed in braces «{…}».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseAssign(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses an assign statement in an attribute declaration block.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseBoolAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to a boolean l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseIntegerAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to an integer l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseRealAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to a floating point l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseUnitAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to a Unit l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseStringAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to a string l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseEnumAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to an enum l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseValueTypeAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to a struct (i.e. LeftPosition) l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseDocumentObjectAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to a DocumentObject l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseValueAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to a Value l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseColorAssignment(MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.DocumentObjectModel.Internals.ValueDescriptor)">
            <summary>
            Parses the assignment to a Color l-value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseColor">
            <summary>
            Parses a color. It can be «green», «123456», «0xFFABCDEF», 
            «RGB(r, g, b)», «CMYK(c, m, y, k)», «CMYK(a, c, m, y, k)», «GRAY(g)», or «"MyColor"».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseRGB">
            <summary>
            Parses «RGB(r, g, b)».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseCMYK">
            <summary>
            Parses «CMYK(c, m, y, k)» or «CMYK(a, c, m, y, k)».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ParseGray">
            <summary>
            Parses «GRAY(g)».
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.GetSymbolText(MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            Determines the name/text of the given symbol.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.IsSpaceType(System.String)">
            <summary>
            Returns whether the specified type is a valid SpaceType.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.IsSymbolType(System.String)">
            <summary>
            Returns whether the specified type is a valid enum for \symbol.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.AssertCondition(System.Boolean,MigraDoc.DocumentObjectModel.DomMsgID,System.Object[])">
            <summary>
            If cond is evaluated to false, a DdlParserException with the specified error will be thrown.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.AssertSymbol(MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            If current symbol is not equal symbol a DdlParserException will be thrown.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.AssertSymbol(MigraDoc.DocumentObjectModel.IO.Symbol,MigraDoc.DocumentObjectModel.DomMsgID)">
            <summary>
            If current symbol is not equal symbol a DdlParserException with the specified message id
            will be thrown.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.AssertSymbol(MigraDoc.DocumentObjectModel.IO.Symbol,MigraDoc.DocumentObjectModel.DomMsgID,System.Object[])">
            <summary>
            If current symbol is not equal symbol a DdlParserException with the specified message id
            will be thrown.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ReportParserInfo(MigraDoc.DocumentObjectModel.IO.DdlErrorLevel,MigraDoc.DocumentObjectModel.DomMsgID,System.String[])">
            <summary>
            Creates an ErrorInfo based on the given errorlevel, error and parms and adds it to the ErrorManager2.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ReportParserException(MigraDoc.DocumentObjectModel.DomMsgID,System.String[])">
            <summary>
            Creates an ErrorInfo based on the given error and parms and adds it to the ErrorManager2.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ReportParserException(MigraDoc.DocumentObjectModel.IO.DdlParserException)">
            <summary>
            Adds the ErrorInfo from the ErrorInfoException2 to the ErrorManager2.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ReportParserException(System.Exception,MigraDoc.DocumentObjectModel.DomMsgID,System.String[])">
            <summary>
            Creates an ErrorInfo based on the given inner exception, error and parms and adds it to the ErrorManager2.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ThrowParserException(MigraDoc.DocumentObjectModel.DomMsgID,System.Object[])">
            <summary>
            Creates an ErrorInfo based on the DomMsgID and the specified parameters.
            Throws a DdlParserException with that ErrorInfo.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ThrowParserException(System.Exception,MigraDoc.DocumentObjectModel.DomMsgID,System.Object[])">
            <summary>
            Determines the error message based on the DomMsgID and the parameters.
            Throws a DdlParserException with that error message and the Exception as the inner exception.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.AdjustToNextBlock">
            <summary>
            Used for exception handling. Sets the DDL stream to the next valid position behind
            the current block.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.AdjustToNextStatement">
            <summary>
            Used for exception handling. Sets the DDL stream to the next valid position behind
            the current statement.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ReadCode">
            <summary>
            Shortcut for scanner.ReadCode().
            Reads the next DDL token. Comments are ignored.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.ReadText(System.Boolean)">
            <summary>
            Shortcut for scanner.ReadText().
            Reads either text or \keyword from current position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.MoveToCode">
            <summary>
            Shortcut for scanner.MoveToCode().
            Moves to the next DDL token if Symbol is not set to a valid position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.MoveToParagraphContent">
            <summary>
            Shortcut for scanner.MoveToParagraphContent().
            Moves to the first character the content of a paragraph starts with. Empty lines
            and comments are skipped. Returns true if such a character exists, and false if the
            paragraph ends without content.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlParser.MoveToNextParagraphContentLine(System.Boolean)">
            <summary>
            Shortcut for scanner.MoveToNextParagraphContentLine().
            Moves to the first character of the content of a paragraph beyond an EOL. 
            Returns true if such a character exists and belongs to the current paragraph.
            Returns false if a new line (at root level) or '}' occurs. If a new line caused
            the end of the paragraph, the DDL cursor is moved to the next valid content
            character or '}' respectively.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlParser.Symbol">
            <summary>
            Gets the current symbol from the scanner.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlParser.Token">
            <summary>
            Gets the current token from the scanner.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlParser.TokenType">
            <summary>
            Gets the current token type from the scanner.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Styles">
            <summary>
            Represents the collection of all styles.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.#ctor">
            <summary>
            Initializes a new instance of the Styles class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Styles class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.GetIndex(System.String)">
            <summary>
            Gets the index of a style by name.
            </summary>
            <param name="styleName">Name of the style looking for.</param>
            <returns>Index or -1 if not exists.</returns>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.AddStyle(System.String,System.String)">
            <summary>
            Adds a new style to the styles collection.
            </summary>
            <param name="name">Name of the style.</param>
            <param name="baseStyleName">Name of the base style.</param>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.Add(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Adds a DocumentObject to the styles collection.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.SetupStyles">
            <summary>
            Initialize the built in styles.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Styles into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.SerializeStyle(MigraDoc.DocumentObjectModel.Serializer,System.Int32,System.Boolean[]@,System.Boolean[]@,System.Boolean@)">
            <summary>
            Serialize a style, but serialize its base style first (if that was not yet done).
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Styles.VisitStyle(System.Collections.Hashtable,MigraDoc.DocumentObjectModel.Style,MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Ensures that base styles are visited first.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Styles.Item(System.String)">
            <summary>
            Gets a style by its name.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Styles.Item(System.Int32)">
            <summary>
            Gets a style by index. 
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Styles.Normal">
            <summary>
            Gets the default paragraph style.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Styles.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Styles.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Underline">
            <summary>
            Specifies the underline type for the font.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.LineSpacingRule">
            <summary>
            Specifies the space between lines in a paragraph.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.HyperlinkType">
            <summary>
            Specifies the target of the hyperlink.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.HyperlinkType.Local">
            <summary>
            Targets a position in the document. Same as 'Bookmark'.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.HyperlinkType.Bookmark">
            <summary>
            Targets a position in the document. Same as 'Local'.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.HyperlinkType.Web">
            <summary>
            Targets a resource on the Internet or network. Same as 'Url'.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.HyperlinkType.Url">
            <summary>
            Targets a resource on the Internet or network. Same as 'Web'.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.HyperlinkType.File">
            <summary>
            Targets a physical file.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.Rows">
            <summary>
            Represents the collection of all rows of a table.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Rows.#ctor">
            <summary>
            Initializes a new instance of the Rows class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Rows.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Rows class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Rows.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Rows.AddRow">
            <summary>
            Adds a new row to the rows collection. Allowed only if at least one column exists.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Rows.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Rows into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Rows.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Rows.Table">
            <summary>
            Gets the table the rows collection belongs to.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Rows.Item(System.Int32)">
            <summary>
            Gets a row by its index.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Rows.Alignment">
            <summary>
            Gets or sets the row alignment of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Rows.LeftIndent">
            <summary>
            Gets or sets the left indent of the table. If row alignment is not Left, 
            the value is ignored.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Rows.VerticalAlignment">
            <summary>
            Gets or sets the default vertical alignment for all rows.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Rows.Height">
            <summary>
            Gets or sets the height of the rows.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Rows.HeightRule">
            <summary>
            Gets or sets the rule which is used to determine the height of the rows.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Rows.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Rows.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Image">
            <summary>
            Represents an image in the document or paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Image.#ctor">
            <summary>
            Initializes a new instance of the Image class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Image.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Image class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Image.#ctor(System.String)">
            <summary>
            Initializes a new instance of the Image class from the specified (file) name.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Image.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Image.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Image.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Image into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Image.GetFilePath(System.String)">
            <summary>
            Gets the concrete image path, taking into account the DOM document's DdlFile and
            ImagePath properties as well as the given working directory (which can be null).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Image.Name">
            <summary>
            Gets or sets the name of the image.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Image.ScaleWidth">
            <summary>
            Gets or sets the ScaleWidth of the image.
            If the Width is set to, the resulting image width is ScaleWidth * Width.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Image.ScaleHeight">
            <summary>
            Gets or sets the ScaleHeight of the image.
            If the Height is set too, the resulting image height is ScaleHeight * Height.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Image.LockAspectRatio">
            <summary>
            Gets or sets whether the AspectRatio of the image is kept unchanged.
            If both Width and Height are set, this property is ignored.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Image.PictureFormat">
            <summary>
            Gets or sets the PictureFormat for the image
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Image.Resolution">
            <summary>
            Gets or sets a user defined resolution for the image in dots per inch.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Image.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Unit">
            <summary>
            An Unit consist of a numerical value and an UnitType like Centimeter, Millimeter or Inch.
            Several conversion between different measures are supported.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.#ctor(System.Double)">
            <summary>
            Initializes a new instance of the Unit class with type set to point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.#ctor(System.Double,MigraDoc.DocumentObjectModel.UnitType)">
            <summary>
            Initializes a new instance of the Unit class.
            Throws System.ArgumentException if <code>type</code> is invalid.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.MigraDoc#DocumentObjectModel#Internals#INullableValue#GetValue">
            <summary>
            Gets the value of the unit.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetValue(System.Object)">
            <summary>
            Sets the unit to the given value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetNull">
            <summary>
            Resets this instance,
            i.e. IsNull() will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.ToString(System.IFormatProvider)">
            <summary>
            Returns the object as string using the format information.
            Measure will be added to the end of the string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.ToString(System.String)">
            <summary>
            Returns the object as string using the format.
            Measure will be added to the end of the string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.System#IFormattable#ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns the object as string using the specified format and format information.
            Measure will be added to the end of the string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.ToString">
            <summary>
            Returns the object as string. Measure will be added to the end of the string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.GetSuffix">
            <summary>
            Returns the type of the object as a string like 'pc', 'cm', or 'in'. Empty string is equal to 'pt'.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.FromCentimeter(System.Double)">
            <summary>
            Returns an Unit object. Sets type to centimeter.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.FromMillimeter(System.Double)">
            <summary>
            Returns an Unit object. Sets type to millimeter.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.FromMm(System.Double)">
            <summary>
            Returns an Unit object. Sets type to millimeter.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.FromPoint(System.Double)">
            <summary>
            Returns an Unit object. Sets type to point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.FromInch(System.Double)">
            <summary>
            Returns an Unit object. Sets type to inch.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.FromPica(System.Double)">
            <summary>
            Returns an Unit object. Sets type to pica.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.op_Implicit(System.String)~MigraDoc.DocumentObjectModel.Unit">
            <summary>
            Converts a string to an Unit object.
            If the string contains a suffix like 'cm' or 'in' the object will be converted
            to the appropriate type, otherwise point is assumed.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.op_Implicit(System.Int32)~MigraDoc.DocumentObjectModel.Unit">
            <summary>
            Converts an int to an Unit object with type set to point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.op_Implicit(System.Single)~MigraDoc.DocumentObjectModel.Unit">
            <summary>
            Converts a float to an Unit object with type set to point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.op_Implicit(System.Double)~MigraDoc.DocumentObjectModel.Unit">
            <summary>
            Converts a double to an Unit object with type set to point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.op_Implicit(MigraDoc.DocumentObjectModel.Unit)~System.Double">
            <summary>
            Returns a double value as point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.op_Implicit(MigraDoc.DocumentObjectModel.Unit)~System.Single">
            <summary>
            Returns a float value as point.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.op_Equality(MigraDoc.DocumentObjectModel.Unit,MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Memberwise comparison. To compare by value, 
            use code like Math.Abs(a.Point - b.Point) &lt; 1e-5.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.op_Inequality(MigraDoc.DocumentObjectModel.Unit,MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Memberwise comparison. To compare by value, 
            use code like Math.Abs(a.Point - b.Point) &lt; 1e-5.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.Equals(System.Object)">
            <summary>
            Calls base class Equals.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.GetHashCode">
            <summary>
            Calls base class GetHashCode.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.Parse(System.String)">
            <summary>
            This member is intended to be used by XmlDomainObjectReader only.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Unit.ConvertType(MigraDoc.DocumentObjectModel.UnitType)">
            <summary>
            Converts an existing object from one unit into another unit type.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Unit.Empty">
            <summary>
            Represents the uninitialized Unit object.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Unit.Zero">
            <summary>
            Represents an initialized Unit object with value 0 and unit type point.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Unit.NullValue">
            <summary>
            Represents the uninitialized Unit object. Same as Unit.Empty.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.IsEmpty">
            <summary>
            Determines whether this instance is empty.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.MigraDoc#DocumentObjectModel#Internals#INullableValue#IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.Value">
            <summary>
            Gets or sets the raw value of the object without any conversion.
            To determine the UnitType use property <code>Type</code>.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.Type">
            <summary>
            Gets the UnitType of the object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.Point">
            <summary>
            Gets or sets the value in point.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.Centimeter">
            <summary>
            Gets or sets the value in centimeter.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.Inch">
            <summary>
            Gets or sets the value in inch.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.Millimeter">
            <summary>
            Gets or sets the value in millimeter.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Unit.Pica">
            <summary>
            Gets or sets the value in pica.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.TextFormat">
            <summary>
            Specifies the format of a text.
            Bold, Italic, or Underline will be ignored if NotBold, NotItalic, or NoUnderline respectively are specified at the same time.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TextFormat.Bold">
            <summary>
            Specifies bold text (heavy font weight).
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TextFormat.NotBold">
            <summary>
            Specifies normal font weight.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TextFormat.Italic">
            <summary>
            Specifies italic text.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TextFormat.NotItalic">
            <summary>
            Specifies upright text.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TextFormat.Underline">
            <summary>
            Specifies underlined text.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TextFormat.NoUnderline">
            <summary>
            Specifies text without underline.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.StyleType">
            <summary>
            Specifies the type of a Style object.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.StyleType.Paragraph">
            <summary>
            Style is a paragraph style.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.StyleType.Character">
            <summary>
            Style is a character style. Contains font part only.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Document">
            <summary>
            Represents a MigraDoc document.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.#ctor">
            <summary>
            Initializes a new instance of the Document class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.BindToRenderer(System.Object)">
            <summary>
            Internal function used by renderers to bind this instance to it. 
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.AddSection">
            <summary>
            Adds a new section to the document.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.AddStyle(System.String,System.String)">
            <summary>
            Adds a new style to the document styles.
            </summary>
            <param name="name">Name of the style.</param>
            <param name="baseStyle">Name of the base style.</param>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.Add(MigraDoc.DocumentObjectModel.Section)">
            <summary>
            Adds a new section to the document.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.Add(MigraDoc.DocumentObjectModel.Style)">
            <summary>
            Adds a new style to the document styles.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Document into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Document.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and all it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.IsBoundToRenderer">
            <summary>
            Indicates whether the document is bound to a renderer. A bound document must not be modified anymore.
            Modifying it leads to undefined results of the rendering process.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.LastSection">
            <summary>
            Gets the last section of the document, or null, if the document has no sections.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.Info">
            <summary>
            Gets the document info.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.Styles">
            <summary>
            Gets or sets the styles of the document.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.DefaultTabStop">
            <summary>
            Gets or sets the default tab stop position.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.DefaultPageSetup">
            <summary>
            Gets the default page setup.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.FootnoteLocation">
            <summary>
            Gets or sets the location of the Footnote.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.FootnoteNumberingRule">
            <summary>
            Gets or sets the rule which is used to determine the footnote number on a new page.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.FootnoteNumberStyle">
            <summary>
            Gets or sets the type of number which is used for the footnote.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.FootnoteStartingNumber">
            <summary>
            Gets or sets the starting number of the footnote.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.ImagePath">
            <summary>
            Gets or sets the path for images used by the document.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.UseCmykColor">
            <summary>
            Gets or sets a value indicating whether to use the CMYK color model when rendered as PDF.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.Sections">
            <summary>
            Gets the sections of the document.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.DdlFile">
            <summary>
            Gets the DDL file name.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Document.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.DdlVisibleAttribute">
            <summary>
            Under Construction.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DdlVisibleAttribute.#ctor">
            <summary>
            Initializes a new instance of the DdlVisibleAttribute class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.DdlVisibleAttribute.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the DdlVisibleAttribute class with the specified visibility.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.DdlVisibleAttribute.Visible">
            <summary>
            Gets or sets the visibility.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.Table">
            <summary>
            Represents a table in a document.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.#ctor">
            <summary>
            Initializes a new instance of the Table class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Table class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.AddColumn">
            <summary>
            Adds a new column to the table. Allowed only before any row was added.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.AddColumn(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Adds a new column of the specified width to the table. Allowed only before any row was added.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.AddRow">
            <summary>
            Adds a new row to the table. Allowed only if at least one column was added.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.SetShading(System.Int32,System.Int32,System.Int32,System.Int32,MigraDoc.DocumentObjectModel.Color)">
            <summary>
            Sets a shading of the specified Color in the specified Tablerange.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.SetEdge(System.Int32,System.Int32,System.Int32,System.Int32,MigraDoc.DocumentObjectModel.Tables.Edge,MigraDoc.DocumentObjectModel.BorderStyle,MigraDoc.DocumentObjectModel.Unit,MigraDoc.DocumentObjectModel.Color)">
            <summary>
            Sets the borders surrounding the specified range of the table.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.SetEdge(System.Int32,System.Int32,System.Int32,System.Int32,MigraDoc.DocumentObjectModel.Tables.Edge,MigraDoc.DocumentObjectModel.BorderStyle,MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Sets the borders surrounding the specified range of the table.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Table into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Table.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.IsEmpty">
            <summary>
            Returns true if no cell exists in the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.Columns">
            <summary>
            Gets or sets the Columns collection of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.Rows">
            <summary>
            Gets the Rows collection of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.Style">
            <summary>
            Sets or gets the default style name for all rows and columns of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.Format">
            <summary>
            Gets the default ParagraphFormat for all rows and columns of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.TopPadding">
            <summary>
            Gets or sets the default top padding for all cells of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.BottomPadding">
            <summary>
            Gets or sets the default bottom padding for all cells of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.LeftPadding">
            <summary>
            Gets or sets the default left padding for all cells of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.RightPadding">
            <summary>
            Gets or sets the default right padding for all cells of the table.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.Borders">
            <summary>
            Gets the default Borders object for all cells of the column.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.Shading">
            <summary>
            Gets the default Shading object for all cells of the column.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.KeepTogether">
            <summary>
            Gets or sets a value indicating whether
            to keep all the table rows on the same page.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.Item(System.Int32,System.Int32)">
            <summary>
            Gets the cell with the given row and column indices.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Table.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.RowAlignment">
            <summary>
            Specifies the horizontal alignment of the table.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.ShapePosition">
            <summary>
            Specifies the position of a shape. Values are used for both LeftPositon and TopPosition.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.ShapePosition.Undefined">
            <summary>
            Undefined position.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.ShapePosition.Left">
            <summary>
            Left-aligned position.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.ShapePosition.Right">
            <summary>
            Right-aligned position.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.ShapePosition.Center">
            <summary>
            Centered position.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.ShapePosition.Top">
            <summary>
            Top-aligned position.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.ShapePosition.Bottom">
            <summary>
            Bottom-aligned position.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.ShapePosition.Inside">
            <summary>
            Used with mirrored margins: left-aligned on right page and right-aligned on left page.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.ShapePosition.Outside">
            <summary>
            Used with mirrored margins: left-aligned on left page and right-aligned on right page.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.RelativeHorizontal">
            <summary>
            Reference point of the Left attribute.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.RelativeHorizontal.Character">
            <summary>
            Alignment relative to the right side of the previous element.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.RelativeHorizontal.Column">
            <summary>
            Alignment relative to the right side of the previous element.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.RelativeHorizontal.Margin">
            <summary>
            Alignment relative to page margin.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.RelativeHorizontal.Page">
            <summary>
            Alignment relative to page edge.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.BarcodeType">
            <summary>
            Specifies the type of the barcode.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.BarcodeType.Barcode25i">
            <summary>
            Barcode "Interleaved 2 of 5"
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.BarcodeType.Barcode39">
            <summary>
            Barcode "3 of 9"
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.BarcodeType.Barcode128">
            <summary>
            Barcode "Code 128"
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea">
            <summary>
            Represents the area where the actual chart is drawn.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.#ctor">
            <summary>
            Initializes a new instance of the PlotArea class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the PlotArea class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts PlotArea into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.LineFormat">
            <summary>
            Gets the line format of the plot area's border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.FillFormat">
            <summary>
            Gets the background filling of the plot area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.LeftPadding">
            <summary>
            Gets or sets the left padding of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.RightPadding">
            <summary>
            Gets or sets the right padding of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.TopPadding">
            <summary>
            Gets or sets the top padding of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.BottomPadding">
            <summary>
            Gets or sets the bottom padding of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.PlotArea.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.NBool">
            <summary>
            Represents a nullable boolean value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NBool.MigraDoc#DocumentObjectModel#Internals#INullableValue#GetValue">
            <summary>
            Gets the value of the instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NBool.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetValue(System.Object)">
            <summary>
            Sets the value of the instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NBool.SetNull">
            <summary>
            Resets this instance,
            i.e. IsNull() will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NBool.Equals(System.Object)">
            <summary>
            Returns a value indicating whether this instance is equal to the specified object.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.NBool.val">
            <summary>
            -1 (undefined), 0 (false), or 1 (true).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.NBool.Value">
            <summary>
            Gets or sets the value of the instance.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.NBool.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.SectionField">
            <summary>
            SectionField is used to reference the number of the current section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.SectionField.#ctor">
            <summary>
            Initializes a new instance of the SectionField class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.SectionField.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the SectionField class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.SectionField.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Fields.SectionField.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts SectionField into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Fields.SectionField.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.TabStops">
            <summary>
            A TabStops collection represents all TabStop objects in a paragraph.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.TabStops.TabStopPrecision">
            <summary>
            Specifies the minimal spacing between two TabStop positions.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.#ctor">
            <summary>
            Initializes a new instance of the TabStops class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the TabStops class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.GetTabStopAt(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Gets a TabStop by its position.
            Note that also Removed TabStops are taken into account.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.TabStopExists(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Returns whether a TabStop exists at the given position.
            Note that also Removed TabStops are taken into account
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.AddTabStop(MigraDoc.DocumentObjectModel.TabStop)">
            <summary>
            Adds a TabStop object to the collection. If a TabStop with the same position
            already exists, it is replaced by the new TabStop.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.AddTabStop(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Adds a TabStop object at the specified position to the collection. If a TabStop with the
            same position already exists, it is replaced by the new TabStop.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.AddTabStop(MigraDoc.DocumentObjectModel.Unit,MigraDoc.DocumentObjectModel.TabAlignment,MigraDoc.DocumentObjectModel.TabLeader)">
            <summary>
            Adds a TabStop object to the collection and sets its alignment and leader.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.AddTabStop(MigraDoc.DocumentObjectModel.Unit,MigraDoc.DocumentObjectModel.TabLeader)">
            <summary>
            Adds a TabStop object to the collection and sets its leader.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.AddTabStop(MigraDoc.DocumentObjectModel.Unit,MigraDoc.DocumentObjectModel.TabAlignment)">
            <summary>
            Adds a TabStop object to the collection and sets its alignment.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.RemoveTabStop(MigraDoc.DocumentObjectModel.Unit)">
            <summary>
            Adds a TabStop object to the collection marked to remove the tab stop at
            the given position.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.ClearAll">
            <summary>
            Clears all TabStop objects from the collection. Additionally 'TabStops = null'
            is written to the DDL stream when serialized.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts TabStops into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.TabStops.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.TabStops.Item(System.Int32)">
            <summary>
            Gets a TabStop by its index.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.TabStops.TabsCleared">
            <summary>
            Gets the information if the collection is marked as cleared. Additionally 'TabStops = null'
            is written to the DDL stream when serialized.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.TabStops.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.PageSetup">
            <summary>
            Represents the page setup of a section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageSetup.#ctor">
            <summary>
            Initializes a new instance of the PageSetup class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageSetup.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the PageSetup class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageSetup.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageSetup.GetPageSize(MigraDoc.DocumentObjectModel.PageFormat,MigraDoc.DocumentObjectModel.Unit@,MigraDoc.DocumentObjectModel.Unit@)">
            <summary>
            Gets the page's size and height for the given PageFormat.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageSetup.PreviousPageSetup">
            <summary>
            Gets the PageSetup of the previous section, or null, if the page setup belongs 
            to the first section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.PageSetup.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts PageSetup into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.SectionStart">
            <summary>
            Gets or sets a value which defines whether the section starts on next, odd or even page.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.Orientation">
            <summary>
            Gets or sets the page orientation of the section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.PageWidth">
            <summary>
            Gets or sets the page width.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.StartingNumber">
            <summary>
            Gets or sets the starting number for the first section page.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.PageHeight">
            <summary>
            Gets or sets the page height.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.TopMargin">
            <summary>
            Gets or sets the top margin of the pages in the section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.BottomMargin">
            <summary>
            Gets or sets the bottom margin of the pages in the section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.LeftMargin">
            <summary>
            Gets or sets the left margin of the pages in the section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.RightMargin">
            <summary>
            Gets or sets the right margin of the pages in the section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.OddAndEvenPagesHeaderFooter">
            <summary>
            Gets or sets a value which defines whether the odd and even pages
            of the section have different header and footer.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.DifferentFirstPageHeaderFooter">
            <summary>
            Gets or sets a value which define whether the section has a different
            first page header and footer.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.HeaderDistance">
            <summary>
            Gets or sets the distance between the header and the page top
            of the pages in the section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.FooterDistance">
            <summary>
            Gets or sets the distance between the footer and the page bottom
            of the pages in the section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.MirrorMargins">
            <summary>
            Gets or sets a value which defines whether the odd and even pages
            of the section should change left and right margin.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.HorizontalPageBreak">
            <summary>
            Gets or sets a value which defines whether a page should break horizontally.
            Currently only tables are supported.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.PageFormat">
            <summary>
            Gets or sets the page format of the section.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.DefaultPageSetup">
            <summary>
            Gets a PageSetup object with default values for all properties.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.PageSetup.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Color">
            <summary>
            The Color class represents an ARGB color value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.#ctor(System.UInt32)">
            <summary>
            Initializes a new instance of the Color class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.#ctor(System.Byte,System.Byte,System.Byte)">
            <summary>
            Initializes a new instance of the Color class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.#ctor(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Initializes a new instance of the Color class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.#ctor(System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the Color class with a CMYK color.
            All values must be in a range between 0 to 100 percent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.#ctor(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the Color class with a CMYK color.
            All values must be in a range between 0 to 100 percent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.MigraDoc#DocumentObjectModel#Internals#INullableValue#GetValue">
            <summary>
            Returns the value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetValue(System.Object)">
            <summary>
            Sets the given value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetNull">
            <summary>
            Resets this instance, i.e. IsNull() will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.Equals(System.Object)">
            <summary>
            Calls base class Equals.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.GetHashCode">
            <summary>
            Gets the ARGB value that this Color instance represents.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.op_Equality(MigraDoc.DocumentObjectModel.Color,MigraDoc.DocumentObjectModel.Color)">
            <summary>
            Compares two color objects. True if both argb values are equal, false otherwise.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.op_Inequality(MigraDoc.DocumentObjectModel.Color,MigraDoc.DocumentObjectModel.Color)">
            <summary>
            Compares two color objects. True if both argb values are not equal, false otherwise.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.Parse(System.String)">
            <summary>
            Parses the string and returns a color object.
            Throws ArgumentException if color is invalid.
            </summary>
            <param name="color">integer, hex or color name.</param>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.GetMixedTransparencyColor">
            <summary>
            Gets a non transparent color brightened in terms of transparency if any is given(A &lt; 255),
            otherwise this instance itself.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.ToString">
            <summary>
            Writes the Color object in its hexadecimal value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.FromRgbColor(System.Byte,MigraDoc.DocumentObjectModel.Color)">
            <summary>
            Creates an RGB color from an existing color with a new alpha (transparency) value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.FromCmyk(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Creates a Color structure from the specified CMYK values.
            All values must be in a range between 0 to 100 percent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.FromCmyk(System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Creates a Color structure from the specified CMYK values.
            All values must be in a range between 0 to 100 percent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Color.FromCmykColor(System.Double,MigraDoc.DocumentObjectModel.Color)">
            <summary>
            Creates a CMYK color from an existing color with a new alpha (transparency) value.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Color.Empty">
            <summary>
            Represents a null color.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.IsCmyk">
            <summary>
            Gets a value indicating whether this instance is a CMYK color.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.IsEmpty">
            <summary>
            Determines whether this color is empty.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.MigraDoc#DocumentObjectModel#Internals#INullableValue#IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.Argb">
            <summary>
            Gets or sets the ARGB value.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.RGB">
            <summary>
            Gets or sets the RGB value.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.A">
            <summary>
            Gets the alpha (transparency) part of the RGB Color.
            The values is in the range between 0 to 255.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.R">
            <summary>
            Gets the red part of the Color.
            The values is in the range between 0 to 255.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.G">
            <summary>
            Gets the green part of the Color.
            The values is in the range between 0 to 255.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.B">
            <summary>
            Gets the blue part of the Color.
            The values is in the range between 0 to 255.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.Alpha">
            <summary>
            Gets the alpha (transparency) part of the CMYK Color.
            The values is in the range between 0 (transparent) to 100 (opaque) percent.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.C">
            <summary>
            Gets the cyan part of the Color.
            The values is in the range between 0 to 100 percent.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.M">
            <summary>
            Gets the magenta part of the Color.
            The values is in the range between 0 to 100 percent.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.Y">
            <summary>
            Gets the yellow part of the Color.
            The values is in the range between 0 to 100 percent.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Color.K">
            <summary>
            Gets the key (black) part of the Color.
            The values is in the range between 0 to 100 percent.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.XValue">
            <summary>
            Represents the actual value on the XSeries.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XValue.#ctor">
            <summary>
            Initializes a new instance of the XValue class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XValue.#ctor(System.String)">
            <summary>
            Initializes a new instance of the XValue class with the specified value.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.XValue.Value">
            <summary>
            The actual value of the XValue.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XValue.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.XValue.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts XValue into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.XValue.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.Point">
            <summary>
            Represents a formatted value on the data series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Point.#ctor">
            <summary>
            Initializes a new instance of the Point class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Point.#ctor(System.Double)">
            <summary>
            Initializes a new instance of the Point class with a real value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Point.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Point.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Point.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Point into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Point.LineFormat">
            <summary>
            Gets the line format of the data point's border.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Point.FillFormat">
            <summary>
            Gets the filling format of the data point.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Point.Value">
            <summary>
            The actual value of the data point.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Point.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.DdlWriter">
            <summary>
            Represents the MigraDoc DDL writer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the DdlWriter class with the specified Stream.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.#ctor(System.String)">
            <summary>
            Initializes a new instance of the DdlWriter class with the specified filename.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.#ctor(System.IO.TextWriter)">
            <summary>
            Initializes a new instance of the DdlWriter class with the specified TextWriter.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.Close">
            <summary>
            Closes the underlying serializer and text writer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.Flush">
            <summary>
            Flushes the underlying TextWriter.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteDocument(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Writes the specified DocumentObject to DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteDocument(MigraDoc.DocumentObjectModel.DocumentObjectCollection)">
            <summary>
            Writes the specified DocumentObjectCollection to DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToString(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Writes a DocumentObject type object to string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToString(MigraDoc.DocumentObjectModel.DocumentObject,System.Int32)">
            <summary>
            Writes a DocumentObject type object to string. Indent a new block by indent characters.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToString(MigraDoc.DocumentObjectModel.DocumentObject,System.Int32,System.Int32)">
            <summary>
            Writes a DocumentObject type object to string. Indent a new block by indent + initialIndent characters.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToString(MigraDoc.DocumentObjectModel.DocumentObjectCollection)">
            <summary>
            Writes a DocumentObjectCollection type object to string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToString(MigraDoc.DocumentObjectModel.DocumentObjectCollection,System.Int32)">
            <summary>
            Writes a DocumentObjectCollection type object to string. Indent a new block by _indent characters.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToString(MigraDoc.DocumentObjectModel.DocumentObjectCollection,System.Int32,System.Int32)">
            <summary>
            Writes a DocumentObjectCollection type object to string. Indent a new block by
            indent + initialIndent characters.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToFile(MigraDoc.DocumentObjectModel.DocumentObject,System.String)">
            <summary>
            Writes a document object to a DDL file.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToFile(MigraDoc.DocumentObjectModel.DocumentObject,System.String,System.Int32)">
            <summary>
            Writes a document object to a DDL file. Indent a new block by the specified number of characters.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToFile(MigraDoc.DocumentObjectModel.DocumentObject,System.String,System.Int32,System.Int32)">
            <summary>
            Writes a DocumentObject type object to a DDL file. Indent a new block by indent + initialIndent characters.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToFile(MigraDoc.DocumentObjectModel.DocumentObjectCollection,System.String)">
            <summary>
            Writes a DocumentObjectCollection type object to a DDL file.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToFile(MigraDoc.DocumentObjectModel.DocumentObjectCollection,System.String,System.Int32)">
            <summary>
            Writes a DocumentObjectCollection type object to a DDL file. Indent a new block by
            indent + initialIndent characters.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlWriter.WriteToFile(MigraDoc.DocumentObjectModel.DocumentObjectCollection,System.String,System.Int32,System.Int32)">
            <summary>
            Writes a DocumentObjectCollection type object to a DDL file. Indent a new block by
            indent + initialIndent characters.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlWriter.Indent">
            <summary>
            Gets or sets the indentation for the DDL file.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.IO.DdlWriter.InitialIndent">
            <summary>
            Gets or sets the initial indentation for the DDL file.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.DdlReader">
            <summary>
            Represents a reader that provides access to DDL data.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the DdlReader class with the specified Stream.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.#ctor(System.IO.Stream,MigraDoc.DocumentObjectModel.IO.DdlReaderErrors)">
            <summary>
            Initializes a new instance of the DdlReader class with the specified Stream and ErrorManager2.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.#ctor(System.String)">
            <summary>
            Initializes a new instance of the DdlReader class with the specified filename.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.#ctor(System.String,MigraDoc.DocumentObjectModel.IO.DdlReaderErrors)">
            <summary>
            Initializes a new instance of the DdlReader class with the specified filename and ErrorManager2.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.#ctor(System.IO.TextReader)">
            <summary>
            Initializes a new instance of the DdlReader class with the specified TextReader.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.#ctor(System.IO.TextReader,MigraDoc.DocumentObjectModel.IO.DdlReaderErrors)">
            <summary>
            Initializes a new instance of the DdlReader class with the specified TextReader and ErrorManager2.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.Close">
            <summary>
            Closes the underlying stream or text writer.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.ReadDocument">
            <summary>
            Reads and returns a Document from a file or a DDL string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.ReadObject">
            <summary>
            Reads and returns a DocumentObject from a file or a DDL string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.DocumentFromFile(System.String)">
            <summary>
            Reads and returns a Document from the specified file.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.DocumentFromString(System.String)">
            <summary>
            Reads and returns a Document from the specified DDL string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.ObjectFromFile(System.String,MigraDoc.DocumentObjectModel.IO.DdlReaderErrors)">
            <summary>
            Reads and returns a domain object from the specified file.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.ObjectFromFile(System.String)">
            <summary>
            Reads and returns a domain object from the specified file.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.ObjectFromString(System.String,MigraDoc.DocumentObjectModel.IO.DdlReaderErrors)">
            <summary>
            Reads and returns a domain object from the specified DDL string.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.DdlReader.ObjectFromString(System.String)">
            <summary>
            Reads and returns a domain object from the specified DDL string.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.NString">
            <summary>
            Represents a nullable string value.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NString.MigraDoc#DocumentObjectModel#Internals#INullableValue#GetValue">
            <summary>
            Returns the value of the instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NString.MigraDoc#DocumentObjectModel#Internals#INullableValue#SetValue(System.Object)">
            <summary>
            Sets the value of the instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NString.SetNull">
            <summary>
            Resets this instance,
            i.e. IsNull() will return true afterwards.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Internals.NString.Equals(System.Object)">
            <summary>
            Returns a value indicating whether this instance is equal to the specified object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.NString.Value">
            <summary>
            Gets or sets the value of the instance.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Internals.NString.IsNull">
            <summary>
            Determines whether this instance is null (not set).
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Internals.GV">
            <summary>
            Indicates how to retrieve a value from a DocumentObject.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.GV.ReadWrite">
            <summary>
            Gets the value for reading and writing. If the value does not exist, it is created.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.GV.ReadOnly">
            <summary>
            Gets the value for reading. If the value does not exist, it is not created.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Internals.GV.GetNull">
            <summary>
            Returns null if value is Null or does not exist.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Hyperlink">
            <summary>
            A Hyperlink is used to reference targets in the document (Local), on a drive (File) or a network (Web).
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.#ctor">
            <summary>
            Initializes a new instance of the Hyperlink class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Hyperlink class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the Hyperlink class with the text the hyperlink shall content.
            The type will be treated as Local by default.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.#ctor(System.String,MigraDoc.DocumentObjectModel.HyperlinkType,System.String)">
            <summary>
            Initializes a new instance of the Hyperlink class with the type and text the hyperlink shall
            represent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddText(System.String)">
            <summary>
            Adds a text phrase to the hyperlink.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddChar(System.Char,System.Int32)">
            <summary>
            Adds a single character repeated the specified number of times to the hyperlink.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddChar(System.Char)">
            <summary>
            Adds a single character to the hyperlink.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddCharacter(MigraDoc.DocumentObjectModel.SymbolName,System.Int32)">
            <summary>
            Adds one or more Symbol objects.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddCharacter(MigraDoc.DocumentObjectModel.SymbolName)">
            <summary>
            Adds a Symbol object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddCharacter(System.Char,System.Int32)">
            <summary>
            Adds one or more Symbol objects defined by a character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddCharacter(System.Char)">
            <summary>
            Adds a Symbol object defined by a character.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddSpace(System.Int32)">
            <summary>
            Adds a space character as many as count.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddTab">
            <summary>
            Adds a horizontal tab.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddFormattedText">
            <summary>
            Adds a new FormattedText.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddFormattedText(MigraDoc.DocumentObjectModel.TextFormat)">
            <summary>
            Adds a new FormattedText object with the given format.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddFormattedText(MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Adds a new FormattedText with the given Font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddFormattedText(System.String)">
            <summary>
            Adds a new FormattedText with the given text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddFormattedText(System.String,MigraDoc.DocumentObjectModel.TextFormat)">
            <summary>
            Adds a new FormattedText object with the given text and format.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddFormattedText(System.String,MigraDoc.DocumentObjectModel.Font)">
            <summary>
            Adds a new FormattedText object with the given text and font.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddFormattedText(System.String,System.String)">
            <summary>
            Adds a new FormattedText object with the given text and style.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddBookmark(System.String)">
            <summary>
            Adds a new Bookmark.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddPageField">
            <summary>
            Adds a new PageField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddPageRefField(System.String)">
            <summary>
            Adds a new PageRefField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddNumPagesField">
            <summary>
            Adds a new NumPagesField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddSectionField">
            <summary>
            Adds a new SectionField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddSectionPagesField">
            <summary>
            Adds a new SectionPagesField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddDateField">
            <summary>
            Adds a new DateField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddDateField(System.String)">
            <summary>
            Adds a new DateField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddInfoField(MigraDoc.DocumentObjectModel.Fields.InfoFieldType)">
            <summary>
            Adds a new InfoField.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddFootnote(System.String)">
            <summary>
            Adds a new Footnote with the specified text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddFootnote">
            <summary>
            Adds a new Footnote.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.AddImage(System.String)">
            <summary>
            Adds a new Image object
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Fields.BookmarkField)">
            <summary>
            Adds a new Bookmark
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Fields.PageField)">
            <summary>
            Adds a new PageField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Fields.PageRefField)">
            <summary>
            Adds a new PageRefField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Fields.NumPagesField)">
            <summary>
            Adds a new NumPagesField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Fields.SectionField)">
            <summary>
            Adds a new SectionField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Fields.SectionPagesField)">
            <summary>
            Adds a new SectionPagesField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Fields.DateField)">
            <summary>
            Adds a new DateField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Fields.InfoField)">
            <summary>
            Adds a new InfoField
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Footnote)">
            <summary>
            Adds a new Footnote
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Text)">
            <summary>
            Adds a new Text
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.FormattedText)">
            <summary>
            Adds a new FormattedText
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Shapes.Image)">
            <summary>
            Adds a new Image
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Add(MigraDoc.DocumentObjectModel.Character)">
            <summary>
            Adds a new Character
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Hyperlink.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Hyperlink into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Hyperlink.Font">
            <summary>
            Gets or sets the font object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Hyperlink.Name">
            <summary>
            Gets or sets the target name of the Hyperlink, e.g. an URL or a bookmark's name.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Hyperlink.Type">
            <summary>
            Gets or sets the target type of the Hyperlink.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Hyperlink.Elements">
            <summary>
            Gets the ParagraphElements of the Hyperlink specifying its 'clickable area'.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Hyperlink.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.HeaderFooterIndex">
            <summary>
            Index to the three HeaderFooter objects of a HeadersFooters collection.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.HeaderFooterIndex.Primary">
            <summary>
            Header or footer which is primarily used.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.HeaderFooterIndex.FirstPage">
            <summary>
            Header or footer for the first page of the section.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.HeaderFooterIndex.EvenPage">
            <summary>
            Header or footer for the even pages of the section.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Character">
            <summary>
            Represents a special character in paragraph text.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Character.#ctor">
            <summary>
            Initializes a new instance of the Character class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Character.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Character class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Character.#ctor(MigraDoc.DocumentObjectModel.SymbolName)">
            <summary>
            Initializes a new instance of the Character class with the specified SymbolName.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Character.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Character into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Character.SymbolName">
            <summary>
            Gets or sets the SymbolName. Returns 0 if the type is defined by a character.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Character.Char">
            <summary>
            Gets or sets the SymbolName as character. Returns 0 if the type is defined via an enum.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Character.Count">
            <summary>
            Gets or sets the number of times the character is repeated.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Character.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.RowHeightRule">
            <summary>
            Specifies the calculation rule of the row height.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.Series">
            <summary>
            Represents a series of data on the chart.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.#ctor">
            <summary>
            Initializes a new instance of the Series class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.AddBlank">
            <summary>
            Adds a blank to the series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.Add(System.Double)">
            <summary>
            Adds a real value to the series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.Add(System.Double[])">
            <summary>
            Adds an array of real values to the series.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Series into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.Elements">
            <summary>
            The actual value container of the series.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.Name">
            <summary>
            Gets or sets the name of the series which will be used in the legend.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.LineFormat">
            <summary>
            Gets the line format of the border of each data.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.FillFormat">
            <summary>
            Gets the background filling of the data.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.MarkerSize">
            <summary>
            Gets or sets the size of the marker in a line chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.MarkerStyle">
            <summary>
            Gets or sets the style of the marker in a line chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.MarkerForegroundColor">
            <summary>
            Gets or sets the foreground color of the marker in a line chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.MarkerBackgroundColor">
            <summary>
            Gets or sets the background color of the marker in a line chart.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.ChartType">
            <summary>
            Gets or sets the chart type of the series if it's intended to be different than the global chart type.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.DataLabel">
            <summary>
            Gets the DataLabel of the series.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.HasDataLabel">
            <summary>
            Gets or sets whether the series has a DataLabel.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.Count">
            <summary>
            Gets the elementcount of the series.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Series.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.Gridlines">
            <summary>
            Represents the gridlines on the axes.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Gridlines.#ctor">
            <summary>
            Initializes a new instance of the Gridlines class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Gridlines.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Gridlines class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Gridlines.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Gridlines.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.Gridlines.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Gridlines into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Gridlines.LineFormat">
            <summary>
            Gets the line format of the grid.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.Gridlines.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.KeyWords.SymbolFromName(System.String)">
            <summary>
            Returns Symbol value from name, or Symbol.None if no such Symbol exists.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.IO.KeyWords.NameFromSymbol(MigraDoc.DocumentObjectModel.IO.Symbol)">
            <summary>
            Returns string from Symbol value.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.IO.Symbol">
            <summary>
            The symbols used by DdlScanner/DdlParser.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Fields.InfoFieldType">
            <summary>
            Specifies the information to be shown in the field.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Fields.InfoFieldType.Title">
            <summary>
            Specifies the title for the document.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Fields.InfoFieldType.Author">
            <summary>
            Specifies the author for the document.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Fields.InfoFieldType.Keywords">
            <summary>
            Specifies the keywords for the document.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Fields.InfoFieldType.Subject">
            <summary>
            Specifies the subject for the document.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Sections">
            <summary>
            Represents the collection of document sections.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Sections.#ctor">
            <summary>
            Initializes a new instance of the Sections class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Sections.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Sections class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Sections.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Sections.AddSection">
            <summary>
            Adds a new section.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Sections.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Sections into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Sections.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Sections.Item(System.Int32)">
            <summary>
            Gets a section by its index. First section has index 0.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Sections.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.ListInfo">
            <summary>
            A ListInfo is the representation of a series of paragraphs as a list.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ListInfo.#ctor">
            <summary>
            Initializes a new instance of the ListInfo class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ListInfo.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the ListInfo class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ListInfo.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.ListInfo.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts ListInfo into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ListInfo.ListType">
            <summary>
            Gets or sets the type of the list.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ListInfo.NumberPosition">
            <summary>
            Gets or sets the left indent of the list symbol.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ListInfo.ContinuePreviousList">
            <summary>
            Gets or sets a value indicating whether
            the previous list numbering should be continued.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.ListInfo.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.BorderStyle">
            <summary>
            Specifies the style of the line of the Border object.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BorderStyle.None">
            <summary>
            No border.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BorderStyle.Single">
            <summary>
            A single solid line.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BorderStyle.Dot">
            <summary>
            A dotted line.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BorderStyle.DashSmallGap">
            <summary>
            A dashed line (small gaps).
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BorderStyle.DashLargeGap">
            <summary>
            A dashed line (large gaps).
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BorderStyle.DashDot">
            <summary>
            Alternating dashes and dots.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.BorderStyle.DashDotDot">
            <summary>
            A dash followed by two dots.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.Edge">
            <summary>
            Combinable flags to set Borders using the SetEdge function.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Tables.Columns">
            <summary>
            Represents the columns of a table.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Columns.#ctor">
            <summary>
            Initializes a new instance of the Columns class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Columns.#ctor(MigraDoc.DocumentObjectModel.Unit[])">
            <summary>
            Initializes a new instance of the Columns class containing columns of the specified widths.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Columns.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the Columns class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Columns.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Columns.AddColumn">
            <summary>
            Adds a new column to the columns collection. Allowed only before any row was added.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Columns.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts Columns into DDL.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Tables.Columns.MigraDoc#DocumentObjectModel#Visitors#IVisitable#AcceptVisitor(MigraDoc.DocumentObjectModel.Visitors.DocumentObjectVisitor,System.Boolean)">
            <summary>
            Allows the visitor object to visit the document object and it's child objects.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Columns.Table">
            <summary>
            Gets the table the columns collection belongs to.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Columns.Item(System.Int32)">
            <summary>
            Gets a column by its index.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Columns.Width">
            <summary>
            Gets or sets the default width of all columns.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Columns.Comment">
            <summary>
            Gets or sets a comment associated with this object.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Tables.Columns.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabelType">
            <summary>
            Determines the type of the data label.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabelType.None">
            <summary>
            No DataLabel.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabelType.Percent">
            <summary>
            Percentage of the data. For pie charts only.
            </summary>
        </member>
        <member name="F:MigraDoc.DocumentObjectModel.Shapes.Charts.DataLabelType.Value">
            <summary>
            Value of the data.
            </summary>
        </member>
        <member name="T:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle">
            <summary>
            Represents the title of an axis.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.#ctor">
            <summary>
            Initializes a new instance of the AxisTitle class.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.#ctor(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Initializes a new instance of the AxisTitle class with the specified parent.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.Clone">
            <summary>
            Creates a deep copy of this object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.DeepCopy">
            <summary>
            Implements the deep copy of the object.
            </summary>
        </member>
        <member name="M:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.Serialize(MigraDoc.DocumentObjectModel.Serializer)">
            <summary>
            Converts AxisTitle into DDL.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.Style">
            <summary>
            Gets or sets the style name of the axis.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.Caption">
            <summary>
            Gets or sets the caption of the title.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.Font">
            <summary>
            Gets the font object of the title.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.Orientation">
            <summary>
            Gets or sets the orientation of the caption.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.Alignment">
            <summary>
            Gets or sets the alignment of the caption.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.VerticalAlignment">
            <summary>
            Gets or sets the alignment of the caption.
            </summary>
        </member>
        <member name="P:MigraDoc.DocumentObjectModel.Shapes.Charts.AxisTitle.Meta">
            <summary>
            Returns the meta object of this instance.
            </summary>
        </member>
    </members>
</doc>
