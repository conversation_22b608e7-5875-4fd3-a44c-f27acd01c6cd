<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MigraDoc.Rendering</name>
    </assembly>
    <members>
        <member name="T:MigraDoc.Rendering.PageBreakFormatInfo">
            <summary>
            Formatting information for a page break.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.FormatInfo">
            <summary>
            Abstract base class for formatting information received by calling Format() on a renderer.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.FormatInfo.IsStarting">
            <summary>
            Indicates that the formatted object is starting.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.FormatInfo.IsEnding">
            <summary>
            Indicates that the formatted object is ending.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.FormatInfo.IsComplete">
            <summary>
            Indicates that the formatted object is complete.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.FormatInfo.StartingIsComplete">
            <summary>
            Indicates that the starting of the element is completed
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.FormatInfo.EndingIsComplete">
            <summary>
            Indicates that the ending of the element is completed
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.FormattedTextFrame">
            <summary>
            Represents a formatted text frame.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.IAreaProvider">
            <summary>
            Represents a class that provides a series of Areas to render into.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.IAreaProvider.GetNextArea">
            <summary>
            Gets the next area to render into.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.IAreaProvider.ProbeNextArea">
            <summary>
            Probes the next area to render into like GetNextArea, but doesn't change the provider state. 
            </summary>
            <returns>The area for the next rendering act.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.IAreaProvider.IsAreaBreakBefore(MigraDoc.Rendering.LayoutInfo)">
            <summary>
            Determines whether the element requires an area break before.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.IAreaProvider.PositionVertically(MigraDoc.Rendering.LayoutInfo)">
            <summary>
            Positions the element vertically relatively to the current area.
            </summary>
            <param name="layoutInfo">The layout info of the element.</param>
            <returns>True, if the element was moved by the function.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.IAreaProvider.PositionHorizontally(MigraDoc.Rendering.LayoutInfo)">
            <summary>
            Positions the element horizontally relatively to the current area.
            </summary>
            <param name="layoutInfo">The layout info of the element.</param>
            <returns>True, if the element was moved by the function.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.IAreaProvider.StoreRenderInfos(System.Collections.ArrayList)">
            <summary>
            Stores the RenderInfos of elements on the current area.
            </summary>
            <param name="renderInfos"></param>
        </member>
        <member name="T:MigraDoc.Rendering.FormattedHeaderFooter">
            <summary>
            Represents a formatted header or footer.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ImageRenderInfo">
            <summary>
            Represents rendering information for images.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ShapeRenderInfo">
            <summary>
            Rendering information for shapes.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.RenderInfo">
            <summary>
            Abstract base class for all classes that store rendering information.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.PageInfo">
            <summary>
            Provides information necessary to render the page.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PageInfo.Width">
            <summary>
            Gets the with of the described page as specified in Document.PageSetup, i.e. the orientation
            is not taken into account.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PageInfo.Height">
            <summary>
            Gets the height of the described page as specified in Document.PageSetup, i.e. the orientation
            is not taken into account.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PageInfo.Orientation">
            <summary>
            Gets the orientation of the described page as specified in Document.PageSetup.
            The value has no influence on the properties Width or Height, i.e. if the result is PageOrientation.Landscape
            you must exchange the values of Width or Height to get the real page size.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.FormattedDocument">
            <summary>
            Represents a formatted document.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.FormattedDocument.Format(PdfSharp.Drawing.XGraphics)">
            <summary>
            Formats the document by performing line breaks and page breaks.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.FormattedDocument.FillNumPagesInfo">
            <summary>
            Fills the number pages information after formatting the document.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.FormattedDocument.FillSectionPagesInfo">
            <summary>
            Fills the section pages information after formatting a section.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.FormattedDocument.GetRenderInfos(System.Int32)">
            <summary>
            Gets the rendering information for the page content.
            </summary>
            <param name="page">The page to render.</param>
            <returns>Rendering information for the page content.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.FormattedDocument.GetFormattedHeader(System.Int32)">
            <summary>
            Gets a formatted headerfooter object for header of the given page.
            </summary>
            <param name="page">The page the header shall appear on.</param>
            <returns>The required header, null if none exists to render.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.FormattedDocument.GetFormattedFooter(System.Int32)">
            <summary>
            Gets a formatted headerfooter object for footer of the given page.
            </summary>
            <param name="page">The page the footer shall appear on.</param>
            <returns>The required footer, null if none exists to render.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.FormattedDocument.GetPageInfo(System.Int32)">
            <summary>
            Gets information about the specified page.
            </summary>
            <param name="page">The page the information is asked for.</param>
            <returns>The page information.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.FormattedDocument.GetCurrentAlignment(MigraDoc.Rendering.ElementAlignment)">
            <summary>
            Gets the alignment depending on the currentPage for the alignments "Outside" and "Inside".
            </summary>
            <param name="alignment">The original alignment</param>
            <returns>the alignment depending on the currentPage for the alignments "Outside" and "Inside"</returns>
        </member>
        <member name="P:MigraDoc.Rendering.FormattedDocument.PageCount">
            <summary>
            Gets the number of pages of the document.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.BordersRenderer">
            <summary>
            Renders a single Border.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.BordersRenderer.RenderVertically(MigraDoc.DocumentObjectModel.BorderType,PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit)">
            <summary>
            Renders the border top down.
            </summary>
            <param name="type">The type of the border.</param>
            <param name="left">The left position of the border.</param>
            <param name="top">The top position of the border.</param>
            <param name="height">The height on which to render the border.</param>
        </member>
        <member name="M:MigraDoc.Rendering.BordersRenderer.RenderHorizontally(MigraDoc.DocumentObjectModel.BorderType,PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit)">
            <summary>
            Renders the border top down.
            </summary>
            <param name="type">The type of the border.</param>
            <param name="left">The left position of the border.</param>
            <param name="top">The top position of the border.</param>
            <param name="width">The width on which to render the border.</param>
        </member>
        <member name="T:MigraDoc.Rendering.ChartMapper.XValuesMapper">
            <summary>
            The XValuesMapper class.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ChartMapper.XValuesMapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.ChartMapper.XValuesMapper"/> class.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.TopDownFormatter">
            <summary>
            Formats a series of document elements from top to bottom.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.TopDownFormatter.MarginMax(PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit)">
            <summary>
            Returns the max of the given Margins, if both are positive or 0, the sum otherwise.
            </summary>
            <param name="prevBottomMargin">The bottom margin of the previous element.</param>
            <param name="nextTopMargin">The top margin of the next element.</param>
            <returns></returns>
        </member>
        <member name="M:MigraDoc.Rendering.TopDownFormatter.FormatOnAreas(PdfSharp.Drawing.XGraphics,System.Boolean)">
            <summary>
            Formats the elements on the areas provided by the area provider.
            </summary>
            <param name="gfx">The graphics object to render on.</param>
            <param name="topLevel">if set to <c>true</c> formats the object is on top level.</param>
        </member>
        <member name="M:MigraDoc.Rendering.TopDownFormatter.FinishPage(MigraDoc.Rendering.RenderInfo,System.Boolean,System.Collections.ArrayList@)">
            <summary>
            Finishes rendering for the page.
            </summary>
            <param name="lastRenderInfo">The last render info.</param>
            <param name="pagebreakBefore">set to <c>true</c> if there is a pagebreak before this page.</param>
            <param name="renderInfos">The render infos.</param>
            <returns>
            The RenderInfo to set as previous RenderInfo.
            </returns>
        </member>
        <member name="M:MigraDoc.Rendering.TopDownFormatter.IsForcedAreaBreak(System.Int32,MigraDoc.Rendering.Renderer,MigraDoc.Rendering.Area)">
            <summary>
            Indicates that a break between areas has to be performed before the element with the given idx.
            </summary>
            <param name="idx">Index of the document element.</param>
            <param name="renderer">A formatted renderer for the document element.</param>
            <param name="remainingArea">The remaining area.</param>
        </member>
        <member name="M:MigraDoc.Rendering.TopDownFormatter.PreviousRendererNeedsRemoveEnding(MigraDoc.Rendering.RenderInfo,MigraDoc.Rendering.RenderInfo,MigraDoc.Rendering.Area)">
            <summary>
            Indicates that the Ending of the element has to be removed.
            </summary>
            <param name="prevRenderInfo">The prev render info.</param>
            <param name="succedingRenderInfo">The succeding render info.</param>
            <param name="remainingArea">The remaining area.</param>
        </member>
        <member name="F:MigraDoc.Rendering.TopDownFormatter.MaxCombineElements">
            <summary>
            The maximum number of elements that can be combined via keepwithnext and keeptogether
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ChartMapper.ChartMapper">
            <summary>
            Maps charts from the MigraDoc.DocumentObjectModel to charts from Pdf.Charting.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ChartMapper.ChartMapper.#ctor">
            <summary>
            Initializes a new instance of the chart mapper object.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ChartMapper.ChartMapper.Map(MigraDoc.DocumentObjectModel.Shapes.Charts.Chart)">
            <summary>
            Maps the specified DOM chart.
            </summary>
            <param name="domChart">The DOM chart.</param>
            <returns></returns>
        </member>
        <member name="T:MigraDoc.Rendering.Renderer">
            <summary>
            Abstract base class for all renderers.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Renderer.RenderByInfos(PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit,MigraDoc.Rendering.RenderInfo[])">
            <summary>
            Renders the contents shifted to the given Coordinates.
            </summary>
            <param name="xShift">The x shift.</param>
            <param name="yShift">The y shift.</param>
            <param name="renderInfos">The render infos.</param>
        </member>
        <member name="M:MigraDoc.Rendering.Renderer.Render">
            <summary>
            Renders (draws) the object to the Graphics object.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Renderer.Format(MigraDoc.Rendering.Area,MigraDoc.Rendering.FormatInfo)">
            <summary>
            Formats the object by calculating distances and linebreaks and stopping when the area is filled.
            </summary>
            <param name="area">The area to render into.</param>
            <param name="previousFormatInfo">An information object received from a previous call of Format().
            Null for the first call.</param>
        </member>
        <member name="M:MigraDoc.Rendering.Renderer.Create(PdfSharp.Drawing.XGraphics,MigraDoc.Rendering.DocumentRenderer,MigraDoc.DocumentObjectModel.DocumentObject,MigraDoc.Rendering.FieldInfos)">
            <summary>
            Creates a fitting renderer for the given document object for formatting.
            </summary>
            <param name="gfx">The XGraphics object to do measurements on.</param>
            <param name="documentRenderer">The document renderer.</param>
            <param name="documentObject">the document object to format.</param>
            <param name="fieldInfos">The field infos.</param>
            <returns>The fitting Renderer.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.Renderer.Create(PdfSharp.Drawing.XGraphics,MigraDoc.Rendering.DocumentRenderer,MigraDoc.Rendering.RenderInfo,MigraDoc.Rendering.FieldInfos)">
            <summary>
            Creates a fitting renderer for the render info to render and layout with.
            </summary>
            <param name="gfx">The XGraphics object to render on.</param>
            <param name="documentRenderer">The document renderer.</param>
            <param name="renderInfo">The RenderInfo object stored after a previous call of Format().</param>
            <param name="fieldInfos">The field infos.</param>
            <returns>The fitting Renderer.</returns>
        </member>
        <member name="P:MigraDoc.Rendering.Renderer.MaxElementHeight">
            <summary>
            Determines the maximum height a single element may have.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Renderer.InitialLayoutInfo">
            <summary>
            In inherited classes, gets a layout info with only margin and break information set.
            It can be taken before the documen object is formatted.
            </summary>
            <remarks>
            In inherited classes, the following parts are set properly:
            MarginTop, MarginLeft, MarginRight, MarginBottom, 
            KeepTogether, KeepWithNext, PagebreakBefore, Floating,
            VerticalReference, HorizontalReference.
            </remarks>
        </member>
        <member name="P:MigraDoc.Rendering.Renderer.RenderInfo">
            <summary>
            Gets the render information necessary to render and position the object.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Renderer.FieldInfos">
            <summary>
            Sets the field infos object.
            </summary>
            <remarks>This property is set by the AreaProvider.</remarks>
        </member>
        <member name="T:MigraDoc.Rendering.ShapeRenderer">
            <summary>
            Renders a shape to an XGraphics object.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ShapeRenderer.Format(MigraDoc.Rendering.Area,MigraDoc.Rendering.FormatInfo)">
            <summary>
            Formats the shape.
            </summary>
            <param name="area">The area to fit in the shape.</param>
            <param name="previousFormatInfo"></param>
        </member>
        <member name="P:MigraDoc.Rendering.ShapeRenderer.ShapeWidth">
            <summary>
            Gets the shape width including line width.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.ShapeRenderer.ShapeHeight">
            <summary>
            Gets the shape height including line width.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ParagraphRenderer">
            <summary>
            Summary description for ParagraphRenderer.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.#ctor(PdfSharp.Drawing.XGraphics,MigraDoc.DocumentObjectModel.Paragraph,MigraDoc.Rendering.FieldInfos)">
            <summary>
            Initializes a ParagraphRenderer object for formatting.
            </summary>
            <param name="gfx">The XGraphics object to do measurements on.</param>
            <param name="paragraph">The paragraph to format.</param>
            <param name="fieldInfos">The field infos.</param>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.#ctor(PdfSharp.Drawing.XGraphics,MigraDoc.Rendering.RenderInfo,MigraDoc.Rendering.FieldInfos)">
            <summary>
            Initializes a ParagraphRenderer object for rendering.
            </summary>
            <param name="gfx">The XGraphics object to render on.</param>
            <param name="renderInfo">The render info object containing information necessary for rendering.</param>
            <param name="fieldInfos">The field infos.</param>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.Render">
            <summary>
            Renders the paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.FormatTab">
            <summary>
            Adjusts the current x position to the given tab stop if possible.
            </summary>
            <returns>True, if the text doesn't fit the line any more and the tab causes a line break.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.ProbeAfterLeftAlignedTab(PdfSharp.Drawing.XUnit,System.Boolean@)">
            <summary>
            Probes the paragraph elements after a left aligned tab stop and returns the vertical text position to start at.
            </summary>
            <param name="tabStopPosition">Position of the tab to probe.</param>
            <param name="notFitting">Out parameter determining whether the tab causes a line break.</param>
            <returns>The new x-position to restart behind the tab.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.ProbeAfterRightAlignedTab(PdfSharp.Drawing.XUnit,System.Boolean@)">
            <summary>
            Probes the paragraph elements after a right aligned tab stop and returns the vertical text position to start at.
            </summary>
            <param name="tabStopPosition">Position of the tab to probe.</param>
            <param name="notFitting">Out parameter determining whether the tab causes a line break.</param>
            <returns>The new x-position to restart behind the tab.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.ProbeAfterCenterAlignedTab(PdfSharp.Drawing.XUnit,System.Boolean@)">
            <summary>
            Probes the paragraph elements after a right aligned tab stop and returns the vertical text position to start at.
            </summary>
            <param name="tabStopPosition">Position of the tab to probe.</param>
            <param name="notFitting">Out parameter determining whether the tab causes a line break.</param>
            <returns>The new x-position to restart behind the tab.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.ProbeAfterDecimalAlignedTab(PdfSharp.Drawing.XUnit,System.Boolean@)">
            <summary>
            Probes the paragraph elements after a right aligned tab stop and returns the vertical text position to start at.
            </summary>
            <param name="tabStopPosition">Position of the tab to probe.</param>
            <param name="notFitting">Out parameter determining whether the tab causes a line break.</param>
            <returns>The new x-position to restart behind the tab.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.ProbeAfterTab">
            <summary>
            Probes the paragraph after a tab.
            Caution: This Function resets the word count and line width before doing its work.
            </summary>
            <returns>True if the tab causes a linebreak.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.GetNextTabStop">
            <summary>
            Gets the next tab stop following the current x position.
            </summary>
            <returns>The searched tab stop.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.RenderLine(MigraDoc.Rendering.LineInfo)">
            <summary>
            Renders a single line.
            </summary>
            <param name="lineInfo"></param>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.InitFormat(MigraDoc.Rendering.Area,MigraDoc.Rendering.FormatInfo)">
            <summary>
            Initializes this instance for formatting.
            </summary>
            <param name="area">The area for formatting</param>
            <param name="previousFormatInfo">A previous format info.</param>
            <returns>False, if nothing of the paragraph will fit the area any more.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.GetListSymbol(System.String@,PdfSharp.Drawing.XFont@)">
            <summary>
            Gets information necessary to render or measure the list symbol.
            </summary>
            <param name="symbol">The text to list symbol to render or measure</param>
            <param name="font">The font to use for rendering or measuring.</param>
            <returns>True, if a symbol needs to be rendered.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.Format(MigraDoc.Rendering.Area,MigraDoc.Rendering.FormatInfo)">
            <summary>
            Formats the paragraph by performing line breaks etc.
            </summary>
            <param name="area">The area in which to render.</param>
            <param name="previousFormatInfo">The format info that was obtained on formatting the same paragraph on a previous area.</param>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.FinishLayoutInfo">
            <summary>
            Finishes the layout info by calculating starting and trailing heights.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.FormatElement(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Processes the elements when formatting.
            </summary>
            <param name="docObj"></param>
            <returns></returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.FormatWord(System.String)">
            <summary>
            Helper function for formatting word-like elements like text and fields.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.FormatCharacter(MigraDoc.DocumentObjectModel.Character)">
            <summary>
            Processes (measures) a special character within text.
            </summary>
            <param name="character">The character to process.</param>
            <returns>True if the character should start at a new line.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.FormatBlank">
            <summary>
            Processes (measures) a blank.
            </summary>
            <returns>True if the blank causes a line break.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.FormatText(MigraDoc.DocumentObjectModel.Text)">
            <summary>
            Processes a text element during formatting.
            </summary>
            <param name="text">The text element to measure.</param>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.StartNewLine">
            <summary>
            Starts a new line by resetting measuring values.
            Do not call before the first first line is formatted!
            </summary>
            <returns>True, if the new line may fit the formatting area.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.StoreLineInformation">
            <summary>
            Stores all line information.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphRenderer.GetLineHeight(MigraDoc.DocumentObjectModel.ParagraphFormat,PdfSharp.Drawing.XGraphics,MigraDoc.Rendering.DocumentRenderer)">
            <summary>
            Help function to receive a line height on empty paragraphs.
            </summary>
            <param name="format">The format.</param>
            <param name="gfx">The GFX.</param>
            <param name="renderer">The renderer.</param>
        </member>
        <member name="F:MigraDoc.Rendering.ParagraphRenderer.paragraph">
            <summary>
            The paragraph to format or render.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphRenderer.InitialLayoutInfo">
            <summary>
            Gets a layout info with only margin and break information set.
            It can be taken before the paragraph is formatted.
            </summary>
            <remarks>
            The following layout information is set properly:<br />
            MarginTop, MarginLeft, MarginRight, MarginBottom, KeepTogether, KeepWithNext, PagebreakBefore.
            </remarks>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphRenderer.StartXPosition">
            <summary>
            Gets the horizontal position to start a new line.
            </summary>
            <returns>The position to start the line.</returns>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphRenderer.IgnoreHorizontalGrowth">
            <summary>
            When rendering a justified paragraph, only the part after the last tab stop needs remeasuring.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphRenderer.TopBorderOffset">
            <summary>
            Gets the top border offset for the first line, else 0.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphRenderer.BottomBorderOffset">
            <summary>
            Gets the bottom border offset for the last line, else 0.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphRenderer.CurrentFont">
            <summary>
            The font used for the current paragraph element.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ParagraphRenderer.Phase">
            <summary>
            Process phases of the renderer.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ParagraphRenderer.FormatResult">
            <summary>
            Results that can occur when processing a paragraph element
            during formatting.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.ParagraphRenderer.FormatResult.Ignore">
            <summary>
            Ignore the current element during formatting.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.ParagraphRenderer.FormatResult.Continue">
            <summary>
            Continue with the next element within the same line.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.ParagraphRenderer.FormatResult.NewLine">
            <summary>
            Start a new line from the current object on.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.ParagraphRenderer.FormatResult.NewArea">
            <summary>
            Break formatting and continue in a new area (e.g. a new page).
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.PageBreakRenderInfo">
            <summary>
            Rendering information for page breaks.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.FieldInfos">
            <summary>
            Field information used to fill fields when rendering or formatting.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ChartMapper.LineFormatMapper">
            <summary>
            The LineFormatMapper class.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ChartMapper.LineFormatMapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.ChartMapper.LineFormatMapper"/> class.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.Resources.Messages">
            <summary>
            Provides diagnostic messages taken from the resources.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.TableFormatInfo">
            <summary>
            Formatting information for tables.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.FontHandler">
            <summary>
            Helps measuring and handling fonts.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.FontHandler.FontToXFont(MigraDoc.DocumentObjectModel.Font,PdfSharp.Drawing.XPrivateFontCollection,PdfSharp.Pdf.PdfFontEncoding,PdfSharp.Pdf.PdfFontEmbedding)">
            <summary>
            Converts an DOM Font to an XFont.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.Area">
            <summary>
            Abstract base class for all areas to render in.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Area.GetFittingRect(PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit)">
            <summary>
            Gets the largest fitting rect with the given y position and height.
            </summary>
            <param name="yPosition">Top bound of the searched rectangle.</param>
            <param name="height">Height of the searched rectangle.</param>
            <returns>
            The largest fitting rect with the given y position and height.
            Null if yPosition exceeds the area.
            </returns>
        </member>
        <member name="M:MigraDoc.Rendering.Area.Unite(MigraDoc.Rendering.Area)">
            <summary>
            Returns the union of this area snd the given one.
            </summary>
            <param name="area">The area to unite with.</param>
            <returns>The union of the two areas.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.Area.Lower(PdfSharp.Drawing.XUnit)">
            <summary>
            Lowers the area and makes it smaller.
            </summary>
            <param name="verticalOffset">The measure of lowering.</param>
            <returns>The lowered Area.</returns>
        </member>
        <member name="P:MigraDoc.Rendering.Area.X">
            <summary>
            Gets the left boundary of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Area.Y">
            <summary>
            Gets the top boundary of the area.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Area.Height">
            <summary>
            Gets or sets the height of the smallest rectangle containing the area. 
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Area.Width">
            <summary>
            Gets or sets the width of the smallest rectangle containing the area. 
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Rectangle.#ctor(PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit)">
            <summary>
            Initializes a new rectangle object.
            </summary>
            <param name="x">Left bound of the rectangle.</param>
            <param name="y">Upper bound of the rectangle.</param>
            <param name="width">Width of the rectangle.</param>
            <param name="height">Height of the rectangle.</param>
        </member>
        <member name="M:MigraDoc.Rendering.Rectangle.#ctor(MigraDoc.Rendering.Rectangle)">
            <summary>
            Initializes a new Rectangle by copying its values.
            </summary>
            <param name="rect">The rectangle to copy.</param>
        </member>
        <member name="M:MigraDoc.Rendering.Rectangle.GetFittingRect(PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit)">
            <summary>
            Gets the largest fitting rect with the given y position and height.
            </summary>
            <param name="yPosition">Top bound of the searched rectangle.</param>
            <param name="height">Height of the searched rectangle.</param>
            <returns>The largest fitting rect with the given y position and height</returns>
        </member>
        <member name="M:MigraDoc.Rendering.Rectangle.Unite(MigraDoc.Rendering.Area)">
            <summary>
            Returns the union of the rectangle and the given area.
            </summary>
            <param name="area">The area to unite with.</param>
            <returns>The union of the two areas.</returns>
        </member>
        <member name="P:MigraDoc.Rendering.Rectangle.X">
            <summary>
            Gets or sets the left boundary of the rectangle. 
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Rectangle.Y">
            <summary>
            Gets or sets the top boundary of the rectangle. 
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Rectangle.Width">
            <summary>
            Gets or sets the top boundary of the rectangle. 
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Rectangle.Height">
            <summary>
            Gets or sets the height of the rectangle. 
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ChartMapper.PlotAreaMapper">
            <summary>
            The PlotAreaMapper class.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ChartMapper.PlotAreaMapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.ChartMapper.PlotAreaMapper"/> class.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ImageRenderer">
            <summary>
            Renders images.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.FormattedCell">
            <summary>
            Represents a formatted cell.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.HorizontalReference">
            <summary>
            Horizontal reference point of alignment.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.Floating">
            <summary>
            Floating behavior of layout elements.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ChartFormatInfo">
            <summary>
            Formatting information for a chart.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ShapeFormatInfo">
            <summary>
            Format information for all shapes.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.ShapeFormatInfo.StartingIsComplete">
            <summary>
            Indicates that the starting of the element is completed
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.ShapeFormatInfo.EndingIsComplete">
            <summary>
            Indicates that the ending of the element is completed
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.TextFrameFormatInfo">
            <summary>
            Formatting information for textframes.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ChartRenderer">
            <summary>
            Renders a chart to an XGraphics object.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ChartRenderer.GetTopBottomWidth">
            <summary>
            Gets the width of the top and bottom area.
            Used while formatting.
            </summary>
            <returns>The width of the top and bottom area</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ChartRenderer.GetTopBottomHorizontalPosition(PdfSharp.Drawing.XUnit@,PdfSharp.Drawing.XUnit@)">
            <summary>
            Gets the horizontal boundaries of the top and bottom area.
            Used while rendering.
            </summary>
            <param name="left">The left boundary of the top and bottom area</param>
            <param name="right">The right boundary of the top and bottom area</param>
        </member>
        <member name="T:MigraDoc.Rendering.Printing.MigraDocPrintDocument">
            <summary>
            Represents a specialized System.Drawing.Printing.PrintDocument for MigraDoc documents.
            This component knows about MigraDoc and simplifies printing of MigraDoc documents.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Printing.MigraDocPrintDocument.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.Printing.MigraDocPrintDocument"/> class. 
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Printing.MigraDocPrintDocument.#ctor(MigraDoc.Rendering.DocumentRenderer)">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.Printing.MigraDocPrintDocument"/> class
            with the specified <see cref="T:MigraDoc.Rendering.DocumentRenderer"/> object.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Printing.MigraDocPrintDocument.OnBeginPrint(System.Drawing.Printing.PrintEventArgs)">
            <summary>
            Raises the <see cref="E:System.Drawing.Printing.PrintDocument.BeginPrint"/> event. It is called after the <see cref="M:System.Drawing.Printing.PrintDocument.Print"/> method is called and before the first page of the document prints.
            </summary>
            <param name="e">A <see cref="T:System.Drawing.Printing.PrintEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:MigraDoc.Rendering.Printing.MigraDocPrintDocument.OnQueryPageSettings(System.Drawing.Printing.QueryPageSettingsEventArgs)">
            <summary>
            Raises the <see cref="E:System.Drawing.Printing.PrintDocument.QueryPageSettings"/> event. It is called immediately before each <see cref="E:System.Drawing.Printing.PrintDocument.PrintPage"/> event.
            </summary>
            <param name="e">A <see cref="T:System.Drawing.Printing.QueryPageSettingsEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:MigraDoc.Rendering.Printing.MigraDocPrintDocument.OnPrintPage(System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            Raises the <see cref="E:System.Drawing.Printing.PrintDocument.PrintPage"/> event. It is called before a page prints.
            </summary>
            <param name="e">A <see cref="T:System.Drawing.Printing.PrintPageEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:MigraDoc.Rendering.Printing.MigraDocPrintDocument.OnEndPrint(System.Drawing.Printing.PrintEventArgs)">
            <summary>
            Raises the <see cref="E:System.Drawing.Printing.PrintDocument.EndPrint"/> event. It is called when the last page of the document has printed.
            </summary>
            <param name="e">A <see cref="T:System.Drawing.Printing.PrintEventArgs"/> that contains the event data.</param>
        </member>
        <member name="P:MigraDoc.Rendering.Printing.MigraDocPrintDocument.Renderer">
            <summary>
            Gets or sets the DocumentRenderer that prints the pages of the document.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Printing.MigraDocPrintDocument.SelectedPage">
            <summary>
            Gets or sets the page number that identifies the selected page. It it used on printing when 
            PrintRange.Selection is set.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.TextFrameRenderer">
            <summary>
            Renders textframes.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ChartMapper.AxisMapper">
            <summary>
            The AxisMapper class.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ChartMapper.AxisMapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.ChartMapper.AxisMapper"/> class.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.VerticalLineInfo">
            <summary>
            Vertical measurements of a paragraph line.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.LineInfo">
            <summary>
            Line info object used by the paragraph format info.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ParagraphFormatInfo">
            <summary>
            Formatting information for a paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphFormatInfo.Append(MigraDoc.Rendering.FormatInfo)">
            <summary>
            
            </summary>
            <param name="mergeInfo"></param>
            <returns></returns>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphFormatInfo.IsEnding">
            <summary>
            Indicates whether the paragraph is ending.
            </summary>
            <returns>True if the paragraph is ending.</returns>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphFormatInfo.IsStarting">
            <summary>
            Indicates whether the paragraph is starting.
            </summary>
            <returns>True if the paragraph is starting.</returns>
        </member>
        <member name="T:MigraDoc.Rendering.FillFormatRenderer">
            <summary>
            Renders fill formats.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.TableRenderInfo">
            <summary>
            Rendering information for tables.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.LineFormatRenderer">
            <summary>
            Renders a line format to an XGraphics object.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.FormattedTextArea">
            <summary>
            Represents a formatted text area.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ChartRenderInfo">
            <summary>
            Rendering information for charts.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.Forms.PagePreviewEventHandler">
            <summary>
            Event handler for the PagePreview event.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.Forms.DocumentPreview">
            <summary>
            Represents a Windows control to display a MigraDoc document.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.Forms.DocumentPreview"/> class.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.FirstPage">
            <summary>
            Goes to the first page.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.NextPage">
            <summary>
            Goes to the next page.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.PrevPage">
            <summary>
            Goes to the previous page.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.LastPage">
            <summary>
            Goes to the last page.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.DdlUpdated">
            <summary>
            Called when the Ddl property has changed.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.MakeSmaller">
            <summary>
            Makes zoom factor smaller.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.MakeLarger">
            <summary>
            Makes zoom factor larger.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.OnZoomChanged(System.EventArgs)">
            <summary>
            Raises the ZoomChanged event when the zoom factor changed.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.OnPageChanged(System.EventArgs)">
            <summary>
            Raises the ZoomChanged event when the current page changed.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.Forms.DocumentPreview.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.MouseWheel"/> event.
            </summary>
            <param name="e">A <see cref="T:System.Windows.Forms.MouseEventArgs"/> that contains the event data.</param>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.BorderStyle">
            <summary>
            Gets or sets the border style of the tree view control.
            </summary>
            <value></value>
            <returns>
            One of the <see cref="T:System.Windows.Forms.BorderStyle"/> values. The default is <see cref="F:System.Windows.Forms.BorderStyle.Fixed3D"/>.
            </returns>
            <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
            The assigned value is not one of the <see cref="T:System.Windows.Forms.BorderStyle"/> values.
            </exception>
            <PermissionSet>
            	<IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true"/>
            </PermissionSet>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.PrivateFonts">
            <summary>
            Gets or sets the private fonts of the document. If used, must be set before Ddl is set!
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.Ddl">
            <summary>
            Gets or sets a DDL string or file.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.Page">
            <summary>
            Gets or sets the current page.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.PageCount">
            <summary>
            Gets the number of pages.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.Document">
            <summary>
            Gets or sets the MigraDoc document that is previewed in this control.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.Renderer">
            <summary>
            Gets the underlying DocumentRenderer of the document currently in preview, or null, if no rederer exists.
            You can use this renderer for printing or creating PDF file. This evade the necessity to format the
            document a secound time when you want to print it or convert it into PDF.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.Zoom">
            <summary>
            Gets or sets a predefined zoom factor.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.ZoomPercent">
            <summary>
            Gets or sets an arbitrary zoom factor. The range is from 10 to 800.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.PageColor">
            <summary>
            Gets or sets the color of the page.
            </summary>
            <value>The color of the page.</value>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.DesktopColor">
            <summary>
            Gets or sets the color of the desktop.
            </summary>
            <value>The color of the desktop.</value>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.ShowScrollbars">
            <summary>
            Gets or sets a value indicating whether to show scrollbars.
            </summary>
            <value><c>true</c> if [show scrollbars]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.ShowPage">
            <summary>
            Gets or sets a value indicating whether to show the page.
            </summary>
            <value><c>true</c> if [show page]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:MigraDoc.Rendering.Forms.DocumentPreview.PageSize">
            <summary>
            Gets or sets the page size in point.
            </summary>
        </member>
        <member name="E:MigraDoc.Rendering.Forms.DocumentPreview.ZoomChanged">
            <summary>
            Occurs when the zoom factor changed.
            </summary>
        </member>
        <member name="E:MigraDoc.Rendering.Forms.DocumentPreview.PageChanged">
            <summary>
            Occurs when the current page changed.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ColorHelper.ToXColor(MigraDoc.DocumentObjectModel.Color,System.Boolean)">
            <summary>
            Converts Color to XColor.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.TextFrameRenderInfo">
            <summary>
            Summary description for TextFrameRenderInfo.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ParagraphIterator">
            <summary>
            Iterates sequentially through the elements of a paragraph.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphIterator.#ctor(MigraDoc.DocumentObjectModel.ParagraphElements)">
            <summary>
            Initializes a paragraph iterator pointing on the given paragraph elements object.
            Paragraph iterators received from this paragraph iterator relate to this root node.
            </summary>
            <param name="rootNode">The root node for the paragraph iterator.</param>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphIterator.#ctor(MigraDoc.DocumentObjectModel.ParagraphElements,MigraDoc.DocumentObjectModel.DocumentObject,System.Collections.ArrayList)">
            <summary>
            Initializes a paragraph iterator given the root node, its position in the object tree and the current object
            </summary>
            <param name="rootNode">The node the position indices relate to.</param>
            <param name="current">The element the iterator shall point to.</param>
            <param name="indices">The position of the paragraph iterator in terms of element indices.</param>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphIterator.GetLastLeaf">
            <summary>
            Gets the last leaf of the document object tree.
            </summary>
            <returns>The paragraph iterator pointing to the last leaf in the document object tree.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphIterator.GetFirstLeaf">
            <summary>
            Gets the first leaf of the element tree.
            </summary>
            <returns>The paragraph iterator pointing to the first leaf in the element tree.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphIterator.GetNextLeaf">
            <summary>
            Returns the next iterator in the tree pointing to a leaf.
            </summary>
            <remarks>This function is intended to receive the renderable objects of a paragraph.
            Thus, empty ParagraphElement objects (which are collections) don't count as leafs.</remarks>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphIterator.GetNodeObject(MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Gets the object a paragraph iterator shall point to.
            Only ParagraphElements and renderable objects are allowed.
            </summary>
            <param name="obj">The object to select the node object for.</param>
            <returns>The object a paragraph iterator shall point to.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphIterator.GetPreviousLeaf">
            <summary>
            Returns the previous iterator to a leaf in the document object tree pointing.
            </summary>
            <returns>The previous leaf, null if none exists.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.ParagraphIterator.SeekFirstLeaf">
            <summary>
            Gets the leftmost leaf within the hierarchy.
            </summary>
            <returns>The searched leaf.</returns>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphIterator.IsFirstLeaf">
            <summary>
            Determines whether this iterator is the first leaf of the root node.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphIterator.IsLastLeaf">
            <summary>
            Determines whether this iterator is the last leaf of the document object tree.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.ParagraphIterator.Current">
            <summary>
            Gets the document object this instance ponits to.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ImageFormatInfo">
            <summary>
            Formatting information for an image.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ElementAlignment">
            <summary>
            Alignment of layout elements.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.DocumentRenderer">
            <summary>
            Provides methods to render the document or single parts of it to a XGraphics object.
            </summary>
            <remarks>
            One prepared instance of this class can serve to render several output formats.
            </remarks>
        </member>
        <member name="M:MigraDoc.Rendering.DocumentRenderer.#ctor(MigraDoc.DocumentObjectModel.Document)">
            <summary>
            Initializes a new instance of the DocumentRenderer class.
            </summary>
            <param name="document">The migradoc document to render.</param>
        </member>
        <member name="M:MigraDoc.Rendering.DocumentRenderer.PrepareDocument">
            <summary>
            Prepares this instance for rendering.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.DocumentRenderer.OnPrepareDocumentProgress(System.Int32,System.Int32)">
            <summary>
            Allows applications to display a progress indicator while PrepareDocument() is being executed.
            </summary>
            <param name="value"></param>
            <param name="maximum"></param>
        </member>
        <member name="M:MigraDoc.Rendering.DocumentRenderer.RenderPage(PdfSharp.Drawing.XGraphics,System.Int32)">
            <summary>
            Renders a MigraDoc document to the specified graphics object.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.DocumentRenderer.RenderPage(PdfSharp.Drawing.XGraphics,System.Int32,MigraDoc.Rendering.PageRenderOptions)">
            <summary>
            Renders a MigraDoc document to the specified graphics object.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.DocumentRenderer.GetDocumentObjectsFromPage(System.Int32)">
            <summary>
            Gets the document objects that get rendered on the specified page.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.DocumentRenderer.RenderObject(PdfSharp.Drawing.XGraphics,PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit,PdfSharp.Drawing.XUnit,MigraDoc.DocumentObjectModel.DocumentObject)">
            <summary>
            Renders a single object to the specified graphics object at the given point.
            </summary>
            <param name="graphics">The graphics object to render on.</param>
            <param name="xPosition">The left position of the rendered object.</param>
            <param name="yPosition">The top position of the rendered object.</param>
            <param name="width">The width.</param>
            <param name="documentObject">The document object to render. Can be paragraph, table, or shape.</param>
            <remarks>This function is still in an experimental state.</remarks>
        </member>
        <member name="E:MigraDoc.Rendering.DocumentRenderer.PrepareDocumentProgress">
            <summary>
            Occurs while the document is being prepared (can be used to show a progress bar).
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.DocumentRenderer.HasPrepareDocumentProgress">
            <summary>
            Gets a value indicating whether this instance supports PrepareDocumentProgress.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.DocumentRenderer.FormattedDocument">
            <summary>
            Gets the formatted document of this instance.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.DocumentRenderer.WorkingDirectory">
            <summary>
            Gets or sets the working directory for rendering.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.DocumentRenderer.PrivateFonts">
            <summary>
            Gets or sets the private fonts of the document.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.DocumentRenderer.PrepareDocumentProgressEventArgs">
            <summary>
            Arguments for the PrepareDocumentProgressEvent which is called while a document is being prepared (you can use this to display a progress bar).
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.DocumentRenderer.PrepareDocumentProgressEventArgs.Value">
            <summary>
            Indicates the current step reached in document preparation.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.DocumentRenderer.PrepareDocumentProgressEventArgs.Maximum">
            <summary>
            Indicates the final step in document preparation. The quitient of Value and Maximum can be used to calculate a percentage (e. g. for use in a progress bar).
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.DocumentRenderer.PrepareDocumentProgressEventArgs.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.DocumentRenderer.PrepareDocumentProgressEventArgs"/> class.
            </summary>
            <param name="value">The current step in document preparation.</param>
            <param name="maximum">The latest step in document preparation.</param>
        </member>
        <member name="T:MigraDoc.Rendering.DocumentRenderer.PrepareDocumentProgressEventHandler">
            <summary>
            The event handler that is being called for the PrepareDocumentProgressEvent event.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.TableRenderer">
            <summary>
            Renders a table to an XGraphics object.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.TableRenderer.RenderHeaderRows">
            <summary>
            
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.TableRenderer.Format(MigraDoc.Rendering.Area,MigraDoc.Rendering.FormatInfo)">
            <summary>
            Formats (measures) the table.
            </summary>
            <param name="area">The area on which to fit the table.</param>
            <param name="previousFormatInfo"></param>
        </member>
        <member name="M:MigraDoc.Rendering.TableRenderer.CalcStartingHeight">
            <summary>
            Calcs either the height of the header rows or the height of the uppermost top border.
            </summary>
            <returns></returns>
        </member>
        <member name="M:MigraDoc.Rendering.TableRenderer.CalcMaxTopBorderWidth(System.Int32)">
            <summary>
            Calculates the top border width for the first row that is rendered or formatted.
            </summary>
            <param name="row">The row index.</param>
        </member>
        <member name="M:MigraDoc.Rendering.TableRenderer.CreateNextBottomBorderPosition">
            <summary>
            Creates the next bottom border position.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.TableRenderer.CalcBottomBorderWidth(MigraDoc.DocumentObjectModel.Tables.Cell)">
            <summary>
            Calculates bottom border width of a cell.
            </summary>
            <param name="cell">The cell the bottom border of the row that is probed.</param>
            <returns>The calculated border width.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.TableRenderer.GetMinMergedCell(System.Int32)">
            <summary>
            Gets the first cell in the given row that is merged down minimally.
            </summary>
            <param name="row">The row to prope.</param>
            <returns>The first cell with minimal vertical merge.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.TableRenderer.CalcLastConnectedRow(System.Int32)">
            <summary>
            Calculates the last row that is connected with the given row.
            </summary>
            <param name="row">The row that is probed for downward connection.</param>
            <returns>The last row that is connected with the given row.</returns>
        </member>
        <member name="M:MigraDoc.Rendering.TableRenderer.CalcLastConnectedColumn(System.Int32)">
            <summary>
            Calculates the last column that is connected with the specified column.
            </summary>
            <param name="column">The column that is probed for downward connection.</param>
            <returns>The last column that is connected with the given column.</returns>
        </member>
        <member name="T:MigraDoc.Rendering.PageBreakRenderer">
            <summary>
            Renders a page break to an XGraphics object.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PageBreakRenderer.#ctor(PdfSharp.Drawing.XGraphics,MigraDoc.DocumentObjectModel.PageBreak,MigraDoc.Rendering.FieldInfos)">
            <summary>
            Initializes a ParagraphRenderer object for formatting.
            </summary>
            <param name="gfx">The XGraphics object to do measurements on.</param>
            <param name="pageBreak">The page break.</param>
            <param name="fieldInfos">The field infos.</param>
        </member>
        <member name="M:MigraDoc.Rendering.PageBreakRenderer.#ctor(PdfSharp.Drawing.XGraphics,MigraDoc.Rendering.RenderInfo,MigraDoc.Rendering.FieldInfos)">
            <summary>
            Initializes a ParagraphRenderer object for rendering.
            </summary>
            <param name="gfx">The XGraphics object to render on.</param>
            <param name="renderInfo">The render info object containing information necessary for rendering.</param>
            <param name="fieldInfos">The field infos.</param>
        </member>
        <member name="T:MigraDoc.Rendering.PageRenderOptions">
            <summary>
            Determines the parts of a page to be rendered.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.PageRenderOptions.None">
            <summary>
            renders nothing (creates an empty page)
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.PageRenderOptions.RenderHeader">
            <summary>
            renders Headers
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.PageRenderOptions.RenderFooter">
            <summary>
            renders Footers
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.PageRenderOptions.RenderContent">
            <summary>
            renders Content
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.PageRenderOptions.RenderPdfBackground">
            <summary>
            renders PDF Background pages
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.PageRenderOptions.RenderPdfContent">
            <summary>
            renders PDF content pages
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.PageRenderOptions.All">
            <summary>
            renders all
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.PageRenderOptions.RemovePage">
            <summary>
            creates not even an empty page
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.PdfPrinter">
            <summary>
            Provides the functionality to convert MigraDoc documents into PDF.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfPrinter.#ctor">
            <summary>
            Initializes a new instance of the PdfPrinter class.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfPrinter.PrintDocument">
            <summary>
            Prints a PDF document containing all pages of the document.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfPrinter.Save(System.String)">
            <summary>
            Saves the PDF document to the specified path. If a file already exists, it will be overwritten.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfPrinter.Save(System.IO.Stream,System.Boolean)">
            <summary>
            Saves the PDF document to the specified stream.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfPrinter.PrintPages(System.Int32,System.Int32)">
            <summary>
            Prints the spcified page range.
            </summary>
            <param name="startPage">The first page to print.</param>
            <param name="endPage">The last page to print</param>
        </member>
        <member name="M:MigraDoc.Rendering.PdfPrinter.WriteDocumentInformation">
            <summary>
            Writes document information like author and subject to the PDF document.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PdfPrinter.Document">
            <summary>
            Set the MigraDoc document to be rendered by this printer.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PdfPrinter.DocumentRenderer">
            <summary>
            Gets or sets a document renderer.
            </summary>
            <remarks>
            A document renderer is automatically created and prepared
            when printing before this property was set.
            </remarks>
        </member>
        <member name="P:MigraDoc.Rendering.PdfPrinter.WorkingDirectory">
            <summary>
            Gets or sets a working directory for the printing process.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PdfPrinter.PdfDocument">
            <summary>
            Gets or sets the PDF document to render on.
            </summary>
            <remarks>A PDF document in memory is automatically created when printing before this property was set.</remarks>
        </member>
        <member name="T:MigraDoc.Rendering.NumberFormatter">
            <summary>
            Formats numbers roman or with letters.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.Forms.Zoom">
            <summary>
            Defines a zoom factor used in the preview control.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent800">
            <summary>
            Zoom factor 800%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent600">
            <summary>
            Zoom factor 600%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent400">
            <summary>
            Zoom factor 400%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent200">
            <summary>
            Zoom factor 400%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent150">
            <summary>
            Zoom factor 150%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent100">
            <summary>
            Zoom factor 100%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent75">
            <summary>
            Zoom factor 75%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent50">
            <summary>
            Zoom factor 50%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent25">
            <summary>
            Zoom factor 25%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Percent10">
            <summary>
            Zoom factor 10%.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.BestFit">
            <summary>
            Sets the zoom factor so that the document fits horizontally into the window.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.TextFit">
            <summary>
            Sets the zoom factor so that the printable area of the document fits horizontally into the window.
            Currently not yet implemented and the same as ZoomBestFit.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.FullPage">
            <summary>
            Sets the zoom factor so that the whole document fits completely into the window.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.OriginalSize">
            <summary>
            Sets the zoom factor so that the document is displayed in its real physical size (based on the DPI information returned from the OS for the current monitor).
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Mininum">
            <summary>
            The smallest possible zoom factor.
            </summary>
        </member>
        <member name="F:MigraDoc.Rendering.Forms.Zoom.Maximum">
            <summary>
            The largest possible zoom factor.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ChartMapper.SeriesCollectionMapper">
            <summary>
            The SeriesCollectionMapper class.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.ChartMapper.SeriesCollectionMapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.ChartMapper.SeriesCollectionMapper"/> class.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.ShadingRenderer">
            <summary>
            Renders a Shading to an XGraphics object.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.PdfDocumentRenderer">
            <summary>
            Provides the functionality to convert a MigraDoc document into PDF.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.#ctor">
            <summary>
            Initializes a new instance of the PdfDocumentRenderer class.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the PdfDocumentRenderer class.
            </summary>
            <param name="unicode">If true Unicode encoding is used for all text. If false, WinAnsi encoding is used.</param>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.#ctor(System.Boolean,PdfSharp.Pdf.PdfFontEmbedding)">
            <summary>
            Initializes a new instance of the <see cref="T:MigraDoc.Rendering.PdfDocumentRenderer"/> class.
            </summary>
            <param name="unicode">If true Unicode encoding is used for all text. If false, WinAnsi encoding is used.</param>
            <param name="fontEmbedding">Specifies which fonts will be imbedded in the PDF file.</param>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.RenderDocument">
            <summary>
            Renders the document into a PdfDocument containing all pages of the document.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.PrepareRenderPages">
            <summary>
            Renders the document into a PdfDocument containing all pages of the document.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.Save(System.String)">
            <summary>
            Saves the PdfDocument to the specified path. If a file already exists, it will be overwritten.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.Save(System.IO.Stream,System.Boolean)">
            <summary>
            Saves the PDF document to the specified stream.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.RenderPages(System.Int32,System.Int32)">
            <summary>
            Renders the spcified page range.
            </summary>
            <param name="startPage">The first page to print.</param>
            <param name="endPage">The last page to print</param>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.WriteDocumentInformation">
            <summary>
            Writes document information like author and subject to the PDF document.
            </summary>
        </member>
        <member name="M:MigraDoc.Rendering.PdfDocumentRenderer.CreatePdfDocument">
            <summary>
            Creates a new PDF document.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PdfDocumentRenderer.Unicode">
            <summary>
            Gets a value indicating whether the text is rendered as Unicode.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PdfDocumentRenderer.FontEmbedding">
            <summary>
            Gets a value indicating whether all used fonts are embedded in the document.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PdfDocumentRenderer.Language">
            <summary>
            Gets or sets the language.
            </summary>
            <value>The language.</value>
        </member>
        <member name="P:MigraDoc.Rendering.PdfDocumentRenderer.Document">
            <summary>
            Set the MigraDoc document to be rendered by this printer.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PdfDocumentRenderer.DocumentRenderer">
            <summary>
            Gets or sets a document renderer.
            </summary>
            <remarks>
            A document renderer is automatically created and prepared
            when printing before this property was set.
            </remarks>
        </member>
        <member name="P:MigraDoc.Rendering.PdfDocumentRenderer.PageCount">
            <summary>
            Gets the count of pages.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PdfDocumentRenderer.WorkingDirectory">
            <summary>
            Gets or sets a working directory for the printing process.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.PdfDocumentRenderer.PdfDocument">
            <summary>
            Gets or sets the PDF document to render on.
            </summary>
            <remarks>A PDF document in memory is automatically created when printing before this property was set.</remarks>
        </member>
        <member name="T:MigraDoc.Rendering.ParagraphRenderInfo">
            <summary>
            Represents rendering information for a paragraph.
            </summary>
        </member>
        <member name="T:MigraDoc.Rendering.LayoutInfo">
            <summary>
            Abstract base class to serve as a layoutable unit.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.StartingHeight">
            <summary>
            Gets or sets the height necessary to start the document object.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.TrailingHeight">
            <summary>
            Gets or sets the height necessary to end the document object.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.KeepWithNext">
            <summary>
            Indicates whether the document object shall be kept on one page
            with its successor.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.KeepTogether">
            <summary>
            Indicates whether the document object shall be kept together on one page.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.MarginTop">
            <summary>
            The space that shall be kept free above the element's content.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.MarginRight">
            <summary>
            The space that shall be kept free right to the element's content.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.MarginBottom">
            <summary>
            The space that shall be kept free below the element's content.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.MarginLeft">
            <summary>
            The space that shall be kept free left to the element's content.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.ContentArea">
            <summary>
            Gets or sets the Area needed by the content (including padding and borders for e.g. paragraphs).
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.PageBreakBefore">
            <summary>
            Gets or sets the a value indicating whether the element shall appear on a new page.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.HorizontalReference">
            <summary>
            Gets or sets the reference point for horizontal positioning.
            </summary>
            <remarks>Default value is AreaBoundary.</remarks>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.VerticalReference">
            <summary>
            Gets or sets the reference point for vertical positioning.
            </summary>
            <remarks>Default value is PreviousElement.</remarks>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.HorizontalAlignment">
            <summary>
            Gets or sets the horizontal alignment of the element.
            </summary>
            <remarks>Default value is Near.</remarks>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.VerticalAlignment">
            <summary>
            Gets or sets the vertical alignment of the element.
            </summary>
            <remarks>Default value is Near.</remarks>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.Floating">
            <summary>
            Gets or sets the floating behavior of surrounding elements.
            </summary>
            <remarks>Default value is TopBottom.</remarks>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.Top">
            <summary>
            Gets or sets the top position of the element.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.Left">
            <summary>
            Gets or sets the left position of the element.
            </summary>
        </member>
        <member name="P:MigraDoc.Rendering.LayoutInfo.MinWidth">
            <summary>
            Gets or sets the minimum width of the element.
            </summary>
        </member>
    </members>
</doc>
